{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, computed, unref, openBlock, createElementBlock, normalizeClass, withModifiers, createBlock, Transition, withCtx, withDirectives, createElementVNode, createVNode, vShow, createCommentVNode, normalizeStyle, renderSlot, Fragment, renderList, toDisplayString } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';\nimport { carouselProps, carouselEmits } from './carousel.mjs';\nimport { useCarousel } from './use-carousel.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nconst COMPONENT_NAME = \"ElCarousel\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: carouselProps,\n  emits: carouselEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      root,\n      activeIndex,\n      arrowDisplay,\n      hasLabel,\n      hover,\n      isCardType,\n      items,\n      isVertical,\n      containerStyle,\n      handleButtonEnter,\n      handleButtonLeave,\n      isTransitioning,\n      handleIndicatorClick,\n      handleMouseEnter,\n      handleMouseLeave,\n      handleTransitionEnd,\n      setActiveItem,\n      prev,\n      next,\n      PlaceholderItem,\n      isTwoLengthShow,\n      throttledArrowClick,\n      throttledIndicatorHover\n    } = useCarousel(props, emit, COMPONENT_NAME);\n    const ns = useNamespace(\"carousel\");\n    const {\n      t\n    } = useLocale();\n    const carouselClasses = computed(() => {\n      const classes = [ns.b(), ns.m(props.direction)];\n      if (unref(isCardType)) {\n        classes.push(ns.m(\"card\"));\n      }\n      return classes;\n    });\n    const carouselContainer = computed(() => {\n      const classes = [ns.e(\"container\")];\n      if (props.motionBlur && unref(isTransitioning) && items.value.length > 1) {\n        classes.push(unref(isVertical) ? `${ns.namespace.value}-transitioning-vertical` : `${ns.namespace.value}-transitioning`);\n      }\n      return classes;\n    });\n    const indicatorsClasses = computed(() => {\n      const classes = [ns.e(\"indicators\"), ns.em(\"indicators\", props.direction)];\n      if (unref(hasLabel)) {\n        classes.push(ns.em(\"indicators\", \"labels\"));\n      }\n      if (props.indicatorPosition === \"outside\") {\n        classes.push(ns.em(\"indicators\", \"outside\"));\n      }\n      if (unref(isVertical)) {\n        classes.push(ns.em(\"indicators\", \"right\"));\n      }\n      return classes;\n    });\n    expose({\n      activeIndex,\n      setActiveItem,\n      prev,\n      next\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"root\",\n        ref: root,\n        class: normalizeClass(unref(carouselClasses)),\n        onMouseenter: withModifiers(unref(handleMouseEnter), [\"stop\"]),\n        onMouseleave: withModifiers(unref(handleMouseLeave), [\"stop\"])\n      }, [unref(arrowDisplay) ? (openBlock(), createBlock(Transition, {\n        key: 0,\n        name: \"carousel-arrow-left\",\n        persisted: \"\"\n      }, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"button\", {\n          type: \"button\",\n          class: normalizeClass([unref(ns).e(\"arrow\"), unref(ns).em(\"arrow\", \"left\")]),\n          \"aria-label\": unref(t)(\"el.carousel.leftArrow\"),\n          onMouseenter: $event => unref(handleButtonEnter)(\"left\"),\n          onMouseleave: unref(handleButtonLeave),\n          onClick: withModifiers($event => unref(throttledArrowClick)(unref(activeIndex) - 1), [\"stop\"])\n        }, [createVNode(unref(ElIcon), null, {\n          default: withCtx(() => [createVNode(unref(ArrowLeft))]),\n          _: 1\n        })], 42, [\"aria-label\", \"onMouseenter\", \"onMouseleave\", \"onClick\"]), [[vShow, (_ctx.arrow === \"always\" || unref(hover)) && (props.loop || unref(activeIndex) > 0)]])]),\n        _: 1\n      })) : createCommentVNode(\"v-if\", true), unref(arrowDisplay) ? (openBlock(), createBlock(Transition, {\n        key: 1,\n        name: \"carousel-arrow-right\",\n        persisted: \"\"\n      }, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"button\", {\n          type: \"button\",\n          class: normalizeClass([unref(ns).e(\"arrow\"), unref(ns).em(\"arrow\", \"right\")]),\n          \"aria-label\": unref(t)(\"el.carousel.rightArrow\"),\n          onMouseenter: $event => unref(handleButtonEnter)(\"right\"),\n          onMouseleave: unref(handleButtonLeave),\n          onClick: withModifiers($event => unref(throttledArrowClick)(unref(activeIndex) + 1), [\"stop\"])\n        }, [createVNode(unref(ElIcon), null, {\n          default: withCtx(() => [createVNode(unref(ArrowRight))]),\n          _: 1\n        })], 42, [\"aria-label\", \"onMouseenter\", \"onMouseleave\", \"onClick\"]), [[vShow, (_ctx.arrow === \"always\" || unref(hover)) && (props.loop || unref(activeIndex) < unref(items).length - 1)]])]),\n        _: 1\n      })) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(carouselContainer)),\n        style: normalizeStyle(unref(containerStyle)),\n        onTransitionend: unref(handleTransitionEnd)\n      }, [createVNode(unref(PlaceholderItem)), renderSlot(_ctx.$slots, \"default\")], 46, [\"onTransitionend\"]), _ctx.indicatorPosition !== \"none\" ? (openBlock(), createElementBlock(\"ul\", {\n        key: 2,\n        class: normalizeClass(unref(indicatorsClasses))\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(items), (item, index) => {\n        return withDirectives((openBlock(), createElementBlock(\"li\", {\n          key: index,\n          class: normalizeClass([unref(ns).e(\"indicator\"), unref(ns).em(\"indicator\", _ctx.direction), unref(ns).is(\"active\", index === unref(activeIndex))]),\n          onMouseenter: $event => unref(throttledIndicatorHover)(index),\n          onClick: withModifiers($event => unref(handleIndicatorClick)(index), [\"stop\"])\n        }, [createElementVNode(\"button\", {\n          class: normalizeClass(unref(ns).e(\"button\")),\n          \"aria-label\": unref(t)(\"el.carousel.indicator\", {\n            index: index + 1\n          })\n        }, [unref(hasLabel) ? (openBlock(), createElementBlock(\"span\", {\n          key: 0\n        }, toDisplayString(item.props.label), 1)) : createCommentVNode(\"v-if\", true)], 10, [\"aria-label\"])], 42, [\"onMouseenter\", \"onClick\"])), [[vShow, unref(isTwoLengthShow)(index)]]);\n      }), 128))], 2)) : createCommentVNode(\"v-if\", true), props.motionBlur ? (openBlock(), createElementBlock(\"svg\", {\n        key: 3,\n        xmlns: \"http://www.w3.org/2000/svg\",\n        version: \"1.1\",\n        style: {\n          \"display\": \"none\"\n        }\n      }, [createElementVNode(\"defs\", null, [createElementVNode(\"filter\", {\n        id: \"elCarouselHorizontal\"\n      }, [createElementVNode(\"feGaussianBlur\", {\n        in: \"SourceGraphic\",\n        stdDeviation: \"12,0\"\n      })]), createElementVNode(\"filter\", {\n        id: \"elCarouselVertical\"\n      }, [createElementVNode(\"feGaussianBlur\", {\n        in: \"SourceGraphic\",\n        stdDeviation: \"0,10\"\n      })])])])) : createCommentVNode(\"v-if\", true)], 42, [\"onMouseenter\", \"onMouseleave\"]);\n    };\n  }\n});\nvar Carousel = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"carousel.vue\"]]);\nexport { Carousel as default };", "map": {"version": 3, "names": ["name", "COMPONENT_NAME", "root", "activeIndex", "arrowDisplay", "<PERSON><PERSON><PERSON><PERSON>", "hover", "isCardType", "items", "isVertical", "containerStyle", "handleButtonEnter", "handleButtonLeave", "isTransitioning", "handleIndicatorClick", "handleMouseEnter", "handleMouseLeave", "handleTransitionEnd", "setActiveItem", "prev", "next", "PlaceholderItem", "isTwoLengthShow", "throttledArrowClick", "throttledIndicatorHover", "useCarousel", "props", "emit", "ns", "useNamespace", "t", "useLocale", "carouselClasses", "computed", "classes", "b", "m", "direction", "unref", "push", "carouselContainer", "e", "motionBlur", "value", "length", "namespace", "indicatorsClasses", "em", "indicatorPosition", "expose", "_ctx", "_cache", "openBlock", "createElementBlock", "ref_key", "ref", "class", "normalizeClass", "onMouseenter", "withModifiers"], "sources": ["../../../../../../packages/components/carousel/src/carousel.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"root\"\n    :class=\"carouselClasses\"\n    @mouseenter.stop=\"handleMouseEnter\"\n    @mouseleave.stop=\"handleMouseLeave\"\n  >\n    <transition v-if=\"arrowDisplay\" name=\"carousel-arrow-left\">\n      <button\n        v-show=\"\n          (arrow === 'always' || hover) && (props.loop || activeIndex > 0)\n        \"\n        type=\"button\"\n        :class=\"[ns.e('arrow'), ns.em('arrow', 'left')]\"\n        :aria-label=\"t('el.carousel.leftArrow')\"\n        @mouseenter=\"handleButtonEnter('left')\"\n        @mouseleave=\"handleButtonLeave\"\n        @click.stop=\"throttledArrowClick(activeIndex - 1)\"\n      >\n        <ElIcon>\n          <ArrowLeft />\n        </ElIcon>\n      </button>\n    </transition>\n    <transition v-if=\"arrowDisplay\" name=\"carousel-arrow-right\">\n      <button\n        v-show=\"\n          (arrow === 'always' || hover) &&\n          (props.loop || activeIndex < items.length - 1)\n        \"\n        type=\"button\"\n        :class=\"[ns.e('arrow'), ns.em('arrow', 'right')]\"\n        :aria-label=\"t('el.carousel.rightArrow')\"\n        @mouseenter=\"handleButtonEnter('right')\"\n        @mouseleave=\"handleButtonLeave\"\n        @click.stop=\"throttledArrowClick(activeIndex + 1)\"\n      >\n        <ElIcon>\n          <ArrowRight />\n        </ElIcon>\n      </button>\n    </transition>\n    <div\n      :class=\"carouselContainer\"\n      :style=\"containerStyle\"\n      @transitionend=\"handleTransitionEnd\"\n    >\n      <PlaceholderItem />\n      <slot />\n    </div>\n    <ul v-if=\"indicatorPosition !== 'none'\" :class=\"indicatorsClasses\">\n      <li\n        v-for=\"(item, index) in items\"\n        v-show=\"isTwoLengthShow(index)\"\n        :key=\"index\"\n        :class=\"[\n          ns.e('indicator'),\n          ns.em('indicator', direction),\n          ns.is('active', index === activeIndex),\n        ]\"\n        @mouseenter=\"throttledIndicatorHover(index)\"\n        @click.stop=\"handleIndicatorClick(index)\"\n      >\n        <button\n          :class=\"ns.e('button')\"\n          :aria-label=\"t('el.carousel.indicator', { index: index + 1 })\"\n        >\n          <span v-if=\"hasLabel\">{{ item.props.label }}</span>\n        </button>\n      </li>\n    </ul>\n    <svg\n      v-if=\"props.motionBlur\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      version=\"1.1\"\n      style=\"display: none\"\n    >\n      <defs>\n        <filter id=\"elCarouselHorizontal\">\n          <feGaussianBlur in=\"SourceGraphic\" stdDeviation=\"12,0\" />\n        </filter>\n        <filter id=\"elCarouselVertical\">\n          <feGaussianBlur in=\"SourceGraphic\" stdDeviation=\"0,10\" />\n        </filter>\n      </defs>\n    </svg>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, unref } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { carouselEmits, carouselProps } from './carousel'\nimport { useCarousel } from './use-carousel'\n\nconst COMPONENT_NAME = 'ElCarousel'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(carouselProps)\nconst emit = defineEmits(carouselEmits)\nconst {\n  root,\n  activeIndex,\n  arrowDisplay,\n  hasLabel,\n  hover,\n  isCardType,\n  items,\n  isVertical,\n  containerStyle,\n  handleButtonEnter,\n  handleButtonLeave,\n  isTransitioning,\n  handleIndicatorClick,\n  handleMouseEnter,\n  handleMouseLeave,\n  handleTransitionEnd,\n  setActiveItem,\n  prev,\n  next,\n  PlaceholderItem,\n  isTwoLengthShow,\n  throttledArrowClick,\n  throttledIndicatorHover,\n} = useCarousel(props, emit, COMPONENT_NAME)\nconst ns = useNamespace('carousel')\n\nconst { t } = useLocale()\n\nconst carouselClasses = computed(() => {\n  const classes = [ns.b(), ns.m(props.direction)]\n  if (unref(isCardType)) {\n    classes.push(ns.m('card'))\n  }\n  return classes\n})\n\nconst carouselContainer = computed(() => {\n  const classes = [ns.e('container')]\n  if (props.motionBlur && unref(isTransitioning) && items.value.length > 1) {\n    classes.push(\n      unref(isVertical)\n        ? `${ns.namespace.value}-transitioning-vertical`\n        : `${ns.namespace.value}-transitioning`\n    )\n  }\n  return classes\n})\n\nconst indicatorsClasses = computed(() => {\n  const classes = [ns.e('indicators'), ns.em('indicators', props.direction)]\n  if (unref(hasLabel)) {\n    classes.push(ns.em('indicators', 'labels'))\n  }\n  if (props.indicatorPosition === 'outside') {\n    classes.push(ns.em('indicators', 'outside'))\n  }\n  if (unref(isVertical)) {\n    classes.push(ns.em('indicators', 'right'))\n  }\n  return classes\n})\n\ndefineExpose({\n  /** @description active slide index */\n  activeIndex,\n  /** @description manually switch slide, index of the slide to be switched to, starting from 0; or the `name` of corresponding `el-carousel-item` */\n  setActiveItem,\n  /** @description switch to the previous slide */\n  prev,\n  /** @description switch to the next slide */\n  next,\n})\n</script>\n"], "mappings": ";;;;;;;;;;mCAkGc;EACZA,IAAM,EAAAC;AACR;;;;;;;;;;IAIM;MACJC,IAAA;MACAC,WAAA;MACAC,YAAA;MACAC,QAAA;MACAC,KAAA;MACAC,UAAA;MACAC,KAAA;MACAC,UAAA;MACAC,cAAA;MACAC,iBAAA;MACAC,iBAAA;MACAC,eAAA;MACAC,oBAAA;MACAC,gBAAA;MACAC,gBAAA;MACAC,mBAAA;MACAC,aAAA;MACAC,IAAA;MACAC,IAAA;MACAC,eAAA;MACAC,eAAA;MACAC,mBAAA;MACAC;IAAA,CACE,GAAAC,WAAA,CAAYC,KAAO,EAAAC,IAAA,EAAM1B,cAAc;IACrC,MAAA2B,EAAA,GAAKC,YAAA,CAAa,UAAU;IAE5B;MAAEC;IAAE,IAAIC,SAAU;IAElB,MAAAC,eAAA,GAAkBC,QAAA,CAAS,MAAM;MAC/B,MAAAC,OAAA,GAAU,CAACN,EAAG,CAAAO,CAAA,IAAKP,EAAG,CAAAQ,CAAA,CAAEV,KAAM,CAAAW,SAAS,CAAC;MAC1C,IAAAC,KAAA,CAAM/B,UAAU,CAAG;QACrB2B,OAAA,CAAQK,IAAK,CAAAX,EAAA,CAAGQ,CAAE,OAAM,CAAC;MAAA;MAEpB,OAAAF,OAAA;IAAA,CACR;IAEK,MAAAM,iBAAA,GAAoBP,QAAA,CAAS,MAAM;MACvC,MAAMC,OAAU,IAACN,EAAG,CAAAa,CAAA,CAAE,WAAW,CAAC;MAC9B,IAAAf,KAAA,CAAMgB,UAAA,IAAcJ,KAAM,CAAAzB,eAAe,KAAKL,KAAM,CAAAmC,KAAA,CAAMC,MAAA,GAAS,CAAG;QAChEV,OAAA,CAAAK,IAAA,CAAAD,KAAA,CAAA7B,UAAA,OAAAmB,EAAA,CAAAiB,SAAA,CAAAF,KAAA,+BAAAf,EAAA,CAAAiB,SAAA,CAAAF,KAAA;MAAA;MAIR,OAAAT,OAAA;IAAA,CACF;IACO,MAAAY,iBAAA,GAAAb,QAAA;MACR,MAAAC,OAAA,IAAAN,EAAA,CAAAa,CAAA,gBAAAb,EAAA,CAAAmB,EAAA,eAAArB,KAAA,CAAAW,SAAA;MAEK,IAAAC,KAAA,CAAAjC,QAAA;QACE6B,OAAA,CAAAK,IAAA,CAAAX,EAAW,CAAAmB,EAAG,CAAE,cAAe,QAAM;MAC3C;MACE,IAAArB,KAAA,CAAQsB,iBAAW,gBAAsB;QAC3Cd,OAAA,CAAAK,IAAA,CAAAX,EAAA,CAAAmB,EAAA;MACA;MACE,IAAAT,KAAA,CAAQ7B,UAAW;QACrByB,OAAA,CAAAK,IAAA,CAAAX,EAAA,CAAAmB,EAAA;MACA;MACE,OAAAb,OAAa;IAA4B,CAC3C;IACOe,MAAA;MACR9C,WAAA;MAEYe,aAAA;MAAAC,IAAA;MAEXC;IAAA;IAEA,QAAA8B,IAAA,EAAAC,MAAA;MAAA,OAAAC,SAAA,IAAAC,kBAAA;QAEAC,OAAA;QAAAC,GAAA,EAAArD,IAAA;QAEAsD,KAAA,EAAAC,cAAA,CAAAnB,KAAA,CAAAN,eAAA;QACD0B,YAAA,EAAAC,aAAA,CAAArB,KAAA,CAAAvB,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}