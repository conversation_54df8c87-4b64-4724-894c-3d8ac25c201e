{"ast": null, "code": "import { cAF, rAF } from '../../../../utils/raf.mjs';\nconst useGridWheel = ({\n  atXEndEdge,\n  atXStartEdge,\n  atYEndEdge,\n  atYStartEdge\n}, onWheelDelta) => {\n  let frameHandle = null;\n  let xOffset = 0;\n  let yOffset = 0;\n  const hasReachedEdge = (x, y) => {\n    const xEdgeReached = x <= 0 && atXStartEdge.value || x >= 0 && atXEndEdge.value;\n    const yEdgeReached = y <= 0 && atYStartEdge.value || y >= 0 && atYEndEdge.value;\n    return xEdgeReached && yEdgeReached;\n  };\n  const onWheel = e => {\n    cAF(frameHandle);\n    let x = e.deltaX;\n    let y = e.deltaY;\n    if (Math.abs(x) > Math.abs(y)) {\n      y = 0;\n    } else {\n      x = 0;\n    }\n    if (e.shiftKey && y !== 0) {\n      x = y;\n      y = 0;\n    }\n    if (hasReachedEdge(xOffset, yOffset) && hasReachedEdge(xOffset + x, yOffset + y)) return;\n    xOffset += x;\n    yOffset += y;\n    e.preventDefault();\n    frameHandle = rAF(() => {\n      onWheelDelta(xOffset, yOffset);\n      xOffset = 0;\n      yOffset = 0;\n    });\n  };\n  return {\n    hasReachedEdge,\n    onWheel\n  };\n};\nexport { useGridWheel };", "map": {"version": 3, "names": ["useGridWheel", "atXEndEdge", "atXStartEdge", "atYEndEdge", "atYStartEdge", "onWheelDelta", "frameHandle", "xOffset", "yOffset", "hasReached<PERSON><PERSON>", "x", "y", "xEdgeReached", "value", "yEdgeReached", "onWheel", "e", "cAF", "deltaX", "deltaY", "Math", "abs", "shift<PERSON>ey", "preventDefault", "rAF"], "sources": ["../../../../../../../packages/components/virtual-list/src/hooks/use-grid-wheel.ts"], "sourcesContent": ["import { cAF, rAF } from '@element-plus/utils'\n\nimport type { ComputedRef } from 'vue'\n\ninterface GridWheelState {\n  atXStartEdge: ComputedRef<boolean>\n  atXEndEdge: ComputedRef<boolean>\n  atYStartEdge: ComputedRef<boolean>\n  atYEndEdge: ComputedRef<boolean>\n}\n\ntype GridWheelHandler = (x: number, y: number) => void\n\nexport const useGridWheel = (\n  { atXEndEdge, atXStartEdge, atYEndEdge, atYStartEdge }: GridWheelState,\n  onWheelDelta: GridWheelHandler\n) => {\n  let frameHandle: number | null = null\n  let xOffset = 0\n  let yOffset = 0\n\n  const hasReachedEdge = (x: number, y: number) => {\n    const xEdgeReached =\n      (x <= 0 && atXStartEdge.value) || (x >= 0 && atXEndEdge.value)\n    const yEdgeReached =\n      (y <= 0 && atYStartEdge.value) || (y >= 0 && atYEndEdge.value)\n    return xEdgeReached && yEdgeReached\n  }\n\n  const onWheel = (e: WheelEvent) => {\n    cAF(frameHandle!)\n\n    let x = e.deltaX\n    let y = e.deltaY\n    // Simulate native behavior when using touch pad/track pad for wheeling.\n    if (Math.abs(x) > Math.abs(y)) {\n      y = 0\n    } else {\n      x = 0\n    }\n\n    // Special case for windows machine with shift key + wheel scrolling\n    if (e.shiftKey && y !== 0) {\n      x = y\n      y = 0\n    }\n\n    if (\n      hasReachedEdge(xOffset, yOffset) &&\n      hasReachedEdge(xOffset + x, yOffset + y)\n    )\n      return\n\n    xOffset += x\n    yOffset += y\n\n    e.preventDefault()\n\n    frameHandle = rAF(() => {\n      onWheelDelta(xOffset, yOffset)\n      xOffset = 0\n      yOffset = 0\n    })\n  }\n\n  return {\n    hasReachedEdge,\n    onWheel,\n  }\n}\n"], "mappings": ";AACY,MAACA,YAAY,GAAGA,CAAC;EAAEC,UAAU;EAAEC,YAAY;EAAEC,UAAU;EAAEC;AAAY,CAAE,EAAEC,YAAY,KAAK;EACpG,IAAIC,WAAW,GAAG,IAAI;EACtB,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,OAAO,GAAG,CAAC;EACf,MAAMC,cAAc,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC/B,MAAMC,YAAY,GAAGF,CAAC,IAAI,CAAC,IAAIR,YAAY,CAACW,KAAK,IAAIH,CAAC,IAAI,CAAC,IAAIT,UAAU,CAACY,KAAK;IAC/E,MAAMC,YAAY,GAAGH,CAAC,IAAI,CAAC,IAAIP,YAAY,CAACS,KAAK,IAAIF,CAAC,IAAI,CAAC,IAAIR,UAAU,CAACU,KAAK;IAC/E,OAAOD,YAAY,IAAIE,YAAY;EACvC,CAAG;EACD,MAAMC,OAAO,GAAIC,CAAC,IAAK;IACrBC,GAAG,CAACX,WAAW,CAAC;IAChB,IAAII,CAAC,GAAGM,CAAC,CAACE,MAAM;IAChB,IAAIP,CAAC,GAAGK,CAAC,CAACG,MAAM;IAChB,IAAIC,IAAI,CAACC,GAAG,CAACX,CAAC,CAAC,GAAGU,IAAI,CAACC,GAAG,CAACV,CAAC,CAAC,EAAE;MAC7BA,CAAC,GAAG,CAAC;IACX,CAAK,MAAM;MACLD,CAAC,GAAG,CAAC;IACX;IACI,IAAIM,CAAC,CAACM,QAAQ,IAAIX,CAAC,KAAK,CAAC,EAAE;MACzBD,CAAC,GAAGC,CAAC;MACLA,CAAC,GAAG,CAAC;IACX;IACI,IAAIF,cAAc,CAACF,OAAO,EAAEC,OAAO,CAAC,IAAIC,cAAc,CAACF,OAAO,GAAGG,CAAC,EAAEF,OAAO,GAAGG,CAAC,CAAC,EAC9E;IACFJ,OAAO,IAAIG,CAAC;IACZF,OAAO,IAAIG,CAAC;IACZK,CAAC,CAACO,cAAc,EAAE;IAClBjB,WAAW,GAAGkB,GAAG,CAAC,MAAM;MACtBnB,YAAY,CAACE,OAAO,EAAEC,OAAO,CAAC;MAC9BD,OAAO,GAAG,CAAC;MACXC,OAAO,GAAG,CAAC;IACjB,CAAK,CAAC;EACN,CAAG;EACD,OAAO;IACLC,cAAc;IACdM;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}