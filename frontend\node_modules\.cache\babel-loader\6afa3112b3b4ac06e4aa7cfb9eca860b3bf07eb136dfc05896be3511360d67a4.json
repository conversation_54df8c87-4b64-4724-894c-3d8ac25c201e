{"ast": null, "code": "import { ref } from 'vue';\nexport default {\n  name: 'Help',\n  setup() {\n    const activeNames = ref(['1']);\n    return {\n      activeNames\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "name", "setup", "activeNames"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Help.vue"], "sourcesContent": ["<template>\n  <div class=\"help-container\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h2>帮助中心</h2>\n        </div>\n      </template>\n      \n      <el-collapse v-model=\"activeNames\">\n        <el-collapse-item title=\"如何预约座位？\" name=\"1\">\n          <div class=\"help-content\">\n            <p>预约座位的步骤如下：</p>\n            <ol>\n              <li>在侧边栏菜单中点击\"座位管理\" -> \"预约座位\"</li>\n              <li>选择您想要预约的自习室</li>\n              <li>在座位地图上选择一个可用的座位（绿色表示可用）</li>\n              <li>选择预约的时间段</li>\n              <li>点击\"确认预约\"按钮</li>\n              <li>预约成功后，您可以在\"个人中心\" -> \"我的预约\"中查看预约详情</li>\n            </ol>\n          </div>\n        </el-collapse-item>\n        \n        <el-collapse-item title=\"如何取消预约？\" name=\"2\">\n          <div class=\"help-content\">\n            <p>取消预约的步骤如下：</p>\n            <ol>\n              <li>在侧边栏菜单中点击\"个人中心\" -> \"我的预约\"</li>\n              <li>找到您想要取消的预约记录</li>\n              <li>点击\"取消预约\"按钮</li>\n              <li>在弹出的确认对话框中点击\"确认\"</li>\n            </ol>\n            <p class=\"warning\">注意：预约开始前30分钟内取消预约将会扣除信誉分，请提前安排好您的时间。</p>\n          </div>\n        </el-collapse-item>\n        \n        <el-collapse-item title=\"如何签到和签退？\" name=\"3\">\n          <div class=\"help-content\">\n            <p>签到和签退的步骤如下：</p>\n            <ol>\n              <li>在侧边栏菜单中点击\"个人中心\" -> \"我的预约\"</li>\n              <li>找到当前正在进行的预约记录</li>\n              <li>点击\"签到\"或\"签退\"按钮</li>\n              <li>使用手机扫描生成的二维码完成操作</li>\n            </ol>\n            <p class=\"warning\">注意：预约开始后15分钟内未签到将视为爽约，会扣除信誉分。</p>\n          </div>\n        </el-collapse-item>\n        \n        <el-collapse-item title=\"信誉分是什么？\" name=\"4\">\n          <div class=\"help-content\">\n            <p>信誉分是衡量用户使用自习室行为的指标：</p>\n            <ul>\n              <li>新用户初始信誉分为100分</li>\n              <li>按时签到签退、正常使用座位会维持或提高信誉分</li>\n              <li>爽约、迟到、提前离开等行为会扣除信誉分</li>\n              <li>信誉分低于60分将限制预约功能</li>\n              <li>信誉分低于30分将暂停使用系统的权限</li>\n            </ul>\n            <p>您可以在\"个人中心\" -> \"信誉分记录\"中查看详细的信誉分变动记录。</p>\n          </div>\n        </el-collapse-item>\n        \n        <el-collapse-item title=\"联系管理员\" name=\"5\">\n          <div class=\"help-content\">\n            <p>如果您在使用过程中遇到任何问题，可以通过以下方式联系管理员：</p>\n            <ul>\n              <li>电子邮件：<EMAIL></li>\n              <li>电话：123-4567-8910</li>\n              <li>前台服务台：图书馆一楼大厅</li>\n            </ul>\n            <p>服务时间：周一至周日 8:00-22:00</p>\n          </div>\n        </el-collapse-item>\n      </el-collapse>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref } from 'vue';\n\nexport default {\n  name: 'Help',\n  setup() {\n    const activeNames = ref(['1']);\n    \n    return {\n      activeNames\n    };\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.help-container {\n  padding: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.help-content {\n  padding: 10px;\n  line-height: 1.6;\n  \n  ol, ul {\n    padding-left: 20px;\n    margin: 10px 0;\n  }\n  \n  .warning {\n    color: #E6A23C;\n    font-weight: bold;\n    margin: 10px 0;\n  }\n}\n</style>\n"], "mappings": "AAiFA,SAASA,GAAE,QAAS,KAAK;AAEzB,eAAe;EACbC,IAAI,EAAE,MAAM;EACZC,KAAKA,CAAA,EAAG;IACN,MAAMC,WAAU,GAAIH,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAE9B,OAAO;MACLG;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}