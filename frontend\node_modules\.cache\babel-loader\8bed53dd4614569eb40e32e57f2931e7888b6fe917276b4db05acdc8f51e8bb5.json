{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport { defineComponent, computed, ref, openBlock, createElementBlock, normalizeClass, unref, createVNode, mergeProps, createSlots, renderList, withCtx, renderSlot, normalizeProps, guardReactiveProps, createElementVNode, normalizeStyle, withModifiers, nextTick } from 'vue';\nimport { pick } from 'lodash-unified';\nimport { ElInput } from '../../input/index.mjs';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { mentionProps, mentionEmits } from './mention.mjs';\nimport { getCursorPosition, getMentionCtx } from './helper.mjs';\nimport ElMentionDropdown from './mention-dropdown2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { inputProps } from '../../input/src/input.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { useFocusController } from '../../../hooks/use-focus-controller/index.mjs';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT } from '../../../constants/event.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { isFunction } from '@vue/shared';\nconst __default__ = defineComponent({\n  name: \"ElMention\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: mentionProps,\n  emits: mentionEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const passInputProps = computed(() => pick(props, Object.keys(inputProps)));\n    const ns = useNamespace(\"mention\");\n    const disabled = useFormDisabled();\n    const contentId = useId();\n    const elInputRef = ref();\n    const tooltipRef = ref();\n    const dropdownRef = ref();\n    const visible = ref(false);\n    const cursorStyle = ref();\n    const mentionCtx = ref();\n    const computedPlacement = computed(() => props.showArrow ? props.placement : `${props.placement}-start`);\n    const computedFallbackPlacements = computed(() => props.showArrow ? [\"bottom\", \"top\"] : [\"bottom-start\", \"top-start\"]);\n    const filteredOptions = computed(() => {\n      const {\n        filterOption,\n        options\n      } = props;\n      if (!mentionCtx.value || !filterOption) return options;\n      return options.filter(option => filterOption(mentionCtx.value.pattern, option));\n    });\n    const dropdownVisible = computed(() => {\n      return visible.value && (!!filteredOptions.value.length || props.loading);\n    });\n    const hoveringId = computed(() => {\n      var _a;\n      return `${contentId.value}-${(_a = dropdownRef.value) == null ? void 0 : _a.hoveringIndex}`;\n    });\n    const handleInputChange = value => {\n      emit(UPDATE_MODEL_EVENT, value);\n      emit(INPUT_EVENT, value);\n      syncAfterCursorMove();\n    };\n    const handleInputKeyDown = event => {\n      var _a, _b, _c, _d;\n      if (!(\"code\" in event) || ((_a = elInputRef.value) == null ? void 0 : _a.isComposing)) return;\n      switch (event.code) {\n        case EVENT_CODE.left:\n        case EVENT_CODE.right:\n          syncAfterCursorMove();\n          break;\n        case EVENT_CODE.up:\n        case EVENT_CODE.down:\n          if (!visible.value) return;\n          event.preventDefault();\n          (_b = dropdownRef.value) == null ? void 0 : _b.navigateOptions(event.code === EVENT_CODE.up ? \"prev\" : \"next\");\n          break;\n        case EVENT_CODE.enter:\n        case EVENT_CODE.numpadEnter:\n          if (!visible.value) return;\n          event.preventDefault();\n          if ((_c = dropdownRef.value) == null ? void 0 : _c.hoverOption) {\n            (_d = dropdownRef.value) == null ? void 0 : _d.selectHoverOption();\n          } else {\n            visible.value = false;\n          }\n          break;\n        case EVENT_CODE.esc:\n          if (!visible.value) return;\n          event.preventDefault();\n          visible.value = false;\n          break;\n        case EVENT_CODE.backspace:\n          if (props.whole && mentionCtx.value) {\n            const {\n              splitIndex,\n              selectionEnd,\n              pattern,\n              prefixIndex,\n              prefix\n            } = mentionCtx.value;\n            const inputEl = getInputEl();\n            if (!inputEl) return;\n            const inputValue = inputEl.value;\n            const matchOption = props.options.find(item => item.value === pattern);\n            const isWhole = isFunction(props.checkIsWhole) ? props.checkIsWhole(pattern, prefix) : matchOption;\n            if (isWhole && splitIndex !== -1 && splitIndex + 1 === selectionEnd) {\n              event.preventDefault();\n              const newValue = inputValue.slice(0, prefixIndex) + inputValue.slice(splitIndex + 1);\n              emit(UPDATE_MODEL_EVENT, newValue);\n              emit(INPUT_EVENT, newValue);\n              const newSelectionEnd = prefixIndex;\n              nextTick(() => {\n                inputEl.selectionStart = newSelectionEnd;\n                inputEl.selectionEnd = newSelectionEnd;\n                syncDropdownVisible();\n              });\n            }\n          }\n      }\n    };\n    const {\n      wrapperRef\n    } = useFocusController(elInputRef, {\n      beforeFocus() {\n        return disabled.value;\n      },\n      afterFocus() {\n        syncAfterCursorMove();\n      },\n      beforeBlur(event) {\n        var _a;\n        return (_a = tooltipRef.value) == null ? void 0 : _a.isFocusInsideContent(event);\n      },\n      afterBlur() {\n        visible.value = false;\n      }\n    });\n    const handleInputMouseDown = () => {\n      syncAfterCursorMove();\n    };\n    const handleSelect = item => {\n      if (!mentionCtx.value) return;\n      const inputEl = getInputEl();\n      if (!inputEl) return;\n      const inputValue = inputEl.value;\n      const {\n        split\n      } = props;\n      const newEndPart = inputValue.slice(mentionCtx.value.end);\n      const alreadySeparated = newEndPart.startsWith(split);\n      const newMiddlePart = `${item.value}${alreadySeparated ? \"\" : split}`;\n      const newValue = inputValue.slice(0, mentionCtx.value.start) + newMiddlePart + newEndPart;\n      emit(UPDATE_MODEL_EVENT, newValue);\n      emit(INPUT_EVENT, newValue);\n      emit(\"select\", item, mentionCtx.value.prefix);\n      const newSelectionEnd = mentionCtx.value.start + newMiddlePart.length + (alreadySeparated ? 1 : 0);\n      nextTick(() => {\n        inputEl.selectionStart = newSelectionEnd;\n        inputEl.selectionEnd = newSelectionEnd;\n        inputEl.focus();\n        syncDropdownVisible();\n      });\n    };\n    const getInputEl = () => {\n      var _a, _b;\n      return props.type === \"textarea\" ? (_a = elInputRef.value) == null ? void 0 : _a.textarea : (_b = elInputRef.value) == null ? void 0 : _b.input;\n    };\n    const syncAfterCursorMove = () => {\n      setTimeout(() => {\n        syncCursor();\n        syncDropdownVisible();\n        nextTick(() => {\n          var _a;\n          return (_a = tooltipRef.value) == null ? void 0 : _a.updatePopper();\n        });\n      }, 0);\n    };\n    const syncCursor = () => {\n      const inputEl = getInputEl();\n      if (!inputEl) return;\n      const caretPosition = getCursorPosition(inputEl);\n      const inputRect = inputEl.getBoundingClientRect();\n      const elInputRect = elInputRef.value.$el.getBoundingClientRect();\n      cursorStyle.value = {\n        position: \"absolute\",\n        width: 0,\n        height: `${caretPosition.height}px`,\n        left: `${caretPosition.left + inputRect.left - elInputRect.left}px`,\n        top: `${caretPosition.top + inputRect.top - elInputRect.top}px`\n      };\n    };\n    const syncDropdownVisible = () => {\n      const inputEl = getInputEl();\n      if (document.activeElement !== inputEl) {\n        visible.value = false;\n        return;\n      }\n      const {\n        prefix,\n        split\n      } = props;\n      mentionCtx.value = getMentionCtx(inputEl, prefix, split);\n      if (mentionCtx.value && mentionCtx.value.splitIndex === -1) {\n        visible.value = true;\n        emit(\"search\", mentionCtx.value.pattern, mentionCtx.value.prefix);\n        return;\n      }\n      visible.value = false;\n    };\n    expose({\n      input: elInputRef,\n      tooltip: tooltipRef,\n      dropdownVisible\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"wrapperRef\",\n        ref: wrapperRef,\n        class: normalizeClass(unref(ns).b())\n      }, [createVNode(unref(ElInput), mergeProps(mergeProps(unref(passInputProps), _ctx.$attrs), {\n        ref_key: \"elInputRef\",\n        ref: elInputRef,\n        \"model-value\": _ctx.modelValue,\n        disabled: unref(disabled),\n        role: unref(dropdownVisible) ? \"combobox\" : void 0,\n        \"aria-activedescendant\": unref(dropdownVisible) ? unref(hoveringId) || \"\" : void 0,\n        \"aria-controls\": unref(dropdownVisible) ? unref(contentId) : void 0,\n        \"aria-expanded\": unref(dropdownVisible) || void 0,\n        \"aria-label\": _ctx.ariaLabel,\n        \"aria-autocomplete\": unref(dropdownVisible) ? \"none\" : void 0,\n        \"aria-haspopup\": unref(dropdownVisible) ? \"listbox\" : void 0,\n        onInput: handleInputChange,\n        onKeydown: handleInputKeyDown,\n        onMousedown: handleInputMouseDown\n      }), createSlots({\n        _: 2\n      }, [renderList(_ctx.$slots, (_, name) => {\n        return {\n          name,\n          fn: withCtx(slotProps => [renderSlot(_ctx.$slots, name, normalizeProps(guardReactiveProps(slotProps)))])\n        };\n      })]), 1040, [\"model-value\", \"disabled\", \"role\", \"aria-activedescendant\", \"aria-controls\", \"aria-expanded\", \"aria-label\", \"aria-autocomplete\", \"aria-haspopup\"]), createVNode(unref(ElTooltip), {\n        ref_key: \"tooltipRef\",\n        ref: tooltipRef,\n        visible: unref(dropdownVisible),\n        \"popper-class\": [unref(ns).e(\"popper\"), _ctx.popperClass],\n        \"popper-options\": _ctx.popperOptions,\n        placement: unref(computedPlacement),\n        \"fallback-placements\": unref(computedFallbackPlacements),\n        effect: \"light\",\n        pure: \"\",\n        offset: _ctx.offset,\n        \"show-arrow\": _ctx.showArrow\n      }, {\n        default: withCtx(() => [createElementVNode(\"div\", {\n          style: normalizeStyle(cursorStyle.value)\n        }, null, 4)]),\n        content: withCtx(() => {\n          var _a;\n          return [createVNode(ElMentionDropdown, {\n            ref_key: \"dropdownRef\",\n            ref: dropdownRef,\n            options: unref(filteredOptions),\n            disabled: unref(disabled),\n            loading: _ctx.loading,\n            \"content-id\": unref(contentId),\n            \"aria-label\": _ctx.ariaLabel,\n            onSelect: handleSelect,\n            onClick: withModifiers((_a = elInputRef.value) == null ? void 0 : _a.focus, [\"stop\"])\n          }, createSlots({\n            _: 2\n          }, [renderList(_ctx.$slots, (_, name) => {\n            return {\n              name,\n              fn: withCtx(slotProps => [renderSlot(_ctx.$slots, name, normalizeProps(guardReactiveProps(slotProps)))])\n            };\n          })]), 1032, [\"options\", \"disabled\", \"loading\", \"content-id\", \"aria-label\", \"onClick\"])];\n        }),\n        _: 3\n      }, 8, [\"visible\", \"popper-class\", \"popper-options\", \"placement\", \"fallback-placements\", \"offset\", \"show-arrow\"])], 2);\n    };\n  }\n});\nvar Mention = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"mention.vue\"]]);\nexport { Mention as default };", "map": {"version": 3, "names": ["name", "inheritAttrs", "passInputProps", "computed", "pick", "props", "Object", "keys", "inputProps", "ns", "useNamespace", "disabled", "useFormDisabled", "contentId", "useId", "elInputRef", "ref", "tooltipRef", "dropdownRef", "visible", "cursorStyle", "mentionCtx", "computedPlacement", "showArrow", "placement", "computedFallbackPlacements", "filteredOptions", "filterOption", "options", "value", "filter", "option", "pattern", "dropdownVisible", "length", "loading", "hoveringId", "_a", "hoveringIndex", "handleInputChange", "emit", "UPDATE_MODEL_EVENT", "INPUT_EVENT", "syncAfterCursorMove", "handleInputKeyDown", "event", "_b", "_c", "_d", "isComposing", "code", "EVENT_CODE", "left", "right", "up", "down", "preventDefault", "navigateOptions", "enter", "numpadEnter", "hoverOption", "selectHoverOption", "esc", "backspace", "whole", "splitIndex", "selectionEnd", "prefixIndex", "prefix", "inputEl", "getInputEl", "inputValue", "matchOption", "find", "item", "isWhole", "isFunction", "checkIsWhole", "newValue", "slice", "newSelectionEnd", "nextTick", "selectionStart", "syncDropdownVisible", "wrapperRef", "useFocusController", "beforeFocus", "afterFocus", "beforeBlur", "isFocusInsideContent", "after<PERSON><PERSON>r", "handleInputMouseDown", "handleSelect", "split", "newEndPart", "end", "alreadySeparated", "startsWith", "newMiddlePart", "start", "focus", "type", "textarea", "input", "setTimeout", "syncCursor", "updatePopper", "caretPosition", "getCursorPosition", "inputRect", "getBoundingClientRect", "elInputRect", "$el", "position", "width", "height", "top", "document", "activeElement", "getMentionCtx"], "sources": ["../../../../../../packages/components/mention/src/mention.vue"], "sourcesContent": ["<template>\n  <div ref=\"wrapperRef\" :class=\"ns.b()\">\n    <el-input\n      v-bind=\"mergeProps(passInputProps, $attrs)\"\n      ref=\"elInputRef\"\n      :model-value=\"modelValue\"\n      :disabled=\"disabled\"\n      :role=\"dropdownVisible ? 'combobox' : undefined\"\n      :aria-activedescendant=\"dropdownVisible ? hoveringId || '' : undefined\"\n      :aria-controls=\"dropdownVisible ? contentId : undefined\"\n      :aria-expanded=\"dropdownVisible || undefined\"\n      :aria-label=\"ariaLabel\"\n      :aria-autocomplete=\"dropdownVisible ? 'none' : undefined\"\n      :aria-haspopup=\"dropdownVisible ? 'listbox' : undefined\"\n      @input=\"handleInputChange\"\n      @keydown=\"handleInputKeyDown\"\n      @mousedown=\"handleInputMouseDown\"\n    >\n      <template v-for=\"(_, name) in $slots\" #[name]=\"slotProps\">\n        <slot :name=\"name\" v-bind=\"slotProps\" />\n      </template>\n    </el-input>\n    <el-tooltip\n      ref=\"tooltipRef\"\n      :visible=\"dropdownVisible\"\n      :popper-class=\"[ns.e('popper'), popperClass]\"\n      :popper-options=\"popperOptions\"\n      :placement=\"computedPlacement\"\n      :fallback-placements=\"computedFallbackPlacements\"\n      effect=\"light\"\n      pure\n      :offset=\"offset\"\n      :show-arrow=\"showArrow\"\n    >\n      <template #default>\n        <div :style=\"cursorStyle\" />\n      </template>\n      <template #content>\n        <el-mention-dropdown\n          ref=\"dropdownRef\"\n          :options=\"filteredOptions\"\n          :disabled=\"disabled\"\n          :loading=\"loading\"\n          :content-id=\"contentId\"\n          :aria-label=\"ariaLabel\"\n          @select=\"handleSelect\"\n          @click.stop=\"elInputRef?.focus\"\n        >\n          <template v-for=\"(_, name) in $slots\" #[name]=\"slotProps\">\n            <slot :name=\"name\" v-bind=\"slotProps\" />\n          </template>\n        </el-mention-dropdown>\n      </template>\n    </el-tooltip>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, mergeProps, nextTick, ref } from 'vue'\nimport { pick } from 'lodash-unified'\nimport { useFocusController, useId, useNamespace } from '@element-plus/hooks'\nimport ElInput, { inputProps } from '@element-plus/components/input'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport {\n  EVENT_CODE,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { isFunction } from '@element-plus/utils'\nimport { mentionEmits, mentionProps } from './mention'\nimport { getCursorPosition, getMentionCtx } from './helper'\nimport ElMentionDropdown from './mention-dropdown.vue'\n\nimport type { Placement } from '@popperjs/core'\nimport type { CSSProperties } from 'vue'\nimport type { InputInstance } from '@element-plus/components/input'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\nimport type { MentionCtx, MentionOption } from './types'\n\ndefineOptions({\n  name: 'ElMention',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(mentionProps)\nconst emit = defineEmits(mentionEmits)\n\nconst passInputProps = computed(() => pick(props, Object.keys(inputProps)))\n\nconst ns = useNamespace('mention')\nconst disabled = useFormDisabled()\nconst contentId = useId()\n\nconst elInputRef = ref<InputInstance>()\nconst tooltipRef = ref<TooltipInstance>()\nconst dropdownRef = ref<InstanceType<typeof ElMentionDropdown>>()\n\nconst visible = ref(false)\nconst cursorStyle = ref<CSSProperties>()\nconst mentionCtx = ref<MentionCtx>()\n\nconst computedPlacement = computed<Placement>(() =>\n  props.showArrow ? props.placement : `${props.placement}-start`\n)\n\nconst computedFallbackPlacements = computed<Placement[]>(() =>\n  props.showArrow ? ['bottom', 'top'] : ['bottom-start', 'top-start']\n)\n\nconst filteredOptions = computed(() => {\n  const { filterOption, options } = props\n  if (!mentionCtx.value || !filterOption) return options\n  return options.filter((option) =>\n    filterOption(mentionCtx.value!.pattern, option)\n  )\n})\n\nconst dropdownVisible = computed(() => {\n  return visible.value && (!!filteredOptions.value.length || props.loading)\n})\n\nconst hoveringId = computed(() => {\n  return `${contentId.value}-${dropdownRef.value?.hoveringIndex}`\n})\n\nconst handleInputChange = (value: string) => {\n  emit(UPDATE_MODEL_EVENT, value)\n  emit(INPUT_EVENT, value)\n  syncAfterCursorMove()\n}\n\nconst handleInputKeyDown = (event: KeyboardEvent | Event) => {\n  if (!('code' in event) || elInputRef.value?.isComposing) return\n\n  switch (event.code) {\n    case EVENT_CODE.left:\n    case EVENT_CODE.right:\n      syncAfterCursorMove()\n      break\n    case EVENT_CODE.up:\n    case EVENT_CODE.down:\n      if (!visible.value) return\n      event.preventDefault()\n      dropdownRef.value?.navigateOptions(\n        event.code === EVENT_CODE.up ? 'prev' : 'next'\n      )\n      break\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n      if (!visible.value) return\n      event.preventDefault()\n      if (dropdownRef.value?.hoverOption) {\n        dropdownRef.value?.selectHoverOption()\n      } else {\n        visible.value = false\n      }\n      break\n    case EVENT_CODE.esc:\n      if (!visible.value) return\n      event.preventDefault()\n      visible.value = false\n      break\n    case EVENT_CODE.backspace:\n      if (props.whole && mentionCtx.value) {\n        const { splitIndex, selectionEnd, pattern, prefixIndex, prefix } =\n          mentionCtx.value\n        const inputEl = getInputEl()\n        if (!inputEl) return\n        const inputValue = inputEl.value\n        const matchOption = props.options.find((item) => item.value === pattern)\n        const isWhole = isFunction(props.checkIsWhole)\n          ? props.checkIsWhole(pattern, prefix)\n          : matchOption\n        if (isWhole && splitIndex !== -1 && splitIndex + 1 === selectionEnd) {\n          event.preventDefault()\n          const newValue =\n            inputValue.slice(0, prefixIndex) + inputValue.slice(splitIndex + 1)\n          emit(UPDATE_MODEL_EVENT, newValue)\n          emit(INPUT_EVENT, newValue)\n\n          const newSelectionEnd = prefixIndex\n          nextTick(() => {\n            // input value is updated\n            inputEl.selectionStart = newSelectionEnd\n            inputEl.selectionEnd = newSelectionEnd\n            syncDropdownVisible()\n          })\n        }\n      }\n  }\n}\n\nconst { wrapperRef } = useFocusController(elInputRef, {\n  beforeFocus() {\n    return disabled.value\n  },\n  afterFocus() {\n    syncAfterCursorMove()\n  },\n  beforeBlur(event) {\n    return tooltipRef.value?.isFocusInsideContent(event)\n  },\n  afterBlur() {\n    visible.value = false\n  },\n})\n\nconst handleInputMouseDown = () => {\n  syncAfterCursorMove()\n}\n\nconst handleSelect = (item: MentionOption) => {\n  if (!mentionCtx.value) return\n  const inputEl = getInputEl()\n  if (!inputEl) return\n  const inputValue = inputEl.value\n  const { split } = props\n\n  const newEndPart = inputValue.slice(mentionCtx.value.end)\n  const alreadySeparated = newEndPart.startsWith(split)\n  const newMiddlePart = `${item.value}${alreadySeparated ? '' : split}`\n\n  const newValue =\n    inputValue.slice(0, mentionCtx.value.start) + newMiddlePart + newEndPart\n\n  emit(UPDATE_MODEL_EVENT, newValue)\n  emit(INPUT_EVENT, newValue)\n  emit('select', item, mentionCtx.value.prefix)\n\n  const newSelectionEnd =\n    mentionCtx.value.start + newMiddlePart.length + (alreadySeparated ? 1 : 0)\n\n  nextTick(() => {\n    // input value is updated\n    inputEl.selectionStart = newSelectionEnd\n    inputEl.selectionEnd = newSelectionEnd\n    inputEl.focus()\n    syncDropdownVisible()\n  })\n}\n\nconst getInputEl = () =>\n  props.type === 'textarea'\n    ? elInputRef.value?.textarea\n    : elInputRef.value?.input\n\nconst syncAfterCursorMove = () => {\n  // can't use nextTick(), get cursor position will be wrong\n  setTimeout(() => {\n    syncCursor()\n    syncDropdownVisible()\n    nextTick(() => tooltipRef.value?.updatePopper())\n  }, 0)\n}\n\nconst syncCursor = () => {\n  const inputEl = getInputEl()\n  if (!inputEl) return\n\n  const caretPosition = getCursorPosition(inputEl)\n  const inputRect = inputEl.getBoundingClientRect()\n  const elInputRect = elInputRef.value!.$el.getBoundingClientRect()\n\n  cursorStyle.value = {\n    position: 'absolute',\n    width: 0,\n    height: `${caretPosition.height}px`,\n    left: `${caretPosition.left + inputRect.left - elInputRect.left}px`,\n    top: `${caretPosition.top + inputRect.top - elInputRect.top}px`,\n  }\n}\n\nconst syncDropdownVisible = () => {\n  const inputEl = getInputEl()\n  if (document.activeElement !== inputEl) {\n    visible.value = false\n    return\n  }\n  const { prefix, split } = props\n  mentionCtx.value = getMentionCtx(inputEl, prefix, split)\n  if (mentionCtx.value && mentionCtx.value.splitIndex === -1) {\n    visible.value = true\n    emit('search', mentionCtx.value.pattern, mentionCtx.value.prefix)\n    return\n  }\n  visible.value = false\n}\n\ndefineExpose({\n  input: elInputRef,\n  tooltip: tooltipRef,\n  dropdownVisible,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;mCAgFc;EACZA,IAAM;EACNC,YAAc;AAChB;;;;;;;;;;IAKM,MAAAC,cAAA,GAAiBC,QAAA,CAAS,MAAMC,IAAA,CAAKC,KAAA,EAAOC,MAAO,CAAAC,IAAA,CAAKC,UAAU,CAAC,CAAC;IAEpE,MAAAC,EAAA,GAAKC,YAAA,CAAa,SAAS;IACjC,MAAMC,QAAA,GAAWC,eAAgB;IACjC,MAAMC,SAAA,GAAYC,KAAM;IAExB,MAAMC,UAAA,GAAaC,GAAmB;IACtC,MAAMC,UAAA,GAAaD,GAAqB;IACxC,MAAME,WAAA,GAAcF,GAA4C;IAE1D,MAAAG,OAAA,GAAUH,GAAA,CAAI,KAAK;IACzB,MAAMI,WAAA,GAAcJ,GAAmB;IACvC,MAAMK,UAAA,GAAaL,GAAgB;IAEnC,MAAMM,iBAAoB,GAAAnB,QAAA,OAAAE,KAAA,CAAAkB,SAAA,GAAAlB,KAAA,CAAAmB,SAAA,MAAAnB,KAAA,CAAAmB,SAAA;IAAA,MAAAC,0BACA,GAAYtB,QAAA,OAAAE,KAAkB,CAAAkB,SAAA;IACxD,MAAAG,eAAA,GAAAvB,QAAA;MAEA,MAAmC;QAAAwB,YAAA;QAAAC;MAAA,IAAAvB,KAAA;MAAsB,KAAAgB,UACrC,CAAAQ,KAAA,IAAC,CAAAF,YAAe,EACpC,OAAAC,OAAA;MAEM,OAAAA,OAAA,CAAAE,MAAA,CAAkBC,MAAA,IAAeJ,YAAA,CAAAN,UAAA,CAAAQ,KAAA,CAAAG,OAAA,EAAAD,MAAA;IACrC,CAAM;IACN,MAAIE,eAAqB,GAAA9B,QAAA;MACzB,OAAOgB,OAAQ,CAAAU,KAAA,OAAAH,eAAA,CAAAG,KAAA,CAAAK,MAAA,IAAA7B,KAAA,CAAA8B,OAAA;IAAA;IAEf,MAAAC,UAAA,GAAAjC,QAAA;MACD,IAAAkC,EAAA;MAEK,UAAAxB,SAAA,CAAAgB,KAAA,KAAAQ,EAAiC,GAAAnB,WAAA,CAAAW,KAAA,qBAAAQ,EAAA,CAAAC,aAAA;IACrC;IACF,MAACC,iBAAA,GAAAV,KAAA;MAEKW,IAAA,CAAAC,kBAAA,EAAAZ,KAA4B;MAChCW,IAAA,CAAAE,WAAoB,EAAAb,KAAA;MACrBc,mBAAA;IAED,CAAM;IACJ,MAAAC,kBAAA,GAA8BC,KAAA;MAC9B,IAAAR,EAAA,EAAAS,EAAA,EAAAC,EAAA,EAAAC,EAAkB;MACE,gBAAAH,KAAA,OAAAR,EAAA,GAAAtB,UAAA,CAAAc,KAAA,qBAAAQ,EAAA,CAAAY,WAAA,GACtB;MAEM,QAAAJ,KAAA,CAAAK,IAAA;QACJ,KAAMC,UAAoB,CAAAC,IAAA;QAE1B,KAAAD,UAAoB,CAAAE,KAAA;UAAAV,mBACF;UAAA;QAEM,KAAAQ,UAAA,CAAAG,EAAA;QACpB,KAAAH,UAAA,CAAAI,IAAA;UAAA,IACc,CAAApC,OAAA,CAAAU,KAAA;UAEVgB,KAAC,CAAAW,cAAe;UACpB,CAAAV,EAAA,GAAM5B,WAAe,CAAAW,KAAA,qBAAAiB,EAAA,CAAAW,eAAA,CAAAZ,KAAA,CAAAK,IAAA,KAAAC,UAAA,CAAAG,EAAA;UACrB;QAAmB,KACXH,UAAA,CAAAO,KAAoB;QAC5B,KAAAP,UAAA,CAAAQ,WAAA;UACA,KAAAxC,OAAA,CAAAU,KAAA;UACcgB,KACA,CAAAW,cAAA;UACV,KAACT,EAAA,GAAA7B,WAAe,CAAAW,KAAA,qBAAAkB,EAAA,CAAAa,WAAA;YACpB,CAAAZ,EAAA,GAAqB9B,WAAA,CAAAW,KAAA,qBAAAmB,EAAA,CAAAa,iBAAA;UACrB,CAAI;YACF1C,OAAA,CAAAU,KAAA,QAAqC;UAAA;UAErC;QACF,KAAAsB,UAAA,CAAAW,GAAA;UACA,KAAA3C,OAAA,CAAAU,KAAA;UAEIgB,KAAC,CAAAW,cAAe;UACpBrC,OAAqB,CAAAU,KAAA;UACrB;QACA,KAAAsB,UAAA,CAAAY,SAAA;UAAA,IACc1D,KAAA,CAAA2D,KAAA,IAAA3C,UAAA,CAAAQ,KAAA;YACV;cAAMoC,UAAS;cAAAC,YAAkB;cAAAlC,OAAA;cAAAmC,WAAA;cAAAC;YAAA,IAAA/C,UAAA,CAAAQ,KAAA;YACnC,MAAMwC,OAAc,GAAAC,UAAA;YAEpB,KAAAD,OAAA,EACA;YACA,MAAME,UAAA,GAAaF,OAAQ,CAAAxC,KAAA;YACrB,MAAA2C,WAAA,GAAcnE,KAAA,CAAMuB,OAAQ,CAAA6C,IAAA,CAAMC,IAAS,IAAAA,IAAA,CAAK7C,KAAA,KAAUG,OAAO;YACjE,MAAA2C,OAAA,GAAUC,UAAA,CAAWvE,KAAM,CAAAwE,YAAY,IACzCxE,KAAM,CAAAwE,YAAA,CAAa7C,OAAS,EAAAoC,MAAM,CAClC,GAAAI,WAAA;YACJ,IAAIG,OAAW,IAAAV,UAAA,KAAe,CAAM,KAAAA,UAAA,GAAa,MAAMC,YAAc;cACnErB,KAAA,CAAMW,cAAe;cACf,MAAAsB,QAAA,GACJP,UAAA,CAAWQ,KAAM,IAAGZ,WAAW,CAAI,GAAAI,UAAA,CAAWQ,KAAM,CAAAd,UAAA,GAAa,CAAC;cACpEzB,IAAA,CAAKC,kBAAA,EAAoBqC,QAAQ;cACjCtC,IAAA,CAAKE,WAAA,EAAaoC,QAAQ;cAE1B,MAAME,eAAkB,GAAAb,WAAA;cACxBc,QAAA,CAAS,MAAM;gBAEbZ,OAAA,CAAQa,cAAiB,GAAAF,eAAA;gBACzBX,OAAA,CAAQH,YAAe,GAAAc,eAAA;gBACHG,mBAAA;cAAA,CACrB;YAAA;UACH;MACF;IACJ,CACF;IAEA,MAAM;MAAEC;IAAA,CAAe,GAAAC,kBAAA,CAAmBtE,UAAY;MACpDuE,WAAcA,CAAA;QACZ,OAAO3E,QAAS,CAAAkB,KAAA;MAAA,CAClB;MACA0D,UAAaA,CAAA;QACS5C,mBAAA;MAAA,CACtB;MACA6C,WAAW3C,KAAO;QACT,IAAAR,EAAA;QACT,QAAAA,EAAA,GAAApB,UAAA,CAAAY,KAAA,qBAAAQ,EAAA,CAAAoD,oBAAA,CAAA5C,KAAA;MAAA,CACY;MACV6C,UAAA,EAAgB;QAClBvE,OAAA,CAAAU,KAAA;MAAA;IAGF;IACsB,MAAA8D,oBAAA,GAAAA,CAAA;MACtBhD,mBAAA;IAEA,CAAM;IACA,MAAAiD,YAAY,GAAOlB,IAAA;MACvB,KAAArD,UAAA,CAAgBQ,KAAW,EAC3B;MACA,MAAMwC,OAAA,GAAAC,UAAqB;MACrB,KAAAD,OAAA,EAEN;MACM,MAAAE,UAAA,GAAAF,OAA8B,CAAAxC,KAAA;MACpC,MAAM;QAAAgE;MAAA,IAAAxF,KAAmB;MAEnB,MAAAyF,UAAA,GAAAvB,UACa,CAAAQ,KAAA,CAAA1D,UAAA,CAAAQ,KAAoB,CAAAkE,GAAA;MAEvC,MAAAC,gBAAA,GAAyBF,UAAQ,CAAAG,UAAA,CAAAJ,KAAA;MACjC,MAAAK,aAA0B,MAAAxB,IAAA,CAAA7C,KAAA,GAAAmE,gBAAA,QAAAH,KAAA;MAC1B,MAAef,QAAA,GAAAP,UAAiB,CAAAQ,KAAA,IAAA1D,UAAY,CAAAQ,KAAA,CAAAsE,KAAA,IAAAD,aAAA,GAAAJ,UAAA;MAE5CtD,IAAA,CAAAC,kBAAA,EACaqC,QAAA;MAEbtC,IAAA,CAAAE,WAAe,EAAAoC,QAAA;MAEbtC,IAAA,SAAyB,EAAAkC,IAAA,EAAArD,UAAA,CAAAQ,KAAA,CAAAuC,MAAA;MACzB,MAAAY,eAAuB,GAAA3D,UAAA,CAAAQ,KAAA,CAAAsE,KAAA,GAAAD,aAAA,CAAAhE,MAAA,IAAA8D,gBAAA;MACvBf,QAAA,OAAc;QACMZ,OAAA,CAAAa,cAAA,GAAAF,eAAA;QACrBX,OAAA,CAAAH,YAAA,GAAAc,eAAA;QACHX,OAAA,CAAA+B,KAAA;QAEMjB,mBAAA,EACJ;MAIF;IAEE;IACa,MAAAb,UAAA,GAAAA,CAAA;MACS,IAAAjC,EAAA,EAAAS,EAAA;MACpB,OAAAzC,KAAe,CAAAgG,IAAA,eAAkB,IAAAhE,EAAA,GAAAtB,UAAc,CAAAc,KAAA,qBAAAQ,EAAA,CAAAiE,QAAA,IAAAxD,EAAA,GAAA/B,UAAA,CAAAc,KAAA,qBAAAiB,EAAA,CAAAyD,KAAA;IAAA;IAEnD,MAAA5D,mBAAA,GAAAA,CAAA;MAEA6D,UAAA,OAAmB;QACjBC,UAAA;QACAtB,mBAAc;QAERF,QAAA;UACA,IAAA5C,EAAA;UACN,OAAoB,CAAAA,EAAA,GAAApB,UAAA,CAAAY,KAAkB,SAA0B,YAAAQ,EAAA,CAAAqE,YAAA;QAEhE;MAAoB,GACR;IAAA;IAEV,MAAAD,UAAW,GAAAA,CAAA;MAAoB,MAAApC,OACtB,GAAAC,UAAA;MAAsD,KAAAD,OACvD,EACV;MACF,MAAAsC,aAAA,GAAAC,iBAAA,CAAAvC,OAAA;MAEA,MAAAwC,SAAA,GAAAxC,OAAA,CAA4ByC,qBAAM;MAChC,MAAMC,WAAqB,GAAAhG,UAAA,CAAAc,KAAA,CAAAmF,GAAA,CAAAF,qBAAA;MACvB1F,WAAA,CAAAS,KAAA;QACFoF,QAAQ,EAAQ;QAChBC,KAAA;QACFC,MAAA,KAAAR,aAAA,CAAAQ,MAAA;QACM/D,IAAA,EAAE,GAAQuD,aAAU,CAAAvD,IAAA,GAAAyD,SAAA,CAAAzD,IAAA,GAAA2D,WAAA,CAAA3D,IAAA;QAC1BgE,GAAA,KAAAT,aAAmB,CAAAS,GAAA,GAAAP,SAAuB,CAAAO,GAAA,GAAAL,WAAa,CAAAK,GAAA;MACvD;IACE;IACA,MAAAjC,mBAA0B,GAAAA,CAAA;MAC1B,MAAAd,OAAA,GAAAC,UAAA;MACF,IAAA+C,QAAA,CAAAC,aAAA,KAAAjD,OAAA;QACAlD,OAAgB,CAAAU,KAAA;QAClB;MAEA;MACE,MAAO;QAAAuC,MAAA;QAAAyB;MAAA,IAAAxF,KAAA;MACPgB,UAAS,CAAAQ,KAAA,GAAA0F,aAAA,CAAAlD,OAAA,EAAAD,MAAA,EAAAyB,KAAA;MACT,IAAAxE,UAAA,CAAAQ,KAAA,IAAAR,UAAA,CAAAQ,KAAA,CAAAoC,UAAA;QACD9C,OAAA,CAAAU,KAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}