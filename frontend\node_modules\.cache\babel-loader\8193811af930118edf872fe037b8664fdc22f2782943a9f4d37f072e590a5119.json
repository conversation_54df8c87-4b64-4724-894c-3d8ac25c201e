{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"user-profile\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"loading-container\"\n};\nconst _hoisted_4 = {\n  key: 1\n};\nconst _hoisted_5 = {\n  class: \"security-options\"\n};\nconst _hoisted_6 = {\n  class: \"security-item\"\n};\nconst _hoisted_7 = {\n  class: \"security-item\"\n};\nconst _hoisted_8 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_9 = {\n  class: \"key-management\"\n};\nconst _hoisted_10 = {\n  class: \"key-status\"\n};\nconst _hoisted_11 = {\n  key: 0\n};\nconst _hoisted_12 = {\n  class: \"key-actions\"\n};\nconst _hoisted_13 = {\n  key: 0,\n  class: \"new-key-pair\"\n};\nconst _hoisted_14 = {\n  class: \"key-box\"\n};\nconst _hoisted_15 = {\n  class: \"key-label\"\n};\nconst _hoisted_16 = {\n  class: \"key-box\"\n};\nconst _hoisted_17 = {\n  class: \"key-label\"\n};\nconst _hoisted_18 = {\n  class: \"save-key-action\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"profile-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[14] || (_cache[14] = _createElementVNode(\"h3\", null, \"个人信息\", -1 /* HOISTED */)), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[0] || (_cache[0] = $event => $setup.editMode = !$setup.editMode)\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.editMode ? '取消编辑' : '编辑信息'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    })])]),\n    default: _withCtx(() => [$setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createVNode(_component_el_skeleton, {\n      rows: 5,\n      animated: \"\"\n    })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createVNode(_component_el_form, {\n      ref: \"formRef\",\n      model: $setup.form,\n      rules: $setup.rules,\n      \"label-width\": \"100px\",\n      disabled: !$setup.editMode\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"学号\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userInfo.studentIdHash,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.userInfo.studentIdHash = $event),\n          disabled: \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"]), _cache[15] || (_cache[15] = _createElementVNode(\"small\", {\n          class: \"form-hint\"\n        }, \"学号信息不可修改\", -1 /* HOISTED */))]),\n        _: 1 /* STABLE */,\n        __: [15]\n      }), _createVNode(_component_el_form_item, {\n        label: \"邮箱\",\n        prop: \"email\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.form.email,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.form.email = $event)\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"手机号\",\n        prop: \"phone\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.form.phone,\n          \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.form.phone = $event)\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"信誉分\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_progress, {\n          percentage: $setup.userInfo.creditScore,\n          color: $setup.creditScoreColor,\n          format: $setup.formatCreditScore,\n          \"stroke-width\": 18\n        }, null, 8 /* PROPS */, [\"percentage\", \"color\", \"format\"]), _cache[16] || (_cache[16] = _createElementVNode(\"small\", {\n          class: \"form-hint\"\n        }, \"信誉分影响您的预约权限\", -1 /* HOISTED */))]),\n        _: 1 /* STABLE */,\n        __: [16]\n      }), _createVNode(_component_el_form_item, {\n        label: \"注册时间\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userInfo.createdAt,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.userInfo.createdAt = $event),\n          disabled: \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"最后登录\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userInfo.lastLogin,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.userInfo.lastLogin = $event),\n          disabled: \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), $setup.editMode ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.updateProfile,\n          loading: $setup.updating\n        }, {\n          default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\" 保存修改 \")])),\n          _: 1 /* STABLE */,\n          __: [17]\n        }, 8 /* PROPS */, [\"onClick\", \"loading\"])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\", \"disabled\"])]))]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_card, {\n    class: \"profile-card\"\n  }, {\n    header: _withCtx(() => _cache[18] || (_cache[18] = [_createElementVNode(\"div\", {\n      class: \"card-header\"\n    }, [_createElementVNode(\"h3\", null, \"安全设置\")], -1 /* HOISTED */)])),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[20] || (_cache[20] = _createElementVNode(\"div\", {\n      class: \"security-info\"\n    }, [_createElementVNode(\"h4\", null, \"修改密码\"), _createElementVNode(\"p\", null, \"定期修改密码可以提高账号安全性\")], -1 /* HOISTED */)), _createVNode(_component_el_button, {\n      onClick: $setup.showChangePasswordDialog\n    }, {\n      default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\"修改\")])),\n      _: 1 /* STABLE */,\n      __: [19]\n    }, 8 /* PROPS */, [\"onClick\"])]), _createElementVNode(\"div\", _hoisted_7, [_cache[22] || (_cache[22] = _createElementVNode(\"div\", {\n      class: \"security-info\"\n    }, [_createElementVNode(\"h4\", null, \"SM2密钥管理\"), _createElementVNode(\"p\", null, \"管理您的SM2密钥对，用于安全登录和数据签名\")], -1 /* HOISTED */)), _createVNode(_component_el_button, {\n      onClick: $setup.showKeyManagementDialog\n    }, {\n      default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"管理\")])),\n      _: 1 /* STABLE */,\n      __: [21]\n    }, 8 /* PROPS */, [\"onClick\"])])])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 修改密码对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.passwordDialogVisible,\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.passwordDialogVisible = $event),\n    title: \"修改密码\",\n    width: \"400px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_8, [_createVNode(_component_el_button, {\n      onClick: _cache[9] || (_cache[9] = $event => $setup.passwordDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [23]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.changePassword,\n      loading: $setup.changingPassword\n    }, {\n      default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\" 确认修改 \")])),\n      _: 1 /* STABLE */,\n      __: [24]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      ref: \"passwordFormRef\",\n      model: $setup.passwordForm,\n      rules: $setup.passwordRules,\n      \"label-width\": \"100px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"当前密码\",\n        prop: \"oldPassword\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.passwordForm.oldPassword,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.passwordForm.oldPassword = $event),\n          type: \"password\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"新密码\",\n        prop: \"newPassword\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.passwordForm.newPassword,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.passwordForm.newPassword = $event),\n          type: \"password\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"确认密码\",\n        prop: \"confirmPassword\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.passwordForm.confirmPassword,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.passwordForm.confirmPassword = $event),\n          type: \"password\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" SM2密钥管理对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.keyDialogVisible,\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $setup.keyDialogVisible = $event),\n    title: \"SM2密钥管理\",\n    width: \"500px\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_cache[25] || (_cache[25] = _createElementVNode(\"h4\", null, \"当前状态\", -1 /* HOISTED */)), _createElementVNode(\"p\", null, [_createVNode(_component_el_tag, {\n      type: $setup.userInfo.hasPublicKey ? 'success' : 'danger'\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userInfo.hasPublicKey ? '已设置SM2密钥' : '未设置SM2密钥'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"]), $setup.userInfo.publicKeyExpires ? (_openBlock(), _createElementBlock(\"span\", _hoisted_11, \" (过期时间: \" + _toDisplayString($setup.userInfo.publicKeyExpires) + \") \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.generateNewKeyPair\n    }, {\n      default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\" 生成新密钥对 \")])),\n      _: 1 /* STABLE */,\n      __: [26]\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      type: \"danger\",\n      onClick: $setup.removePublicKey,\n      disabled: !$setup.userInfo.hasPublicKey\n    }, {\n      default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\" 移除公钥 \")])),\n      _: 1 /* STABLE */,\n      __: [27]\n    }, 8 /* PROPS */, [\"onClick\", \"disabled\"])]), $setup.newKeyPair.publicKey ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_cache[33] || (_cache[33] = _createElementVNode(\"h4\", null, \"新生成的密钥对\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_cache[29] || (_cache[29] = _createElementVNode(\"span\", null, \"公钥\", -1 /* HOISTED */)), _createVNode(_component_el_button, {\n      type: \"primary\",\n      size: \"small\",\n      onClick: $setup.copyPublicKey\n    }, {\n      default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\" 复制 \")])),\n      _: 1 /* STABLE */,\n      __: [28]\n    }, 8 /* PROPS */, [\"onClick\"])]), _createVNode(_component_el_input, {\n      modelValue: $setup.newKeyPair.publicKey,\n      \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.newKeyPair.publicKey = $event),\n      type: \"textarea\",\n      rows: 3,\n      readonly: \"\"\n    }, null, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_cache[31] || (_cache[31] = _createElementVNode(\"span\", null, \"私钥\", -1 /* HOISTED */)), _createVNode(_component_el_button, {\n      type: \"primary\",\n      size: \"small\",\n      onClick: $setup.copyPrivateKey\n    }, {\n      default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\" 复制 \")])),\n      _: 1 /* STABLE */,\n      __: [30]\n    }, 8 /* PROPS */, [\"onClick\"])]), _createVNode(_component_el_input, {\n      modelValue: $setup.newKeyPair.privateKey,\n      \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $setup.newKeyPair.privateKey = $event),\n      type: \"textarea\",\n      rows: 3,\n      readonly: \"\",\n      \"show-password\": \"\"\n    }, null, 8 /* PROPS */, [\"modelValue\"])]), _createVNode(_component_el_alert, {\n      title: \"重要提示\",\n      type: \"warning\",\n      description: \"请务必保存您的私钥，私钥将不会存储在服务器上，丢失后无法找回。\",\n      \"show-icon\": \"\",\n      closable: false,\n      class: \"key-warning\"\n    }), _createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_el_button, {\n      type: \"success\",\n      onClick: $setup.saveNewPublicKey\n    }, {\n      default: _withCtx(() => _cache[32] || (_cache[32] = [_createTextVNode(\" 保存新公钥到服务器 \")])),\n      _: 1 /* STABLE */,\n      __: [32]\n    }, 8 /* PROPS */, [\"onClick\"])])])) : _createCommentVNode(\"v-if\", true)])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "_cache", "$event", "$setup", "editMode", "default", "_createTextVNode", "_toDisplayString", "_", "loading", "_hoisted_3", "_component_el_skeleton", "rows", "animated", "_hoisted_4", "_component_el_form", "ref", "model", "form", "rules", "disabled", "_component_el_form_item", "label", "_component_el_input", "modelValue", "userInfo", "studentIdHash", "__", "prop", "email", "phone", "_component_el_progress", "percentage", "creditScore", "color", "creditScoreColor", "format", "formatCreditScore", "createdAt", "lastLogin", "_createBlock", "updateProfile", "updating", "_createCommentVNode", "_hoisted_5", "_hoisted_6", "showChangePasswordDialog", "_hoisted_7", "showKeyManagementDialog", "_component_el_dialog", "passwordDialogVisible", "title", "width", "footer", "_hoisted_8", "changePassword", "changingPassword", "passwordForm", "passwordRules", "oldPassword", "newPassword", "confirmPassword", "keyDialogVisible", "_hoisted_9", "_hoisted_10", "_component_el_tag", "hasPublic<PERSON>ey", "publicKeyExpires", "_hoisted_11", "_hoisted_12", "generateNewKeyPair", "removePublic<PERSON>ey", "newKeyPair", "public<PERSON>ey", "_hoisted_13", "_hoisted_14", "_hoisted_15", "size", "copyPublicKey", "readonly", "_hoisted_16", "_hoisted_17", "copyPrivateKey", "privateKey", "_component_el_alert", "description", "closable", "_hoisted_18", "saveNewPublicKey"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\UserProfile.vue"], "sourcesContent": ["<template>\n  <div class=\"user-profile\">\n    <el-card class=\"profile-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h3>个人信息</h3>\n          <el-button type=\"primary\" @click=\"editMode = !editMode\">\n            {{ editMode ? '取消编辑' : '编辑信息' }}\n          </el-button>\n        </div>\n      </template>\n      \n      <div v-if=\"loading\" class=\"loading-container\">\n        <el-skeleton :rows=\"5\" animated />\n      </div>\n      \n      <div v-else>\n        <el-form\n          ref=\"formRef\"\n          :model=\"form\"\n          :rules=\"rules\"\n          label-width=\"100px\"\n          :disabled=\"!editMode\"\n        >\n          <el-form-item label=\"学号\">\n            <el-input v-model=\"userInfo.studentIdHash\" disabled />\n            <small class=\"form-hint\">学号信息不可修改</small>\n          </el-form-item>\n          \n          <el-form-item label=\"邮箱\" prop=\"email\">\n            <el-input v-model=\"form.email\" />\n          </el-form-item>\n          \n          <el-form-item label=\"手机号\" prop=\"phone\">\n            <el-input v-model=\"form.phone\" />\n          </el-form-item>\n          \n          <el-form-item label=\"信誉分\">\n            <el-progress\n              :percentage=\"userInfo.creditScore\"\n              :color=\"creditScoreColor\"\n              :format=\"formatCreditScore\"\n              :stroke-width=\"18\"\n            />\n            <small class=\"form-hint\">信誉分影响您的预约权限</small>\n          </el-form-item>\n          \n          <el-form-item label=\"注册时间\">\n            <el-input v-model=\"userInfo.createdAt\" disabled />\n          </el-form-item>\n          \n          <el-form-item label=\"最后登录\">\n            <el-input v-model=\"userInfo.lastLogin\" disabled />\n          </el-form-item>\n          \n          <el-form-item v-if=\"editMode\">\n            <el-button type=\"primary\" @click=\"updateProfile\" :loading=\"updating\">\n              保存修改\n            </el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n    </el-card>\n    \n    <el-card class=\"profile-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h3>安全设置</h3>\n        </div>\n      </template>\n      \n      <div class=\"security-options\">\n        <div class=\"security-item\">\n          <div class=\"security-info\">\n            <h4>修改密码</h4>\n            <p>定期修改密码可以提高账号安全性</p>\n          </div>\n          <el-button @click=\"showChangePasswordDialog\">修改</el-button>\n        </div>\n        \n        <div class=\"security-item\">\n          <div class=\"security-info\">\n            <h4>SM2密钥管理</h4>\n            <p>管理您的SM2密钥对，用于安全登录和数据签名</p>\n          </div>\n          <el-button @click=\"showKeyManagementDialog\">管理</el-button>\n        </div>\n      </div>\n    </el-card>\n    \n    <!-- 修改密码对话框 -->\n    <el-dialog\n      v-model=\"passwordDialogVisible\"\n      title=\"修改密码\"\n      width=\"400px\"\n    >\n      <el-form\n        ref=\"passwordFormRef\"\n        :model=\"passwordForm\"\n        :rules=\"passwordRules\"\n        label-width=\"100px\"\n      >\n        <el-form-item label=\"当前密码\" prop=\"oldPassword\">\n          <el-input\n            v-model=\"passwordForm.oldPassword\"\n            type=\"password\"\n            show-password\n          />\n        </el-form-item>\n        \n        <el-form-item label=\"新密码\" prop=\"newPassword\">\n          <el-input\n            v-model=\"passwordForm.newPassword\"\n            type=\"password\"\n            show-password\n          />\n        </el-form-item>\n        \n        <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\n          <el-input\n            v-model=\"passwordForm.confirmPassword\"\n            type=\"password\"\n            show-password\n          />\n        </el-form-item>\n      </el-form>\n      \n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"passwordDialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"changePassword\" :loading=\"changingPassword\">\n            确认修改\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n    \n    <!-- SM2密钥管理对话框 -->\n    <el-dialog\n      v-model=\"keyDialogVisible\"\n      title=\"SM2密钥管理\"\n      width=\"500px\"\n    >\n      <div class=\"key-management\">\n        <div class=\"key-status\">\n          <h4>当前状态</h4>\n          <p>\n            <el-tag :type=\"userInfo.hasPublicKey ? 'success' : 'danger'\">\n              {{ userInfo.hasPublicKey ? '已设置SM2密钥' : '未设置SM2密钥' }}\n            </el-tag>\n            <span v-if=\"userInfo.publicKeyExpires\">\n              (过期时间: {{ userInfo.publicKeyExpires }})\n            </span>\n          </p>\n        </div>\n        \n        <div class=\"key-actions\">\n          <el-button type=\"primary\" @click=\"generateNewKeyPair\">\n            生成新密钥对\n          </el-button>\n          <el-button type=\"danger\" @click=\"removePublicKey\" :disabled=\"!userInfo.hasPublicKey\">\n            移除公钥\n          </el-button>\n        </div>\n        \n        <div v-if=\"newKeyPair.publicKey\" class=\"new-key-pair\">\n          <h4>新生成的密钥对</h4>\n          \n          <div class=\"key-box\">\n            <div class=\"key-label\">\n              <span>公钥</span>\n              <el-button type=\"primary\" size=\"small\" @click=\"copyPublicKey\">\n                复制\n              </el-button>\n            </div>\n            <el-input\n              v-model=\"newKeyPair.publicKey\"\n              type=\"textarea\"\n              :rows=\"3\"\n              readonly\n            />\n          </div>\n          \n          <div class=\"key-box\">\n            <div class=\"key-label\">\n              <span>私钥</span>\n              <el-button type=\"primary\" size=\"small\" @click=\"copyPrivateKey\">\n                复制\n              </el-button>\n            </div>\n            <el-input\n              v-model=\"newKeyPair.privateKey\"\n              type=\"textarea\"\n              :rows=\"3\"\n              readonly\n              show-password\n            />\n          </div>\n          \n          <el-alert\n            title=\"重要提示\"\n            type=\"warning\"\n            description=\"请务必保存您的私钥，私钥将不会存储在服务器上，丢失后无法找回。\"\n            show-icon\n            :closable=\"false\"\n            class=\"key-warning\"\n          />\n          \n          <div class=\"save-key-action\">\n            <el-button type=\"success\" @click=\"saveNewPublicKey\">\n              保存新公钥到服务器\n            </el-button>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport { useStore } from 'vuex';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { SM2Crypto, SM3Hasher } from '@/utils/crypto';\n\nexport default {\n  name: 'UserProfile',\n  setup() {\n    const store = useStore();\n    const formRef = ref(null);\n    const passwordFormRef = ref(null);\n    \n    const loading = ref(true);\n    const updating = ref(false);\n    const changingPassword = ref(false);\n    const editMode = ref(false);\n    \n    const passwordDialogVisible = ref(false);\n    const keyDialogVisible = ref(false);\n    \n    // 用户信息\n    const userInfo = computed(() => store.getters['user/userInfo']);\n    \n    // 编辑表单\n    const form = reactive({\n      email: '',\n      phone: ''\n    });\n    \n    // 修改密码表单\n    const passwordForm = reactive({\n      oldPassword: '',\n      newPassword: '',\n      confirmPassword: ''\n    });\n    \n    // 新密钥对\n    const newKeyPair = reactive({\n      publicKey: '',\n      privateKey: ''\n    });\n    \n    // 信誉分颜色\n    const creditScoreColor = computed(() => {\n      const score = userInfo.value.creditScore || 0;\n      if (score >= 90) return '#67c23a';\n      if (score >= 70) return '#409eff';\n      if (score >= 50) return '#e6a23c';\n      return '#f56c6c';\n    });\n    \n    // 格式化信誉分\n    const formatCreditScore = (percentage) => {\n      return `${percentage}分`;\n    };\n    \n    // 表单验证规则\n    const rules = {\n      email: [\n        { required: true, message: '请输入邮箱', trigger: 'blur' },\n        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }\n      ],\n      phone: [\n        { required: true, message: '请输入手机号', trigger: 'blur' },\n        { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }\n      ]\n    };\n    \n    // 密码表单验证规则\n    const validatePass = (rule, value, callback) => {\n      if (value === '') {\n        callback(new Error('请输入新密码'));\n      } else if (value.length < 6) {\n        callback(new Error('密码长度不能小于6位'));\n      } else {\n        if (passwordForm.confirmPassword !== '') {\n          passwordFormRef.value.validateField('confirmPassword');\n        }\n        callback();\n      }\n    };\n    \n    const validatePass2 = (rule, value, callback) => {\n      if (value === '') {\n        callback(new Error('请再次输入新密码'));\n      } else if (value !== passwordForm.newPassword) {\n        callback(new Error('两次输入密码不一致'));\n      } else {\n        callback();\n      }\n    };\n    \n    const passwordRules = {\n      oldPassword: [\n        { required: true, message: '请输入当前密码', trigger: 'blur' }\n      ],\n      newPassword: [\n        { validator: validatePass, trigger: 'blur' }\n      ],\n      confirmPassword: [\n        { validator: validatePass2, trigger: 'blur' }\n      ]\n    };\n    \n    // 获取用户信息\n    const getUserInfo = async () => {\n      try {\n        loading.value = true;\n        await store.dispatch('user/getUserInfo');\n        \n        // 填充表单\n        form.email = userInfo.value.email || '';\n        form.phone = userInfo.value.phone || '';\n      } catch (error) {\n        ElMessage.error('获取用户信息失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n    \n    // 更新个人信息\n    const updateProfile = async () => {\n      if (!formRef.value) return;\n      \n      await formRef.value.validate(async (valid) => {\n        if (valid) {\n          try {\n            updating.value = true;\n            await store.dispatch('user/updateProfile', form);\n            ElMessage.success('个人信息更新成功');\n            editMode.value = false;\n          } catch (error) {\n            ElMessage.error(error.message || '更新失败');\n          } finally {\n            updating.value = false;\n          }\n        }\n      });\n    };\n    \n    // 显示修改密码对话框\n    const showChangePasswordDialog = () => {\n      passwordDialogVisible.value = true;\n      passwordForm.oldPassword = '';\n      passwordForm.newPassword = '';\n      passwordForm.confirmPassword = '';\n    };\n    \n    // 修改密码\n    const changePassword = async () => {\n      if (!passwordFormRef.value) return;\n      \n      await passwordFormRef.value.validate(async (valid) => {\n        if (valid) {\n          try {\n            changingPassword.value = true;\n            \n            // 对密码进行SM3哈希\n            const oldPasswordHash = SM3Hasher.hash(passwordForm.oldPassword);\n            const newPasswordHash = SM3Hasher.hash(passwordForm.newPassword);\n            \n            await store.dispatch('user/changePassword', {\n              oldPassword: oldPasswordHash,\n              newPassword: newPasswordHash\n            });\n            \n            ElMessage.success('密码修改成功');\n            passwordDialogVisible.value = false;\n          } catch (error) {\n            ElMessage.error(error.message || '密码修改失败');\n          } finally {\n            changingPassword.value = false;\n          }\n        }\n      });\n    };\n    \n    // 显示密钥管理对话框\n    const showKeyManagementDialog = () => {\n      keyDialogVisible.value = true;\n      newKeyPair.publicKey = '';\n      newKeyPair.privateKey = '';\n    };\n    \n    // 生成新密钥对\n    const generateNewKeyPair = () => {\n      const keyPair = SM2Crypto.generateKeyPair();\n      newKeyPair.publicKey = keyPair.publicKey;\n      newKeyPair.privateKey = keyPair.privateKey;\n    };\n    \n    // 复制公钥\n    const copyPublicKey = () => {\n      navigator.clipboard.writeText(newKeyPair.publicKey)\n        .then(() => {\n          ElMessage.success('公钥已复制到剪贴板');\n        })\n        .catch(() => {\n          ElMessage.error('复制失败，请手动复制');\n        });\n    };\n    \n    // 复制私钥\n    const copyPrivateKey = () => {\n      navigator.clipboard.writeText(newKeyPair.privateKey)\n        .then(() => {\n          ElMessage.success('私钥已复制到剪贴板');\n        })\n        .catch(() => {\n          ElMessage.error('复制失败，请手动复制');\n        });\n    };\n    \n    // 保存新公钥到服务器\n    const saveNewPublicKey = async () => {\n      try {\n        await store.dispatch('user/updatePublicKey', {\n          publicKey: newKeyPair.publicKey\n        });\n        \n        ElMessage.success('公钥更新成功');\n        await getUserInfo();\n      } catch (error) {\n        ElMessage.error(error.message || '公钥更新失败');\n      }\n    };\n    \n    // 移除公钥\n    const removePublicKey = async () => {\n      try {\n        await ElMessageBox.confirm(\n          '移除公钥后将无法使用SM2证书登录，确定要继续吗？',\n          '警告',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        );\n        \n        await store.dispatch('user/removePublicKey');\n        ElMessage.success('公钥已移除');\n        await getUserInfo();\n      } catch (error) {\n        if (error !== 'cancel') {\n          ElMessage.error(error.message || '移除公钥失败');\n        }\n      }\n    };\n    \n    onMounted(() => {\n      getUserInfo();\n    });\n    \n    return {\n      formRef,\n      passwordFormRef,\n      loading,\n      updating,\n      changingPassword,\n      editMode,\n      userInfo,\n      form,\n      rules,\n      passwordDialogVisible,\n      passwordForm,\n      passwordRules,\n      keyDialogVisible,\n      newKeyPair,\n      creditScoreColor,\n      formatCreditScore,\n      updateProfile,\n      showChangePasswordDialog,\n      changePassword,\n      showKeyManagementDialog,\n      generateNewKeyPair,\n      copyPublicKey,\n      copyPrivateKey,\n      saveNewPublicKey,\n      removePublicKey\n    };\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.user-profile {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.profile-card {\n  margin-bottom: 20px;\n  \n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    \n    h3 {\n      margin: 0;\n    }\n  }\n}\n\n.loading-container {\n  padding: 20px 0;\n}\n\n.form-hint {\n  display: block;\n  margin-top: 5px;\n  color: #909399;\n  font-size: 12px;\n}\n\n.security-options {\n  .security-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 15px 0;\n    border-bottom: 1px solid #ebeef5;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .security-info {\n      h4 {\n        margin: 0 0 5px 0;\n      }\n      \n      p {\n        margin: 0;\n        color: #606266;\n        font-size: 14px;\n      }\n    }\n  }\n}\n\n.key-management {\n  .key-status {\n    margin-bottom: 20px;\n    \n    h4 {\n      margin: 0 0 10px 0;\n    }\n  }\n  \n  .key-actions {\n    display: flex;\n    gap: 10px;\n    margin-bottom: 20px;\n  }\n  \n  .new-key-pair {\n    margin-top: 20px;\n    \n    h4 {\n      margin: 0 0 10px 0;\n    }\n    \n    .key-box {\n      margin-bottom: 15px;\n      \n      .key-label {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 5px;\n        font-weight: bold;\n      }\n    }\n    \n    .key-warning {\n      margin: 15px 0;\n    }\n    \n    .save-key-action {\n      margin-top: 20px;\n      text-align: center;\n    }\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAc;;EAGdA,KAAK,EAAC;AAAa;;EAJhCC,GAAA;EAY0BD,KAAK,EAAC;;;EAZhCC,GAAA;AAAA;;EAuEWD,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAe;;EAQrBA,KAAK,EAAC;AAAe;;EAgDpBA,KAAK,EAAC;AAAe;;EAexBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAY;;EAhJ/BC,GAAA;AAAA;;EA4JaD,KAAK,EAAC;AAAa;;EA5JhCC,GAAA;EAqKyCD,KAAK,EAAC;;;EAGhCA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAW;;EAcnBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAW;;EAwBnBA,KAAK,EAAC;AAAiB;;;;;;;;;;;;uBA/MpCE,mBAAA,CAuNM,OAvNNC,UAuNM,GAtNJC,YAAA,CA4DUC,kBAAA;IA5DDL,KAAK,EAAC;EAAc;IAChBM,MAAM,EAAAC,QAAA,CACf,MAKM,CALNC,mBAAA,CAKM,OALNC,UAKM,G,4BAJJD,mBAAA,CAAa,YAAT,MAAI,sBACRJ,YAAA,CAEYM,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,QAAQ,IAAID,MAAA,CAAAC,QAAQ;;MANhEC,OAAA,EAAAV,QAAA,CAOY,MAAgC,CAP5CW,gBAAA,CAAAC,gBAAA,CAOeJ,MAAA,CAAAC,QAAQ,mC;MAPvBI,CAAA;;IAAAH,OAAA,EAAAV,QAAA,CAYqB,MAGG,CAHPQ,MAAA,CAAAM,OAAO,I,cAAlBnB,mBAAA,CAEM,OAFNoB,UAEM,GADJlB,YAAA,CAAkCmB,sBAAA;MAApBC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAR;2BAGzBvB,mBAAA,CA6CM,OA7DZwB,UAAA,GAiBQtB,YAAA,CA2CUuB,kBAAA;MA1CRC,GAAG,EAAC,SAAS;MACZC,KAAK,EAAEd,MAAA,CAAAe,IAAI;MACXC,KAAK,EAAEhB,MAAA,CAAAgB,KAAK;MACb,aAAW,EAAC,OAAO;MAClBC,QAAQ,GAAGjB,MAAA,CAAAC;;MAtBtBC,OAAA,EAAAV,QAAA,CAwBU,MAGe,CAHfH,YAAA,CAGe6B,uBAAA;QAHDC,KAAK,EAAC;MAAI;QAxBlCjB,OAAA,EAAAV,QAAA,CAyBY,MAAsD,CAAtDH,YAAA,CAAsD+B,mBAAA;UAzBlEC,UAAA,EAyB+BrB,MAAA,CAAAsB,QAAQ,CAACC,aAAa;UAzBrD,uBAAAzB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAyB+BC,MAAA,CAAAsB,QAAQ,CAACC,aAAa,GAAAxB,MAAA;UAAEkB,QAAQ,EAAR;6EAC3CxB,mBAAA,CAAyC;UAAlCR,KAAK,EAAC;QAAW,GAAC,UAAQ,qB;QA1B7CoB,CAAA;QAAAmB,EAAA;UA6BUnC,YAAA,CAEe6B,uBAAA;QAFDC,KAAK,EAAC,IAAI;QAACM,IAAI,EAAC;;QA7BxCvB,OAAA,EAAAV,QAAA,CA8BY,MAAiC,CAAjCH,YAAA,CAAiC+B,mBAAA;UA9B7CC,UAAA,EA8B+BrB,MAAA,CAAAe,IAAI,CAACW,KAAK;UA9BzC,uBAAA5B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA8B+BC,MAAA,CAAAe,IAAI,CAACW,KAAK,GAAA3B,MAAA;;QA9BzCM,CAAA;UAiCUhB,YAAA,CAEe6B,uBAAA;QAFDC,KAAK,EAAC,KAAK;QAACM,IAAI,EAAC;;QAjCzCvB,OAAA,EAAAV,QAAA,CAkCY,MAAiC,CAAjCH,YAAA,CAAiC+B,mBAAA;UAlC7CC,UAAA,EAkC+BrB,MAAA,CAAAe,IAAI,CAACY,KAAK;UAlCzC,uBAAA7B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAkC+BC,MAAA,CAAAe,IAAI,CAACY,KAAK,GAAA5B,MAAA;;QAlCzCM,CAAA;UAqCUhB,YAAA,CAQe6B,uBAAA;QARDC,KAAK,EAAC;MAAK;QArCnCjB,OAAA,EAAAV,QAAA,CAsCY,MAKE,CALFH,YAAA,CAKEuC,sBAAA;UAJCC,UAAU,EAAE7B,MAAA,CAAAsB,QAAQ,CAACQ,WAAW;UAChCC,KAAK,EAAE/B,MAAA,CAAAgC,gBAAgB;UACvBC,MAAM,EAAEjC,MAAA,CAAAkC,iBAAiB;UACzB,cAAY,EAAE;gGAEjBzC,mBAAA,CAA4C;UAArCR,KAAK,EAAC;QAAW,GAAC,aAAW,qB;QA5ChDoB,CAAA;QAAAmB,EAAA;UA+CUnC,YAAA,CAEe6B,uBAAA;QAFDC,KAAK,EAAC;MAAM;QA/CpCjB,OAAA,EAAAV,QAAA,CAgDY,MAAkD,CAAlDH,YAAA,CAAkD+B,mBAAA;UAhD9DC,UAAA,EAgD+BrB,MAAA,CAAAsB,QAAQ,CAACa,SAAS;UAhDjD,uBAAArC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAgD+BC,MAAA,CAAAsB,QAAQ,CAACa,SAAS,GAAApC,MAAA;UAAEkB,QAAQ,EAAR;;QAhDnDZ,CAAA;UAmDUhB,YAAA,CAEe6B,uBAAA;QAFDC,KAAK,EAAC;MAAM;QAnDpCjB,OAAA,EAAAV,QAAA,CAoDY,MAAkD,CAAlDH,YAAA,CAAkD+B,mBAAA;UApD9DC,UAAA,EAoD+BrB,MAAA,CAAAsB,QAAQ,CAACc,SAAS;UApDjD,uBAAAtC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAoD+BC,MAAA,CAAAsB,QAAQ,CAACc,SAAS,GAAArC,MAAA;UAAEkB,QAAQ,EAAR;;QApDnDZ,CAAA;UAuD8BL,MAAA,CAAAC,QAAQ,I,cAA5BoC,YAAA,CAIenB,uBAAA;QA3DzBhC,GAAA;MAAA;QAAAgB,OAAA,EAAAV,QAAA,CAwDY,MAEY,CAFZH,YAAA,CAEYM,oBAAA;UAFDC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAEG,MAAA,CAAAsC,aAAa;UAAGhC,OAAO,EAAEN,MAAA,CAAAuC;;UAxDvErC,OAAA,EAAAV,QAAA,CAwDiF,MAErEM,MAAA,SAAAA,MAAA,QA1DZK,gBAAA,CAwDiF,QAErE,E;UA1DZE,CAAA;UAAAmB,EAAA;;QAAAnB,CAAA;YAAAmC,mBAAA,e;MAAAnC,CAAA;;IAAAA,CAAA;MAgEIhB,YAAA,CAwBUC,kBAAA;IAxBDL,KAAK,EAAC;EAAc;IAChBM,MAAM,EAAAC,QAAA,CACf,MAEMM,MAAA,SAAAA,MAAA,QAFNL,mBAAA,CAEM;MAFDR,KAAK,EAAC;IAAa,IACtBQ,mBAAA,CAAa,YAAT,MAAI,E;IAnElBS,OAAA,EAAAV,QAAA,CAuEM,MAgBM,CAhBNC,mBAAA,CAgBM,OAhBNgD,UAgBM,GAfJhD,mBAAA,CAMM,OANNiD,UAMM,G,4BALJjD,mBAAA,CAGM;MAHDR,KAAK,EAAC;IAAe,IACxBQ,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAsB,WAAnB,iBAAe,E,sBAEpBJ,YAAA,CAA2DM,oBAAA;MAA/CE,OAAK,EAAEG,MAAA,CAAA2C;IAAwB;MA7ErDzC,OAAA,EAAAV,QAAA,CA6EuD,MAAEM,MAAA,SAAAA,MAAA,QA7EzDK,gBAAA,CA6EuD,IAAE,E;MA7EzDE,CAAA;MAAAmB,EAAA;sCAgFQ/B,mBAAA,CAMM,OANNmD,UAMM,G,4BALJnD,mBAAA,CAGM;MAHDR,KAAK,EAAC;IAAe,IACxBQ,mBAAA,CAAgB,YAAZ,SAAO,GACXA,mBAAA,CAA6B,WAA1B,wBAAsB,E,sBAE3BJ,YAAA,CAA0DM,oBAAA;MAA9CE,OAAK,EAAEG,MAAA,CAAA6C;IAAuB;MArFpD3C,OAAA,EAAAV,QAAA,CAqFsD,MAAEM,MAAA,SAAAA,MAAA,QArFxDK,gBAAA,CAqFsD,IAAE,E;MArFxDE,CAAA;MAAAmB,EAAA;;IAAAnB,CAAA;MA0FImC,mBAAA,aAAgB,EAChBnD,YAAA,CA4CYyD,oBAAA;IAvIhBzB,UAAA,EA4FerB,MAAA,CAAA+C,qBAAqB;IA5FpC,uBAAAjD,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA4FeC,MAAA,CAAA+C,qBAAqB,GAAAhD,MAAA;IAC9BiD,KAAK,EAAC,MAAM;IACZC,KAAK,EAAC;;IAiCKC,MAAM,EAAA1D,QAAA,CACf,MAKO,CALPC,mBAAA,CAKO,QALP0D,UAKO,GAJL9D,YAAA,CAAgEM,oBAAA;MAApDE,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAA+C,qBAAqB;;MAjIlD7C,OAAA,EAAAV,QAAA,CAiI4D,MAAEM,MAAA,SAAAA,MAAA,QAjI9DK,gBAAA,CAiI4D,IAAE,E;MAjI9DE,CAAA;MAAAmB,EAAA;QAkIUnC,YAAA,CAEYM,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEG,MAAA,CAAAoD,cAAc;MAAG9C,OAAO,EAAEN,MAAA,CAAAqD;;MAlItEnD,OAAA,EAAAV,QAAA,CAkIwF,MAE9EM,MAAA,SAAAA,MAAA,QApIVK,gBAAA,CAkIwF,QAE9E,E;MApIVE,CAAA;MAAAmB,EAAA;;IAAAtB,OAAA,EAAAV,QAAA,CAgGM,MA6BU,CA7BVH,YAAA,CA6BUuB,kBAAA;MA5BRC,GAAG,EAAC,iBAAiB;MACpBC,KAAK,EAAEd,MAAA,CAAAsD,YAAY;MACnBtC,KAAK,EAAEhB,MAAA,CAAAuD,aAAa;MACrB,aAAW,EAAC;;MApGpBrD,OAAA,EAAAV,QAAA,CAsGQ,MAMe,CANfH,YAAA,CAMe6B,uBAAA;QANDC,KAAK,EAAC,MAAM;QAACM,IAAI,EAAC;;QAtGxCvB,OAAA,EAAAV,QAAA,CAuGU,MAIE,CAJFH,YAAA,CAIE+B,mBAAA;UA3GZC,UAAA,EAwGqBrB,MAAA,CAAAsD,YAAY,CAACE,WAAW;UAxG7C,uBAAA1D,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAwGqBC,MAAA,CAAAsD,YAAY,CAACE,WAAW,GAAAzD,MAAA;UACjCH,IAAI,EAAC,UAAU;UACf,eAAa,EAAb;;QA1GZS,CAAA;UA8GQhB,YAAA,CAMe6B,uBAAA;QANDC,KAAK,EAAC,KAAK;QAACM,IAAI,EAAC;;QA9GvCvB,OAAA,EAAAV,QAAA,CA+GU,MAIE,CAJFH,YAAA,CAIE+B,mBAAA;UAnHZC,UAAA,EAgHqBrB,MAAA,CAAAsD,YAAY,CAACG,WAAW;UAhH7C,uBAAA3D,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAgHqBC,MAAA,CAAAsD,YAAY,CAACG,WAAW,GAAA1D,MAAA;UACjCH,IAAI,EAAC,UAAU;UACf,eAAa,EAAb;;QAlHZS,CAAA;UAsHQhB,YAAA,CAMe6B,uBAAA;QANDC,KAAK,EAAC,MAAM;QAACM,IAAI,EAAC;;QAtHxCvB,OAAA,EAAAV,QAAA,CAuHU,MAIE,CAJFH,YAAA,CAIE+B,mBAAA;UA3HZC,UAAA,EAwHqBrB,MAAA,CAAAsD,YAAY,CAACI,eAAe;UAxHjD,uBAAA5D,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAwHqBC,MAAA,CAAAsD,YAAY,CAACI,eAAe,GAAA3D,MAAA;UACrCH,IAAI,EAAC,UAAU;UACf,eAAa,EAAb;;QA1HZS,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;qCAyIImC,mBAAA,gBAAmB,EACnBnD,YAAA,CA6EYyD,oBAAA;IAvNhBzB,UAAA,EA2IerB,MAAA,CAAA2D,gBAAgB;IA3I/B,uBAAA7D,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA2IeC,MAAA,CAAA2D,gBAAgB,GAAA5D,MAAA;IACzBiD,KAAK,EAAC,SAAS;IACfC,KAAK,EAAC;;IA7IZ/C,OAAA,EAAAV,QAAA,CA+IM,MAuEM,CAvENC,mBAAA,CAuEM,OAvENmE,UAuEM,GAtEJnE,mBAAA,CAUM,OAVNoE,WAUM,G,4BATJpE,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAOI,YANFJ,YAAA,CAESyE,iBAAA;MAFAlE,IAAI,EAAEI,MAAA,CAAAsB,QAAQ,CAACyC,YAAY;;MAnJhD7D,OAAA,EAAAV,QAAA,CAoJc,MAAqD,CApJnEW,gBAAA,CAAAC,gBAAA,CAoJiBJ,MAAA,CAAAsB,QAAQ,CAACyC,YAAY,2C;MApJtC1D,CAAA;iCAsJwBL,MAAA,CAAAsB,QAAQ,CAAC0C,gBAAgB,I,cAArC7E,mBAAA,CAEO,QAxJnB8E,WAAA,EAsJmD,UAC9B,GAAA7D,gBAAA,CAAGJ,MAAA,CAAAsB,QAAQ,CAAC0C,gBAAgB,IAAG,IACxC,mBAxJZxB,mBAAA,e,KA4JQ/C,mBAAA,CAOM,OAPNyE,WAOM,GANJ7E,YAAA,CAEYM,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEG,MAAA,CAAAmE;;MA7J5CjE,OAAA,EAAAV,QAAA,CA6JgE,MAEtDM,MAAA,SAAAA,MAAA,QA/JVK,gBAAA,CA6JgE,UAEtD,E;MA/JVE,CAAA;MAAAmB,EAAA;oCAgKUnC,YAAA,CAEYM,oBAAA;MAFDC,IAAI,EAAC,QAAQ;MAAEC,OAAK,EAAEG,MAAA,CAAAoE,eAAe;MAAGnD,QAAQ,GAAGjB,MAAA,CAAAsB,QAAQ,CAACyC;;MAhKjF7D,OAAA,EAAAV,QAAA,CAgK+F,MAErFM,MAAA,SAAAA,MAAA,QAlKVK,gBAAA,CAgK+F,QAErF,E;MAlKVE,CAAA;MAAAmB,EAAA;kDAqKmBxB,MAAA,CAAAqE,UAAU,CAACC,SAAS,I,cAA/BnF,mBAAA,CAgDM,OAhDNoF,WAgDM,G,4BA/CJ9E,mBAAA,CAAgB,YAAZ,SAAO,sBAEXA,mBAAA,CAaM,OAbN+E,WAaM,GAZJ/E,mBAAA,CAKM,OALNgF,WAKM,G,4BAJJhF,mBAAA,CAAe,cAAT,IAAE,sBACRJ,YAAA,CAEYM,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAC8E,IAAI,EAAC,OAAO;MAAE7E,OAAK,EAAEG,MAAA,CAAA2E;;MA3K7DzE,OAAA,EAAAV,QAAA,CA2K4E,MAE9DM,MAAA,SAAAA,MAAA,QA7KdK,gBAAA,CA2K4E,MAE9D,E;MA7KdE,CAAA;MAAAmB,EAAA;sCA+KYnC,YAAA,CAKE+B,mBAAA;MApLdC,UAAA,EAgLuBrB,MAAA,CAAAqE,UAAU,CAACC,SAAS;MAhL3C,uBAAAxE,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAgLuBC,MAAA,CAAAqE,UAAU,CAACC,SAAS,GAAAvE,MAAA;MAC7BH,IAAI,EAAC,UAAU;MACda,IAAI,EAAE,CAAC;MACRmE,QAAQ,EAAR;+CAIJnF,mBAAA,CAcM,OAdNoF,WAcM,GAbJpF,mBAAA,CAKM,OALNqF,WAKM,G,4BAJJrF,mBAAA,CAAe,cAAT,IAAE,sBACRJ,YAAA,CAEYM,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAC8E,IAAI,EAAC,OAAO;MAAE7E,OAAK,EAAEG,MAAA,CAAA+E;;MA1L7D7E,OAAA,EAAAV,QAAA,CA0L6E,MAE/DM,MAAA,SAAAA,MAAA,QA5LdK,gBAAA,CA0L6E,MAE/D,E;MA5LdE,CAAA;MAAAmB,EAAA;sCA8LYnC,YAAA,CAME+B,mBAAA;MApMdC,UAAA,EA+LuBrB,MAAA,CAAAqE,UAAU,CAACW,UAAU;MA/L5C,uBAAAlF,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA+LuBC,MAAA,CAAAqE,UAAU,CAACW,UAAU,GAAAjF,MAAA;MAC9BH,IAAI,EAAC,UAAU;MACda,IAAI,EAAE,CAAC;MACRmE,QAAQ,EAAR,EAAQ;MACR,eAAa,EAAb;+CAIJvF,YAAA,CAOE4F,mBAAA;MANAjC,KAAK,EAAC,MAAM;MACZpD,IAAI,EAAC,SAAS;MACdsF,WAAW,EAAC,iCAAiC;MAC7C,WAAS,EAAT,EAAS;MACRC,QAAQ,EAAE,KAAK;MAChBlG,KAAK,EAAC;QAGRQ,mBAAA,CAIM,OAJN2F,WAIM,GAHJ/F,YAAA,CAEYM,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEG,MAAA,CAAAqF;;MAjN9CnF,OAAA,EAAAV,QAAA,CAiNgE,MAEpDM,MAAA,SAAAA,MAAA,QAnNZK,gBAAA,CAiNgE,aAEpD,E;MAnNZE,CAAA;MAAAmB,EAAA;0CAAAgB,mBAAA,e;IAAAnC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}