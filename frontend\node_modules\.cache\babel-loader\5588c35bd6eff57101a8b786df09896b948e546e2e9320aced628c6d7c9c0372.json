{"ast": null, "code": "import { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nconst paginationTotalProps = buildProps({\n  total: {\n    type: Number,\n    default: 1e3\n  }\n});\nexport { paginationTotalProps };", "map": {"version": 3, "names": ["paginationTotalProps", "buildProps", "total", "type", "Number", "default"], "sources": ["../../../../../../../packages/components/pagination/src/components/total.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport type Total from './total.vue'\nimport type { ExtractPropTypes } from 'vue'\n\nexport const paginationTotalProps = buildProps({\n  total: {\n    type: Number,\n    default: 1000,\n  },\n} as const)\n\nexport type PaginationTotalProps = ExtractPropTypes<typeof paginationTotalProps>\n\nexport type TotalInstance = InstanceType<typeof Total> & unknown\n"], "mappings": ";AACY,MAACA,oBAAoB,GAAGC,UAAU,CAAC;EAC7CC,KAAK,EAAE;IACLC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}