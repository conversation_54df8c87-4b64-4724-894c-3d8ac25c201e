{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createBlock as _createBlock, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"seat-reservation\"\n};\nconst _hoisted_2 = {\n  class: \"page-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-left\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"loading-container\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"step-content\"\n};\nconst _hoisted_6 = {\n  key: 2\n};\nconst _hoisted_7 = {\n  key: 2,\n  class: \"step-actions\"\n};\nconst _hoisted_8 = {\n  class: \"step-content\"\n};\nconst _hoisted_9 = {\n  class: \"card-header\"\n};\nconst _hoisted_10 = {\n  class: \"date-selector\"\n};\nconst _hoisted_11 = {\n  key: 0,\n  class: \"loading-time-slots\"\n};\nconst _hoisted_12 = {\n  class: \"time-slots-container\"\n};\nconst _hoisted_13 = {\n  class: \"time-slots-header\"\n};\nconst _hoisted_14 = {\n  class: \"time-hint\"\n};\nconst _hoisted_15 = {\n  key: 0,\n  class: \"empty-time-slots\"\n};\nconst _hoisted_16 = {\n  key: 1,\n  class: \"time-slots\"\n};\nconst _hoisted_17 = [\"onClick\"];\nconst _hoisted_18 = {\n  class: \"time-range\"\n};\nconst _hoisted_19 = {\n  class: \"selected-time-range\"\n};\nconst _hoisted_20 = {\n  key: 0,\n  class: \"selected-time\"\n};\nconst _hoisted_21 = {\n  class: \"duration\"\n};\nconst _hoisted_22 = {\n  key: 1,\n  class: \"no-selection\"\n};\nconst _hoisted_23 = {\n  class: \"step-actions\"\n};\nconst _hoisted_24 = {\n  class: \"step-content\"\n};\nconst _hoisted_25 = {\n  class: \"duration\"\n};\nconst _hoisted_26 = {\n  class: \"step-actions\"\n};\nconst _hoisted_27 = {\n  class: \"step-content\"\n};\nconst _hoisted_28 = {\n  class: \"reservation-details\"\n};\nconst _hoisted_29 = {\n  class: \"action-buttons\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_step = _resolveComponent(\"el-step\");\n  const _component_el_steps = _resolveComponent(\"el-steps\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_date_picker = _resolveComponent(\"el-date-picker\");\n  const _component_el_result = _resolveComponent(\"el-result\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n    onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.back()),\n    icon: $setup.ArrowLeft\n  }, {\n    default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"返回\")])),\n    _: 1 /* STABLE */,\n    __: [6]\n  }, 8 /* PROPS */, [\"icon\"]), _cache[7] || (_cache[7] = _createElementVNode(\"h2\", null, \"座位预约\", -1 /* HOISTED */))])]), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createVNode(_component_el_skeleton, {\n    rows: 10,\n    animated: \"\"\n  })])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createVNode(_component_el_steps, {\n    active: $setup.currentStep,\n    \"finish-status\": \"success\",\n    class: \"reservation-steps\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_step, {\n      title: \"选择座位\"\n    }), _createVNode(_component_el_step, {\n      title: \"选择时间\"\n    }), _createVNode(_component_el_step, {\n      title: \"确认预约\"\n    }), _createVNode(_component_el_step, {\n      title: \"预约成功\"\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"active\"]), _createCommentVNode(\" 步骤1：选择座位 \"), $setup.currentStep === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_el_card, {\n    shadow: \"hover\",\n    class: \"seat-info-card\"\n  }, {\n    header: _withCtx(() => _cache[8] || (_cache[8] = [_createElementVNode(\"div\", {\n      class: \"card-header\"\n    }, [_createElementVNode(\"h3\", null, \"座位信息\")], -1 /* HOISTED */)])),\n    default: _withCtx(() => [$setup.seat ? (_openBlock(), _createBlock(_component_el_descriptions, {\n      key: 0,\n      column: 2,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"自习室\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.room?.name), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"位置\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.room?.location), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"座位编号\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.seat.seat_number), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"座位位置\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(`${$setup.seat.row}排${$setup.seat.column}列`), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"设施\"\n      }, {\n        default: _withCtx(() => [$setup.seat.is_power_outlet ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 0,\n          type: \"success\",\n          effect: \"plain\"\n        }, {\n          default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"有电源\")])),\n          _: 1 /* STABLE */,\n          __: [9]\n        })) : _createCommentVNode(\"v-if\", true), $setup.seat.is_window_seat ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 1,\n          type: \"success\",\n          effect: \"plain\"\n        }, {\n          default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"靠窗\")])),\n          _: 1 /* STABLE */,\n          __: [10]\n        })) : _createCommentVNode(\"v-if\", true), !$setup.seat.is_power_outlet && !$setup.seat.is_window_seat ? (_openBlock(), _createElementBlock(\"span\", _hoisted_6, \"无特殊设施\")) : _createCommentVNode(\"v-if\", true)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"状态\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_tag, {\n          type: $setup.getSeatStatusType($setup.seat.status)\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getSeatStatusText($setup.seat.status)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })) : (_openBlock(), _createBlock(_component_el_empty, {\n      key: 1,\n      description: \"未选择座位\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[1] || (_cache[1] = $event => _ctx.$router.push('/seat/rooms'))\n      }, {\n        default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\" 去选择座位 \")])),\n        _: 1 /* STABLE */,\n        __: [11]\n      })]),\n      _: 1 /* STABLE */\n    })), $setup.seat ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.nextStep\n    }, {\n      default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"下一步\")])),\n      _: 1 /* STABLE */,\n      __: [12]\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      onClick: _cache[2] || (_cache[2] = $event => _ctx.$router.push('/seat/rooms'))\n    }, {\n      default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"重新选择\")])),\n      _: 1 /* STABLE */,\n      __: [13]\n    })])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  })])) : $setup.currentStep === 1 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 步骤2：选择时间 \"), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_card, {\n    shadow: \"hover\",\n    class: \"time-selection-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_cache[15] || (_cache[15] = _createElementVNode(\"h3\", null, \"选择预约时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_10, [_cache[14] || (_cache[14] = _createElementVNode(\"span\", null, \"日期：\", -1 /* HOISTED */)), _createVNode(_component_el_date_picker, {\n      modelValue: $setup.selectedDate,\n      \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.selectedDate = $event),\n      type: \"date\",\n      placeholder: \"选择日期\",\n      format: \"YYYY-MM-DD\",\n      \"value-format\": \"YYYY-MM-DD\",\n      \"disabled-date\": $setup.disabledDate,\n      onChange: $setup.loadTimeSlots\n    }, null, 8 /* PROPS */, [\"modelValue\", \"disabled-date\", \"onChange\"])])])]),\n    default: _withCtx(() => [$setup.loadingTimeSlots ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createVNode(_component_el_skeleton, {\n      rows: 5,\n      animated: \"\"\n    })])) : (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_cache[16] || (_cache[16] = _createElementVNode(\"h4\", null, \"可用时间段\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_14, \" 开放时间: \" + _toDisplayString($setup.formatTime($setup.room?.open_time)) + \" - \" + _toDisplayString($setup.formatTime($setup.room?.close_time)), 1 /* TEXT */)]), $setup.timeSlots.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createVNode(_component_el_empty, {\n      description: \"当前日期没有可用的时间段\"\n    })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.timeSlots, (slot, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: index,\n        class: _normalizeClass([\"time-slot\", {\n          unavailable: !slot.is_available,\n          selected: $setup.isTimeSlotSelected(slot)\n        }]),\n        onClick: $event => $setup.selectTimeSlot(slot)\n      }, [_createElementVNode(\"span\", _hoisted_18, _toDisplayString($setup.formatTimeSlot(slot)), 1 /* TEXT */), slot.is_available ? (_openBlock(), _createBlock(_component_el_tag, {\n        key: 0,\n        type: \"success\",\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [...(_cache[17] || (_cache[17] = [_createTextVNode(\"可用\")]))]),\n        _: 1 /* STABLE */,\n        __: [17]\n      })) : (_openBlock(), _createBlock(_component_el_tag, {\n        key: 1,\n        type: \"danger\",\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [...(_cache[18] || (_cache[18] = [_createTextVNode(\"已约\")]))]),\n        _: 1 /* STABLE */,\n        __: [18]\n      }))], 10 /* CLASS, PROPS */, _hoisted_17);\n    }), 128 /* KEYED_FRAGMENT */))])), _createElementVNode(\"div\", _hoisted_19, [_cache[19] || (_cache[19] = _createElementVNode(\"h4\", null, \"已选时间段\", -1 /* HOISTED */)), $setup.selectedStartTime && $setup.selectedEndTime ? (_openBlock(), _createElementBlock(\"p\", _hoisted_20, [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.selectedStartTime)) + \" - \" + _toDisplayString($setup.formatDateTime($setup.selectedEndTime)) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_21, \"(\" + _toDisplayString($setup.calculateDuration()) + \")\", 1 /* TEXT */)])) : (_openBlock(), _createElementBlock(\"p\", _hoisted_22, \"请选择时间段\"))])]), _createElementVNode(\"div\", _hoisted_23, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.nextStep,\n      disabled: !$setup.canProceed\n    }, {\n      default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\"下一步\")])),\n      _: 1 /* STABLE */,\n      __: [20]\n    }, 8 /* PROPS */, [\"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n      onClick: $setup.prevStep\n    }, {\n      default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"上一步\")])),\n      _: 1 /* STABLE */,\n      __: [21]\n    }, 8 /* PROPS */, [\"onClick\"])])], 64 /* STABLE_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  })])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.currentStep === 2 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" 步骤3：确认预约 \"), _createElementVNode(\"div\", _hoisted_24, [_createVNode(_component_el_card, {\n    shadow: \"hover\",\n    class: \"confirm-card\"\n  }, {\n    header: _withCtx(() => _cache[22] || (_cache[22] = [_createElementVNode(\"div\", {\n      class: \"card-header\"\n    }, [_createElementVNode(\"h3\", null, \"确认预约信息\")], -1 /* HOISTED */)])),\n    default: _withCtx(() => [_createVNode(_component_el_descriptions, {\n      column: 1,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"自习室\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.room?.name), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"位置\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.room?.location), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"座位编号\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.seat?.seat_number), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"预约日期\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedDate), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"预约时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.selectedStartTime)) + \" - \" + _toDisplayString($setup.formatDateTime($setup.selectedEndTime)) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_25, \"(\" + _toDisplayString($setup.calculateDuration()) + \")\", 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _cache[25] || (_cache[25] = _createElementVNode(\"div\", {\n      class: \"reservation-notes\"\n    }, [_createElementVNode(\"h4\", null, \"预约须知\"), _createElementVNode(\"ul\", null, [_createElementVNode(\"li\", null, \"预约成功后，请在预约开始时间后30分钟内完成签到，否则系统将自动取消预约。\"), _createElementVNode(\"li\", null, \"如需取消预约，请提前操作，避免影响信誉分。\"), _createElementVNode(\"li\", null, \"请遵守自习室规定，保持安静，不要占用他人座位。\"), _createElementVNode(\"li\", null, \"离开时请及时签退，方便他人使用。\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_26, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitReservation,\n      loading: $setup.submitting\n    }, {\n      default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"确认预约\")])),\n      _: 1 /* STABLE */,\n      __: [23]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"]), _createVNode(_component_el_button, {\n      onClick: $setup.prevStep\n    }, {\n      default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\"上一步\")])),\n      _: 1 /* STABLE */,\n      __: [24]\n    }, 8 /* PROPS */, [\"onClick\"])])]),\n    _: 1 /* STABLE */,\n    __: [25]\n  })])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.currentStep === 3 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 3\n  }, [_createCommentVNode(\" 步骤4：预约成功 \"), _createElementVNode(\"div\", _hoisted_27, [_createVNode(_component_el_card, {\n    shadow: \"hover\",\n    class: \"success-card\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_result, {\n      icon: \"success\",\n      title: \"预约成功\",\n      \"sub-title\": \"您已成功预约座位\"\n    }, {\n      extra: _withCtx(() => [_createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"p\", null, [_cache[26] || (_cache[26] = _createElementVNode(\"strong\", null, \"预约编号：\", -1 /* HOISTED */)), _createTextVNode(_toDisplayString($setup.reservation?.id), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[27] || (_cache[27] = _createElementVNode(\"strong\", null, \"自习室：\", -1 /* HOISTED */)), _createTextVNode(_toDisplayString($setup.room?.name), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[28] || (_cache[28] = _createElementVNode(\"strong\", null, \"座位编号：\", -1 /* HOISTED */)), _createTextVNode(_toDisplayString($setup.seat?.seat_number), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[29] || (_cache[29] = _createElementVNode(\"strong\", null, \"预约时间：\", -1 /* HOISTED */)), _createTextVNode(_toDisplayString($setup.formatDateTime($setup.reservation?.start_time)) + \" - \" + _toDisplayString($setup.formatDateTime($setup.reservation?.end_time)), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[30] || (_cache[30] = _createElementVNode(\"strong\", null, \"签到码：\", -1 /* HOISTED */)), _createTextVNode(_toDisplayString($setup.reservation?.reservation_code), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_29, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[4] || (_cache[4] = $event => _ctx.$router.push('/user/reservations'))\n      }, {\n        default: _withCtx(() => _cache[31] || (_cache[31] = [_createTextVNode(\" 查看我的预约 \")])),\n        _: 1 /* STABLE */,\n        __: [31]\n      }), _createVNode(_component_el_button, {\n        onClick: _cache[5] || (_cache[5] = $event => _ctx.$router.push('/seat/rooms'))\n      }, {\n        default: _withCtx(() => _cache[32] || (_cache[32] = [_createTextVNode(\" 返回自习室列表 \")])),\n        _: 1 /* STABLE */,\n        __: [32]\n      })])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */))]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_button", "onClick", "_cache", "$event", "_ctx", "$router", "back", "icon", "$setup", "ArrowLeft", "default", "_withCtx", "_createTextVNode", "_", "__", "loading", "_hoisted_4", "_component_el_skeleton", "rows", "animated", "_Fragment", "_component_el_steps", "active", "currentStep", "_component_el_step", "title", "_createCommentVNode", "_hoisted_5", "_component_el_card", "shadow", "header", "seat", "_createBlock", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "label", "_toDisplayString", "room", "name", "location", "seat_number", "row", "is_power_outlet", "_component_el_tag", "type", "effect", "is_window_seat", "_hoisted_6", "getSeatStatusType", "status", "getSeatStatusText", "_component_el_empty", "description", "push", "_hoisted_7", "nextStep", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_component_el_date_picker", "modelValue", "selectedDate", "placeholder", "format", "disabledDate", "onChange", "loadTimeSlots", "loadingTimeSlots", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "formatTime", "open_time", "close_time", "timeSlots", "length", "_hoisted_15", "_hoisted_16", "_renderList", "slot", "index", "_normalizeClass", "is_available", "isTimeSlotSelected", "selectTimeSlot", "_hoisted_18", "formatTimeSlot", "size", "_hoisted_17", "_hoisted_19", "selectedStartTime", "selectedEndTime", "_hoisted_20", "formatDateTime", "_hoisted_21", "calculateDuration", "_hoisted_22", "_hoisted_23", "disabled", "canProceed", "prevStep", "_hoisted_24", "_hoisted_25", "_hoisted_26", "submitReservation", "submitting", "_hoisted_27", "_component_el_result", "extra", "_hoisted_28", "reservation", "id", "start_time", "end_time", "reservation_code", "_hoisted_29"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\SeatReservation.vue"], "sourcesContent": ["<template>\n  <div class=\"seat-reservation\">\n    <div class=\"page-header\">\n      <div class=\"header-left\">\n        <el-button @click=\"$router.back()\" :icon=\"ArrowLeft\">返回</el-button>\n        <h2>座位预约</h2>\n      </div>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"10\" animated />\n    </div>\n\n    <template v-else>\n      <el-steps :active=\"currentStep\" finish-status=\"success\" class=\"reservation-steps\">\n        <el-step title=\"选择座位\" />\n        <el-step title=\"选择时间\" />\n        <el-step title=\"确认预约\" />\n        <el-step title=\"预约成功\" />\n      </el-steps>\n\n      <!-- 步骤1：选择座位 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <el-card shadow=\"hover\" class=\"seat-info-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3>座位信息</h3>\n            </div>\n          </template>\n\n          <template v-if=\"seat\">\n            <el-descriptions :column=\"2\" border>\n              <el-descriptions-item label=\"自习室\">{{ room?.name }}</el-descriptions-item>\n              <el-descriptions-item label=\"位置\">{{ room?.location }}</el-descriptions-item>\n              <el-descriptions-item label=\"座位编号\">{{ seat.seat_number }}</el-descriptions-item>\n              <el-descriptions-item label=\"座位位置\">{{\n                `${seat.row}排${seat.column}列`\n              }}</el-descriptions-item>\n              <el-descriptions-item label=\"设施\">\n                <el-tag v-if=\"seat.is_power_outlet\" type=\"success\" effect=\"plain\">有电源</el-tag>\n                <el-tag v-if=\"seat.is_window_seat\" type=\"success\" effect=\"plain\">靠窗</el-tag>\n                <span v-if=\"!seat.is_power_outlet && !seat.is_window_seat\">无特殊设施</span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"状态\">\n                <el-tag :type=\"getSeatStatusType(seat.status)\">\n                  {{ getSeatStatusText(seat.status) }}\n                </el-tag>\n              </el-descriptions-item>\n            </el-descriptions>\n          </template>\n\n          <template v-else>\n            <el-empty description=\"未选择座位\">\n              <el-button type=\"primary\" @click=\"$router.push('/seat/rooms')\">\n                去选择座位\n              </el-button>\n            </el-empty>\n          </template>\n\n          <div v-if=\"seat\" class=\"step-actions\">\n            <el-button type=\"primary\" @click=\"nextStep\">下一步</el-button>\n            <el-button @click=\"$router.push('/seat/rooms')\">重新选择</el-button>\n          </div>\n        </el-card>\n      </div>\n\n      <!-- 步骤2：选择时间 -->\n      <div v-else-if=\"currentStep === 1\" class=\"step-content\">\n        <el-card shadow=\"hover\" class=\"time-selection-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3>选择预约时间</h3>\n              <div class=\"date-selector\">\n                <span>日期：</span>\n                <el-date-picker\n                  v-model=\"selectedDate\"\n                  type=\"date\"\n                  placeholder=\"选择日期\"\n                  format=\"YYYY-MM-DD\"\n                  value-format=\"YYYY-MM-DD\"\n                  :disabled-date=\"disabledDate\"\n                  @change=\"loadTimeSlots\"\n                />\n              </div>\n            </div>\n          </template>\n\n          <div v-if=\"loadingTimeSlots\" class=\"loading-time-slots\">\n            <el-skeleton :rows=\"5\" animated />\n          </div>\n\n          <template v-else>\n            <div class=\"time-slots-container\">\n              <div class=\"time-slots-header\">\n                <h4>可用时间段</h4>\n                <p class=\"time-hint\">\n                  开放时间: {{ formatTime(room?.open_time) }} -\n                  {{ formatTime(room?.close_time) }}\n                </p>\n              </div>\n\n              <div v-if=\"timeSlots.length === 0\" class=\"empty-time-slots\">\n                <el-empty description=\"当前日期没有可用的时间段\" />\n              </div>\n\n              <div v-else class=\"time-slots\">\n                <div\n                  v-for=\"(slot, index) in timeSlots\"\n                  :key=\"index\"\n                  class=\"time-slot\"\n                  :class=\"{\n                    unavailable: !slot.is_available,\n                    selected: isTimeSlotSelected(slot),\n                  }\"\n                  @click=\"selectTimeSlot(slot)\"\n                >\n                  <span class=\"time-range\">{{ formatTimeSlot(slot) }}</span>\n                  <el-tag v-if=\"slot.is_available\" type=\"success\" size=\"small\">可用</el-tag>\n                  <el-tag v-else type=\"danger\" size=\"small\">已约</el-tag>\n                </div>\n              </div>\n\n              <div class=\"selected-time-range\">\n                <h4>已选时间段</h4>\n                <template v-if=\"selectedStartTime && selectedEndTime\">\n                  <p class=\"selected-time\">\n                    {{ formatDateTime(selectedStartTime) }} -\n                    {{ formatDateTime(selectedEndTime) }}\n                    <span class=\"duration\">({{ calculateDuration() }})</span>\n                  </p>\n                </template>\n                <p v-else class=\"no-selection\">请选择时间段</p>\n              </div>\n            </div>\n\n            <div class=\"step-actions\">\n              <el-button type=\"primary\" @click=\"nextStep\" :disabled=\"!canProceed\">下一步</el-button>\n              <el-button @click=\"prevStep\">上一步</el-button>\n            </div>\n          </template>\n        </el-card>\n      </div>\n\n      <!-- 步骤3：确认预约 -->\n      <div v-else-if=\"currentStep === 2\" class=\"step-content\">\n        <el-card shadow=\"hover\" class=\"confirm-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3>确认预约信息</h3>\n            </div>\n          </template>\n\n          <el-descriptions :column=\"1\" border>\n            <el-descriptions-item label=\"自习室\">{{ room?.name }}</el-descriptions-item>\n            <el-descriptions-item label=\"位置\">{{ room?.location }}</el-descriptions-item>\n            <el-descriptions-item label=\"座位编号\">{{ seat?.seat_number }}</el-descriptions-item>\n            <el-descriptions-item label=\"预约日期\">{{ selectedDate }}</el-descriptions-item>\n            <el-descriptions-item label=\"预约时间\">\n              {{ formatDateTime(selectedStartTime) }} -\n              {{ formatDateTime(selectedEndTime) }}\n              <span class=\"duration\">({{ calculateDuration() }})</span>\n            </el-descriptions-item>\n          </el-descriptions>\n\n          <div class=\"reservation-notes\">\n            <h4>预约须知</h4>\n            <ul>\n              <li>预约成功后，请在预约开始时间后30分钟内完成签到，否则系统将自动取消预约。</li>\n              <li>如需取消预约，请提前操作，避免影响信誉分。</li>\n              <li>请遵守自习室规定，保持安静，不要占用他人座位。</li>\n              <li>离开时请及时签退，方便他人使用。</li>\n            </ul>\n          </div>\n\n          <div class=\"step-actions\">\n            <el-button type=\"primary\" @click=\"submitReservation\" :loading=\"submitting\"\n              >确认预约</el-button\n            >\n            <el-button @click=\"prevStep\">上一步</el-button>\n          </div>\n        </el-card>\n      </div>\n\n      <!-- 步骤4：预约成功 -->\n      <div v-else-if=\"currentStep === 3\" class=\"step-content\">\n        <el-card shadow=\"hover\" class=\"success-card\">\n          <el-result icon=\"success\" title=\"预约成功\" sub-title=\"您已成功预约座位\">\n            <template #extra>\n              <div class=\"reservation-details\">\n                <p><strong>预约编号：</strong>{{ reservation?.id }}</p>\n                <p><strong>自习室：</strong>{{ room?.name }}</p>\n                <p><strong>座位编号：</strong>{{ seat?.seat_number }}</p>\n                <p>\n                  <strong>预约时间：</strong>{{ formatDateTime(reservation?.start_time) }} -\n                  {{ formatDateTime(reservation?.end_time) }}\n                </p>\n                <p><strong>签到码：</strong>{{ reservation?.reservation_code }}</p>\n              </div>\n\n              <div class=\"action-buttons\">\n                <el-button type=\"primary\" @click=\"$router.push('/user/reservations')\">\n                  查看我的预约\n                </el-button>\n                <el-button @click=\"$router.push('/seat/rooms')\"> 返回自习室列表 </el-button>\n              </div>\n            </template>\n          </el-result>\n        </el-card>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted, watch } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRoute, useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { ArrowLeft } from \"@element-plus/icons-vue\";\n\nexport default {\n  name: \"SeatReservation\",\n  setup() {\n    const store = useStore();\n    const route = useRoute();\n    const router = useRouter();\n\n    const loading = ref(true);\n    const loadingTimeSlots = ref(false);\n    const submitting = ref(false);\n    const currentStep = ref(0);\n\n    const room = ref(null);\n    const seat = ref(null);\n    const timeSlots = ref([]);\n    const reservation = ref(null);\n\n    const selectedDate = ref(\"\");\n    const selectedStartTime = ref(null);\n    const selectedEndTime = ref(null);\n\n    // 是否可以进行下一步\n    const canProceed = computed(() => {\n      if (currentStep.value === 0) {\n        return !!seat.value;\n      } else if (currentStep.value === 1) {\n        return !!selectedStartTime.value && !!selectedEndTime.value;\n      }\n      return true;\n    });\n\n    // 加载座位信息\n    const loadSeatInfo = async () => {\n      try {\n        loading.value = true;\n\n        const seatId = route.query.seatId;\n        if (!seatId) {\n          ElMessage.error(\"缺少座位ID参数\");\n          router.push(\"/seat/rooms\");\n          return;\n        }\n\n        // 加载座位信息\n        const seatData = await store.dispatch(\"seat/getSeatById\", seatId);\n        seat.value = seatData;\n\n        // 加载自习室信息\n        const roomData = await store.dispatch(\"seat/getRoomById\", seat.value.room);\n        room.value = roomData;\n\n        // 设置默认日期\n        if (route.query.date) {\n          selectedDate.value = route.query.date;\n        } else {\n          selectedDate.value = formatDateForSelect(new Date());\n        }\n\n        // 加载时间段\n        await loadTimeSlots();\n      } catch (error) {\n        ElMessage.error(\"加载座位信息失败\");\n        router.push(\"/seat/rooms\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 加载时间段\n    const loadTimeSlots = async () => {\n      try {\n        loadingTimeSlots.value = true;\n\n        // 重置选择的时间\n        selectedStartTime.value = null;\n        selectedEndTime.value = null;\n\n        // 加载时间段\n        const timeSlotsData = await store.dispatch(\"seat/getSeatTimeSlots\", {\n          seatId: seat.value.id,\n          date: selectedDate.value,\n        });\n\n        timeSlots.value = timeSlotsData.time_slots;\n      } catch (error) {\n        ElMessage.error(\"加载时间段失败\");\n      } finally {\n        loadingTimeSlots.value = false;\n      }\n    };\n\n    // 选择时间段\n    const selectTimeSlot = (slot) => {\n      if (!slot.is_available) return;\n\n      const startTime = new Date(slot.start_time);\n      const endTime = new Date(slot.end_time);\n\n      // 如果没有选择开始时间，或者已经选择了开始和结束时间\n      if (!selectedStartTime.value || (selectedStartTime.value && selectedEndTime.value)) {\n        selectedStartTime.value = startTime;\n        selectedEndTime.value = endTime;\n      }\n      // 如果已经选择了开始时间，但没有选择结束时间\n      else if (selectedStartTime.value && !selectedEndTime.value) {\n        // 如果选择的时间早于已选的开始时间，则作为新的开始时间\n        if (startTime < selectedStartTime.value) {\n          selectedStartTime.value = startTime;\n        }\n        // 如果选择的时间晚于已选的开始时间，则作为结束时间\n        else if (startTime > selectedStartTime.value) {\n          // 检查中间是否有不可用的时间段\n          const hasUnavailableSlot = timeSlots.value.some((s) => {\n            const slotStart = new Date(s.start_time);\n            const slotEnd = new Date(s.end_time);\n            return !s.is_available && slotStart >= selectedStartTime.value && slotEnd <= endTime;\n          });\n\n          if (hasUnavailableSlot) {\n            ElMessage.warning(\"所选时间段中包含已被预约的时间\");\n            return;\n          }\n\n          selectedEndTime.value = endTime;\n        }\n      }\n    };\n\n    // 判断时间段是否被选中\n    const isTimeSlotSelected = (slot) => {\n      if (!selectedStartTime.value) return false;\n\n      const slotStart = new Date(slot.start_time);\n      const slotEnd = new Date(slot.end_time);\n\n      if (selectedEndTime.value) {\n        return slotStart >= selectedStartTime.value && slotEnd <= selectedEndTime.value;\n      } else {\n        return slotStart.getTime() === selectedStartTime.value.getTime();\n      }\n    };\n\n    // 计算时长\n    const calculateDuration = () => {\n      if (!selectedStartTime.value || !selectedEndTime.value) return \"\";\n\n      const diffMs = selectedEndTime.value - selectedStartTime.value;\n      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\n\n      return `${diffHrs}小时${diffMins}分钟`;\n    };\n\n    // 禁用日期\n    const disabledDate = (date) => {\n      // 禁用过去的日期和7天后的日期\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n\n      const maxDate = new Date();\n      maxDate.setDate(maxDate.getDate() + 6);\n      maxDate.setHours(23, 59, 59, 999);\n\n      return date < today || date > maxDate;\n    };\n\n    // 提交预约\n    const submitReservation = async () => {\n      try {\n        submitting.value = true;\n\n        // 提交预约\n        const reservationData = await store.dispatch(\"seat/createReservation\", {\n          seatId: seat.value.id,\n          startTime: selectedStartTime.value.toISOString(),\n          endTime: selectedEndTime.value.toISOString(),\n        });\n\n        reservation.value = reservationData;\n\n        // 进入下一步\n        currentStep.value = 3;\n\n        ElMessage.success(\"预约成功\");\n      } catch (error) {\n        ElMessage.error(error.message || \"预约失败\");\n      } finally {\n        submitting.value = false;\n      }\n    };\n\n    // 下一步\n    const nextStep = () => {\n      if (!canProceed.value) return;\n      currentStep.value++;\n    };\n\n    // 上一步\n    const prevStep = () => {\n      currentStep.value--;\n    };\n\n    // 获取座位状态类型\n    const getSeatStatusType = (status) => {\n      switch (status) {\n        case \"available\":\n          return \"success\";\n        case \"occupied\":\n          return \"danger\";\n        case \"disabled\":\n          return \"info\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取座位状态文本\n    const getSeatStatusText = (status) => {\n      switch (status) {\n        case \"available\":\n          return \"可用\";\n        case \"occupied\":\n          return \"已占用\";\n        case \"disabled\":\n          return \"禁用\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化时间\n    const formatTime = (timeString) => {\n      if (!timeString) return \"\";\n\n      // 时间格式为 \"HH:MM:SS\"，只显示 \"HH:MM\"\n      return timeString.substring(0, 5);\n    };\n\n    // 格式化日期时间\n    const formatDateTime = (dateTime) => {\n      if (!dateTime) return \"\";\n\n      const date = new Date(dateTime);\n      return `${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 格式化时间段\n    const formatTimeSlot = (slot) => {\n      const start = new Date(slot.start_time);\n      const end = new Date(slot.end_time);\n\n      return `${padZero(start.getHours())}:${padZero(start.getMinutes())} - ${padZero(\n        end.getHours()\n      )}:${padZero(end.getMinutes())}`;\n    };\n\n    // 格式化日期（用于选择器值）\n    function formatDateForSelect(date) {\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`;\n    }\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n\n    // 监听日期变化\n    watch(selectedDate, () => {\n      loadTimeSlots();\n    });\n\n    onMounted(() => {\n      loadSeatInfo();\n    });\n\n    return {\n      loading,\n      loadingTimeSlots,\n      submitting,\n      currentStep,\n      room,\n      seat,\n      timeSlots,\n      reservation,\n      selectedDate,\n      selectedStartTime,\n      selectedEndTime,\n      canProceed,\n      loadTimeSlots,\n      selectTimeSlot,\n      isTimeSlotSelected,\n      calculateDuration,\n      disabledDate,\n      submitReservation,\n      nextStep,\n      prevStep,\n      getSeatStatusType,\n      getSeatStatusText,\n      formatTime,\n      formatDateTime,\n      formatTimeSlot,\n      ArrowLeft,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.seat-reservation {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  .header-left {\n    display: flex;\n    align-items: center;\n    gap: 15px;\n\n    h2 {\n      margin: 0;\n    }\n  }\n}\n\n.reservation-steps {\n  margin-bottom: 30px;\n}\n\n.loading-container {\n  padding: 40px 0;\n}\n\n.step-content {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  h3 {\n    margin: 0;\n  }\n\n  .date-selector {\n    display: flex;\n    align-items: center;\n    gap: 10px;\n  }\n}\n\n.step-actions {\n  margin-top: 20px;\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n}\n\n.loading-time-slots {\n  padding: 20px 0;\n}\n\n.time-slots-container {\n  .time-slots-header {\n    margin-bottom: 15px;\n\n    h4 {\n      margin: 0 0 5px 0;\n    }\n\n    .time-hint {\n      margin: 0;\n      color: #909399;\n      font-size: 14px;\n    }\n  }\n\n  .empty-time-slots {\n    padding: 20px 0;\n  }\n\n  .time-slots {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n    gap: 10px;\n    margin-bottom: 20px;\n\n    .time-slot {\n      padding: 10px;\n      border-radius: 4px;\n      border: 1px solid #dcdfe6;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      cursor: pointer;\n      transition: all 0.3s;\n\n      &:hover:not(.unavailable) {\n        border-color: #409eff;\n        background-color: #ecf5ff;\n      }\n\n      &.selected {\n        border-color: #409eff;\n        background-color: #ecf5ff;\n      }\n\n      &.unavailable {\n        opacity: 0.6;\n        cursor: not-allowed;\n      }\n\n      .time-range {\n        font-size: 14px;\n      }\n    }\n  }\n\n  .selected-time-range {\n    margin-top: 20px;\n    padding: 15px;\n    background-color: #f5f7fa;\n    border-radius: 4px;\n\n    h4 {\n      margin: 0 0 10px 0;\n    }\n\n    .selected-time {\n      margin: 0;\n      font-size: 16px;\n      font-weight: bold;\n      color: #409eff;\n\n      .duration {\n        margin-left: 10px;\n        font-size: 14px;\n        font-weight: normal;\n        color: #606266;\n      }\n    }\n\n    .no-selection {\n      margin: 0;\n      color: #909399;\n    }\n  }\n}\n\n.reservation-notes {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  h4 {\n    margin: 0 0 10px 0;\n  }\n\n  ul {\n    margin: 0;\n    padding-left: 20px;\n\n    li {\n      margin-bottom: 5px;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n  }\n}\n\n.reservation-details {\n  text-align: left;\n  margin-bottom: 20px;\n\n  p {\n    margin: 5px 0;\n  }\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n}\n\n.duration {\n  margin-left: 10px;\n  font-size: 14px;\n  color: #606266;\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EAH9BC,GAAA;EASwBD,KAAK,EAAC;;;EAT9BC,GAAA;EAsBoCD,KAAK,EAAC;;;EAtB1CC,GAAA;AAAA;;EAAAA,GAAA;EA2D2BD,KAAK,EAAC;;;EAQQA,KAAK,EAAC;AAAc;;EAG5CA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAe;;EAxExCC,GAAA;EAuFuCD,KAAK,EAAC;;;EAK5BA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAmB;;EAEzBA,KAAK,EAAC;AAAW;;EA/FpCC,GAAA;EAqGiDD,KAAK,EAAC;;;EArGvDC,GAAA;EAyG0BD,KAAK,EAAC;;oBAzGhC;;EAoHwBA,KAAK,EAAC;AAAY;;EAMvBA,KAAK,EAAC;AAAqB;;EA1H9CC,GAAA;EA6HqBD,KAAK,EAAC;;;EAGDA,KAAK,EAAC;AAAU;;EAhI1CC,GAAA;EAmI0BD,KAAK,EAAC;;;EAIfA,KAAK,EAAC;AAAc;;EASIA,KAAK,EAAC;AAAc;;EAgBzCA,KAAK,EAAC;AAAU;;EAcrBA,KAAK,EAAC;AAAc;;EAUMA,KAAK,EAAC;AAAc;;EAI1CA,KAAK,EAAC;AAAqB;;EAW3BA,KAAK,EAAC;AAAgB;;;;;;;;;;;;;uBAtMvCE,mBAAA,CAiNM,OAjNNC,UAiNM,GAhNJC,mBAAA,CAKM,OALNC,UAKM,GAJJD,mBAAA,CAGM,OAHNE,UAGM,GAFJC,YAAA,CAAmEC,oBAAA;IAAvDC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;IAAKC,IAAI,EAAEC,MAAA,CAAAC;;IAJlDC,OAAA,EAAAC,QAAA,CAI6D,MAAET,MAAA,QAAAA,MAAA,OAJ/DU,gBAAA,CAI6D,IAAE,E;IAJ/DC,CAAA;IAAAC,EAAA;yDAKQlB,mBAAA,CAAa,YAAT,MAAI,qB,KAIDY,MAAA,CAAAO,OAAO,I,cAAlBrB,mBAAA,CAEM,OAFNsB,UAEM,GADJjB,YAAA,CAAmCkB,sBAAA;IAArBC,IAAI,EAAE,EAAE;IAAEC,QAAQ,EAAR;yBAG1BzB,mBAAA,CAoMW0B,SAAA;IAjNf3B,GAAA;EAAA,IAcMM,YAAA,CAKWsB,mBAAA;IALAC,MAAM,EAAEd,MAAA,CAAAe,WAAW;IAAE,eAAa,EAAC,SAAS;IAAC/B,KAAK,EAAC;;IAdpEkB,OAAA,EAAAC,QAAA,CAeQ,MAAwB,CAAxBZ,YAAA,CAAwByB,kBAAA;MAAfC,KAAK,EAAC;IAAM,IACrB1B,YAAA,CAAwByB,kBAAA;MAAfC,KAAK,EAAC;IAAM,IACrB1B,YAAA,CAAwByB,kBAAA;MAAfC,KAAK,EAAC;IAAM,IACrB1B,YAAA,CAAwByB,kBAAA;MAAfC,KAAK,EAAC;IAAM,G;IAlB7BZ,CAAA;iCAqBMa,mBAAA,cAAiB,EACNlB,MAAA,CAAAe,WAAW,U,cAAtB7B,mBAAA,CA0CM,OA1CNiC,UA0CM,GAzCJ5B,YAAA,CAwCU6B,kBAAA;IAxCDC,MAAM,EAAC,OAAO;IAACrC,KAAK,EAAC;;IACjBsC,MAAM,EAAAnB,QAAA,CACf,MAEMT,MAAA,QAAAA,MAAA,OAFNN,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAa,IACtBI,mBAAA,CAAa,YAAT,MAAI,E;IA1BtBc,OAAA,EAAAC,QAAA,CAyBY,MAqCV,CAhCwBH,MAAA,CAAAuB,IAAI,I,cAClBC,YAAA,CAiBkBC,0BAAA;MAhD9BxC,GAAA;MA+B8ByC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAN;;MA/BzCzB,OAAA,EAAAC,QAAA,CAgCc,MAAyE,CAAzEZ,YAAA,CAAyEqC,+BAAA;QAAnDC,KAAK,EAAC;MAAK;QAhC/C3B,OAAA,EAAAC,QAAA,CAgCgD,MAAgB,CAhChEC,gBAAA,CAAA0B,gBAAA,CAgCmD9B,MAAA,CAAA+B,IAAI,EAAEC,IAAI,iB;QAhC7D3B,CAAA;UAiCcd,YAAA,CAA4EqC,+BAAA;QAAtDC,KAAK,EAAC;MAAI;QAjC9C3B,OAAA,EAAAC,QAAA,CAiC+C,MAAoB,CAjCnEC,gBAAA,CAAA0B,gBAAA,CAiCkD9B,MAAA,CAAA+B,IAAI,EAAEE,QAAQ,iB;QAjChE5B,CAAA;UAkCcd,YAAA,CAAgFqC,+BAAA;QAA1DC,KAAK,EAAC;MAAM;QAlChD3B,OAAA,EAAAC,QAAA,CAkCiD,MAAsB,CAlCvEC,gBAAA,CAAA0B,gBAAA,CAkCoD9B,MAAA,CAAAuB,IAAI,CAACW,WAAW,iB;QAlCpE7B,CAAA;UAmCcd,YAAA,CAEyBqC,+BAAA;QAFHC,KAAK,EAAC;MAAM;QAnChD3B,OAAA,EAAAC,QAAA,CAmCiD,MAEjC,CArChBC,gBAAA,CAAA0B,gBAAA,IAoCmB9B,MAAA,CAAAuB,IAAI,CAACY,GAAG,IAAInC,MAAA,CAAAuB,IAAI,CAACG,MAAM,oB;QApC1CrB,CAAA;UAsCcd,YAAA,CAIuBqC,+BAAA;QAJDC,KAAK,EAAC;MAAI;QAtC9C3B,OAAA,EAAAC,QAAA,CA4CA,MACW,CANmBH,MAAA,CAAAuB,IAAI,CAACa,eAAe,I,cAAlCZ,YAAA,CAA8Ea,iBAAA;UAvC9FpD,GAAA;UAuCoDqD,IAAI,EAAC,SAAS;UAACC,MAAM,EAAC;;UAvC1ErC,OAAA,EAAAC,QAAA,CAuCkF,MAAGT,MAAA,QAAAA,MAAA,OAvCrFU,gBAAA,CAuCkF,KAAG,E;UAvCrFC,CAAA;UAAAC,EAAA;cAAAY,mBAAA,gBAwC8BlB,MAAA,CAAAuB,IAAI,CAACiB,cAAc,I,cAAjChB,YAAA,CAA4Ea,iBAAA;UAxC5FpD,GAAA;UAwCmDqD,IAAI,EAAC,SAAS;UAACC,MAAM,EAAC;;UAxCzErC,OAAA,EAAAC,QAAA,CAwCiF,MAAET,MAAA,SAAAA,MAAA,QAxCnFU,gBAAA,CAwCiF,IAAE,E;UAxCnFC,CAAA;UAAAC,EAAA;cAAAY,mBAAA,gB,CAyC6BlB,MAAA,CAAAuB,IAAI,CAACa,eAAe,KAAKpC,MAAA,CAAAuB,IAAI,CAACiB,cAAc,I,cAAzDtD,mBAAA,CAAuE,QAzCvFuD,UAAA,EAyC2E,OAAK,KAzChFvB,mBAAA,e;QAAAb,CAAA;UA2Ccd,YAAA,CAIuBqC,+BAAA;QAJDC,KAAK,EAAC;MAAI;QA3C9C3B,OAAA,EAAAC,QAAA,CA4CgB,MAES,CAFTZ,YAAA,CAES8C,iBAAA;UAFAC,IAAI,EAAEtC,MAAA,CAAA0C,iBAAiB,CAAC1C,MAAA,CAAAuB,IAAI,CAACoB,MAAM;;UA5C5DzC,OAAA,EAAAC,QAAA,CA6CkB,MAAoC,CA7CtDC,gBAAA,CAAA0B,gBAAA,CA6CqB9B,MAAA,CAAA4C,iBAAiB,CAAC5C,MAAA,CAAAuB,IAAI,CAACoB,MAAM,kB;UA7ClDtC,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;yBAoDYmB,YAAA,CAIWqB,mBAAA;MAxDvB5D,GAAA;MAoDsB6D,WAAW,EAAC;;MApDlC5C,OAAA,EAAAC,QAAA,CAqDc,MAEY,CAFZZ,YAAA,CAEYC,oBAAA;QAFD8C,IAAI,EAAC,SAAS;QAAE7C,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACkD,IAAI;;QArD5D7C,OAAA,EAAAC,QAAA,CAqD6E,MAE/DT,MAAA,SAAAA,MAAA,QAvDdU,gBAAA,CAqD6E,SAE/D,E;QAvDdC,CAAA;QAAAC,EAAA;;MAAAD,CAAA;SA2DqBL,MAAA,CAAAuB,IAAI,I,cAAfrC,mBAAA,CAGM,OAHN8D,UAGM,GAFJzD,YAAA,CAA2DC,oBAAA;MAAhD8C,IAAI,EAAC,SAAS;MAAE7C,OAAK,EAAEO,MAAA,CAAAiD;;MA5D9C/C,OAAA,EAAAC,QAAA,CA4DwD,MAAGT,MAAA,SAAAA,MAAA,QA5D3DU,gBAAA,CA4DwD,KAAG,E;MA5D3DC,CAAA;MAAAC,EAAA;oCA6DYf,YAAA,CAAgEC,oBAAA;MAApDC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACkD,IAAI;;MA7D3C7C,OAAA,EAAAC,QAAA,CA6D4D,MAAIT,MAAA,SAAAA,MAAA,QA7DhEU,gBAAA,CA6D4D,MAAI,E;MA7DhEC,CAAA;MAAAC,EAAA;YAAAY,mBAAA,e;IAAAb,CAAA;UAmEsBL,MAAA,CAAAe,WAAW,U,cAA3B7B,mBAAA,CA0EM0B,SAAA;IA7IZ3B,GAAA;EAAA,IAkEMiC,mBAAA,cAAiB,EACjB9B,mBAAA,CA0EM,OA1EN8D,UA0EM,GAzEJ3D,YAAA,CAwEU6B,kBAAA;IAxEDC,MAAM,EAAC,OAAO;IAACrC,KAAK,EAAC;;IACjBsC,MAAM,EAAAnB,QAAA,CACf,MAcM,CAdNf,mBAAA,CAcM,OAdN+D,UAcM,G,4BAbJ/D,mBAAA,CAAe,YAAX,QAAM,sBACVA,mBAAA,CAWM,OAXNgE,WAWM,G,4BAVJhE,mBAAA,CAAgB,cAAV,KAAG,sBACTG,YAAA,CAQE8D,yBAAA;MAlFlBC,UAAA,EA2E2BtD,MAAA,CAAAuD,YAAY;MA3EvC,uBAAA7D,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA2E2BK,MAAA,CAAAuD,YAAY,GAAA5D,MAAA;MACrB2C,IAAI,EAAC,MAAM;MACXkB,WAAW,EAAC,MAAM;MAClBC,MAAM,EAAC,YAAY;MACnB,cAAY,EAAC,YAAY;MACxB,eAAa,EAAEzD,MAAA,CAAA0D,YAAY;MAC3BC,QAAM,EAAE3D,MAAA,CAAA4D;;IAjF3B1D,OAAA,EAAAC,QAAA,CAiGM,MAKsD,CAfvCH,MAAA,CAAA6D,gBAAgB,I,cAA3B3E,mBAAA,CAEM,OAFN4E,WAEM,GADJvE,YAAA,CAAkCkB,sBAAA;MAApBC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAR;2BAGzBzB,mBAAA,CAgDW0B,SAAA;MA3IrB3B,GAAA;IAAA,IA4FYG,mBAAA,CAyCM,OAzCN2E,WAyCM,GAxCJ3E,mBAAA,CAMM,OANN4E,WAMM,G,4BALJ5E,mBAAA,CAAc,YAAV,OAAK,sBACTA,mBAAA,CAGI,KAHJ6E,WAGI,EAHiB,SACb,GAAAnC,gBAAA,CAAG9B,MAAA,CAAAkE,UAAU,CAAClE,MAAA,CAAA+B,IAAI,EAAEoC,SAAS,KAAI,KACvC,GAAArC,gBAAA,CAAG9B,MAAA,CAAAkE,UAAU,CAAClE,MAAA,CAAA+B,IAAI,EAAEqC,UAAU,kB,GAIvBpE,MAAA,CAAAqE,SAAS,CAACC,MAAM,U,cAA3BpF,mBAAA,CAEM,OAFNqF,WAEM,GADJhF,YAAA,CAAuCsD,mBAAA;MAA7BC,WAAW,EAAC;IAAc,G,oBAGtC5D,mBAAA,CAeM,OAfNsF,WAeM,I,kBAdJtF,mBAAA,CAaM0B,SAAA,QAvHtB6D,WAAA,CA2G0CzE,MAAA,CAAAqE,SAAS,EA3GnD,CA2G0BK,IAAI,EAAEC,KAAK;2BADrBzF,mBAAA,CAaM;QAXHD,GAAG,EAAE0F,KAAK;QACX3F,KAAK,EA7GvB4F,eAAA,EA6GwB,WAAW;wBAC2BF,IAAI,CAACG,YAAY;oBAAgC7E,MAAA,CAAA8E,kBAAkB,CAACJ,IAAI;;QAInHjF,OAAK,EAAAE,MAAA,IAAEK,MAAA,CAAA+E,cAAc,CAACL,IAAI;UAE3BtF,mBAAA,CAA0D,QAA1D4F,WAA0D,EAAAlD,gBAAA,CAA9B9B,MAAA,CAAAiF,cAAc,CAACP,IAAI,mBACjCA,IAAI,CAACG,YAAY,I,cAA/BrD,YAAA,CAAwEa,iBAAA;QArH1FpD,GAAA;QAqHmDqD,IAAI,EAAC,SAAS;QAAC4C,IAAI,EAAC;;QArHvEhF,OAAA,EAAAC,QAAA,CAqH+E,MAAE,KAAAT,MAAA,SAAAA,MAAA,QArHjFU,gBAAA,CAqH+E,IAAE,E;QArHjFC,CAAA;QAAAC,EAAA;2BAsHkBkB,YAAA,CAAqDa,iBAAA;QAtHvEpD,GAAA;QAsHiCqD,IAAI,EAAC,QAAQ;QAAC4C,IAAI,EAAC;;QAtHpDhF,OAAA,EAAAC,QAAA,CAsH4D,MAAE,KAAAT,MAAA,SAAAA,MAAA,QAtH9DU,gBAAA,CAsH4D,IAAE,E;QAtH9DC,CAAA;QAAAC,EAAA;mCAAA6E,WAAA;uCA0Hc/F,mBAAA,CAUM,OAVNgG,WAUM,G,4BATJhG,mBAAA,CAAc,YAAV,OAAK,sBACOY,MAAA,CAAAqF,iBAAiB,IAAIrF,MAAA,CAAAsF,eAAe,I,cAClDpG,mBAAA,CAII,KAJJqG,WAII,GAjItBnF,gBAAA,CAAA0B,gBAAA,CA8HuB9B,MAAA,CAAAwF,cAAc,CAACxF,MAAA,CAAAqF,iBAAiB,KAAI,KACvC,GAAAvD,gBAAA,CAAG9B,MAAA,CAAAwF,cAAc,CAACxF,MAAA,CAAAsF,eAAe,KAAI,GACrC,iBAAAlG,mBAAA,CAAyD,QAAzDqG,WAAyD,EAAlC,GAAC,GAAA3D,gBAAA,CAAG9B,MAAA,CAAA0F,iBAAiB,MAAK,GAAC,gB,oBAGtDxG,mBAAA,CAAyC,KAAzCyG,WAAyC,EAAV,QAAM,G,KAIzCvG,mBAAA,CAGM,OAHNwG,WAGM,GAFJrG,YAAA,CAAmFC,oBAAA;MAAxE8C,IAAI,EAAC,SAAS;MAAE7C,OAAK,EAAEO,MAAA,CAAAiD,QAAQ;MAAG4C,QAAQ,GAAG7F,MAAA,CAAA8F;;MAxItE5F,OAAA,EAAAC,QAAA,CAwIkF,MAAGT,MAAA,SAAAA,MAAA,QAxIrFU,gBAAA,CAwIkF,KAAG,E;MAxIrFC,CAAA;MAAAC,EAAA;gDAyIcf,YAAA,CAA4CC,oBAAA;MAAhCC,OAAK,EAAEO,MAAA,CAAA+F;IAAQ;MAzIzC7F,OAAA,EAAAC,QAAA,CAyI2C,MAAGT,MAAA,SAAAA,MAAA,QAzI9CU,gBAAA,CAyI2C,KAAG,E;MAzI9CC,CAAA;MAAAC,EAAA;;IAAAD,CAAA;2DAgJsBL,MAAA,CAAAe,WAAW,U,cAA3B7B,mBAAA,CAqCM0B,SAAA;IArLZ3B,GAAA;EAAA,IA+IMiC,mBAAA,cAAiB,EACjB9B,mBAAA,CAqCM,OArCN4G,WAqCM,GApCJzG,YAAA,CAmCU6B,kBAAA;IAnCDC,MAAM,EAAC,OAAO;IAACrC,KAAK,EAAC;;IACjBsC,MAAM,EAAAnB,QAAA,CACf,MAEMT,MAAA,SAAAA,MAAA,QAFNN,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAa,IACtBI,mBAAA,CAAe,YAAX,QAAM,E;IApJxBc,OAAA,EAAAC,QAAA,CAwJU,MAUkB,CAVlBZ,YAAA,CAUkBkC,0BAAA;MAVAC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAN;;MAxJvCzB,OAAA,EAAAC,QAAA,CAyJY,MAAyE,CAAzEZ,YAAA,CAAyEqC,+BAAA;QAAnDC,KAAK,EAAC;MAAK;QAzJ7C3B,OAAA,EAAAC,QAAA,CAyJ8C,MAAgB,CAzJ9DC,gBAAA,CAAA0B,gBAAA,CAyJiD9B,MAAA,CAAA+B,IAAI,EAAEC,IAAI,iB;QAzJ3D3B,CAAA;UA0JYd,YAAA,CAA4EqC,+BAAA;QAAtDC,KAAK,EAAC;MAAI;QA1J5C3B,OAAA,EAAAC,QAAA,CA0J6C,MAAoB,CA1JjEC,gBAAA,CAAA0B,gBAAA,CA0JgD9B,MAAA,CAAA+B,IAAI,EAAEE,QAAQ,iB;QA1J9D5B,CAAA;UA2JYd,YAAA,CAAiFqC,+BAAA;QAA3DC,KAAK,EAAC;MAAM;QA3J9C3B,OAAA,EAAAC,QAAA,CA2J+C,MAAuB,CA3JtEC,gBAAA,CAAA0B,gBAAA,CA2JkD9B,MAAA,CAAAuB,IAAI,EAAEW,WAAW,iB;QA3JnE7B,CAAA;UA4JYd,YAAA,CAA4EqC,+BAAA;QAAtDC,KAAK,EAAC;MAAM;QA5J9C3B,OAAA,EAAAC,QAAA,CA4J+C,MAAkB,CA5JjEC,gBAAA,CAAA0B,gBAAA,CA4JkD9B,MAAA,CAAAuD,YAAY,iB;QA5J9DlD,CAAA;UA6JYd,YAAA,CAIuBqC,+BAAA;QAJDC,KAAK,EAAC;MAAM;QA7J9C3B,OAAA,EAAAC,QAAA,CA8Jc,MAAuC,CA9JrDC,gBAAA,CAAA0B,gBAAA,CA8JiB9B,MAAA,CAAAwF,cAAc,CAACxF,MAAA,CAAAqF,iBAAiB,KAAI,KACvC,GAAAvD,gBAAA,CAAG9B,MAAA,CAAAwF,cAAc,CAACxF,MAAA,CAAAsF,eAAe,KAAI,GACrC,iBAAAlG,mBAAA,CAAyD,QAAzD6G,WAAyD,EAAlC,GAAC,GAAAnE,gBAAA,CAAG9B,MAAA,CAAA0F,iBAAiB,MAAK,GAAC,gB;QAhKhErF,CAAA;;MAAAA,CAAA;oCAoKUjB,mBAAA,CAQM;MARDJ,KAAK,EAAC;IAAmB,IAC5BI,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAKK,aAJHA,mBAAA,CAA8C,YAA1C,uCAAqC,GACzCA,mBAAA,CAA8B,YAA1B,uBAAqB,GACzBA,mBAAA,CAAgC,YAA5B,yBAAuB,GAC3BA,mBAAA,CAAyB,YAArB,kBAAgB,E,wBAIxBA,mBAAA,CAKM,OALN8G,WAKM,GAJJ3G,YAAA,CAECC,oBAAA;MAFU8C,IAAI,EAAC,SAAS;MAAE7C,OAAK,EAAEO,MAAA,CAAAmG,iBAAiB;MAAG5F,OAAO,EAAEP,MAAA,CAAAoG;;MA/K3ElG,OAAA,EAAAC,QAAA,CAgLe,MAAIT,MAAA,SAAAA,MAAA,QAhLnBU,gBAAA,CAgLe,MAAI,E;MAhLnBC,CAAA;MAAAC,EAAA;+CAkLYf,YAAA,CAA4CC,oBAAA;MAAhCC,OAAK,EAAEO,MAAA,CAAA+F;IAAQ;MAlLvC7F,OAAA,EAAAC,QAAA,CAkLyC,MAAGT,MAAA,SAAAA,MAAA,QAlL5CU,gBAAA,CAkLyC,KAAG,E;MAlL5CC,CAAA;MAAAC,EAAA;;IAAAD,CAAA;IAAAC,EAAA;2DAwLsBN,MAAA,CAAAe,WAAW,U,cAA3B7B,mBAAA,CAwBM0B,SAAA;IAhNZ3B,GAAA;EAAA,IAuLMiC,mBAAA,cAAiB,EACjB9B,mBAAA,CAwBM,OAxBNiH,WAwBM,GAvBJ9G,YAAA,CAsBU6B,kBAAA;IAtBDC,MAAM,EAAC,OAAO;IAACrC,KAAK,EAAC;;IAzLtCkB,OAAA,EAAAC,QAAA,CA0LU,MAoBY,CApBZZ,YAAA,CAoBY+G,oBAAA;MApBDvG,IAAI,EAAC,SAAS;MAACkB,KAAK,EAAC,MAAM;MAAC,WAAS,EAAC;;MACpCsF,KAAK,EAAApG,QAAA,CACd,MASM,CATNf,mBAAA,CASM,OATNoH,WASM,GARJpH,mBAAA,CAAkD,Y,4BAA/CA,mBAAA,CAAsB,gBAAd,OAAK,sBA7LhCgB,gBAAA,CAAA0B,gBAAA,CA6L4C9B,MAAA,CAAAyG,WAAW,EAAEC,EAAE,iB,GAC3CtH,mBAAA,CAA4C,Y,4BAAzCA,mBAAA,CAAqB,gBAAb,MAAI,sBA9L/BgB,gBAAA,CAAA0B,gBAAA,CA8L2C9B,MAAA,CAAA+B,IAAI,EAAEC,IAAI,iB,GACrC5C,mBAAA,CAAoD,Y,4BAAjDA,mBAAA,CAAsB,gBAAd,OAAK,sBA/LhCgB,gBAAA,CAAA0B,gBAAA,CA+L4C9B,MAAA,CAAAuB,IAAI,EAAEW,WAAW,iB,GAC7C9C,mBAAA,CAGI,Y,4BAFFA,mBAAA,CAAsB,gBAAd,OAAK,sBAjM/BgB,gBAAA,CAAA0B,gBAAA,CAiM2C9B,MAAA,CAAAwF,cAAc,CAACxF,MAAA,CAAAyG,WAAW,EAAEE,UAAU,KAAI,KACnE,GAAA7E,gBAAA,CAAG9B,MAAA,CAAAwF,cAAc,CAACxF,MAAA,CAAAyG,WAAW,EAAEG,QAAQ,kB,GAEzCxH,mBAAA,CAA+D,Y,4BAA5DA,mBAAA,CAAqB,gBAAb,MAAI,sBApM/BgB,gBAAA,CAAA0B,gBAAA,CAoM2C9B,MAAA,CAAAyG,WAAW,EAAEI,gBAAgB,iB,KAG1DzH,mBAAA,CAKM,OALN0H,WAKM,GAJJvH,YAAA,CAEYC,oBAAA;QAFD8C,IAAI,EAAC,SAAS;QAAE7C,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACkD,IAAI;;QAxM9D7C,OAAA,EAAAC,QAAA,CAwMsF,MAEtET,MAAA,SAAAA,MAAA,QA1MhBU,gBAAA,CAwMsF,UAEtE,E;QA1MhBC,CAAA;QAAAC,EAAA;UA2MgBf,YAAA,CAAqEC,oBAAA;QAAzDC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACkD,IAAI;;QA3M/C7C,OAAA,EAAAC,QAAA,CA2MgE,MAAST,MAAA,SAAAA,MAAA,QA3MzEU,gBAAA,CA2MgE,WAAS,E;QA3MzEC,CAAA;QAAAC,EAAA;;MAAAD,CAAA;;IAAAA,CAAA;2DAAAa,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}