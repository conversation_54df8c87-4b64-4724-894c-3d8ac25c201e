{"ast": null, "code": "import { defineComponent, renderSlot } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  name: \"ElCollectionItem\",\n  inheritAttrs: false\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return renderSlot(_ctx.$slots, \"default\");\n}\nvar CollectionItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"collection-item.vue\"]]);\nexport { CollectionItem as default };", "map": {"version": 3, "names": ["defineComponent", "name", "inheritAttrs", "renderSlot", "_ctx", "$slots"], "sources": ["../../../../../../packages/components/collection/src/collection-item.vue"], "sourcesContent": ["<template>\n  <slot />\n</template>\n\n<script lang=\"ts\" setup>\ndefineOptions({\n  name: 'ElCollectionItem',\n  inheritAttrs: false,\n})\n</script>\n"], "mappings": ";;iCAKcA,eAAA;EACZC,IAAM;EACNC,YAAc;AAChB;;SAPEC,UAAQ,CAAAC,IAAA,CAAAC,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}