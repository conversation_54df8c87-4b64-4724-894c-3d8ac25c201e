{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport negate from './negate.js';\nimport pickBy from './pickBy.js';\n\n/**\n * The opposite of `_.pickBy`; this method creates an object composed of\n * the own and inherited enumerable string keyed properties of `object` that\n * `predicate` doesn't return truthy for. The predicate is invoked with two\n * arguments: (value, key).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The source object.\n * @param {Function} [predicate=_.identity] The function invoked per property.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.omitBy(object, _.isNumber);\n * // => { 'b': '2' }\n */\nfunction omitBy(object, predicate) {\n  return pickBy(object, negate(baseIteratee(predicate)));\n}\nexport default omitBy;", "map": {"version": 3, "names": ["baseIteratee", "negate", "pickBy", "omitBy", "object", "predicate"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/omitBy.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport negate from './negate.js';\nimport pickBy from './pickBy.js';\n\n/**\n * The opposite of `_.pickBy`; this method creates an object composed of\n * the own and inherited enumerable string keyed properties of `object` that\n * `predicate` doesn't return truthy for. The predicate is invoked with two\n * arguments: (value, key).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The source object.\n * @param {Function} [predicate=_.identity] The function invoked per property.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.omitBy(object, _.isNumber);\n * // => { 'b': '2' }\n */\nfunction omitBy(object, predicate) {\n  return pickBy(object, negate(baseIteratee(predicate)));\n}\n\nexport default omitBy;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,MAAM,MAAM,aAAa;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,MAAM,EAAEC,SAAS,EAAE;EACjC,OAAOH,MAAM,CAACE,MAAM,EAAEH,MAAM,CAACD,YAAY,CAACK,SAAS,CAAC,CAAC,CAAC;AACxD;AAEA,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}