{"ast": null, "code": "import { isString } from '@vue/shared';\nclass ElementPlusError extends Error {\n  constructor(m) {\n    super(m);\n    this.name = \"ElementPlusError\";\n  }\n}\nfunction throwError(scope, m) {\n  throw new ElementPlusError(`[${scope}] ${m}`);\n}\nfunction debugWarn(scope, message) {\n  if (process.env.NODE_ENV !== \"production\") {\n    const error = isString(scope) ? new ElementPlusError(`[${scope}] ${message}`) : scope;\n    console.warn(error);\n  }\n}\nexport { debugWarn, throwError };", "map": {"version": 3, "names": ["ElementPlusError", "Error", "constructor", "m", "name", "throwError", "scope", "debugWarn", "message", "process", "env", "NODE_ENV", "error", "isString", "console", "warn"], "sources": ["../../../../packages/utils/error.ts"], "sourcesContent": ["import { isString } from './types'\n\nclass ElementPlusError extends Error {\n  constructor(m: string) {\n    super(m)\n    this.name = 'ElementPlusError'\n  }\n}\n\nexport function throwError(scope: string, m: string): never {\n  throw new ElementPlusError(`[${scope}] ${m}`)\n}\n\nexport function debugWarn(err: Error): void\nexport function debugWarn(scope: string, message: string): void\nexport function debugWarn(scope: string | Error, message?: string): void {\n  if (process.env.NODE_ENV !== 'production') {\n    const error: Error = isString(scope)\n      ? new ElementPlusError(`[${scope}] ${message}`)\n      : scope\n    // eslint-disable-next-line no-console\n    console.warn(error)\n  }\n}\n"], "mappings": ";AACA,MAAMA,gBAAgB,SAASC,KAAK,CAAC;EACnCC,WAAWA,CAACC,CAAC,EAAE;IACb,KAAK,CAACA,CAAC,CAAC;IACR,IAAI,CAACC,IAAI,GAAG,kBAAkB;EAClC;AACA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAEH,CAAC,EAAE;EACnC,MAAM,IAAIH,gBAAgB,CAAC,IAAIM,KAAK,KAAKH,CAAC,EAAE,CAAC;AAC/C;AACO,SAASI,SAASA,CAACD,KAAK,EAAEE,OAAO,EAAE;EACxC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,KAAK,GAAGC,QAAQ,CAACP,KAAK,CAAC,GAAG,IAAIN,gBAAgB,CAAC,IAAIM,KAAK,KAAKE,OAAO,EAAE,CAAC,GAAGF,KAAK;IACrFQ,OAAO,CAACC,IAAI,CAACH,KAAK,CAAC;EACvB;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}