{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, ref, computed, watch, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, createBlock, withCtx, resolveDynamicComponent, renderSlot } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { avatarProps, avatarEmits } from './avatar.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isString } from '@vue/shared';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nconst __default__ = defineComponent({\n  name: \"ElAvatar\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: avatarProps,\n  emits: avatarEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"avatar\");\n    const hasLoadError = ref(false);\n    const avatarClass = computed(() => {\n      const {\n        size,\n        icon,\n        shape\n      } = props;\n      const classList = [ns.b()];\n      if (isString(size)) classList.push(ns.m(size));\n      if (icon) classList.push(ns.m(\"icon\"));\n      if (shape) classList.push(ns.m(shape));\n      return classList;\n    });\n    const sizeStyle = computed(() => {\n      const {\n        size\n      } = props;\n      return isNumber(size) ? ns.cssVarBlock({\n        size: addUnit(size) || \"\"\n      }) : void 0;\n    });\n    const fitStyle = computed(() => ({\n      objectFit: props.fit\n    }));\n    watch(() => props.src, () => hasLoadError.value = false);\n    function handleError(e) {\n      hasLoadError.value = true;\n      emit(\"error\", e);\n    }\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"span\", {\n        class: normalizeClass(unref(avatarClass)),\n        style: normalizeStyle(unref(sizeStyle))\n      }, [(_ctx.src || _ctx.srcSet) && !hasLoadError.value ? (openBlock(), createElementBlock(\"img\", {\n        key: 0,\n        src: _ctx.src,\n        alt: _ctx.alt,\n        srcset: _ctx.srcSet,\n        style: normalizeStyle(unref(fitStyle)),\n        onError: handleError\n      }, null, 44, [\"src\", \"alt\", \"srcset\"])) : _ctx.icon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 1\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))]),\n        _: 1\n      })) : renderSlot(_ctx.$slots, \"default\", {\n        key: 2\n      })], 6);\n    };\n  }\n});\nvar Avatar = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"avatar.vue\"]]);\nexport { Avatar as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace", "hasLoadError", "ref", "avatarClass", "computed", "size", "icon", "shape", "props", "classList", "b", "isString", "push", "m", "sizeStyle", "isNumber", "cssVarBlock", "addUnit", "fitStyle", "objectFit", "fit", "watch", "src", "value", "handleError", "e", "emit"], "sources": ["../../../../../../packages/components/avatar/src/avatar.vue"], "sourcesContent": ["<template>\n  <span :class=\"avatarClass\" :style=\"sizeStyle\">\n    <img\n      v-if=\"(src || srcSet) && !hasLoadError\"\n      :src=\"src\"\n      :alt=\"alt\"\n      :srcset=\"srcSet\"\n      :style=\"fitStyle\"\n      @error=\"handleError\"\n    />\n    <el-icon v-else-if=\"icon\">\n      <component :is=\"icon\" />\n    </el-icon>\n    <slot v-else />\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, watch } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useNamespace } from '@element-plus/hooks'\nimport { addUnit, isNumber, isString } from '@element-plus/utils'\nimport { avatarEmits, avatarProps } from './avatar'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElAvatar',\n})\n\nconst props = defineProps(avatarProps)\nconst emit = defineEmits(avatarEmits)\n\nconst ns = useNamespace('avatar')\n\nconst hasLoadError = ref(false)\n\nconst avatarClass = computed(() => {\n  const { size, icon, shape } = props\n  const classList = [ns.b()]\n  if (isString(size)) classList.push(ns.m(size))\n  if (icon) classList.push(ns.m('icon'))\n  if (shape) classList.push(ns.m(shape))\n  return classList\n})\n\nconst sizeStyle = computed(() => {\n  const { size } = props\n  return isNumber(size)\n    ? (ns.cssVarBlock({\n        size: addUnit(size) || '',\n      }) as CSSProperties)\n    : undefined\n})\n\nconst fitStyle = computed<CSSProperties>(() => ({\n  objectFit: props.fit,\n}))\n\n// need reset hasLoadError to false if src changed\nwatch(\n  () => props.src,\n  () => (hasLoadError.value = false)\n)\n\nfunction handleError(e: Event) {\n  hasLoadError.value = true\n  emit('error', e)\n}\n</script>\n"], "mappings": ";;;;;;;;;mCA0Bc;EACZA,IAAM;AACR;;;;;;;;;IAKM,MAAAC,EAAA,GAAKC,YAAA,CAAa,QAAQ;IAE1B,MAAAC,YAAA,GAAeC,GAAA,CAAI,KAAK;IAExB,MAAAC,WAAA,GAAcC,QAAA,CAAS,MAAM;MACjC,MAAM;QAAEC,IAAA;QAAMC,IAAM;QAAAC;MAAA,CAAU,GAAAC,KAAA;MAC9B,MAAMC,SAAY,IAACV,EAAG,CAAAW,CAAA,EAAG;MACrB,IAAAC,QAAA,CAASN,IAAI,CAAG,EACpBI,SAAoB,CAAAG,IAAA,CAAAb,EAAA,CAAAc,CAAA,CAAAR,IAAQ;MAC5B,IAAIC,IAAA,EACGG,SAAA,CAAAG,IAAA,CAAAb,EAAA,CAAAc,CAAA;MACR,IAAAN,KAAA,EAEKE,SAAA,CAAAG,IAAY,CAAAb,EAAA,CAAAc,CAAA,CAAAN,KAAe;MACzB,OAAAE,SAAW;IACjB;IAEM,MAAAK,SAAc,GAAAV,QAAS;MACzB,MACA;QAAAC;MAAA,IAAAG,KAAA;MACL,OAAAO,QAAA,CAAAV,IAAA,IAAAN,EAAA,CAAAiB,WAAA;QAEKX,IAAA,EAAAY,OAAW,CAAAZ,IAAA,KAA+B;MAAA,WACnC;IAAM,CACjB;IAGF,MAAAa,QAAA,GAAAd,QAAA;MACEe,SAAY,EAAAX,KAAA,CAAAY;IAAA,CACZ;IACFC,KAAA,OAAAb,KAAA,CAAAc,GAAA,QAAArB,YAAA,CAAAsB,KAAA;IAEA,SAASC,YAAYC,CAAU;MAC7BxB,YAAA,CAAasB,KAAQ;MACrBG,IAAA,CAAK,SAASD,CAAC;IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}