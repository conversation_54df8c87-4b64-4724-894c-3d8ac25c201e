{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { watch } from 'vue';\nimport { isClient, useEventListener } from '@vueuse/core';\nimport { EVENT_CODE } from '../../constants/aria.mjs';\nconst modalStack = [];\nconst closeModal = e => {\n  if (modalStack.length === 0) return;\n  if (e.code === EVENT_CODE.esc) {\n    e.stopPropagation();\n    const topModal = modalStack[modalStack.length - 1];\n    topModal.handleClose();\n  }\n};\nconst useModal = (instance, visibleRef) => {\n  watch(visibleRef, val => {\n    if (val) {\n      modalStack.push(instance);\n    } else {\n      modalStack.splice(modalStack.indexOf(instance), 1);\n    }\n  });\n};\nif (isClient) useEventListener(document, \"keydown\", closeModal);\nexport { useModal };", "map": {"version": 3, "names": ["modalStack", "closeModal", "e", "length", "code", "EVENT_CODE", "esc", "stopPropagation", "topModal", "handleClose", "useModal", "instance", "visibleRef", "watch", "val", "push", "splice", "indexOf", "isClient", "useEventListener", "document"], "sources": ["../../../../../packages/hooks/use-modal/index.ts"], "sourcesContent": ["import { watch } from 'vue'\nimport { isClient, useEventListener } from '@vueuse/core'\nimport { EVENT_CODE } from '@element-plus/constants'\n\nimport type { Ref } from 'vue'\n\ntype ModalInstance = {\n  handleClose: () => void\n}\n\nconst modalStack: ModalInstance[] = []\n\nconst closeModal = (e: KeyboardEvent) => {\n  if (modalStack.length === 0) return\n  if (e.code === EVENT_CODE.esc) {\n    e.stopPropagation()\n    const topModal = modalStack[modalStack.length - 1]\n    topModal.handleClose()\n  }\n}\n\nexport const useModal = (instance: ModalInstance, visibleRef: Ref<boolean>) => {\n  watch(visibleRef, (val) => {\n    if (val) {\n      modalStack.push(instance)\n    } else {\n      modalStack.splice(modalStack.indexOf(instance), 1)\n    }\n  })\n}\n\nif (isClient) useEventListener(document, 'keydown', closeModal)\n"], "mappings": ";;;;AAGA,MAAMA,UAAU,GAAG,EAAE;AACrB,MAAMC,UAAU,GAAIC,CAAC,IAAK;EACxB,IAAIF,UAAU,CAACG,MAAM,KAAK,CAAC,EACzB;EACF,IAAID,CAAC,CAACE,IAAI,KAAKC,UAAU,CAACC,GAAG,EAAE;IAC7BJ,CAAC,CAACK,eAAe,EAAE;IACnB,MAAMC,QAAQ,GAAGR,UAAU,CAACA,UAAU,CAACG,MAAM,GAAG,CAAC,CAAC;IAClDK,QAAQ,CAACC,WAAW,EAAE;EAC1B;AACA,CAAC;AACW,MAACC,QAAQ,GAAGA,CAACC,QAAQ,EAAEC,UAAU,KAAK;EAChDC,KAAK,CAACD,UAAU,EAAGE,GAAG,IAAK;IACzB,IAAIA,GAAG,EAAE;MACPd,UAAU,CAACe,IAAI,CAACJ,QAAQ,CAAC;IAC/B,CAAK,MAAM;MACLX,UAAU,CAACgB,MAAM,CAAChB,UAAU,CAACiB,OAAO,CAACN,QAAQ,CAAC,EAAE,CAAC,CAAC;IACxD;EACA,CAAG,CAAC;AACJ;AACA,IAAIO,QAAQ,EACVC,gBAAgB,CAACC,QAAQ,EAAE,SAAS,EAAEnB,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}