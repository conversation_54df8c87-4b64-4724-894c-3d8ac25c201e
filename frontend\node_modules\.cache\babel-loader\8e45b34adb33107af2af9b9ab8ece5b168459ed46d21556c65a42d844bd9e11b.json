{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isArray, isDate } from '@vue/shared';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT } from '../../../constants/event.mjs';\nconst isValidRange = range => isArray(range) && range.length === 2 && range.every(item => isDate(item));\nconst calendarProps = buildProps({\n  modelValue: {\n    type: Date\n  },\n  range: {\n    type: definePropType(Array),\n    validator: isValidRange\n  }\n});\nconst calendarEmits = {\n  [UPDATE_MODEL_EVENT]: value => isDate(value),\n  [INPUT_EVENT]: value => isDate(value)\n};\nexport { calendarEmits, calendarProps };", "map": {"version": 3, "names": ["isValidRange", "range", "isArray", "length", "every", "item", "isDate", "calendarProps", "buildProps", "modelValue", "type", "Date", "definePropType", "Array", "validator", "calendarEmits", "UPDATE_MODEL_EVENT", "value", "INPUT_EVENT"], "sources": ["../../../../../../packages/components/calendar/src/calendar.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isArray,\n  isDate,\n} from '@element-plus/utils'\nimport { INPUT_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport type { ExtractPropTypes } from 'vue'\n\nexport type CalendarDateType =\n  | 'prev-month'\n  | 'next-month'\n  | 'prev-year'\n  | 'next-year'\n  | 'today'\n\nconst isValidRange = (range: unknown): range is [Date, Date] =>\n  isArray(range) && range.length === 2 && range.every((item) => isDate(item))\n\nexport const calendarProps = buildProps({\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: Date,\n  },\n  /**\n   * @description time range, including start time and end time.\n   *   Start time must be start day of week, end time must be end day of week, the time span cannot exceed two months.\n   */\n  range: {\n    type: definePropType<[Date, Date]>(Array),\n    validator: isValidRange,\n  },\n} as const)\nexport type CalendarProps = ExtractPropTypes<typeof calendarProps>\n\nexport const calendarEmits = {\n  [UPDATE_MODEL_EVENT]: (value: Date) => isDate(value),\n  [INPUT_EVENT]: (value: Date) => isDate(value),\n}\nexport type CalendarEmits = typeof calendarEmits\n"], "mappings": ";;;;;AAOA,MAAMA,YAAY,GAAIC,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,IAAIA,KAAK,CAACE,MAAM,KAAK,CAAC,IAAIF,KAAK,CAACG,KAAK,CAAEC,IAAI,IAAKC,MAAM,CAACD,IAAI,CAAC,CAAC;AAC/F,MAACE,aAAa,GAAGC,UAAU,CAAC;EACtCC,UAAU,EAAE;IACVC,IAAI,EAAEC;EACV,CAAG;EACDV,KAAK,EAAE;IACLS,IAAI,EAAEE,cAAc,CAACC,KAAK,CAAC;IAC3BC,SAAS,EAAEd;EACf;AACA,CAAC;AACW,MAACe,aAAa,GAAG;EAC3B,CAACC,kBAAkB,GAAIC,KAAK,IAAKX,MAAM,CAACW,KAAK,CAAC;EAC9C,CAACC,WAAW,GAAID,KAAK,IAAKX,MAAM,CAACW,KAAK;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}