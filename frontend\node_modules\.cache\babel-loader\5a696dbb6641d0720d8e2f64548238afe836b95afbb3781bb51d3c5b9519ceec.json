{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { ref, computed, onMounted, watch } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRoute, useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { ArrowLeft } from \"@element-plus/icons-vue\";\nexport default {\n  name: \"SeatReservation\",\n  setup() {\n    const store = useStore();\n    const route = useRoute();\n    const router = useRouter();\n    const loading = ref(true);\n    const loadingTimeSlots = ref(false);\n    const submitting = ref(false);\n    const currentStep = ref(0);\n    const room = ref(null);\n    const seat = ref(null);\n    const timeSlots = ref([]);\n    const reservation = ref(null);\n    const selectedDate = ref(\"\");\n    const selectedStartTime = ref(null);\n    const selectedEndTime = ref(null);\n\n    // 是否可以进行下一步\n    const canProceed = computed(() => {\n      if (currentStep.value === 0) {\n        return !!seat.value;\n      } else if (currentStep.value === 1) {\n        return !!selectedStartTime.value && !!selectedEndTime.value;\n      }\n      return true;\n    });\n\n    // 加载座位信息\n    const loadSeatInfo = async () => {\n      try {\n        loading.value = true;\n        const seatId = route.query.seatId;\n        if (!seatId) {\n          ElMessage.error(\"缺少座位ID参数\");\n          router.push(\"/seat/rooms\");\n          return;\n        }\n\n        // 加载座位信息\n        const seatData = await store.dispatch(\"seat/getSeatById\", seatId);\n        seat.value = seatData;\n\n        // 加载自习室信息\n        const roomData = await store.dispatch(\"seat/getRoomById\", seat.value.room);\n        room.value = roomData;\n\n        // 设置默认日期\n        if (route.query.date) {\n          selectedDate.value = route.query.date;\n        } else {\n          selectedDate.value = formatDateForSelect(new Date());\n        }\n\n        // 加载时间段\n        await loadTimeSlots();\n      } catch (error) {\n        ElMessage.error(\"加载座位信息失败\");\n        router.push(\"/seat/rooms\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 加载时间段\n    const loadTimeSlots = async () => {\n      try {\n        loadingTimeSlots.value = true;\n\n        // 重置选择的时间\n        selectedStartTime.value = null;\n        selectedEndTime.value = null;\n\n        // 加载时间段\n        const timeSlotsData = await store.dispatch(\"seat/getSeatTimeSlots\", {\n          seatId: seat.value.id,\n          date: selectedDate.value\n        });\n        timeSlots.value = timeSlotsData.time_slots;\n      } catch (error) {\n        ElMessage.error(\"加载时间段失败\");\n      } finally {\n        loadingTimeSlots.value = false;\n      }\n    };\n\n    // 选择时间段\n    const selectTimeSlot = slot => {\n      if (!slot.is_available) return;\n      const startTime = new Date(slot.start_time);\n      const endTime = new Date(slot.end_time);\n\n      // 如果没有选择开始时间，或者已经选择了开始和结束时间\n      if (!selectedStartTime.value || selectedStartTime.value && selectedEndTime.value) {\n        selectedStartTime.value = startTime;\n        selectedEndTime.value = endTime;\n      }\n      // 如果已经选择了开始时间，但没有选择结束时间\n      else if (selectedStartTime.value && !selectedEndTime.value) {\n        // 如果选择的时间早于已选的开始时间，则作为新的开始时间\n        if (startTime < selectedStartTime.value) {\n          selectedStartTime.value = startTime;\n        }\n        // 如果选择的时间晚于已选的开始时间，则作为结束时间\n        else if (startTime > selectedStartTime.value) {\n          // 检查中间是否有不可用的时间段\n          const hasUnavailableSlot = timeSlots.value.some(s => {\n            const slotStart = new Date(s.start_time);\n            const slotEnd = new Date(s.end_time);\n            return !s.is_available && slotStart >= selectedStartTime.value && slotEnd <= endTime;\n          });\n          if (hasUnavailableSlot) {\n            ElMessage.warning(\"所选时间段中包含已被预约的时间\");\n            return;\n          }\n          selectedEndTime.value = endTime;\n        }\n      }\n    };\n\n    // 判断时间段是否被选中\n    const isTimeSlotSelected = slot => {\n      if (!selectedStartTime.value) return false;\n      const slotStart = new Date(slot.start_time);\n      const slotEnd = new Date(slot.end_time);\n      if (selectedEndTime.value) {\n        return slotStart >= selectedStartTime.value && slotEnd <= selectedEndTime.value;\n      } else {\n        return slotStart.getTime() === selectedStartTime.value.getTime();\n      }\n    };\n\n    // 计算时长\n    const calculateDuration = () => {\n      if (!selectedStartTime.value || !selectedEndTime.value) return \"\";\n      const diffMs = selectedEndTime.value - selectedStartTime.value;\n      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffMins = Math.floor(diffMs % (1000 * 60 * 60) / (1000 * 60));\n      return `${diffHrs}小时${diffMins}分钟`;\n    };\n\n    // 禁用日期\n    const disabledDate = date => {\n      // 禁用过去的日期和7天后的日期\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const maxDate = new Date();\n      maxDate.setDate(maxDate.getDate() + 6);\n      maxDate.setHours(23, 59, 59, 999);\n      return date < today || date > maxDate;\n    };\n\n    // 提交预约\n    const submitReservation = async () => {\n      try {\n        submitting.value = true;\n\n        // 提交预约\n        const reservationData = await store.dispatch(\"seat/createReservation\", {\n          seatId: seat.value.id,\n          startTime: selectedStartTime.value.toISOString(),\n          endTime: selectedEndTime.value.toISOString()\n        });\n        reservation.value = reservationData;\n\n        // 进入下一步\n        currentStep.value = 3;\n        ElMessage.success(\"预约成功\");\n      } catch (error) {\n        ElMessage.error(error.message || \"预约失败\");\n      } finally {\n        submitting.value = false;\n      }\n    };\n\n    // 下一步\n    const nextStep = () => {\n      if (!canProceed.value) return;\n      currentStep.value++;\n    };\n\n    // 上一步\n    const prevStep = () => {\n      currentStep.value--;\n    };\n\n    // 获取座位状态类型\n    const getSeatStatusType = status => {\n      switch (status) {\n        case \"available\":\n          return \"success\";\n        case \"occupied\":\n          return \"danger\";\n        case \"disabled\":\n          return \"info\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取座位状态文本\n    const getSeatStatusText = status => {\n      switch (status) {\n        case \"available\":\n          return \"可用\";\n        case \"occupied\":\n          return \"已占用\";\n        case \"disabled\":\n          return \"禁用\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化时间\n    const formatTime = timeString => {\n      if (!timeString) return \"\";\n\n      // 时间格式为 \"HH:MM:SS\"，只显示 \"HH:MM\"\n      return timeString.substring(0, 5);\n    };\n\n    // 格式化日期时间\n    const formatDateTime = dateTime => {\n      if (!dateTime) return \"\";\n      const date = new Date(dateTime);\n      return `${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 格式化时间段\n    const formatTimeSlot = slot => {\n      const start = new Date(slot.start_time);\n      const end = new Date(slot.end_time);\n      return `${padZero(start.getHours())}:${padZero(start.getMinutes())} - ${padZero(end.getHours())}:${padZero(end.getMinutes())}`;\n    };\n\n    // 格式化日期（用于选择器值）\n    function formatDateForSelect(date) {\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`;\n    }\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n\n    // 监听日期变化\n    watch(selectedDate, () => {\n      loadTimeSlots();\n    });\n    onMounted(() => {\n      loadSeatInfo();\n    });\n    return {\n      loading,\n      loadingTimeSlots,\n      submitting,\n      currentStep,\n      room,\n      seat,\n      timeSlots,\n      reservation,\n      selectedDate,\n      selectedStartTime,\n      selectedEndTime,\n      canProceed,\n      loadTimeSlots,\n      selectTimeSlot,\n      isTimeSlotSelected,\n      calculateDuration,\n      disabledDate,\n      submitReservation,\n      nextStep,\n      prevStep,\n      getSeatStatusType,\n      getSeatStatusText,\n      formatTime,\n      formatDateTime,\n      formatTimeSlot,\n      ArrowLeft\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "watch", "useStore", "useRoute", "useRouter", "ElMessage", "ArrowLeft", "name", "setup", "store", "route", "router", "loading", "loadingTimeSlots", "submitting", "currentStep", "room", "seat", "timeSlots", "reservation", "selectedDate", "selectedStartTime", "selectedEndTime", "canProceed", "value", "loadSeatInfo", "seatId", "query", "error", "push", "seatData", "dispatch", "roomData", "date", "formatDateForSelect", "Date", "loadTimeSlots", "timeSlotsData", "id", "time_slots", "selectTimeSlot", "slot", "is_available", "startTime", "start_time", "endTime", "end_time", "hasUnavailableSlot", "some", "s", "slotStart", "slotEnd", "warning", "isTimeSlotSelected", "getTime", "calculateDuration", "diffMs", "diffHrs", "Math", "floor", "diffMins", "disabledDate", "today", "setHours", "maxDate", "setDate", "getDate", "submitReservation", "reservationData", "toISOString", "success", "message", "nextStep", "prevStep", "getSeatStatusType", "status", "getSeatStatusText", "formatTime", "timeString", "substring", "formatDateTime", "dateTime", "padZero", "getHours", "getMinutes", "formatTimeSlot", "start", "end", "getFullYear", "getMonth", "num"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\SeatReservation.vue"], "sourcesContent": ["<template>\n  <div class=\"seat-reservation\">\n    <div class=\"page-header\">\n      <div class=\"header-left\">\n        <el-button @click=\"$router.back()\" :icon=\"ArrowLeft\">返回</el-button>\n        <h2>座位预约</h2>\n      </div>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"10\" animated />\n    </div>\n\n    <template v-else>\n      <el-steps\n        :active=\"currentStep\"\n        finish-status=\"success\"\n        class=\"reservation-steps\"\n      >\n        <el-step title=\"选择座位\" />\n        <el-step title=\"选择时间\" />\n        <el-step title=\"确认预约\" />\n        <el-step title=\"预约成功\" />\n      </el-steps>\n\n      <!-- 步骤1：选择座位 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <el-card shadow=\"hover\" class=\"seat-info-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3>座位信息</h3>\n            </div>\n          </template>\n\n          <template v-if=\"seat\">\n            <el-descriptions :column=\"2\" border>\n              <el-descriptions-item label=\"自习室\">{{\n                room?.name\n              }}</el-descriptions-item>\n              <el-descriptions-item label=\"位置\">{{\n                room?.location\n              }}</el-descriptions-item>\n              <el-descriptions-item label=\"座位编号\">{{\n                seat.seat_number\n              }}</el-descriptions-item>\n              <el-descriptions-item label=\"座位位置\">{{\n                `${seat.row}排${seat.column}列`\n              }}</el-descriptions-item>\n              <el-descriptions-item label=\"设施\">\n                <el-tag\n                  v-if=\"seat.is_power_outlet\"\n                  type=\"success\"\n                  effect=\"plain\"\n                  >有电源</el-tag\n                >\n                <el-tag v-if=\"seat.is_window_seat\" type=\"success\" effect=\"plain\"\n                  >靠窗</el-tag\n                >\n                <span v-if=\"!seat.is_power_outlet && !seat.is_window_seat\"\n                  >无特殊设施</span\n                >\n              </el-descriptions-item>\n              <el-descriptions-item label=\"状态\">\n                <el-tag :type=\"getSeatStatusType(seat.status)\">\n                  {{ getSeatStatusText(seat.status) }}\n                </el-tag>\n              </el-descriptions-item>\n            </el-descriptions>\n          </template>\n\n          <template v-else>\n            <el-empty description=\"未选择座位\">\n              <el-button type=\"primary\" @click=\"$router.push('/seat/rooms')\">\n                去选择座位\n              </el-button>\n            </el-empty>\n          </template>\n\n          <div v-if=\"seat\" class=\"step-actions\">\n            <el-button type=\"primary\" @click=\"nextStep\">下一步</el-button>\n            <el-button @click=\"$router.push('/seat/rooms')\">重新选择</el-button>\n          </div>\n        </el-card>\n      </div>\n\n      <!-- 步骤2：选择时间 -->\n      <div v-else-if=\"currentStep === 1\" class=\"step-content\">\n        <el-card shadow=\"hover\" class=\"time-selection-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3>选择预约时间</h3>\n              <div class=\"date-selector\">\n                <span>日期：</span>\n                <el-date-picker\n                  v-model=\"selectedDate\"\n                  type=\"date\"\n                  placeholder=\"选择日期\"\n                  format=\"YYYY-MM-DD\"\n                  value-format=\"YYYY-MM-DD\"\n                  :disabled-date=\"disabledDate\"\n                  @change=\"loadTimeSlots\"\n                />\n              </div>\n            </div>\n          </template>\n\n          <div v-if=\"loadingTimeSlots\" class=\"loading-time-slots\">\n            <el-skeleton :rows=\"5\" animated />\n          </div>\n\n          <template v-else>\n            <div class=\"time-slots-container\">\n              <div class=\"time-slots-header\">\n                <h4>可用时间段</h4>\n                <p class=\"time-hint\">\n                  开放时间: {{ formatTime(room?.open_time) }} -\n                  {{ formatTime(room?.close_time) }}\n                </p>\n              </div>\n\n              <div v-if=\"timeSlots.length === 0\" class=\"empty-time-slots\">\n                <el-empty description=\"当前日期没有可用的时间段\" />\n              </div>\n\n              <div v-else class=\"time-slots\">\n                <div\n                  v-for=\"(slot, index) in timeSlots\"\n                  :key=\"index\"\n                  class=\"time-slot\"\n                  :class=\"{\n                    unavailable: !slot.is_available,\n                    selected: isTimeSlotSelected(slot),\n                  }\"\n                  @click=\"selectTimeSlot(slot)\"\n                >\n                  <span class=\"time-range\">{{ formatTimeSlot(slot) }}</span>\n                  <el-tag v-if=\"slot.is_available\" type=\"success\" size=\"small\"\n                    >可用</el-tag\n                  >\n                  <el-tag v-else type=\"danger\" size=\"small\">已约</el-tag>\n                </div>\n              </div>\n\n              <div class=\"selected-time-range\">\n                <h4>已选时间段</h4>\n                <template v-if=\"selectedStartTime && selectedEndTime\">\n                  <p class=\"selected-time\">\n                    {{ formatDateTime(selectedStartTime) }} -\n                    {{ formatDateTime(selectedEndTime) }}\n                    <span class=\"duration\">({{ calculateDuration() }})</span>\n                  </p>\n                </template>\n                <p v-else class=\"no-selection\">请选择时间段</p>\n              </div>\n            </div>\n\n            <div class=\"step-actions\">\n              <el-button\n                type=\"primary\"\n                @click=\"nextStep\"\n                :disabled=\"!canProceed\"\n                >下一步</el-button\n              >\n              <el-button @click=\"prevStep\">上一步</el-button>\n            </div>\n          </template>\n        </el-card>\n      </div>\n\n      <!-- 步骤3：确认预约 -->\n      <div v-else-if=\"currentStep === 2\" class=\"step-content\">\n        <el-card shadow=\"hover\" class=\"confirm-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3>确认预约信息</h3>\n            </div>\n          </template>\n\n          <el-descriptions :column=\"1\" border>\n            <el-descriptions-item label=\"自习室\">{{\n              room?.name\n            }}</el-descriptions-item>\n            <el-descriptions-item label=\"位置\">{{\n              room?.location\n            }}</el-descriptions-item>\n            <el-descriptions-item label=\"座位编号\">{{\n              seat?.seat_number\n            }}</el-descriptions-item>\n            <el-descriptions-item label=\"预约日期\">{{\n              selectedDate\n            }}</el-descriptions-item>\n            <el-descriptions-item label=\"预约时间\">\n              {{ formatDateTime(selectedStartTime) }} -\n              {{ formatDateTime(selectedEndTime) }}\n              <span class=\"duration\">({{ calculateDuration() }})</span>\n            </el-descriptions-item>\n          </el-descriptions>\n\n          <div class=\"reservation-notes\">\n            <h4>预约须知</h4>\n            <ul>\n              <li>\n                预约成功后，请在预约开始时间后30分钟内完成签到，否则系统将自动取消预约。\n              </li>\n              <li>如需取消预约，请提前操作，避免影响信誉分。</li>\n              <li>请遵守自习室规定，保持安静，不要占用他人座位。</li>\n              <li>离开时请及时签退，方便他人使用。</li>\n            </ul>\n          </div>\n\n          <div class=\"step-actions\">\n            <el-button\n              type=\"primary\"\n              @click=\"submitReservation\"\n              :loading=\"submitting\"\n              >确认预约</el-button\n            >\n            <el-button @click=\"prevStep\">上一步</el-button>\n          </div>\n        </el-card>\n      </div>\n\n      <!-- 步骤4：预约成功 -->\n      <div v-else-if=\"currentStep === 3\" class=\"step-content\">\n        <el-card shadow=\"hover\" class=\"success-card\">\n          <el-result\n            icon=\"success\"\n            title=\"预约成功\"\n            sub-title=\"您已成功预约座位\"\n          >\n            <template #extra>\n              <div class=\"reservation-details\">\n                <p><strong>预约编号：</strong>{{ reservation?.id }}</p>\n                <p><strong>自习室：</strong>{{ room?.name }}</p>\n                <p><strong>座位编号：</strong>{{ seat?.seat_number }}</p>\n                <p>\n                  <strong>预约时间：</strong\n                  >{{ formatDateTime(reservation?.start_time) }} -\n                  {{ formatDateTime(reservation?.end_time) }}\n                </p>\n                <p>\n                  <strong>签到码：</strong>{{ reservation?.reservation_code }}\n                </p>\n              </div>\n\n              <div class=\"action-buttons\">\n                <el-button\n                  type=\"primary\"\n                  @click=\"$router.push('/user/reservations')\"\n                >\n                  查看我的预约\n                </el-button>\n                <el-button @click=\"$router.push('/seat/rooms')\">\n                  返回自习室列表\n                </el-button>\n              </div>\n            </template>\n          </el-result>\n        </el-card>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted, watch } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRoute, useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { ArrowLeft } from \"@element-plus/icons-vue\";\n\nexport default {\n  name: \"SeatReservation\",\n  setup() {\n    const store = useStore();\n    const route = useRoute();\n    const router = useRouter();\n\n    const loading = ref(true);\n    const loadingTimeSlots = ref(false);\n    const submitting = ref(false);\n    const currentStep = ref(0);\n\n    const room = ref(null);\n    const seat = ref(null);\n    const timeSlots = ref([]);\n    const reservation = ref(null);\n\n    const selectedDate = ref(\"\");\n    const selectedStartTime = ref(null);\n    const selectedEndTime = ref(null);\n\n    // 是否可以进行下一步\n    const canProceed = computed(() => {\n      if (currentStep.value === 0) {\n        return !!seat.value;\n      } else if (currentStep.value === 1) {\n        return !!selectedStartTime.value && !!selectedEndTime.value;\n      }\n      return true;\n    });\n\n    // 加载座位信息\n    const loadSeatInfo = async () => {\n      try {\n        loading.value = true;\n\n        const seatId = route.query.seatId;\n        if (!seatId) {\n          ElMessage.error(\"缺少座位ID参数\");\n          router.push(\"/seat/rooms\");\n          return;\n        }\n\n        // 加载座位信息\n        const seatData = await store.dispatch(\"seat/getSeatById\", seatId);\n        seat.value = seatData;\n\n        // 加载自习室信息\n        const roomData = await store.dispatch(\n          \"seat/getRoomById\",\n          seat.value.room\n        );\n        room.value = roomData;\n\n        // 设置默认日期\n        if (route.query.date) {\n          selectedDate.value = route.query.date;\n        } else {\n          selectedDate.value = formatDateForSelect(new Date());\n        }\n\n        // 加载时间段\n        await loadTimeSlots();\n      } catch (error) {\n        ElMessage.error(\"加载座位信息失败\");\n        router.push(\"/seat/rooms\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 加载时间段\n    const loadTimeSlots = async () => {\n      try {\n        loadingTimeSlots.value = true;\n\n        // 重置选择的时间\n        selectedStartTime.value = null;\n        selectedEndTime.value = null;\n\n        // 加载时间段\n        const timeSlotsData = await store.dispatch(\"seat/getSeatTimeSlots\", {\n          seatId: seat.value.id,\n          date: selectedDate.value,\n        });\n\n        timeSlots.value = timeSlotsData.time_slots;\n      } catch (error) {\n        ElMessage.error(\"加载时间段失败\");\n      } finally {\n        loadingTimeSlots.value = false;\n      }\n    };\n\n    // 选择时间段\n    const selectTimeSlot = (slot) => {\n      if (!slot.is_available) return;\n\n      const startTime = new Date(slot.start_time);\n      const endTime = new Date(slot.end_time);\n\n      // 如果没有选择开始时间，或者已经选择了开始和结束时间\n      if (\n        !selectedStartTime.value ||\n        (selectedStartTime.value && selectedEndTime.value)\n      ) {\n        selectedStartTime.value = startTime;\n        selectedEndTime.value = endTime;\n      }\n      // 如果已经选择了开始时间，但没有选择结束时间\n      else if (selectedStartTime.value && !selectedEndTime.value) {\n        // 如果选择的时间早于已选的开始时间，则作为新的开始时间\n        if (startTime < selectedStartTime.value) {\n          selectedStartTime.value = startTime;\n        }\n        // 如果选择的时间晚于已选的开始时间，则作为结束时间\n        else if (startTime > selectedStartTime.value) {\n          // 检查中间是否有不可用的时间段\n          const hasUnavailableSlot = timeSlots.value.some((s) => {\n            const slotStart = new Date(s.start_time);\n            const slotEnd = new Date(s.end_time);\n            return (\n              !s.is_available &&\n              slotStart >= selectedStartTime.value &&\n              slotEnd <= endTime\n            );\n          });\n\n          if (hasUnavailableSlot) {\n            ElMessage.warning(\"所选时间段中包含已被预约的时间\");\n            return;\n          }\n\n          selectedEndTime.value = endTime;\n        }\n      }\n    };\n\n    // 判断时间段是否被选中\n    const isTimeSlotSelected = (slot) => {\n      if (!selectedStartTime.value) return false;\n\n      const slotStart = new Date(slot.start_time);\n      const slotEnd = new Date(slot.end_time);\n\n      if (selectedEndTime.value) {\n        return (\n          slotStart >= selectedStartTime.value &&\n          slotEnd <= selectedEndTime.value\n        );\n      } else {\n        return slotStart.getTime() === selectedStartTime.value.getTime();\n      }\n    };\n\n    // 计算时长\n    const calculateDuration = () => {\n      if (!selectedStartTime.value || !selectedEndTime.value) return \"\";\n\n      const diffMs = selectedEndTime.value - selectedStartTime.value;\n      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\n\n      return `${diffHrs}小时${diffMins}分钟`;\n    };\n\n    // 禁用日期\n    const disabledDate = (date) => {\n      // 禁用过去的日期和7天后的日期\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n\n      const maxDate = new Date();\n      maxDate.setDate(maxDate.getDate() + 6);\n      maxDate.setHours(23, 59, 59, 999);\n\n      return date < today || date > maxDate;\n    };\n\n    // 提交预约\n    const submitReservation = async () => {\n      try {\n        submitting.value = true;\n\n        // 提交预约\n        const reservationData = await store.dispatch(\"seat/createReservation\", {\n          seatId: seat.value.id,\n          startTime: selectedStartTime.value.toISOString(),\n          endTime: selectedEndTime.value.toISOString(),\n        });\n\n        reservation.value = reservationData;\n\n        // 进入下一步\n        currentStep.value = 3;\n\n        ElMessage.success(\"预约成功\");\n      } catch (error) {\n        ElMessage.error(error.message || \"预约失败\");\n      } finally {\n        submitting.value = false;\n      }\n    };\n\n    // 下一步\n    const nextStep = () => {\n      if (!canProceed.value) return;\n      currentStep.value++;\n    };\n\n    // 上一步\n    const prevStep = () => {\n      currentStep.value--;\n    };\n\n    // 获取座位状态类型\n    const getSeatStatusType = (status) => {\n      switch (status) {\n        case \"available\":\n          return \"success\";\n        case \"occupied\":\n          return \"danger\";\n        case \"disabled\":\n          return \"info\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取座位状态文本\n    const getSeatStatusText = (status) => {\n      switch (status) {\n        case \"available\":\n          return \"可用\";\n        case \"occupied\":\n          return \"已占用\";\n        case \"disabled\":\n          return \"禁用\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化时间\n    const formatTime = (timeString) => {\n      if (!timeString) return \"\";\n\n      // 时间格式为 \"HH:MM:SS\"，只显示 \"HH:MM\"\n      return timeString.substring(0, 5);\n    };\n\n    // 格式化日期时间\n    const formatDateTime = (dateTime) => {\n      if (!dateTime) return \"\";\n\n      const date = new Date(dateTime);\n      return `${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 格式化时间段\n    const formatTimeSlot = (slot) => {\n      const start = new Date(slot.start_time);\n      const end = new Date(slot.end_time);\n\n      return `${padZero(start.getHours())}:${padZero(\n        start.getMinutes()\n      )} - ${padZero(end.getHours())}:${padZero(end.getMinutes())}`;\n    };\n\n    // 格式化日期（用于选择器值）\n    function formatDateForSelect(date) {\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(\n        date.getDate()\n      )}`;\n    }\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n\n    // 监听日期变化\n    watch(selectedDate, () => {\n      loadTimeSlots();\n    });\n\n    onMounted(() => {\n      loadSeatInfo();\n    });\n\n    return {\n      loading,\n      loadingTimeSlots,\n      submitting,\n      currentStep,\n      room,\n      seat,\n      timeSlots,\n      reservation,\n      selectedDate,\n      selectedStartTime,\n      selectedEndTime,\n      canProceed,\n      loadTimeSlots,\n      selectTimeSlot,\n      isTimeSlotSelected,\n      calculateDuration,\n      disabledDate,\n      submitReservation,\n      nextStep,\n      prevStep,\n      getSeatStatusType,\n      getSeatStatusText,\n      formatTime,\n      formatDateTime,\n      formatTimeSlot,\n      ArrowLeft,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.seat-reservation {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  .header-left {\n    display: flex;\n    align-items: center;\n    gap: 15px;\n\n    h2 {\n      margin: 0;\n    }\n  }\n}\n\n.reservation-steps {\n  margin-bottom: 30px;\n}\n\n.loading-container {\n  padding: 40px 0;\n}\n\n.step-content {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  h3 {\n    margin: 0;\n  }\n\n  .date-selector {\n    display: flex;\n    align-items: center;\n    gap: 10px;\n  }\n}\n\n.step-actions {\n  margin-top: 20px;\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n}\n\n.loading-time-slots {\n  padding: 20px 0;\n}\n\n.time-slots-container {\n  .time-slots-header {\n    margin-bottom: 15px;\n\n    h4 {\n      margin: 0 0 5px 0;\n    }\n\n    .time-hint {\n      margin: 0;\n      color: #909399;\n      font-size: 14px;\n    }\n  }\n\n  .empty-time-slots {\n    padding: 20px 0;\n  }\n\n  .time-slots {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n    gap: 10px;\n    margin-bottom: 20px;\n\n    .time-slot {\n      padding: 10px;\n      border-radius: 4px;\n      border: 1px solid #dcdfe6;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      cursor: pointer;\n      transition: all 0.3s;\n\n      &:hover:not(.unavailable) {\n        border-color: #409eff;\n        background-color: #ecf5ff;\n      }\n\n      &.selected {\n        border-color: #409eff;\n        background-color: #ecf5ff;\n      }\n\n      &.unavailable {\n        opacity: 0.6;\n        cursor: not-allowed;\n      }\n\n      .time-range {\n        font-size: 14px;\n      }\n    }\n  }\n\n  .selected-time-range {\n    margin-top: 20px;\n    padding: 15px;\n    background-color: #f5f7fa;\n    border-radius: 4px;\n\n    h4 {\n      margin: 0 0 10px 0;\n    }\n\n    .selected-time {\n      margin: 0;\n      font-size: 16px;\n      font-weight: bold;\n      color: #409eff;\n\n      .duration {\n        margin-left: 10px;\n        font-size: 14px;\n        font-weight: normal;\n        color: #606266;\n      }\n    }\n\n    .no-selection {\n      margin: 0;\n      color: #909399;\n    }\n  }\n}\n\n.reservation-notes {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  h4 {\n    margin: 0 0 10px 0;\n  }\n\n  ul {\n    margin: 0;\n    padding-left: 20px;\n\n    li {\n      margin-bottom: 5px;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n  }\n}\n\n.reservation-details {\n  text-align: left;\n  margin-bottom: 20px;\n\n  p {\n    margin: 5px 0;\n  }\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n}\n\n.duration {\n  margin-left: 10px;\n  font-size: 14px;\n  color: #606266;\n}\n</style>\n"], "mappings": ";;;AAyQA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAI,QAAS,KAAK;AACrD,SAASC,QAAO,QAAS,MAAM;AAC/B,SAASC,QAAQ,EAAEC,SAAQ,QAAS,YAAY;AAChD,SAASC,SAAQ,QAAS,cAAc;AACxC,SAASC,SAAQ,QAAS,yBAAyB;AAEnD,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIP,QAAQ,CAAC,CAAC;IACxB,MAAMQ,KAAI,GAAIP,QAAQ,CAAC,CAAC;IACxB,MAAMQ,MAAK,GAAIP,SAAS,CAAC,CAAC;IAE1B,MAAMQ,OAAM,GAAId,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMe,gBAAe,GAAIf,GAAG,CAAC,KAAK,CAAC;IACnC,MAAMgB,UAAS,GAAIhB,GAAG,CAAC,KAAK,CAAC;IAC7B,MAAMiB,WAAU,GAAIjB,GAAG,CAAC,CAAC,CAAC;IAE1B,MAAMkB,IAAG,GAAIlB,GAAG,CAAC,IAAI,CAAC;IACtB,MAAMmB,IAAG,GAAInB,GAAG,CAAC,IAAI,CAAC;IACtB,MAAMoB,SAAQ,GAAIpB,GAAG,CAAC,EAAE,CAAC;IACzB,MAAMqB,WAAU,GAAIrB,GAAG,CAAC,IAAI,CAAC;IAE7B,MAAMsB,YAAW,GAAItB,GAAG,CAAC,EAAE,CAAC;IAC5B,MAAMuB,iBAAgB,GAAIvB,GAAG,CAAC,IAAI,CAAC;IACnC,MAAMwB,eAAc,GAAIxB,GAAG,CAAC,IAAI,CAAC;;IAEjC;IACA,MAAMyB,UAAS,GAAIxB,QAAQ,CAAC,MAAM;MAChC,IAAIgB,WAAW,CAACS,KAAI,KAAM,CAAC,EAAE;QAC3B,OAAO,CAAC,CAACP,IAAI,CAACO,KAAK;MACrB,OAAO,IAAIT,WAAW,CAACS,KAAI,KAAM,CAAC,EAAE;QAClC,OAAO,CAAC,CAACH,iBAAiB,CAACG,KAAI,IAAK,CAAC,CAACF,eAAe,CAACE,KAAK;MAC7D;MACA,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,MAAMC,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFb,OAAO,CAACY,KAAI,GAAI,IAAI;QAEpB,MAAME,MAAK,GAAIhB,KAAK,CAACiB,KAAK,CAACD,MAAM;QACjC,IAAI,CAACA,MAAM,EAAE;UACXrB,SAAS,CAACuB,KAAK,CAAC,UAAU,CAAC;UAC3BjB,MAAM,CAACkB,IAAI,CAAC,aAAa,CAAC;UAC1B;QACF;;QAEA;QACA,MAAMC,QAAO,GAAI,MAAMrB,KAAK,CAACsB,QAAQ,CAAC,kBAAkB,EAAEL,MAAM,CAAC;QACjET,IAAI,CAACO,KAAI,GAAIM,QAAQ;;QAErB;QACA,MAAME,QAAO,GAAI,MAAMvB,KAAK,CAACsB,QAAQ,CACnC,kBAAkB,EAClBd,IAAI,CAACO,KAAK,CAACR,IACb,CAAC;QACDA,IAAI,CAACQ,KAAI,GAAIQ,QAAQ;;QAErB;QACA,IAAItB,KAAK,CAACiB,KAAK,CAACM,IAAI,EAAE;UACpBb,YAAY,CAACI,KAAI,GAAId,KAAK,CAACiB,KAAK,CAACM,IAAI;QACvC,OAAO;UACLb,YAAY,CAACI,KAAI,GAAIU,mBAAmB,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;QACtD;;QAEA;QACA,MAAMC,aAAa,CAAC,CAAC;MACvB,EAAE,OAAOR,KAAK,EAAE;QACdvB,SAAS,CAACuB,KAAK,CAAC,UAAU,CAAC;QAC3BjB,MAAM,CAACkB,IAAI,CAAC,aAAa,CAAC;MAC5B,UAAU;QACRjB,OAAO,CAACY,KAAI,GAAI,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAMY,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFvB,gBAAgB,CAACW,KAAI,GAAI,IAAI;;QAE7B;QACAH,iBAAiB,CAACG,KAAI,GAAI,IAAI;QAC9BF,eAAe,CAACE,KAAI,GAAI,IAAI;;QAE5B;QACA,MAAMa,aAAY,GAAI,MAAM5B,KAAK,CAACsB,QAAQ,CAAC,uBAAuB,EAAE;UAClEL,MAAM,EAAET,IAAI,CAACO,KAAK,CAACc,EAAE;UACrBL,IAAI,EAAEb,YAAY,CAACI;QACrB,CAAC,CAAC;QAEFN,SAAS,CAACM,KAAI,GAAIa,aAAa,CAACE,UAAU;MAC5C,EAAE,OAAOX,KAAK,EAAE;QACdvB,SAAS,CAACuB,KAAK,CAAC,SAAS,CAAC;MAC5B,UAAU;QACRf,gBAAgB,CAACW,KAAI,GAAI,KAAK;MAChC;IACF,CAAC;;IAED;IACA,MAAMgB,cAAa,GAAKC,IAAI,IAAK;MAC/B,IAAI,CAACA,IAAI,CAACC,YAAY,EAAE;MAExB,MAAMC,SAAQ,GAAI,IAAIR,IAAI,CAACM,IAAI,CAACG,UAAU,CAAC;MAC3C,MAAMC,OAAM,GAAI,IAAIV,IAAI,CAACM,IAAI,CAACK,QAAQ,CAAC;;MAEvC;MACA,IACE,CAACzB,iBAAiB,CAACG,KAAI,IACtBH,iBAAiB,CAACG,KAAI,IAAKF,eAAe,CAACE,KAAK,EACjD;QACAH,iBAAiB,CAACG,KAAI,GAAImB,SAAS;QACnCrB,eAAe,CAACE,KAAI,GAAIqB,OAAO;MACjC;MACA;MAAA,KACK,IAAIxB,iBAAiB,CAACG,KAAI,IAAK,CAACF,eAAe,CAACE,KAAK,EAAE;QAC1D;QACA,IAAImB,SAAQ,GAAItB,iBAAiB,CAACG,KAAK,EAAE;UACvCH,iBAAiB,CAACG,KAAI,GAAImB,SAAS;QACrC;QACA;QAAA,KACK,IAAIA,SAAQ,GAAItB,iBAAiB,CAACG,KAAK,EAAE;UAC5C;UACA,MAAMuB,kBAAiB,GAAI7B,SAAS,CAACM,KAAK,CAACwB,IAAI,CAAEC,CAAC,IAAK;YACrD,MAAMC,SAAQ,GAAI,IAAIf,IAAI,CAACc,CAAC,CAACL,UAAU,CAAC;YACxC,MAAMO,OAAM,GAAI,IAAIhB,IAAI,CAACc,CAAC,CAACH,QAAQ,CAAC;YACpC,OACE,CAACG,CAAC,CAACP,YAAW,IACdQ,SAAQ,IAAK7B,iBAAiB,CAACG,KAAI,IACnC2B,OAAM,IAAKN,OAAM;UAErB,CAAC,CAAC;UAEF,IAAIE,kBAAkB,EAAE;YACtB1C,SAAS,CAAC+C,OAAO,CAAC,iBAAiB,CAAC;YACpC;UACF;UAEA9B,eAAe,CAACE,KAAI,GAAIqB,OAAO;QACjC;MACF;IACF,CAAC;;IAED;IACA,MAAMQ,kBAAiB,GAAKZ,IAAI,IAAK;MACnC,IAAI,CAACpB,iBAAiB,CAACG,KAAK,EAAE,OAAO,KAAK;MAE1C,MAAM0B,SAAQ,GAAI,IAAIf,IAAI,CAACM,IAAI,CAACG,UAAU,CAAC;MAC3C,MAAMO,OAAM,GAAI,IAAIhB,IAAI,CAACM,IAAI,CAACK,QAAQ,CAAC;MAEvC,IAAIxB,eAAe,CAACE,KAAK,EAAE;QACzB,OACE0B,SAAQ,IAAK7B,iBAAiB,CAACG,KAAI,IACnC2B,OAAM,IAAK7B,eAAe,CAACE,KAAI;MAEnC,OAAO;QACL,OAAO0B,SAAS,CAACI,OAAO,CAAC,MAAMjC,iBAAiB,CAACG,KAAK,CAAC8B,OAAO,CAAC,CAAC;MAClE;IACF,CAAC;;IAED;IACA,MAAMC,iBAAgB,GAAIA,CAAA,KAAM;MAC9B,IAAI,CAAClC,iBAAiB,CAACG,KAAI,IAAK,CAACF,eAAe,CAACE,KAAK,EAAE,OAAO,EAAE;MAEjE,MAAMgC,MAAK,GAAIlC,eAAe,CAACE,KAAI,GAAIH,iBAAiB,CAACG,KAAK;MAC9D,MAAMiC,OAAM,GAAIC,IAAI,CAACC,KAAK,CAACH,MAAK,IAAK,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC,CAAC;MACrD,MAAMI,QAAO,GAAIF,IAAI,CAACC,KAAK,CAAEH,MAAK,IAAK,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC,IAAK,IAAG,GAAI,EAAE,CAAC,CAAC;MAEtE,OAAO,GAAGC,OAAO,KAAKG,QAAQ,IAAI;IACpC,CAAC;;IAED;IACA,MAAMC,YAAW,GAAK5B,IAAI,IAAK;MAC7B;MACA,MAAM6B,KAAI,GAAI,IAAI3B,IAAI,CAAC,CAAC;MACxB2B,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAE1B,MAAMC,OAAM,GAAI,IAAI7B,IAAI,CAAC,CAAC;MAC1B6B,OAAO,CAACC,OAAO,CAACD,OAAO,CAACE,OAAO,CAAC,IAAI,CAAC,CAAC;MACtCF,OAAO,CAACD,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;MAEjC,OAAO9B,IAAG,GAAI6B,KAAI,IAAK7B,IAAG,GAAI+B,OAAO;IACvC,CAAC;;IAED;IACA,MAAMG,iBAAgB,GAAI,MAAAA,CAAA,KAAY;MACpC,IAAI;QACFrD,UAAU,CAACU,KAAI,GAAI,IAAI;;QAEvB;QACA,MAAM4C,eAAc,GAAI,MAAM3D,KAAK,CAACsB,QAAQ,CAAC,wBAAwB,EAAE;UACrEL,MAAM,EAAET,IAAI,CAACO,KAAK,CAACc,EAAE;UACrBK,SAAS,EAAEtB,iBAAiB,CAACG,KAAK,CAAC6C,WAAW,CAAC,CAAC;UAChDxB,OAAO,EAAEvB,eAAe,CAACE,KAAK,CAAC6C,WAAW,CAAC;QAC7C,CAAC,CAAC;QAEFlD,WAAW,CAACK,KAAI,GAAI4C,eAAe;;QAEnC;QACArD,WAAW,CAACS,KAAI,GAAI,CAAC;QAErBnB,SAAS,CAACiE,OAAO,CAAC,MAAM,CAAC;MAC3B,EAAE,OAAO1C,KAAK,EAAE;QACdvB,SAAS,CAACuB,KAAK,CAACA,KAAK,CAAC2C,OAAM,IAAK,MAAM,CAAC;MAC1C,UAAU;QACRzD,UAAU,CAACU,KAAI,GAAI,KAAK;MAC1B;IACF,CAAC;;IAED;IACA,MAAMgD,QAAO,GAAIA,CAAA,KAAM;MACrB,IAAI,CAACjD,UAAU,CAACC,KAAK,EAAE;MACvBT,WAAW,CAACS,KAAK,EAAE;IACrB,CAAC;;IAED;IACA,MAAMiD,QAAO,GAAIA,CAAA,KAAM;MACrB1D,WAAW,CAACS,KAAK,EAAE;IACrB,CAAC;;IAED;IACA,MAAMkD,iBAAgB,GAAKC,MAAM,IAAK;MACpC,QAAQA,MAAM;QACZ,KAAK,WAAW;UACd,OAAO,SAAS;QAClB,KAAK,UAAU;UACb,OAAO,QAAQ;QACjB,KAAK,UAAU;UACb,OAAO,MAAM;QACf;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAMC,iBAAgB,GAAKD,MAAM,IAAK;MACpC,QAAQA,MAAM;QACZ,KAAK,WAAW;UACd,OAAO,IAAI;QACb,KAAK,UAAU;UACb,OAAO,KAAK;QACd,KAAK,UAAU;UACb,OAAO,IAAI;QACb;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAME,UAAS,GAAKC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;;MAE1B;MACA,OAAOA,UAAU,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC;;IAED;IACA,MAAMC,cAAa,GAAKC,QAAQ,IAAK;MACnC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;MAExB,MAAMhD,IAAG,GAAI,IAAIE,IAAI,CAAC8C,QAAQ,CAAC;MAC/B,OAAO,GAAGC,OAAO,CAACjD,IAAI,CAACkD,QAAQ,CAAC,CAAC,CAAC,IAAID,OAAO,CAACjD,IAAI,CAACmD,UAAU,CAAC,CAAC,CAAC,EAAE;IACpE,CAAC;;IAED;IACA,MAAMC,cAAa,GAAK5C,IAAI,IAAK;MAC/B,MAAM6C,KAAI,GAAI,IAAInD,IAAI,CAACM,IAAI,CAACG,UAAU,CAAC;MACvC,MAAM2C,GAAE,GAAI,IAAIpD,IAAI,CAACM,IAAI,CAACK,QAAQ,CAAC;MAEnC,OAAO,GAAGoC,OAAO,CAACI,KAAK,CAACH,QAAQ,CAAC,CAAC,CAAC,IAAID,OAAO,CAC5CI,KAAK,CAACF,UAAU,CAAC,CACnB,CAAC,MAAMF,OAAO,CAACK,GAAG,CAACJ,QAAQ,CAAC,CAAC,CAAC,IAAID,OAAO,CAACK,GAAG,CAACH,UAAU,CAAC,CAAC,CAAC,EAAE;IAC/D,CAAC;;IAED;IACA,SAASlD,mBAAmBA,CAACD,IAAI,EAAE;MACjC,OAAO,GAAGA,IAAI,CAACuD,WAAW,CAAC,CAAC,IAAIN,OAAO,CAACjD,IAAI,CAACwD,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAIP,OAAO,CACrEjD,IAAI,CAACiC,OAAO,CAAC,CACf,CAAC,EAAE;IACL;;IAEA;IACA,SAASgB,OAAOA,CAACQ,GAAG,EAAE;MACpB,OAAOA,GAAE,GAAI,EAAC,GAAI,IAAIA,GAAG,EAAC,GAAIA,GAAG;IACnC;;IAEA;IACAzF,KAAK,CAACmB,YAAY,EAAE,MAAM;MACxBgB,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC;IAEFpC,SAAS,CAAC,MAAM;MACdyB,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC;IAEF,OAAO;MACLb,OAAO;MACPC,gBAAgB;MAChBC,UAAU;MACVC,WAAW;MACXC,IAAI;MACJC,IAAI;MACJC,SAAS;MACTC,WAAW;MACXC,YAAY;MACZC,iBAAiB;MACjBC,eAAe;MACfC,UAAU;MACVa,aAAa;MACbI,cAAc;MACda,kBAAkB;MAClBE,iBAAiB;MACjBM,YAAY;MACZM,iBAAiB;MACjBK,QAAQ;MACRC,QAAQ;MACRC,iBAAiB;MACjBE,iBAAiB;MACjBC,UAAU;MACVG,cAAc;MACdK,cAAc;MACd/E;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}