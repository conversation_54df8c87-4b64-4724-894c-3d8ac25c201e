{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { inject, computed, getCurrentInstance, toRaw, watch } from 'vue';\nimport { castArray, get, isEqual } from 'lodash-unified';\nimport { selectKey, selectGroupKey } from './token.mjs';\nimport { COMPONENT_NAME } from './option.mjs';\nimport { escapeStringRegexp } from '../../../utils/strings.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nimport { isObject } from '@vue/shared';\nfunction useOption(props, states) {\n  const select = inject(selectKey);\n  if (!select) {\n    throwError(COMPONENT_NAME, \"usage: <el-select><el-option /></el-select/>\");\n  }\n  const selectGroup = inject(selectGroupKey, {\n    disabled: false\n  });\n  const itemSelected = computed(() => {\n    return contains(castArray(select.props.modelValue), props.value);\n  });\n  const limitReached = computed(() => {\n    var _a;\n    if (select.props.multiple) {\n      const modelValue = castArray((_a = select.props.modelValue) != null ? _a : []);\n      return !itemSelected.value && modelValue.length >= select.props.multipleLimit && select.props.multipleLimit > 0;\n    } else {\n      return false;\n    }\n  });\n  const currentLabel = computed(() => {\n    return props.label || (isObject(props.value) ? \"\" : props.value);\n  });\n  const currentValue = computed(() => {\n    return props.value || props.label || \"\";\n  });\n  const isDisabled = computed(() => {\n    return props.disabled || states.groupDisabled || limitReached.value;\n  });\n  const instance = getCurrentInstance();\n  const contains = (arr = [], target) => {\n    if (!isObject(props.value)) {\n      return arr && arr.includes(target);\n    } else {\n      const valueKey = select.props.valueKey;\n      return arr && arr.some(item => {\n        return toRaw(get(item, valueKey)) === get(target, valueKey);\n      });\n    }\n  };\n  const hoverItem = () => {\n    if (!props.disabled && !selectGroup.disabled) {\n      select.states.hoveringIndex = select.optionsArray.indexOf(instance.proxy);\n    }\n  };\n  const updateOption = query => {\n    const regexp = new RegExp(escapeStringRegexp(query), \"i\");\n    states.visible = regexp.test(String(currentLabel.value)) || props.created;\n  };\n  watch(() => currentLabel.value, () => {\n    if (!props.created && !select.props.remote) select.setSelected();\n  });\n  watch(() => props.value, (val, oldVal) => {\n    const {\n      remote,\n      valueKey\n    } = select.props;\n    const shouldUpdate = remote ? val !== oldVal : !isEqual(val, oldVal);\n    if (shouldUpdate) {\n      select.onOptionDestroy(oldVal, instance.proxy);\n      select.onOptionCreate(instance.proxy);\n    }\n    if (!props.created && !remote) {\n      if (valueKey && isObject(val) && isObject(oldVal) && val[valueKey] === oldVal[valueKey]) {\n        return;\n      }\n      select.setSelected();\n    }\n  });\n  watch(() => selectGroup.disabled, () => {\n    states.groupDisabled = selectGroup.disabled;\n  }, {\n    immediate: true\n  });\n  return {\n    select,\n    currentLabel,\n    currentValue,\n    itemSelected,\n    isDisabled,\n    hoverItem,\n    updateOption\n  };\n}\nexport { useOption };", "map": {"version": 3, "names": ["useOption", "props", "states", "select", "inject", "<PERSON><PERSON><PERSON>", "throwError", "COMPONENT_NAME", "selectGroup", "selectGroupKey", "disabled", "itemSelected", "computed", "contains", "<PERSON><PERSON><PERSON><PERSON>", "modelValue", "value", "limitReached", "_a", "multiple", "length", "multipleLimit", "current<PERSON><PERSON><PERSON>", "label", "isObject", "currentValue", "isDisabled", "groupDisabled", "instance", "getCurrentInstance", "arr", "target", "includes", "valueKey", "some", "item", "toRaw", "get", "hoverItem", "hoveringIndex", "optionsArray", "indexOf", "proxy", "updateOption", "query", "regexp", "RegExp", "escapeStringRegexp", "visible", "test", "String", "created", "watch", "remote", "setSelected", "val", "oldVal", "shouldUpdate", "isEqual", "onOptionDestroy", "onOptionCreate", "immediate"], "sources": ["../../../../../../packages/components/select/src/useOption.ts"], "sourcesContent": ["import { computed, getCurrentInstance, inject, toRaw, watch } from 'vue'\nimport { get, isEqual } from 'lodash-unified'\nimport {\n  ensureArray,\n  escapeStringRegexp,\n  isObject,\n  throwError,\n} from '@element-plus/utils'\nimport { selectGroupKey, selectKey } from './token'\nimport { COMPONENT_NAME } from './option'\n\nimport type { OptionInternalInstance, OptionProps, OptionStates } from './type'\n\nexport function useOption(props: OptionProps, states: OptionStates) {\n  // inject\n  const select = inject(selectKey)\n  if (!select) {\n    throwError(COMPONENT_NAME, 'usage: <el-select><el-option /></el-select/>')\n  }\n  const selectGroup = inject(selectGroupKey, { disabled: false })\n\n  // computed\n  const itemSelected = computed(() => {\n    return contains(ensureArray(select.props.modelValue), props.value)\n  })\n\n  const limitReached = computed(() => {\n    if (select.props.multiple) {\n      const modelValue = ensureArray(select.props.modelValue ?? [])\n      return (\n        !itemSelected.value &&\n        modelValue.length >= select.props.multipleLimit &&\n        select.props.multipleLimit > 0\n      )\n    } else {\n      return false\n    }\n  })\n\n  const currentLabel = computed(() => {\n    return props.label || (isObject(props.value) ? '' : props.value)\n  })\n\n  const currentValue = computed(() => {\n    return props.value || props.label || ''\n  })\n\n  const isDisabled = computed(() => {\n    return props.disabled || states.groupDisabled || limitReached.value\n  })\n\n  const instance = getCurrentInstance()! as OptionInternalInstance\n  const contains = <T>(arr: T[] = [], target: T) => {\n    if (!isObject(props.value)) {\n      return arr && arr.includes(target)\n    } else {\n      const valueKey = select.props.valueKey\n      return (\n        arr &&\n        arr.some((item) => {\n          return toRaw(get(item, valueKey)) === get(target, valueKey)\n        })\n      )\n    }\n  }\n\n  const hoverItem = () => {\n    if (!props.disabled && !selectGroup.disabled) {\n      select.states.hoveringIndex = select.optionsArray.indexOf(instance.proxy)\n    }\n  }\n\n  const updateOption = (query: string) => {\n    const regexp = new RegExp(escapeStringRegexp(query), 'i')\n    states.visible = regexp.test(String(currentLabel.value)) || props.created\n  }\n\n  watch(\n    () => currentLabel.value,\n    () => {\n      if (!props.created && !select.props.remote) select.setSelected()\n    }\n  )\n\n  watch(\n    () => props.value,\n    (val, oldVal) => {\n      const { remote, valueKey } = select.props\n      const shouldUpdate = remote ? val !== oldVal : !isEqual(val, oldVal)\n      if (shouldUpdate) {\n        select.onOptionDestroy(oldVal, instance.proxy)\n        select.onOptionCreate(instance.proxy)\n      }\n\n      if (!props.created && !remote) {\n        if (\n          valueKey &&\n          isObject(val) &&\n          isObject(oldVal) &&\n          val[valueKey] === oldVal[valueKey]\n        ) {\n          return\n        }\n        select.setSelected()\n      }\n    }\n  )\n\n  watch(\n    () => selectGroup.disabled,\n    () => {\n      states.groupDisabled = selectGroup.disabled\n    },\n    { immediate: true }\n  )\n\n  return {\n    select,\n    currentLabel,\n    currentValue,\n    itemSelected,\n    isDisabled,\n    hoverItem,\n    updateOption,\n  }\n}\n"], "mappings": ";;;;;;;;;AAUO,SAASA,SAASA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACvC,MAAMC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAAC;EAChC,IAAI,CAACF,MAAM,EAAE;IACXG,UAAU,CAACC,cAAc,EAAE,8CAA8C,CAAC;EAC9E;EACE,MAAMC,WAAW,GAAGJ,MAAM,CAACK,cAAc,EAAE;IAAEC,QAAQ,EAAE;EAAK,CAAE,CAAC;EAC/D,MAAMC,YAAY,GAAGC,QAAQ,CAAC,MAAM;IAClC,OAAOC,QAAQ,CAACC,SAAW,CAACX,MAAM,CAACF,KAAK,CAACc,UAAU,CAAC,EAAEd,KAAK,CAACe,KAAK,CAAC;EACtE,CAAG,CAAC;EACF,MAAMC,YAAY,GAAGL,QAAQ,CAAC,MAAM;IAClC,IAAIM,EAAE;IACN,IAAIf,MAAM,CAACF,KAAK,CAACkB,QAAQ,EAAE;MACzB,MAAMJ,UAAU,GAAGD,SAAW,CAAC,CAACI,EAAE,GAAGf,MAAM,CAACF,KAAK,CAACc,UAAU,KAAK,IAAI,GAAGG,EAAE,GAAG,EAAE,CAAC;MAChF,OAAO,CAACP,YAAY,CAACK,KAAK,IAAID,UAAU,CAACK,MAAM,IAAIjB,MAAM,CAACF,KAAK,CAACoB,aAAa,IAAIlB,MAAM,CAACF,KAAK,CAACoB,aAAa,GAAG,CAAC;IACrH,CAAK,MAAM;MACL,OAAO,KAAK;IAClB;EACA,CAAG,CAAC;EACF,MAAMC,YAAY,GAAGV,QAAQ,CAAC,MAAM;IAClC,OAAOX,KAAK,CAACsB,KAAK,KAAKC,QAAQ,CAACvB,KAAK,CAACe,KAAK,CAAC,GAAG,EAAE,GAAGf,KAAK,CAACe,KAAK,CAAC;EACpE,CAAG,CAAC;EACF,MAAMS,YAAY,GAAGb,QAAQ,CAAC,MAAM;IAClC,OAAOX,KAAK,CAACe,KAAK,IAAIf,KAAK,CAACsB,KAAK,IAAI,EAAE;EAC3C,CAAG,CAAC;EACF,MAAMG,UAAU,GAAGd,QAAQ,CAAC,MAAM;IAChC,OAAOX,KAAK,CAACS,QAAQ,IAAIR,MAAM,CAACyB,aAAa,IAAIV,YAAY,CAACD,KAAK;EACvE,CAAG,CAAC;EACF,MAAMY,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,MAAMhB,QAAQ,GAAGA,CAACiB,GAAG,GAAG,EAAE,EAAEC,MAAM,KAAK;IACrC,IAAI,CAACP,QAAQ,CAACvB,KAAK,CAACe,KAAK,CAAC,EAAE;MAC1B,OAAOc,GAAG,IAAIA,GAAG,CAACE,QAAQ,CAACD,MAAM,CAAC;IACxC,CAAK,MAAM;MACL,MAAME,QAAQ,GAAG9B,MAAM,CAACF,KAAK,CAACgC,QAAQ;MACtC,OAAOH,GAAG,IAAIA,GAAG,CAACI,IAAI,CAAEC,IAAI,IAAK;QAC/B,OAAOC,KAAK,CAACC,GAAG,CAACF,IAAI,EAAEF,QAAQ,CAAC,CAAC,KAAKI,GAAG,CAACN,MAAM,EAAEE,QAAQ,CAAC;MACnE,CAAO,CAAC;IACR;EACA,CAAG;EACD,MAAMK,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAACrC,KAAK,CAACS,QAAQ,IAAI,CAACF,WAAW,CAACE,QAAQ,EAAE;MAC5CP,MAAM,CAACD,MAAM,CAACqC,aAAa,GAAGpC,MAAM,CAACqC,YAAY,CAACC,OAAO,CAACb,QAAQ,CAACc,KAAK,CAAC;IAC/E;EACA,CAAG;EACD,MAAMC,YAAY,GAAIC,KAAK,IAAK;IAC9B,MAAMC,MAAM,GAAG,IAAIC,MAAM,CAACC,kBAAkB,CAACH,KAAK,CAAC,EAAE,GAAG,CAAC;IACzD1C,MAAM,CAAC8C,OAAO,GAAGH,MAAM,CAACI,IAAI,CAACC,MAAM,CAAC5B,YAAY,CAACN,KAAK,CAAC,CAAC,IAAIf,KAAK,CAACkD,OAAO;EAC7E,CAAG;EACDC,KAAK,CAAC,MAAM9B,YAAY,CAACN,KAAK,EAAE,MAAM;IACpC,IAAI,CAACf,KAAK,CAACkD,OAAO,IAAI,CAAChD,MAAM,CAACF,KAAK,CAACoD,MAAM,EACxClD,MAAM,CAACmD,WAAW,EAAE;EAC1B,CAAG,CAAC;EACFF,KAAK,CAAC,MAAMnD,KAAK,CAACe,KAAK,EAAE,CAACuC,GAAG,EAAEC,MAAM,KAAK;IACxC,MAAM;MAAEH,MAAM;MAAEpB;IAAQ,CAAE,GAAG9B,MAAM,CAACF,KAAK;IACzC,MAAMwD,YAAY,GAAGJ,MAAM,GAAGE,GAAG,KAAKC,MAAM,GAAG,CAACE,OAAO,CAACH,GAAG,EAAEC,MAAM,CAAC;IACpE,IAAIC,YAAY,EAAE;MAChBtD,MAAM,CAACwD,eAAe,CAACH,MAAM,EAAE5B,QAAQ,CAACc,KAAK,CAAC;MAC9CvC,MAAM,CAACyD,cAAc,CAAChC,QAAQ,CAACc,KAAK,CAAC;IAC3C;IACI,IAAI,CAACzC,KAAK,CAACkD,OAAO,IAAI,CAACE,MAAM,EAAE;MAC7B,IAAIpB,QAAQ,IAAIT,QAAQ,CAAC+B,GAAG,CAAC,IAAI/B,QAAQ,CAACgC,MAAM,CAAC,IAAID,GAAG,CAACtB,QAAQ,CAAC,KAAKuB,MAAM,CAACvB,QAAQ,CAAC,EAAE;QACvF;MACR;MACM9B,MAAM,CAACmD,WAAW,EAAE;IAC1B;EACA,CAAG,CAAC;EACFF,KAAK,CAAC,MAAM5C,WAAW,CAACE,QAAQ,EAAE,MAAM;IACtCR,MAAM,CAACyB,aAAa,GAAGnB,WAAW,CAACE,QAAQ;EAC/C,CAAG,EAAE;IAAEmD,SAAS,EAAE;EAAI,CAAE,CAAC;EACvB,OAAO;IACL1D,MAAM;IACNmB,YAAY;IACZG,YAAY;IACZd,YAAY;IACZe,UAAU;IACVY,SAAS;IACTK;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}