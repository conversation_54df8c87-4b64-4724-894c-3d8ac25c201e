{"ast": null, "code": "import { defineComponent, getCurrentInstance, ref, computed, unref, inject, resolveComponent, openBlock, createBlock, withCtx, createVNode, mergeProps, renderSlot } from 'vue';\nimport ElRovingFocusItem from '../../roving-focus-group/src/roving-focus-item.mjs';\nimport ElDropdownItemImpl from './dropdown-item-impl.mjs';\nimport { useDropdown } from './useDropdown.mjs';\nimport { ElCollectionItem, dropdownItemProps } from './dropdown.mjs';\nimport { DROPDOWN_INJECTION_KEY } from './tokens.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { composeEventHandlers, whenMouse } from '../../../utils/dom/event.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElDropdownItem\",\n  components: {\n    ElDropdownCollectionItem: ElCollectionItem,\n    ElRovingFocusItem,\n    ElDropdownItemImpl\n  },\n  inheritAttrs: false,\n  props: dropdownItemProps,\n  emits: [\"pointermove\", \"pointerleave\", \"click\"],\n  setup(props, {\n    emit,\n    attrs\n  }) {\n    const {\n      elDropdown\n    } = useDropdown();\n    const _instance = getCurrentInstance();\n    const itemRef = ref(null);\n    const textContent = computed(() => {\n      var _a, _b;\n      return (_b = (_a = unref(itemRef)) == null ? void 0 : _a.textContent) != null ? _b : \"\";\n    });\n    const {\n      onItemEnter,\n      onItemLeave\n    } = inject(DROPDOWN_INJECTION_KEY, void 0);\n    const handlePointerMove = composeEventHandlers(e => {\n      emit(\"pointermove\", e);\n      return e.defaultPrevented;\n    }, whenMouse(e => {\n      if (props.disabled) {\n        onItemLeave(e);\n        return;\n      }\n      const target = e.currentTarget;\n      if (target === document.activeElement || target.contains(document.activeElement)) {\n        return;\n      }\n      onItemEnter(e);\n      if (!e.defaultPrevented) {\n        target == null ? void 0 : target.focus();\n      }\n    }));\n    const handlePointerLeave = composeEventHandlers(e => {\n      emit(\"pointerleave\", e);\n      return e.defaultPrevented;\n    }, whenMouse(onItemLeave));\n    const handleClick = composeEventHandlers(e => {\n      if (props.disabled) {\n        return;\n      }\n      emit(\"click\", e);\n      return e.type !== \"keydown\" && e.defaultPrevented;\n    }, e => {\n      var _a, _b, _c;\n      if (props.disabled) {\n        e.stopImmediatePropagation();\n        return;\n      }\n      if ((_a = elDropdown == null ? void 0 : elDropdown.hideOnClick) == null ? void 0 : _a.value) {\n        (_b = elDropdown.handleClick) == null ? void 0 : _b.call(elDropdown);\n      }\n      (_c = elDropdown.commandHandler) == null ? void 0 : _c.call(elDropdown, props.command, _instance, e);\n    });\n    const propsAndAttrs = computed(() => ({\n      ...props,\n      ...attrs\n    }));\n    return {\n      handleClick,\n      handlePointerMove,\n      handlePointerLeave,\n      textContent,\n      propsAndAttrs\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _a;\n  const _component_el_dropdown_item_impl = resolveComponent(\"el-dropdown-item-impl\");\n  const _component_el_roving_focus_item = resolveComponent(\"el-roving-focus-item\");\n  const _component_el_dropdown_collection_item = resolveComponent(\"el-dropdown-collection-item\");\n  return openBlock(), createBlock(_component_el_dropdown_collection_item, {\n    disabled: _ctx.disabled,\n    \"text-value\": (_a = _ctx.textValue) != null ? _a : _ctx.textContent\n  }, {\n    default: withCtx(() => [createVNode(_component_el_roving_focus_item, {\n      focusable: !_ctx.disabled\n    }, {\n      default: withCtx(() => [createVNode(_component_el_dropdown_item_impl, mergeProps(_ctx.propsAndAttrs, {\n        onPointerleave: _ctx.handlePointerLeave,\n        onPointermove: _ctx.handlePointerMove,\n        onClickimpl: _ctx.handleClick\n      }), {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 16, [\"onPointerleave\", \"onPointermove\", \"onClickimpl\"])]),\n      _: 3\n    }, 8, [\"focusable\"])]),\n    _: 3\n  }, 8, [\"disabled\", \"text-value\"]);\n}\nvar DropdownItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"dropdown-item.vue\"]]);\nexport { DropdownItem as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "name", "components", "ElDropdownCollectionItem", "ElCollectionItem", "ElRovingFocusItem", "ElDropdownItemImpl", "inheritAttrs", "props", "dropdownItemProps", "emits", "setup", "emit", "attrs", "elDropdown", "useDropdown", "_instance", "getCurrentInstance", "itemRef", "ref", "textContent", "computed", "_a", "_b", "unref", "onItemEnter", "onItemLeave", "inject", "DROPDOWN_INJECTION_KEY", "handlePointerMove", "composeEventHandlers", "e", "defaultPrevented", "whenMouse", "disabled", "target", "currentTarget", "document", "activeElement", "contains", "focus", "handlePointerLeave", "handleClick", "type", "_c", "stopImmediatePropagation", "hideOnClick", "value", "call", "command<PERSON><PERSON>ler", "command", "propsAndAttrs", "_sfc_render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_el_dropdown_item_impl", "resolveComponent", "_component_el_roving_focus_item", "createVNode", "focusable", "mergeProps", "onPointerleave", "onPointermove", "default", "withCtx", "renderSlot", "$slots", "_"], "sources": ["../../../../../../packages/components/dropdown/src/dropdown-item.vue"], "sourcesContent": ["<template>\n  <el-dropdown-collection-item\n    :disabled=\"disabled\"\n    :text-value=\"textValue ?? textContent\"\n  >\n    <el-roving-focus-item :focusable=\"!disabled\">\n      <el-dropdown-item-impl\n        v-bind=\"propsAndAttrs\"\n        @pointerleave=\"handlePointerLeave\"\n        @pointermove=\"handlePointerMove\"\n        @clickimpl=\"handleClick\"\n      >\n        <slot />\n      </el-dropdown-item-impl>\n    </el-roving-focus-item>\n  </el-dropdown-collection-item>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  inject,\n  ref,\n  unref,\n} from 'vue'\nimport { ElRovingFocusItem } from '@element-plus/components/roving-focus-group'\nimport { composeEventHandlers, whenMouse } from '@element-plus/utils'\nimport ElDropdownItemImpl from './dropdown-item-impl.vue'\nimport { useDropdown } from './useDropdown'\nimport {\n  ElCollectionItem as ElDropdownCollectionItem,\n  dropdownItemProps,\n} from './dropdown'\nimport { DROPDOWN_INJECTION_KEY } from './tokens'\n\nexport default defineComponent({\n  name: 'ElDropdownItem',\n  components: {\n    ElDropdownCollectionItem,\n    ElRovingFocusItem,\n    ElDropdownItemImpl,\n  },\n  inheritAttrs: false,\n  props: dropdownItemProps,\n  emits: ['pointermove', 'pointerleave', 'click'],\n  setup(props, { emit, attrs }) {\n    const { elDropdown } = useDropdown()\n    const _instance = getCurrentInstance()\n    const itemRef = ref<HTMLElement | null>(null)\n    const textContent = computed(() => unref(itemRef)?.textContent ?? '')\n    const { onItemEnter, onItemLeave } = inject(\n      DROPDOWN_INJECTION_KEY,\n      undefined\n    )!\n\n    const handlePointerMove = composeEventHandlers(\n      (e: PointerEvent) => {\n        emit('pointermove', e)\n        return e.defaultPrevented\n      },\n      whenMouse((e) => {\n        if (props.disabled) {\n          onItemLeave(e)\n          return\n        }\n\n        const target = e.currentTarget as HTMLElement\n        /**\n         * This handles the following scenario:\n         *   when the item contains a form element such as input element\n         *   when the mouse is moving over the element itself which is contained by\n         *   the item, the default focusing logic should be prevented so that\n         *   it won't cause weird action.\n         */\n        if (\n          target === document.activeElement ||\n          target.contains(document.activeElement)\n        ) {\n          return\n        }\n\n        onItemEnter(e)\n        if (!e.defaultPrevented) {\n          target?.focus()\n        }\n      })\n    )\n\n    const handlePointerLeave = composeEventHandlers((e: PointerEvent) => {\n      emit('pointerleave', e)\n      return e.defaultPrevented\n    }, whenMouse(onItemLeave))\n\n    const handleClick = composeEventHandlers(\n      (e: PointerEvent) => {\n        if (props.disabled) {\n          return\n        }\n        emit('click', e)\n        return e.type !== 'keydown' && e.defaultPrevented\n      },\n      (e) => {\n        if (props.disabled) {\n          e.stopImmediatePropagation()\n          return\n        }\n        if (elDropdown?.hideOnClick?.value) {\n          elDropdown.handleClick?.()\n        }\n        elDropdown.commandHandler?.(props.command, _instance, e)\n      }\n    )\n\n    // direct usage of v-bind={ ...$props, ...$attrs } causes type errors\n    const propsAndAttrs = computed(() => ({ ...props, ...attrs }))\n\n    return {\n      handleClick,\n      handlePointerMove,\n      handlePointerLeave,\n      textContent,\n      propsAndAttrs,\n    }\n  },\n})\n</script>\n"], "mappings": ";;;;;;;;AAqCA,MAAKA,SAAA,GAAaC,eAAa;EAC7BC,IAAM;EACNC,UAAY;IAAAC,wBAAA,EACVC,gBAAA;IACAC,iBAAA;IACAC;EAAA,CACF;EACAC,YAAc;EACdC,KAAO,EAAAC,iBAAA;EACPC,KAAO,GAAC,aAAe,kBAAgB,OAAO;EAC9CC,KAAMA,CAAAH,KAAA,EAAO;IAAEI,IAAA;IAAMC;EAAA,CAAS;IACtB;MAAEC;IAAW,IAAIC,WAAY;IACnC,MAAMC,SAAA,GAAYC,kBAAmB;IAC/B,MAAAC,OAAA,GAAUC,GAAA,CAAwB,IAAI;IAC5C,MAAMC,WAAA,GAAcC,QAAS,OAAM;MAC7B,IAAAC,EAAE,EAAaC,EAAA;MACnB,QAAAA,EAAA,IAAAD,EAAA,GAAAE,KAAA,CAAAN,OAAA,sBAAAI,EAAA,CAAAF,WAAA,YAAAG,EAAA;IAAA,CACA;IACF;MAAAE,WAAA;MAAAC;IAAA,IAAAC,MAAA,CAAAC,sBAAA;IAEA,MAAMC,iBAAoB,GAAAC,oBAAA,CAAAC,CAAA;MACxBnB,IAAqB,gBAAAmB,CAAA;MACnB,OAAKA,CAAA,CAAAC,gBAAgB;IACrB,GAAAC,SAAS,CAAAF,CAAA;MACX,IAAAvB,KAAA,CAAA0B,QAAA;QACAR,WAAiB,CAAAK,CAAA;QACf;MACE;MACA,MAAAI,MAAA,GAAAJ,CAAA,CAAAK,aAAA;MACF,IAAAD,MAAA,KAAAE,QAAA,CAAAC,aAAA,IAAAH,MAAA,CAAAI,QAAA,CAAAF,QAAA,CAAAC,aAAA;QAEA;MAQA;MAIEb,WAAA,CAAAM,CAAA;MACF,KAAAA,CAAA,CAAAC,gBAAA;QAEAG,MAAA,QAAa,YAAAA,MAAA,CAAAK,KAAA;MACb;IACE;IACF,MAAAC,kBAAA,GAAAX,oBAAA,CAAAC,CAAA;MACFnB,IAAC,iBAAAmB,CAAA;MACH,OAAAA,CAAA,CAAAC,gBAAA;IAEA,CAAM,EAAAC,SAAA,CAAAP,WAAA;IACJ,MAAAgB,WAAA,GAAAZ,oBAAsB,CAAAC,CAAA;MACtB,IAAAvB,KAAS,CAAA0B,QAAA;QACE;MAEb;MACEtB,IAAqB,UAAAmB,CAAA;MACnB,OAAAA,CAAA,CAAAY,IAAoB,kBAAAZ,CAAA,CAAAC,gBAAA;IAClB,GAAAD,CAAA;MACF,IAAAT,EAAA,EAAAC,EAAA,EAAAqB,EAAA;MACA,IAAApC,KAAA,CAAA0B,QAAe;QACRH,CAAA,CAAAc,wBAAW;QACpB;MAAA;MAEE,KAAAvB,EAAA,GAAAR,UAAoB,oBAAAA,UAAA,CAAAgC,WAAA,qBAAAxB,EAAA,CAAAyB,KAAA;QAClB,CAAAxB,EAAA,GAA2BT,UAAA,CAAA4B,WAAA,qBAAAnB,EAAA,CAAAyB,IAAA,CAAAlC,UAAA;MAC3B;MACF,CAAA8B,EAAA,GAAA9B,UAAA,CAAAmC,cAAA,qBAAAL,EAAA,CAAAI,IAAA,CAAAlC,UAAA,EAAAN,KAAA,CAAA0C,OAAA,EAAAlC,SAAA,EAAAe,CAAA;IACA,CAAI;IACF,MAAAoB,aAAyB,GAAA9B,QAAA;MAAA,GAAAb,KAAA;MAAA,GAAAK;IAAA;IAC3B;MACA6B,WAAA;MACFb,iBAAA;MACFY,kBAAA;MAGMrB,WAAA;MAEC+B;IAAA,CACL;EAAA;AACA,CACA;AACA,SACAC,YAAAC,IAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,QAAA;EACF,IAAApC,EAAA;EACF,MAAAqC,gCAAA,GAAAC,gBAAA;EACD,MAAAC,+BAAA,GAAAD,gBAAA;;;;;;2BA/G+B,CAbjBE,WAAA,CAAAD,+BAAA;MAAAE,SAAA,EACe,CAAAV,IAAA,CAAAnB;IAAA;sBAWH,QAAA4B,WAAA,CAAAH,gCAAA,EAAAK,UAAA,CAAAX,IAAA,CAAAF,aAAA;QAAAc,cATY,EAAAZ,IAAA,CAAAZ,kBAAA;QAAAyB,aAAA,EAAAb,IAAA,CAAAxB,iBAAA;yBAQT,CAAAa;MAAA;QALrByB,OAAc,EAAAC,OAAA,QACDC,UAAA,CAAAhB,IAAA,CAAAiB,MAAA,aACF;QAAAC,CAAA;8BAEJ;MAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}