{"ast": null, "code": "import { defineComponent, inject, provide, onMounted, watch, unref, onBeforeUnmount, openBlock, createElementBlock, mergeProps, createVNode, withCtx, renderSlot } from 'vue';\nimport { isNil } from 'lodash-unified';\nimport ElFocusTrap from '../../focus-trap/src/focus-trap.mjs';\nimport { POPPER_CONTENT_INJECTION_KEY } from './constants.mjs';\nimport { popperContentProps, popperContentEmits } from './content.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { usePopperContentFocusTrap } from './composables/use-focus-trap.mjs';\nimport { usePopperContent } from './composables/use-content.mjs';\nimport { usePopperContentDOM } from './composables/use-content-dom.mjs';\nimport { formItemContextKey } from '../../form/src/constants.mjs';\nimport { NOOP } from '@vue/shared';\nimport { isElement } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPopperContent\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: popperContentProps,\n  emits: popperContentEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      focusStartRef,\n      trapped,\n      onFocusAfterReleased,\n      onFocusAfterTrapped,\n      onFocusInTrap,\n      onFocusoutPrevented,\n      onReleaseRequested\n    } = usePopperContentFocusTrap(props, emit);\n    const {\n      attributes,\n      arrowRef,\n      contentRef,\n      styles,\n      instanceRef,\n      role,\n      update\n    } = usePopperContent(props);\n    const {\n      ariaModal,\n      arrowStyle,\n      contentAttrs,\n      contentClass,\n      contentStyle,\n      updateZIndex\n    } = usePopperContentDOM(props, {\n      styles,\n      attributes,\n      role\n    });\n    const formItemContext = inject(formItemContextKey, void 0);\n    provide(POPPER_CONTENT_INJECTION_KEY, {\n      arrowStyle,\n      arrowRef\n    });\n    if (formItemContext) {\n      provide(formItemContextKey, {\n        ...formItemContext,\n        addInputId: NOOP,\n        removeInputId: NOOP\n      });\n    }\n    let triggerTargetAriaStopWatch = void 0;\n    const updatePopper = (shouldUpdateZIndex = true) => {\n      update();\n      shouldUpdateZIndex && updateZIndex();\n    };\n    const togglePopperAlive = () => {\n      updatePopper(false);\n      if (props.visible && props.focusOnShow) {\n        trapped.value = true;\n      } else if (props.visible === false) {\n        trapped.value = false;\n      }\n    };\n    onMounted(() => {\n      watch(() => props.triggerTargetEl, (triggerTargetEl, prevTriggerTargetEl) => {\n        triggerTargetAriaStopWatch == null ? void 0 : triggerTargetAriaStopWatch();\n        triggerTargetAriaStopWatch = void 0;\n        const el = unref(triggerTargetEl || contentRef.value);\n        const prevEl = unref(prevTriggerTargetEl || contentRef.value);\n        if (isElement(el)) {\n          triggerTargetAriaStopWatch = watch([role, () => props.ariaLabel, ariaModal, () => props.id], watches => {\n            [\"role\", \"aria-label\", \"aria-modal\", \"id\"].forEach((key, idx) => {\n              isNil(watches[idx]) ? el.removeAttribute(key) : el.setAttribute(key, watches[idx]);\n            });\n          }, {\n            immediate: true\n          });\n        }\n        if (prevEl !== el && isElement(prevEl)) {\n          [\"role\", \"aria-label\", \"aria-modal\", \"id\"].forEach(key => {\n            prevEl.removeAttribute(key);\n          });\n        }\n      }, {\n        immediate: true\n      });\n      watch(() => props.visible, togglePopperAlive, {\n        immediate: true\n      });\n    });\n    onBeforeUnmount(() => {\n      triggerTargetAriaStopWatch == null ? void 0 : triggerTargetAriaStopWatch();\n      triggerTargetAriaStopWatch = void 0;\n    });\n    expose({\n      popperContentRef: contentRef,\n      popperInstanceRef: instanceRef,\n      updatePopper,\n      contentStyle\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", mergeProps({\n        ref_key: \"contentRef\",\n        ref: contentRef\n      }, unref(contentAttrs), {\n        style: unref(contentStyle),\n        class: unref(contentClass),\n        tabindex: \"-1\",\n        onMouseenter: e => _ctx.$emit(\"mouseenter\", e),\n        onMouseleave: e => _ctx.$emit(\"mouseleave\", e)\n      }), [createVNode(unref(ElFocusTrap), {\n        trapped: unref(trapped),\n        \"trap-on-focus-in\": true,\n        \"focus-trap-el\": unref(contentRef),\n        \"focus-start-el\": unref(focusStartRef),\n        onFocusAfterTrapped: unref(onFocusAfterTrapped),\n        onFocusAfterReleased: unref(onFocusAfterReleased),\n        onFocusin: unref(onFocusInTrap),\n        onFocusoutPrevented: unref(onFocusoutPrevented),\n        onReleaseRequested: unref(onReleaseRequested)\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 8, [\"trapped\", \"focus-trap-el\", \"focus-start-el\", \"onFocusAfterTrapped\", \"onFocusAfterReleased\", \"onFocusin\", \"onFocusoutPrevented\", \"onReleaseRequested\"])], 16, [\"onMouseenter\", \"onMouseleave\"]);\n    };\n  }\n});\nvar ElPopperContent = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"content.vue\"]]);\nexport { ElPopperContent as default };", "map": {"version": 3, "names": ["name", "focusStartRef", "trapped", "onFocusAfterReleased", "onFocusAfterTrapped", "onFocusInTrap", "onFocusoutPrevented", "onReleaseRequested", "usePopperContentFocusTrap", "props", "emit", "attributes", "arrowRef", "contentRef", "styles", "instanceRef", "role", "update", "usePopperContent", "ariaModal", "arrowStyle", "contentAttrs", "contentClass", "contentStyle", "updateZIndex", "usePopperContentDOM", "formItemContext", "inject", "formItemContextKey", "provide", "POPPER_CONTENT_INJECTION_KEY", "addInputId", "NOOP", "removeInputId", "triggerTargetAriaStopWatch", "updatePopper", "shouldUpdateZIndex", "togglePopperAlive", "visible", "focusOnShow", "value", "onMounted", "watch", "triggerTargetEl", "prevTriggerTargetEl", "el", "unref", "prevEl", "isElement", "aria<PERSON><PERSON><PERSON>", "id", "watches", "for<PERSON>ach", "key", "idx", "isNil", "removeAttribute", "setAttribute", "immediate", "onBeforeUnmount", "expose", "popperContentRef", "popperInstanceRef", "_ctx", "_cache", "openBlock", "createElementBlock", "mergeProps", "ref_key", "ref", "style", "class", "tabindex", "onMouseenter", "e", "$emit", "onMouseleave", "createVNode", "ElFocusTrap", "onFocusin"], "sources": ["../../../../../../packages/components/popper/src/content.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"contentRef\"\n    v-bind=\"contentAttrs\"\n    :style=\"contentStyle\"\n    :class=\"contentClass\"\n    tabindex=\"-1\"\n    @mouseenter=\"(e) => $emit('mouseenter', e)\"\n    @mouseleave=\"(e) => $emit('mouseleave', e)\"\n  >\n    <el-focus-trap\n      :trapped=\"trapped\"\n      :trap-on-focus-in=\"true\"\n      :focus-trap-el=\"contentRef\"\n      :focus-start-el=\"focusStartRef\"\n      @focus-after-trapped=\"onFocusAfterTrapped\"\n      @focus-after-released=\"onFocusAfterReleased\"\n      @focusin=\"onFocusInTrap\"\n      @focusout-prevented=\"onFocusoutPrevented\"\n      @release-requested=\"onReleaseRequested\"\n    >\n      <slot />\n    </el-focus-trap>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { inject, onBeforeUnmount, onMounted, provide, unref, watch } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { NOOP, isElement } from '@element-plus/utils'\nimport ElFocusTrap from '@element-plus/components/focus-trap'\nimport { formItemContextKey } from '@element-plus/components/form'\nimport { POPPER_CONTENT_INJECTION_KEY } from './constants'\nimport { popperContentEmits, popperContentProps } from './content'\nimport {\n  usePopperContent,\n  usePopperContentDOM,\n  usePopperContentFocusTrap,\n} from './composables'\n\nimport type { WatchStopHandle } from 'vue'\n\ndefineOptions({\n  name: 'ElPopperContent',\n})\n\nconst emit = defineEmits(popperContentEmits)\n\nconst props = defineProps(popperContentProps)\n\nconst {\n  focusStartRef,\n  trapped,\n\n  onFocusAfterReleased,\n  onFocusAfterTrapped,\n  onFocusInTrap,\n  onFocusoutPrevented,\n  onReleaseRequested,\n} = usePopperContentFocusTrap(props, emit)\n\nconst { attributes, arrowRef, contentRef, styles, instanceRef, role, update } =\n  usePopperContent(props)\n\nconst {\n  ariaModal,\n  arrowStyle,\n  contentAttrs,\n  contentClass,\n  contentStyle,\n  updateZIndex,\n} = usePopperContentDOM(props, {\n  styles,\n  attributes,\n  role,\n})\n\nconst formItemContext = inject(formItemContextKey, undefined)\n\nprovide(POPPER_CONTENT_INJECTION_KEY, {\n  arrowStyle,\n  arrowRef,\n})\n\nif (formItemContext) {\n  // disallow auto-id from inside popper content\n  provide(formItemContextKey, {\n    ...formItemContext,\n    addInputId: NOOP,\n    removeInputId: NOOP,\n  })\n}\n\nlet triggerTargetAriaStopWatch: WatchStopHandle | undefined = undefined\n\nconst updatePopper = (shouldUpdateZIndex = true) => {\n  update()\n  shouldUpdateZIndex && updateZIndex()\n}\n\nconst togglePopperAlive = () => {\n  updatePopper(false)\n  if (props.visible && props.focusOnShow) {\n    trapped.value = true\n  } else if (props.visible === false) {\n    trapped.value = false\n  }\n}\n\nonMounted(() => {\n  watch(\n    () => props.triggerTargetEl,\n    (triggerTargetEl, prevTriggerTargetEl) => {\n      triggerTargetAriaStopWatch?.()\n      triggerTargetAriaStopWatch = undefined\n\n      const el = unref(triggerTargetEl || contentRef.value)\n      const prevEl = unref(prevTriggerTargetEl || contentRef.value)\n\n      if (isElement(el)) {\n        triggerTargetAriaStopWatch = watch(\n          [role, () => props.ariaLabel, ariaModal, () => props.id],\n          (watches) => {\n            ;['role', 'aria-label', 'aria-modal', 'id'].forEach((key, idx) => {\n              isNil(watches[idx])\n                ? el.removeAttribute(key)\n                : el.setAttribute(key, watches[idx]!)\n            })\n          },\n          { immediate: true }\n        )\n      }\n      if (prevEl !== el && isElement(prevEl)) {\n        ;['role', 'aria-label', 'aria-modal', 'id'].forEach((key) => {\n          prevEl.removeAttribute(key)\n        })\n      }\n    },\n    { immediate: true }\n  )\n\n  watch(() => props.visible, togglePopperAlive, { immediate: true })\n})\n\nonBeforeUnmount(() => {\n  triggerTargetAriaStopWatch?.()\n  triggerTargetAriaStopWatch = undefined\n})\n\ndefineExpose({\n  /**\n   * @description popper content element\n   */\n  popperContentRef: contentRef,\n  /**\n   * @description popperjs instance\n   */\n  popperInstanceRef: instanceRef,\n  /**\n   * @description method for updating popper\n   */\n  updatePopper,\n\n  /**\n   * @description content style\n   */\n  contentStyle,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;mCA0Cc;EACZA,IAAM;AACR;;;;;;;;;;IAMM;MACJC,aAAA;MACAC,OAAA;MAEAC,oBAAA;MACAC,mBAAA;MACAC,aAAA;MACAC,mBAAA;MACAC;IAAA,CACF,GAAIC,yBAA0B,CAAAC,KAAA,EAAOC,IAAI;IAEnC;MAAEC,UAAY;MAAAC,QAAA;MAAUC,UAAY;MAAAC,MAAA;MAAQC,WAAA;MAAaC,IAAM;MAAAC;IAAA,CACnE,GAAAC,gBAAA,CAAiBT,KAAK;IAElB;MACJU,SAAA;MACAC,UAAA;MACAC,YAAA;MACAC,YAAA;MACAC,YAAA;MACAC;IAAA,CACF,GAAIC,mBAAA,CAAoBhB,KAAO;MAC7BK,MAAA;MACAH,UAAA;MACAK;IAAA,CACD;IAEK,MAAAU,eAAA,GAAkBC,MAAO,CAAAC,kBAAA,EAAoB,KAAS;IAE5DC,OAAA,CAAQC,4BAA8B;MACpCV,UAAA;MACAR;IAAA,CACD;IAED,IAAIc,eAAiB;MAEnBG,OAAA,CAAQD,kBAAoB;QAC1B,GAAGF,eAAA;QACHK,UAAY,EAAAC,IAAA;QACZC,aAAe,EAAAD;MAAA,CAChB;IAAA;IAGH,IAAIE,0BAA0D;IAExD,MAAAC,YAAA,GAAeA,CAACC,kBAAA,GAAqB,IAAS;MAC3CnB,MAAA;MACPmB,kBAAA,IAAsBZ,YAAa;IAAA,CACrC;IAEA,MAAMa,iBAAA,GAAoBA,CAAA,KAAM;MAC9BF,YAAA,CAAa,KAAK;MACd,IAAA1B,KAAA,CAAM6B,OAAW,IAAA7B,KAAA,CAAM8B,WAAa;QACtCrC,OAAA,CAAQsC,KAAQ;MAAA,CAClB,UAAW/B,KAAM,CAAA6B,OAAA,KAAY,KAAO;QAClCpC,OAAA,CAAQsC,KAAQ;MAAA;IAClB,CACF;IAEAC,SAAA,CAAU,MAAM;MACdC,KAAA,OAAAjC,KAAA,CAAAkC,eAAA,GAAAA,eAAA,EAAAC,mBAAA;QACEV,0BAAY,oBAAAA,0BAAA;QACZA,0BAA0C;QACX,MAAAW,EAAA,GAAAC,KAAA,CAAAH,eAAA,IAAA9B,UAAA,CAAA2B,KAAA;QACA,MAAAO,MAAA,GAAAD,KAAA,CAAAF,mBAAA,IAAA/B,UAAA,CAAA2B,KAAA;QAE7B,IAAAQ,SAAW,CAAAH,EAAA,GAAM;UACjBX,0BAA4C,GAAAQ,KAAA,EAAA1B,IAAA,QAAAP,KAAA,CAAAwC,SAAgB,EAAA9B,SAAA,QAAAV,KAAA,CAAAyC,EAAA,GAAAC,OAAA;YAG7B,2CAAAC,OAAA,EAAAC,GAAA,EAAAC,GAAA;cAC3BC,KAAA,CAAAJ,OAAa,CAAAG,GAAA,EAAM,GAAAT,EAAA,CAAAW,eAAsB,CAAAH,GAAA,IAAAR,EAAA,CAAAY,YAAc,CAAAJ,GAAA,EAAAF,OAAA,CAAAG,GAAA;YAAA,EACvD;UACE;YAAAI,SAAA;UAAA;QAAC;QACC,IAAAX,MAAA,KAAAF,EAAc,IAAAG,SAAI,CAAAD,MACX;UAGX,2CAAAK,OAAA,CAAAC,GAAA;YACAN,MAAA,CAAAS,eAAkB,CAAAH,GAAA;UAAA,CACpB;QAAA;MAEF;QAAAK,SAAe;MAAA;MACbhB,KAAA,OAAAjC,KAAA,CAAA6B,OAAA,EAAAD,iBAAA;QAAAqB,SAAA;MAAA;IAAC;IACCC,eAAA;MAA0BzB,0BAC3B,oBAAAA,0BAAA;MACHA,0BAAA;IAAA,CACF;IACA0B,MAAE;MACJC,gBAAA,EAAAhD,UAAA;MAEAiD,iBAAkB,EAAA/C,WAAA;MACnBoB,YAAA;MAEDZ;IACE,CAA6B;IACA,QAAAwC,IAAA,EAAAC,MAAA;MAC9B,OAAAC,SAAA,IAAAC,kBAAA,QAAAC,UAAA;QAEYC,OAAA;QAAAC,GAAA,EAAAxD;MAAA,GAAAiC,KAAA,CAAAzB,YAAA;QAAAiD,KAAA,EAAAxB,KAAA,CAAAvB,YAAA;QAIOgD,KAAA,EAAAzB,KAAA,CAAAxB,YAAA;QAAAkD,QAAA;QAAAC,YAAA,EAAAC,CAAA,IAAAX,IAAA,CAAAY,KAAA,eAAAD,CAAA;QAAAE,YAAA,EAAAF,CAAA,IAAAX,IAAA,CAAAY,KAAA,eAAAD,CAAA;MAAA,CAIC,IAAAG,WAAA,CAAA/B,KAAA,CAAAgC,WAAA;QAAA5E,OAAA,EAAA4C,KAAA,CAAA5C,OAAA;QAAA;QAInB,iBAAA4C,KAAA,CAAAjC,UAAA;QAAA,kBAAAiC,KAAA,CAAA7C,aAAA;QAAAG,mBAAA,EAAA0C,KAAA,CAAA1C,mBAAA;QAAAD,oBAAA,EAAA2C,KAAA,CAAA3C,oBAAA;QAKA4E,SAAA,EAAAjC,KAAA,CAAAzC,aAAA;QACDC,mBAAA,EAAAwC,KAAA,CAAAxC,mBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}