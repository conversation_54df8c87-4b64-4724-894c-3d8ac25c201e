{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport { defineComponent, ref, reactive, computed, watch, openBlock, createElementBlock, unref, normalizeClass, createElementVNode, normalizeStyle, Fragment, renderList, renderSlot, createTextVNode, toDisplayString, createCommentVNode } from 'vue';\nimport { useActiveElement, useResizeObserver } from '@vueuse/core';\nimport { segmentedProps, segmentedEmits, defaultProps } from './segmented.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { useFormSize, useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { isObject } from '@vue/shared';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElSegmented\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: segmentedProps,\n  emits: segmentedEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"segmented\");\n    const segmentedId = useId();\n    const segmentedSize = useFormSize();\n    const _disabled = useFormDisabled();\n    const {\n      formItem\n    } = useFormItem();\n    const {\n      inputId,\n      isLabeledByFormItem\n    } = useFormItemInputId(props, {\n      formItemContext: formItem\n    });\n    const segmentedRef = ref(null);\n    const activeElement = useActiveElement();\n    const state = reactive({\n      isInit: false,\n      width: 0,\n      height: 0,\n      translateX: 0,\n      translateY: 0,\n      focusVisible: false\n    });\n    const handleChange = item => {\n      const value = getValue(item);\n      emit(UPDATE_MODEL_EVENT, value);\n      emit(CHANGE_EVENT, value);\n    };\n    const aliasProps = computed(() => ({\n      ...defaultProps,\n      ...props.props\n    }));\n    const getValue = item => {\n      return isObject(item) ? item[aliasProps.value.value] : item;\n    };\n    const getLabel = item => {\n      return isObject(item) ? item[aliasProps.value.label] : item;\n    };\n    const getDisabled = item => {\n      return !!(_disabled.value || (isObject(item) ? item[aliasProps.value.disabled] : false));\n    };\n    const getSelected = item => {\n      return props.modelValue === getValue(item);\n    };\n    const getOption = value => {\n      return props.options.find(item => getValue(item) === value);\n    };\n    const getItemCls = item => {\n      return [ns.e(\"item\"), ns.is(\"selected\", getSelected(item)), ns.is(\"disabled\", getDisabled(item))];\n    };\n    const updateSelect = () => {\n      if (!segmentedRef.value) return;\n      const selectedItem = segmentedRef.value.querySelector(\".is-selected\");\n      const selectedItemInput = segmentedRef.value.querySelector(\".is-selected input\");\n      if (!selectedItem || !selectedItemInput) {\n        state.width = 0;\n        state.height = 0;\n        state.translateX = 0;\n        state.translateY = 0;\n        state.focusVisible = false;\n        return;\n      }\n      const rect = selectedItem.getBoundingClientRect();\n      state.isInit = true;\n      if (props.direction === \"vertical\") {\n        state.height = rect.height;\n        state.translateY = selectedItem.offsetTop;\n      } else {\n        state.width = rect.width;\n        state.translateX = selectedItem.offsetLeft;\n      }\n      try {\n        state.focusVisible = selectedItemInput.matches(\":focus-visible\");\n      } catch (e) {}\n    };\n    const segmentedCls = computed(() => [ns.b(), ns.m(segmentedSize.value), ns.is(\"block\", props.block)]);\n    const selectedStyle = computed(() => ({\n      width: props.direction === \"vertical\" ? \"100%\" : `${state.width}px`,\n      height: props.direction === \"vertical\" ? `${state.height}px` : \"100%\",\n      transform: props.direction === \"vertical\" ? `translateY(${state.translateY}px)` : `translateX(${state.translateX}px)`,\n      display: state.isInit ? \"block\" : \"none\"\n    }));\n    const selectedCls = computed(() => [ns.e(\"item-selected\"), ns.is(\"disabled\", getDisabled(getOption(props.modelValue))), ns.is(\"focus-visible\", state.focusVisible)]);\n    const name = computed(() => {\n      return props.name || segmentedId.value;\n    });\n    useResizeObserver(segmentedRef, updateSelect);\n    watch(activeElement, updateSelect);\n    watch(() => props.modelValue, () => {\n      var _a;\n      updateSelect();\n      if (props.validateEvent) {\n        (_a = formItem == null ? void 0 : formItem.validate) == null ? void 0 : _a.call(formItem, \"change\").catch(err => debugWarn(err));\n      }\n    }, {\n      flush: \"post\"\n    });\n    return (_ctx, _cache) => {\n      return _ctx.options.length ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        id: unref(inputId),\n        ref_key: \"segmentedRef\",\n        ref: segmentedRef,\n        class: normalizeClass(unref(segmentedCls)),\n        role: \"radiogroup\",\n        \"aria-label\": !unref(isLabeledByFormItem) ? _ctx.ariaLabel || \"segmented\" : void 0,\n        \"aria-labelledby\": unref(isLabeledByFormItem) ? unref(formItem).labelId : void 0\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass([unref(ns).e(\"group\"), unref(ns).m(props.direction)])\n      }, [createElementVNode(\"div\", {\n        style: normalizeStyle(unref(selectedStyle)),\n        class: normalizeClass(unref(selectedCls))\n      }, null, 6), (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.options, (item, index) => {\n        return openBlock(), createElementBlock(\"label\", {\n          key: index,\n          class: normalizeClass(getItemCls(item))\n        }, [createElementVNode(\"input\", {\n          class: normalizeClass(unref(ns).e(\"item-input\")),\n          type: \"radio\",\n          name: unref(name),\n          disabled: getDisabled(item),\n          checked: getSelected(item),\n          onChange: $event => handleChange(item)\n        }, null, 42, [\"name\", \"disabled\", \"checked\", \"onChange\"]), createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).e(\"item-label\"))\n        }, [renderSlot(_ctx.$slots, \"default\", {\n          item\n        }, () => [createTextVNode(toDisplayString(getLabel(item)), 1)])], 2)], 2);\n      }), 128))], 2)], 10, [\"id\", \"aria-label\", \"aria-labelledby\"])) : createCommentVNode(\"v-if\", true);\n    };\n  }\n});\nvar Segmented = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"segmented.vue\"]]);\nexport { Segmented as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace", "segmentedId", "useId", "segmentedSize", "useFormSize", "_disabled", "useFormDisabled", "formItem", "useFormItem", "inputId", "isLabeledByFormItem", "useFormItemInputId", "props", "formItemContext", "segmentedRef", "ref", "activeElement", "useActiveElement", "state", "reactive", "isInit", "width", "height", "translateX", "translateY", "focusVisible", "handleChange", "item", "value", "getValue", "emit", "UPDATE_MODEL_EVENT", "CHANGE_EVENT", "aliasProps", "computed", "defaultProps", "isObject", "get<PERSON><PERSON><PERSON>", "label", "getDisabled", "disabled", "getSelected", "modelValue", "getOption", "options", "find", "getItemCls", "e", "is", "updateSelect", "selectedItem", "querySelector", "selectedItemInput", "rect", "getBoundingClientRect", "direction", "offsetTop", "offsetLeft", "matches", "segmentedCls", "b", "m", "block", "selected<PERSON><PERSON><PERSON>", "transform", "display", "selectedCls", "useResizeObserver", "watch", "_a", "validateEvent", "validate", "call", "catch", "err", "debugWarn", "flush", "_ctx", "_cache", "length", "openBlock", "createElementBlock", "key", "id", "unref", "ref_key"], "sources": ["../../../../../../packages/components/segmented/src/segmented.vue"], "sourcesContent": ["<template>\n  <div\n    v-if=\"options.length\"\n    :id=\"inputId\"\n    ref=\"segmentedRef\"\n    :class=\"segmentedCls\"\n    role=\"radiogroup\"\n    :aria-label=\"!isLabeledByFormItem ? ariaLabel || 'segmented' : undefined\"\n    :aria-labelledby=\"isLabeledByFormItem ? formItem!.labelId : undefined\"\n  >\n    <div :class=\"[ns.e('group'), ns.m(props.direction)]\">\n      <div :style=\"selectedStyle\" :class=\"selectedCls\" />\n      <label\n        v-for=\"(item, index) in options\"\n        :key=\"index\"\n        :class=\"getItemCls(item)\"\n      >\n        <input\n          :class=\"ns.e('item-input')\"\n          type=\"radio\"\n          :name=\"name\"\n          :disabled=\"getDisabled(item)\"\n          :checked=\"getSelected(item)\"\n          @change=\"handleChange(item)\"\n        />\n        <div :class=\"ns.e('item-label')\">\n          <slot :item=\"item\">{{ getLabel(item) }}</slot>\n        </div>\n      </label>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, reactive, ref, watch } from 'vue'\nimport { useActiveElement, useResizeObserver } from '@vueuse/core'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\nimport { debugWarn, isObject } from '@element-plus/utils'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { defaultProps, segmentedEmits, segmentedProps } from './segmented'\nimport type { Option } from './types'\n\ndefineOptions({\n  name: 'ElSegmented',\n})\n\nconst props = defineProps(segmentedProps)\nconst emit = defineEmits(segmentedEmits)\n\nconst ns = useNamespace('segmented')\nconst segmentedId = useId()\nconst segmentedSize = useFormSize()\nconst _disabled = useFormDisabled()\nconst { formItem } = useFormItem()\nconst { inputId, isLabeledByFormItem } = useFormItemInputId(props, {\n  formItemContext: formItem,\n})\n\nconst segmentedRef = ref<HTMLElement | null>(null)\nconst activeElement = useActiveElement()\n\nconst state = reactive({\n  isInit: false,\n  width: 0,\n  height: 0,\n  translateX: 0,\n  translateY: 0,\n  focusVisible: false,\n})\n\nconst handleChange = (item: Option) => {\n  const value = getValue(item)\n  emit(UPDATE_MODEL_EVENT, value)\n  emit(CHANGE_EVENT, value)\n}\n\nconst aliasProps = computed(() => ({ ...defaultProps, ...props.props }))\n\nconst getValue = (item: Option) => {\n  return isObject(item) ? item[aliasProps.value.value] : item\n}\n\nconst getLabel = (item: Option) => {\n  return isObject(item) ? item[aliasProps.value.label] : item\n}\n\nconst getDisabled = (item: Option | undefined) => {\n  return !!(\n    _disabled.value ||\n    (isObject(item) ? item[aliasProps.value.disabled] : false)\n  )\n}\n\nconst getSelected = (item: Option) => {\n  return props.modelValue === getValue(item)\n}\n\nconst getOption = (value: any) => {\n  return props.options.find((item) => getValue(item) === value)\n}\n\nconst getItemCls = (item: Option) => {\n  return [\n    ns.e('item'),\n    ns.is('selected', getSelected(item)),\n    ns.is('disabled', getDisabled(item)),\n  ]\n}\n\nconst updateSelect = () => {\n  if (!segmentedRef.value) return\n  const selectedItem = segmentedRef.value.querySelector(\n    '.is-selected'\n  ) as HTMLElement\n  const selectedItemInput = segmentedRef.value.querySelector(\n    '.is-selected input'\n  ) as HTMLElement\n  if (!selectedItem || !selectedItemInput) {\n    state.width = 0\n    state.height = 0\n    state.translateX = 0\n    state.translateY = 0\n    state.focusVisible = false\n    return\n  }\n  const rect = selectedItem.getBoundingClientRect()\n  state.isInit = true\n  if (props.direction === 'vertical') {\n    state.height = rect.height\n    state.translateY = selectedItem.offsetTop\n  } else {\n    state.width = rect.width\n    state.translateX = selectedItem.offsetLeft\n  }\n  try {\n    // This will failed in test\n    state.focusVisible = selectedItemInput.matches(':focus-visible')\n  } catch {}\n}\n\nconst segmentedCls = computed(() => [\n  ns.b(),\n  ns.m(segmentedSize.value),\n  ns.is('block', props.block),\n])\n\nconst selectedStyle = computed(() => ({\n  width: props.direction === 'vertical' ? '100%' : `${state.width}px`,\n  height: props.direction === 'vertical' ? `${state.height}px` : '100%',\n  transform:\n    props.direction === 'vertical'\n      ? `translateY(${state.translateY}px)`\n      : `translateX(${state.translateX}px)`,\n  display: state.isInit ? 'block' : 'none',\n}))\n\nconst selectedCls = computed(() => [\n  ns.e('item-selected'),\n  ns.is('disabled', getDisabled(getOption(props.modelValue))),\n  ns.is('focus-visible', state.focusVisible),\n])\n\nconst name = computed(() => {\n  return props.name || segmentedId.value\n})\n\nuseResizeObserver(segmentedRef, updateSelect)\n\nwatch(activeElement, updateSelect)\n\nwatch(\n  () => props.modelValue,\n  () => {\n    updateSelect()\n    if (props.validateEvent) {\n      formItem?.validate?.('change').catch((err) => debugWarn(err))\n    }\n  },\n  {\n    flush: 'post',\n  }\n)\n</script>\n"], "mappings": ";;;;;;;;;;;;;mCAgDc;EACZA,IAAM;AACR;;;;;;;;;IAKM,MAAAC,EAAA,GAAKC,YAAA,CAAa,WAAW;IACnC,MAAMC,WAAA,GAAcC,KAAM;IAC1B,MAAMC,aAAA,GAAgBC,WAAY;IAClC,MAAMC,SAAA,GAAYC,eAAgB;IAC5B;MAAEC;IAAS,IAAIC,WAAY;IACjC,MAAM;MAAEC,OAAA;MAASC;IAAoB,IAAIC,kBAAA,CAAmBC,KAAO;MACjEC,eAAiB,EAAAN;IAAA,CAClB;IAEK,MAAAO,YAAA,GAAeC,GAAA,CAAwB,IAAI;IACjD,MAAMC,aAAA,GAAgBC,gBAAiB;IAEvC,MAAMC,KAAA,GAAQC,QAAS;MACrBC,MAAQ;MACRC,KAAO;MACPC,MAAQ;MACRC,UAAY;MACZC,UAAY;MACZC,YAAc;IAAA,CACf;IAEK,MAAAC,YAAA,GAAgBC,IAAiB;MAC/B,MAAAC,KAAA,GAAQC,QAAA,CAASF,IAAI;MAC3BG,IAAA,CAAKC,kBAAA,EAAoBH,KAAK;MAC9BE,IAAA,CAAKE,YAAA,EAAcJ,KAAK;IAAA,CAC1B;IAEM,MAAAK,UAAA,GAAaC,QAAA,CAAS,OAAO;MAAE,GAAGC,YAAc;MAAA,GAAGvB,KAAM,CAAAA;IAAA,CAAQ;IAEjE,MAAAiB,QAAA,GAAYF,IAAiB;MACjC,OAAOS,QAAA,CAAST,IAAI,IAAIA,IAAA,CAAKM,UAAW,CAAAL,KAAA,CAAMA,KAAK,CAAI,GAAAD,IAAA;IAAA,CACzD;IAEM,MAAAU,QAAA,GAAYV,IAAiB;MACjC,OAAOS,QAAA,CAAST,IAAI,IAAIA,IAAA,CAAKM,UAAW,CAAAL,KAAA,CAAMU,KAAK,CAAI,GAAAX,IAAA;IAAA,CACzD;IAEM,MAAAY,WAAA,GAAeZ,IAA6B;MACzC,QAAC,EACNtB,SAAA,CAAUuB,KACT,KAAAQ,QAAA,CAAST,IAAI,IAAIA,IAAK,CAAAM,UAAA,CAAWL,KAAM,CAAAY,QAAQ,CAAI;IAAA,CAExD;IAEM,MAAAC,WAAA,GAAed,IAAiB;MAC7B,OAAAf,KAAA,CAAM8B,UAAe,KAAAb,QAAA,CAASF,IAAI;IAAA,CAC3C;IAEM,MAAAgB,SAAA,GAAaf,KAAe;MACzB,OAAAhB,KAAA,CAAMgC,OAAA,CAAQC,IAAK,CAAClB,IAAA,IAASE,QAAS,CAAAF,IAAI,MAAMC,KAAK;IAAA,CAC9D;IAEM,MAAAkB,UAAA,GAAcnB,IAAiB;MAC5B,QACL5B,EAAA,CAAGgD,CAAA,CAAE,MAAM,GACXhD,EAAG,CAAAiD,EAAA,CAAG,UAAY,EAAAP,WAAA,CAAYd,IAAI,CAAC,GACnC5B,EAAG,CAAAiD,EAAA,CAAG,UAAY,EAAAT,WAAA,CAAYZ,IAAI,CAAC,EACrC;IAAA,CACF;IAEA,MAAMsB,YAAA,GAAeA,CAAA,KAAM;MACrB,KAACnC,YAAA,CAAac,KAAO,EACnB;MACJ,MAAAsB,YAAA,GAAApC,YAAA,CAAAc,KAAA,CAAAuB,aAAA;MACF,MAAAC,iBAAA,GAAAtC,YAAA,CAAAc,KAAA,CAAAuB,aAAA;MACM,KAAAD,YAAA,KAAAE,iBAAiC,EAAM;QAC3ClC,KAAA,CAAAG,KAAA;QACFH,KAAA,CAAAI,MAAA;QACIJ,KAAiB,CAAAK,UAAA,IAAC;QACpBL,KAAA,CAAMM,UAAQ;QACdN,KAAA,CAAMO,YAAS;QACf;MACA;MACA,MAAA4B,IAAqB,GAAAH,YAAA,CAAAI,qBAAA;MACrBpC,KAAA,CAAAE,MAAA;MACF,IAAAR,KAAA,CAAA2C,SAAA;QACMrC,KAAA,CAAAI,MAAA,GAAA+B,IAAA,CAAA/B,MAA0C;QAChDJ,KAAe,CAAAM,UAAA,GAAA0B,YAAA,CAAAM,SAAA;MACf,CAAI;QACFtC,KAAA,CAAMG,KAAA,GAAAgC,IAAc,CAAAhC,KAAA;QACpBH,KAAA,CAAMK,UAAA,GAAa2B,YAAa,CAAAO,UAAA;MAAA;MAEhC;QACAvC,KAAA,CAAMO,YAAA,GAA0B2B,iBAAA,CAAAM,OAAA;MAAA,CAClC,QAAAX,CAAA,GACA;IAEE,CAAM;IAAyD,MACzDY,YAAA,GAAAzB,QAAA,QAACnC,EAAA,CAAA6D,CAAA,IACX7D,EAAA,CAAA8D,CAAA,CAAA1D,aAAA,CAAAyB,KAAA,GAEM7B,EAAA,CAAAiD,EAAA,UAAApC,KAAA,CAAAkD,KAAA,EAA8B,CAClC;IACA,MAAKC,aAAA,GAAmB7B,QAAA;MACxBb,KAAG,EAAYT,KAAA,CAAA2C,SAAW,8BAAArC,KAAA,CAAAG,KAAA;MAC3BC,MAAA,EAAAV,KAAA,CAAA2C,SAAA,qBAAArC,KAAA,CAAAI,MAAA;MAEK0C,SAAA,EAAApD,KAAA,CAAA2C,SAAA,KAAgC,2BAAArC,KAAA,CAAAM,UAAA,sBAAAN,KAAA,CAAAK,UAAA;MACpC0C,OAAO,EAAM/C,KAAA,CAAAE,MAAA;IAAkD,EAC/D;IACA,MAAA8C,WACQ,GAAAhC,QAAA,OACF,CAENnC,EAAA,CAAAgD,CAAA,gBAAe,GACfhD,EAAA,CAAAiD,EAAA,aAAAT,WAAA,CAAAI,SAAA,CAAA/B,KAAA,CAAA8B,UAAA,KAEI3C,EAAA,CAAAiD,EAAA,kBAAA9B,KAA6B,CAAAO,YAAA,EACjC;IACA,MAAA3B,IAAkB,GAAAoC,QAAA;MAClB,OAAuBtB,KAAA,CAAAd,IAAA,IAAAG,WAAkB,CAAA2B,KAAA;IAAA,CAC1C;IAEKuC,iBAAA,CAAArD,YAAsB,EAAAmC,YAAA;IACnBmB,KAAA,CAAApD,aAAA,EAAAiC,YAA0B;IACnCmB,KAAC,OAAAxD,KAAA,CAAA8B,UAAA;MAED,IAAA2B,EAAA;MAEApB,YAAA;MAEA,IAAArC,KAAA,CAAA0D,aAAA;QAAA,CAAAD,EAAA,GACc9D,QAAA,oBAAAA,QAAA,CAAAgE,QAAA,qBAAAF,EAAA,CAAAG,IAAA,CAAAjE,QAAA,YAAAkE,KAAA,CAAAC,GAAA,IAAAC,SAAA,CAAAD,GAAA;MAAA;IAEV,CAAa;MACbE,KAAA;IACE,CAAU;IACZ,QAAAC,IAAA,EAAAC,MAAA;MACF,OAAAD,IAAA,CAAAjC,OAAA,CAAAmC,MAAA,IAAAC,SAAA,IAAAC,kBAAA;QACAC,GAAA;QACEC,EAAO,EAAAC,KAAA,CAAA3E,OAAA;QACT4E,OAAA;QACFtE,GAAA,EAAAD,YAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}