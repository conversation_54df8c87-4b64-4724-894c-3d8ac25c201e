{"ast": null, "code": "import Carousel from './src/carousel2.mjs';\nimport CarouselItem from './src/carousel-item2.mjs';\nexport { carouselEmits, carouselProps } from './src/carousel.mjs';\nexport { carouselItemProps } from './src/carousel-item.mjs';\nexport { CAROUSEL_ITEM_NAME, carouselContextKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElCarousel = withInstall(Carousel, {\n  CarouselItem\n});\nconst ElCarouselItem = withNoopInstall(CarouselItem);\nexport { ElCarousel, ElCarouselItem, ElCarousel as default };", "map": {"version": 3, "names": ["ElCarousel", "withInstall", "Carousel", "CarouselItem", "ElCarouselItem", "withNoopInstall"], "sources": ["../../../../../packages/components/carousel/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON>oopInstall } from '@element-plus/utils'\nimport Carousel from './src/carousel.vue'\nimport CarouselItem from './src/carousel-item.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCarousel: SFCWithInstall<typeof Carousel> & {\n  CarouselItem: typeof CarouselItem\n} = withInstall(Carousel, {\n  CarouselItem,\n})\n\nexport default ElCarousel\n\nexport const ElCarouselItem: SFCWithInstall<typeof CarouselItem> =\n  withNoopInstall(CarouselItem)\n\nexport * from './src/carousel'\nexport * from './src/carousel-item'\nexport * from './src/constants'\n\nexport type { CarouselInstance, CarouselItemInstance } from './src/instance'\n"], "mappings": ";;;;;;AAGY,MAACA,UAAU,GAAGC,WAAW,CAACC,QAAQ,EAAE;EAC9CC;AACF,CAAC;AAEW,MAACC,cAAc,GAAGC,eAAe,CAACF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}