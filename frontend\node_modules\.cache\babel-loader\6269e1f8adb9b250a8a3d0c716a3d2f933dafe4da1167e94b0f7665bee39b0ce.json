{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, withDirectives, createElementVNode, isRef, withModifiers, vModelRadio, normalizeStyle, renderSlot, createTextVNode, toDisplayString } from 'vue';\nimport { useRadio } from './use-radio.mjs';\nimport { radioButtonProps } from './radio-button.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElRadioButton\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: radioButtonProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"radio\");\n    const {\n      radioRef,\n      focus,\n      size,\n      disabled,\n      modelValue,\n      radioGroup,\n      actualValue\n    } = useRadio(props);\n    const activeStyle = computed(() => {\n      return {\n        backgroundColor: (radioGroup == null ? void 0 : radioGroup.fill) || \"\",\n        borderColor: (radioGroup == null ? void 0 : radioGroup.fill) || \"\",\n        boxShadow: (radioGroup == null ? void 0 : radioGroup.fill) ? `-1px 0 0 0 ${radioGroup.fill}` : \"\",\n        color: (radioGroup == null ? void 0 : radioGroup.textColor) || \"\"\n      };\n    });\n    return (_ctx, _cache) => {\n      var _a;\n      return openBlock(), createElementBlock(\"label\", {\n        class: normalizeClass([unref(ns).b(\"button\"), unref(ns).is(\"active\", unref(modelValue) === unref(actualValue)), unref(ns).is(\"disabled\", unref(disabled)), unref(ns).is(\"focus\", unref(focus)), unref(ns).bm(\"button\", unref(size))])\n      }, [withDirectives(createElementVNode(\"input\", {\n        ref_key: \"radioRef\",\n        ref: radioRef,\n        \"onUpdate:modelValue\": $event => isRef(modelValue) ? modelValue.value = $event : null,\n        class: normalizeClass(unref(ns).be(\"button\", \"original-radio\")),\n        value: unref(actualValue),\n        type: \"radio\",\n        name: _ctx.name || ((_a = unref(radioGroup)) == null ? void 0 : _a.name),\n        disabled: unref(disabled),\n        onFocus: $event => focus.value = true,\n        onBlur: $event => focus.value = false,\n        onClick: withModifiers(() => {}, [\"stop\"])\n      }, null, 42, [\"onUpdate:modelValue\", \"value\", \"name\", \"disabled\", \"onFocus\", \"onBlur\", \"onClick\"]), [[vModelRadio, unref(modelValue)]]), createElementVNode(\"span\", {\n        class: normalizeClass(unref(ns).be(\"button\", \"inner\")),\n        style: normalizeStyle(unref(modelValue) === unref(actualValue) ? unref(activeStyle) : {}),\n        onKeydown: withModifiers(() => {}, [\"stop\"])\n      }, [renderSlot(_ctx.$slots, \"default\", {}, () => [createTextVNode(toDisplayString(_ctx.label), 1)])], 46, [\"onKeydown\"])], 2);\n    };\n  }\n});\nvar RadioButton = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"radio-button.vue\"]]);\nexport { RadioButton as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace", "radioRef", "focus", "size", "disabled", "modelValue", "radioGroup", "actualValue", "useRadio", "props", "activeStyle", "computed", "backgroundColor", "fill", "borderColor", "boxShadow", "color", "textColor"], "sources": ["../../../../../../packages/components/radio/src/radio-button.vue"], "sourcesContent": ["<template>\n  <label\n    :class=\"[\n      ns.b('button'),\n      ns.is('active', modelValue === actualValue),\n      ns.is('disabled', disabled),\n      ns.is('focus', focus),\n      ns.bm('button', size),\n    ]\"\n  >\n    <input\n      ref=\"radioRef\"\n      v-model=\"modelValue\"\n      :class=\"ns.be('button', 'original-radio')\"\n      :value=\"actualValue\"\n      type=\"radio\"\n      :name=\"name || radioGroup?.name\"\n      :disabled=\"disabled\"\n      @focus=\"focus = true\"\n      @blur=\"focus = false\"\n      @click.stop\n    />\n    <span\n      :class=\"ns.be('button', 'inner')\"\n      :style=\"modelValue === actualValue ? activeStyle : {}\"\n      @keydown.stop\n    >\n      <slot>\n        {{ label }}\n      </slot>\n    </span>\n  </label>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useRadio } from './use-radio'\nimport { radioButtonProps } from './radio-button'\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElRadioButton',\n})\n\nconst props = defineProps(radioButtonProps)\n\nconst ns = useNamespace('radio')\nconst { radioRef, focus, size, disabled, modelValue, radioGroup, actualValue } =\n  useRadio(props)\n\nconst activeStyle = computed<CSSProperties>(() => {\n  return {\n    backgroundColor: radioGroup?.fill || '',\n    borderColor: radioGroup?.fill || '',\n    boxShadow: radioGroup?.fill ? `-1px 0 0 0 ${radioGroup.fill}` : '',\n    color: radioGroup?.textColor || '',\n  }\n})\n</script>\n"], "mappings": ";;;;;mCAyCc;EACZA,IAAM;AACR;;;;;;IAIM,MAAAC,EAAA,GAAKC,YAAA,CAAa,OAAO;IACzB;MAAEC,QAAU;MAAAC,KAAA;MAAOC,IAAM;MAAAC,QAAA;MAAUC,UAAA;MAAYC,UAAY;MAAAC;IAAA,CAC/D,GAAAC,QAAA,CAASC,KAAK;IAEV,MAAAC,WAAA,GAAcC,QAAA,CAAwB,MAAM;MACzC;QACLC,eAAA,EAAiB,CAAAN,UAAA,IAAoB,gBAAAA,UAAA,CAAAO,IAAA;QACrCC,WAAA,EAAa,CAAAR,UAAA,IAAoB,gBAAAA,UAAA,CAAAO,IAAA;QACjCE,SAAA,EAAW,CAAYT,UAAA,QAAO,GAAc,SAAAA,UAAA,CAAAO,IAAA,IAAoB,cAAAP,UAAA,CAAAO,IAAA;QAChEG,KAAA,EAAO,CAAAV,UAAA,IAAyB,gBAAAA,UAAA,CAAAW,SAAA;MAAA,CAClC;IAAA,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}