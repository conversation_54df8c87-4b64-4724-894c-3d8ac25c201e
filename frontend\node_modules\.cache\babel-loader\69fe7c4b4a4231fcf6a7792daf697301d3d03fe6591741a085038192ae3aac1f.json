{"ast": null, "code": "import { LEFT_CHECK_CHANGE_EVENT, RIGHT_CHECK_CHANGE_EVENT } from '../transfer.mjs';\nconst useCheckedChange = (checkedState, emit) => {\n  const onSourceCheckedChange = (val, movedKeys) => {\n    checkedState.leftChecked = val;\n    if (!movedKeys) return;\n    emit(LEFT_CHECK_CHANGE_EVENT, val, movedKeys);\n  };\n  const onTargetCheckedChange = (val, movedKeys) => {\n    checkedState.rightChecked = val;\n    if (!movedKeys) return;\n    emit(RIGHT_CHECK_CHANGE_EVENT, val, movedKeys);\n  };\n  return {\n    onSourceCheckedChange,\n    onTargetCheckedChange\n  };\n};\nexport { useCheckedChange };", "map": {"version": 3, "names": ["useCheckedChange", "checkedState", "emit", "onSourceCheckedChange", "val", "<PERSON><PERSON><PERSON><PERSON>", "leftChecked", "LEFT_CHECK_CHANGE_EVENT", "onTargetCheckedChange", "rightChecked", "RIGHT_CHECK_CHANGE_EVENT"], "sources": ["../../../../../../../packages/components/transfer/src/composables/use-checked-change.ts"], "sourcesContent": ["import { LEFT_CHECK_CHANGE_EVENT, RIGHT_CHECK_CHANGE_EVENT } from '../transfer'\n\nimport type { SetupContext } from 'vue'\nimport type {\n  TransferCheckedState,\n  TransferEmits,\n  TransferKey,\n} from '../transfer'\n\nexport const useCheckedChange = (\n  checkedState: TransferCheckedState,\n  emit: SetupContext<TransferEmits>['emit']\n) => {\n  const onSourceCheckedChange = (\n    val: TransferKey[],\n    movedKeys?: TransferKey[]\n  ) => {\n    checkedState.leftChecked = val\n    if (!movedKeys) return\n    emit(LEFT_CHECK_CHANGE_EVENT, val, movedKeys)\n  }\n\n  const onTargetCheckedChange = (\n    val: TransferKey[],\n    movedKeys?: TransferKey[]\n  ) => {\n    checkedState.rightChecked = val\n    if (!movedKeys) return\n    emit(RIGHT_CHECK_CHANGE_EVENT, val, movedKeys)\n  }\n\n  return {\n    onSourceCheckedChange,\n    onTargetCheckedChange,\n  }\n}\n"], "mappings": ";AACY,MAACA,gBAAgB,GAAGA,CAACC,YAAY,EAAEC,IAAI,KAAK;EACtD,MAAMC,qBAAqB,GAAGA,CAACC,GAAG,EAAEC,SAAS,KAAK;IAChDJ,YAAY,CAACK,WAAW,GAAGF,GAAG;IAC9B,IAAI,CAACC,SAAS,EACZ;IACFH,IAAI,CAACK,uBAAuB,EAAEH,GAAG,EAAEC,SAAS,CAAC;EACjD,CAAG;EACD,MAAMG,qBAAqB,GAAGA,CAACJ,GAAG,EAAEC,SAAS,KAAK;IAChDJ,YAAY,CAACQ,YAAY,GAAGL,GAAG;IAC/B,IAAI,CAACC,SAAS,EACZ;IACFH,IAAI,CAACQ,wBAAwB,EAAEN,GAAG,EAAEC,SAAS,CAAC;EAClD,CAAG;EACD,OAAO;IACLF,qBAAqB;IACrBK;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}