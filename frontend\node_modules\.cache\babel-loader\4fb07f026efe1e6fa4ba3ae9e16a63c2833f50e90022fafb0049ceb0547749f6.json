{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst watermarkProps = buildProps({\n  zIndex: {\n    type: Number,\n    default: 9\n  },\n  rotate: {\n    type: Number,\n    default: -22\n  },\n  width: Number,\n  height: Number,\n  image: String,\n  content: {\n    type: definePropType([String, Array]),\n    default: \"Element Plus\"\n  },\n  font: {\n    type: definePropType(Object)\n  },\n  gap: {\n    type: definePropType(Array),\n    default: () => [100, 100]\n  },\n  offset: {\n    type: definePropType(Array)\n  }\n});\nexport { watermarkProps };", "map": {"version": 3, "names": ["watermarkProps", "buildProps", "zIndex", "type", "Number", "default", "rotate", "width", "height", "image", "String", "content", "definePropType", "Array", "font", "Object", "gap", "offset"], "sources": ["../../../../../../packages/components/watermark/src/watermark.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Watermark from './watermark.vue'\n\nexport interface WatermarkFontType {\n  color?: string\n  fontSize?: number | string\n  fontWeight?: 'normal' | 'light' | 'weight' | number\n  fontStyle?: 'none' | 'normal' | 'italic' | 'oblique'\n  fontFamily?: string\n  textAlign?: 'start' | 'end' | 'left' | 'right' | 'center'\n  textBaseline?:\n    | 'top'\n    | 'hanging'\n    | 'middle'\n    | 'alphabetic'\n    | 'ideographic'\n    | 'bottom'\n}\n\nexport const watermarkProps = buildProps({\n  /**\n   * @description The z-index of the appended watermark element\n   */\n  zIndex: {\n    type: Number,\n    default: 9,\n  },\n  /**\n   * @description The rotation angle of the watermark\n   */\n  rotate: {\n    type: Number,\n    default: -22,\n  },\n  /**\n   * @description The width of the watermark\n   */\n  width: Number,\n  /**\n   * @description The height of the watermark\n   */\n  height: Number,\n  /**\n   * @description Image source, it is recommended to export 2x or 3x image, high priority (support base64 format)\n   */\n  image: String,\n  /**\n   * @description Watermark text content\n   */\n  content: {\n    type: definePropType<string | string[]>([String, Array]),\n    default: 'Element Plus',\n  },\n  /**\n   * @description Text style\n   */\n  font: {\n    type: definePropType<WatermarkFontType>(Object),\n  },\n  /**\n   * @description The spacing between watermarks\n   */\n  gap: {\n    type: definePropType<[number, number]>(Array),\n    default: () => [100, 100],\n  },\n  /**\n   * @description The offset of the watermark from the upper left corner of the container. The default is gap/2\n   */\n  offset: {\n    type: definePropType<[number, number]>(Array),\n  },\n} as const)\n\nexport type WatermarkProps = ExtractPropTypes<typeof watermarkProps>\nexport type WatermarkInstance = InstanceType<typeof Watermark> & unknown\n"], "mappings": ";AACY,MAACA,cAAc,GAAGC,UAAU,CAAC;EACvCC,MAAM,EAAE;IACNC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,MAAM,EAAE;IACNH,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE,CAAC;EACd,CAAG;EACDE,KAAK,EAAEH,MAAM;EACbI,MAAM,EAAEJ,MAAM;EACdK,KAAK,EAAEC,MAAM;EACbC,OAAO,EAAE;IACPR,IAAI,EAAES,cAAc,CAAC,CAACF,MAAM,EAAEG,KAAK,CAAC,CAAC;IACrCR,OAAO,EAAE;EACb,CAAG;EACDS,IAAI,EAAE;IACJX,IAAI,EAAES,cAAc,CAACG,MAAM;EAC/B,CAAG;EACDC,GAAG,EAAE;IACHb,IAAI,EAAES,cAAc,CAACC,KAAK,CAAC;IAC3BR,OAAO,EAAEA,CAAA,KAAM,CAAC,GAAG,EAAE,GAAG;EAC5B,CAAG;EACDY,MAAM,EAAE;IACNd,IAAI,EAAES,cAAc,CAACC,KAAK;EAC9B;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}