{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, getCurrentInstance, ref, computed, onBeforeMount, onMounted, onBeforeUnmount, Fragment, h } from 'vue';\nimport { ElCheckbox } from '../../../checkbox/index.mjs';\nimport { cellStarts } from '../config.mjs';\nimport { mergeOptions, compose } from '../util.mjs';\nimport useWatcher from './watcher-helper.mjs';\nimport useRender from './render-helper.mjs';\nimport defaultProps from './defaults.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nimport { isArray, isString } from '@vue/shared';\nlet columnIdSeed = 1;\nvar ElTableColumn = defineComponent({\n  name: \"ElTableColumn\",\n  components: {\n    ElCheckbox\n  },\n  props: defaultProps,\n  setup(props, {\n    slots\n  }) {\n    const instance = getCurrentInstance();\n    const columnConfig = ref({});\n    const owner = computed(() => {\n      let parent2 = instance.parent;\n      while (parent2 && !parent2.tableId) {\n        parent2 = parent2.parent;\n      }\n      return parent2;\n    });\n    const {\n      registerNormalWatchers,\n      registerComplexWatchers\n    } = useWatcher(owner, props);\n    const {\n      columnId,\n      isSubColumn,\n      realHeaderAlign,\n      columnOrTableParent,\n      setColumnWidth,\n      setColumnForcedProps,\n      setColumnRenders,\n      getPropsData,\n      getColumnElIndex,\n      realAlign,\n      updateColumnOrder\n    } = useRender(props, slots, owner);\n    const parent = columnOrTableParent.value;\n    columnId.value = `${parent.tableId || parent.columnId}_column_${columnIdSeed++}`;\n    onBeforeMount(() => {\n      isSubColumn.value = owner.value !== parent;\n      const type = props.type || \"default\";\n      const sortable = props.sortable === \"\" ? true : props.sortable;\n      const showOverflowTooltip = type === \"selection\" ? false : isUndefined(props.showOverflowTooltip) ? parent.props.showOverflowTooltip : props.showOverflowTooltip;\n      const tooltipFormatter = isUndefined(props.tooltipFormatter) ? parent.props.tooltipFormatter : props.tooltipFormatter;\n      const defaults = {\n        ...cellStarts[type],\n        id: columnId.value,\n        type,\n        property: props.prop || props.property,\n        align: realAlign,\n        headerAlign: realHeaderAlign,\n        showOverflowTooltip,\n        tooltipFormatter,\n        filterable: props.filters || props.filterMethod,\n        filteredValue: [],\n        filterPlacement: \"\",\n        filterClassName: \"\",\n        isColumnGroup: false,\n        isSubColumn: false,\n        filterOpened: false,\n        sortable,\n        index: props.index,\n        rawColumnKey: instance.vnode.key\n      };\n      const basicProps = [\"columnKey\", \"label\", \"className\", \"labelClassName\", \"type\", \"renderHeader\", \"formatter\", \"fixed\", \"resizable\"];\n      const sortProps = [\"sortMethod\", \"sortBy\", \"sortOrders\"];\n      const selectProps = [\"selectable\", \"reserveSelection\"];\n      const filterProps = [\"filterMethod\", \"filters\", \"filterMultiple\", \"filterOpened\", \"filteredValue\", \"filterPlacement\", \"filterClassName\"];\n      let column = getPropsData(basicProps, sortProps, selectProps, filterProps);\n      column = mergeOptions(defaults, column);\n      const chains = compose(setColumnRenders, setColumnWidth, setColumnForcedProps);\n      column = chains(column);\n      columnConfig.value = column;\n      registerNormalWatchers();\n      registerComplexWatchers();\n    });\n    onMounted(() => {\n      var _a;\n      const parent2 = columnOrTableParent.value;\n      const children = isSubColumn.value ? parent2.vnode.el.children : (_a = parent2.refs.hiddenColumns) == null ? void 0 : _a.children;\n      const getColumnIndex = () => getColumnElIndex(children || [], instance.vnode.el);\n      columnConfig.value.getColumnIndex = getColumnIndex;\n      const columnIndex = getColumnIndex();\n      columnIndex > -1 && owner.value.store.commit(\"insertColumn\", columnConfig.value, isSubColumn.value ? parent2.columnConfig.value : null, updateColumnOrder);\n    });\n    onBeforeUnmount(() => {\n      const getColumnIndex = columnConfig.value.getColumnIndex;\n      const columnIndex = getColumnIndex ? getColumnIndex() : -1;\n      columnIndex > -1 && owner.value.store.commit(\"removeColumn\", columnConfig.value, isSubColumn.value ? parent.columnConfig.value : null, updateColumnOrder);\n    });\n    instance.columnId = columnId.value;\n    instance.columnConfig = columnConfig;\n    return;\n  },\n  render() {\n    var _a, _b, _c;\n    try {\n      const renderDefault = (_b = (_a = this.$slots).default) == null ? void 0 : _b.call(_a, {\n        row: {},\n        column: {},\n        $index: -1\n      });\n      const children = [];\n      if (isArray(renderDefault)) {\n        for (const childNode of renderDefault) {\n          if (((_c = childNode.type) == null ? void 0 : _c.name) === \"ElTableColumn\" || childNode.shapeFlag & 2) {\n            children.push(childNode);\n          } else if (childNode.type === Fragment && isArray(childNode.children)) {\n            childNode.children.forEach(vnode2 => {\n              if ((vnode2 == null ? void 0 : vnode2.patchFlag) !== 1024 && !isString(vnode2 == null ? void 0 : vnode2.children)) {\n                children.push(vnode2);\n              }\n            });\n          }\n        }\n      }\n      const vnode = h(\"div\", children);\n      return vnode;\n    } catch (e) {\n      return h(\"div\", []);\n    }\n  }\n});\nexport { ElTableColumn as default };", "map": {"version": 3, "names": ["columnIdSeed", "ElTableColumn", "defineComponent", "name", "components", "ElCheckbox", "props", "defaultProps", "setup", "slots", "instance", "getCurrentInstance", "columnConfig", "ref", "owner", "computed", "parent2", "parent", "tableId", "registerNormalWatchers", "registerComplexWatchers", "useWatcher", "columnId", "isSubColumn", "realHeaderAlign", "columnOrTableParent", "setColumn<PERSON><PERSON><PERSON>", "setColumnForcedProps", "setColumnRenders", "getPropsData", "getColumnElIndex", "realAlign", "updateColumnOrder", "useRender", "value", "onBeforeMount", "type", "sortable", "showOverflowTooltip", "isUndefined", "tooltipFormatter", "defaults", "cellStarts", "id", "property", "prop", "align", "headerAlign", "filterable", "filters", "filterMethod", "filteredValue", "filterPlacement", "filterClassName", "isColumnGroup", "filterOpened", "index", "rawColumnKey", "vnode", "key", "basicProps", "sortProps", "selectProps", "filterProps", "column", "mergeOptions", "chains", "compose", "onMounted", "_a", "children", "el", "refs", "hiddenColumns", "getColumnIndex", "columnIndex", "store", "commit", "onBeforeUnmount", "render", "_b", "_c", "renderDefault", "$slots", "default", "call", "row", "$index", "isArray", "childNode", "shapeFlag", "push", "Fragment", "for<PERSON>ach", "vnode2", "patchFlag", "isString", "h", "e"], "sources": ["../../../../../../../packages/components/table/src/table-column/index.ts"], "sourcesContent": ["// @ts-nocheck\nimport {\n  Fragment,\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  h,\n  onBeforeMount,\n  onBeforeUnmount,\n  onMounted,\n  ref,\n} from 'vue'\nimport ElCheckbox from '@element-plus/components/checkbox'\nimport { isArray, isString, isUndefined } from '@element-plus/utils'\nimport { cellStarts } from '../config'\nimport { compose, mergeOptions } from '../util'\nimport useWatcher from './watcher-helper'\nimport useRender from './render-helper'\nimport defaultProps from './defaults'\nimport type { TableColumn, TableColumnCtx } from './defaults'\n\nimport type { DefaultRow } from '../table/defaults'\n\nlet columnIdSeed = 1\n\nexport default defineComponent({\n  name: 'ElTableColumn',\n  components: {\n    ElCheckbox,\n  },\n  props: defaultProps,\n  setup(props, { slots }) {\n    const instance = getCurrentInstance() as TableColumn<DefaultRow>\n    const columnConfig = ref<Partial<TableColumnCtx<DefaultRow>>>({})\n    const owner = computed(() => {\n      let parent = instance.parent as any\n      while (parent && !parent.tableId) {\n        parent = parent.parent\n      }\n      return parent\n    })\n\n    const { registerNormalWatchers, registerComplexWatchers } = useWatcher(\n      owner,\n      props\n    )\n    const {\n      columnId,\n      isSubColumn,\n      realHeaderAlign,\n      columnOrTableParent,\n      setColumnWidth,\n      setColumnForcedProps,\n      setColumnRenders,\n      getPropsData,\n      getColumnElIndex,\n      realAlign,\n      updateColumnOrder,\n    } = useRender(props as unknown as TableColumnCtx<unknown>, slots, owner)\n\n    const parent = columnOrTableParent.value\n    columnId.value = `${\n      parent.tableId || parent.columnId\n    }_column_${columnIdSeed++}`\n    onBeforeMount(() => {\n      isSubColumn.value = owner.value !== parent\n\n      const type = props.type || 'default'\n      const sortable = props.sortable === '' ? true : props.sortable\n      //The selection column should not be affected by `showOverflowTooltip`.\n      const showOverflowTooltip =\n        type === 'selection'\n          ? false\n          : isUndefined(props.showOverflowTooltip)\n          ? parent.props.showOverflowTooltip\n          : props.showOverflowTooltip\n      const tooltipFormatter = isUndefined(props.tooltipFormatter)\n        ? parent.props.tooltipFormatter\n        : props.tooltipFormatter\n      const defaults = {\n        ...cellStarts[type],\n        id: columnId.value,\n        type,\n        property: props.prop || props.property,\n        align: realAlign,\n        headerAlign: realHeaderAlign,\n        showOverflowTooltip,\n        tooltipFormatter,\n        // filter 相关属性\n        filterable: props.filters || props.filterMethod,\n        filteredValue: [],\n        filterPlacement: '',\n        filterClassName: '',\n        isColumnGroup: false,\n        isSubColumn: false,\n        filterOpened: false,\n        // sort 相关属性\n        sortable,\n        // index 列\n        index: props.index,\n        // <el-table-column key=\"xxx\" />\n        rawColumnKey: instance.vnode.key,\n      }\n\n      const basicProps = [\n        'columnKey',\n        'label',\n        'className',\n        'labelClassName',\n        'type',\n        'renderHeader',\n        'formatter',\n        'fixed',\n        'resizable',\n      ]\n      const sortProps = ['sortMethod', 'sortBy', 'sortOrders']\n      const selectProps = ['selectable', 'reserveSelection']\n      const filterProps = [\n        'filterMethod',\n        'filters',\n        'filterMultiple',\n        'filterOpened',\n        'filteredValue',\n        'filterPlacement',\n        'filterClassName',\n      ]\n\n      let column = getPropsData(basicProps, sortProps, selectProps, filterProps)\n\n      column = mergeOptions(defaults, column)\n      // 注意 compose 中函数执行的顺序是从右到左\n      const chains = compose(\n        setColumnRenders,\n        setColumnWidth,\n        setColumnForcedProps\n      )\n      column = chains(column)\n      columnConfig.value = column\n\n      // 注册 watcher\n      registerNormalWatchers()\n      registerComplexWatchers()\n    })\n    onMounted(() => {\n      const parent = columnOrTableParent.value\n      const children = isSubColumn.value\n        ? parent.vnode.el.children\n        : parent.refs.hiddenColumns?.children\n      const getColumnIndex = () =>\n        getColumnElIndex(children || [], instance.vnode.el)\n      columnConfig.value.getColumnIndex = getColumnIndex\n      const columnIndex = getColumnIndex()\n      columnIndex > -1 &&\n        owner.value.store.commit(\n          'insertColumn',\n          columnConfig.value,\n          isSubColumn.value ? parent.columnConfig.value : null,\n          updateColumnOrder\n        )\n    })\n    onBeforeUnmount(() => {\n      const getColumnIndex = columnConfig.value.getColumnIndex\n      const columnIndex = getColumnIndex ? getColumnIndex() : -1\n      columnIndex > -1 &&\n        owner.value.store.commit(\n          'removeColumn',\n          columnConfig.value,\n          isSubColumn.value ? parent.columnConfig.value : null,\n          updateColumnOrder\n        )\n    })\n    instance.columnId = columnId.value\n\n    instance.columnConfig = columnConfig\n    return\n  },\n  render() {\n    try {\n      const renderDefault = this.$slots.default?.({\n        row: {},\n        column: {},\n        $index: -1,\n      })\n      const children = []\n      if (isArray(renderDefault)) {\n        for (const childNode of renderDefault) {\n          if (\n            childNode.type?.name === 'ElTableColumn' ||\n            childNode.shapeFlag & 2\n          ) {\n            children.push(childNode)\n          } else if (\n            childNode.type === Fragment &&\n            isArray(childNode.children)\n          ) {\n            childNode.children.forEach((vnode) => {\n              // No rendering when vnode is dynamic slot or text\n              if (vnode?.patchFlag !== 1024 && !isString(vnode?.children)) {\n                children.push(vnode)\n              }\n            })\n          }\n        }\n      }\n      const vnode = h('div', children)\n      return vnode\n    } catch {\n      return h('div', [])\n    }\n  },\n})\n"], "mappings": ";;;;;;;;;;;;AAkBA,IAAIA,YAAY,GAAG,CAAC;AACpB,IAAAC,aAAA,GAAeC,eAAe,CAAC;EAC7BC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IACVC;EACJ,CAAG;EACDC,KAAK,EAAEC,YAAY;EACnBC,KAAKA,CAACF,KAAK,EAAE;IAAEG;EAAK,CAAE,EAAE;IACtB,MAAMC,QAAQ,GAAGC,kBAAkB,EAAE;IACrC,MAAMC,YAAY,GAAGC,GAAG,CAAC,EAAE,CAAC;IAC5B,MAAMC,KAAK,GAAGC,QAAQ,CAAC,MAAM;MAC3B,IAAIC,OAAO,GAAGN,QAAQ,CAACO,MAAM;MAC7B,OAAOD,OAAO,IAAI,CAACA,OAAO,CAACE,OAAO,EAAE;QAClCF,OAAO,GAAGA,OAAO,CAACC,MAAM;MAChC;MACM,OAAOD,OAAO;IACpB,CAAK,CAAC;IACF,MAAM;MAAEG,sBAAsB;MAAEC;IAAuB,CAAE,GAAGC,UAAU,CAACP,KAAK,EAAER,KAAK,CAAC;IACpF,MAAM;MACJgB,QAAQ;MACRC,WAAW;MACXC,eAAe;MACfC,mBAAmB;MACnBC,cAAc;MACdC,oBAAoB;MACpBC,gBAAgB;MAChBC,YAAY;MACZC,gBAAgB;MAChBC,SAAS;MACTC;IACN,CAAK,GAAGC,SAAS,CAAC3B,KAAK,EAAEG,KAAK,EAAEK,KAAK,CAAC;IAClC,MAAMG,MAAM,GAAGQ,mBAAmB,CAACS,KAAK;IACxCZ,QAAQ,CAACY,KAAK,GAAG,GAAGjB,MAAM,CAACC,OAAO,IAAID,MAAM,CAACK,QAAQ,WAAWtB,YAAY,EAAE,EAAE;IAChFmC,aAAa,CAAC,MAAM;MAClBZ,WAAW,CAACW,KAAK,GAAGpB,KAAK,CAACoB,KAAK,KAAKjB,MAAM;MAC1C,MAAMmB,IAAI,GAAG9B,KAAK,CAAC8B,IAAI,IAAI,SAAS;MACpC,MAAMC,QAAQ,GAAG/B,KAAK,CAAC+B,QAAQ,KAAK,EAAE,GAAG,IAAI,GAAG/B,KAAK,CAAC+B,QAAQ;MAC9D,MAAMC,mBAAmB,GAAGF,IAAI,KAAK,WAAW,GAAG,KAAK,GAAGG,WAAW,CAACjC,KAAK,CAACgC,mBAAmB,CAAC,GAAGrB,MAAM,CAACX,KAAK,CAACgC,mBAAmB,GAAGhC,KAAK,CAACgC,mBAAmB;MAChK,MAAME,gBAAgB,GAAGD,WAAW,CAACjC,KAAK,CAACkC,gBAAgB,CAAC,GAAGvB,MAAM,CAACX,KAAK,CAACkC,gBAAgB,GAAGlC,KAAK,CAACkC,gBAAgB;MACrH,MAAMC,QAAQ,GAAG;QACf,GAAGC,UAAU,CAACN,IAAI,CAAC;QACnBO,EAAE,EAAErB,QAAQ,CAACY,KAAK;QAClBE,IAAI;QACJQ,QAAQ,EAAEtC,KAAK,CAACuC,IAAI,IAAIvC,KAAK,CAACsC,QAAQ;QACtCE,KAAK,EAAEf,SAAS;QAChBgB,WAAW,EAAEvB,eAAe;QAC5Bc,mBAAmB;QACnBE,gBAAgB;QAChBQ,UAAU,EAAE1C,KAAK,CAAC2C,OAAO,IAAI3C,KAAK,CAAC4C,YAAY;QAC/CC,aAAa,EAAE,EAAE;QACjBC,eAAe,EAAE,EAAE;QACnBC,eAAe,EAAE,EAAE;QACnBC,aAAa,EAAE,KAAK;QACpB/B,WAAW,EAAE,KAAK;QAClBgC,YAAY,EAAE,KAAK;QACnBlB,QAAQ;QACRmB,KAAK,EAAElD,KAAK,CAACkD,KAAK;QAClBC,YAAY,EAAE/C,QAAQ,CAACgD,KAAK,CAACC;MACrC,CAAO;MACD,MAAMC,UAAU,GAAG,CACjB,WAAW,EACX,OAAO,EACP,WAAW,EACX,gBAAgB,EAChB,MAAM,EACN,cAAc,EACd,WAAW,EACX,OAAO,EACP,WAAW,CACZ;MACD,MAAMC,SAAS,GAAG,CAAC,YAAY,EAAE,QAAQ,EAAE,YAAY,CAAC;MACxD,MAAMC,WAAW,GAAG,CAAC,YAAY,EAAE,kBAAkB,CAAC;MACtD,MAAMC,WAAW,GAAG,CAClB,cAAc,EACd,SAAS,EACT,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,iBAAiB,EACjB,iBAAiB,CAClB;MACD,IAAIC,MAAM,GAAGnC,YAAY,CAAC+B,UAAU,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,CAAC;MAC1EC,MAAM,GAAGC,YAAY,CAACxB,QAAQ,EAAEuB,MAAM,CAAC;MACvC,MAAME,MAAM,GAAGC,OAAO,CAACvC,gBAAgB,EAAEF,cAAc,EAAEC,oBAAoB,CAAC;MAC9EqC,MAAM,GAAGE,MAAM,CAACF,MAAM,CAAC;MACvBpD,YAAY,CAACsB,KAAK,GAAG8B,MAAM;MAC3B7C,sBAAsB,EAAE;MACxBC,uBAAuB,EAAE;IAC/B,CAAK,CAAC;IACFgD,SAAS,CAAC,MAAM;MACd,IAAIC,EAAE;MACN,MAAMrD,OAAO,GAAGS,mBAAmB,CAACS,KAAK;MACzC,MAAMoC,QAAQ,GAAG/C,WAAW,CAACW,KAAK,GAAGlB,OAAO,CAAC0C,KAAK,CAACa,EAAE,CAACD,QAAQ,GAAG,CAACD,EAAE,GAAGrD,OAAO,CAACwD,IAAI,CAACC,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,EAAE,CAACC,QAAQ;MACjI,MAAMI,cAAc,GAAGA,CAAA,KAAM5C,gBAAgB,CAACwC,QAAQ,IAAI,EAAE,EAAE5D,QAAQ,CAACgD,KAAK,CAACa,EAAE,CAAC;MAChF3D,YAAY,CAACsB,KAAK,CAACwC,cAAc,GAAGA,cAAc;MAClD,MAAMC,WAAW,GAAGD,cAAc,EAAE;MACpCC,WAAW,GAAG,CAAC,CAAC,IAAI7D,KAAK,CAACoB,KAAK,CAAC0C,KAAK,CAACC,MAAM,CAAC,cAAc,EAAEjE,YAAY,CAACsB,KAAK,EAAEX,WAAW,CAACW,KAAK,GAAGlB,OAAO,CAACJ,YAAY,CAACsB,KAAK,GAAG,IAAI,EAAEF,iBAAiB,CAAC;IAChK,CAAK,CAAC;IACF8C,eAAe,CAAC,MAAM;MACpB,MAAMJ,cAAc,GAAG9D,YAAY,CAACsB,KAAK,CAACwC,cAAc;MACxD,MAAMC,WAAW,GAAGD,cAAc,GAAGA,cAAc,EAAE,GAAG,CAAC,CAAC;MAC1DC,WAAW,GAAG,CAAC,CAAC,IAAI7D,KAAK,CAACoB,KAAK,CAAC0C,KAAK,CAACC,MAAM,CAAC,cAAc,EAAEjE,YAAY,CAACsB,KAAK,EAAEX,WAAW,CAACW,KAAK,GAAGjB,MAAM,CAACL,YAAY,CAACsB,KAAK,GAAG,IAAI,EAAEF,iBAAiB,CAAC;IAC/J,CAAK,CAAC;IACFtB,QAAQ,CAACY,QAAQ,GAAGA,QAAQ,CAACY,KAAK;IAClCxB,QAAQ,CAACE,YAAY,GAAGA,YAAY;IACpC;EACJ,CAAG;EACDmE,MAAMA,CAAA,EAAG;IACP,IAAIV,EAAE,EAAEW,EAAE,EAAEC,EAAE;IACd,IAAI;MACF,MAAMC,aAAa,GAAG,CAACF,EAAE,GAAG,CAACX,EAAE,GAAG,IAAI,CAACc,MAAM,EAAEC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,EAAE,CAACK,IAAI,CAAChB,EAAE,EAAE;QACrFiB,GAAG,EAAE,EAAE;QACPtB,MAAM,EAAE,EAAE;QACVuB,MAAM,EAAE,CAAC;MACjB,CAAO,CAAC;MACF,MAAMjB,QAAQ,GAAG,EAAE;MACnB,IAAIkB,OAAO,CAACN,aAAa,CAAC,EAAE;QAC1B,KAAK,MAAMO,SAAS,IAAIP,aAAa,EAAE;UACrC,IAAI,CAAC,CAACD,EAAE,GAAGQ,SAAS,CAACrD,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6C,EAAE,CAAC9E,IAAI,MAAM,eAAe,IAAIsF,SAAS,CAACC,SAAS,GAAG,CAAC,EAAE;YACrGpB,QAAQ,CAACqB,IAAI,CAACF,SAAS,CAAC;UACpC,CAAW,MAAM,IAAIA,SAAS,CAACrD,IAAI,KAAKwD,QAAQ,IAAIJ,OAAO,CAACC,SAAS,CAACnB,QAAQ,CAAC,EAAE;YACrEmB,SAAS,CAACnB,QAAQ,CAACuB,OAAO,CAAEC,MAAM,IAAK;cACrC,IAAI,CAACA,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,SAAS,MAAM,IAAI,IAAI,CAACC,QAAQ,CAACF,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACxB,QAAQ,CAAC,EAAE;gBACjHA,QAAQ,CAACqB,IAAI,CAACG,MAAM,CAAC;cACrC;YACA,CAAa,CAAC;UACd;QACA;MACA;MACM,MAAMpC,KAAK,GAAGuC,CAAC,CAAC,KAAK,EAAE3B,QAAQ,CAAC;MAChC,OAAOZ,KAAK;IAClB,CAAK,CAAC,OAAOwC,CAAC,EAAE;MACV,OAAOD,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC;IACzB;EACA;AACA,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}