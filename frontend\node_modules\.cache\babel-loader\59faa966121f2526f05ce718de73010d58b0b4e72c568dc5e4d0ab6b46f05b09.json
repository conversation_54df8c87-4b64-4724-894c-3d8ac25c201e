{"ast": null, "code": "import Form from './src/form2.mjs';\nimport FormItem from './src/form-item.mjs';\nexport { formEmits, formMetaProps, formProps } from './src/form.mjs';\nexport { formItemProps, formItemValidateStates } from './src/form-item2.mjs';\nexport { formContextKey, formItemContextKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nexport { useDisabled, useFormDisabled, useFormSize, useSize } from './src/hooks/use-form-common-props.mjs';\nexport { useFormItem, useFormItemInputId } from './src/hooks/use-form-item.mjs';\nconst ElForm = withInstall(Form, {\n  FormItem\n});\nconst ElFormItem = withNoopInstall(FormItem);\nexport { ElForm, ElFormItem, ElForm as default };", "map": {"version": 3, "names": ["ElForm", "withInstall", "Form", "FormItem", "ElFormItem", "withNoopInstall"], "sources": ["../../../../../packages/components/form/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON>oopInstall } from '@element-plus/utils'\nimport Form from './src/form.vue'\nimport FormItem from './src/form-item.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElForm: SFCWithInstall<typeof Form> & {\n  FormItem: typeof FormItem\n} = withInstall(Form, {\n  FormItem,\n})\nexport default ElForm\nexport const ElFormItem: SFCWithInstall<typeof FormItem> =\n  withNoopInstall(FormItem)\n\nexport * from './src/form'\nexport * from './src/form-item'\nexport * from './src/types'\nexport * from './src/constants'\nexport * from './src/hooks'\n\nexport type FormInstance = InstanceType<typeof Form> & unknown\nexport type FormItemInstance = InstanceType<typeof FormItem> & unknown\n"], "mappings": ";;;;;;;;AAGY,MAACA,MAAM,GAAGC,WAAW,CAACC,IAAI,EAAE;EACtCC;AACF,CAAC;AAEW,MAACC,UAAU,GAAGC,eAAe,CAACF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}