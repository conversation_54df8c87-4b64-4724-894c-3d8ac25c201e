{"ast": null, "code": "import InputTag from './src/input-tag2.mjs';\nexport { inputTagEmits, inputTagProps } from './src/input-tag.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElInputTag = withInstall(InputTag);\nexport { ElInputTag, ElInputTag as default };", "map": {"version": 3, "names": ["ElInputTag", "withInstall", "InputTag"], "sources": ["../../../../../packages/components/input-tag/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport InputTag from './src/input-tag.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElInputTag: SFCWithInstall<typeof InputTag> = withInstall(InputTag)\nexport default ElInputTag\n\nexport * from './src/input-tag'\nexport type { InputTagInstance } from './src/instance'\n"], "mappings": ";;;AAEY,MAACA,UAAU,GAAGC,WAAW,CAACC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}