{"ast": null, "code": "import { CircleCheckFilled, WarningFilled, CircleCloseFilled, InfoFilled } from '@element-plus/icons-vue';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst IconMap = {\n  success: \"icon-success\",\n  warning: \"icon-warning\",\n  error: \"icon-error\",\n  info: \"icon-info\"\n};\nconst IconComponentMap = {\n  [IconMap.success]: CircleCheckFilled,\n  [IconMap.warning]: WarningFilled,\n  [IconMap.error]: CircleCloseFilled,\n  [IconMap.info]: InfoFilled\n};\nconst resultProps = buildProps({\n  title: {\n    type: String,\n    default: \"\"\n  },\n  subTitle: {\n    type: String,\n    default: \"\"\n  },\n  icon: {\n    type: String,\n    values: [\"success\", \"warning\", \"info\", \"error\"],\n    default: \"info\"\n  }\n});\nexport { IconComponentMap, IconMap, resultProps };", "map": {"version": 3, "names": ["IconMap", "success", "warning", "error", "info", "IconComponentMap", "CircleCheckFilled", "WarningFilled", "CircleCloseFilled", "InfoFilled", "resultProps", "buildProps", "title", "type", "String", "default", "subTitle", "icon", "values"], "sources": ["../../../../../../packages/components/result/src/result.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport {\n  CircleCheckFilled,\n  CircleCloseFilled,\n  InfoFilled,\n  WarningFilled,\n} from '@element-plus/icons-vue'\nimport type { Component, ExtractPropTypes } from 'vue'\nimport type Result from './result.vue'\n\nexport const IconMap = {\n  success: 'icon-success',\n  warning: 'icon-warning',\n  error: 'icon-error',\n  info: 'icon-info',\n} as const\n\nexport const IconComponentMap: Record<\n  typeof IconMap[keyof typeof IconMap],\n  Component\n> = {\n  [IconMap.success]: CircleCheckFilled,\n  [IconMap.warning]: WarningFilled,\n  [IconMap.error]: CircleCloseFilled,\n  [IconMap.info]: InfoFilled,\n}\n\nexport const resultProps = buildProps({\n  /**\n   * @description title of result\n   */\n  title: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description sub title of result\n   */\n  subTitle: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description icon type of result\n   */\n  icon: {\n    type: String,\n    values: ['success', 'warning', 'info', 'error'],\n    default: 'info',\n  },\n} as const)\n\nexport type ResultProps = ExtractPropTypes<typeof resultProps>\n\nexport type ResultInstance = InstanceType<typeof Result> & unknown\n"], "mappings": ";;AAOY,MAACA,OAAO,GAAG;EACrBC,OAAO,EAAE,cAAc;EACvBC,OAAO,EAAE,cAAc;EACvBC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAE;AACR;AACY,MAACC,gBAAgB,GAAG;EAC9B,CAACL,OAAO,CAACC,OAAO,GAAGK,iBAAiB;EACpC,CAACN,OAAO,CAACE,OAAO,GAAGK,aAAa;EAChC,CAACP,OAAO,CAACG,KAAK,GAAGK,iBAAiB;EAClC,CAACR,OAAO,CAACI,IAAI,GAAGK;AAClB;AACY,MAACC,WAAW,GAAGC,UAAU,CAAC;EACpCC,KAAK,EAAE;IACLC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,QAAQ,EAAE;IACRH,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDE,IAAI,EAAE;IACJJ,IAAI,EAAEC,MAAM;IACZI,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;IAC/CH,OAAO,EAAE;EACb;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}