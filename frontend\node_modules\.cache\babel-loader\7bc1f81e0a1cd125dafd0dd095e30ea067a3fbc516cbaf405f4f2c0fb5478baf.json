{"ast": null, "code": "import { shallowReactive } from 'vue';\nconst instances = shallowReactive([]);\nconst getInstance = id => {\n  const idx = instances.findIndex(instance => instance.id === id);\n  const current = instances[idx];\n  let prev;\n  if (idx > 0) {\n    prev = instances[idx - 1];\n  }\n  return {\n    current,\n    prev\n  };\n};\nconst getLastOffset = id => {\n  const {\n    prev\n  } = getInstance(id);\n  if (!prev) return 0;\n  return prev.vm.exposed.bottom.value;\n};\nconst getOffsetOrSpace = (id, offset) => {\n  const idx = instances.findIndex(instance => instance.id === id);\n  return idx > 0 ? 16 : offset;\n};\nexport { getInstance, getLastOffset, getOffsetOrSpace, instances };", "map": {"version": 3, "names": ["instances", "shallowReactive", "getInstance", "id", "idx", "findIndex", "instance", "current", "prev", "getLastOffset", "vm", "exposed", "bottom", "value", "getOffsetOrSpace", "offset"], "sources": ["../../../../../../packages/components/message/src/instance.ts"], "sourcesContent": ["import { shallowReactive } from 'vue'\nimport type { ComponentInternalInstance, VNode } from 'vue'\nimport type { Mutable } from '@element-plus/utils'\nimport type { MessageHandler, MessageProps } from './message'\n\nexport type MessageContext = {\n  id: string\n  vnode: VNode\n  handler: MessageHandler\n  vm: ComponentInternalInstance\n  props: Mutable<MessageProps>\n}\n\nexport const instances: MessageContext[] = shallowReactive([])\n\nexport const getInstance = (id: string) => {\n  const idx = instances.findIndex((instance) => instance.id === id)\n  const current = instances[idx]\n  let prev: MessageContext | undefined\n  if (idx > 0) {\n    prev = instances[idx - 1]\n  }\n  return { current, prev }\n}\n\nexport const getLastOffset = (id: string): number => {\n  const { prev } = getInstance(id)\n  if (!prev) return 0\n  return prev.vm.exposed!.bottom.value\n}\n\nexport const getOffsetOrSpace = (id: string, offset: number) => {\n  const idx = instances.findIndex((instance) => instance.id === id)\n  return idx > 0 ? 16 : offset\n}\n"], "mappings": ";AACY,MAACA,SAAS,GAAGC,eAAe,CAAC,EAAE;AAC/B,MAACC,WAAW,GAAIC,EAAE,IAAK;EACjC,MAAMC,GAAG,GAAGJ,SAAS,CAACK,SAAS,CAAEC,QAAQ,IAAKA,QAAQ,CAACH,EAAE,KAAKA,EAAE,CAAC;EACjE,MAAMI,OAAO,GAAGP,SAAS,CAACI,GAAG,CAAC;EAC9B,IAAII,IAAI;EACR,IAAIJ,GAAG,GAAG,CAAC,EAAE;IACXI,IAAI,GAAGR,SAAS,CAACI,GAAG,GAAG,CAAC,CAAC;EAC7B;EACE,OAAO;IAAEG,OAAO;IAAEC;EAAI,CAAE;AAC1B;AACY,MAACC,aAAa,GAAIN,EAAE,IAAK;EACnC,MAAM;IAAEK;EAAI,CAAE,GAAGN,WAAW,CAACC,EAAE,CAAC;EAChC,IAAI,CAACK,IAAI,EACP,OAAO,CAAC;EACV,OAAOA,IAAI,CAACE,EAAE,CAACC,OAAO,CAACC,MAAM,CAACC,KAAK;AACrC;AACY,MAACC,gBAAgB,GAAGA,CAACX,EAAE,EAAEY,MAAM,KAAK;EAC9C,MAAMX,GAAG,GAAGJ,SAAS,CAACK,SAAS,CAAEC,QAAQ,IAAKA,QAAQ,CAACH,EAAE,KAAKA,EAAE,CAAC;EACjE,OAAOC,GAAG,GAAG,CAAC,GAAG,EAAE,GAAGW,MAAM;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}