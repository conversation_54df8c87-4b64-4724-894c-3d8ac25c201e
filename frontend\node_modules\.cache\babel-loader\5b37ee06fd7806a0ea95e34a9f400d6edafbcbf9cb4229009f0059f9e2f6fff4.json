{"ast": null, "code": "import { NOOP } from '@vue/shared';\nconst useSameTarget = handleClick => {\n  if (!handleClick) {\n    return {\n      onClick: NOOP,\n      onMousedown: NOOP,\n      onMouseup: NOOP\n    };\n  }\n  let mousedownTarget = false;\n  let mouseupTarget = false;\n  const onClick = e => {\n    if (mousedownTarget && mouseupTarget) {\n      handleClick(e);\n    }\n    mousedownTarget = mouseupTarget = false;\n  };\n  const onMousedown = e => {\n    mousedownTarget = e.target === e.currentTarget;\n  };\n  const onMouseup = e => {\n    mouseupTarget = e.target === e.currentTarget;\n  };\n  return {\n    onClick,\n    onMousedown,\n    onMouseup\n  };\n};\nexport { useSameTarget };", "map": {"version": 3, "names": ["useSameTarget", "handleClick", "onClick", "NOOP", "onMousedown", "onMouseup", "mousedownTarget", "mouseupTarget", "e", "target", "currentTarget"], "sources": ["../../../../../packages/hooks/use-same-target/index.ts"], "sourcesContent": ["import { NOOP } from '@element-plus/utils'\n\nexport const useSameTarget = (handleClick?: (e: MouseEvent) => void) => {\n  if (!handleClick) {\n    return { onClick: NOOP, onMousedown: NOOP, onMouseup: NOOP }\n  }\n\n  let mousedownTarget = false\n  let mouseupTarget = false\n  // refer to this https://javascript.info/mouse-events-basics\n  // events fired in the order: mousedown -> mouseup -> click\n  // we need to set the mousedown handle to false after click fired.\n  const onClick = (e: MouseEvent) => {\n    // if and only if\n    if (mousedownTarget && mouseupTarget) {\n      handleClick(e)\n    }\n    mousedownTarget = mouseupTarget = false\n  }\n\n  const onMousedown = (e: MouseEvent) => {\n    // marking current mousedown target.\n    mousedownTarget = e.target === e.currentTarget\n  }\n  const onMouseup = (e: MouseEvent) => {\n    // marking current mouseup target.\n    mouseupTarget = e.target === e.currentTarget\n  }\n\n  return { onClick, onMousedown, onMouseup }\n}\n"], "mappings": ";AACY,MAACA,aAAa,GAAIC,WAAW,IAAK;EAC5C,IAAI,CAACA,WAAW,EAAE;IAChB,OAAO;MAAEC,OAAO,EAAEC,IAAI;MAAEC,WAAW,EAAED,IAAI;MAAEE,SAAS,EAAEF;IAAI,CAAE;EAChE;EACE,IAAIG,eAAe,GAAG,KAAK;EAC3B,IAAIC,aAAa,GAAG,KAAK;EACzB,MAAML,OAAO,GAAIM,CAAC,IAAK;IACrB,IAAIF,eAAe,IAAIC,aAAa,EAAE;MACpCN,WAAW,CAACO,CAAC,CAAC;IACpB;IACIF,eAAe,GAAGC,aAAa,GAAG,KAAK;EAC3C,CAAG;EACD,MAAMH,WAAW,GAAII,CAAC,IAAK;IACzBF,eAAe,GAAGE,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa;EAClD,CAAG;EACD,MAAML,SAAS,GAAIG,CAAC,IAAK;IACvBD,aAAa,GAAGC,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa;EAChD,CAAG;EACD,OAAO;IAAER,OAAO;IAAEE,WAAW;IAAEC;EAAS,CAAE;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}