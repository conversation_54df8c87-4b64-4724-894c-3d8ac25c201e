{"ast": null, "code": "import Dialog from './src/dialog2.mjs';\nexport { useDialog } from './src/use-dialog.mjs';\nexport { dialogEmits, dialogProps } from './src/dialog.mjs';\nexport { dialogInjectionKey } from './src/constants.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElDialog = withInstall(Dialog);\nexport { ElDialog, ElDialog as default };", "map": {"version": 3, "names": ["ElDialog", "withInstall", "Dialog"], "sources": ["../../../../../packages/components/dialog/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Dialog from './src/dialog.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElDialog: SFCWithInstall<typeof Dialog> = withInstall(Dialog)\nexport default ElDialog\n\nexport * from './src/use-dialog'\nexport * from './src/dialog'\nexport * from './src/constants'\n"], "mappings": ";;;;;AAEY,MAACA,QAAQ,GAAGC,WAAW,CAACC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}