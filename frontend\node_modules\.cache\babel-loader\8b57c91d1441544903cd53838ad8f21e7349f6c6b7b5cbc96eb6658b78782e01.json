{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_dayOfYear = t();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, t, n) {\n    t.prototype.dayOfYear = function (e) {\n      var t = Math.round((n(this).startOf(\"day\") - n(this).startOf(\"year\")) / 864e5) + 1;\n      return null == e ? t : this.add(e - t, \"day\");\n    };\n  };\n});", "map": {"version": 3, "names": ["e", "t", "exports", "module", "define", "amd", "globalThis", "self", "dayjs_plugin_dayOfYear", "n", "prototype", "dayOfYear", "Math", "round", "startOf", "add"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/dayjs/plugin/dayOfYear.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_dayOfYear=t()}(this,(function(){\"use strict\";return function(e,t,n){t.prototype.dayOfYear=function(e){var t=Math.round((n(this).startOf(\"day\")-n(this).startOf(\"year\"))/864e5)+1;return null==e?t:this.add(e-t,\"day\")}}}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAACH,CAAC,CAAC,GAAC,CAACD,CAAC,GAAC,WAAW,IAAE,OAAOM,UAAU,GAACA,UAAU,GAACN,CAAC,IAAEO,IAAI,EAAEC,sBAAsB,GAACP,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC,IAAI,EAAE,YAAU;EAAC,YAAY;;EAAC,OAAO,UAASD,CAAC,EAACC,CAAC,EAACQ,CAAC,EAAC;IAACR,CAAC,CAACS,SAAS,CAACC,SAAS,GAAC,UAASX,CAAC,EAAC;MAAC,IAAIC,CAAC,GAACW,IAAI,CAACC,KAAK,CAAC,CAACJ,CAAC,CAAC,IAAI,CAAC,CAACK,OAAO,CAAC,KAAK,CAAC,GAACL,CAAC,CAAC,IAAI,CAAC,CAACK,OAAO,CAAC,MAAM,CAAC,IAAE,KAAK,CAAC,GAAC,CAAC;MAAC,OAAO,IAAI,IAAEd,CAAC,GAACC,CAAC,GAAC,IAAI,CAACc,GAAG,CAACf,CAAC,GAACC,CAAC,EAAC,KAAK,CAAC;IAAA,CAAC;EAAA,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}