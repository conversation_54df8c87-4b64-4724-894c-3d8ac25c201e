{"ast": null, "code": "import \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nexport { castArray as ensureArray } from 'lodash-unified';\nimport { isArray } from '@vue/shared';\nconst unique = arr => [...new Set(arr)];\nconst castArray = arr => {\n  if (!arr && arr !== 0) return [];\n  return isArray(arr) ? arr : [arr];\n};\nexport { castArray, unique };", "map": {"version": 3, "names": ["unique", "arr", "Set", "<PERSON><PERSON><PERSON><PERSON>", "isArray"], "sources": ["../../../../packages/utils/arrays.ts"], "sourcesContent": ["import { isArray } from './types'\n\nexport const unique = <T>(arr: T[]) => [...new Set(arr)]\n\ntype Many<T> = T | ReadonlyArray<T>\n// TODO: rename to `ensureArray`\n/** like `_.castArray`, except falsy value returns empty array. */\nexport const castArray = <T>(arr: Many<T>): T[] => {\n  if (!arr && (arr as any) !== 0) return []\n  return isArray(arr) ? arr : [arr as T]\n}\n\n// TODO: remove import alias\n// avoid naming conflicts\nexport { castArray as ensureArray } from 'lodash-unified'\n"], "mappings": ";;;;;;;;;AACY,MAACA,MAAM,GAAIC,GAAG,IAAK,CAAC,GAAG,IAAIC,GAAG,CAACD,GAAG,CAAC;AACnC,MAACE,SAAS,GAAIF,GAAG,IAAK;EAChC,IAAI,CAACA,GAAG,IAAIA,GAAG,KAAK,CAAC,EACnB,OAAO,EAAE;EACX,OAAOG,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}