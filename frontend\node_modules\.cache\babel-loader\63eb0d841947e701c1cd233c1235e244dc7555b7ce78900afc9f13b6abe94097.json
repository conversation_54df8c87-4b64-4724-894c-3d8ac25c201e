{"ast": null, "code": "import baseLodash from './_baseLodash.js';\nimport wrapperClone from './_wrapperClone.js';\n\n/**\n * Creates a clone of the chain sequence planting `value` as the wrapped value.\n *\n * @name plant\n * @memberOf _\n * @since 3.2.0\n * @category Seq\n * @param {*} value The value to plant.\n * @returns {Object} Returns the new `lodash` wrapper instance.\n * @example\n *\n * function square(n) {\n *   return n * n;\n * }\n *\n * var wrapped = _([1, 2]).map(square);\n * var other = wrapped.plant([3, 4]);\n *\n * other.value();\n * // => [9, 16]\n *\n * wrapped.value();\n * // => [1, 4]\n */\nfunction wrapperPlant(value) {\n  var result,\n    parent = this;\n  while (parent instanceof baseLodash) {\n    var clone = wrapperClone(parent);\n    clone.__index__ = 0;\n    clone.__values__ = undefined;\n    if (result) {\n      previous.__wrapped__ = clone;\n    } else {\n      result = clone;\n    }\n    var previous = clone;\n    parent = parent.__wrapped__;\n  }\n  previous.__wrapped__ = value;\n  return result;\n}\nexport default wrapperPlant;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "wrapperClone", "wrapperPlant", "value", "result", "parent", "clone", "__index__", "__values__", "undefined", "previous", "__wrapped__"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/plant.js"], "sourcesContent": ["import baseLodash from './_baseLodash.js';\nimport wrapperClone from './_wrapperClone.js';\n\n/**\n * Creates a clone of the chain sequence planting `value` as the wrapped value.\n *\n * @name plant\n * @memberOf _\n * @since 3.2.0\n * @category Seq\n * @param {*} value The value to plant.\n * @returns {Object} Returns the new `lodash` wrapper instance.\n * @example\n *\n * function square(n) {\n *   return n * n;\n * }\n *\n * var wrapped = _([1, 2]).map(square);\n * var other = wrapped.plant([3, 4]);\n *\n * other.value();\n * // => [9, 16]\n *\n * wrapped.value();\n * // => [1, 4]\n */\nfunction wrapperPlant(value) {\n  var result,\n      parent = this;\n\n  while (parent instanceof baseLodash) {\n    var clone = wrapperClone(parent);\n    clone.__index__ = 0;\n    clone.__values__ = undefined;\n    if (result) {\n      previous.__wrapped__ = clone;\n    } else {\n      result = clone;\n    }\n    var previous = clone;\n    parent = parent.__wrapped__;\n  }\n  previous.__wrapped__ = value;\n  return result;\n}\n\nexport default wrapperPlant;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,MAAM;IACNC,MAAM,GAAG,IAAI;EAEjB,OAAOA,MAAM,YAAYL,UAAU,EAAE;IACnC,IAAIM,KAAK,GAAGL,YAAY,CAACI,MAAM,CAAC;IAChCC,KAAK,CAACC,SAAS,GAAG,CAAC;IACnBD,KAAK,CAACE,UAAU,GAAGC,SAAS;IAC5B,IAAIL,MAAM,EAAE;MACVM,QAAQ,CAACC,WAAW,GAAGL,KAAK;IAC9B,CAAC,MAAM;MACLF,MAAM,GAAGE,KAAK;IAChB;IACA,IAAII,QAAQ,GAAGJ,KAAK;IACpBD,MAAM,GAAGA,MAAM,CAACM,WAAW;EAC7B;EACAD,QAAQ,CAACC,WAAW,GAAGR,KAAK;EAC5B,OAAOC,MAAM;AACf;AAEA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}