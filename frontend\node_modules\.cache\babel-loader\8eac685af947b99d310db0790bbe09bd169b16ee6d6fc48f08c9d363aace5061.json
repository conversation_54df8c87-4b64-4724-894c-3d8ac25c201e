{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nconst colProps = buildProps({\n  tag: {\n    type: String,\n    default: \"div\"\n  },\n  span: {\n    type: Number,\n    default: 24\n  },\n  offset: {\n    type: Number,\n    default: 0\n  },\n  pull: {\n    type: Number,\n    default: 0\n  },\n  push: {\n    type: Number,\n    default: 0\n  },\n  xs: {\n    type: definePropType([Number, Object]),\n    default: () => mutable({})\n  },\n  sm: {\n    type: definePropType([Number, Object]),\n    default: () => mutable({})\n  },\n  md: {\n    type: definePropType([Number, Object]),\n    default: () => mutable({})\n  },\n  lg: {\n    type: definePropType([Number, Object]),\n    default: () => mutable({})\n  },\n  xl: {\n    type: definePropType([Number, Object]),\n    default: () => mutable({})\n  }\n});\nexport { colProps };", "map": {"version": 3, "names": ["colProps", "buildProps", "tag", "type", "String", "default", "span", "Number", "offset", "pull", "push", "xs", "definePropType", "Object", "mutable", "sm", "md", "lg", "xl"], "sources": ["../../../../../../packages/components/col/src/col.ts"], "sourcesContent": ["import { buildProps, definePropType, mutable } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Col from './col.vue'\n\nexport type ColSizeObject = {\n  span?: number\n  offset?: number\n  pull?: number\n  push?: number\n}\nexport type ColSize = number | ColSizeObject\n\nexport const colProps = buildProps({\n  /**\n   * @description custom element tag\n   */\n  tag: {\n    type: String,\n    default: 'div',\n  },\n  /**\n   * @description number of column the grid spans\n   */\n  span: {\n    type: Number,\n    default: 24,\n  },\n  /**\n   * @description number of spacing on the left side of the grid\n   */\n  offset: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description number of columns that grid moves to the left\n   */\n  pull: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description number of columns that grid moves to the right\n   */\n  push: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description `<768px` Responsive columns or column props object\n   */\n  xs: {\n    type: definePropType<ColSize>([Number, Object]),\n    default: () => mutable({} as const),\n  },\n  /**\n   * @description `≥768px` Responsive columns or column props object\n   */\n  sm: {\n    type: definePropType<ColSize>([Number, Object]),\n    default: () => mutable({} as const),\n  },\n  /**\n   * @description `≥992px` Responsive columns or column props object\n   */\n  md: {\n    type: definePropType<ColSize>([Number, Object]),\n    default: () => mutable({} as const),\n  },\n  /**\n   * @description `≥1200px` Responsive columns or column props object\n   */\n  lg: {\n    type: definePropType<ColSize>([Number, Object]),\n    default: () => mutable({} as const),\n  },\n  /**\n   * @description `≥1920px` Responsive columns or column props object\n   */\n  xl: {\n    type: definePropType<ColSize>([Number, Object]),\n    default: () => mutable({} as const),\n  },\n} as const)\nexport type ColProps = ExtractPropTypes<typeof colProps>\nexport type ColInstance = InstanceType<typeof Col> & unknown\n"], "mappings": ";;AACY,MAACA,QAAQ,GAAGC,UAAU,CAAC;EACjCC,GAAG,EAAE;IACHC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,IAAI,EAAE;IACJH,IAAI,EAAEI,MAAM;IACZF,OAAO,EAAE;EACb,CAAG;EACDG,MAAM,EAAE;IACNL,IAAI,EAAEI,MAAM;IACZF,OAAO,EAAE;EACb,CAAG;EACDI,IAAI,EAAE;IACJN,IAAI,EAAEI,MAAM;IACZF,OAAO,EAAE;EACb,CAAG;EACDK,IAAI,EAAE;IACJP,IAAI,EAAEI,MAAM;IACZF,OAAO,EAAE;EACb,CAAG;EACDM,EAAE,EAAE;IACFR,IAAI,EAAES,cAAc,CAAC,CAACL,MAAM,EAAEM,MAAM,CAAC,CAAC;IACtCR,OAAO,EAAEA,CAAA,KAAMS,OAAO,CAAC,EAAE;EAC7B,CAAG;EACDC,EAAE,EAAE;IACFZ,IAAI,EAAES,cAAc,CAAC,CAACL,MAAM,EAAEM,MAAM,CAAC,CAAC;IACtCR,OAAO,EAAEA,CAAA,KAAMS,OAAO,CAAC,EAAE;EAC7B,CAAG;EACDE,EAAE,EAAE;IACFb,IAAI,EAAES,cAAc,CAAC,CAACL,MAAM,EAAEM,MAAM,CAAC,CAAC;IACtCR,OAAO,EAAEA,CAAA,KAAMS,OAAO,CAAC,EAAE;EAC7B,CAAG;EACDG,EAAE,EAAE;IACFd,IAAI,EAAES,cAAc,CAAC,CAACL,MAAM,EAAEM,MAAM,CAAC,CAAC;IACtCR,OAAO,EAAEA,CAAA,KAAMS,OAAO,CAAC,EAAE;EAC7B,CAAG;EACDI,EAAE,EAAE;IACFf,IAAI,EAAES,cAAc,CAAC,CAACL,MAAM,EAAEM,MAAM,CAAC,CAAC;IACtCR,OAAO,EAAEA,CAAA,KAAMS,OAAO,CAAC,EAAE;EAC7B;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}