{"ast": null, "code": "import ColorPicker from './src/color-picker2.mjs';\nexport { colorPickerContextKey, colorPickerEmits, colorPickerProps } from './src/color-picker.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElColorPicker = withInstall(ColorPicker);\nexport { ElColorPicker, ElColorPicker as default };", "map": {"version": 3, "names": ["ElColorPicker", "withInstall", "ColorPicker"], "sources": ["../../../../../packages/components/color-picker/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport ColorPicker from './src/color-picker.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElColorPicker: SFCWithInstall<typeof ColorPicker> =\n  withInstall(ColorPicker)\nexport default ElColorPicker\n\nexport * from './src/color-picker'\n"], "mappings": ";;;AAEY,MAACA,aAAa,GAAGC,WAAW,CAACC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}