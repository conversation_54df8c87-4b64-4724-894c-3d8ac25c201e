{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, normalizeStyle } from 'vue';\nimport { alphaSliderProps } from '../props/alpha-slider.mjs';\nimport { useAlphaSlider, useAlphaSliderDOM } from '../composables/use-alpha-slider.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nconst COMPONENT_NAME = \"ElColorAlphaSlider\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: alphaSliderProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const {\n      alpha,\n      alphaLabel,\n      bar,\n      thumb,\n      handleDrag,\n      handleClick,\n      handleKeydown\n    } = useAlphaSlider(props);\n    const {\n      rootKls,\n      barKls,\n      barStyle,\n      thumbKls,\n      thumbStyle,\n      update\n    } = useAlphaSliderDOM(props, {\n      bar,\n      thumb,\n      handleDrag\n    });\n    expose({\n      update,\n      bar,\n      thumb\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(rootKls))\n      }, [createElementVNode(\"div\", {\n        ref_key: \"bar\",\n        ref: bar,\n        class: normalizeClass(unref(barKls)),\n        style: normalizeStyle(unref(barStyle)),\n        onClick: unref(handleClick)\n      }, null, 14, [\"onClick\"]), createElementVNode(\"div\", {\n        ref_key: \"thumb\",\n        ref: thumb,\n        class: normalizeClass(unref(thumbKls)),\n        style: normalizeStyle(unref(thumbStyle)),\n        \"aria-label\": unref(alphaLabel),\n        \"aria-valuenow\": unref(alpha),\n        \"aria-orientation\": _ctx.vertical ? \"vertical\" : \"horizontal\",\n        \"aria-valuemin\": \"0\",\n        \"aria-valuemax\": \"100\",\n        role: \"slider\",\n        tabindex: \"0\",\n        onKeydown: unref(handleKeydown)\n      }, null, 46, [\"aria-label\", \"aria-valuenow\", \"aria-orientation\", \"onKeydown\"])], 2);\n    };\n  }\n});\nvar AlphaSlider = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"alpha-slider.vue\"]]);\nexport { AlphaSlider as default };", "map": {"version": 3, "names": ["name", "COMPONENT_NAME", "alpha", "alphaLabel", "bar", "thumb", "handleDrag", "handleClick", "handleKeydown", "useAlphaSlider", "props", "rootKls", "barKls", "barStyle", "thumbKls", "thumbStyle", "update", "useAlphaSliderDOM", "expose", "_ctx", "_cache", "openBlock", "createElementBlock", "class", "normalizeClass", "unref", "createElementVNode", "ref_key", "ref", "style", "normalizeStyle", "onClick"], "sources": ["../../../../../../../packages/components/color-picker/src/components/alpha-slider.vue"], "sourcesContent": ["<template>\n  <div :class=\"rootKls\">\n    <div ref=\"bar\" :class=\"barKls\" :style=\"barStyle\" @click=\"handleClick\" />\n    <div\n      ref=\"thumb\"\n      :class=\"thumbKls\"\n      :style=\"thumbStyle\"\n      :aria-label=\"alphaLabel\"\n      :aria-valuenow=\"alpha\"\n      :aria-orientation=\"vertical ? 'vertical' : 'horizontal'\"\n      aria-valuemin=\"0\"\n      aria-valuemax=\"100\"\n      role=\"slider\"\n      tabindex=\"0\"\n      @keydown=\"handleKeydown\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { alphaSliderProps } from '../props/alpha-slider'\nimport {\n  useAlphaSlider,\n  useAlphaSliderDOM,\n} from '../composables/use-alpha-slider'\n\nconst COMPONENT_NAME = 'ElColorAlphaSlider'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(alphaSliderProps)\n\nconst {\n  alpha,\n  alphaLabel,\n  bar,\n  thumb,\n  handleDrag,\n  handleClick,\n  handleKeydown,\n} = useAlphaSlider(props)\n\nconst { rootKls, barKls, barStyle, thumbKls, thumbStyle, update } =\n  useAlphaSliderDOM(props, {\n    bar,\n    thumb,\n    handleDrag,\n  })\n\ndefineExpose({\n  /**\n   * @description update alpha slider manually\n   * @type {Function}\n   */\n  update,\n  /**\n   * @description bar element ref\n   * @type {HTMLElement}\n   */\n  bar,\n  /**\n   * @description thumb element ref\n   * @type {HTMLElement}\n   */\n  thumb,\n})\n</script>\n"], "mappings": ";;;;;mCA4Bc;EACZA,IAAM,EAAAC;AACR;;;;;;;;IAIM;MACJC,KAAA;MACAC,UAAA;MACAC,GAAA;MACAC,KAAA;MACAC,UAAA;MACAC,WAAA;MACAC;IAAA,CACF,GAAIC,cAAA,CAAeC,KAAK;IAElB;MAAEC,OAAA;MAASC,MAAQ;MAAAC,QAAA;MAAUC,QAAA;MAAUC,UAAY;MAAAC;IAAA,CACvD,GAAAC,iBAAA,CAAkBP,KAAO;MACvBN,GAAA;MACAC,KAAA;MACAC;IAAA,CACD;IAEUY,MAAA;MAAAF,MAAA;MAAAZ,GAAA;MAAAC;IAAA;IAKX,QAAAc,IAAA,EAAAC,MAAA;MAAA,OAAAC,SAAA,IAAAC,kBAAA;QAAAC,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAAd,OAAA;MAAA,IAAAe,kBAAA;QAKAC,OAAA;QAAAC,GAAA,EAAAxB,GAAA;QAAAmB,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAAb,MAAA;QAAAiB,KAAA,EAAAC,cAAA,CAAAL,KAAA,CAAAZ,QAAA;QAAAkB,OAAA,EAAAN,KAAA,CAAAlB,WAAA;MAAA,CAKA,0BACDmB,kBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}