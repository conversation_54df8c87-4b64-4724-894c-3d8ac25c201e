{"ast": null, "code": "const placeholderSign = Symbol(\"placeholder\");\nexport { placeholderSign };", "map": {"version": 3, "names": ["placeholderSign", "Symbol"], "sources": ["../../../../../../packages/components/table-v2/src/private.ts"], "sourcesContent": ["export const placeholderSign = Symbol('placeholder')\n"], "mappings": "AAAY,MAACA,eAAe,GAAGC,MAAM,CAAC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}