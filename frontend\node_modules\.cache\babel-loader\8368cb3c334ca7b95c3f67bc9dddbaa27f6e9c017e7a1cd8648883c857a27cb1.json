{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, renderSlot } from 'vue';\nimport { checkTagProps, checkTagEmits } from './check-tag.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElCheckTag\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: checkTagProps,\n  emits: checkTagEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"check-tag\");\n    const isDisabled = computed(() => props.disabled);\n    const containerKls = computed(() => [ns.b(), ns.is(\"checked\", props.checked), ns.is(\"disabled\", isDisabled.value), ns.m(props.type || \"primary\")]);\n    const handleChange = () => {\n      if (isDisabled.value) return;\n      const checked = !props.checked;\n      emit(CHANGE_EVENT, checked);\n      emit(\"update:checked\", checked);\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"span\", {\n        class: normalizeClass(unref(containerKls)),\n        onClick: handleChange\n      }, [renderSlot(_ctx.$slots, \"default\")], 2);\n    };\n  }\n});\nvar CheckTag = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"check-tag.vue\"]]);\nexport { CheckTag as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace", "isDisabled", "computed", "props", "disabled", "containerKls", "b", "is", "checked", "value", "m", "type", "handleChange", "emit", "CHANGE_EVENT"], "sources": ["../../../../../../packages/components/check-tag/src/check-tag.vue"], "sourcesContent": ["<template>\n  <span :class=\"containerKls\" @click=\"handleChange\">\n    <slot />\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { useNamespace } from '@element-plus/hooks'\nimport { checkTagEmits, checkTagProps } from './check-tag'\n\ndefineOptions({\n  name: 'ElCheckTag',\n})\nconst props = defineProps(checkTagProps)\nconst emit = defineEmits(checkTagEmits)\n\nconst ns = useNamespace('check-tag')\nconst isDisabled = computed(() => props.disabled)\nconst containerKls = computed(() => [\n  ns.b(),\n  ns.is('checked', props.checked),\n  ns.is('disabled', isDisabled.value),\n  ns.m(props.type || 'primary'),\n])\n\nconst handleChange = () => {\n  if (isDisabled.value) return\n\n  const checked = !props.checked\n  emit(CHANGE_EVENT, checked)\n  emit('update:checked', checked)\n}\n</script>\n"], "mappings": ";;;;;mCAYc;EACZA,IAAM;AACR;;;;;;;;;IAIM,MAAAC,EAAA,GAAKC,YAAA,CAAa,WAAW;IACnC,MAAMC,UAAa,GAAAC,QAAA,CAAS,MAAMC,KAAA,CAAMC,QAAQ;IAC1C,MAAAC,YAAA,GAAeH,QAAA,CAAS,MAAM,CAClCH,EAAA,CAAGO,CAAE,IACLP,EAAG,CAAAQ,EAAA,CAAG,SAAW,EAAAJ,KAAA,CAAMK,OAAO,GAC9BT,EAAG,CAAAQ,EAAA,CAAG,UAAY,EAAAN,UAAA,CAAWQ,KAAK,GAClCV,EAAG,CAAAW,CAAA,CAAEP,KAAM,CAAAQ,IAAA,IAAQ,SAAS,EAC7B;IAED,MAAMC,YAAA,GAAeA,CAAA,KAAM;MACzB,IAAIX,UAAA,CAAWQ,KAAO,EAEhB;MACN,MAAAD,OAAA,IAAAL,KAA0B,CAAAK,OAAA;MAC1BK,IAAA,CAAKC,YAAA,EAAAN,OAAyB;MAChCK,IAAA,mBAAAL,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}