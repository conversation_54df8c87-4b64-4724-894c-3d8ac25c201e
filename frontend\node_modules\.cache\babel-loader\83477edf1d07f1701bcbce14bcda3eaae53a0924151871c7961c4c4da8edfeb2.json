{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createBlock as _createBlock, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"reservation-detail\"\n};\nconst _hoisted_2 = {\n  class: \"page-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-left\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"loading-container\"\n};\nconst _hoisted_5 = {\n  key: 1,\n  class: \"empty-container\"\n};\nconst _hoisted_6 = {\n  key: 2,\n  class: \"detail-content\"\n};\nconst _hoisted_7 = {\n  class: \"card-header\"\n};\nconst _hoisted_8 = {\n  class: \"duration\"\n};\nconst _hoisted_9 = {\n  class: \"action-buttons\"\n};\nconst _hoisted_10 = {\n  class: \"cancel-dialog\"\n};\nconst _hoisted_11 = {\n  class: \"cancel-warning\"\n};\nconst _hoisted_12 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n    onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.back()),\n    icon: \"ArrowLeft\"\n  }, {\n    default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"返回\")])),\n    _: 1 /* STABLE */,\n    __: [3]\n  }), _cache[4] || (_cache[4] = _createElementVNode(\"h2\", null, \"预约详情\", -1 /* HOISTED */))])]), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createVNode(_component_el_skeleton, {\n    rows: 10,\n    animated: \"\"\n  })])) : !$setup.reservation ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_el_empty, {\n    description: \"未找到预约信息\"\n  })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createVNode(_component_el_card, {\n    shadow: \"hover\",\n    class: \"detail-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_cache[5] || (_cache[5] = _createElementVNode(\"h3\", null, \"基本信息\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n      type: $setup.getReservationStatusType($setup.reservation.status)\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getReservationStatusText($setup.reservation.status)), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_descriptions, {\n      column: 2,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"预约号\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.reservation.id), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"创建时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.reservation.created_at)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"预约时间\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.reservation.start_time)) + \" 至 \" + _toDisplayString($setup.formatDateTime($setup.reservation.end_time)) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_8, \" (\" + _toDisplayString($setup.calculateDuration($setup.reservation.start_time, $setup.reservation.end_time)) + \") \", 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), $setup.reservation.check_in_time ? (_openBlock(), _createBlock(_component_el_descriptions_item, {\n        key: 0,\n        label: \"签到时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.reservation.check_in_time)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), $setup.reservation.check_out_time ? (_openBlock(), _createBlock(_component_el_descriptions_item, {\n        key: 1,\n        label: \"签退时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.reservation.check_out_time)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), $setup.reservation.status === 'cancelled' && $setup.reservation.cancel_reason ? (_openBlock(), _createBlock(_component_el_descriptions_item, {\n        key: 2,\n        label: \"取消原因\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.reservation.cancel_reason), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_card, {\n    shadow: \"hover\",\n    class: \"detail-card\"\n  }, {\n    header: _withCtx(() => _cache[6] || (_cache[6] = [_createElementVNode(\"div\", {\n      class: \"card-header\"\n    }, [_createElementVNode(\"h3\", null, \"座位信息\")], -1 /* HOISTED */)])),\n    default: _withCtx(() => [_createVNode(_component_el_descriptions, {\n      column: 2,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"自习室\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.reservation.seat?.room?.name), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"位置\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.reservation.seat?.room?.location), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"座位号\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.reservation.seat?.seat_number), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"座位类型\"\n      }, {\n        default: _withCtx(() => [$setup.reservation.seat?.is_window_seat ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 0,\n          size: \"small\",\n          effect: \"plain\",\n          style: {\n            \"margin-right\": \"5px\"\n          }\n        }, {\n          default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\"靠窗\")])),\n          _: 1 /* STABLE */,\n          __: [7]\n        })) : _createCommentVNode(\"v-if\", true), $setup.reservation.seat?.is_power_outlet ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 1,\n          size: \"small\",\n          effect: \"plain\"\n        }, {\n          default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\"有电源\")])),\n          _: 1 /* STABLE */,\n          __: [8]\n        })) : _createCommentVNode(\"v-if\", true)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"座位描述\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.reservation.seat?.description || \"无\"), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_9, [$setup.reservation.status === 'pending' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleCheckIn\n  }, {\n    default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"签到\")])),\n    _: 1 /* STABLE */,\n    __: [9]\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    type: \"danger\",\n    onClick: $setup.handleCancel\n  }, {\n    default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"取消预约\")])),\n    _: 1 /* STABLE */,\n    __: [10]\n  }, 8 /* PROPS */, [\"onClick\"])], 64 /* STABLE_FRAGMENT */)) : $setup.reservation.status === 'checked_in' ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 1,\n    type: \"success\",\n    onClick: $setup.handleCheckOut\n  }, {\n    default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"签退\")])),\n    _: 1 /* STABLE */,\n    __: [11]\n  }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])])), _createCommentVNode(\" 取消预约对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.cancelDialogVisible,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.cancelDialogVisible = $event),\n    title: \"取消预约\",\n    width: \"400px\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_10, [_cache[14] || (_cache[14] = _createElementVNode(\"p\", null, \"您确定要取消此预约吗？\", -1 /* HOISTED */)), _createElementVNode(\"p\", null, \"座位号: \" + _toDisplayString($setup.reservation?.seat?.seat_number), 1 /* TEXT */), _createElementVNode(\"p\", null, \" 预约时间: \" + _toDisplayString($setup.formatDateTime($setup.reservation?.start_time)) + \" - \" + _toDisplayString($setup.formatDateTime($setup.reservation?.end_time)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_el_alert, {\n      title: \"取消预约提示\",\n      type: \"warning\",\n      description: \"距离预约开始时间不足30分钟取消，可能会影响您的信誉分。\",\n      \"show-icon\": \"\",\n      closable: false\n    })]), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_button, {\n      onClick: _cache[1] || (_cache[1] = $event => $setup.cancelDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"返回\")])),\n      _: 1 /* STABLE */,\n      __: [12]\n    }), _createVNode(_component_el_button, {\n      type: \"danger\",\n      onClick: $setup.confirmCancel,\n      loading: $setup.processing\n    }, {\n      default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"确认取消\")])),\n      _: 1 /* STABLE */,\n      __: [13]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_button", "onClick", "_cache", "$event", "_ctx", "$router", "back", "icon", "default", "_withCtx", "_createTextVNode", "_", "__", "$setup", "loading", "_hoisted_4", "_component_el_skeleton", "rows", "animated", "reservation", "_hoisted_5", "_component_el_empty", "description", "_hoisted_6", "_component_el_card", "shadow", "header", "_hoisted_7", "_component_el_tag", "type", "getReservationStatusType", "status", "_toDisplayString", "getReservationStatusText", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "label", "id", "formatDateTime", "created_at", "span", "start_time", "end_time", "_hoisted_8", "calculateDuration", "check_in_time", "_createBlock", "_createCommentVNode", "check_out_time", "cancel_reason", "seat", "room", "name", "location", "seat_number", "is_window_seat", "size", "effect", "style", "is_power_outlet", "_hoisted_9", "_Fragment", "handleCheckIn", "handleCancel", "handleCheckOut", "_component_el_dialog", "modelValue", "cancelDialogVisible", "title", "width", "_hoisted_10", "_hoisted_11", "_component_el_alert", "closable", "_hoisted_12", "confirmCancel", "processing"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\ReservationDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"reservation-detail\">\n    <div class=\"page-header\">\n      <div class=\"header-left\">\n        <el-button @click=\"$router.back()\" icon=\"ArrowLeft\">返回</el-button>\n        <h2>预约详情</h2>\n      </div>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"10\" animated />\n    </div>\n\n    <div v-else-if=\"!reservation\" class=\"empty-container\">\n      <el-empty description=\"未找到预约信息\" />\n    </div>\n\n    <div v-else class=\"detail-content\">\n      <el-card shadow=\"hover\" class=\"detail-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <h3>基本信息</h3>\n            <el-tag :type=\"getReservationStatusType(reservation.status)\">\n              {{ getReservationStatusText(reservation.status) }}\n            </el-tag>\n          </div>\n        </template>\n\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"预约号\">\n            {{ reservation.id }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"创建时间\">\n            {{ formatDateTime(reservation.created_at) }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"预约时间\" :span=\"2\">\n            {{ formatDateTime(reservation.start_time) }} 至\n            {{ formatDateTime(reservation.end_time) }}\n            <span class=\"duration\">\n              ({{ calculateDuration(reservation.start_time, reservation.end_time) }})\n            </span>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"签到时间\" v-if=\"reservation.check_in_time\">\n            {{ formatDateTime(reservation.check_in_time) }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"签退时间\" v-if=\"reservation.check_out_time\">\n            {{ formatDateTime(reservation.check_out_time) }}\n          </el-descriptions-item>\n          <el-descriptions-item\n            label=\"取消原因\"\n            v-if=\"reservation.status === 'cancelled' && reservation.cancel_reason\"\n            :span=\"2\"\n          >\n            {{ reservation.cancel_reason }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n\n      <el-card shadow=\"hover\" class=\"detail-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <h3>座位信息</h3>\n          </div>\n        </template>\n\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"自习室\">\n            {{ reservation.seat?.room?.name }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"位置\">\n            {{ reservation.seat?.room?.location }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"座位号\">\n            {{ reservation.seat?.seat_number }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"座位类型\">\n            <el-tag\n              v-if=\"reservation.seat?.is_window_seat\"\n              size=\"small\"\n              effect=\"plain\"\n              style=\"margin-right: 5px\"\n              >靠窗</el-tag\n            >\n            <el-tag\n              v-if=\"reservation.seat?.is_power_outlet\"\n              size=\"small\"\n              effect=\"plain\"\n              >有电源</el-tag\n            >\n          </el-descriptions-item>\n          <el-descriptions-item label=\"座位描述\" :span=\"2\">\n            {{ reservation.seat?.description || \"无\" }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n\n      <div class=\"action-buttons\">\n        <template v-if=\"reservation.status === 'pending'\">\n          <el-button type=\"primary\" @click=\"handleCheckIn\">签到</el-button>\n          <el-button type=\"danger\" @click=\"handleCancel\">取消预约</el-button>\n        </template>\n\n        <template v-else-if=\"reservation.status === 'checked_in'\">\n          <el-button type=\"success\" @click=\"handleCheckOut\">签退</el-button>\n        </template>\n      </div>\n    </div>\n\n    <!-- 取消预约对话框 -->\n    <el-dialog v-model=\"cancelDialogVisible\" title=\"取消预约\" width=\"400px\">\n      <div class=\"cancel-dialog\">\n        <p>您确定要取消此预约吗？</p>\n        <p>座位号: {{ reservation?.seat?.seat_number }}</p>\n        <p>\n          预约时间: {{ formatDateTime(reservation?.start_time) }} -\n          {{ formatDateTime(reservation?.end_time) }}\n        </p>\n\n        <div class=\"cancel-warning\">\n          <el-alert\n            title=\"取消预约提示\"\n            type=\"warning\"\n            description=\"距离预约开始时间不足30分钟取消，可能会影响您的信誉分。\"\n            show-icon\n            :closable=\"false\"\n          />\n        </div>\n\n        <div class=\"dialog-footer\">\n          <el-button @click=\"cancelDialogVisible = false\">返回</el-button>\n          <el-button type=\"danger\" @click=\"confirmCancel\" :loading=\"processing\"\n            >确认取消</el-button\n          >\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRoute, useRouter } from \"vue-router\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nimport { ArrowLeft } from \"@element-plus/icons-vue\";\n\nexport default {\n  name: \"ReservationDetail\",\n  components: {\n    ArrowLeft,\n  },\n  setup() {\n    const store = useStore();\n    const route = useRoute();\n    const router = useRouter();\n\n    const loading = ref(true);\n    const processing = ref(false);\n    const cancelDialogVisible = ref(false);\n\n    // 获取预约详情\n    const getReservationDetail = async () => {\n      try {\n        loading.value = true;\n        const reservationId = route.params.id;\n        \n        if (!reservationId) {\n          ElMessage.error(\"预约ID不能为空\");\n          return;\n        }\n\n        await store.dispatch(\"seat/getReservationById\", reservationId);\n      } catch (error) {\n        ElMessage.error(\"获取预约详情失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 预约信息\n    const reservation = computed(() => {\n      return store.getters[\"seat/currentReservation\"];\n    });\n\n    // 获取预约状态类型\n    const getReservationStatusType = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getReservationStatusText = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化日期时间\n    const formatDateTime = (dateTimeString) => {\n      if (!dateTimeString) return \"\";\n\n      const date = new Date(dateTimeString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(\n        date.getDate()\n      )} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 计算时长\n    const calculateDuration = (startTime, endTime) => {\n      if (!startTime || !endTime) return \"\";\n\n      const start = new Date(startTime);\n      const end = new Date(endTime);\n      const diffMs = end - start;\n      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\n\n      return `${diffHrs}小时${diffMins}分钟`;\n    };\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n\n    // 处理签到\n    const handleCheckIn = async () => {\n      try {\n        processing.value = true;\n\n        await store.dispatch(\"seat/checkIn\", {\n          reservationId: reservation.value.id,\n        });\n\n        ElMessage.success(\"签到成功\");\n        getReservationDetail();\n      } catch (error) {\n        ElMessage.error(error.message || \"签到失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 处理签退\n    const handleCheckOut = async () => {\n      try {\n        processing.value = true;\n\n        await store.dispatch(\"seat/checkOut\", {\n          reservationId: reservation.value.id,\n        });\n\n        ElMessage.success(\"签退成功\");\n        getReservationDetail();\n      } catch (error) {\n        ElMessage.error(error.message || \"签退失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 处理取消预约\n    const handleCancel = () => {\n      cancelDialogVisible.value = true;\n    };\n\n    // 确认取消预约\n    const confirmCancel = async () => {\n      try {\n        processing.value = true;\n\n        await store.dispatch(\"seat/cancelReservation\", {\n          reservationId: reservation.value.id,\n        });\n\n        ElMessage.success(\"预约已取消\");\n        cancelDialogVisible.value = false;\n        getReservationDetail();\n      } catch (error) {\n        ElMessage.error(error.message || \"取消预约失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    onMounted(() => {\n      getReservationDetail();\n    });\n\n    return {\n      loading,\n      processing,\n      reservation,\n      cancelDialogVisible,\n      getReservationStatusType,\n      getReservationStatusText,\n      formatDateTime,\n      calculateDuration,\n      handleCheckIn,\n      handleCheckOut,\n      handleCancel,\n      confirmCancel,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.reservation-detail {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  .header-left {\n    display: flex;\n    align-items: center;\n    gap: 10px;\n\n    h2 {\n      margin: 0;\n    }\n  }\n}\n\n.loading-container,\n.empty-container {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.detail-content {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.detail-card {\n  margin-bottom: 20px;\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    h3 {\n      margin: 0;\n    }\n  }\n}\n\n.duration {\n  color: #909399;\n  margin-left: 10px;\n  font-size: 14px;\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  margin-top: 30px;\n}\n\n.cancel-dialog {\n  p {\n    margin: 10px 0;\n  }\n\n  .cancel-warning {\n    margin: 20px 0;\n  }\n\n  .dialog-footer {\n    margin-top: 20px;\n    display: flex;\n    justify-content: flex-end;\n    gap: 10px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EAH9BC,GAAA;EASwBD,KAAK,EAAC;;;EAT9BC,GAAA;EAakCD,KAAK,EAAC;;;EAbxCC,GAAA;EAiBgBD,KAAK,EAAC;;;EAGPA,KAAK,EAAC;AAAa;;EAkBhBA,KAAK,EAAC;AAAU;;EA0DvBA,KAAK,EAAC;AAAgB;;EActBA,KAAK,EAAC;AAAe;;EAQnBA,KAAK,EAAC;AAAgB;;EAUtBA,KAAK,EAAC;AAAe;;;;;;;;;;;uBA/HhCE,mBAAA,CAuIM,OAvINC,UAuIM,GAtIJC,mBAAA,CAKM,OALNC,UAKM,GAJJD,mBAAA,CAGM,OAHNE,UAGM,GAFJC,YAAA,CAAkEC,oBAAA;IAAtDC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;IAAIC,IAAI,EAAC;;IAJhDC,OAAA,EAAAC,QAAA,CAI4D,MAAEP,MAAA,QAAAA,MAAA,OAJ9DQ,gBAAA,CAI4D,IAAE,E;IAJ9DC,CAAA;IAAAC,EAAA;gCAKQhB,mBAAA,CAAa,YAAT,MAAI,qB,KAIDiB,MAAA,CAAAC,OAAO,I,cAAlBpB,mBAAA,CAEM,OAFNqB,UAEM,GADJhB,YAAA,CAAmCiB,sBAAA;IAArBC,IAAI,EAAE,EAAE;IAAEC,QAAQ,EAAR;WAGTL,MAAA,CAAAM,WAAW,I,cAA5BzB,mBAAA,CAEM,OAFN0B,UAEM,GADJrB,YAAA,CAAkCsB,mBAAA;IAAxBC,WAAW,EAAC;EAAS,G,oBAGjC5B,mBAAA,CAyFM,OAzFN6B,UAyFM,GAxFJxB,YAAA,CAsCUyB,kBAAA;IAtCDC,MAAM,EAAC,OAAO;IAACjC,KAAK,EAAC;;IACjBkC,MAAM,EAAAjB,QAAA,CACf,MAKM,CALNb,mBAAA,CAKM,OALN+B,UAKM,G,0BAJJ/B,mBAAA,CAAa,YAAT,MAAI,sBACRG,YAAA,CAES6B,iBAAA;MAFAC,IAAI,EAAEhB,MAAA,CAAAiB,wBAAwB,CAACjB,MAAA,CAAAM,WAAW,CAACY,MAAM;;MAtBtEvB,OAAA,EAAAC,QAAA,CAuBc,MAAkD,CAvBhEC,gBAAA,CAAAsB,gBAAA,CAuBiBnB,MAAA,CAAAoB,wBAAwB,CAACpB,MAAA,CAAAM,WAAW,CAACY,MAAM,kB;MAvB5DpB,CAAA;;IAAAH,OAAA,EAAAC,QAAA,CA4BQ,MA2BkB,CA3BlBV,YAAA,CA2BkBmC,0BAAA;MA3BAC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAN;;MA5BrC5B,OAAA,EAAAC,QAAA,CA6BU,MAEuB,CAFvBV,YAAA,CAEuBsC,+BAAA;QAFDC,KAAK,EAAC;MAAK;QA7B3C9B,OAAA,EAAAC,QAAA,CA8BY,MAAoB,CA9BhCC,gBAAA,CAAAsB,gBAAA,CA8BenB,MAAA,CAAAM,WAAW,CAACoB,EAAE,iB;QA9B7B5B,CAAA;UAgCUZ,YAAA,CAEuBsC,+BAAA;QAFDC,KAAK,EAAC;MAAM;QAhC5C9B,OAAA,EAAAC,QAAA,CAiCY,MAA4C,CAjCxDC,gBAAA,CAAAsB,gBAAA,CAiCenB,MAAA,CAAA2B,cAAc,CAAC3B,MAAA,CAAAM,WAAW,CAACsB,UAAU,kB;QAjCpD9B,CAAA;UAmCUZ,YAAA,CAMuBsC,+BAAA;QANDC,KAAK,EAAC,MAAM;QAAEI,IAAI,EAAE;;QAnCpDlC,OAAA,EAAAC,QAAA,CAoCY,MAA4C,CApCxDC,gBAAA,CAAAsB,gBAAA,CAoCenB,MAAA,CAAA2B,cAAc,CAAC3B,MAAA,CAAAM,WAAW,CAACwB,UAAU,KAAI,KAC5C,GAAAX,gBAAA,CAAGnB,MAAA,CAAA2B,cAAc,CAAC3B,MAAA,CAAAM,WAAW,CAACyB,QAAQ,KAAI,GAC1C,iBAAAhD,mBAAA,CAEO,QAFPiD,UAEO,EAFgB,IACpB,GAAAb,gBAAA,CAAGnB,MAAA,CAAAiC,iBAAiB,CAACjC,MAAA,CAAAM,WAAW,CAACwB,UAAU,EAAE9B,MAAA,CAAAM,WAAW,CAACyB,QAAQ,KAAI,IACxE,gB;QAxCZjC,CAAA;UA0CmDE,MAAA,CAAAM,WAAW,CAAC4B,aAAa,I,cAAlEC,YAAA,CAEuBX,+BAAA;QA5CjC5C,GAAA;QA0CgC6C,KAAK,EAAC;;QA1CtC9B,OAAA,EAAAC,QAAA,CA2CY,MAA+C,CA3C3DC,gBAAA,CAAAsB,gBAAA,CA2CenB,MAAA,CAAA2B,cAAc,CAAC3B,MAAA,CAAAM,WAAW,CAAC4B,aAAa,kB;QA3CvDpC,CAAA;YAAAsC,mBAAA,gBA6CmDpC,MAAA,CAAAM,WAAW,CAAC+B,cAAc,I,cAAnEF,YAAA,CAEuBX,+BAAA;QA/CjC5C,GAAA;QA6CgC6C,KAAK,EAAC;;QA7CtC9B,OAAA,EAAAC,QAAA,CA8CY,MAAgD,CA9C5DC,gBAAA,CAAAsB,gBAAA,CA8CenB,MAAA,CAAA2B,cAAc,CAAC3B,MAAA,CAAAM,WAAW,CAAC+B,cAAc,kB;QA9CxDvC,CAAA;YAAAsC,mBAAA,gBAkDkBpC,MAAA,CAAAM,WAAW,CAACY,MAAM,oBAAoBlB,MAAA,CAAAM,WAAW,CAACgC,aAAa,I,cAFvEH,YAAA,CAMuBX,+BAAA;QAtDjC5C,GAAA;QAiDY6C,KAAK,EAAC,MAAM;QAEXI,IAAI,EAAE;;QAnDnBlC,OAAA,EAAAC,QAAA,CAqDY,MAA+B,CArD3CC,gBAAA,CAAAsB,gBAAA,CAqDenB,MAAA,CAAAM,WAAW,CAACgC,aAAa,iB;QArDxCxC,CAAA;YAAAsC,mBAAA,e;MAAAtC,CAAA;;IAAAA,CAAA;MA0DMZ,YAAA,CAoCUyB,kBAAA;IApCDC,MAAM,EAAC,OAAO;IAACjC,KAAK,EAAC;;IACjBkC,MAAM,EAAAjB,QAAA,CACf,MAEMP,MAAA,QAAAA,MAAA,OAFNN,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAa,IACtBI,mBAAA,CAAa,YAAT,MAAI,E;IA7DpBY,OAAA,EAAAC,QAAA,CAiEQ,MA4BkB,CA5BlBV,YAAA,CA4BkBmC,0BAAA;MA5BAC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAN;;MAjErC5B,OAAA,EAAAC,QAAA,CAkEU,MAEuB,CAFvBV,YAAA,CAEuBsC,+BAAA;QAFDC,KAAK,EAAC;MAAK;QAlE3C9B,OAAA,EAAAC,QAAA,CAmEY,MAAkC,CAnE9CC,gBAAA,CAAAsB,gBAAA,CAmEenB,MAAA,CAAAM,WAAW,CAACiC,IAAI,EAAEC,IAAI,EAAEC,IAAI,iB;QAnE3C3C,CAAA;UAqEUZ,YAAA,CAEuBsC,+BAAA;QAFDC,KAAK,EAAC;MAAI;QArE1C9B,OAAA,EAAAC,QAAA,CAsEY,MAAsC,CAtElDC,gBAAA,CAAAsB,gBAAA,CAsEenB,MAAA,CAAAM,WAAW,CAACiC,IAAI,EAAEC,IAAI,EAAEE,QAAQ,iB;QAtE/C5C,CAAA;UAwEUZ,YAAA,CAEuBsC,+BAAA;QAFDC,KAAK,EAAC;MAAK;QAxE3C9B,OAAA,EAAAC,QAAA,CAyEY,MAAmC,CAzE/CC,gBAAA,CAAAsB,gBAAA,CAyEenB,MAAA,CAAAM,WAAW,CAACiC,IAAI,EAAEI,WAAW,iB;QAzE5C7C,CAAA;UA2EUZ,YAAA,CAcuBsC,+BAAA;QAdDC,KAAK,EAAC;MAAM;QA3E5C9B,OAAA,EAAAC,QAAA,CA4EY,MAMC,CALOI,MAAA,CAAAM,WAAW,CAACiC,IAAI,EAAEK,cAAc,I,cADxCT,YAAA,CAMCpB,iBAAA;UAlFbnC,GAAA;UA8EciE,IAAI,EAAC,OAAO;UACZC,MAAM,EAAC,OAAO;UACdC,KAAyB,EAAzB;YAAA;UAAA;;UAhFdpD,OAAA,EAAAC,QAAA,CAiFe,MAAEP,MAAA,QAAAA,MAAA,OAjFjBQ,gBAAA,CAiFe,IAAE,E;UAjFjBC,CAAA;UAAAC,EAAA;cAAAqC,mBAAA,gBAoFoBpC,MAAA,CAAAM,WAAW,CAACiC,IAAI,EAAES,eAAe,I,cADzCb,YAAA,CAKCpB,iBAAA;UAxFbnC,GAAA;UAqFciE,IAAI,EAAC,OAAO;UACZC,MAAM,EAAC;;UAtFrBnD,OAAA,EAAAC,QAAA,CAuFe,MAAGP,MAAA,QAAAA,MAAA,OAvFlBQ,gBAAA,CAuFe,KAAG,E;UAvFlBC,CAAA;UAAAC,EAAA;cAAAqC,mBAAA,e;QAAAtC,CAAA;UA0FUZ,YAAA,CAEuBsC,+BAAA;QAFDC,KAAK,EAAC,MAAM;QAAEI,IAAI,EAAE;;QA1FpDlC,OAAA,EAAAC,QAAA,CA2FY,MAA0C,CA3FtDC,gBAAA,CAAAsB,gBAAA,CA2FenB,MAAA,CAAAM,WAAW,CAACiC,IAAI,EAAE9B,WAAW,wB;QA3F5CX,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;MAgGMf,mBAAA,CASM,OATNkE,UASM,GARYjD,MAAA,CAAAM,WAAW,CAACY,MAAM,kB,cAAlCrC,mBAAA,CAGWqE,SAAA;IApGnBtE,GAAA;EAAA,IAkGUM,YAAA,CAA+DC,oBAAA;IAApD6B,IAAI,EAAC,SAAS;IAAE5B,OAAK,EAAEY,MAAA,CAAAmD;;IAlG5CxD,OAAA,EAAAC,QAAA,CAkG2D,MAAEP,MAAA,QAAAA,MAAA,OAlG7DQ,gBAAA,CAkG2D,IAAE,E;IAlG7DC,CAAA;IAAAC,EAAA;kCAmGUb,YAAA,CAA+DC,oBAAA;IAApD6B,IAAI,EAAC,QAAQ;IAAE5B,OAAK,EAAEY,MAAA,CAAAoD;;IAnG3CzD,OAAA,EAAAC,QAAA,CAmGyD,MAAIP,MAAA,SAAAA,MAAA,QAnG7DQ,gBAAA,CAmGyD,MAAI,E;IAnG7DC,CAAA;IAAAC,EAAA;gEAsG6BC,MAAA,CAAAM,WAAW,CAACY,MAAM,qB,cACrCiB,YAAA,CAAgEhD,oBAAA;IAvG1EP,GAAA;IAuGqBoC,IAAI,EAAC,SAAS;IAAE5B,OAAK,EAAEY,MAAA,CAAAqD;;IAvG5C1D,OAAA,EAAAC,QAAA,CAuG4D,MAAEP,MAAA,SAAAA,MAAA,QAvG9DQ,gBAAA,CAuG4D,IAAE,E;IAvG9DC,CAAA;IAAAC,EAAA;oCAAAqC,mBAAA,e,MA4GIA,mBAAA,aAAgB,EAChBlD,YAAA,CA0BYoE,oBAAA;IAvIhBC,UAAA,EA6GwBvD,MAAA,CAAAwD,mBAAmB;IA7G3C,uBAAAnE,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA6GwBU,MAAA,CAAAwD,mBAAmB,GAAAlE,MAAA;IAAEmE,KAAK,EAAC,MAAM;IAACC,KAAK,EAAC;;IA7GhE/D,OAAA,EAAAC,QAAA,CA8GM,MAwBM,CAxBNb,mBAAA,CAwBM,OAxBN4E,WAwBM,G,4BAvBJ5E,mBAAA,CAAkB,WAAf,aAAW,sBACdA,mBAAA,CAAgD,WAA7C,OAAK,GAAAoC,gBAAA,CAAGnB,MAAA,CAAAM,WAAW,EAAEiC,IAAI,EAAEI,WAAW,kBACzC5D,mBAAA,CAGI,WAHD,SACK,GAAAoC,gBAAA,CAAGnB,MAAA,CAAA2B,cAAc,CAAC3B,MAAA,CAAAM,WAAW,EAAEwB,UAAU,KAAI,KACnD,GAAAX,gBAAA,CAAGnB,MAAA,CAAA2B,cAAc,CAAC3B,MAAA,CAAAM,WAAW,EAAEyB,QAAQ,mBAGzChD,mBAAA,CAQM,OARN6E,WAQM,GAPJ1E,YAAA,CAME2E,mBAAA;MALAJ,KAAK,EAAC,QAAQ;MACdzC,IAAI,EAAC,SAAS;MACdP,WAAW,EAAC,8BAA8B;MAC1C,WAAS,EAAT,EAAS;MACRqD,QAAQ,EAAE;UAIf/E,mBAAA,CAKM,OALNgF,WAKM,GAJJ7E,YAAA,CAA8DC,oBAAA;MAAlDC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEU,MAAA,CAAAwD,mBAAmB;;MAjIhD7D,OAAA,EAAAC,QAAA,CAiI0D,MAAEP,MAAA,SAAAA,MAAA,QAjI5DQ,gBAAA,CAiI0D,IAAE,E;MAjI5DC,CAAA;MAAAC,EAAA;QAkIUb,YAAA,CAECC,oBAAA;MAFU6B,IAAI,EAAC,QAAQ;MAAE5B,OAAK,EAAEY,MAAA,CAAAgE,aAAa;MAAG/D,OAAO,EAAED,MAAA,CAAAiE;;MAlIpEtE,OAAA,EAAAC,QAAA,CAmIa,MAAIP,MAAA,SAAAA,MAAA,QAnIjBQ,gBAAA,CAmIa,MAAI,E;MAnIjBC,CAAA;MAAAC,EAAA;;IAAAD,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}