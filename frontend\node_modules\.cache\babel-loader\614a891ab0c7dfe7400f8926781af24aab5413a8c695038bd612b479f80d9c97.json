{"ast": null, "code": "import { computed } from \"vue\";\nexport default {\n  name: \"AppFooter\",\n  setup() {\n    const currentYear = computed(() => new Date().getFullYear());\n    return {\n      currentYear\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "name", "setup", "currentYear", "Date", "getFullYear"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Footer.vue"], "sourcesContent": ["<template>\n  <footer class=\"app-footer\">\n    <div class=\"footer-content\">\n      <p>\n        &copy; {{ currentYear }} 图书馆自习室管理系统 |\n        基于国密算法的安全座位管理平台\n      </p>\n      <p>\n        <a href=\"/about\">关于我们</a> | <a href=\"/privacy\">隐私政策</a> |\n        <a href=\"/terms\">使用条款</a> |\n        <a href=\"/contact\">联系我们</a>\n      </p>\n    </div>\n  </footer>\n</template>\n\n<script>\nimport { computed } from \"vue\";\n\nexport default {\n  name: \"AppFooter\",\n  setup() {\n    const currentYear = computed(() => new Date().getFullYear());\n\n    return {\n      currentYear,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-footer {\n  background-color: #f5f7fa;\n  padding: 15px 0;\n  text-align: center;\n  font-size: 0.9rem;\n  color: #606266;\n  border-top: 1px solid #e6e6e6;\n}\n\n.footer-content {\n  max-width: 1200px;\n  margin: 0 auto;\n\n  p {\n    margin: 5px 0;\n  }\n\n  a {\n    color: #409eff;\n    text-decoration: none;\n\n    &:hover {\n      text-decoration: underline;\n    }\n  }\n}\n</style>\n"], "mappings": "AAiBA,SAASA,QAAO,QAAS,KAAK;AAE9B,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,KAAKA,CAAA,EAAG;IACN,MAAMC,WAAU,GAAIH,QAAQ,CAAC,MAAM,IAAII,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;IAE5D,OAAO;MACLF;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}