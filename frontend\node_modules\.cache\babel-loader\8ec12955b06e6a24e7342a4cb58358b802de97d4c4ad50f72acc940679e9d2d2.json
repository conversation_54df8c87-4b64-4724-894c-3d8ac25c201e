{"ast": null, "code": "import Segmented from './src/segmented2.mjs';\nexport { defaultProps, segmentedEmits, segmentedProps } from './src/segmented.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElSegmented = withInstall(Segmented);\nexport { ElSegmented, ElSegmented as default };", "map": {"version": 3, "names": ["ElSegmented", "withInstall", "Segmented"], "sources": ["../../../../../packages/components/segmented/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Segmented from './src/segmented.vue'\n\nexport const ElSegmented = withInstall(Segmented)\nexport default ElSegmented\n\nexport * from './src/segmented'\n"], "mappings": ";;;AAEY,MAACA,WAAW,GAAGC,WAAW,CAACC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}