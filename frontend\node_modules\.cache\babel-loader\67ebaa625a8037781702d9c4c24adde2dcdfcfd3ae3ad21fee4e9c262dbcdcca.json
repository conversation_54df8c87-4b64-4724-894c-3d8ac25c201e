{"ast": null, "code": "import { nextTick } from 'vue';\nimport { createLoadingComponent } from './loading.mjs';\nimport { isClient } from '@vueuse/core';\nimport { isString } from '@vue/shared';\nimport { getStyle, addClass, removeClass } from '../../../utils/dom/style.mjs';\nlet fullscreenInstance = void 0;\nconst Loading = function (options = {}) {\n  if (!isClient) return void 0;\n  const resolved = resolveOptions(options);\n  if (resolved.fullscreen && fullscreenInstance) {\n    return fullscreenInstance;\n  }\n  const instance = createLoadingComponent({\n    ...resolved,\n    closed: () => {\n      var _a;\n      (_a = resolved.closed) == null ? void 0 : _a.call(resolved);\n      if (resolved.fullscreen) fullscreenInstance = void 0;\n    }\n  }, Loading._context);\n  addStyle(resolved, resolved.parent, instance);\n  addClassList(resolved, resolved.parent, instance);\n  resolved.parent.vLoadingAddClassList = () => addClassList(resolved, resolved.parent, instance);\n  let loadingNumber = resolved.parent.getAttribute(\"loading-number\");\n  if (!loadingNumber) {\n    loadingNumber = \"1\";\n  } else {\n    loadingNumber = `${Number.parseInt(loadingNumber) + 1}`;\n  }\n  resolved.parent.setAttribute(\"loading-number\", loadingNumber);\n  resolved.parent.appendChild(instance.$el);\n  nextTick(() => instance.visible.value = resolved.visible);\n  if (resolved.fullscreen) {\n    fullscreenInstance = instance;\n  }\n  return instance;\n};\nconst resolveOptions = options => {\n  var _a, _b, _c, _d;\n  let target;\n  if (isString(options.target)) {\n    target = (_a = document.querySelector(options.target)) != null ? _a : document.body;\n  } else {\n    target = options.target || document.body;\n  }\n  return {\n    parent: target === document.body || options.body ? document.body : target,\n    background: options.background || \"\",\n    svg: options.svg || \"\",\n    svgViewBox: options.svgViewBox || \"\",\n    spinner: options.spinner || false,\n    text: options.text || \"\",\n    fullscreen: target === document.body && ((_b = options.fullscreen) != null ? _b : true),\n    lock: (_c = options.lock) != null ? _c : false,\n    customClass: options.customClass || \"\",\n    visible: (_d = options.visible) != null ? _d : true,\n    beforeClose: options.beforeClose,\n    closed: options.closed,\n    target\n  };\n};\nconst addStyle = async (options, parent, instance) => {\n  const {\n    nextZIndex\n  } = instance.vm.zIndex || instance.vm._.exposed.zIndex;\n  const maskStyle = {};\n  if (options.fullscreen) {\n    instance.originalPosition.value = getStyle(document.body, \"position\");\n    instance.originalOverflow.value = getStyle(document.body, \"overflow\");\n    maskStyle.zIndex = nextZIndex();\n  } else if (options.parent === document.body) {\n    instance.originalPosition.value = getStyle(document.body, \"position\");\n    await nextTick();\n    for (const property of [\"top\", \"left\"]) {\n      const scroll = property === \"top\" ? \"scrollTop\" : \"scrollLeft\";\n      maskStyle[property] = `${options.target.getBoundingClientRect()[property] + document.body[scroll] + document.documentElement[scroll] - Number.parseInt(getStyle(document.body, `margin-${property}`), 10)}px`;\n    }\n    for (const property of [\"height\", \"width\"]) {\n      maskStyle[property] = `${options.target.getBoundingClientRect()[property]}px`;\n    }\n  } else {\n    instance.originalPosition.value = getStyle(parent, \"position\");\n  }\n  for (const [key, value] of Object.entries(maskStyle)) {\n    instance.$el.style[key] = value;\n  }\n};\nconst addClassList = (options, parent, instance) => {\n  const ns = instance.vm.ns || instance.vm._.exposed.ns;\n  if (![\"absolute\", \"fixed\", \"sticky\"].includes(instance.originalPosition.value)) {\n    addClass(parent, ns.bm(\"parent\", \"relative\"));\n  } else {\n    removeClass(parent, ns.bm(\"parent\", \"relative\"));\n  }\n  if (options.fullscreen && options.lock) {\n    addClass(parent, ns.bm(\"parent\", \"hidden\"));\n  } else {\n    removeClass(parent, ns.bm(\"parent\", \"hidden\"));\n  }\n};\nLoading._context = null;\nexport { Loading as default };", "map": {"version": 3, "names": ["fullscreenInstance", "Loading", "options", "isClient", "resolved", "resolveOptions", "fullscreen", "instance", "createLoadingComponent", "closed", "_a", "call", "_context", "addStyle", "parent", "addClassList", "vLoadingAddClassList", "loadingNumber", "getAttribute", "Number", "parseInt", "setAttribute", "append<PERSON><PERSON><PERSON>", "$el", "nextTick", "visible", "value", "_b", "_c", "_d", "target", "isString", "document", "querySelector", "body", "background", "svg", "svgViewBox", "spinner", "text", "lock", "customClass", "beforeClose", "nextZIndex", "vm", "zIndex", "_", "exposed", "maskStyle", "originalPosition", "getStyle", "originalOverflow", "property", "scroll", "getBoundingClientRect", "documentElement", "key", "Object", "entries", "style", "ns", "includes", "addClass", "bm", "removeClass"], "sources": ["../../../../../../packages/components/loading/src/service.ts"], "sourcesContent": ["// @ts-nocheck\nimport { nextTick } from 'vue'\nimport {\n  addClass,\n  getStyle,\n  isClient,\n  isString,\n  removeClass,\n} from '@element-plus/utils'\nimport { createLoadingComponent } from './loading'\n\nimport type { UseNamespaceReturn, UseZIndexReturn } from '@element-plus/hooks'\nimport type { LoadingInstance } from './loading'\nimport type { LoadingOptionsResolved } from '..'\nimport type { LoadingOptions } from './types'\nimport type { AppContext, CSSProperties } from 'vue'\n\nlet fullscreenInstance: LoadingInstance | undefined = undefined\n\nconst Loading = function (options: LoadingOptions = {}): LoadingInstance {\n  if (!isClient) return undefined as any\n\n  const resolved = resolveOptions(options)\n\n  if (resolved.fullscreen && fullscreenInstance) {\n    return fullscreenInstance\n  }\n\n  const instance = createLoadingComponent(\n    {\n      ...resolved,\n      closed: () => {\n        resolved.closed?.()\n        if (resolved.fullscreen) fullscreenInstance = undefined\n      },\n    },\n    Loading._context\n  )\n\n  addStyle(resolved, resolved.parent, instance)\n  addClassList(resolved, resolved.parent, instance)\n\n  resolved.parent.vLoadingAddClassList = () =>\n    addClassList(resolved, resolved.parent, instance)\n\n  /**\n   * add loading-number to parent.\n   * because if a fullscreen loading is triggered when somewhere\n   * a v-loading.body was triggered before and it's parent is\n   * document.body which with a margin , the fullscreen loading's\n   * destroySelf function will remove 'el-loading-parent--relative',\n   * and then the position of v-loading.body will be error.\n   */\n  let loadingNumber: string | null =\n    resolved.parent.getAttribute('loading-number')\n  if (!loadingNumber) {\n    loadingNumber = '1'\n  } else {\n    loadingNumber = `${Number.parseInt(loadingNumber) + 1}`\n  }\n  resolved.parent.setAttribute('loading-number', loadingNumber)\n\n  resolved.parent.appendChild(instance.$el)\n\n  // after instance render, then modify visible to trigger transition\n  nextTick(() => (instance.visible.value = resolved.visible))\n\n  if (resolved.fullscreen) {\n    fullscreenInstance = instance\n  }\n  return instance\n}\n\nconst resolveOptions = (options: LoadingOptions): LoadingOptionsResolved => {\n  let target: HTMLElement\n  if (isString(options.target)) {\n    target =\n      document.querySelector<HTMLElement>(options.target) ?? document.body\n  } else {\n    target = options.target || document.body\n  }\n  return {\n    parent: target === document.body || options.body ? document.body : target,\n    background: options.background || '',\n    svg: options.svg || '',\n    svgViewBox: options.svgViewBox || '',\n    spinner: options.spinner || false,\n    text: options.text || '',\n    fullscreen: target === document.body && (options.fullscreen ?? true),\n    lock: options.lock ?? false,\n    customClass: options.customClass || '',\n    visible: options.visible ?? true,\n    beforeClose: options.beforeClose,\n    closed: options.closed,\n    target,\n  }\n}\n\nconst addStyle = async (\n  options: LoadingOptionsResolved,\n  parent: HTMLElement,\n  instance: LoadingInstance\n) => {\n  // Compatible with the instance data format of vue@3.2.12 and earlier versions #12351\n  const { nextZIndex } =\n    ((instance.vm as any).zIndex as UseZIndexReturn) ||\n    (instance.vm as any)._.exposed.zIndex\n\n  const maskStyle: CSSProperties = {}\n  if (options.fullscreen) {\n    instance.originalPosition.value = getStyle(document.body, 'position')\n    instance.originalOverflow.value = getStyle(document.body, 'overflow')\n    maskStyle.zIndex = nextZIndex()\n  } else if (options.parent === document.body) {\n    instance.originalPosition.value = getStyle(document.body, 'position')\n    /**\n     * await dom render when visible is true in init,\n     * because some component's height maybe 0.\n     * e.g. el-table.\n     */\n    await nextTick()\n    for (const property of ['top', 'left']) {\n      const scroll = property === 'top' ? 'scrollTop' : 'scrollLeft'\n      maskStyle[property] = `${\n        (options.target as HTMLElement).getBoundingClientRect()[property] +\n        document.body[scroll] +\n        document.documentElement[scroll] -\n        Number.parseInt(getStyle(document.body, `margin-${property}`), 10)\n      }px`\n    }\n    for (const property of ['height', 'width']) {\n      maskStyle[property] = `${\n        (options.target as HTMLElement).getBoundingClientRect()[property]\n      }px`\n    }\n  } else {\n    instance.originalPosition.value = getStyle(parent, 'position')\n  }\n  for (const [key, value] of Object.entries(maskStyle)) {\n    instance.$el.style[key] = value\n  }\n}\n\nconst addClassList = (\n  options: LoadingOptions,\n  parent: HTMLElement,\n  instance: LoadingInstance\n) => {\n  // Compatible with the instance data format of vue@3.2.12 and earlier versions #12351\n  const ns =\n    ((instance.vm as any).ns as UseNamespaceReturn) ||\n    (instance.vm as any)._.exposed.ns\n\n  if (\n    !['absolute', 'fixed', 'sticky'].includes(instance.originalPosition.value)\n  ) {\n    addClass(parent, ns.bm('parent', 'relative'))\n  } else {\n    removeClass(parent, ns.bm('parent', 'relative'))\n  }\n  if (options.fullscreen && options.lock) {\n    addClass(parent, ns.bm('parent', 'hidden'))\n  } else {\n    removeClass(parent, ns.bm('parent', 'hidden'))\n  }\n}\n\nLoading._context = null as AppContext | null\nexport default Loading\n"], "mappings": ";;;;;AASA,IAAIA,kBAAkB,GAAG,KAAK,CAAC;AAC1B,MAACC,OAAO,GAAG,SAAAA,CAASC,OAAO,GAAG,EAAE,EAAE;EACrC,IAAI,CAACC,QAAQ,EACX,OAAO,KAAK,CAAC;EACf,MAAMC,QAAQ,GAAGC,cAAc,CAACH,OAAO,CAAC;EACxC,IAAIE,QAAQ,CAACE,UAAU,IAAIN,kBAAkB,EAAE;IAC7C,OAAOA,kBAAkB;EAC7B;EACE,MAAMO,QAAQ,GAAGC,sBAAsB,CAAC;IACtC,GAAGJ,QAAQ;IACXK,MAAM,EAAEA,CAAA,KAAM;MACZ,IAAIC,EAAE;MACN,CAACA,EAAE,GAAGN,QAAQ,CAACK,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,EAAE,CAACC,IAAI,CAACP,QAAQ,CAAC;MAC3D,IAAIA,QAAQ,CAACE,UAAU,EACrBN,kBAAkB,GAAG,KAAK,CAAC;IACnC;EACA,CAAG,EAAEC,OAAO,CAACW,QAAQ,CAAC;EACpBC,QAAQ,CAACT,QAAQ,EAAEA,QAAQ,CAACU,MAAM,EAAEP,QAAQ,CAAC;EAC7CQ,YAAY,CAACX,QAAQ,EAAEA,QAAQ,CAACU,MAAM,EAAEP,QAAQ,CAAC;EACjDH,QAAQ,CAACU,MAAM,CAACE,oBAAoB,GAAG,MAAMD,YAAY,CAACX,QAAQ,EAAEA,QAAQ,CAACU,MAAM,EAAEP,QAAQ,CAAC;EAC9F,IAAIU,aAAa,GAAGb,QAAQ,CAACU,MAAM,CAACI,YAAY,CAAC,gBAAgB,CAAC;EAClE,IAAI,CAACD,aAAa,EAAE;IAClBA,aAAa,GAAG,GAAG;EACvB,CAAG,MAAM;IACLA,aAAa,GAAG,GAAGE,MAAM,CAACC,QAAQ,CAACH,aAAa,CAAC,GAAG,CAAC,EAAE;EAC3D;EACEb,QAAQ,CAACU,MAAM,CAACO,YAAY,CAAC,gBAAgB,EAAEJ,aAAa,CAAC;EAC7Db,QAAQ,CAACU,MAAM,CAACQ,WAAW,CAACf,QAAQ,CAACgB,GAAG,CAAC;EACzCC,QAAQ,CAAC,MAAMjB,QAAQ,CAACkB,OAAO,CAACC,KAAK,GAAGtB,QAAQ,CAACqB,OAAO,CAAC;EACzD,IAAIrB,QAAQ,CAACE,UAAU,EAAE;IACvBN,kBAAkB,GAAGO,QAAQ;EACjC;EACE,OAAOA,QAAQ;AACjB;AACA,MAAMF,cAAc,GAAIH,OAAO,IAAK;EAClC,IAAIQ,EAAE,EAAEiB,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,IAAIC,MAAM;EACV,IAAIC,QAAQ,CAAC7B,OAAO,CAAC4B,MAAM,CAAC,EAAE;IAC5BA,MAAM,GAAG,CAACpB,EAAE,GAAGsB,QAAQ,CAACC,aAAa,CAAC/B,OAAO,CAAC4B,MAAM,CAAC,KAAK,IAAI,GAAGpB,EAAE,GAAGsB,QAAQ,CAACE,IAAI;EACvF,CAAG,MAAM;IACLJ,MAAM,GAAG5B,OAAO,CAAC4B,MAAM,IAAIE,QAAQ,CAACE,IAAI;EAC5C;EACE,OAAO;IACLpB,MAAM,EAAEgB,MAAM,KAAKE,QAAQ,CAACE,IAAI,IAAIhC,OAAO,CAACgC,IAAI,GAAGF,QAAQ,CAACE,IAAI,GAAGJ,MAAM;IACzEK,UAAU,EAAEjC,OAAO,CAACiC,UAAU,IAAI,EAAE;IACpCC,GAAG,EAAElC,OAAO,CAACkC,GAAG,IAAI,EAAE;IACtBC,UAAU,EAAEnC,OAAO,CAACmC,UAAU,IAAI,EAAE;IACpCC,OAAO,EAAEpC,OAAO,CAACoC,OAAO,IAAI,KAAK;IACjCC,IAAI,EAAErC,OAAO,CAACqC,IAAI,IAAI,EAAE;IACxBjC,UAAU,EAAEwB,MAAM,KAAKE,QAAQ,CAACE,IAAI,KAAK,CAACP,EAAE,GAAGzB,OAAO,CAACI,UAAU,KAAK,IAAI,GAAGqB,EAAE,GAAG,IAAI,CAAC;IACvFa,IAAI,EAAE,CAACZ,EAAE,GAAG1B,OAAO,CAACsC,IAAI,KAAK,IAAI,GAAGZ,EAAE,GAAG,KAAK;IAC9Ca,WAAW,EAAEvC,OAAO,CAACuC,WAAW,IAAI,EAAE;IACtChB,OAAO,EAAE,CAACI,EAAE,GAAG3B,OAAO,CAACuB,OAAO,KAAK,IAAI,GAAGI,EAAE,GAAG,IAAI;IACnDa,WAAW,EAAExC,OAAO,CAACwC,WAAW;IAChCjC,MAAM,EAAEP,OAAO,CAACO,MAAM;IACtBqB;EACJ,CAAG;AACH,CAAC;AACD,MAAMjB,QAAQ,GAAG,MAAAA,CAAOX,OAAO,EAAEY,MAAM,EAAEP,QAAQ,KAAK;EACpD,MAAM;IAAEoC;EAAU,CAAE,GAAGpC,QAAQ,CAACqC,EAAE,CAACC,MAAM,IAAItC,QAAQ,CAACqC,EAAE,CAACE,CAAC,CAACC,OAAO,CAACF,MAAM;EACzE,MAAMG,SAAS,GAAG,EAAE;EACpB,IAAI9C,OAAO,CAACI,UAAU,EAAE;IACtBC,QAAQ,CAAC0C,gBAAgB,CAACvB,KAAK,GAAGwB,QAAQ,CAAClB,QAAQ,CAACE,IAAI,EAAE,UAAU,CAAC;IACrE3B,QAAQ,CAAC4C,gBAAgB,CAACzB,KAAK,GAAGwB,QAAQ,CAAClB,QAAQ,CAACE,IAAI,EAAE,UAAU,CAAC;IACrEc,SAAS,CAACH,MAAM,GAAGF,UAAU,EAAE;EACnC,CAAG,MAAM,IAAIzC,OAAO,CAACY,MAAM,KAAKkB,QAAQ,CAACE,IAAI,EAAE;IAC3C3B,QAAQ,CAAC0C,gBAAgB,CAACvB,KAAK,GAAGwB,QAAQ,CAAClB,QAAQ,CAACE,IAAI,EAAE,UAAU,CAAC;IACrE,MAAMV,QAAQ,EAAE;IAChB,KAAK,MAAM4B,QAAQ,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE;MACtC,MAAMC,MAAM,GAAGD,QAAQ,KAAK,KAAK,GAAG,WAAW,GAAG,YAAY;MAC9DJ,SAAS,CAACI,QAAQ,CAAC,GAAG,GAAGlD,OAAO,CAAC4B,MAAM,CAACwB,qBAAqB,EAAE,CAACF,QAAQ,CAAC,GAAGpB,QAAQ,CAACE,IAAI,CAACmB,MAAM,CAAC,GAAGrB,QAAQ,CAACuB,eAAe,CAACF,MAAM,CAAC,GAAGlC,MAAM,CAACC,QAAQ,CAAC8B,QAAQ,CAAClB,QAAQ,CAACE,IAAI,EAAE,UAAUkB,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI;IACnN;IACI,KAAK,MAAMA,QAAQ,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;MAC1CJ,SAAS,CAACI,QAAQ,CAAC,GAAG,GAAGlD,OAAO,CAAC4B,MAAM,CAACwB,qBAAqB,EAAE,CAACF,QAAQ,CAAC,IAAI;IACnF;EACA,CAAG,MAAM;IACL7C,QAAQ,CAAC0C,gBAAgB,CAACvB,KAAK,GAAGwB,QAAQ,CAACpC,MAAM,EAAE,UAAU,CAAC;EAClE;EACE,KAAK,MAAM,CAAC0C,GAAG,EAAE9B,KAAK,CAAC,IAAI+B,MAAM,CAACC,OAAO,CAACV,SAAS,CAAC,EAAE;IACpDzC,QAAQ,CAACgB,GAAG,CAACoC,KAAK,CAACH,GAAG,CAAC,GAAG9B,KAAK;EACnC;AACA,CAAC;AACD,MAAMX,YAAY,GAAGA,CAACb,OAAO,EAAEY,MAAM,EAAEP,QAAQ,KAAK;EAClD,MAAMqD,EAAE,GAAGrD,QAAQ,CAACqC,EAAE,CAACgB,EAAE,IAAIrD,QAAQ,CAACqC,EAAE,CAACE,CAAC,CAACC,OAAO,CAACa,EAAE;EACrD,IAAI,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACtD,QAAQ,CAAC0C,gBAAgB,CAACvB,KAAK,CAAC,EAAE;IAC9EoC,QAAQ,CAAChD,MAAM,EAAE8C,EAAE,CAACG,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;EACjD,CAAG,MAAM;IACLC,WAAW,CAAClD,MAAM,EAAE8C,EAAE,CAACG,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;EACpD;EACE,IAAI7D,OAAO,CAACI,UAAU,IAAIJ,OAAO,CAACsC,IAAI,EAAE;IACtCsB,QAAQ,CAAChD,MAAM,EAAE8C,EAAE,CAACG,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;EAC/C,CAAG,MAAM;IACLC,WAAW,CAAClD,MAAM,EAAE8C,EAAE,CAACG,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;EAClD;AACA,CAAC;AACD9D,OAAO,CAACW,QAAQ,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}