{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, inject, ref, watch, watchEffect, openBlock, createElementBlock, normalizeClass, createElementVNode, Fragment, renderList, normalizeStyle } from 'vue';\nimport { colorPickerContextKey } from '../color-picker.mjs';\nimport Color from '../utils/color.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nconst _sfc_main = defineComponent({\n  props: {\n    colors: {\n      type: Array,\n      required: true\n    },\n    color: {\n      type: Object,\n      required: true\n    },\n    enableAlpha: {\n      type: Boolean,\n      required: true\n    }\n  },\n  setup(props) {\n    const ns = useNamespace(\"color-predefine\");\n    const {\n      currentColor\n    } = inject(colorPickerContextKey);\n    const rgbaColors = ref(parseColors(props.colors, props.color));\n    watch(() => currentColor.value, val => {\n      const color = new Color();\n      color.fromString(val);\n      rgbaColors.value.forEach(item => {\n        item.selected = color.compare(item);\n      });\n    });\n    watchEffect(() => {\n      rgbaColors.value = parseColors(props.colors, props.color);\n    });\n    function handleSelect(index) {\n      props.color.fromString(props.colors[index]);\n    }\n    function parseColors(colors, color) {\n      return colors.map(value => {\n        const c = new Color();\n        c.enableAlpha = props.enableAlpha;\n        c.format = \"rgba\";\n        c.fromString(value);\n        c.selected = c.value === color.value;\n        return c;\n      });\n    }\n    return {\n      rgbaColors,\n      handleSelect,\n      ns\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass(_ctx.ns.b())\n  }, [createElementVNode(\"div\", {\n    class: normalizeClass(_ctx.ns.e(\"colors\"))\n  }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.rgbaColors, (item, index) => {\n    return openBlock(), createElementBlock(\"div\", {\n      key: _ctx.colors[index],\n      class: normalizeClass([_ctx.ns.e(\"color-selector\"), _ctx.ns.is(\"alpha\", item._alpha < 100), {\n        selected: item.selected\n      }]),\n      onClick: $event => _ctx.handleSelect(index)\n    }, [createElementVNode(\"div\", {\n      style: normalizeStyle({\n        backgroundColor: item.value\n      })\n    }, null, 4)], 10, [\"onClick\"]);\n  }), 128))], 2)], 2);\n}\nvar Predefine = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"predefine.vue\"]]);\nexport { Predefine as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "props", "colors", "type", "Array", "required", "color", "Object", "enableAlpha", "Boolean", "setup", "ns", "useNamespace", "currentColor", "inject", "colorPickerContextKey", "rgbaColors", "ref", "parseColors", "watch", "value", "val", "Color", "fromString", "for<PERSON>ach", "item", "selected", "compare", "watchEffect", "handleSelect", "index", "map", "c", "format", "_sfc_render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "openBlock", "createElementBlock", "createElementVNode", "class", "normalizeClass", "e", "Fragment", "renderList", "is", "_alpha", "style", "normalizeStyle", "backgroundColor", "Predefine", "_export_sfc"], "sources": ["../../../../../../../packages/components/color-picker/src/components/predefine.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <div :class=\"ns.e('colors')\">\n      <div\n        v-for=\"(item, index) in rgbaColors\"\n        :key=\"colors[index]\"\n        :class=\"[\n          ns.e('color-selector'),\n          ns.is('alpha', item._alpha < 100),\n          { selected: item.selected },\n        ]\"\n        @click=\"handleSelect(index)\"\n      >\n        <div :style=\"{ backgroundColor: item.value }\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, inject, ref, watch, watchEffect } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { colorPickerContextKey } from '../color-picker'\nimport Color from '../utils/color'\n\nimport type { PropType, Ref } from 'vue'\n\nexport default defineComponent({\n  props: {\n    colors: {\n      type: Array as PropType<string[]>,\n      required: true,\n    },\n    color: {\n      type: Object as PropType<Color>,\n      required: true,\n    },\n    enableAlpha: {\n      type: Boolean,\n      required: true,\n    },\n  },\n  setup(props) {\n    const ns = useNamespace('color-predefine')\n    const { currentColor } = inject(colorPickerContextKey)!\n\n    const rgbaColors = ref(parseColors(props.colors, props.color)) as Ref<\n      Color[]\n    >\n\n    watch(\n      () => currentColor.value,\n      (val) => {\n        const color = new Color()\n        color.fromString(val)\n\n        rgbaColors.value.forEach((item) => {\n          item.selected = color.compare(item)\n        })\n      }\n    )\n\n    watchEffect(() => {\n      rgbaColors.value = parseColors(props.colors, props.color)\n    })\n\n    function handleSelect(index: number) {\n      props.color.fromString(props.colors[index])\n    }\n\n    function parseColors(colors: string[], color: Color) {\n      return colors.map((value) => {\n        const c = new Color()\n        c.enableAlpha = props.enableAlpha\n        c.format = 'rgba'\n        c.fromString(value)\n        c.selected = c.value === color.value\n        return c\n      })\n    }\n    return {\n      rgbaColors,\n      handleSelect,\n      ns,\n    }\n  },\n})\n</script>\n"], "mappings": ";;;;;;;;AA2BA,MAAKA,SAAA,GAAaC,eAAa;EAC7BC,KAAO;IACLC,MAAQ;MACNC,IAAM,EAAAC,KAAA;MACNC,QAAU;IAAA,CACZ;IACAC,KAAO;MACLH,IAAM,EAAAI,MAAA;MACNF,QAAU;IAAA,CACZ;IACAG,WAAa;MACXL,IAAM,EAAAM,OAAA;MACNJ,QAAU;IAAA;EACZ,CACF;EACAK,MAAMT,KAAO;IACL,MAAAU,EAAA,GAAKC,YAAA,CAAa,iBAAiB;IACzC,MAAM;MAAEC;IAAA,CAAiB,GAAAC,MAAA,CAAOC,qBAAqB;IAErD,MAAMC,UAAA,GAAaC,GAAI,CAAAC,WAAA,CAAYjB,KAAA,CAAMC,MAAQ,EAAAD,KAAA,CAAMK,KAAK,CAAC;IAI7Da,KAAA,OAAAN,YAAA,CAAAO,KAAA,EAAAC,GAAA;MACE,MAAMf,KAAa,OAAAgB,KAAA;MACnBhB,KAAS,CAAAiB,UAAA,CAAAF,GAAA;MACDL,UAAA,CAAAI,KAAQ,CAAAI,OAAU,CAAAC,IAAA;QACxBA,IAAA,CAAAC,QAAA,GAAApB,KAAoB,CAAAqB,OAAA,CAAAF,IAAA;MAEpB,CAAW;IACT,CAAK;IAA6BG,WACnC;MACHZ,UAAA,CAAAI,KAAA,GAAAF,WAAA,CAAAjB,KAAA,CAAAC,MAAA,EAAAD,KAAA,CAAAK,KAAA;IAAA,CACF;IAEA,SAAAuB,YAAkBA,CAAAC,KAAA;MAChB7B,KAAA,CAAAK,KAAW,CAAQiB,UAAA,CAAAtB,KAAA,CAAAC,MAAkB,CAAA4B,KAAA;IAAmB;IAG1D,SAASZ,YAAAhB,MAA4B,EAAAI,KAAA;MACnC,OAAYJ,MAAA,CAAA6B,GAAA,CAAAX,KAAiB;QAC/B,MAAAY,CAAA,OAAAV,KAAA;QAESU,CAAA,CAAAxB,WAAA,GAAAP,KAAA,CAAAO,WAA4C;QAC5CwB,CAAA,CAAAC,MAAA,SAAY;QACXD,CAAA,CAAAT,UAAA,CAAAH,KAAc;QACpBY,CAAA,CAAEN,QAAA,GAAAM,CAAA,CAAAZ,KAAoB,KAAAd,KAAA,CAAAc,KAAA;QACtB,OAAWY,CAAA;MACX;IACA;IACO;MACThB,UAAC;MACHa,YAAA;MACOlB;IAAA,CACL;EAAA;AACA,CACA;AACF,SACFuB,YAAAC,IAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,QAAA;EACD,OAAAC,SAAA,IAAAC,kBAAA;;MArFCC,kBAAA;IAeMC,KAAA,EAAAC,cAAA,CAAAV,IAAA,CAAAxB,EAAA,CAAAmC,CAAA;EAAA,KAfAL,SAAO,QAAAC,kBAAI,CAAAK,QAAA,QAAAC,UAAA,CAAAb,IAAA,CAAAnB,UAAA,GAAAS,IAAA,EAAAK,KAAA;IAAA,OAAAW,SAAA,IAAAC,kBAAA;;MACfE,KAAA,EAAAC,cAAA,EAaMV,IAAA,CAAAxB,EAAA,CAAAmC,CAAA,oBAAAX,IAAA,CAAAxB,EAAA,CAAAsC,EAAA,UAAAxB,IAAA,CAAAyB,MAAA,SAbA;QAAAxB,QAAO,EAAAD,IAAA,CAAAC;MAAA,EAAI;;IACf,IAWMiB,kBAAA;MAAAQ,KAAA,EAAAC,cAAA;QAAAC,eAAA,EAAA5B,IAAA,CAAAL;MAAA;IAAA,CAVoB,W;EAUpB,CATH,SAAiB,KACZ;AAAkB;AAAoG,IAAAkC,SAAA,kBAAAC,WAAA,CAAAxD,SAAA,cAAAmC,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}