{"ast": null, "code": "const selectGroupKey = Symbol(\"ElSelectGroup\");\nconst selectKey = Symbol(\"ElSelect\");\nexport { selectGroupKey, selectKey };", "map": {"version": 3, "names": ["selectGroupKey", "Symbol", "<PERSON><PERSON><PERSON>"], "sources": ["../../../../../../packages/components/select/src/token.ts"], "sourcesContent": ["import type { InjectionKey } from 'vue'\nimport type { SelectContext, SelectGroupContext } from './type'\n\n// For individual build sharing injection key, we had to make `Symbol` to string\nexport const selectGroupKey: InjectionKey<SelectGroupContext> =\n  Symbol('ElSelectGroup')\n\nexport const selectKey: InjectionKey<SelectContext> = Symbol('ElSelect')\n"], "mappings": "AAAY,MAACA,cAAc,GAAGC,MAAM,CAAC,eAAe;AACxC,MAACC,SAAS,GAAGD,MAAM,CAAC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}