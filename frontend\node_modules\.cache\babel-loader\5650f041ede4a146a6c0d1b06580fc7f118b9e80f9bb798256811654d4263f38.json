{"ast": null, "code": "import { defineComponent, useSlots, ref, computed, openBlock, createBlock, Transition, unref, withCtx, withDirectives, createElementVNode, normalizeClass, renderSlot, resolveDynamicComponent, createCommentVNode, createElementBlock, createTextVNode, toDisplayString, Fragment, createVNode, vShow } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { alertProps, alertEmits } from './alert.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { TypeComponentsMap, TypeComponents } from '../../../utils/vue/icon.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElAlert\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: alertProps,\n  emits: alertEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const {\n      Close\n    } = TypeComponents;\n    const slots = useSlots();\n    const ns = useNamespace(\"alert\");\n    const visible = ref(true);\n    const iconComponent = computed(() => TypeComponentsMap[props.type]);\n    const hasDesc = computed(() => !!(props.description || slots.default));\n    const close = evt => {\n      visible.value = false;\n      emit(\"close\", evt);\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(Transition, {\n        name: unref(ns).b(\"fade\"),\n        persisted: \"\"\n      }, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"div\", {\n          class: normalizeClass([unref(ns).b(), unref(ns).m(_ctx.type), unref(ns).is(\"center\", _ctx.center), unref(ns).is(_ctx.effect)]),\n          role: \"alert\"\n        }, [_ctx.showIcon && (_ctx.$slots.icon || unref(iconComponent)) ? (openBlock(), createBlock(unref(ElIcon), {\n          key: 0,\n          class: normalizeClass([unref(ns).e(\"icon\"), {\n            [unref(ns).is(\"big\")]: unref(hasDesc)\n          }])\n        }, {\n          default: withCtx(() => [renderSlot(_ctx.$slots, \"icon\", {}, () => [(openBlock(), createBlock(resolveDynamicComponent(unref(iconComponent))))])]),\n          _: 3\n        }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).e(\"content\"))\n        }, [_ctx.title || _ctx.$slots.title ? (openBlock(), createElementBlock(\"span\", {\n          key: 0,\n          class: normalizeClass([unref(ns).e(\"title\"), {\n            \"with-description\": unref(hasDesc)\n          }])\n        }, [renderSlot(_ctx.$slots, \"title\", {}, () => [createTextVNode(toDisplayString(_ctx.title), 1)])], 2)) : createCommentVNode(\"v-if\", true), unref(hasDesc) ? (openBlock(), createElementBlock(\"p\", {\n          key: 1,\n          class: normalizeClass(unref(ns).e(\"description\"))\n        }, [renderSlot(_ctx.$slots, \"default\", {}, () => [createTextVNode(toDisplayString(_ctx.description), 1)])], 2)) : createCommentVNode(\"v-if\", true), _ctx.closable ? (openBlock(), createElementBlock(Fragment, {\n          key: 2\n        }, [_ctx.closeText ? (openBlock(), createElementBlock(\"div\", {\n          key: 0,\n          class: normalizeClass([unref(ns).e(\"close-btn\"), unref(ns).is(\"customed\")]),\n          onClick: close\n        }, toDisplayString(_ctx.closeText), 3)) : (openBlock(), createBlock(unref(ElIcon), {\n          key: 1,\n          class: normalizeClass(unref(ns).e(\"close-btn\")),\n          onClick: close\n        }, {\n          default: withCtx(() => [createVNode(unref(Close))]),\n          _: 1\n        }, 8, [\"class\"]))], 64)) : createCommentVNode(\"v-if\", true)], 2)], 2), [[vShow, visible.value]])]),\n        _: 3\n      }, 8, [\"name\"]);\n    };\n  }\n});\nvar Alert = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"alert.vue\"]]);\nexport { Alert as default };", "map": {"version": 3, "names": ["name", "Close", "TypeComponents", "slots", "useSlots", "ns", "useNamespace", "visible", "ref", "iconComponent", "computed", "TypeComponentsMap", "props", "type", "hasDesc", "description", "default", "close", "evt", "value", "emit"], "sources": ["../../../../../../packages/components/alert/src/alert.vue"], "sourcesContent": ["<template>\n  <transition :name=\"ns.b('fade')\">\n    <div\n      v-show=\"visible\"\n      :class=\"[ns.b(), ns.m(type), ns.is('center', center), ns.is(effect)]\"\n      role=\"alert\"\n    >\n      <el-icon\n        v-if=\"showIcon && ($slots.icon || iconComponent)\"\n        :class=\"[ns.e('icon'), { [ns.is('big')]: hasDesc }]\"\n      >\n        <slot name=\"icon\">\n          <component :is=\"iconComponent\" />\n        </slot>\n      </el-icon>\n\n      <div :class=\"ns.e('content')\">\n        <span\n          v-if=\"title || $slots.title\"\n          :class=\"[ns.e('title'), { 'with-description': hasDesc }]\"\n        >\n          <slot name=\"title\">{{ title }}</slot>\n        </span>\n        <p v-if=\"hasDesc\" :class=\"ns.e('description')\">\n          <slot>\n            {{ description }}\n          </slot>\n        </p>\n        <template v-if=\"closable\">\n          <div\n            v-if=\"closeText\"\n            :class=\"[ns.e('close-btn'), ns.is('customed')]\"\n            @click=\"close\"\n          >\n            {{ closeText }}\n          </div>\n          <el-icon v-else :class=\"ns.e('close-btn')\" @click=\"close\">\n            <Close />\n          </el-icon>\n        </template>\n      </div>\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, useSlots } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { TypeComponents, TypeComponentsMap } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { alertEmits, alertProps } from './alert'\n\nconst { Close } = TypeComponents\n\ndefineOptions({\n  name: 'ElAlert',\n})\n\nconst props = defineProps(alertProps)\nconst emit = defineEmits(alertEmits)\nconst slots = useSlots()\n\nconst ns = useNamespace('alert')\n\nconst visible = ref(true)\n\nconst iconComponent = computed(() => TypeComponentsMap[props.type])\n\nconst hasDesc = computed(() => !!(props.description || slots.default))\n\nconst close = (evt: MouseEvent) => {\n  visible.value = false\n  emit('close', evt)\n}\n</script>\n"], "mappings": ";;;;;;mCAsDc;EACZA,IAAM;AACR;;;;;;;;;IAJM;MAAEC;IAAA,CAAU,GAAAC,cAAA;IAQlB,MAAMC,KAAA,GAAQC,QAAS;IAEjB,MAAAC,EAAA,GAAKC,YAAA,CAAa,OAAO;IAEzB,MAAAC,OAAA,GAAUC,GAAA,CAAI,IAAI;IAExB,MAAMC,aAAA,GAAgBC,QAAS,OAAMC,iBAAkB,CAAAC,KAAA,CAAMC,IAAI,CAAC;IAE5D,MAAAC,OAAA,GAAUJ,QAAA,CAAS,MAAM,CAAC,EAAEE,KAAM,CAAAG,WAAA,IAAeZ,KAAA,CAAMa,OAAQ;IAE/D,MAAAC,KAAA,GAASC,GAAoB;MACjCX,OAAA,CAAQY,KAAQ;MAChBC,IAAA,CAAK,SAASF,GAAG;IAAA,CACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}