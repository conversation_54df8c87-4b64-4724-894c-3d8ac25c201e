{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { ref, computed, unref, watch, nextTick } from 'vue';\nimport dayjs from 'dayjs';\nimport { flatten } from 'lodash-unified';\nimport { buildPickerTable } from '../utils.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { castArray } from '../../../../utils/arrays.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isArray } from '@vue/shared';\nconst isNormalDay = (type = \"\") => {\n  return [\"normal\", \"today\"].includes(type);\n};\nconst useBasicDateTable = (props, emit) => {\n  const {\n    lang\n  } = useLocale();\n  const tbodyRef = ref();\n  const currentCellRef = ref();\n  const lastRow = ref();\n  const lastColumn = ref();\n  const tableRows = ref([[], [], [], [], [], []]);\n  let focusWithClick = false;\n  const firstDayOfWeek = props.date.$locale().weekStart || 7;\n  const WEEKS_CONSTANT = props.date.locale(\"en\").localeData().weekdaysShort().map(_ => _.toLowerCase());\n  const offsetDay = computed(() => {\n    return firstDayOfWeek > 3 ? 7 - firstDayOfWeek : -firstDayOfWeek;\n  });\n  const startDate = computed(() => {\n    const startDayOfMonth = props.date.startOf(\"month\");\n    return startDayOfMonth.subtract(startDayOfMonth.day() || 7, \"day\");\n  });\n  const WEEKS = computed(() => {\n    return WEEKS_CONSTANT.concat(WEEKS_CONSTANT).slice(firstDayOfWeek, firstDayOfWeek + 7);\n  });\n  const hasCurrent = computed(() => {\n    return flatten(unref(rows)).some(row => {\n      return row.isCurrent;\n    });\n  });\n  const days = computed(() => {\n    const startOfMonth = props.date.startOf(\"month\");\n    const startOfMonthDay = startOfMonth.day() || 7;\n    const dateCountOfMonth = startOfMonth.daysInMonth();\n    const dateCountOfLastMonth = startOfMonth.subtract(1, \"month\").daysInMonth();\n    return {\n      startOfMonthDay,\n      dateCountOfMonth,\n      dateCountOfLastMonth\n    };\n  });\n  const selectedDate = computed(() => {\n    return props.selectionMode === \"dates\" ? castArray(props.parsedValue) : [];\n  });\n  const setDateText = (cell, {\n    count,\n    rowIndex,\n    columnIndex\n  }) => {\n    const {\n      startOfMonthDay,\n      dateCountOfMonth,\n      dateCountOfLastMonth\n    } = unref(days);\n    const offset = unref(offsetDay);\n    if (rowIndex >= 0 && rowIndex <= 1) {\n      const numberOfDaysFromPreviousMonth = startOfMonthDay + offset < 0 ? 7 + startOfMonthDay + offset : startOfMonthDay + offset;\n      if (columnIndex + rowIndex * 7 >= numberOfDaysFromPreviousMonth) {\n        cell.text = count;\n        return true;\n      } else {\n        cell.text = dateCountOfLastMonth - (numberOfDaysFromPreviousMonth - columnIndex % 7) + 1 + rowIndex * 7;\n        cell.type = \"prev-month\";\n      }\n    } else {\n      if (count <= dateCountOfMonth) {\n        cell.text = count;\n      } else {\n        cell.text = count - dateCountOfMonth;\n        cell.type = \"next-month\";\n      }\n      return true;\n    }\n    return false;\n  };\n  const setCellMetadata = (cell, {\n    columnIndex,\n    rowIndex\n  }, count) => {\n    const {\n      disabledDate,\n      cellClassName\n    } = props;\n    const _selectedDate = unref(selectedDate);\n    const shouldIncrement = setDateText(cell, {\n      count,\n      rowIndex,\n      columnIndex\n    });\n    const cellDate = cell.dayjs.toDate();\n    cell.selected = _selectedDate.find(d => d.isSame(cell.dayjs, \"day\"));\n    cell.isSelected = !!cell.selected;\n    cell.isCurrent = isCurrent(cell);\n    cell.disabled = disabledDate == null ? void 0 : disabledDate(cellDate);\n    cell.customClass = cellClassName == null ? void 0 : cellClassName(cellDate);\n    return shouldIncrement;\n  };\n  const setRowMetadata = row => {\n    if (props.selectionMode === \"week\") {\n      const [start, end] = props.showWeekNumber ? [1, 7] : [0, 6];\n      const isActive = isWeekActive(row[start + 1]);\n      row[start].inRange = isActive;\n      row[start].start = isActive;\n      row[end].inRange = isActive;\n      row[end].end = isActive;\n    }\n  };\n  const rows = computed(() => {\n    const {\n      minDate,\n      maxDate,\n      rangeState,\n      showWeekNumber\n    } = props;\n    const offset = unref(offsetDay);\n    const rows_ = unref(tableRows);\n    const dateUnit = \"day\";\n    let count = 1;\n    if (showWeekNumber) {\n      for (let rowIndex = 0; rowIndex < 6; rowIndex++) {\n        if (!rows_[rowIndex][0]) {\n          rows_[rowIndex][0] = {\n            type: \"week\",\n            text: unref(startDate).add(rowIndex * 7 + 1, dateUnit).week()\n          };\n        }\n      }\n    }\n    buildPickerTable({\n      row: 6,\n      column: 7\n    }, rows_, {\n      startDate: minDate,\n      columnIndexOffset: showWeekNumber ? 1 : 0,\n      nextEndDate: rangeState.endDate || maxDate || rangeState.selecting && minDate || null,\n      now: dayjs().locale(unref(lang)).startOf(dateUnit),\n      unit: dateUnit,\n      relativeDateGetter: idx => unref(startDate).add(idx - offset, dateUnit),\n      setCellMetadata: (...args) => {\n        if (setCellMetadata(...args, count)) {\n          count += 1;\n        }\n      },\n      setRowMetadata\n    });\n    return rows_;\n  });\n  watch(() => props.date, async () => {\n    var _a;\n    if ((_a = unref(tbodyRef)) == null ? void 0 : _a.contains(document.activeElement)) {\n      await nextTick();\n      await focus();\n    }\n  });\n  const focus = async () => {\n    var _a;\n    return (_a = unref(currentCellRef)) == null ? void 0 : _a.focus();\n  };\n  const isCurrent = cell => {\n    return props.selectionMode === \"date\" && isNormalDay(cell.type) && cellMatchesDate(cell, props.parsedValue);\n  };\n  const cellMatchesDate = (cell, date) => {\n    if (!date) return false;\n    return dayjs(date).locale(unref(lang)).isSame(props.date.date(Number(cell.text)), \"day\");\n  };\n  const getDateOfCell = (row, column) => {\n    const offsetFromStart = row * 7 + (column - (props.showWeekNumber ? 1 : 0)) - unref(offsetDay);\n    return unref(startDate).add(offsetFromStart, \"day\");\n  };\n  const handleMouseMove = event => {\n    var _a;\n    if (!props.rangeState.selecting) return;\n    let target = event.target;\n    if (target.tagName === \"SPAN\") {\n      target = (_a = target.parentNode) == null ? void 0 : _a.parentNode;\n    }\n    if (target.tagName === \"DIV\") {\n      target = target.parentNode;\n    }\n    if (target.tagName !== \"TD\") return;\n    const row = target.parentNode.rowIndex - 1;\n    const column = target.cellIndex;\n    if (unref(rows)[row][column].disabled) return;\n    if (row !== unref(lastRow) || column !== unref(lastColumn)) {\n      lastRow.value = row;\n      lastColumn.value = column;\n      emit(\"changerange\", {\n        selecting: true,\n        endDate: getDateOfCell(row, column)\n      });\n    }\n  };\n  const isSelectedCell = cell => {\n    return !unref(hasCurrent) && (cell == null ? void 0 : cell.text) === 1 && cell.type === \"normal\" || cell.isCurrent;\n  };\n  const handleFocus = event => {\n    if (focusWithClick || unref(hasCurrent) || props.selectionMode !== \"date\") return;\n    handlePickDate(event, true);\n  };\n  const handleMouseDown = event => {\n    const target = event.target.closest(\"td\");\n    if (!target) return;\n    focusWithClick = true;\n  };\n  const handleMouseUp = event => {\n    const target = event.target.closest(\"td\");\n    if (!target) return;\n    focusWithClick = false;\n  };\n  const handleRangePick = newDate => {\n    if (!props.rangeState.selecting || !props.minDate) {\n      emit(\"pick\", {\n        minDate: newDate,\n        maxDate: null\n      });\n      emit(\"select\", true);\n    } else {\n      if (newDate >= props.minDate) {\n        emit(\"pick\", {\n          minDate: props.minDate,\n          maxDate: newDate\n        });\n      } else {\n        emit(\"pick\", {\n          minDate: newDate,\n          maxDate: props.minDate\n        });\n      }\n      emit(\"select\", false);\n    }\n  };\n  const handleWeekPick = newDate => {\n    const weekNumber = newDate.week();\n    const value = `${newDate.year()}w${weekNumber}`;\n    emit(\"pick\", {\n      year: newDate.year(),\n      week: weekNumber,\n      value,\n      date: newDate.startOf(\"week\")\n    });\n  };\n  const handleDatesPick = (newDate, selected) => {\n    const newValue = selected ? castArray(props.parsedValue).filter(d => (d == null ? void 0 : d.valueOf()) !== newDate.valueOf()) : castArray(props.parsedValue).concat([newDate]);\n    emit(\"pick\", newValue);\n  };\n  const handlePickDate = (event, isKeyboardMovement = false) => {\n    const target = event.target.closest(\"td\");\n    if (!target) return;\n    const row = target.parentNode.rowIndex - 1;\n    const column = target.cellIndex;\n    const cell = unref(rows)[row][column];\n    if (cell.disabled || cell.type === \"week\") return;\n    const newDate = getDateOfCell(row, column);\n    switch (props.selectionMode) {\n      case \"range\":\n        {\n          handleRangePick(newDate);\n          break;\n        }\n      case \"date\":\n        {\n          emit(\"pick\", newDate, isKeyboardMovement);\n          break;\n        }\n      case \"week\":\n        {\n          handleWeekPick(newDate);\n          break;\n        }\n      case \"dates\":\n        {\n          handleDatesPick(newDate, !!cell.selected);\n          break;\n        }\n    }\n  };\n  const isWeekActive = cell => {\n    if (props.selectionMode !== \"week\") return false;\n    let newDate = props.date.startOf(\"day\");\n    if (cell.type === \"prev-month\") {\n      newDate = newDate.subtract(1, \"month\");\n    }\n    if (cell.type === \"next-month\") {\n      newDate = newDate.add(1, \"month\");\n    }\n    newDate = newDate.date(Number.parseInt(cell.text, 10));\n    if (props.parsedValue && !isArray(props.parsedValue)) {\n      const dayOffset = (props.parsedValue.day() - firstDayOfWeek + 7) % 7 - 1;\n      const weekDate = props.parsedValue.subtract(dayOffset, \"day\");\n      return weekDate.isSame(newDate, \"day\");\n    }\n    return false;\n  };\n  return {\n    WEEKS,\n    rows,\n    tbodyRef,\n    currentCellRef,\n    focus,\n    isCurrent,\n    isWeekActive,\n    isSelectedCell,\n    handlePickDate,\n    handleMouseUp,\n    handleMouseDown,\n    handleMouseMove,\n    handleFocus\n  };\n};\nconst useBasicDateTableDOM = (props, {\n  isCurrent,\n  isWeekActive\n}) => {\n  const ns = useNamespace(\"date-table\");\n  const {\n    t\n  } = useLocale();\n  const tableKls = computed(() => [ns.b(), {\n    \"is-week-mode\": props.selectionMode === \"week\"\n  }]);\n  const tableLabel = computed(() => t(\"el.datepicker.dateTablePrompt\"));\n  const weekLabel = computed(() => t(\"el.datepicker.week\"));\n  const getCellClasses = cell => {\n    const classes = [];\n    if (isNormalDay(cell.type) && !cell.disabled) {\n      classes.push(\"available\");\n      if (cell.type === \"today\") {\n        classes.push(\"today\");\n      }\n    } else {\n      classes.push(cell.type);\n    }\n    if (isCurrent(cell)) {\n      classes.push(\"current\");\n    }\n    if (cell.inRange && (isNormalDay(cell.type) || props.selectionMode === \"week\")) {\n      classes.push(\"in-range\");\n      if (cell.start) {\n        classes.push(\"start-date\");\n      }\n      if (cell.end) {\n        classes.push(\"end-date\");\n      }\n    }\n    if (cell.disabled) {\n      classes.push(\"disabled\");\n    }\n    if (cell.selected) {\n      classes.push(\"selected\");\n    }\n    if (cell.customClass) {\n      classes.push(cell.customClass);\n    }\n    return classes.join(\" \");\n  };\n  const getRowKls = cell => [ns.e(\"row\"), {\n    current: isWeekActive(cell)\n  }];\n  return {\n    tableKls,\n    tableLabel,\n    weekLabel,\n    getCellClasses,\n    getRowKls,\n    t\n  };\n};\nexport { useBasicDateTable, useBasicDateTableDOM };", "map": {"version": 3, "names": ["isNormalDay", "type", "includes", "useBasicDateTable", "props", "emit", "lang", "useLocale", "tbodyRef", "ref", "currentCellRef", "lastRow", "lastColumn", "tableRows", "focusWithClick", "firstDayOfWeek", "date", "$locale", "weekStart", "WEEKS_CONSTANT", "locale", "localeData", "weekdaysShort", "map", "_", "toLowerCase", "offsetDay", "computed", "startDate", "startDayOfMonth", "startOf", "subtract", "day", "WEEKS", "concat", "slice", "has<PERSON><PERSON>rent", "flatten", "unref", "rows", "some", "row", "isCurrent", "days", "startOfMonth", "startOfMonthDay", "dateCountOfMonth", "daysInMonth", "dateCountOfLastMonth", "selectedDate", "selectionMode", "<PERSON><PERSON><PERSON><PERSON>", "parsedValue", "setDateText", "cell", "count", "rowIndex", "columnIndex", "offset", "numberOfDaysFromPreviousMonth", "text", "setCellMetadata", "disabledDate", "cellClassName", "_selectedDate", "shouldIncrement", "cellDate", "dayjs", "toDate", "selected", "find", "d", "isSame", "isSelected", "disabled", "customClass", "setRowMetadata", "start", "end", "showWeekNumber", "isActive", "isWeekActive", "inRange", "minDate", "maxDate", "rangeState", "rows_", "dateUnit", "add", "week", "buildPickerTable", "column", "columnIndexOffset", "nextEndDate", "endDate", "selecting", "now", "unit", "relativeDateGetter", "idx", "args", "watch", "_a", "contains", "document", "activeElement", "nextTick", "focus", "cellMatchesDate", "Number", "getDateOfCell", "offsetFromStart", "handleMouseMove", "event", "target", "tagName", "parentNode", "cellIndex", "value", "isSelectedCell", "handleFocus", "handlePickDate", "handleMouseDown", "closest", "handleMouseUp", "handleRangePick", "newDate", "handleWeekPick", "weekNumber", "year", "handleDatesPick", "newValue", "filter", "valueOf", "isKeyboardMovement", "parseInt", "isArray", "dayOffset", "weekDate", "useBasicDateTableDOM", "ns", "useNamespace", "t", "tableKls", "b", "tableLabel", "week<PERSON><PERSON><PERSON>", "getCellClasses", "classes", "push", "join", "getRowKls", "e", "current"], "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-basic-date-table.ts"], "sourcesContent": ["import { computed, nextTick, ref, unref, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { flatten } from 'lodash-unified'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { castArray, isArray } from '@element-plus/utils'\nimport { buildPickerTable } from '../utils'\n\nimport type { SetupContext } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type { DateCell } from '../date-picker.type'\nimport type {\n  BasicDateTableEmits,\n  BasicDateTableProps,\n} from '../props/basic-date-table'\n\nconst isNormalDay = (type = '') => {\n  return ['normal', 'today'].includes(type)\n}\n\nexport const useBasicDateTable = (\n  props: BasicDateTableProps,\n  emit: SetupContext<BasicDateTableEmits>['emit']\n) => {\n  const { lang } = useLocale()\n  const tbodyRef = ref<HTMLElement>()\n  const currentCellRef = ref<HTMLElement>()\n  // data\n  const lastRow = ref<number>()\n  const lastColumn = ref<number>()\n  const tableRows = ref<DateCell[][]>([[], [], [], [], [], []])\n\n  let focusWithClick = false\n\n  // todo better way to get Day.js locale object\n  const firstDayOfWeek = (props.date as any).$locale().weekStart || 7\n  const WEEKS_CONSTANT = props.date\n    .locale('en')\n    .localeData()\n    .weekdaysShort()\n    .map((_) => _.toLowerCase())\n\n  const offsetDay = computed(() => {\n    // Sunday 7(0), cal the left and right offset days, 3217654, such as Monday is -1, the is to adjust the position of the first two rows of dates\n    return firstDayOfWeek > 3 ? 7 - firstDayOfWeek : -firstDayOfWeek\n  })\n\n  const startDate = computed(() => {\n    const startDayOfMonth = props.date.startOf('month')\n    return startDayOfMonth.subtract(startDayOfMonth.day() || 7, 'day')\n  })\n\n  const WEEKS = computed(() => {\n    return WEEKS_CONSTANT.concat(WEEKS_CONSTANT).slice(\n      firstDayOfWeek,\n      firstDayOfWeek + 7\n    )\n  })\n\n  const hasCurrent = computed<boolean>(() => {\n    return flatten(unref(rows)).some((row) => {\n      return row.isCurrent\n    })\n  })\n\n  const days = computed(() => {\n    const startOfMonth = props.date.startOf('month')\n    const startOfMonthDay = startOfMonth.day() || 7 // day of first day\n    const dateCountOfMonth = startOfMonth.daysInMonth()\n\n    const dateCountOfLastMonth = startOfMonth.subtract(1, 'month').daysInMonth()\n\n    return {\n      startOfMonthDay,\n      dateCountOfMonth,\n      dateCountOfLastMonth,\n    }\n  })\n\n  const selectedDate = computed(() => {\n    return props.selectionMode === 'dates'\n      ? (castArray(props.parsedValue) as Dayjs[])\n      : ([] as Dayjs[])\n  })\n\n  // Return value indicates should the counter be incremented\n  type CellCoordinate = { columnIndex: number; rowIndex: number }\n  type CellMeta = CellCoordinate & {\n    count: number\n  }\n  const setDateText = (\n    cell: DateCell,\n    { count, rowIndex, columnIndex }: CellMeta\n  ): boolean => {\n    const { startOfMonthDay, dateCountOfMonth, dateCountOfLastMonth } =\n      unref(days)\n    const offset = unref(offsetDay)\n    if (rowIndex >= 0 && rowIndex <= 1) {\n      const numberOfDaysFromPreviousMonth =\n        startOfMonthDay + offset < 0\n          ? 7 + startOfMonthDay + offset\n          : startOfMonthDay + offset\n\n      if (columnIndex + rowIndex * 7 >= numberOfDaysFromPreviousMonth) {\n        cell.text = count\n        return true\n      } else {\n        cell.text =\n          dateCountOfLastMonth -\n          (numberOfDaysFromPreviousMonth - (columnIndex % 7)) +\n          1 +\n          rowIndex * 7\n        cell.type = 'prev-month'\n      }\n    } else {\n      if (count <= dateCountOfMonth) {\n        cell.text = count\n      } else {\n        cell.text = count - dateCountOfMonth\n        cell.type = 'next-month'\n      }\n      return true\n    }\n    return false\n  }\n\n  const setCellMetadata = (\n    cell: DateCell,\n    { columnIndex, rowIndex }: CellCoordinate,\n    count: number\n  ) => {\n    const { disabledDate, cellClassName } = props\n    const _selectedDate = unref(selectedDate)\n    const shouldIncrement = setDateText(cell, { count, rowIndex, columnIndex })\n\n    const cellDate = cell.dayjs!.toDate()\n    cell.selected = _selectedDate.find((d) => d.isSame(cell.dayjs, 'day'))\n    cell.isSelected = !!cell.selected\n    cell.isCurrent = isCurrent(cell)\n    cell.disabled = disabledDate?.(cellDate)\n    cell.customClass = cellClassName?.(cellDate)\n    return shouldIncrement\n  }\n\n  const setRowMetadata = (row: DateCell[]) => {\n    if (props.selectionMode === 'week') {\n      const [start, end] = props.showWeekNumber ? [1, 7] : [0, 6]\n      const isActive = isWeekActive(row[start + 1])\n      row[start].inRange = isActive\n      row[start].start = isActive\n      row[end].inRange = isActive\n      row[end].end = isActive\n    }\n  }\n\n  const rows = computed(() => {\n    const { minDate, maxDate, rangeState, showWeekNumber } = props\n\n    const offset = unref(offsetDay)\n    const rows_ = unref(tableRows)\n    const dateUnit = 'day'\n    let count = 1\n\n    if (showWeekNumber) {\n      for (let rowIndex = 0; rowIndex < 6; rowIndex++) {\n        if (!rows_[rowIndex][0]) {\n          rows_[rowIndex][0] = {\n            type: 'week',\n            text: unref(startDate)\n              .add(rowIndex * 7 + 1, dateUnit)\n              .week(),\n          }\n        }\n      }\n    }\n\n    buildPickerTable({ row: 6, column: 7 }, rows_, {\n      startDate: minDate,\n      columnIndexOffset: showWeekNumber ? 1 : 0,\n      nextEndDate:\n        rangeState.endDate ||\n        maxDate ||\n        (rangeState.selecting && minDate) ||\n        null,\n      now: dayjs().locale(unref(lang)).startOf(dateUnit),\n      unit: dateUnit,\n      relativeDateGetter: (idx: number) =>\n        unref(startDate).add(idx - offset, dateUnit),\n      setCellMetadata: (...args) => {\n        if (setCellMetadata(...args, count)) {\n          count += 1\n        }\n      },\n\n      setRowMetadata,\n    })\n\n    return rows_\n  })\n\n  watch(\n    () => props.date,\n    async () => {\n      if (unref(tbodyRef)?.contains(document.activeElement)) {\n        await nextTick()\n        await focus()\n        // currentCellRef.value?.focus()\n      }\n    }\n  )\n\n  const focus = async () => unref(currentCellRef)?.focus()\n\n  const isCurrent = (cell: DateCell): boolean => {\n    return (\n      props.selectionMode === 'date' &&\n      isNormalDay(cell.type) &&\n      cellMatchesDate(cell, props.parsedValue as Dayjs)\n    )\n  }\n\n  const cellMatchesDate = (cell: DateCell, date: Dayjs) => {\n    if (!date) return false\n    return dayjs(date)\n      .locale(unref(lang))\n      .isSame(props.date.date(Number(cell.text)), 'day')\n  }\n\n  const getDateOfCell = (row: number, column: number) => {\n    const offsetFromStart =\n      row * 7 + (column - (props.showWeekNumber ? 1 : 0)) - unref(offsetDay)\n    return unref(startDate).add(offsetFromStart, 'day')\n  }\n\n  const handleMouseMove = (event: MouseEvent) => {\n    if (!props.rangeState.selecting) return\n\n    let target = event.target as HTMLElement\n    if (target.tagName === 'SPAN') {\n      target = target.parentNode?.parentNode as HTMLElement\n    }\n    if (target.tagName === 'DIV') {\n      target = target.parentNode as HTMLElement\n    }\n    if (target.tagName !== 'TD') return\n\n    const row = (target.parentNode as HTMLTableRowElement).rowIndex - 1\n    const column = (target as HTMLTableCellElement).cellIndex\n\n    // can not select disabled date\n    if (unref(rows)[row][column].disabled) return\n\n    // only update rangeState when mouse moves to a new cell\n    // this avoids frequent Date object creation and improves performance\n    if (row !== unref(lastRow) || column !== unref(lastColumn)) {\n      lastRow.value = row\n      lastColumn.value = column\n      emit('changerange', {\n        selecting: true,\n        endDate: getDateOfCell(row, column),\n      })\n    }\n  }\n\n  const isSelectedCell = (cell: DateCell) => {\n    return (\n      (!unref(hasCurrent) && cell?.text === 1 && cell.type === 'normal') ||\n      cell.isCurrent\n    )\n  }\n\n  const handleFocus = (event: FocusEvent) => {\n    if (focusWithClick || unref(hasCurrent) || props.selectionMode !== 'date')\n      return\n    handlePickDate(event, true)\n  }\n\n  const handleMouseDown = (event: MouseEvent) => {\n    const target = (event.target as HTMLElement).closest('td')\n    if (!target) return\n    focusWithClick = true\n  }\n\n  const handleMouseUp = (event: MouseEvent) => {\n    const target = (event.target as HTMLElement).closest('td')\n    if (!target) return\n    focusWithClick = false\n  }\n\n  const handleRangePick = (newDate: Dayjs) => {\n    if (!props.rangeState.selecting || !props.minDate) {\n      emit('pick', { minDate: newDate, maxDate: null })\n      emit('select', true)\n    } else {\n      if (newDate >= props.minDate) {\n        emit('pick', { minDate: props.minDate, maxDate: newDate })\n      } else {\n        emit('pick', { minDate: newDate, maxDate: props.minDate })\n      }\n      emit('select', false)\n    }\n  }\n\n  const handleWeekPick = (newDate: Dayjs) => {\n    const weekNumber = newDate.week()\n    const value = `${newDate.year()}w${weekNumber}`\n    emit('pick', {\n      year: newDate.year(),\n      week: weekNumber,\n      value,\n      date: newDate.startOf('week'),\n    })\n  }\n\n  const handleDatesPick = (newDate: Dayjs, selected: boolean) => {\n    const newValue = selected\n      ? castArray(props.parsedValue).filter(\n          (d) => d?.valueOf() !== newDate.valueOf()\n        )\n      : castArray(props.parsedValue).concat([newDate])\n    emit('pick', newValue)\n  }\n\n  const handlePickDate = (\n    event: FocusEvent | MouseEvent,\n    isKeyboardMovement = false\n  ) => {\n    const target = (event.target as HTMLElement).closest('td')\n\n    if (!target) return\n\n    const row = (target.parentNode as HTMLTableRowElement).rowIndex - 1\n    const column = (target as HTMLTableCellElement).cellIndex\n    const cell = unref(rows)[row][column]\n\n    if (cell.disabled || cell.type === 'week') return\n\n    const newDate = getDateOfCell(row, column)\n\n    switch (props.selectionMode) {\n      case 'range': {\n        handleRangePick(newDate)\n        break\n      }\n      case 'date': {\n        emit('pick', newDate, isKeyboardMovement)\n        break\n      }\n      case 'week': {\n        handleWeekPick(newDate)\n        break\n      }\n      case 'dates': {\n        handleDatesPick(newDate, !!cell.selected)\n        break\n      }\n      default: {\n        break\n      }\n    }\n  }\n\n  const isWeekActive = (cell: DateCell) => {\n    if (props.selectionMode !== 'week') return false\n    let newDate = props.date.startOf('day')\n\n    if (cell.type === 'prev-month') {\n      newDate = newDate.subtract(1, 'month')\n    }\n\n    if (cell.type === 'next-month') {\n      newDate = newDate.add(1, 'month')\n    }\n\n    newDate = newDate.date(Number.parseInt(cell.text as any, 10))\n\n    if (props.parsedValue && !isArray(props.parsedValue)) {\n      const dayOffset = ((props.parsedValue.day() - firstDayOfWeek + 7) % 7) - 1\n      const weekDate = props.parsedValue.subtract(dayOffset, 'day')\n      return weekDate.isSame(newDate, 'day')\n    }\n    return false\n  }\n\n  return {\n    WEEKS,\n    rows,\n    tbodyRef,\n    currentCellRef,\n\n    // cellMatchesDate,\n    // getDateOfCell,\n    focus,\n    isCurrent,\n    isWeekActive,\n    isSelectedCell,\n\n    handlePickDate,\n    handleMouseUp,\n    handleMouseDown,\n    handleMouseMove,\n    handleFocus,\n  }\n}\n\nexport const useBasicDateTableDOM = (\n  props: BasicDateTableProps,\n  {\n    isCurrent,\n    isWeekActive,\n  }: Pick<ReturnType<typeof useBasicDateTable>, 'isCurrent' | 'isWeekActive'>\n) => {\n  const ns = useNamespace('date-table')\n  const { t } = useLocale()\n\n  const tableKls = computed(() => [\n    ns.b(),\n    { 'is-week-mode': props.selectionMode === 'week' },\n  ])\n\n  const tableLabel = computed(() => t('el.datepicker.dateTablePrompt'))\n  const weekLabel = computed(() => t('el.datepicker.week'))\n\n  const getCellClasses = (cell: DateCell) => {\n    const classes: string[] = []\n    if (isNormalDay(cell.type) && !cell.disabled) {\n      classes.push('available')\n      if (cell.type === 'today') {\n        classes.push('today')\n      }\n    } else {\n      classes.push(cell.type!)\n    }\n\n    if (isCurrent(cell)) {\n      classes.push('current')\n    }\n\n    if (\n      cell.inRange &&\n      (isNormalDay(cell.type) || props.selectionMode === 'week')\n    ) {\n      classes.push('in-range')\n\n      if (cell.start) {\n        classes.push('start-date')\n      }\n\n      if (cell.end) {\n        classes.push('end-date')\n      }\n    }\n\n    if (cell.disabled) {\n      classes.push('disabled')\n    }\n\n    if (cell.selected) {\n      classes.push('selected')\n    }\n\n    if (cell.customClass) {\n      classes.push(cell.customClass)\n    }\n\n    return classes.join(' ')\n  }\n\n  const getRowKls = (cell: DateCell) => [\n    ns.e('row'),\n    { current: isWeekActive(cell) },\n  ]\n\n  return {\n    tableKls,\n    tableLabel,\n    weekLabel,\n\n    getCellClasses,\n    getRowKls,\n    t,\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;AAMA,MAAMA,WAAW,GAAGA,CAACC,IAAI,GAAG,EAAE,KAAK;EACjC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACD,IAAI,CAAC;AAC3C,CAAC;AACW,MAACE,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;EAChD,MAAM;IAAEC;EAAI,CAAE,GAAGC,SAAS,EAAE;EAC5B,MAAMC,QAAQ,GAAGC,GAAG,EAAE;EACtB,MAAMC,cAAc,GAAGD,GAAG,EAAE;EAC5B,MAAME,OAAO,GAAGF,GAAG,EAAE;EACrB,MAAMG,UAAU,GAAGH,GAAG,EAAE;EACxB,MAAMI,SAAS,GAAGJ,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAC/C,IAAIK,cAAc,GAAG,KAAK;EAC1B,MAAMC,cAAc,GAAGX,KAAK,CAACY,IAAI,CAACC,OAAO,EAAE,CAACC,SAAS,IAAI,CAAC;EAC1D,MAAMC,cAAc,GAAGf,KAAK,CAACY,IAAI,CAACI,MAAM,CAAC,IAAI,CAAC,CAACC,UAAU,EAAE,CAACC,aAAa,EAAE,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,EAAE,CAAC;EACvG,MAAMC,SAAS,GAAGC,QAAQ,CAAC,MAAM;IAC/B,OAAOZ,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGA,cAAc,GAAG,CAACA,cAAc;EACpE,CAAG,CAAC;EACF,MAAMa,SAAS,GAAGD,QAAQ,CAAC,MAAM;IAC/B,MAAME,eAAe,GAAGzB,KAAK,CAACY,IAAI,CAACc,OAAO,CAAC,OAAO,CAAC;IACnD,OAAOD,eAAe,CAACE,QAAQ,CAACF,eAAe,CAACG,GAAG,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC;EACtE,CAAG,CAAC;EACF,MAAMC,KAAK,GAAGN,QAAQ,CAAC,MAAM;IAC3B,OAAOR,cAAc,CAACe,MAAM,CAACf,cAAc,CAAC,CAACgB,KAAK,CAACpB,cAAc,EAAEA,cAAc,GAAG,CAAC,CAAC;EAC1F,CAAG,CAAC;EACF,MAAMqB,UAAU,GAAGT,QAAQ,CAAC,MAAM;IAChC,OAAOU,OAAO,CAACC,KAAK,CAACC,IAAI,CAAC,CAAC,CAACC,IAAI,CAAEC,GAAG,IAAK;MACxC,OAAOA,GAAG,CAACC,SAAS;IAC1B,CAAK,CAAC;EACN,CAAG,CAAC;EACF,MAAMC,IAAI,GAAGhB,QAAQ,CAAC,MAAM;IAC1B,MAAMiB,YAAY,GAAGxC,KAAK,CAACY,IAAI,CAACc,OAAO,CAAC,OAAO,CAAC;IAChD,MAAMe,eAAe,GAAGD,YAAY,CAACZ,GAAG,EAAE,IAAI,CAAC;IAC/C,MAAMc,gBAAgB,GAAGF,YAAY,CAACG,WAAW,EAAE;IACnD,MAAMC,oBAAoB,GAAGJ,YAAY,CAACb,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAACgB,WAAW,EAAE;IAC5E,OAAO;MACLF,eAAe;MACfC,gBAAgB;MAChBE;IACN,CAAK;EACL,CAAG,CAAC;EACF,MAAMC,YAAY,GAAGtB,QAAQ,CAAC,MAAM;IAClC,OAAOvB,KAAK,CAAC8C,aAAa,KAAK,OAAO,GAAGC,SAAS,CAAC/C,KAAK,CAACgD,WAAW,CAAC,GAAG,EAAE;EAC9E,CAAG,CAAC;EACF,MAAMC,WAAW,GAAGA,CAACC,IAAI,EAAE;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAW,CAAE,KAAK;IAC9D,MAAM;MAAEZ,eAAe;MAAEC,gBAAgB;MAAEE;IAAoB,CAAE,GAAGV,KAAK,CAACK,IAAI,CAAC;IAC/E,MAAMe,MAAM,GAAGpB,KAAK,CAACZ,SAAS,CAAC;IAC/B,IAAI8B,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC,EAAE;MAClC,MAAMG,6BAA6B,GAAGd,eAAe,GAAGa,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGb,eAAe,GAAGa,MAAM,GAAGb,eAAe,GAAGa,MAAM;MAC5H,IAAID,WAAW,GAAGD,QAAQ,GAAG,CAAC,IAAIG,6BAA6B,EAAE;QAC/DL,IAAI,CAACM,IAAI,GAAGL,KAAK;QACjB,OAAO,IAAI;MACnB,CAAO,MAAM;QACLD,IAAI,CAACM,IAAI,GAAGZ,oBAAoB,IAAIW,6BAA6B,GAAGF,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGD,QAAQ,GAAG,CAAC;QACvGF,IAAI,CAACrD,IAAI,GAAG,YAAY;MAChC;IACA,CAAK,MAAM;MACL,IAAIsD,KAAK,IAAIT,gBAAgB,EAAE;QAC7BQ,IAAI,CAACM,IAAI,GAAGL,KAAK;MACzB,CAAO,MAAM;QACLD,IAAI,CAACM,IAAI,GAAGL,KAAK,GAAGT,gBAAgB;QACpCQ,IAAI,CAACrD,IAAI,GAAG,YAAY;MAChC;MACM,OAAO,IAAI;IACjB;IACI,OAAO,KAAK;EAChB,CAAG;EACD,MAAM4D,eAAe,GAAGA,CAACP,IAAI,EAAE;IAAEG,WAAW;IAAED;EAAQ,CAAE,EAAED,KAAK,KAAK;IAClE,MAAM;MAAEO,YAAY;MAAEC;IAAa,CAAE,GAAG3D,KAAK;IAC7C,MAAM4D,aAAa,GAAG1B,KAAK,CAACW,YAAY,CAAC;IACzC,MAAMgB,eAAe,GAAGZ,WAAW,CAACC,IAAI,EAAE;MAAEC,KAAK;MAAEC,QAAQ;MAAEC;IAAW,CAAE,CAAC;IAC3E,MAAMS,QAAQ,GAAGZ,IAAI,CAACa,KAAK,CAACC,MAAM,EAAE;IACpCd,IAAI,CAACe,QAAQ,GAAGL,aAAa,CAACM,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAClB,IAAI,CAACa,KAAK,EAAE,KAAK,CAAC,CAAC;IACtEb,IAAI,CAACmB,UAAU,GAAG,CAAC,CAACnB,IAAI,CAACe,QAAQ;IACjCf,IAAI,CAACZ,SAAS,GAAGA,SAAS,CAACY,IAAI,CAAC;IAChCA,IAAI,CAACoB,QAAQ,GAAGZ,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACI,QAAQ,CAAC;IACtEZ,IAAI,CAACqB,WAAW,GAAGZ,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACG,QAAQ,CAAC;IAC3E,OAAOD,eAAe;EAC1B,CAAG;EACD,MAAMW,cAAc,GAAInC,GAAG,IAAK;IAC9B,IAAIrC,KAAK,CAAC8C,aAAa,KAAK,MAAM,EAAE;MAClC,MAAM,CAAC2B,KAAK,EAAEC,GAAG,CAAC,GAAG1E,KAAK,CAAC2E,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MAC3D,MAAMC,QAAQ,GAAGC,YAAY,CAACxC,GAAG,CAACoC,KAAK,GAAG,CAAC,CAAC,CAAC;MAC7CpC,GAAG,CAACoC,KAAK,CAAC,CAACK,OAAO,GAAGF,QAAQ;MAC7BvC,GAAG,CAACoC,KAAK,CAAC,CAACA,KAAK,GAAGG,QAAQ;MAC3BvC,GAAG,CAACqC,GAAG,CAAC,CAACI,OAAO,GAAGF,QAAQ;MAC3BvC,GAAG,CAACqC,GAAG,CAAC,CAACA,GAAG,GAAGE,QAAQ;IAC7B;EACA,CAAG;EACD,MAAMzC,IAAI,GAAGZ,QAAQ,CAAC,MAAM;IAC1B,MAAM;MAAEwD,OAAO;MAAEC,OAAO;MAAEC,UAAU;MAAEN;IAAc,CAAE,GAAG3E,KAAK;IAC9D,MAAMsD,MAAM,GAAGpB,KAAK,CAACZ,SAAS,CAAC;IAC/B,MAAM4D,KAAK,GAAGhD,KAAK,CAACzB,SAAS,CAAC;IAC9B,MAAM0E,QAAQ,GAAG,KAAK;IACtB,IAAIhC,KAAK,GAAG,CAAC;IACb,IAAIwB,cAAc,EAAE;MAClB,KAAK,IAAIvB,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAEA,QAAQ,EAAE,EAAE;QAC/C,IAAI,CAAC8B,KAAK,CAAC9B,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB8B,KAAK,CAAC9B,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG;YACnBvD,IAAI,EAAE,MAAM;YACZ2D,IAAI,EAAEtB,KAAK,CAACV,SAAS,CAAC,CAAC4D,GAAG,CAAChC,QAAQ,GAAG,CAAC,GAAG,CAAC,EAAE+B,QAAQ,CAAC,CAACE,IAAI;UACvE,CAAW;QACX;MACA;IACA;IACIC,gBAAgB,CAAC;MAAEjD,GAAG,EAAE,CAAC;MAAEkD,MAAM,EAAE;IAAC,CAAE,EAAEL,KAAK,EAAE;MAC7C1D,SAAS,EAAEuD,OAAO;MAClBS,iBAAiB,EAAEb,cAAc,GAAG,CAAC,GAAG,CAAC;MACzCc,WAAW,EAAER,UAAU,CAACS,OAAO,IAAIV,OAAO,IAAIC,UAAU,CAACU,SAAS,IAAIZ,OAAO,IAAI,IAAI;MACrFa,GAAG,EAAE7B,KAAK,EAAE,CAAC/C,MAAM,CAACkB,KAAK,CAAChC,IAAI,CAAC,CAAC,CAACwB,OAAO,CAACyD,QAAQ,CAAC;MAClDU,IAAI,EAAEV,QAAQ;MACdW,kBAAkB,EAAGC,GAAG,IAAK7D,KAAK,CAACV,SAAS,CAAC,CAAC4D,GAAG,CAACW,GAAG,GAAGzC,MAAM,EAAE6B,QAAQ,CAAC;MACzE1B,eAAe,EAAEA,CAAC,GAAGuC,IAAI,KAAK;QAC5B,IAAIvC,eAAe,CAAC,GAAGuC,IAAI,EAAE7C,KAAK,CAAC,EAAE;UACnCA,KAAK,IAAI,CAAC;QACpB;MACA,CAAO;MACDqB;IACN,CAAK,CAAC;IACF,OAAOU,KAAK;EAChB,CAAG,CAAC;EACFe,KAAK,CAAC,MAAMjG,KAAK,CAACY,IAAI,EAAE,YAAY;IAClC,IAAIsF,EAAE;IACN,IAAI,CAACA,EAAE,GAAGhE,KAAK,CAAC9B,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8F,EAAE,CAACC,QAAQ,CAACC,QAAQ,CAACC,aAAa,CAAC,EAAE;MACjF,MAAMC,QAAQ,EAAE;MAChB,MAAMC,KAAK,EAAE;IACnB;EACA,CAAG,CAAC;EACF,MAAMA,KAAK,GAAG,MAAAA,CAAA,KAAY;IACxB,IAAIL,EAAE;IACN,OAAO,CAACA,EAAE,GAAGhE,KAAK,CAAC5B,cAAc,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4F,EAAE,CAACK,KAAK,EAAE;EACrE,CAAG;EACD,MAAMjE,SAAS,GAAIY,IAAI,IAAK;IAC1B,OAAOlD,KAAK,CAAC8C,aAAa,KAAK,MAAM,IAAIlD,WAAW,CAACsD,IAAI,CAACrD,IAAI,CAAC,IAAI2G,eAAe,CAACtD,IAAI,EAAElD,KAAK,CAACgD,WAAW,CAAC;EAC/G,CAAG;EACD,MAAMwD,eAAe,GAAGA,CAACtD,IAAI,EAAEtC,IAAI,KAAK;IACtC,IAAI,CAACA,IAAI,EACP,OAAO,KAAK;IACd,OAAOmD,KAAK,CAACnD,IAAI,CAAC,CAACI,MAAM,CAACkB,KAAK,CAAChC,IAAI,CAAC,CAAC,CAACkE,MAAM,CAACpE,KAAK,CAACY,IAAI,CAACA,IAAI,CAAC6F,MAAM,CAACvD,IAAI,CAACM,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC;EAC5F,CAAG;EACD,MAAMkD,aAAa,GAAGA,CAACrE,GAAG,EAAEkD,MAAM,KAAK;IACrC,MAAMoB,eAAe,GAAGtE,GAAG,GAAG,CAAC,IAAIkD,MAAM,IAAIvF,KAAK,CAAC2E,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGzC,KAAK,CAACZ,SAAS,CAAC;IAC9F,OAAOY,KAAK,CAACV,SAAS,CAAC,CAAC4D,GAAG,CAACuB,eAAe,EAAE,KAAK,CAAC;EACvD,CAAG;EACD,MAAMC,eAAe,GAAIC,KAAK,IAAK;IACjC,IAAIX,EAAE;IACN,IAAI,CAAClG,KAAK,CAACiF,UAAU,CAACU,SAAS,EAC7B;IACF,IAAImB,MAAM,GAAGD,KAAK,CAACC,MAAM;IACzB,IAAIA,MAAM,CAACC,OAAO,KAAK,MAAM,EAAE;MAC7BD,MAAM,GAAG,CAACZ,EAAE,GAAGY,MAAM,CAACE,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGd,EAAE,CAACc,UAAU;IACxE;IACI,IAAIF,MAAM,CAACC,OAAO,KAAK,KAAK,EAAE;MAC5BD,MAAM,GAAGA,MAAM,CAACE,UAAU;IAChC;IACI,IAAIF,MAAM,CAACC,OAAO,KAAK,IAAI,EACzB;IACF,MAAM1E,GAAG,GAAGyE,MAAM,CAACE,UAAU,CAAC5D,QAAQ,GAAG,CAAC;IAC1C,MAAMmC,MAAM,GAAGuB,MAAM,CAACG,SAAS;IAC/B,IAAI/E,KAAK,CAACC,IAAI,CAAC,CAACE,GAAG,CAAC,CAACkD,MAAM,CAAC,CAACjB,QAAQ,EACnC;IACF,IAAIjC,GAAG,KAAKH,KAAK,CAAC3B,OAAO,CAAC,IAAIgF,MAAM,KAAKrD,KAAK,CAAC1B,UAAU,CAAC,EAAE;MAC1DD,OAAO,CAAC2G,KAAK,GAAG7E,GAAG;MACnB7B,UAAU,CAAC0G,KAAK,GAAG3B,MAAM;MACzBtF,IAAI,CAAC,aAAa,EAAE;QAClB0F,SAAS,EAAE,IAAI;QACfD,OAAO,EAAEgB,aAAa,CAACrE,GAAG,EAAEkD,MAAM;MAC1C,CAAO,CAAC;IACR;EACA,CAAG;EACD,MAAM4B,cAAc,GAAIjE,IAAI,IAAK;IAC/B,OAAO,CAAChB,KAAK,CAACF,UAAU,CAAC,IAAI,CAACkB,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACM,IAAI,MAAM,CAAC,IAAIN,IAAI,CAACrD,IAAI,KAAK,QAAQ,IAAIqD,IAAI,CAACZ,SAAS;EACtH,CAAG;EACD,MAAM8E,WAAW,GAAIP,KAAK,IAAK;IAC7B,IAAInG,cAAc,IAAIwB,KAAK,CAACF,UAAU,CAAC,IAAIhC,KAAK,CAAC8C,aAAa,KAAK,MAAM,EACvE;IACFuE,cAAc,CAACR,KAAK,EAAE,IAAI,CAAC;EAC/B,CAAG;EACD,MAAMS,eAAe,GAAIT,KAAK,IAAK;IACjC,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM,CAACS,OAAO,CAAC,IAAI,CAAC;IACzC,IAAI,CAACT,MAAM,EACT;IACFpG,cAAc,GAAG,IAAI;EACzB,CAAG;EACD,MAAM8G,aAAa,GAAIX,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM,CAACS,OAAO,CAAC,IAAI,CAAC;IACzC,IAAI,CAACT,MAAM,EACT;IACFpG,cAAc,GAAG,KAAK;EAC1B,CAAG;EACD,MAAM+G,eAAe,GAAIC,OAAO,IAAK;IACnC,IAAI,CAAC1H,KAAK,CAACiF,UAAU,CAACU,SAAS,IAAI,CAAC3F,KAAK,CAAC+E,OAAO,EAAE;MACjD9E,IAAI,CAAC,MAAM,EAAE;QAAE8E,OAAO,EAAE2C,OAAO;QAAE1C,OAAO,EAAE;MAAI,CAAE,CAAC;MACjD/E,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;IAC1B,CAAK,MAAM;MACL,IAAIyH,OAAO,IAAI1H,KAAK,CAAC+E,OAAO,EAAE;QAC5B9E,IAAI,CAAC,MAAM,EAAE;UAAE8E,OAAO,EAAE/E,KAAK,CAAC+E,OAAO;UAAEC,OAAO,EAAE0C;QAAO,CAAE,CAAC;MAClE,CAAO,MAAM;QACLzH,IAAI,CAAC,MAAM,EAAE;UAAE8E,OAAO,EAAE2C,OAAO;UAAE1C,OAAO,EAAEhF,KAAK,CAAC+E;QAAO,CAAE,CAAC;MAClE;MACM9E,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;IAC3B;EACA,CAAG;EACD,MAAM0H,cAAc,GAAID,OAAO,IAAK;IAClC,MAAME,UAAU,GAAGF,OAAO,CAACrC,IAAI,EAAE;IACjC,MAAM6B,KAAK,GAAG,GAAGQ,OAAO,CAACG,IAAI,EAAE,IAAID,UAAU,EAAE;IAC/C3H,IAAI,CAAC,MAAM,EAAE;MACX4H,IAAI,EAAEH,OAAO,CAACG,IAAI,EAAE;MACpBxC,IAAI,EAAEuC,UAAU;MAChBV,KAAK;MACLtG,IAAI,EAAE8G,OAAO,CAAChG,OAAO,CAAC,MAAM;IAClC,CAAK,CAAC;EACN,CAAG;EACD,MAAMoG,eAAe,GAAGA,CAACJ,OAAO,EAAEzD,QAAQ,KAAK;IAC7C,MAAM8D,QAAQ,GAAG9D,QAAQ,GAAGlB,SAAS,CAAC/C,KAAK,CAACgD,WAAW,CAAC,CAACgF,MAAM,CAAE7D,CAAC,IAAK,CAACA,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC8D,OAAO,EAAE,MAAMP,OAAO,CAACO,OAAO,EAAE,CAAC,GAAGlF,SAAS,CAAC/C,KAAK,CAACgD,WAAW,CAAC,CAAClB,MAAM,CAAC,CAAC4F,OAAO,CAAC,CAAC;IACjLzH,IAAI,CAAC,MAAM,EAAE8H,QAAQ,CAAC;EAC1B,CAAG;EACD,MAAMV,cAAc,GAAGA,CAACR,KAAK,EAAEqB,kBAAkB,GAAG,KAAK,KAAK;IAC5D,MAAMpB,MAAM,GAAGD,KAAK,CAACC,MAAM,CAACS,OAAO,CAAC,IAAI,CAAC;IACzC,IAAI,CAACT,MAAM,EACT;IACF,MAAMzE,GAAG,GAAGyE,MAAM,CAACE,UAAU,CAAC5D,QAAQ,GAAG,CAAC;IAC1C,MAAMmC,MAAM,GAAGuB,MAAM,CAACG,SAAS;IAC/B,MAAM/D,IAAI,GAAGhB,KAAK,CAACC,IAAI,CAAC,CAACE,GAAG,CAAC,CAACkD,MAAM,CAAC;IACrC,IAAIrC,IAAI,CAACoB,QAAQ,IAAIpB,IAAI,CAACrD,IAAI,KAAK,MAAM,EACvC;IACF,MAAM6H,OAAO,GAAGhB,aAAa,CAACrE,GAAG,EAAEkD,MAAM,CAAC;IAC1C,QAAQvF,KAAK,CAAC8C,aAAa;MACzB,KAAK,OAAO;QAAE;UACZ2E,eAAe,CAACC,OAAO,CAAC;UACxB;QACR;MACM,KAAK,MAAM;QAAE;UACXzH,IAAI,CAAC,MAAM,EAAEyH,OAAO,EAAEQ,kBAAkB,CAAC;UACzC;QACR;MACM,KAAK,MAAM;QAAE;UACXP,cAAc,CAACD,OAAO,CAAC;UACvB;QACR;MACM,KAAK,OAAO;QAAE;UACZI,eAAe,CAACJ,OAAO,EAAE,CAAC,CAACxE,IAAI,CAACe,QAAQ,CAAC;UACzC;QACR;IAIA;EACA,CAAG;EACD,MAAMY,YAAY,GAAI3B,IAAI,IAAK;IAC7B,IAAIlD,KAAK,CAAC8C,aAAa,KAAK,MAAM,EAChC,OAAO,KAAK;IACd,IAAI4E,OAAO,GAAG1H,KAAK,CAACY,IAAI,CAACc,OAAO,CAAC,KAAK,CAAC;IACvC,IAAIwB,IAAI,CAACrD,IAAI,KAAK,YAAY,EAAE;MAC9B6H,OAAO,GAAGA,OAAO,CAAC/F,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC;IAC5C;IACI,IAAIuB,IAAI,CAACrD,IAAI,KAAK,YAAY,EAAE;MAC9B6H,OAAO,GAAGA,OAAO,CAACtC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;IACvC;IACIsC,OAAO,GAAGA,OAAO,CAAC9G,IAAI,CAAC6F,MAAM,CAAC0B,QAAQ,CAACjF,IAAI,CAACM,IAAI,EAAE,EAAE,CAAC,CAAC;IACtD,IAAIxD,KAAK,CAACgD,WAAW,IAAI,CAACoF,OAAO,CAACpI,KAAK,CAACgD,WAAW,CAAC,EAAE;MACpD,MAAMqF,SAAS,GAAG,CAACrI,KAAK,CAACgD,WAAW,CAACpB,GAAG,EAAE,GAAGjB,cAAc,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;MACxE,MAAM2H,QAAQ,GAAGtI,KAAK,CAACgD,WAAW,CAACrB,QAAQ,CAAC0G,SAAS,EAAE,KAAK,CAAC;MAC7D,OAAOC,QAAQ,CAAClE,MAAM,CAACsD,OAAO,EAAE,KAAK,CAAC;IAC5C;IACI,OAAO,KAAK;EAChB,CAAG;EACD,OAAO;IACL7F,KAAK;IACLM,IAAI;IACJ/B,QAAQ;IACRE,cAAc;IACdiG,KAAK;IACLjE,SAAS;IACTuC,YAAY;IACZsC,cAAc;IACdE,cAAc;IACdG,aAAa;IACbF,eAAe;IACfV,eAAe;IACfQ;EACJ,CAAG;AACH;AACY,MAACmB,oBAAoB,GAAGA,CAACvI,KAAK,EAAE;EAC1CsC,SAAS;EACTuC;AACF,CAAC,KAAK;EACJ,MAAM2D,EAAE,GAAGC,YAAY,CAAC,YAAY,CAAC;EACrC,MAAM;IAAEC;EAAC,CAAE,GAAGvI,SAAS,EAAE;EACzB,MAAMwI,QAAQ,GAAGpH,QAAQ,CAAC,MAAM,CAC9BiH,EAAE,CAACI,CAAC,EAAE,EACN;IAAE,cAAc,EAAE5I,KAAK,CAAC8C,aAAa,KAAK;EAAM,CAAE,CACnD,CAAC;EACF,MAAM+F,UAAU,GAAGtH,QAAQ,CAAC,MAAMmH,CAAC,CAAC,+BAA+B,CAAC,CAAC;EACrE,MAAMI,SAAS,GAAGvH,QAAQ,CAAC,MAAMmH,CAAC,CAAC,oBAAoB,CAAC,CAAC;EACzD,MAAMK,cAAc,GAAI7F,IAAI,IAAK;IAC/B,MAAM8F,OAAO,GAAG,EAAE;IAClB,IAAIpJ,WAAW,CAACsD,IAAI,CAACrD,IAAI,CAAC,IAAI,CAACqD,IAAI,CAACoB,QAAQ,EAAE;MAC5C0E,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC;MACzB,IAAI/F,IAAI,CAACrD,IAAI,KAAK,OAAO,EAAE;QACzBmJ,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;MAC7B;IACA,CAAK,MAAM;MACLD,OAAO,CAACC,IAAI,CAAC/F,IAAI,CAACrD,IAAI,CAAC;IAC7B;IACI,IAAIyC,SAAS,CAACY,IAAI,CAAC,EAAE;MACnB8F,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;IAC7B;IACI,IAAI/F,IAAI,CAAC4B,OAAO,KAAKlF,WAAW,CAACsD,IAAI,CAACrD,IAAI,CAAC,IAAIG,KAAK,CAAC8C,aAAa,KAAK,MAAM,CAAC,EAAE;MAC9EkG,OAAO,CAACC,IAAI,CAAC,UAAU,CAAC;MACxB,IAAI/F,IAAI,CAACuB,KAAK,EAAE;QACduE,OAAO,CAACC,IAAI,CAAC,YAAY,CAAC;MAClC;MACM,IAAI/F,IAAI,CAACwB,GAAG,EAAE;QACZsE,OAAO,CAACC,IAAI,CAAC,UAAU,CAAC;MAChC;IACA;IACI,IAAI/F,IAAI,CAACoB,QAAQ,EAAE;MACjB0E,OAAO,CAACC,IAAI,CAAC,UAAU,CAAC;IAC9B;IACI,IAAI/F,IAAI,CAACe,QAAQ,EAAE;MACjB+E,OAAO,CAACC,IAAI,CAAC,UAAU,CAAC;IAC9B;IACI,IAAI/F,IAAI,CAACqB,WAAW,EAAE;MACpByE,OAAO,CAACC,IAAI,CAAC/F,IAAI,CAACqB,WAAW,CAAC;IACpC;IACI,OAAOyE,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;EAC5B,CAAG;EACD,MAAMC,SAAS,GAAIjG,IAAI,IAAK,CAC1BsF,EAAE,CAACY,CAAC,CAAC,KAAK,CAAC,EACX;IAAEC,OAAO,EAAExE,YAAY,CAAC3B,IAAI;EAAC,CAAE,CAChC;EACD,OAAO;IACLyF,QAAQ;IACRE,UAAU;IACVC,SAAS;IACTC,cAAc;IACdI,SAAS;IACTT;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}