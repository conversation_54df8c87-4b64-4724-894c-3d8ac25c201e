{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { nextTick } from 'vue';\nimport { throttle } from 'lodash-unified';\nimport { isFunction } from '@vue/shared';\nimport { throwError } from '../../../utils/error.mjs';\nimport { getScrollContainer } from '../../../utils/dom/scroll.mjs';\nimport { getOffsetTopDistance } from '../../../utils/dom/position.mjs';\nconst SCOPE = \"ElInfiniteScroll\";\nconst CHECK_INTERVAL = 50;\nconst DEFAULT_DELAY = 200;\nconst DEFAULT_DISTANCE = 0;\nconst attributes = {\n  delay: {\n    type: Number,\n    default: DEFAULT_DELAY\n  },\n  distance: {\n    type: Number,\n    default: DEFAULT_DISTANCE\n  },\n  disabled: {\n    type: Boolean,\n    default: false\n  },\n  immediate: {\n    type: Boolean,\n    default: true\n  }\n};\nconst getScrollOptions = (el, instance) => {\n  return Object.entries(attributes).reduce((acm, [name, option]) => {\n    var _a, _b;\n    const {\n      type,\n      default: defaultValue\n    } = option;\n    const attrVal = el.getAttribute(`infinite-scroll-${name}`);\n    let value = (_b = (_a = instance[attrVal]) != null ? _a : attrVal) != null ? _b : defaultValue;\n    value = value === \"false\" ? false : value;\n    value = type(value);\n    acm[name] = Number.isNaN(value) ? defaultValue : value;\n    return acm;\n  }, {});\n};\nconst destroyObserver = el => {\n  const {\n    observer\n  } = el[SCOPE];\n  if (observer) {\n    observer.disconnect();\n    delete el[SCOPE].observer;\n  }\n};\nconst handleScroll = (el, cb) => {\n  const {\n    container,\n    containerEl,\n    instance,\n    observer,\n    lastScrollTop\n  } = el[SCOPE];\n  const {\n    disabled,\n    distance\n  } = getScrollOptions(el, instance);\n  const {\n    clientHeight,\n    scrollHeight,\n    scrollTop\n  } = containerEl;\n  const delta = scrollTop - lastScrollTop;\n  el[SCOPE].lastScrollTop = scrollTop;\n  if (observer || disabled || delta < 0) return;\n  let shouldTrigger = false;\n  if (container === el) {\n    shouldTrigger = scrollHeight - (clientHeight + scrollTop) <= distance;\n  } else {\n    const {\n      clientTop,\n      scrollHeight: height\n    } = el;\n    const offsetTop = getOffsetTopDistance(el, containerEl);\n    shouldTrigger = scrollTop + clientHeight >= offsetTop + clientTop + height - distance;\n  }\n  if (shouldTrigger) {\n    cb.call(instance);\n  }\n};\nfunction checkFull(el, cb) {\n  const {\n    containerEl,\n    instance\n  } = el[SCOPE];\n  const {\n    disabled\n  } = getScrollOptions(el, instance);\n  if (disabled || containerEl.clientHeight === 0) return;\n  if (containerEl.scrollHeight <= containerEl.clientHeight) {\n    cb.call(instance);\n  } else {\n    destroyObserver(el);\n  }\n}\nconst InfiniteScroll = {\n  async mounted(el, binding) {\n    const {\n      instance,\n      value: cb\n    } = binding;\n    if (!isFunction(cb)) {\n      throwError(SCOPE, \"'v-infinite-scroll' binding value must be a function\");\n    }\n    await nextTick();\n    const {\n      delay,\n      immediate\n    } = getScrollOptions(el, instance);\n    const container = getScrollContainer(el, true);\n    const containerEl = container === window ? document.documentElement : container;\n    const onScroll = throttle(handleScroll.bind(null, el, cb), delay);\n    if (!container) return;\n    el[SCOPE] = {\n      instance,\n      container,\n      containerEl,\n      delay,\n      cb,\n      onScroll,\n      lastScrollTop: containerEl.scrollTop\n    };\n    if (immediate) {\n      const observer = new MutationObserver(throttle(checkFull.bind(null, el, cb), CHECK_INTERVAL));\n      el[SCOPE].observer = observer;\n      observer.observe(el, {\n        childList: true,\n        subtree: true\n      });\n      checkFull(el, cb);\n    }\n    container.addEventListener(\"scroll\", onScroll);\n  },\n  unmounted(el) {\n    if (!el[SCOPE]) return;\n    const {\n      container,\n      onScroll\n    } = el[SCOPE];\n    container == null ? void 0 : container.removeEventListener(\"scroll\", onScroll);\n    destroyObserver(el);\n  },\n  async updated(el) {\n    if (!el[SCOPE]) {\n      await nextTick();\n    } else {\n      const {\n        containerEl,\n        cb,\n        observer\n      } = el[SCOPE];\n      if (containerEl.clientHeight && observer) {\n        checkFull(el, cb);\n      }\n    }\n  }\n};\nexport { CHECK_INTERVAL, DEFAULT_DELAY, DEFAULT_DISTANCE, SCOPE, InfiniteScroll as default };", "map": {"version": 3, "names": ["SCOPE", "CHECK_INTERVAL", "DEFAULT_DELAY", "DEFAULT_DISTANCE", "attributes", "delay", "type", "Number", "default", "distance", "disabled", "Boolean", "immediate", "getScrollOptions", "el", "instance", "Object", "entries", "reduce", "acm", "name", "option", "_a", "_b", "defaultValue", "attrVal", "getAttribute", "value", "isNaN", "destroyObserver", "observer", "disconnect", "handleScroll", "cb", "container", "containerEl", "lastScrollTop", "clientHeight", "scrollHeight", "scrollTop", "delta", "should<PERSON><PERSON>ger", "clientTop", "height", "offsetTop", "getOffsetTopDistance", "call", "checkFull", "InfiniteScroll", "mounted", "binding", "isFunction", "throwError", "nextTick", "getScrollContainer", "window", "document", "documentElement", "onScroll", "throttle", "bind", "MutationObserver", "observe", "childList", "subtree", "addEventListener", "unmounted", "removeEventListener", "updated"], "sources": ["../../../../../../packages/components/infinite-scroll/src/index.ts"], "sourcesContent": ["// @ts-nocheck\nimport { nextTick } from 'vue'\nimport { throttle } from 'lodash-unified'\nimport {\n  getOffsetTopDistance,\n  getScrollContainer,\n  isFunction,\n  throwError,\n} from '@element-plus/utils'\n\nimport type { ComponentPublicInstance, ObjectDirective } from 'vue'\n\nexport const SCOPE = 'ElInfiniteScroll'\nexport const CHECK_INTERVAL = 50\nexport const DEFAULT_DELAY = 200\nexport const DEFAULT_DISTANCE = 0\n\nconst attributes = {\n  delay: {\n    type: Number,\n    default: DEFAULT_DELAY,\n  },\n  distance: {\n    type: Number,\n    default: DEFAULT_DISTANCE,\n  },\n  disabled: {\n    type: Boolean,\n    default: false,\n  },\n  immediate: {\n    type: Boolean,\n    default: true,\n  },\n}\n\ntype Attrs = typeof attributes\ntype ScrollOptions = { [K in keyof Attrs]: Attrs[K]['default'] }\ntype InfiniteScrollCallback = () => void\ntype InfiniteScrollEl = HTMLElement & {\n  [SCOPE]: {\n    container: HTMLElement | Window\n    containerEl: HTMLElement\n    instance: ComponentPublicInstance\n    delay: number // export for test\n    lastScrollTop: number\n    cb: InfiniteScrollCallback\n    onScroll: () => void\n    observer?: MutationObserver\n  }\n}\n\nconst getScrollOptions = (\n  el: HTMLElement,\n  instance: ComponentPublicInstance\n): ScrollOptions => {\n  return Object.entries(attributes).reduce((acm, [name, option]) => {\n    const { type, default: defaultValue } = option\n    const attrVal = el.getAttribute(`infinite-scroll-${name}`)\n    let value = instance[attrVal] ?? attrVal ?? defaultValue\n    value = value === 'false' ? false : value\n    value = type(value)\n    acm[name] = Number.isNaN(value) ? defaultValue : value\n    return acm\n  }, {} as ScrollOptions)\n}\n\nconst destroyObserver = (el: InfiniteScrollEl) => {\n  const { observer } = el[SCOPE]\n\n  if (observer) {\n    observer.disconnect()\n    delete el[SCOPE].observer\n  }\n}\n\nconst handleScroll = (el: InfiniteScrollEl, cb: InfiniteScrollCallback) => {\n  const { container, containerEl, instance, observer, lastScrollTop } =\n    el[SCOPE]\n  const { disabled, distance } = getScrollOptions(el, instance)\n  const { clientHeight, scrollHeight, scrollTop } = containerEl\n  const delta = scrollTop - lastScrollTop\n\n  el[SCOPE].lastScrollTop = scrollTop\n\n  // trigger only if full check has done and not disabled and scroll down\n  if (observer || disabled || delta < 0) return\n\n  let shouldTrigger = false\n\n  if (container === el) {\n    shouldTrigger = scrollHeight - (clientHeight + scrollTop) <= distance\n  } else {\n    // get the scrollHeight since el might be visible overflow\n    const { clientTop, scrollHeight: height } = el\n    const offsetTop = getOffsetTopDistance(el, containerEl)\n    shouldTrigger =\n      scrollTop + clientHeight >= offsetTop + clientTop + height - distance\n  }\n\n  if (shouldTrigger) {\n    cb.call(instance)\n  }\n}\n\nfunction checkFull(el: InfiniteScrollEl, cb: InfiniteScrollCallback) {\n  const { containerEl, instance } = el[SCOPE]\n  const { disabled } = getScrollOptions(el, instance)\n\n  if (disabled || containerEl.clientHeight === 0) return\n\n  if (containerEl.scrollHeight <= containerEl.clientHeight) {\n    cb.call(instance)\n  } else {\n    destroyObserver(el)\n  }\n}\n\nconst InfiniteScroll: ObjectDirective<\n  InfiniteScrollEl,\n  InfiniteScrollCallback\n> = {\n  async mounted(el, binding) {\n    const { instance, value: cb } = binding\n\n    if (!isFunction(cb)) {\n      throwError(SCOPE, \"'v-infinite-scroll' binding value must be a function\")\n    }\n\n    // ensure parentNode mounted\n    await nextTick()\n\n    const { delay, immediate } = getScrollOptions(el, instance)\n    const container = getScrollContainer(el, true)\n    const containerEl =\n      container === window\n        ? document.documentElement\n        : (container as HTMLElement)\n    const onScroll = throttle(handleScroll.bind(null, el, cb), delay)\n\n    if (!container) return\n\n    el[SCOPE] = {\n      instance,\n      container,\n      containerEl,\n      delay,\n      cb,\n      onScroll,\n      lastScrollTop: containerEl.scrollTop,\n    }\n\n    if (immediate) {\n      const observer = new MutationObserver(\n        throttle(checkFull.bind(null, el, cb), CHECK_INTERVAL)\n      )\n      el[SCOPE].observer = observer\n      observer.observe(el, { childList: true, subtree: true })\n      checkFull(el, cb)\n    }\n\n    container.addEventListener('scroll', onScroll)\n  },\n  unmounted(el) {\n    if (!el[SCOPE]) return\n    const { container, onScroll } = el[SCOPE]\n\n    container?.removeEventListener('scroll', onScroll)\n    destroyObserver(el)\n  },\n  async updated(el) {\n    if (!el[SCOPE]) {\n      await nextTick()\n    } else {\n      const { containerEl, cb, observer } = el[SCOPE]\n      if (containerEl.clientHeight && observer) {\n        checkFull(el, cb)\n      }\n    }\n  },\n}\n\nexport default InfiniteScroll\n"], "mappings": ";;;;;;;;AAQY,MAACA,KAAK,GAAG;AACT,MAACC,cAAc,GAAG;AAClB,MAACC,aAAa,GAAG;AACjB,MAACC,gBAAgB,GAAG;AAChC,MAAMC,UAAU,GAAG;EACjBC,KAAK,EAAE;IACLC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAEN;EACb,CAAG;EACDO,QAAQ,EAAE;IACRH,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAEL;EACb,CAAG;EACDO,QAAQ,EAAE;IACRJ,IAAI,EAAEK,OAAO;IACbH,OAAO,EAAE;EACb,CAAG;EACDI,SAAS,EAAE;IACTN,IAAI,EAAEK,OAAO;IACbH,OAAO,EAAE;EACb;AACA,CAAC;AACD,MAAMK,gBAAgB,GAAGA,CAACC,EAAE,EAAEC,QAAQ,KAAK;EACzC,OAAOC,MAAM,CAACC,OAAO,CAACb,UAAU,CAAC,CAACc,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,IAAI,EAAEC,MAAM,CAAC,KAAK;IAChE,IAAIC,EAAE,EAAEC,EAAE;IACV,MAAM;MAAEjB,IAAI;MAAEE,OAAO,EAAEgB;IAAY,CAAE,GAAGH,MAAM;IAC9C,MAAMI,OAAO,GAAGX,EAAE,CAACY,YAAY,CAAC,mBAAmBN,IAAI,EAAE,CAAC;IAC1D,IAAIO,KAAK,GAAG,CAACJ,EAAE,GAAG,CAACD,EAAE,GAAGP,QAAQ,CAACU,OAAO,CAAC,KAAK,IAAI,GAAGH,EAAE,GAAGG,OAAO,KAAK,IAAI,GAAGF,EAAE,GAAGC,YAAY;IAC9FG,KAAK,GAAGA,KAAK,KAAK,OAAO,GAAG,KAAK,GAAGA,KAAK;IACzCA,KAAK,GAAGrB,IAAI,CAACqB,KAAK,CAAC;IACnBR,GAAG,CAACC,IAAI,CAAC,GAAGb,MAAM,CAACqB,KAAK,CAACD,KAAK,CAAC,GAAGH,YAAY,GAAGG,KAAK;IACtD,OAAOR,GAAG;EACd,CAAG,EAAE,EAAE,CAAC;AACR,CAAC;AACD,MAAMU,eAAe,GAAIf,EAAE,IAAK;EAC9B,MAAM;IAAEgB;EAAQ,CAAE,GAAGhB,EAAE,CAACd,KAAK,CAAC;EAC9B,IAAI8B,QAAQ,EAAE;IACZA,QAAQ,CAACC,UAAU,EAAE;IACrB,OAAOjB,EAAE,CAACd,KAAK,CAAC,CAAC8B,QAAQ;EAC7B;AACA,CAAC;AACD,MAAME,YAAY,GAAGA,CAAClB,EAAE,EAAEmB,EAAE,KAAK;EAC/B,MAAM;IAAEC,SAAS;IAAEC,WAAW;IAAEpB,QAAQ;IAAEe,QAAQ;IAAEM;EAAa,CAAE,GAAGtB,EAAE,CAACd,KAAK,CAAC;EAC/E,MAAM;IAAEU,QAAQ;IAAED;EAAQ,CAAE,GAAGI,gBAAgB,CAACC,EAAE,EAAEC,QAAQ,CAAC;EAC7D,MAAM;IAAEsB,YAAY;IAAEC,YAAY;IAAEC;EAAS,CAAE,GAAGJ,WAAW;EAC7D,MAAMK,KAAK,GAAGD,SAAS,GAAGH,aAAa;EACvCtB,EAAE,CAACd,KAAK,CAAC,CAACoC,aAAa,GAAGG,SAAS;EACnC,IAAIT,QAAQ,IAAIpB,QAAQ,IAAI8B,KAAK,GAAG,CAAC,EACnC;EACF,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIP,SAAS,KAAKpB,EAAE,EAAE;IACpB2B,aAAa,GAAGH,YAAY,IAAID,YAAY,GAAGE,SAAS,CAAC,IAAI9B,QAAQ;EACzE,CAAG,MAAM;IACL,MAAM;MAAEiC,SAAS;MAAEJ,YAAY,EAAEK;IAAM,CAAE,GAAG7B,EAAE;IAC9C,MAAM8B,SAAS,GAAGC,oBAAoB,CAAC/B,EAAE,EAAEqB,WAAW,CAAC;IACvDM,aAAa,GAAGF,SAAS,GAAGF,YAAY,IAAIO,SAAS,GAAGF,SAAS,GAAGC,MAAM,GAAGlC,QAAQ;EACzF;EACE,IAAIgC,aAAa,EAAE;IACjBR,EAAE,CAACa,IAAI,CAAC/B,QAAQ,CAAC;EACrB;AACA,CAAC;AACD,SAASgC,SAASA,CAACjC,EAAE,EAAEmB,EAAE,EAAE;EACzB,MAAM;IAAEE,WAAW;IAAEpB;EAAQ,CAAE,GAAGD,EAAE,CAACd,KAAK,CAAC;EAC3C,MAAM;IAAEU;EAAQ,CAAE,GAAGG,gBAAgB,CAACC,EAAE,EAAEC,QAAQ,CAAC;EACnD,IAAIL,QAAQ,IAAIyB,WAAW,CAACE,YAAY,KAAK,CAAC,EAC5C;EACF,IAAIF,WAAW,CAACG,YAAY,IAAIH,WAAW,CAACE,YAAY,EAAE;IACxDJ,EAAE,CAACa,IAAI,CAAC/B,QAAQ,CAAC;EACrB,CAAG,MAAM;IACLc,eAAe,CAACf,EAAE,CAAC;EACvB;AACA;AACK,MAACkC,cAAc,GAAG;EACrB,MAAMC,OAAOA,CAACnC,EAAE,EAAEoC,OAAO,EAAE;IACzB,MAAM;MAAEnC,QAAQ;MAAEY,KAAK,EAAEM;IAAE,CAAE,GAAGiB,OAAO;IACvC,IAAI,CAACC,UAAU,CAAClB,EAAE,CAAC,EAAE;MACnBmB,UAAU,CAACpD,KAAK,EAAE,sDAAsD,CAAC;IAC/E;IACI,MAAMqD,QAAQ,EAAE;IAChB,MAAM;MAAEhD,KAAK;MAAEO;IAAS,CAAE,GAAGC,gBAAgB,CAACC,EAAE,EAAEC,QAAQ,CAAC;IAC3D,MAAMmB,SAAS,GAAGoB,kBAAkB,CAACxC,EAAE,EAAE,IAAI,CAAC;IAC9C,MAAMqB,WAAW,GAAGD,SAAS,KAAKqB,MAAM,GAAGC,QAAQ,CAACC,eAAe,GAAGvB,SAAS;IAC/E,MAAMwB,QAAQ,GAAGC,QAAQ,CAAC3B,YAAY,CAAC4B,IAAI,CAAC,IAAI,EAAE9C,EAAE,EAAEmB,EAAE,CAAC,EAAE5B,KAAK,CAAC;IACjE,IAAI,CAAC6B,SAAS,EACZ;IACFpB,EAAE,CAACd,KAAK,CAAC,GAAG;MACVe,QAAQ;MACRmB,SAAS;MACTC,WAAW;MACX9B,KAAK;MACL4B,EAAE;MACFyB,QAAQ;MACRtB,aAAa,EAAED,WAAW,CAACI;IACjC,CAAK;IACD,IAAI3B,SAAS,EAAE;MACb,MAAMkB,QAAQ,GAAG,IAAI+B,gBAAgB,CAACF,QAAQ,CAACZ,SAAS,CAACa,IAAI,CAAC,IAAI,EAAE9C,EAAE,EAAEmB,EAAE,CAAC,EAAEhC,cAAc,CAAC,CAAC;MAC7Fa,EAAE,CAACd,KAAK,CAAC,CAAC8B,QAAQ,GAAGA,QAAQ;MAC7BA,QAAQ,CAACgC,OAAO,CAAChD,EAAE,EAAE;QAAEiD,SAAS,EAAE,IAAI;QAAEC,OAAO,EAAE;MAAI,CAAE,CAAC;MACxDjB,SAAS,CAACjC,EAAE,EAAEmB,EAAE,CAAC;IACvB;IACIC,SAAS,CAAC+B,gBAAgB,CAAC,QAAQ,EAAEP,QAAQ,CAAC;EAClD,CAAG;EACDQ,SAASA,CAACpD,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,CAACd,KAAK,CAAC,EACZ;IACF,MAAM;MAAEkC,SAAS;MAAEwB;IAAQ,CAAE,GAAG5C,EAAE,CAACd,KAAK,CAAC;IACzCkC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACiC,mBAAmB,CAAC,QAAQ,EAAET,QAAQ,CAAC;IAC9E7B,eAAe,CAACf,EAAE,CAAC;EACvB,CAAG;EACD,MAAMsD,OAAOA,CAACtD,EAAE,EAAE;IAChB,IAAI,CAACA,EAAE,CAACd,KAAK,CAAC,EAAE;MACd,MAAMqD,QAAQ,EAAE;IACtB,CAAK,MAAM;MACL,MAAM;QAAElB,WAAW;QAAEF,EAAE;QAAEH;MAAQ,CAAE,GAAGhB,EAAE,CAACd,KAAK,CAAC;MAC/C,IAAImC,WAAW,CAACE,YAAY,IAAIP,QAAQ,EAAE;QACxCiB,SAAS,CAACjC,EAAE,EAAEmB,EAAE,CAAC;MACzB;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}