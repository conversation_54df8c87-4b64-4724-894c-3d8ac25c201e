{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, unref, renderSlot } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElMain\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  setup(__props) {\n    const ns = useNamespace(\"main\");\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"main\", {\n        class: normalizeClass(unref(ns).b())\n      }, [renderSlot(_ctx.$slots, \"default\")], 2);\n    };\n  }\n});\nvar Main = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"main.vue\"]]);\nexport { Main as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace"], "sources": ["../../../../../../packages/components/container/src/main.vue"], "sourcesContent": ["<template>\n  <main :class=\"ns.b()\">\n    <slot />\n  </main>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useNamespace } from '@element-plus/hooks'\n\ndefineOptions({\n  name: 'El<PERSON>ain',\n})\n\nconst ns = useNamespace('main')\n</script>\n"], "mappings": ";;;mCASc;EACZA,IAAM;AACR;;;;IAEM,MAAAC,EAAA,GAAKC,YAAA,CAAa,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}