{"ast": null, "code": "import { defineComponent, getCurrentInstance, nextTick, watch } from 'vue';\nimport { ElOption } from '../../select/index.mjs';\nconst component = defineComponent({\n  extends: ElOption,\n  setup(props, ctx) {\n    const result = ElOption.setup(props, ctx);\n    delete result.selectOptionClick;\n    const vm = getCurrentInstance().proxy;\n    nextTick(() => {\n      if (!result.select.states.cachedOptions.get(vm.value)) {\n        result.select.onOptionCreate(vm);\n      }\n    });\n    watch(() => ctx.attrs.visible, val => {\n      nextTick(() => {\n        result.states.visible = val;\n      });\n    }, {\n      immediate: true\n    });\n    return result;\n  },\n  methods: {\n    selectOptionClick() {\n      this.$el.parentElement.click();\n    }\n  }\n});\nexport { component as default };", "map": {"version": 3, "names": ["component", "defineComponent", "extends", "ElOption", "setup", "props", "ctx", "result", "selectOptionClick", "vm", "getCurrentInstance", "proxy", "nextTick", "select", "states", "cachedOptions", "get", "value", "onOptionCreate", "watch", "attrs", "visible", "val", "immediate", "methods", "$el", "parentElement", "click"], "sources": ["../../../../../../packages/components/tree-select/src/tree-select-option.ts"], "sourcesContent": ["import { defineComponent, getCurrentInstance, nextTick, watch } from 'vue'\nimport { ElOption } from '@element-plus/components/select'\n\nconst component = defineComponent({\n  extends: ElOption,\n  setup(props, ctx) {\n    const result = (ElOption.setup as NonNullable<any>)(props, ctx)\n\n    // use methods.selectOptionClick\n    delete result.selectOptionClick\n\n    const vm = (getCurrentInstance() as NonNullable<any>).proxy\n\n    // Fix: https://github.com/element-plus/element-plus/issues/7917\n    // `el-option` will delete the cache before unmount,\n    // This is normal for flat arrays `<el-select><el-option v-for=\"3\"></el-select>`,\n    // Because the same node key does not create a difference node,\n    // But in tree data, the same key at different levels will create diff nodes,\n    // So the destruction of `el-option` in `nextTick` will be slower than\n    // the creation of new `el-option`, which will delete the new node,\n    // here restore the deleted node.\n    // @link https://github.com/element-plus/element-plus/blob/6df6e49db07b38d6cc3b5e9a960782bd30879c11/packages/components/select/src/option.vue#L78\n    nextTick(() => {\n      if (!result.select.states.cachedOptions.get(vm.value)) {\n        result.select.onOptionCreate(vm)\n      }\n    })\n\n    watch(\n      () => ctx.attrs.visible,\n      (val) => {\n        nextTick(() => {\n          result.states.visible = val\n        })\n      },\n      {\n        immediate: true,\n      }\n    )\n\n    return result\n  },\n  methods: {\n    selectOptionClick() {\n      // $el.parentElement => el-tree-node__content\n      this.$el.parentElement.click()\n    },\n  },\n})\n\nexport default component\n"], "mappings": ";;AAEK,MAACA,SAAS,GAAGC,eAAe,CAAC;EAChCC,OAAO,EAAEC,QAAQ;EACjBC,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAChB,MAAMC,MAAM,GAAGJ,QAAQ,CAACC,KAAK,CAACC,KAAK,EAAEC,GAAG,CAAC;IACzC,OAAOC,MAAM,CAACC,iBAAiB;IAC/B,MAAMC,EAAE,GAAGC,kBAAkB,EAAE,CAACC,KAAK;IACrCC,QAAQ,CAAC,MAAM;MACb,IAAI,CAACL,MAAM,CAACM,MAAM,CAACC,MAAM,CAACC,aAAa,CAACC,GAAG,CAACP,EAAE,CAACQ,KAAK,CAAC,EAAE;QACrDV,MAAM,CAACM,MAAM,CAACK,cAAc,CAACT,EAAE,CAAC;MACxC;IACA,CAAK,CAAC;IACFU,KAAK,CAAC,MAAMb,GAAG,CAACc,KAAK,CAACC,OAAO,EAAGC,GAAG,IAAK;MACtCV,QAAQ,CAAC,MAAM;QACbL,MAAM,CAACO,MAAM,CAACO,OAAO,GAAGC,GAAG;MACnC,CAAO,CAAC;IACR,CAAK,EAAE;MACDC,SAAS,EAAE;IACjB,CAAK,CAAC;IACF,OAAOhB,MAAM;EACjB,CAAG;EACDiB,OAAO,EAAE;IACPhB,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACiB,GAAG,CAACC,aAAa,CAACC,KAAK,EAAE;IACpC;EACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}