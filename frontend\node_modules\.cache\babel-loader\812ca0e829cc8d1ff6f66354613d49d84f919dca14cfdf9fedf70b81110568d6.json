{"ast": null, "code": "import { defineComponent, ref, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, toDisplayString, createVNode } from 'vue';\nimport { ElInput } from '../../../input/index.mjs';\nimport { usePagination } from '../usePagination.mjs';\nimport { paginationJumperProps } from './jumper.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPaginationJumper\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: paginationJumperProps,\n  setup(__props) {\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"pagination\");\n    const {\n      pageCount,\n      disabled,\n      currentPage,\n      changeEvent\n    } = usePagination();\n    const userInput = ref();\n    const innerValue = computed(() => {\n      var _a;\n      return (_a = userInput.value) != null ? _a : currentPage == null ? void 0 : currentPage.value;\n    });\n    function handleInput(val) {\n      userInput.value = val ? +val : \"\";\n    }\n    function handleChange(val) {\n      val = Math.trunc(+val);\n      changeEvent == null ? void 0 : changeEvent(val);\n      userInput.value = void 0;\n    }\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"span\", {\n        class: normalizeClass(unref(ns).e(\"jump\")),\n        disabled: unref(disabled)\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass([unref(ns).e(\"goto\")])\n      }, toDisplayString(unref(t)(\"el.pagination.goto\")), 3), createVNode(unref(ElInput), {\n        size: _ctx.size,\n        class: normalizeClass([unref(ns).e(\"editor\"), unref(ns).is(\"in-pagination\")]),\n        min: 1,\n        max: unref(pageCount),\n        disabled: unref(disabled),\n        \"model-value\": unref(innerValue),\n        \"validate-event\": false,\n        \"aria-label\": unref(t)(\"el.pagination.page\"),\n        type: \"number\",\n        \"onUpdate:modelValue\": handleInput,\n        onChange: handleChange\n      }, null, 8, [\"size\", \"class\", \"max\", \"disabled\", \"model-value\", \"aria-label\"]), createElementVNode(\"span\", {\n        class: normalizeClass([unref(ns).e(\"classifier\")])\n      }, toDisplayString(unref(t)(\"el.pagination.pageClassifier\")), 3)], 10, [\"disabled\"]);\n    };\n  }\n});\nvar Jumper = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"jumper.vue\"]]);\nexport { Jumper as default };", "map": {"version": 3, "names": ["name", "t", "useLocale", "ns", "useNamespace", "pageCount", "disabled", "currentPage", "changeEvent", "usePagination", "userInput", "ref", "innerValue", "computed", "_a", "value", "handleInput", "val", "handleChange", "Math", "trunc"], "sources": ["../../../../../../../packages/components/pagination/src/components/jumper.vue"], "sourcesContent": ["<template>\n  <span :class=\"ns.e('jump')\" :disabled=\"disabled\">\n    <span :class=\"[ns.e('goto')]\">{{ t('el.pagination.goto') }}</span>\n    <el-input\n      :size=\"size\"\n      :class=\"[ns.e('editor'), ns.is('in-pagination')]\"\n      :min=\"1\"\n      :max=\"pageCount\"\n      :disabled=\"disabled\"\n      :model-value=\"innerValue\"\n      :validate-event=\"false\"\n      :aria-label=\"t('el.pagination.page')\"\n      type=\"number\"\n      @update:model-value=\"handleInput\"\n      @change=\"handleChange\"\n    />\n    <span :class=\"[ns.e('classifier')]\">{{\n      t('el.pagination.pageClassifier')\n    }}</span>\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref } from 'vue'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport ElInput from '@element-plus/components/input'\nimport { usePagination } from '../usePagination'\nimport { paginationJumperProps } from './jumper'\n\ndefineOptions({\n  name: 'ElPaginationJumper',\n})\n\ndefineProps(paginationJumperProps)\nconst { t } = useLocale()\nconst ns = useNamespace('pagination')\nconst { pageCount, disabled, currentPage, changeEvent } = usePagination()\nconst userInput = ref<number | string>()\nconst innerValue = computed(() => userInput.value ?? currentPage?.value)\n\nfunction handleInput(val: number | string) {\n  userInput.value = val ? +val : ''\n}\n\nfunction handleChange(val: number | string) {\n  val = Math.trunc(+val)\n  changeEvent?.(val)\n  userInput.value = undefined\n}\n</script>\n"], "mappings": ";;;;;;;mCA6Bc;EACZA,IAAM;AACR;;;;;IAGM;MAAEC;IAAE,IAAIC,SAAU;IAClB,MAAAC,EAAA,GAAKC,YAAA,CAAa,YAAY;IACpC,MAAM;MAAEC,SAAW;MAAAC,QAAA;MAAUC,WAAa;MAAAC;IAAA,IAAgBC,aAAc;IACxE,MAAMC,SAAA,GAAYC,GAAqB;IACvC,MAAMC,UAAA,GAAaC,QAAS,OAAM;MAElC,IAAAC,EAAA;MACY,QAAAA,EAAA,GAAAJ,SAAc,CAAAK,KAAO,YAAAD,EAAA,GAAAP,WAAA,oBAAAA,WAAA,CAAAQ,KAAA;IAAA,CACjC;IAEA,SAASC,YAAAC,GAAmC;MACpCP,SAAA,CAAAK,KAAW,GAACE,GAAG,IAAAA,GAAA;IACrB;IACA,SAAAC,YAAkBA,CAAAD,GAAA;MACpBA,GAAA,GAAAE,IAAA,CAAAC,KAAA,EAAAH,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}