{"ast": null, "code": "import { placements } from '@popperjs/core';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst sliderButtonProps = buildProps({\n  modelValue: {\n    type: Number,\n    default: 0\n  },\n  vertical: Boolean,\n  tooltipClass: String,\n  placement: {\n    type: String,\n    values: placements,\n    default: \"top\"\n  }\n});\nconst sliderButtonEmits = {\n  [UPDATE_MODEL_EVENT]: value => isNumber(value)\n};\nexport { sliderButtonEmits, sliderButtonProps };", "map": {"version": 3, "names": ["sliderButtonProps", "buildProps", "modelValue", "type", "Number", "default", "vertical", "Boolean", "tooltipClass", "String", "placement", "values", "placements", "slider<PERSON><PERSON>onE<PERSON>s", "UPDATE_MODEL_EVENT", "value", "isNumber"], "sources": ["../../../../../../packages/components/slider/src/button.ts"], "sourcesContent": ["import { placements } from '@popperjs/core'\nimport { buildProps, isNumber } from '@element-plus/utils'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport type { ComponentPublicInstance, ExtractPropTypes, Ref } from 'vue'\nimport type Button from './button.vue'\n\nexport const sliderButtonProps = buildProps({\n  modelValue: {\n    type: Number,\n    default: 0,\n  },\n  vertical: Boolean,\n  tooltipClass: String,\n  placement: {\n    type: String,\n    values: placements,\n    default: 'top',\n  },\n} as const)\nexport type SliderButtonProps = ExtractPropTypes<typeof sliderButtonProps>\n\nexport const sliderButtonEmits = {\n  [UPDATE_MODEL_EVENT]: (value: number) => isNumber(value),\n}\nexport type SliderButtonEmits = typeof sliderButtonEmits\n\nexport type SliderButtonInstance = ComponentPublicInstance<typeof Button>\n\nexport type ButtonRefs = Record<\n  'firstButton' | 'secondButton',\n  Ref<SliderButtonInstance | undefined>\n>\n\nexport interface SliderButtonInitData {\n  hovering: boolean\n  dragging: boolean\n  isClick: boolean\n  startX: number\n  currentX: number\n  startY: number\n  currentY: number\n  startPosition: number\n  newPosition: number\n  oldValue: number\n}\n"], "mappings": ";;;;AAGY,MAACA,iBAAiB,GAAGC,UAAU,CAAC;EAC1CC,UAAU,EAAE;IACVC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,QAAQ,EAAEC,OAAO;EACjBC,YAAY,EAAEC,MAAM;EACpBC,SAAS,EAAE;IACTP,IAAI,EAAEM,MAAM;IACZE,MAAM,EAAEC,UAAU;IAClBP,OAAO,EAAE;EACb;AACA,CAAC;AACW,MAACQ,iBAAiB,GAAG;EAC/B,CAACC,kBAAkB,GAAIC,KAAK,IAAKC,QAAQ,CAACD,KAAK;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}