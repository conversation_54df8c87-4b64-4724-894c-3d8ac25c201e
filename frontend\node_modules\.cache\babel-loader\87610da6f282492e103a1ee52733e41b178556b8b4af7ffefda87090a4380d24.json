{"ast": null, "code": "import { defineComponent, ref, computed, onMounted, watch, onBeforeUnmount, openBlock, createBlock, unref, createSlots, renderList, withCtx, renderSlot } from 'vue';\nimport { ElStatistic } from '../../statistic/index.mjs';\nimport { countdownProps, countdownEmits } from './countdown.mjs';\nimport { formatTime, getTime } from './utils.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { rAF, cAF } from '../../../utils/raf.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElCountdown\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: countdownProps,\n  emits: countdownEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    let timer;\n    const rawValue = ref(0);\n    const displayValue = computed(() => formatTime(rawValue.value, props.format));\n    const formatter = val => formatTime(val, props.format);\n    const stopTimer = () => {\n      if (timer) {\n        cAF(timer);\n        timer = void 0;\n      }\n    };\n    const startTimer = () => {\n      const timestamp = getTime(props.value);\n      const frameFunc = () => {\n        let diff = timestamp - Date.now();\n        emit(CHANGE_EVENT, diff);\n        if (diff <= 0) {\n          diff = 0;\n          stopTimer();\n          emit(\"finish\");\n        } else {\n          timer = rAF(frameFunc);\n        }\n        rawValue.value = diff;\n      };\n      timer = rAF(frameFunc);\n    };\n    onMounted(() => {\n      rawValue.value = getTime(props.value) - Date.now();\n      watch(() => [props.value, props.format], () => {\n        stopTimer();\n        startTimer();\n      }, {\n        immediate: true\n      });\n    });\n    onBeforeUnmount(() => {\n      stopTimer();\n    });\n    expose({\n      displayValue\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElStatistic), {\n        value: rawValue.value,\n        title: _ctx.title,\n        prefix: _ctx.prefix,\n        suffix: _ctx.suffix,\n        \"value-style\": _ctx.valueStyle,\n        formatter\n      }, createSlots({\n        _: 2\n      }, [renderList(_ctx.$slots, (_, name) => {\n        return {\n          name,\n          fn: withCtx(() => [renderSlot(_ctx.$slots, name)])\n        };\n      })]), 1032, [\"value\", \"title\", \"prefix\", \"suffix\", \"value-style\"]);\n    };\n  }\n});\nvar Countdown = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"countdown.vue\"]]);\nexport { Countdown as default };", "map": {"version": 3, "names": ["name", "timer", "rawValue", "ref", "displayValue", "computed", "formatTime", "value", "props", "format", "formatter", "val", "stopTimer", "cAF", "startTimer", "timestamp", "getTime", "frameFunc", "diff", "Date", "now", "emit", "CHANGE_EVENT", "rAF", "onMounted", "watch", "immediate", "onBeforeUnmount", "expose", "_ctx", "_cache", "openBlock", "createBlock", "unref", "ElStatistic", "title", "prefix", "suffix", "valueStyle"], "sources": ["../../../../../../packages/components/countdown/src/countdown.vue"], "sourcesContent": ["<template>\n  <el-statistic\n    :value=\"rawValue\"\n    :title=\"title\"\n    :prefix=\"prefix\"\n    :suffix=\"suffix\"\n    :value-style=\"valueStyle\"\n    :formatter=\"formatter\"\n  >\n    <template v-for=\"(_, name) in $slots\" #[name]>\n      <slot :name=\"name\" />\n    </template>\n  </el-statistic>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'\nimport { ElStatistic } from '@element-plus/components/statistic'\nimport { cAF, rAF } from '@element-plus/utils'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { countdownEmits, countdownProps } from './countdown'\nimport { formatTime, getTime } from './utils'\n\ndefineOptions({\n  name: 'ElCountdown',\n})\n\nconst props = defineProps(countdownProps)\nconst emit = defineEmits(countdownEmits)\n\nlet timer: ReturnType<typeof rAF> | undefined\nconst rawValue = ref<number>(0)\nconst displayValue = computed(() => formatTime(rawValue.value, props.format))\n\nconst formatter = (val: number) => formatTime(val, props.format)\n\nconst stopTimer = () => {\n  if (timer) {\n    cAF(timer)\n    timer = undefined\n  }\n}\n\nconst startTimer = () => {\n  const timestamp = getTime(props.value)\n  const frameFunc = () => {\n    let diff = timestamp - Date.now()\n    emit(CHANGE_EVENT, diff)\n    if (diff <= 0) {\n      diff = 0\n      stopTimer()\n      emit('finish')\n    } else {\n      timer = rAF(frameFunc)\n    }\n    rawValue.value = diff\n  }\n  timer = rAF(frameFunc)\n}\n\nonMounted(() => {\n  rawValue.value = getTime(props.value) - Date.now()\n\n  watch(\n    () => [props.value, props.format],\n    () => {\n      stopTimer()\n      startTimer()\n    },\n    {\n      immediate: true,\n    }\n  )\n})\n\nonBeforeUnmount(() => {\n  stopTimer()\n})\n\ndefineExpose({\n  /**\n   * @description current display value\n   */\n  displayValue,\n})\n</script>\n"], "mappings": ";;;;;;;mCAuBc;EACZA,IAAM;AACR;;;;;;;;;;IAKI,IAAAC,KAAA;IACE,MAAAC,QAAA,GAAWC,GAAA,CAAY,CAAC;IACxB,MAAAC,YAAA,GAAeC,QAAA,CAAS,MAAMC,UAAA,CAAWJ,QAAA,CAASK,KAAO,EAAAC,KAAA,CAAMC,MAAM,CAAC;IAE5E,MAAMC,SAAA,GAAaC,GAAA,IAAgBL,UAAW,CAAAK,GAAA,EAAKH,KAAA,CAAMC,MAAM;IAE/D,MAAMG,SAAA,GAAYA,CAAA,KAAM;MACtB,IAAIX,KAAO;QACTY,GAAA,CAAIZ,KAAK;QACDA,KAAA;MAAA;IACV,CACF;IAEA,MAAMa,UAAA,GAAaA,CAAA,KAAM;MACjB,MAAAC,SAAA,GAAYC,OAAQ,CAAAR,KAAA,CAAMD,KAAK;MACrC,MAAMU,SAAA,GAAYA,CAAA,KAAM;QAClB,IAAAC,IAAA,GAAOH,SAAY,GAAAI,IAAA,CAAKC,GAAI;QAChCC,IAAA,CAAKC,YAAA,EAAcJ,IAAI;QACvB,IAAIA,IAAA,IAAQ,CAAG;UACNA,IAAA;UACGN,SAAA;UACVS,IAAA,CAAK,QAAQ;QAAA,CACR;UACLpB,KAAA,GAAQsB,GAAA,CAAIN,SAAS;QAAA;QAEvBf,QAAA,CAASK,KAAQ,GAAAW,IAAA;MAAA,CACnB;MACAjB,KAAA,GAAQsB,GAAA,CAAIN,SAAS;IAAA,CACvB;IAEAO,SAAA,CAAU,MAAM;MACdtB,QAAA,CAASK,KAAA,GAAQS,OAAQ,CAAAR,KAAA,CAAMD,KAAK,IAAIY,IAAA,CAAKC,GAAI;MAEjDK,KAAA,QAAAjB,KAAA,CAAAD,KAAA,EAAAC,KAAA,CAAAC,MAAA;QACEG,SAAa;QACbE,UAAM;MACJ,CAAU;QACCY,SAAA;MAAA,CACb;IAAA,CACA;IAAAC,eACa;MACbf,SAAA;IAAA,CACF;IACFgB,MAAC;MAEDxB;IACE,CAAU;IACZ,OAAC,CAAAyB,IAAA,EAAAC,MAAA;MAEY,OAAAC,SAAA,IAAAC,WAAA,CAAAC,KAAA,CAAAC,WAAA;QAAA3B,KAAA,EAAAL,QAAA,CAAAK,KAAA;QAAA4B,KAAA,EAAAN,IAAA,CAAAM,KAAA;QAAAC,MAAA,EAAAP,IAAA,CAAAO,MAAA;QAIXC,MAAA,EAAAR,IAAA,CAAAQ,MAAA;QACD,eAAAR,IAAA,CAAAS,UAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}