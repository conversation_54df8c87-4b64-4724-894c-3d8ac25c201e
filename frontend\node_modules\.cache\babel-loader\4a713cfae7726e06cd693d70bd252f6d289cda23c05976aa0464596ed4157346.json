{"ast": null, "code": "import Teleport from './src/teleport.mjs';\nexport { teleportProps } from './src/teleport2.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElTeleport = withInstall(Teleport);\nexport { ElTeleport, ElTeleport as default };", "map": {"version": 3, "names": ["ElTeleport", "withInstall", "Teleport"], "sources": ["../../../../../packages/components/teleport/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Teleport from './src/teleport.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTeleport: SFCWithInstall<typeof Teleport> = withInstall(Teleport)\n\nexport default ElTeleport\n\nexport * from './src/teleport'\n"], "mappings": ";;;AAEY,MAACA,UAAU,GAAGC,WAAW,CAACC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}