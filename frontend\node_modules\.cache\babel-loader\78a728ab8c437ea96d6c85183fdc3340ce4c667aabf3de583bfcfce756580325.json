{"ast": null, "code": "import { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isString } from '@vue/shared';\nimport { isNumber, isBoolean } from '../../../utils/types.mjs';\nconst checkboxProps = {\n  modelValue: {\n    type: [Number, String, Boolean],\n    default: void 0\n  },\n  label: {\n    type: [String, Boolean, Number, Object],\n    default: void 0\n  },\n  value: {\n    type: [String, Boolean, Number, Object],\n    default: void 0\n  },\n  indeterminate: Boolean,\n  disabled: Boolean,\n  checked: Boolean,\n  name: {\n    type: String,\n    default: void 0\n  },\n  trueValue: {\n    type: [String, Number],\n    default: void 0\n  },\n  falseValue: {\n    type: [String, Number],\n    default: void 0\n  },\n  trueLabel: {\n    type: [String, Number],\n    default: void 0\n  },\n  falseLabel: {\n    type: [String, Number],\n    default: void 0\n  },\n  id: {\n    type: String,\n    default: void 0\n  },\n  border: Boolean,\n  size: useSizeProp,\n  tabindex: [String, Number],\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  ...useAriaProps([\"ariaControls\"])\n};\nconst checkboxEmits = {\n  [UPDATE_MODEL_EVENT]: val => isString(val) || isNumber(val) || isBoolean(val),\n  change: val => isString(val) || isNumber(val) || isBoolean(val)\n};\nexport { checkboxEmits, checkboxProps };", "map": {"version": 3, "names": ["checkboxProps", "modelValue", "type", "Number", "String", "Boolean", "default", "label", "Object", "value", "indeterminate", "disabled", "checked", "name", "trueValue", "falseValue", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "id", "border", "size", "useSizeProp", "tabindex", "validateEvent", "useAriaProps", "checkboxEmits", "UPDATE_MODEL_EVENT", "val", "isString", "isNumber", "isBoolean", "change"], "sources": ["../../../../../../packages/components/checkbox/src/checkbox.ts"], "sourcesContent": ["import { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { useAriaProps, useSizeProp } from '@element-plus/hooks'\nimport { isBoolean, isNumber, isString } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Checkbox from './checkbox.vue'\n\nexport type CheckboxValueType = string | number | boolean\n\nexport const checkboxProps = {\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: [Number, String, Boolean],\n    default: undefined,\n  },\n  /**\n   * @description label of the Checkbox when used inside a `checkbox-group`\n   */\n  label: {\n    type: [String, Boolean, Number, Object],\n    default: undefined,\n  },\n  /**\n   * @description value of the Checkbox when used inside a `checkbox-group`\n   */\n  value: {\n    type: [String, Boolean, Number, Object],\n    default: undefined,\n  },\n  /**\n   * @description Set indeterminate state, only responsible for style control\n   */\n  indeterminate: Boolean,\n  /**\n   * @description whether the Checkbox is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description if the Checkbox is checked\n   */\n  checked: <PERSON>olean,\n  /**\n   * @description native 'name' attribute\n   */\n  name: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description value of the Checkbox if it's checked\n   */\n  trueValue: {\n    type: [String, Number],\n    default: undefined,\n  },\n  /**\n   * @description value of the Checkbox if it's not checked\n   */\n  falseValue: {\n    type: [String, Number],\n    default: undefined,\n  },\n  /**\n   * @deprecated use `trueValue` instead\n   * @description value of the Checkbox if it's checked\n   */\n  trueLabel: {\n    type: [String, Number],\n    default: undefined,\n  },\n  /**\n   * @deprecated use `falseValue` instead\n   * @description value of the Checkbox if it's not checked\n   */\n  falseLabel: {\n    type: [String, Number],\n    default: undefined,\n  },\n  /**\n   * @description input id\n   */\n  id: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description whether to add a border around Checkbox\n   */\n  border: Boolean,\n  /**\n   * @description size of the Checkbox\n   */\n  size: useSizeProp,\n  /**\n   * @description input tabindex\n   */\n  tabindex: [String, Number],\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  ...useAriaProps(['ariaControls']),\n}\n\nexport const checkboxEmits = {\n  [UPDATE_MODEL_EVENT]: (val: CheckboxValueType) =>\n    isString(val) || isNumber(val) || isBoolean(val),\n  change: (val: CheckboxValueType) =>\n    isString(val) || isNumber(val) || isBoolean(val),\n}\n\nexport type CheckboxProps = ExtractPropTypes<typeof checkboxProps>\nexport type CheckboxEmits = typeof checkboxEmits\nexport type CheckboxInstance = InstanceType<typeof Checkbox> & unknown\n"], "mappings": ";;;;;AAGY,MAACA,aAAa,GAAG;EAC3BC,UAAU,EAAE;IACVC,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,EAAEC,OAAO,CAAC;IAC/BC,OAAO,EAAE,KAAK;EAClB,CAAG;EACDC,KAAK,EAAE;IACLL,IAAI,EAAE,CAACE,MAAM,EAAEC,OAAO,EAAEF,MAAM,EAAEK,MAAM,CAAC;IACvCF,OAAO,EAAE,KAAK;EAClB,CAAG;EACDG,KAAK,EAAE;IACLP,IAAI,EAAE,CAACE,MAAM,EAAEC,OAAO,EAAEF,MAAM,EAAEK,MAAM,CAAC;IACvCF,OAAO,EAAE,KAAK;EAClB,CAAG;EACDI,aAAa,EAAEL,OAAO;EACtBM,QAAQ,EAAEN,OAAO;EACjBO,OAAO,EAAEP,OAAO;EAChBQ,IAAI,EAAE;IACJX,IAAI,EAAEE,MAAM;IACZE,OAAO,EAAE,KAAK;EAClB,CAAG;EACDQ,SAAS,EAAE;IACTZ,IAAI,EAAE,CAACE,MAAM,EAAED,MAAM,CAAC;IACtBG,OAAO,EAAE,KAAK;EAClB,CAAG;EACDS,UAAU,EAAE;IACVb,IAAI,EAAE,CAACE,MAAM,EAAED,MAAM,CAAC;IACtBG,OAAO,EAAE,KAAK;EAClB,CAAG;EACDU,SAAS,EAAE;IACTd,IAAI,EAAE,CAACE,MAAM,EAAED,MAAM,CAAC;IACtBG,OAAO,EAAE,KAAK;EAClB,CAAG;EACDW,UAAU,EAAE;IACVf,IAAI,EAAE,CAACE,MAAM,EAAED,MAAM,CAAC;IACtBG,OAAO,EAAE,KAAK;EAClB,CAAG;EACDY,EAAE,EAAE;IACFhB,IAAI,EAAEE,MAAM;IACZE,OAAO,EAAE,KAAK;EAClB,CAAG;EACDa,MAAM,EAAEd,OAAO;EACfe,IAAI,EAAEC,WAAW;EACjBC,QAAQ,EAAE,CAAClB,MAAM,EAAED,MAAM,CAAC;EAC1BoB,aAAa,EAAE;IACbrB,IAAI,EAAEG,OAAO;IACbC,OAAO,EAAE;EACb,CAAG;EACD,GAAGkB,YAAY,CAAC,CAAC,cAAc,CAAC;AAClC;AACY,MAACC,aAAa,GAAG;EAC3B,CAACC,kBAAkB,GAAIC,GAAG,IAAKC,QAAQ,CAACD,GAAG,CAAC,IAAIE,QAAQ,CAACF,GAAG,CAAC,IAAIG,SAAS,CAACH,GAAG,CAAC;EAC/EI,MAAM,EAAGJ,GAAG,IAAKC,QAAQ,CAACD,GAAG,CAAC,IAAIE,QAAQ,CAACF,GAAG,CAAC,IAAIG,SAAS,CAACH,GAAG;AAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}