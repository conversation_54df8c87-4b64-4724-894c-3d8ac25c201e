{"ast": null, "code": "import Card from './src/card2.mjs';\nexport { cardProps } from './src/card.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElCard = withInstall(Card);\nexport { ElCard, ElCard as default };", "map": {"version": 3, "names": ["ElCard", "withInstall", "Card"], "sources": ["../../../../../packages/components/card/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Card from './src/card.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCard: SFCWithInstall<typeof Card> = withInstall(Card)\nexport default ElCard\n\nexport * from './src/card'\nexport type { CardInstance } from './src/instance'\n"], "mappings": ";;;AAEY,MAACA,MAAM,GAAGC,WAAW,CAACC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}