{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { ref, isRef, nextTick } from 'vue';\nimport { isNull } from 'lodash-unified';\nimport { parseHeight } from './util.mjs';\nimport { hasOwn, isString } from '@vue/shared';\nimport { isClient } from '@vueuse/core';\nimport { isNumber } from '../../../utils/types.mjs';\nclass TableLayout {\n  constructor(options) {\n    this.observers = [];\n    this.table = null;\n    this.store = null;\n    this.columns = [];\n    this.fit = true;\n    this.showHeader = true;\n    this.height = ref(null);\n    this.scrollX = ref(false);\n    this.scrollY = ref(false);\n    this.bodyWidth = ref(null);\n    this.fixedWidth = ref(null);\n    this.rightFixedWidth = ref(null);\n    this.gutterWidth = 0;\n    for (const name in options) {\n      if (hasOwn(options, name)) {\n        if (isRef(this[name])) {\n          this[name].value = options[name];\n        } else {\n          this[name] = options[name];\n        }\n      }\n    }\n    if (!this.table) {\n      throw new Error(\"Table is required for Table Layout\");\n    }\n    if (!this.store) {\n      throw new Error(\"Store is required for Table Layout\");\n    }\n  }\n  updateScrollY() {\n    const height = this.height.value;\n    if (isNull(height)) return false;\n    const scrollBarRef = this.table.refs.scrollBarRef;\n    if (this.table.vnode.el && (scrollBarRef == null ? void 0 : scrollBarRef.wrapRef)) {\n      let scrollY = true;\n      const prevScrollY = this.scrollY.value;\n      scrollY = scrollBarRef.wrapRef.scrollHeight > scrollBarRef.wrapRef.clientHeight;\n      this.scrollY.value = scrollY;\n      return prevScrollY !== scrollY;\n    }\n    return false;\n  }\n  setHeight(value, prop = \"height\") {\n    if (!isClient) return;\n    const el = this.table.vnode.el;\n    value = parseHeight(value);\n    this.height.value = Number(value);\n    if (!el && (value || value === 0)) return nextTick(() => this.setHeight(value, prop));\n    if (isNumber(value)) {\n      el.style[prop] = `${value}px`;\n      this.updateElsHeight();\n    } else if (isString(value)) {\n      el.style[prop] = value;\n      this.updateElsHeight();\n    }\n  }\n  setMaxHeight(value) {\n    this.setHeight(value, \"max-height\");\n  }\n  getFlattenColumns() {\n    const flattenColumns = [];\n    const columns = this.table.store.states.columns.value;\n    columns.forEach(column => {\n      if (column.isColumnGroup) {\n        flattenColumns.push.apply(flattenColumns, column.columns);\n      } else {\n        flattenColumns.push(column);\n      }\n    });\n    return flattenColumns;\n  }\n  updateElsHeight() {\n    this.updateScrollY();\n    this.notifyObservers(\"scrollable\");\n  }\n  headerDisplayNone(elm) {\n    if (!elm) return true;\n    let headerChild = elm;\n    while (headerChild.tagName !== \"DIV\") {\n      if (getComputedStyle(headerChild).display === \"none\") {\n        return true;\n      }\n      headerChild = headerChild.parentElement;\n    }\n    return false;\n  }\n  updateColumnsWidth() {\n    if (!isClient) return;\n    const fit = this.fit;\n    const bodyWidth = this.table.vnode.el.clientWidth;\n    let bodyMinWidth = 0;\n    const flattenColumns = this.getFlattenColumns();\n    const flexColumns = flattenColumns.filter(column => !isNumber(column.width));\n    flattenColumns.forEach(column => {\n      if (isNumber(column.width) && column.realWidth) column.realWidth = null;\n    });\n    if (flexColumns.length > 0 && fit) {\n      flattenColumns.forEach(column => {\n        bodyMinWidth += Number(column.width || column.minWidth || 80);\n      });\n      if (bodyMinWidth <= bodyWidth) {\n        this.scrollX.value = false;\n        const totalFlexWidth = bodyWidth - bodyMinWidth;\n        if (flexColumns.length === 1) {\n          flexColumns[0].realWidth = Number(flexColumns[0].minWidth || 80) + totalFlexWidth;\n        } else {\n          const allColumnsWidth = flexColumns.reduce((prev, column) => prev + Number(column.minWidth || 80), 0);\n          const flexWidthPerPixel = totalFlexWidth / allColumnsWidth;\n          let noneFirstWidth = 0;\n          flexColumns.forEach((column, index) => {\n            if (index === 0) return;\n            const flexWidth = Math.floor(Number(column.minWidth || 80) * flexWidthPerPixel);\n            noneFirstWidth += flexWidth;\n            column.realWidth = Number(column.minWidth || 80) + flexWidth;\n          });\n          flexColumns[0].realWidth = Number(flexColumns[0].minWidth || 80) + totalFlexWidth - noneFirstWidth;\n        }\n      } else {\n        this.scrollX.value = true;\n        flexColumns.forEach(column => {\n          column.realWidth = Number(column.minWidth);\n        });\n      }\n      this.bodyWidth.value = Math.max(bodyMinWidth, bodyWidth);\n      this.table.state.resizeState.value.width = this.bodyWidth.value;\n    } else {\n      flattenColumns.forEach(column => {\n        if (!column.width && !column.minWidth) {\n          column.realWidth = 80;\n        } else {\n          column.realWidth = Number(column.width || column.minWidth);\n        }\n        bodyMinWidth += column.realWidth;\n      });\n      this.scrollX.value = bodyMinWidth > bodyWidth;\n      this.bodyWidth.value = bodyMinWidth;\n    }\n    const fixedColumns = this.store.states.fixedColumns.value;\n    if (fixedColumns.length > 0) {\n      let fixedWidth = 0;\n      fixedColumns.forEach(column => {\n        fixedWidth += Number(column.realWidth || column.width);\n      });\n      this.fixedWidth.value = fixedWidth;\n    }\n    const rightFixedColumns = this.store.states.rightFixedColumns.value;\n    if (rightFixedColumns.length > 0) {\n      let rightFixedWidth = 0;\n      rightFixedColumns.forEach(column => {\n        rightFixedWidth += Number(column.realWidth || column.width);\n      });\n      this.rightFixedWidth.value = rightFixedWidth;\n    }\n    this.notifyObservers(\"columns\");\n  }\n  addObserver(observer) {\n    this.observers.push(observer);\n  }\n  removeObserver(observer) {\n    const index = this.observers.indexOf(observer);\n    if (index !== -1) {\n      this.observers.splice(index, 1);\n    }\n  }\n  notifyObservers(event) {\n    const observers = this.observers;\n    observers.forEach(observer => {\n      var _a, _b;\n      switch (event) {\n        case \"columns\":\n          (_a = observer.state) == null ? void 0 : _a.onColumnsChange(this);\n          break;\n        case \"scrollable\":\n          (_b = observer.state) == null ? void 0 : _b.onScrollableChange(this);\n          break;\n        default:\n          throw new Error(`Table Layout don't have event ${event}.`);\n      }\n    });\n  }\n}\nexport { TableLayout as default };", "map": {"version": 3, "names": ["TableLayout", "constructor", "options", "observers", "table", "store", "columns", "fit", "showHeader", "height", "ref", "scrollX", "scrollY", "bodyWidth", "fixedWidth", "rightFixedWidth", "gutterWidth", "name", "hasOwn", "isRef", "value", "Error", "updateScrollY", "isNull", "scrollBarRef", "refs", "vnode", "el", "wrapRef", "prevScrollY", "scrollHeight", "clientHeight", "setHeight", "prop", "isClient", "parseHeight", "Number", "nextTick", "isNumber", "style", "updateElsHeight", "isString", "setMaxHeight", "getFlattenColumns", "flattenColumns", "states", "for<PERSON>ach", "column", "isColumnGroup", "push", "apply", "notifyObservers", "headerDisplayNone", "elm", "headerChild", "tagName", "getComputedStyle", "display", "parentElement", "updateColumnsWidth", "clientWidth", "body<PERSON><PERSON><PERSON><PERSON><PERSON>", "flexColumns", "filter", "width", "realWidth", "length", "min<PERSON><PERSON><PERSON>", "totalFlexWidth", "allColumnsWidth", "reduce", "prev", "flexWidthPerPixel", "none<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "index", "flexWidth", "Math", "floor", "max", "state", "resizeState", "fixedColumns", "rightFixedColumns", "addObserver", "observer", "removeObserver", "indexOf", "splice", "event", "_a", "_b", "onColumnsChange", "onScrollableChange"], "sources": ["../../../../../../packages/components/table/src/table-layout.ts"], "sourcesContent": ["// @ts-nocheck\nimport { isRef, nextTick, ref } from 'vue'\nimport { isNull } from 'lodash-unified'\nimport { hasOwn, isClient, isNumber, isString } from '@element-plus/utils'\nimport { parseHeight } from './util'\n\nimport type { Ref } from 'vue'\nimport type { TableColumnCtx } from './table-column/defaults'\nimport type { TableHeader } from './table-header'\nimport type { Table } from './table/defaults'\nimport type { Store } from './store'\n\nclass TableLayout<T> {\n  observers: TableHeader[]\n  table: Table<T>\n  store: Store<T>\n  columns: TableColumnCtx<T>[]\n  fit: boolean\n  showHeader: boolean\n\n  height: Ref<null | number>\n  scrollX: Ref<boolean>\n  scrollY: Ref<boolean>\n  bodyWidth: Ref<null | number>\n  fixedWidth: Ref<null | number>\n  rightFixedWidth: Ref<null | number>\n  tableHeight: Ref<null | number>\n  headerHeight: Ref<null | number> // Table Header Height\n  appendHeight: Ref<null | number> // Append Slot Height\n  footerHeight: Ref<null | number> // Table Footer Height\n  gutterWidth: number\n  constructor(options: Record<string, any>) {\n    this.observers = []\n    this.table = null\n    this.store = null\n    this.columns = []\n    this.fit = true\n    this.showHeader = true\n    this.height = ref(null)\n    this.scrollX = ref(false)\n    this.scrollY = ref(false)\n    this.bodyWidth = ref(null)\n    this.fixedWidth = ref(null)\n    this.rightFixedWidth = ref(null)\n    this.gutterWidth = 0\n    for (const name in options) {\n      if (hasOwn(options, name)) {\n        if (isRef(this[name])) {\n          this[name as string].value = options[name]\n        } else {\n          this[name as string] = options[name]\n        }\n      }\n    }\n    if (!this.table) {\n      throw new Error('Table is required for Table Layout')\n    }\n    if (!this.store) {\n      throw new Error('Store is required for Table Layout')\n    }\n  }\n\n  updateScrollY() {\n    const height = this.height.value\n    /**\n     * When the height is not initialized, it is null.\n     * After the table is initialized, when the height is not configured, the height is 0.\n     */\n    if (isNull(height)) return false\n    const scrollBarRef = this.table.refs.scrollBarRef\n    if (this.table.vnode.el && scrollBarRef?.wrapRef) {\n      let scrollY = true\n      const prevScrollY = this.scrollY.value\n      scrollY =\n        scrollBarRef.wrapRef.scrollHeight > scrollBarRef.wrapRef.clientHeight\n      this.scrollY.value = scrollY\n      return prevScrollY !== scrollY\n    }\n    return false\n  }\n\n  setHeight(value: string | number, prop = 'height') {\n    if (!isClient) return\n    const el = this.table.vnode.el\n    value = parseHeight(value)\n    this.height.value = Number(value)\n\n    if (!el && (value || value === 0))\n      return nextTick(() => this.setHeight(value, prop))\n\n    if (isNumber(value)) {\n      el.style[prop] = `${value}px`\n      this.updateElsHeight()\n    } else if (isString(value)) {\n      el.style[prop] = value\n      this.updateElsHeight()\n    }\n  }\n\n  setMaxHeight(value: string | number) {\n    this.setHeight(value, 'max-height')\n  }\n\n  getFlattenColumns(): TableColumnCtx<T>[] {\n    const flattenColumns = []\n    const columns = this.table.store.states.columns.value\n    columns.forEach((column) => {\n      if (column.isColumnGroup) {\n        // eslint-disable-next-line prefer-spread\n        flattenColumns.push.apply(flattenColumns, column.columns)\n      } else {\n        flattenColumns.push(column)\n      }\n    })\n\n    return flattenColumns\n  }\n\n  updateElsHeight() {\n    this.updateScrollY()\n    this.notifyObservers('scrollable')\n  }\n\n  headerDisplayNone(elm: HTMLElement) {\n    if (!elm) return true\n    let headerChild = elm\n    while (headerChild.tagName !== 'DIV') {\n      if (getComputedStyle(headerChild).display === 'none') {\n        return true\n      }\n      headerChild = headerChild.parentElement\n    }\n    return false\n  }\n\n  updateColumnsWidth() {\n    if (!isClient) return\n    const fit = this.fit\n    const bodyWidth = this.table.vnode.el.clientWidth\n    let bodyMinWidth = 0\n\n    const flattenColumns = this.getFlattenColumns()\n    const flexColumns = flattenColumns.filter(\n      (column) => !isNumber(column.width)\n    )\n    flattenColumns.forEach((column) => {\n      // Clean those columns whose width changed from flex to unflex\n      if (isNumber(column.width) && column.realWidth) column.realWidth = null\n    })\n    if (flexColumns.length > 0 && fit) {\n      flattenColumns.forEach((column) => {\n        bodyMinWidth += Number(column.width || column.minWidth || 80)\n      })\n      if (bodyMinWidth <= bodyWidth) {\n        // DON'T HAVE SCROLL BAR\n        this.scrollX.value = false\n\n        const totalFlexWidth = bodyWidth - bodyMinWidth\n\n        if (flexColumns.length === 1) {\n          flexColumns[0].realWidth =\n            Number(flexColumns[0].minWidth || 80) + totalFlexWidth\n        } else {\n          const allColumnsWidth = flexColumns.reduce(\n            (prev, column) => prev + Number(column.minWidth || 80),\n            0\n          )\n          const flexWidthPerPixel = totalFlexWidth / allColumnsWidth\n          let noneFirstWidth = 0\n\n          flexColumns.forEach((column, index) => {\n            if (index === 0) return\n            const flexWidth = Math.floor(\n              Number(column.minWidth || 80) * flexWidthPerPixel\n            )\n            noneFirstWidth += flexWidth\n            column.realWidth = Number(column.minWidth || 80) + flexWidth\n          })\n\n          flexColumns[0].realWidth =\n            Number(flexColumns[0].minWidth || 80) +\n            totalFlexWidth -\n            noneFirstWidth\n        }\n      } else {\n        // HAVE HORIZONTAL SCROLL BAR\n        this.scrollX.value = true\n        flexColumns.forEach((column) => {\n          column.realWidth = Number(column.minWidth)\n        })\n      }\n\n      this.bodyWidth.value = Math.max(bodyMinWidth, bodyWidth)\n      this.table.state.resizeState.value.width = this.bodyWidth.value\n    } else {\n      flattenColumns.forEach((column) => {\n        if (!column.width && !column.minWidth) {\n          column.realWidth = 80\n        } else {\n          column.realWidth = Number(column.width || column.minWidth)\n        }\n        bodyMinWidth += column.realWidth\n      })\n      this.scrollX.value = bodyMinWidth > bodyWidth\n\n      this.bodyWidth.value = bodyMinWidth\n    }\n\n    const fixedColumns = this.store.states.fixedColumns.value\n\n    if (fixedColumns.length > 0) {\n      let fixedWidth = 0\n      fixedColumns.forEach((column) => {\n        fixedWidth += Number(column.realWidth || column.width)\n      })\n\n      this.fixedWidth.value = fixedWidth\n    }\n\n    const rightFixedColumns = this.store.states.rightFixedColumns.value\n    if (rightFixedColumns.length > 0) {\n      let rightFixedWidth = 0\n      rightFixedColumns.forEach((column) => {\n        rightFixedWidth += Number(column.realWidth || column.width)\n      })\n\n      this.rightFixedWidth.value = rightFixedWidth\n    }\n    this.notifyObservers('columns')\n  }\n\n  addObserver(observer: TableHeader) {\n    this.observers.push(observer)\n  }\n\n  removeObserver(observer: TableHeader) {\n    const index = this.observers.indexOf(observer)\n    if (index !== -1) {\n      this.observers.splice(index, 1)\n    }\n  }\n\n  notifyObservers(event: string) {\n    const observers = this.observers\n    observers.forEach((observer) => {\n      switch (event) {\n        case 'columns':\n          observer.state?.onColumnsChange(this)\n          break\n        case 'scrollable':\n          observer.state?.onScrollableChange(this)\n          break\n        default:\n          throw new Error(`Table Layout don't have event ${event}.`)\n      }\n    })\n  }\n}\n\nexport default TableLayout\n"], "mappings": ";;;;;;;;;;;AAIA,MAAMA,WAAW,CAAC;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,GAAG,GAAG,IAAI;IACf,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,MAAM,GAAGC,GAAG,CAAC,IAAI,CAAC;IACvB,IAAI,CAACC,OAAO,GAAGD,GAAG,CAAC,KAAK,CAAC;IACzB,IAAI,CAACE,OAAO,GAAGF,GAAG,CAAC,KAAK,CAAC;IACzB,IAAI,CAACG,SAAS,GAAGH,GAAG,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACI,UAAU,GAAGJ,GAAG,CAAC,IAAI,CAAC;IAC3B,IAAI,CAACK,eAAe,GAAGL,GAAG,CAAC,IAAI,CAAC;IAChC,IAAI,CAACM,WAAW,GAAG,CAAC;IACpB,KAAK,MAAMC,IAAI,IAAIf,OAAO,EAAE;MAC1B,IAAIgB,MAAM,CAAChB,OAAO,EAAEe,IAAI,CAAC,EAAE;QACzB,IAAIE,KAAK,CAAC,IAAI,CAACF,IAAI,CAAC,CAAC,EAAE;UACrB,IAAI,CAACA,IAAI,CAAC,CAACG,KAAK,GAAGlB,OAAO,CAACe,IAAI,CAAC;QAC1C,CAAS,MAAM;UACL,IAAI,CAACA,IAAI,CAAC,GAAGf,OAAO,CAACe,IAAI,CAAC;QACpC;MACA;IACA;IACI,IAAI,CAAC,IAAI,CAACb,KAAK,EAAE;MACf,MAAM,IAAIiB,KAAK,CAAC,oCAAoC,CAAC;IAC3D;IACI,IAAI,CAAC,IAAI,CAAChB,KAAK,EAAE;MACf,MAAM,IAAIgB,KAAK,CAAC,oCAAoC,CAAC;IAC3D;EACA;EACEC,aAAaA,CAAA,EAAG;IACd,MAAMb,MAAM,GAAG,IAAI,CAACA,MAAM,CAACW,KAAK;IAChC,IAAIG,MAAM,CAACd,MAAM,CAAC,EAChB,OAAO,KAAK;IACd,MAAMe,YAAY,GAAG,IAAI,CAACpB,KAAK,CAACqB,IAAI,CAACD,YAAY;IACjD,IAAI,IAAI,CAACpB,KAAK,CAACsB,KAAK,CAACC,EAAE,KAAKH,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACI,OAAO,CAAC,EAAE;MACjF,IAAIhB,OAAO,GAAG,IAAI;MAClB,MAAMiB,WAAW,GAAG,IAAI,CAACjB,OAAO,CAACQ,KAAK;MACtCR,OAAO,GAAGY,YAAY,CAACI,OAAO,CAACE,YAAY,GAAGN,YAAY,CAACI,OAAO,CAACG,YAAY;MAC/E,IAAI,CAACnB,OAAO,CAACQ,KAAK,GAAGR,OAAO;MAC5B,OAAOiB,WAAW,KAAKjB,OAAO;IACpC;IACI,OAAO,KAAK;EAChB;EACEoB,SAASA,CAACZ,KAAK,EAAEa,IAAI,GAAG,QAAQ,EAAE;IAChC,IAAI,CAACC,QAAQ,EACX;IACF,MAAMP,EAAE,GAAG,IAAI,CAACvB,KAAK,CAACsB,KAAK,CAACC,EAAE;IAC9BP,KAAK,GAAGe,WAAW,CAACf,KAAK,CAAC;IAC1B,IAAI,CAACX,MAAM,CAACW,KAAK,GAAGgB,MAAM,CAAChB,KAAK,CAAC;IACjC,IAAI,CAACO,EAAE,KAAKP,KAAK,IAAIA,KAAK,KAAK,CAAC,CAAC,EAC/B,OAAOiB,QAAQ,CAAC,MAAM,IAAI,CAACL,SAAS,CAACZ,KAAK,EAAEa,IAAI,CAAC,CAAC;IACpD,IAAIK,QAAQ,CAAClB,KAAK,CAAC,EAAE;MACnBO,EAAE,CAACY,KAAK,CAACN,IAAI,CAAC,GAAG,GAAGb,KAAK,IAAI;MAC7B,IAAI,CAACoB,eAAe,EAAE;IAC5B,CAAK,MAAM,IAAIC,QAAQ,CAACrB,KAAK,CAAC,EAAE;MAC1BO,EAAE,CAACY,KAAK,CAACN,IAAI,CAAC,GAAGb,KAAK;MACtB,IAAI,CAACoB,eAAe,EAAE;IAC5B;EACA;EACEE,YAAYA,CAACtB,KAAK,EAAE;IAClB,IAAI,CAACY,SAAS,CAACZ,KAAK,EAAE,YAAY,CAAC;EACvC;EACEuB,iBAAiBA,CAAA,EAAG;IAClB,MAAMC,cAAc,GAAG,EAAE;IACzB,MAAMtC,OAAO,GAAG,IAAI,CAACF,KAAK,CAACC,KAAK,CAACwC,MAAM,CAACvC,OAAO,CAACc,KAAK;IACrDd,OAAO,CAACwC,OAAO,CAAEC,MAAM,IAAK;MAC1B,IAAIA,MAAM,CAACC,aAAa,EAAE;QACxBJ,cAAc,CAACK,IAAI,CAACC,KAAK,CAACN,cAAc,EAAEG,MAAM,CAACzC,OAAO,CAAC;MACjE,CAAO,MAAM;QACLsC,cAAc,CAACK,IAAI,CAACF,MAAM,CAAC;MACnC;IACA,CAAK,CAAC;IACF,OAAOH,cAAc;EACzB;EACEJ,eAAeA,CAAA,EAAG;IAChB,IAAI,CAAClB,aAAa,EAAE;IACpB,IAAI,CAAC6B,eAAe,CAAC,YAAY,CAAC;EACtC;EACEC,iBAAiBA,CAACC,GAAG,EAAE;IACrB,IAAI,CAACA,GAAG,EACN,OAAO,IAAI;IACb,IAAIC,WAAW,GAAGD,GAAG;IACrB,OAAOC,WAAW,CAACC,OAAO,KAAK,KAAK,EAAE;MACpC,IAAIC,gBAAgB,CAACF,WAAW,CAAC,CAACG,OAAO,KAAK,MAAM,EAAE;QACpD,OAAO,IAAI;MACnB;MACMH,WAAW,GAAGA,WAAW,CAACI,aAAa;IAC7C;IACI,OAAO,KAAK;EAChB;EACEC,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAACzB,QAAQ,EACX;IACF,MAAM3B,GAAG,GAAG,IAAI,CAACA,GAAG;IACpB,MAAMM,SAAS,GAAG,IAAI,CAACT,KAAK,CAACsB,KAAK,CAACC,EAAE,CAACiC,WAAW;IACjD,IAAIC,YAAY,GAAG,CAAC;IACpB,MAAMjB,cAAc,GAAG,IAAI,CAACD,iBAAiB,EAAE;IAC/C,MAAMmB,WAAW,GAAGlB,cAAc,CAACmB,MAAM,CAAEhB,MAAM,IAAK,CAACT,QAAQ,CAACS,MAAM,CAACiB,KAAK,CAAC,CAAC;IAC9EpB,cAAc,CAACE,OAAO,CAAEC,MAAM,IAAK;MACjC,IAAIT,QAAQ,CAACS,MAAM,CAACiB,KAAK,CAAC,IAAIjB,MAAM,CAACkB,SAAS,EAC5ClB,MAAM,CAACkB,SAAS,GAAG,IAAI;IAC/B,CAAK,CAAC;IACF,IAAIH,WAAW,CAACI,MAAM,GAAG,CAAC,IAAI3D,GAAG,EAAE;MACjCqC,cAAc,CAACE,OAAO,CAAEC,MAAM,IAAK;QACjCc,YAAY,IAAIzB,MAAM,CAACW,MAAM,CAACiB,KAAK,IAAIjB,MAAM,CAACoB,QAAQ,IAAI,EAAE,CAAC;MACrE,CAAO,CAAC;MACF,IAAIN,YAAY,IAAIhD,SAAS,EAAE;QAC7B,IAAI,CAACF,OAAO,CAACS,KAAK,GAAG,KAAK;QAC1B,MAAMgD,cAAc,GAAGvD,SAAS,GAAGgD,YAAY;QAC/C,IAAIC,WAAW,CAACI,MAAM,KAAK,CAAC,EAAE;UAC5BJ,WAAW,CAAC,CAAC,CAAC,CAACG,SAAS,GAAG7B,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACK,QAAQ,IAAI,EAAE,CAAC,GAAGC,cAAc;QAC3F,CAAS,MAAM;UACL,MAAMC,eAAe,GAAGP,WAAW,CAACQ,MAAM,CAAC,CAACC,IAAI,EAAExB,MAAM,KAAKwB,IAAI,GAAGnC,MAAM,CAACW,MAAM,CAACoB,QAAQ,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;UACrG,MAAMK,iBAAiB,GAAGJ,cAAc,GAAGC,eAAe;UAC1D,IAAII,cAAc,GAAG,CAAC;UACtBX,WAAW,CAAChB,OAAO,CAAC,CAACC,MAAM,EAAE2B,KAAK,KAAK;YACrC,IAAIA,KAAK,KAAK,CAAC,EACb;YACF,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACzC,MAAM,CAACW,MAAM,CAACoB,QAAQ,IAAI,EAAE,CAAC,GAAGK,iBAAiB,CAAC;YAC/EC,cAAc,IAAIE,SAAS;YAC3B5B,MAAM,CAACkB,SAAS,GAAG7B,MAAM,CAACW,MAAM,CAACoB,QAAQ,IAAI,EAAE,CAAC,GAAGQ,SAAS;UACxE,CAAW,CAAC;UACFb,WAAW,CAAC,CAAC,CAAC,CAACG,SAAS,GAAG7B,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACK,QAAQ,IAAI,EAAE,CAAC,GAAGC,cAAc,GAAGK,cAAc;QAC5G;MACA,CAAO,MAAM;QACL,IAAI,CAAC9D,OAAO,CAACS,KAAK,GAAG,IAAI;QACzB0C,WAAW,CAAChB,OAAO,CAAEC,MAAM,IAAK;UAC9BA,MAAM,CAACkB,SAAS,GAAG7B,MAAM,CAACW,MAAM,CAACoB,QAAQ,CAAC;QACpD,CAAS,CAAC;MACV;MACM,IAAI,CAACtD,SAAS,CAACO,KAAK,GAAGwD,IAAI,CAACE,GAAG,CAACjB,YAAY,EAAEhD,SAAS,CAAC;MACxD,IAAI,CAACT,KAAK,CAAC2E,KAAK,CAACC,WAAW,CAAC5D,KAAK,CAAC4C,KAAK,GAAG,IAAI,CAACnD,SAAS,CAACO,KAAK;IACrE,CAAK,MAAM;MACLwB,cAAc,CAACE,OAAO,CAAEC,MAAM,IAAK;QACjC,IAAI,CAACA,MAAM,CAACiB,KAAK,IAAI,CAACjB,MAAM,CAACoB,QAAQ,EAAE;UACrCpB,MAAM,CAACkB,SAAS,GAAG,EAAE;QAC/B,CAAS,MAAM;UACLlB,MAAM,CAACkB,SAAS,GAAG7B,MAAM,CAACW,MAAM,CAACiB,KAAK,IAAIjB,MAAM,CAACoB,QAAQ,CAAC;QACpE;QACQN,YAAY,IAAId,MAAM,CAACkB,SAAS;MACxC,CAAO,CAAC;MACF,IAAI,CAACtD,OAAO,CAACS,KAAK,GAAGyC,YAAY,GAAGhD,SAAS;MAC7C,IAAI,CAACA,SAAS,CAACO,KAAK,GAAGyC,YAAY;IACzC;IACI,MAAMoB,YAAY,GAAG,IAAI,CAAC5E,KAAK,CAACwC,MAAM,CAACoC,YAAY,CAAC7D,KAAK;IACzD,IAAI6D,YAAY,CAACf,MAAM,GAAG,CAAC,EAAE;MAC3B,IAAIpD,UAAU,GAAG,CAAC;MAClBmE,YAAY,CAACnC,OAAO,CAAEC,MAAM,IAAK;QAC/BjC,UAAU,IAAIsB,MAAM,CAACW,MAAM,CAACkB,SAAS,IAAIlB,MAAM,CAACiB,KAAK,CAAC;MAC9D,CAAO,CAAC;MACF,IAAI,CAAClD,UAAU,CAACM,KAAK,GAAGN,UAAU;IACxC;IACI,MAAMoE,iBAAiB,GAAG,IAAI,CAAC7E,KAAK,CAACwC,MAAM,CAACqC,iBAAiB,CAAC9D,KAAK;IACnE,IAAI8D,iBAAiB,CAAChB,MAAM,GAAG,CAAC,EAAE;MAChC,IAAInD,eAAe,GAAG,CAAC;MACvBmE,iBAAiB,CAACpC,OAAO,CAAEC,MAAM,IAAK;QACpChC,eAAe,IAAIqB,MAAM,CAACW,MAAM,CAACkB,SAAS,IAAIlB,MAAM,CAACiB,KAAK,CAAC;MACnE,CAAO,CAAC;MACF,IAAI,CAACjD,eAAe,CAACK,KAAK,GAAGL,eAAe;IAClD;IACI,IAAI,CAACoC,eAAe,CAAC,SAAS,CAAC;EACnC;EACEgC,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAACjF,SAAS,CAAC8C,IAAI,CAACmC,QAAQ,CAAC;EACjC;EACEC,cAAcA,CAACD,QAAQ,EAAE;IACvB,MAAMV,KAAK,GAAG,IAAI,CAACvE,SAAS,CAACmF,OAAO,CAACF,QAAQ,CAAC;IAC9C,IAAIV,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAACvE,SAAS,CAACoF,MAAM,CAACb,KAAK,EAAE,CAAC,CAAC;IACrC;EACA;EACEvB,eAAeA,CAACqC,KAAK,EAAE;IACrB,MAAMrF,SAAS,GAAG,IAAI,CAACA,SAAS;IAChCA,SAAS,CAAC2C,OAAO,CAAEsC,QAAQ,IAAK;MAC9B,IAAIK,EAAE,EAAEC,EAAE;MACV,QAAQF,KAAK;QACX,KAAK,SAAS;UACZ,CAACC,EAAE,GAAGL,QAAQ,CAACL,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGU,EAAE,CAACE,eAAe,CAAC,IAAI,CAAC;UACjE;QACF,KAAK,YAAY;UACf,CAACD,EAAE,GAAGN,QAAQ,CAACL,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,EAAE,CAACE,kBAAkB,CAAC,IAAI,CAAC;UACpE;QACF;UACE,MAAM,IAAIvE,KAAK,CAAC,iCAAiCmE,KAAK,GAAG,CAAC;MACpE;IACA,CAAK,CAAC;EACN;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}