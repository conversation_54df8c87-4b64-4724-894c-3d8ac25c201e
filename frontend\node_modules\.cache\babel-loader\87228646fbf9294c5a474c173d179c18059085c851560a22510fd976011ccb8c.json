{"ast": null, "code": "import { defineComponent, ref, provide, createVNode, mergeProps } from 'vue';\nimport dayjs from 'dayjs';\nimport customParseFormat from 'dayjs/plugin/customParseFormat.js';\nimport { DEFAULT_FORMATS_TIME } from './constants.mjs';\nimport CommonPicker from './common/picker.mjs';\nimport TimePickPanel from './time-picker-com/panel-time-pick.mjs';\nimport TimeRangePanel from './time-picker-com/panel-time-range.mjs';\nimport { timePickerDefaultProps } from './common/props.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\ndayjs.extend(customParseFormat);\nvar TimePicker = defineComponent({\n  name: \"ElTimePicker\",\n  install: null,\n  props: {\n    ...timePickerDefaultProps,\n    isRange: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: [UPDATE_MODEL_EVENT],\n  setup(props, ctx) {\n    const commonPicker = ref();\n    const [type, Panel] = props.isRange ? [\"timerange\", TimeRangePanel] : [\"time\", TimePickPanel];\n    const modelUpdater = value => ctx.emit(UPDATE_MODEL_EVENT, value);\n    provide(\"ElPopperOptions\", props.popperOptions);\n    ctx.expose({\n      focus: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.focus();\n      },\n      blur: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.blur();\n      },\n      handleOpen: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.handleOpen();\n      },\n      handleClose: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.handleClose();\n      }\n    });\n    return () => {\n      var _a;\n      const format = (_a = props.format) != null ? _a : DEFAULT_FORMATS_TIME;\n      return createVNode(CommonPicker, mergeProps(props, {\n        \"ref\": commonPicker,\n        \"type\": type,\n        \"format\": format,\n        \"onUpdate:modelValue\": modelUpdater\n      }), {\n        default: props2 => createVNode(Panel, props2, null)\n      });\n    };\n  }\n});\nexport { TimePicker as default };", "map": {"version": 3, "names": ["dayjs", "extend", "customParseFormat", "TimePicker", "defineComponent", "name", "install", "props", "isRange", "type", "Boolean", "default", "emits", "UPDATE_MODEL_EVENT", "setup", "ctx", "commonPicker", "ref", "Panel", "TimeRangePanel", "TimePickPanel", "provide", "popperOptions", "expose", "focus", "_a", "value", "blur", "handleOpen", "handleClose", "createVNode", "CommonPicker", "mergeProps", "format", "modelUpdater"], "sources": ["../../../../../../packages/components/time-picker/src/time-picker.tsx"], "sourcesContent": ["import { defineComponent, provide, ref } from 'vue'\nimport dayjs from 'dayjs'\nimport customParseFormat from 'dayjs/plugin/customParseFormat.js'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { DEFAULT_FORMATS_TIME } from './constants'\nimport Picker from './common/picker.vue'\nimport TimePickPanel from './time-picker-com/panel-time-pick.vue'\nimport TimeRangePanel from './time-picker-com/panel-time-range.vue'\nimport { timePickerDefaultProps } from './common/props'\n\ndayjs.extend(customParseFormat)\n\nexport default defineComponent({\n  name: 'ElTimePicker',\n  install: null,\n  props: {\n    ...timePickerDefaultProps,\n    /**\n     * @description whether to pick a time range\n     */\n    isRange: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  emits: [UPDATE_MODEL_EVENT],\n  setup(props, ctx) {\n    const commonPicker = ref<InstanceType<typeof Picker>>()\n    const [type, Panel] = props.isRange\n      ? ['timerange', TimeRangePanel]\n      : ['time', TimePickPanel]\n\n    const modelUpdater = (value: any) => ctx.emit(UPDATE_MODEL_EVENT, value)\n    provide('ElPopperOptions', props.popperOptions)\n    ctx.expose({\n      /**\n       * @description focus the Input component\n       */\n      focus: () => {\n        commonPicker.value?.focus()\n      },\n      /**\n       * @description blur the Input component\n       */\n      blur: () => {\n        commonPicker.value?.blur()\n      },\n      /**\n       * @description open the TimePicker popper\n       */\n      handleOpen: () => {\n        commonPicker.value?.handleOpen()\n      },\n      /**\n       * @description close the TimePicker popper\n       */\n      handleClose: () => {\n        commonPicker.value?.handleClose()\n      },\n    })\n\n    return () => {\n      const format = props.format ?? DEFAULT_FORMATS_TIME\n\n      return (\n        <Picker\n          {...props}\n          ref={commonPicker}\n          type={type}\n          format={format}\n          onUpdate:modelValue={modelUpdater}\n        >\n          {{\n            default: (props: any) => <Panel {...props} />,\n          }}\n        </Picker>\n      )\n    }\n  },\n})\n"], "mappings": ";;;;;;;;;AAUAA,KAAK,CAACC,MAAN,CAAaC,iBAAb;AAEA,IAAAC,UAAA,GAAeC,eAAe,CAAC;EAC7BC,IAAI,EAAE,cADuB;EAE7BC,OAAO,EAAE,IAFoB;EAG7BC,KAAK,EAAE;;IAELC,OAAA;MACJC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACI;EACE;EACAC,KAAA,GAAAC,kBAAS;EAFFC,MAAAP,KAAA,EAAAQ,GAAA;IARkB,MAAAC,YAAA,GAAAC,GAAA;IAaxB,MAAG,CAAAR,IAAA,EAAAS,KAAA,IAAAX,KAbqB,CAAAC,OAAA,iBAAAW,cAAA,aAAAC,aAAA;;IAc7BC,OAAM,kBAAY,EAAAd,KAAA,CAAAe,aAAA;IAChBP,GAAM,CAAAQ,MAAA;MACNC,KAAO,EAADA,CAAA;;QAIA,CAAAC,EAAA,GAAAT,YAAgB,CAAAU,KAAe,KAAG,IAAC,GAAK,SAAAD,EAAA,CAAAD,KAAT,EAA6B;;MAClEG,IAAA,EAAQN,CAAA,KAAD;QACH,IAAAI,EAAJ;QACE,CAAAA,EAAA,GAAAT,YAAA,CAAAU,KAAA,qBAAAD,EAAA,CAAAE,IAAA;MACN;MACAC,UAAA,EAAAA,CAAA;QACM,IAAAH,EAAa;QACX,CAAYA,EAAA,GAAAT,YAAZ,CAAAU,KAAA,qBAAAD,EAAA,CAAAG,UAAA;OALO;;QAOT,IAAAH,EAAA;QACN,CAAAA,EAAA,GAAAT,YAAA,CAAAU,KAAA,qBAAAD,EAAA,CAAAI,WAAA;MACA;IACM;WACc;MACb,IAZQJ,EAAA;;MAaT,OAAAK,WAAA,CAAAC,YAAA,EAAAC,UAAA,CAAAzB,KAAA;QACN,OAAAS,YAAA;QACA,QAAAP,IAAA;QACM,QAAU,EAAEwB,MAAM;QAChB,uBAAAC;OAjBO;;MAmBT;IACN;EACA;AACM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}