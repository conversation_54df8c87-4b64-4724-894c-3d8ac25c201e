{"ast": null, "code": "import { defineComponent, ref, computed, reactive, onMounted, h } from 'vue';\nimport { pick } from 'lodash-unified';\nimport { ElSelect } from '../../select/index.mjs';\nimport { ElTree } from '../../tree/index.mjs';\nimport { useSelect } from './select.mjs';\nimport { useTree } from './tree.mjs';\nimport CacheOptions from './cache-options.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElTreeSelect\",\n  inheritAttrs: false,\n  props: {\n    ...ElSelect.props,\n    ...ElTree.props,\n    cacheData: {\n      type: Array,\n      default: () => []\n    }\n  },\n  setup(props, context) {\n    const {\n      slots,\n      expose\n    } = context;\n    const select = ref();\n    const tree = ref();\n    const key = computed(() => props.nodeKey || props.valueKey || \"value\");\n    const selectProps = useSelect(props, context, {\n      select,\n      tree,\n      key\n    });\n    const {\n      cacheOptions,\n      ...treeProps\n    } = useTree(props, context, {\n      select,\n      tree,\n      key\n    });\n    const methods = reactive({});\n    expose(methods);\n    onMounted(() => {\n      Object.assign(methods, {\n        ...pick(tree.value, [\"filter\", \"updateKeyChildren\", \"getCheckedNodes\", \"setCheckedNodes\", \"getCheckedKeys\", \"setCheckedKeys\", \"setChecked\", \"getHalfCheckedNodes\", \"getHalfCheckedKeys\", \"getCurrentKey\", \"getCurrentNode\", \"setCurrentKey\", \"setCurrentNode\", \"getNode\", \"remove\", \"append\", \"insertBefore\", \"insertAfter\"]),\n        ...pick(select.value, [\"focus\", \"blur\", \"selectedLabel\"])\n      });\n    });\n    return () => h(ElSelect, reactive({\n      ...selectProps,\n      ref: ref2 => select.value = ref2\n    }), {\n      ...slots,\n      default: () => [h(CacheOptions, {\n        data: cacheOptions.value\n      }), h(ElTree, reactive({\n        ...treeProps,\n        ref: ref2 => tree.value = ref2\n      }))]\n    });\n  }\n});\nvar TreeSelect = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tree-select.vue\"]]);\nexport { TreeSelect as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "name", "inheritAttrs", "props", "ElSelect", "ElTree", "cacheData", "type", "Array", "default", "setup", "context", "slots", "expose", "select", "ref", "tree", "key", "computed", "nodeKey", "valueKey", "selectProps", "useSelect", "cacheOptions", "treeProps", "useTree", "methods", "reactive", "onMounted", "Object", "assign", "pick", "value", "h", "ref2", "CacheOptions", "data", "TreeSelect", "_export_sfc"], "sources": ["../../../../../../packages/components/tree-select/src/tree-select.vue"], "sourcesContent": ["<script lang=\"ts\">\n// @ts-nocheck\nimport { computed, defineComponent, h, onMounted, reactive, ref } from 'vue'\nimport { pick } from 'lodash-unified'\nimport ElSelect from '@element-plus/components/select'\nimport ElTree from '@element-plus/components/tree'\nimport { useSelect } from './select'\nimport { useTree } from './tree'\nimport CacheOptions from './cache-options'\n\nexport default defineComponent({\n  name: 'ElTreeSelect',\n  // disable `ElSelect` inherit current attrs\n  inheritAttrs: false,\n  props: {\n    ...ElSelect.props,\n    ...ElTree.props,\n    /**\n     * @description The cached data of the lazy node, the structure is the same as the data, used to get the label of the unloaded data\n     */\n    cacheData: {\n      type: Array,\n      default: () => [],\n    },\n  },\n  setup(props, context) {\n    const { slots, expose } = context\n\n    const select = ref<InstanceType<typeof ElSelect>>()\n    const tree = ref<InstanceType<typeof ElTree>>()\n\n    const key = computed(() => props.nodeKey || props.valueKey || 'value')\n\n    const selectProps = useSelect(props, context, { select, tree, key })\n    const { cacheOptions, ...treeProps } = useTree(props, context, {\n      select,\n      tree,\n      key,\n    })\n\n    // expose ElTree/ElSelect methods\n    const methods = reactive({})\n    expose(methods)\n    onMounted(() => {\n      Object.assign(methods, {\n        ...pick(tree.value, [\n          'filter',\n          'updateKeyChildren',\n          'getCheckedNodes',\n          'setCheckedNodes',\n          'getCheckedKeys',\n          'setCheckedKeys',\n          'setChecked',\n          'getHalfCheckedNodes',\n          'getHalfCheckedKeys',\n          'getCurrentKey',\n          'getCurrentNode',\n          'setCurrentKey',\n          'setCurrentNode',\n          'getNode',\n          'remove',\n          'append',\n          'insertBefore',\n          'insertAfter',\n        ]),\n        ...pick(select.value, ['focus', 'blur', 'selectedLabel']),\n      })\n    })\n\n    return () =>\n      h(\n        ElSelect,\n        /**\n         * 1. The `props` is processed into `Refs`, but `v-bind` and\n         * render function props cannot read `Refs`, so use `reactive`\n         * unwrap the `Refs` and keep reactive.\n         * 2. The keyword `ref` requires `Ref`, but `reactive` broke it,\n         * so use function.\n         */\n        reactive({\n          ...selectProps,\n          ref: (ref) => (select.value = ref),\n        }),\n        {\n          ...slots,\n          default: () => [\n            h(CacheOptions, { data: cacheOptions.value }),\n            h(\n              ElTree,\n              reactive({\n                ...treeProps,\n                ref: (ref) => (tree.value = ref),\n              })\n            ),\n          ],\n        }\n      )\n  },\n})\n</script>\n"], "mappings": ";;;;;;;;AAUA,MAAKA,SAAA,GAAaC,eAAa;EAC7BC,IAAM;EAAAC,YAAA;EAENC,KAAc;IACP,GAAAC,QAAA,CAAAD,KAAA;IACL,GAAGE,MAAS,CAAAF,KAAA;IACZG,SAAU;MAAAC,IAAA,EAAAC,KAAA;MAAAC,OAAA,EAAAA,CAAA;IAAA;EAAA;EAICC,KACHA,CAAAP,KAAA,EAAAQ,OAAA;IACN;MAAAC,KAAA;MAAAC;IAAgB,IAAAF,OAAA;IAClB,MAAAG,MAAA,GAAAC,GAAA;IACF,MAAAC,IAAA,GAAAD,GAAA;IACA,MAAAE,GAAA,GAAsBC,QAAA,OAAAf,KAAA,CAAAgB,OAAA,IAAAhB,KAAA,CAAAiB,QAAA;IACd,MAAAC,WAAS,GAAAC,SAAW,CAAAnB,KAAA,EAAAQ,OAAA;MAAAG,MAAA;MAAAE,IAAA;MAAAC;IAAA;IAE1B,MAAM;MAAAM,YAA4C;MAAA,GAAAC;IAAA,IAAAC,OAAA,CAAAtB,KAAA,EAAAQ,OAAA;MAClDG,MAAA;MAEAE,IAAM;MAEAC;IACN;IACE,MAAAS,OAAA,GAAAC,QAAA;IACAd,MAAA,CAAAa,OAAA;IACAE,SAAA;MACDC,MAAA,CAAAC,MAAA,CAAAJ,OAAA;QAGK,GAAAK,IAAA,CAAAf,IAAU,CAASgB,KAAA,GACzB,QAAc,EACd,mBAAgB,EACd,iBAAuB,EACrB,iBAAoB,EAClB,kBACA,kBACA,cACA,uBACA,sBACA,iBACA,kBACA,iBACA,kBACA,WACA,UACA,UACA,gBACA,cACA;QACA,GAAAD,IAAA,CAAAjB,MAAA,CAAAkB,KAAA;MAAA,CACA;IAAA,CACA;IAAA,OACD,MAAAC,CAAA,CAAA7B,QAAA,EAAAuB,QAAA;MACD,GAAAN,WAAe;MACjBN,GAAC,EAAAmB,IAAA,IAAApB,MAAA,CAAAkB,KAAA,GAAAE;IAAA,CACF;MAED,GAAAtB,KACE;MACEH,OAAA,EAAAA,CAAA,MAAAwB,CAAA,CAAAE,YAAA;QAAAC,IAAA,EAAAb,YAAA,CAAAS;MAAA,IAAAC,CAAA,CAAA5B,MAAA,EAAAsB,QAAA;QAAA,GAAAH,SAAA;QAAAT,GAAA,EAAAmB,IAAA,IAAAlB,IAAA,CAAAgB,KAAA,GAAAE;MAAA;IAAA;EAAA;AAQS;AAEuB,IAAAG,UAAA,GAC/B,eAAAC,WAAA,CAAAvC,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}