{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, inject, ref, computed, unref, watch, onMounted, provide, openBlock, createElementBlock, normalizeStyle, normalizeClass, renderSlot, createVNode, withCtx, Fragment, createTextVNode, toDisplayString, createCommentVNode } from 'vue';\nimport { offset } from '@floating-ui/dom';\nimport ElVisuallyHidden from '../../visual-hidden/src/visual-hidden2.mjs';\nimport { tooltipV2RootKey, tooltipV2ContentKey } from './constants.mjs';\nimport { tooltipV2ContentProps } from './content2.mjs';\nimport { tooltipV2CommonProps } from './common.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useFloating, arrowMiddleware } from '../../../hooks/use-floating/index.mjs';\nimport { useZIndex } from '../../../hooks/use-z-index/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTooltipV2Content\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    ...tooltipV2ContentProps,\n    ...tooltipV2CommonProps\n  },\n  setup(__props) {\n    const props = __props;\n    const {\n      triggerRef,\n      contentId\n    } = inject(tooltipV2RootKey);\n    const placement = ref(props.placement);\n    const strategy = ref(props.strategy);\n    const arrowRef = ref(null);\n    const {\n      referenceRef,\n      contentRef,\n      middlewareData,\n      x,\n      y,\n      update\n    } = useFloating({\n      placement,\n      strategy,\n      middleware: computed(() => {\n        const middleware = [offset(props.offset)];\n        if (props.showArrow) {\n          middleware.push(arrowMiddleware({\n            arrowRef\n          }));\n        }\n        return middleware;\n      })\n    });\n    const zIndex = useZIndex().nextZIndex();\n    const ns = useNamespace(\"tooltip-v2\");\n    const side = computed(() => {\n      return placement.value.split(\"-\")[0];\n    });\n    const contentStyle = computed(() => {\n      return {\n        position: unref(strategy),\n        top: `${unref(y) || 0}px`,\n        left: `${unref(x) || 0}px`,\n        zIndex\n      };\n    });\n    const arrowStyle = computed(() => {\n      if (!props.showArrow) return {};\n      const {\n        arrow\n      } = unref(middlewareData);\n      return {\n        [`--${ns.namespace.value}-tooltip-v2-arrow-x`]: `${arrow == null ? void 0 : arrow.x}px` || \"\",\n        [`--${ns.namespace.value}-tooltip-v2-arrow-y`]: `${arrow == null ? void 0 : arrow.y}px` || \"\"\n      };\n    });\n    const contentClass = computed(() => [ns.e(\"content\"), ns.is(\"dark\", props.effect === \"dark\"), ns.is(unref(strategy)), props.contentClass]);\n    watch(arrowRef, () => update());\n    watch(() => props.placement, val => placement.value = val);\n    onMounted(() => {\n      watch(() => props.reference || triggerRef.value, el => {\n        referenceRef.value = el || void 0;\n      }, {\n        immediate: true\n      });\n    });\n    provide(tooltipV2ContentKey, {\n      arrowRef\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"contentRef\",\n        ref: contentRef,\n        style: normalizeStyle(unref(contentStyle)),\n        \"data-tooltip-v2-root\": \"\"\n      }, [!_ctx.nowrap ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        \"data-side\": unref(side),\n        class: normalizeClass(unref(contentClass))\n      }, [renderSlot(_ctx.$slots, \"default\", {\n        contentStyle: unref(contentStyle),\n        contentClass: unref(contentClass)\n      }), createVNode(unref(ElVisuallyHidden), {\n        id: unref(contentId),\n        role: \"tooltip\"\n      }, {\n        default: withCtx(() => [_ctx.ariaLabel ? (openBlock(), createElementBlock(Fragment, {\n          key: 0\n        }, [createTextVNode(toDisplayString(_ctx.ariaLabel), 1)], 64)) : renderSlot(_ctx.$slots, \"default\", {\n          key: 1\n        })]),\n        _: 3\n      }, 8, [\"id\"]), renderSlot(_ctx.$slots, \"arrow\", {\n        style: normalizeStyle(unref(arrowStyle)),\n        side: unref(side)\n      })], 10, [\"data-side\"])) : createCommentVNode(\"v-if\", true)], 4);\n    };\n  }\n});\nvar TooltipV2Content = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"content.vue\"]]);\nexport { TooltipV2Content as default };", "map": {"version": 3, "names": ["name", "triggerRef", "contentId", "inject", "tooltipV2RootKey", "placement", "ref", "props", "strategy", "arrowRef", "referenceRef", "contentRef", "middlewareData", "x", "y", "update", "useFloating", "middleware", "computed", "offset", "showArrow", "push", "arrowMiddleware", "zIndex", "useZIndex", "nextZIndex", "ns", "useNamespace", "side", "value", "split", "contentStyle", "position", "unref", "top", "left", "arrowStyle", "arrow", "namespace", "contentClass", "e", "is", "effect", "watch", "val", "onMounted", "reference", "el", "immediate", "provide", "tooltipV2ContentKey", "_ctx", "_cache", "openBlock", "createElementBlock", "ref_key", "style", "normalizeStyle", "nowrap"], "sources": ["../../../../../../packages/components/tooltip-v2/src/content.vue"], "sourcesContent": ["<template>\n  <div ref=\"contentRef\" :style=\"contentStyle\" data-tooltip-v2-root>\n    <div v-if=\"!nowrap\" :data-side=\"side\" :class=\"contentClass\">\n      <slot :content-style=\"contentStyle\" :content-class=\"contentClass\" />\n      <el-visually-hidden :id=\"contentId\" role=\"tooltip\">\n        <template v-if=\"ariaLabel\">\n          {{ ariaLabel }}\n        </template>\n        <slot v-else />\n      </el-visually-hidden>\n      <slot name=\"arrow\" :style=\"arrowStyle\" :side=\"side\" />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject, onMounted, provide, ref, unref, watch } from 'vue'\nimport { offset } from '@floating-ui/dom'\nimport {\n  arrowMiddleware,\n  useFloating,\n  useNamespace,\n  useZIndex,\n} from '@element-plus/hooks'\nimport ElVisuallyHidden from '@element-plus/components/visual-hidden'\nimport { tooltipV2ContentKey, tooltipV2RootKey } from './constants'\nimport { tooltipV2ContentProps } from './content'\nimport { tooltipV2CommonProps } from './common'\n\nimport type { CSSProperties } from 'vue'\nimport type { Middleware } from '@floating-ui/dom'\n\ndefineOptions({\n  name: 'ElTooltipV2Content',\n})\n\nconst props = defineProps({ ...tooltipV2ContentProps, ...tooltipV2CommonProps })\n\nconst { triggerRef, contentId } = inject(tooltipV2RootKey)!\n\nconst placement = ref(props.placement)\nconst strategy = ref(props.strategy)\nconst arrowRef = ref<HTMLElement | null>(null)\n\nconst { referenceRef, contentRef, middlewareData, x, y, update } = useFloating({\n  placement,\n  strategy,\n  middleware: computed(() => {\n    const middleware: Middleware[] = [offset(props.offset)]\n\n    if (props.showArrow) {\n      middleware.push(\n        arrowMiddleware({\n          arrowRef,\n        })\n      )\n    }\n\n    return middleware\n  }),\n})\n\nconst zIndex = useZIndex().nextZIndex()\n\nconst ns = useNamespace('tooltip-v2')\n\nconst side = computed(() => {\n  return placement.value.split('-')[0]\n})\n\nconst contentStyle = computed<CSSProperties>(() => {\n  return {\n    position: unref(strategy),\n    top: `${unref(y) || 0}px`,\n    left: `${unref(x) || 0}px`,\n    zIndex,\n  }\n})\n\nconst arrowStyle = computed<CSSProperties>(() => {\n  if (!props.showArrow) return {}\n\n  const { arrow } = unref(middlewareData)\n\n  return {\n    [`--${ns.namespace.value}-tooltip-v2-arrow-x`]: `${arrow?.x}px` || '',\n    [`--${ns.namespace.value}-tooltip-v2-arrow-y`]: `${arrow?.y}px` || '',\n  }\n})\n\nconst contentClass = computed(() => [\n  ns.e('content'),\n  ns.is('dark', props.effect === 'dark'),\n  ns.is(unref(strategy)),\n  props.contentClass,\n])\n\nwatch(arrowRef, () => update())\n\nwatch(\n  () => props.placement,\n  (val) => (placement.value = val)\n)\n\nonMounted(() => {\n  watch(\n    () => props.reference || triggerRef.value,\n    (el) => {\n      referenceRef.value = el || undefined\n    },\n    {\n      immediate: true,\n    }\n  )\n})\n\nprovide(tooltipV2ContentKey, { arrowRef })\n</script>\n"], "mappings": ";;;;;;;;;;;mCAgCc;EACZA,IAAM;AACR;;;;;;;;;IAIA,MAAM;MAAEC,UAAA;MAAYC;IAAU,IAAIC,MAAA,CAAOC,gBAAgB;IAEnD,MAAAC,SAAA,GAAYC,GAAI,CAAAC,KAAA,CAAMF,SAAS;IAC/B,MAAAG,QAAA,GAAWF,GAAI,CAAAC,KAAA,CAAMC,QAAQ;IAC7B,MAAAC,QAAA,GAAWH,GAAA,CAAwB,IAAI;IAEvC;MAAEI,YAAA;MAAcC,UAAY;MAAAC,cAAA;MAAgBC,CAAA;MAAGC,CAAG;MAAAC;IAAA,IAAWC,WAAY;MAC7EX,SAAA;MACAG,QAAA;MACAS,UAAA,EAAYC,QAAA,CAAS,MAAM;QACzB,MAAMD,UAA2B,IAACE,MAAO,CAAAZ,KAAA,CAAMY,MAAM,CAAC;QAEtD,IAAIZ,KAAA,CAAMa,SAAW;UACRH,UAAA,CAAAI,IAAA,CAAAC,eAAA;YACTb;UAAgB,CACd;QAAA;QAEJ,OAAAQ,UAAA;MAAA,CACF;IAEA,CAAO;IAAA,MACRM,MAAA,GAAAC,SAAA,GAAAC,UAAA;IACH,MAACC,EAAA,GAAAC,YAAA;IAEK,MAAAC,IAAA,GAAAV,QAAmB,OAAa;MAEhC,OAAAb,SAAA,CAAAwB,KAAkB,CAAYC,KAAA;IAEpC,CAAM;IACJ,MAAAC,YAAiB,GAAAb,QAAY,OAAK;MACnC;QAEKc,QAAA,EAAAC,KAAA,CAAAzB,QAAA;QACG0B,GAAA,KAAAD,KAAA,CAAAnB,CAAA;QACLqB,IAAA,KAAAF,KAAA,CAAApB,CAAgB,CAAQ;QACxBU;MAAqB;IACC,CACtB;IACF,MAAAa,UAAA,GAAAlB,QAAA;MACD,KAAAX,KAAA,CAAAa,SAAA,EAEK;MACJ,MAAK;QAAAiB;MAAiB,IAAAJ,KAAA,CAAArB,cAAQ;MAE9B,OAAQ;QAED,MAAAc,EAAA,CAAAY,SAAA,CAAAT,KAAA,2BAAAQ,KAAA,oBAAAA,KAAA,CAAAxB,CAAA;QACL,CAAC,KAAKa,EAAA,CAAGY,SAAU,CAAAT,KAAK,qBAAqB,GAAG,GAAGQ,KAAO,IAAS,gBAAAA,KAAA,CAAAvB,CAAA;MAAA,CACnE;IAAmE,CACrE;IACF,MAACyB,YAAA,GAAArB,QAAA,QAEKQ,EAAA,CAAAc,CAAA,aACJd,EAAA,CAAGe,EAAE,CAAS,QAAAlC,KAAA,CAAAmC,MAAA,cACdhB,EAAG,CAAAe,EAAA,CAAGR,KAAQ,CAAAzB,QAAM,IACpBD,KAAG,CAAGgC,YAAM,CAAS,CACrB;IACFI,KAAC,CAAAlC,QAAA,QAAAM,MAAA;IAEK4B,KAAA,OAAApC,KAAgB,CAAAF,SAAA,EAAQuC,GAAA,IAAAvC,SAAA,CAAAwB,KAAA,GAAAe,GAAA;IAE9BC,SAAA;MACEF,KAAA,CAAM,MAAMpC,KAAA,CAAAuC,SAAA,IAAA7C,UAAA,CAAA4B,KAAA,EAAAkB,EAAA;QACFrC,YAAA,CAAAmB,KAAkB,GAAAkB,EAAA;MAAA,CAC9B;QAEAC,SAAgB;MACd;IAAA,CACE;IAAoCC,OAC5B,CAAAC,mBAAA;MAAAzC;IAAA;IACN,QAAA0C,IAAA,EAAAC,MAAA;MACF,OAAAC,SAAA,IAAAC,kBAAA;QACAC,OAAA;QAAAjD,GACa,EAAAK,UAAA;QACb6C,KAAA,EAAAC,cAAA,CAAAxB,KAAA,CAAAF,YAAA;QACF;MAAA,CACD,GAEO,CAAAoB,IAAA,CAAAO,MAAA,IAAAL,SAAuB,IAAAC,kBAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}