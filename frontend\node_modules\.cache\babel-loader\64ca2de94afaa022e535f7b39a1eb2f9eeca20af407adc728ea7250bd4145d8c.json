{"ast": null, "code": "import { defineComponent, toRef, unref, openBlock, createElement<PERSON>lock, mergeProps, Fragment, renderList, renderSlot, createVNode, normalizeClass, createBlock, createCommentVNode, normalizeProps } from 'vue';\nimport { skeletonProps } from './skeleton.mjs';\nimport SkeletonItem from './skeleton-item2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useThrottleRender } from '../../../hooks/use-throttle-render/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElSkeleton\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: skeletonProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"skeleton\");\n    const uiLoading = useThrottleRender(toRef(props, \"loading\"), props.throttle);\n    expose({\n      uiLoading\n    });\n    return (_ctx, _cache) => {\n      return unref(uiLoading) ? (openBlock(), createElementBlock(\"div\", mergeProps({\n        key: 0,\n        class: [unref(ns).b(), unref(ns).is(\"animated\", _ctx.animated)]\n      }, _ctx.$attrs), [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.count, i => {\n        return openBlock(), createElementBlock(Fragment, {\n          key: i\n        }, [unref(uiLoading) ? renderSlot(_ctx.$slots, \"template\", {\n          key: i\n        }, () => [createVNode(SkeletonItem, {\n          class: normalizeClass(unref(ns).is(\"first\")),\n          variant: \"p\"\n        }, null, 8, [\"class\"]), (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.rows, item => {\n          return openBlock(), createBlock(SkeletonItem, {\n            key: item,\n            class: normalizeClass([unref(ns).e(\"paragraph\"), unref(ns).is(\"last\", item === _ctx.rows && _ctx.rows > 1)]),\n            variant: \"p\"\n          }, null, 8, [\"class\"]);\n        }), 128))]) : createCommentVNode(\"v-if\", true)], 64);\n      }), 128))], 16)) : renderSlot(_ctx.$slots, \"default\", normalizeProps(mergeProps({\n        key: 1\n      }, _ctx.$attrs)));\n    };\n  }\n});\nvar Skeleton = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"skeleton.vue\"]]);\nexport { Skeleton as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace", "uiLoading", "useThrottleRender", "toRef", "props", "throttle", "expose", "_ctx", "_cache"], "sources": ["../../../../../../packages/components/skeleton/src/skeleton.vue"], "sourcesContent": ["<template>\n  <template v-if=\"uiLoading\">\n    <div :class=\"[ns.b(), ns.is('animated', animated)]\" v-bind=\"$attrs\">\n      <template v-for=\"i in count\" :key=\"i\">\n        <slot v-if=\"uiLoading\" :key=\"i\" name=\"template\">\n          <el-skeleton-item :class=\"ns.is('first')\" variant=\"p\" />\n          <el-skeleton-item\n            v-for=\"item in rows\"\n            :key=\"item\"\n            :class=\"[\n              ns.e('paragraph'),\n              ns.is('last', item === rows && rows > 1),\n            ]\"\n            variant=\"p\"\n          />\n        </slot>\n      </template>\n    </div>\n  </template>\n  <template v-else>\n    <slot v-bind=\"$attrs\" />\n  </template>\n</template>\n\n<script lang=\"ts\" setup>\nimport { toRef } from 'vue'\nimport { useNamespace, useThrottleRender } from '@element-plus/hooks'\nimport { skeletonProps } from './skeleton'\nimport ElSkeletonItem from './skeleton-item.vue'\n\ndefineOptions({\n  name: 'ElSkeleton',\n})\nconst props = defineProps(skeletonProps)\n\nconst ns = useNamespace('skeleton')\nconst uiLoading = useThrottleRender(toRef(props, 'loading'), props.throttle)\n\ndefineExpose({\n  /** @description loading state */\n  uiLoading,\n})\n</script>\n"], "mappings": ";;;;;;mCA8Bc;EACZA,IAAM;AACR;;;;;;;;IAGM,MAAAC,EAAA,GAAKC,YAAA,CAAa,UAAU;IAClC,MAAMC,SAAA,GAAYC,iBAAkB,CAAAC,KAAA,CAAMC,KAAA,EAAO,SAAS,GAAGA,KAAA,CAAMC,QAAQ;IAE9DC,MAAA;MAAAL;IAAA,CAEX;IACF,OAAC,CAAAM,IAAA,EAAAC,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}