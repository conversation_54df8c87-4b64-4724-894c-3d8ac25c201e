{"ast": null, "code": "import CascaderPanel from './src/index.mjs';\nexport { CASCADER_PANEL_INJECTION_KEY } from './src/types.mjs';\nexport { CommonProps, DefaultProps, useCascaderConfig } from './src/config.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElCascaderPanel = withInstall(CascaderPanel);\nexport { ElCascaderPanel, ElCascaderPanel as default };", "map": {"version": 3, "names": ["ElCascaderPanel", "withInstall", "CascaderPanel"], "sources": ["../../../../../packages/components/cascader-panel/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport CascaderPanel from './src/index.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCascaderPanel: SFCWithInstall<typeof CascaderPanel> =\n  withInstall(CascaderPanel)\n\nexport default ElCascaderPanel\nexport * from './src/types'\nexport * from './src/config'\nexport * from './src/instance'\n"], "mappings": ";;;;AAEY,MAACA,eAAe,GAAGC,WAAW,CAACC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}