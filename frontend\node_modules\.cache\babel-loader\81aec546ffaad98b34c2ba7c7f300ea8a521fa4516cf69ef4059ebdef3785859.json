{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { shallowRef, isVNode } from 'vue';\nimport { flattedChildren } from '../../utils/vue/vnode.mjs';\nconst getOrderedChildren = (vm, childComponentName, children) => {\n  const nodes = flattedChildren(vm.subTree).filter(n => {\n    var _a;\n    return isVNode(n) && ((_a = n.type) == null ? void 0 : _a.name) === childComponentName && !!n.component;\n  });\n  const uids = nodes.map(n => n.component.uid);\n  return uids.map(uid => children[uid]).filter(p => !!p);\n};\nconst useOrderedChildren = (vm, childComponentName) => {\n  const children = {};\n  const orderedChildren = shallowRef([]);\n  const addChild = child => {\n    children[child.uid] = child;\n    orderedChildren.value = getOrderedChildren(vm, childComponentName, children);\n  };\n  const removeChild = uid => {\n    delete children[uid];\n    orderedChildren.value = orderedChildren.value.filter(children2 => children2.uid !== uid);\n  };\n  return {\n    children: orderedChildren,\n    addChild,\n    removeChild\n  };\n};\nexport { useOrderedChildren };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vm", "childComponentName", "children", "nodes", "flatted<PERSON><PERSON><PERSON><PERSON>", "subTree", "filter", "n", "_a", "isVNode", "type", "name", "component", "uids", "map", "uid", "p", "useOrderedChildren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shallowRef", "<PERSON><PERSON><PERSON><PERSON>", "child", "value", "<PERSON><PERSON><PERSON><PERSON>", "children2"], "sources": ["../../../../../packages/hooks/use-ordered-children/index.ts"], "sourcesContent": ["import { isVNode, shallowRef } from 'vue'\nimport { flattedChildren } from '@element-plus/utils'\n\nimport type { ComponentInternalInstance, VNode } from 'vue'\n\nconst getOrderedChildren = <T>(\n  vm: ComponentInternalInstance,\n  childComponentName: string,\n  children: Record<number, T>\n): T[] => {\n  const nodes = flattedChildren(vm.subTree).filter(\n    (n): n is VNode =>\n      isVNode(n) &&\n      (n.type as any)?.name === childComponentName &&\n      !!n.component\n  )\n  const uids = nodes.map((n) => n.component!.uid)\n  return uids.map((uid) => children[uid]).filter((p) => !!p)\n}\n\nexport const useOrderedChildren = <T extends { uid: number }>(\n  vm: ComponentInternalInstance,\n  childComponentName: string\n) => {\n  const children: Record<number, T> = {}\n  const orderedChildren = shallowRef<T[]>([])\n\n  // TODO: split into two functions: addChild and sortChildren\n  const addChild = (child: T) => {\n    children[child.uid] = child\n    orderedChildren.value = getOrderedChildren(vm, childComponentName, children)\n  }\n  const removeChild = (uid: number) => {\n    delete children[uid]\n    orderedChildren.value = orderedChildren.value.filter(\n      (children) => children.uid !== uid\n    )\n  }\n\n  return {\n    children: orderedChildren,\n    addChild,\n    removeChild,\n  }\n}\n"], "mappings": ";;;;;AAEA,MAAMA,kBAAkB,GAAGA,CAACC,EAAE,EAAEC,kBAAkB,EAAEC,QAAQ,KAAK;EAC/D,MAAMC,KAAK,GAAGC,eAAe,CAACJ,EAAE,CAACK,OAAO,CAAC,CAACC,MAAM,CAAEC,CAAC,IAAK;IACtD,IAAIC,EAAE;IACN,OAAOC,OAAO,CAACF,CAAC,CAAC,IAAI,CAAC,CAACC,EAAE,GAAGD,CAAC,CAACG,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,IAAI,MAAMV,kBAAkB,IAAI,CAAC,CAACM,CAAC,CAACK,SAAS;EAC3G,CAAG,CAAC;EACF,MAAMC,IAAI,GAAGV,KAAK,CAACW,GAAG,CAAEP,CAAC,IAAKA,CAAC,CAACK,SAAS,CAACG,GAAG,CAAC;EAC9C,OAAOF,IAAI,CAACC,GAAG,CAAEC,GAAG,IAAKb,QAAQ,CAACa,GAAG,CAAC,CAAC,CAACT,MAAM,CAAEU,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;AAC5D,CAAC;AACW,MAACC,kBAAkB,GAAGA,CAACjB,EAAE,EAAEC,kBAAkB,KAAK;EAC5D,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMgB,eAAe,GAAGC,UAAU,CAAC,EAAE,CAAC;EACtC,MAAMC,QAAQ,GAAIC,KAAK,IAAK;IAC1BnB,QAAQ,CAACmB,KAAK,CAACN,GAAG,CAAC,GAAGM,KAAK;IAC3BH,eAAe,CAACI,KAAK,GAAGvB,kBAAkB,CAACC,EAAE,EAAEC,kBAAkB,EAAEC,QAAQ,CAAC;EAChF,CAAG;EACD,MAAMqB,WAAW,GAAIR,GAAG,IAAK;IAC3B,OAAOb,QAAQ,CAACa,GAAG,CAAC;IACpBG,eAAe,CAACI,KAAK,GAAGJ,eAAe,CAACI,KAAK,CAAChB,MAAM,CAAEkB,SAAS,IAAKA,SAAS,CAACT,GAAG,KAAKA,GAAG,CAAC;EAC9F,CAAG;EACD,OAAO;IACLb,QAAQ,EAAEgB,eAAe;IACzBE,QAAQ;IACRG;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}