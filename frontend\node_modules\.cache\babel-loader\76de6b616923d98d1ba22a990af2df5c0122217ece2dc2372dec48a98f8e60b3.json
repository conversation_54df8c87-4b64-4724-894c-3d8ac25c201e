{"ast": null, "code": "import { defineComponent, resolveComponent, openBlock, createBlock, withCtx, createVNode, normalizeProps, guardReactiveProps, renderSlot } from 'vue';\nimport ElRovingFocusGroupImpl from './roving-focus-group-impl.mjs';\nimport { ElCollection } from './roving-focus-group2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElRovingFocusGroup\",\n  components: {\n    ElFocusGroupCollection: ElCollection,\n    ElRovingFocusGroupImpl\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_roving_focus_group_impl = resolveComponent(\"el-roving-focus-group-impl\");\n  const _component_el_focus_group_collection = resolveComponent(\"el-focus-group-collection\");\n  return openBlock(), createBlock(_component_el_focus_group_collection, null, {\n    default: withCtx(() => [createVNode(_component_el_roving_focus_group_impl, normalizeProps(guardReactiveProps(_ctx.$attrs)), {\n      default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n      _: 3\n    }, 16)]),\n    _: 3\n  });\n}\nvar ElRovingFocusGroup = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"roving-focus-group.vue\"]]);\nexport { ElRovingFocusGroup as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "name", "components", "ElFocusGroupCollection", "ElCollection", "ElRovingFocusGroupImpl", "createBlock", "_component_el_focus_group_collection", "default", "withCtx", "createVNode", "_component_el_roving_focus_group_impl", "normalizeProps", "guardReactiveProps", "_ctx", "$attrs", "renderSlot", "$slots", "_"], "sources": ["../../../../../../packages/components/roving-focus-group/src/roving-focus-group.vue"], "sourcesContent": ["<template>\n  <el-focus-group-collection>\n    <el-roving-focus-group-impl v-bind=\"$attrs\">\n      <slot />\n    </el-roving-focus-group-impl>\n  </el-focus-group-collection>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent } from 'vue'\nimport ElRovingFocusGroupImpl from './roving-focus-group-impl.vue'\nimport { ElCollection as ElFocusGroupCollection } from './roving-focus-group'\n\nexport default defineComponent({\n  name: 'ElRovingFocusGroup',\n  components: {\n    ElFocusGroupCollection,\n    ElRovingFocusGroupImpl,\n  },\n})\n</script>\n"], "mappings": ";;;;AAaA,MAAKA,SAAA,GAAaC,eAAa;EAC7BC,IAAM;EACNC,UAAY;IAAAC,sBAAA,EACVC,YAAA;IACAC;EAAA;AAEJ,CAAC;;;;sBAlBCC,WAI4B,CAAAC,oCAAA;IAAAC,OAAA,EAAAC,OAAA,CAH1B,MAE6B,CAF7BC,WAAA,CAAAC,qCAAA,EAAAC,cAAA,CAAAC,kBAAA,CAAAC,IAAA,CAAAC,MAAA;MAE6BP,OAAA,EAAAC,OAAA,QAAAO,UAAA,CAAAF,IAAA,CAAAG,MAAA,WAFO,CAAM;MAAAC,CAAA;IAChC,MAAR,CAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}