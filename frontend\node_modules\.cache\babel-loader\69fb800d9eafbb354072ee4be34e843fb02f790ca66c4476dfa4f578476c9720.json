{"ast": null, "code": "import { isArray, isObject, isString } from '@vue/shared';\nexport { isArray, isDate, isFunction, isObject, isPlainObject, isPromise, isString, isSymbol } from '@vue/shared';\nimport { isNil } from 'lodash-unified';\nconst isUndefined = val => val === void 0;\nconst isBoolean = val => typeof val === \"boolean\";\nconst isNumber = val => typeof val === \"number\";\nconst isEmpty = val => !val && val !== 0 || isArray(val) && val.length === 0 || isObject(val) && !Object.keys(val).length;\nconst isElement = e => {\n  if (typeof Element === \"undefined\") return false;\n  return e instanceof Element;\n};\nconst isPropAbsent = prop => isNil(prop);\nconst isStringNumber = val => {\n  if (!isString(val)) {\n    return false;\n  }\n  return !Number.isNaN(Number(val));\n};\nconst isWindow = val => val === window;\nexport { isBoolean, isElement, isEmpty, isNumber, isPropAbsent, isStringNumber, isUndefined, isWindow };", "map": {"version": 3, "names": ["isUndefined", "val", "isBoolean", "isNumber", "isEmpty", "isArray", "length", "isObject", "Object", "keys", "isElement", "e", "Element", "isPropAbsent", "prop", "isNil", "isStringNumber", "isString", "Number", "isNaN", "isWindow", "window"], "sources": ["../../../../packages/utils/types.ts"], "sourcesContent": ["import { isArray, isObject, isString } from '@vue/shared'\nimport { isNil } from 'lodash-unified'\n\nexport {\n  isArray,\n  isFunction,\n  isObject,\n  isString,\n  isDate,\n  isPromise,\n  isSymbol,\n  isPlainObject,\n} from '@vue/shared'\n\nexport const isUndefined = (val: any): val is undefined => val === undefined\nexport const isBoolean = (val: any): val is boolean => typeof val === 'boolean'\nexport const isNumber = (val: any): val is number => typeof val === 'number'\n\nexport const isEmpty = (val: unknown) =>\n  (!val && val !== 0) ||\n  (isArray(val) && val.length === 0) ||\n  (isObject(val) && !Object.keys(val).length)\n\nexport const isElement = (e: unknown): e is Element => {\n  if (typeof Element === 'undefined') return false\n  return e instanceof Element\n}\n\nexport const isPropAbsent = (prop: unknown): prop is null | undefined =>\n  isNil(prop)\n\nexport const isStringNumber = (val: string): boolean => {\n  if (!isString(val)) {\n    return false\n  }\n  return !Number.isNaN(Number(val))\n}\n\nexport const isWindow = (val: unknown): val is Window => val === window\n"], "mappings": ";;;AAYY,MAACA,WAAW,GAAIC,GAAG,IAAKA,GAAG,KAAK,KAAK;AACrC,MAACC,SAAS,GAAID,GAAG,IAAK,OAAOA,GAAG,KAAK;AACrC,MAACE,QAAQ,GAAIF,GAAG,IAAK,OAAOA,GAAG,KAAK;AACpC,MAACG,OAAO,GAAIH,GAAG,IAAK,CAACA,GAAG,IAAIA,GAAG,KAAK,CAAC,IAAII,OAAO,CAACJ,GAAG,CAAC,IAAIA,GAAG,CAACK,MAAM,KAAK,CAAC,IAAIC,QAAQ,CAACN,GAAG,CAAC,IAAI,CAACO,MAAM,CAACC,IAAI,CAACR,GAAG,CAAC,CAACK,MAAA;AAChH,MAACI,SAAS,GAAIC,CAAC,IAAK;EAC9B,IAAI,OAAOC,OAAO,KAAK,WAAW,EAChC,OAAO,KAAK;EACd,OAAOD,CAAC,YAAYC,OAAO;AAC7B;AACY,MAACC,YAAY,GAAIC,IAAI,IAAKC,KAAK,CAACD,IAAI;AACpC,MAACE,cAAc,GAAIf,GAAG,IAAK;EACrC,IAAI,CAACgB,QAAQ,CAAChB,GAAG,CAAC,EAAE;IAClB,OAAO,KAAK;EAChB;EACE,OAAO,CAACiB,MAAM,CAACC,KAAK,CAACD,MAAM,CAACjB,GAAG,CAAC,CAAC;AACnC;AACY,MAACmB,QAAQ,GAAInB,GAAG,IAAKA,GAAG,KAAKoB,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}