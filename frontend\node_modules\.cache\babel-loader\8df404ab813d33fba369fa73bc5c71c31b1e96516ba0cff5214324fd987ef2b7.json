{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, Fragment as _Fragment, toDisplayString as _toDisplayString, withModifiers as _withModifiers } from \"vue\";\nconst _hoisted_1 = {\n  class: \"register-container\"\n};\nconst _hoisted_2 = {\n  class: \"register-card\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"step-content\"\n};\nconst _hoisted_4 = {\n  class: \"step-content\"\n};\nconst _hoisted_5 = {\n  class: \"key-generation\"\n};\nconst _hoisted_6 = {\n  class: \"key-box\"\n};\nconst _hoisted_7 = {\n  class: \"key-label\"\n};\nconst _hoisted_8 = {\n  class: \"key-actions-buttons\"\n};\nconst _hoisted_9 = {\n  class: \"key-box\"\n};\nconst _hoisted_10 = {\n  class: \"key-label\"\n};\nconst _hoisted_11 = {\n  class: \"key-actions-buttons\"\n};\nconst _hoisted_12 = {\n  class: \"key-actions\"\n};\nconst _hoisted_13 = {\n  class: \"step-content\"\n};\nconst _hoisted_14 = {\n  class: \"register-confirm\"\n};\nconst _hoisted_15 = {\n  class: \"confirm-actions\"\n};\nconst _hoisted_16 = {\n  class: \"button-group\"\n};\nconst _hoisted_17 = {\n  class: \"step-content\"\n};\nconst _hoisted_18 = {\n  class: \"register-success\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_step = _resolveComponent(\"el-step\");\n  const _component_el_steps = _resolveComponent(\"el-steps\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_DocumentCopy = _resolveComponent(\"DocumentCopy\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_Download = _resolveComponent(\"Download\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_el_result = _resolveComponent(\"el-result\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[32] || (_cache[32] = _createElementVNode(\"div\", {\n    class: \"register-header\"\n  }, [_createElementVNode(\"h2\", null, \"基于国密算法的图书馆自习室座位管理系统\"), _createElementVNode(\"h3\", null, \"用户注册\")], -1 /* HOISTED */)), _createVNode(_component_el_steps, {\n    active: $setup.currentStep,\n    \"finish-status\": \"success\",\n    simple: \"\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_step, {\n      title: \"填写信息\"\n    }), _createVNode(_component_el_step, {\n      title: \"生成密钥\"\n    }), _createVNode(_component_el_step, {\n      title: \"完成注册\"\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"active\"]), _createCommentVNode(\" 步骤1：填写基本信息 \"), $setup.currentStep === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    \"label-position\": \"top\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"学号\",\n      prop: \"studentId\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.studentId,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.form.studentId = $event),\n        placeholder: \"请输入学号\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"密码\",\n      prop: \"password\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.password,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.form.password = $event),\n        type: \"password\",\n        placeholder: \"请输入密码\",\n        \"show-password\": \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"确认密码\",\n      prop: \"confirmPassword\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.confirmPassword,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.form.confirmPassword = $event),\n        type: \"password\",\n        placeholder: \"请再次输入密码\",\n        \"show-password\": \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"邮箱\",\n      prop: \"email\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.email,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.form.email = $event),\n        placeholder: \"请输入邮箱\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"手机号\",\n      prop: \"phone\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.phone,\n        \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.form.phone = $event),\n        placeholder: \"请输入手机号\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.nextStep\n      }, {\n        default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"下一步\")])),\n        _: 1 /* STABLE */,\n        __: [12]\n      }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n        onClick: _cache[5] || (_cache[5] = $event => _ctx.$router.push('/login'))\n      }, {\n        default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"返回登录\")])),\n        _: 1 /* STABLE */,\n        __: [13]\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])])) : $setup.currentStep === 1 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 步骤2：生成SM2密钥对 \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_cache[23] || (_cache[23] = _createElementVNode(\"h3\", null, \"SM2密钥对生成\", -1 /* HOISTED */)), _cache[24] || (_cache[24] = _createElementVNode(\"p\", {\n    class: \"description\"\n  }, \" SM2密钥对用于安全登录和数据签名，请妥善保管您的私钥，公钥将上传至服务器。 \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_cache[16] || (_cache[16] = _createElementVNode(\"span\", null, \"公钥\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    size: \"small\",\n    onClick: $setup.copyPublicKey\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_DocumentCopy)]),\n      _: 1 /* STABLE */\n    }), _cache[14] || (_cache[14] = _createTextVNode(\" 复制 \"))]),\n    _: 1 /* STABLE */,\n    __: [14]\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    type: \"success\",\n    size: \"small\",\n    onClick: $setup.downloadPublicKey\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Download)]),\n      _: 1 /* STABLE */\n    }), _cache[15] || (_cache[15] = _createTextVNode(\" 下载 \"))]),\n    _: 1 /* STABLE */,\n    __: [15]\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createVNode(_component_el_input, {\n    modelValue: $setup.keyPair.publicKey,\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.keyPair.publicKey = $event),\n    type: \"textarea\",\n    rows: 3,\n    readonly: \"\"\n  }, null, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_cache[19] || (_cache[19] = _createElementVNode(\"span\", null, \"私钥\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    size: \"small\",\n    onClick: $setup.copyPrivateKey\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_DocumentCopy)]),\n      _: 1 /* STABLE */\n    }), _cache[17] || (_cache[17] = _createTextVNode(\" 复制 \"))]),\n    _: 1 /* STABLE */,\n    __: [17]\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    type: \"success\",\n    size: \"small\",\n    onClick: $setup.downloadPrivateKey\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Download)]),\n      _: 1 /* STABLE */\n    }), _cache[18] || (_cache[18] = _createTextVNode(\" 下载 \"))]),\n    _: 1 /* STABLE */,\n    __: [18]\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createVNode(_component_el_input, {\n    modelValue: $setup.keyPair.privateKey,\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.keyPair.privateKey = $event),\n    type: \"textarea\",\n    rows: 3,\n    readonly: \"\",\n    \"show-password\": \"\"\n  }, null, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.regenerateKeyPair\n  }, {\n    default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\"重新生成\")])),\n    _: 1 /* STABLE */,\n    __: [20]\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    type: \"success\",\n    onClick: $setup.nextStep\n  }, {\n    default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"下一步\")])),\n    _: 1 /* STABLE */,\n    __: [21]\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    onClick: $setup.prevStep\n  }, {\n    default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\"上一步\")])),\n    _: 1 /* STABLE */,\n    __: [22]\n  }, 8 /* PROPS */, [\"onClick\"])]), _createVNode(_component_el_alert, {\n    title: \"重要提示\",\n    type: \"warning\",\n    description: \"请务必保存您的私钥，私钥将不会存储在服务器上，丢失后无法找回。\",\n    \"show-icon\": \"\",\n    closable: false,\n    class: \"key-warning\"\n  })])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.currentStep === 2 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" 步骤3：完成注册 \"), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_cache[30] || (_cache[30] = _createElementVNode(\"h3\", null, \"确认注册信息\", -1 /* HOISTED */)), _createVNode(_component_el_descriptions, {\n    column: 1,\n    border: \"\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n      label: \"学号\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.form.studentId), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_descriptions_item, {\n      label: \"邮箱\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.form.email), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_descriptions_item, {\n      label: \"手机号\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.form.phone), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_descriptions_item, {\n      label: \"是否使用SM2密钥\"\n    }, {\n      default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\"是\")])),\n      _: 1 /* STABLE */,\n      __: [25]\n    })]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_checkbox, {\n    modelValue: $setup.agreement,\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.agreement = $event)\n  }, {\n    default: _withCtx(() => [_cache[26] || (_cache[26] = _createTextVNode(\" 我已阅读并同意 \")), _createElementVNode(\"a\", {\n      href: \"#\",\n      onClick: _cache[8] || (_cache[8] = _withModifiers((...args) => $setup.showTerms && $setup.showTerms(...args), [\"prevent\"]))\n    }, \"《用户协议》\"), _cache[27] || (_cache[27] = _createTextVNode(\" 和 \")), _createElementVNode(\"a\", {\n      href: \"#\",\n      onClick: _cache[9] || (_cache[9] = _withModifiers((...args) => $setup.showPrivacy && $setup.showPrivacy(...args), [\"prevent\"]))\n    }, \"《隐私政策》\")]),\n    _: 1 /* STABLE */,\n    __: [26, 27]\n  }, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    loading: $setup.loading,\n    disabled: !$setup.agreement,\n    onClick: $setup.handleRegister\n  }, {\n    default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\" 提交注册 \")])),\n    _: 1 /* STABLE */,\n    __: [28]\n  }, 8 /* PROPS */, [\"loading\", \"disabled\", \"onClick\"]), _createVNode(_component_el_button, {\n    onClick: $setup.prevStep\n  }, {\n    default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\"上一步\")])),\n    _: 1 /* STABLE */,\n    __: [29]\n  }, 8 /* PROPS */, [\"onClick\"])])])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.currentStep === 3 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 3\n  }, [_createCommentVNode(\" 步骤4：注册成功 \"), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_el_result, {\n    icon: \"success\",\n    title: \"注册成功\",\n    \"sub-title\": \"您已成功注册基于国密算法的图书馆自习室座位管理系统账号\"\n  }, {\n    extra: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[11] || (_cache[11] = $event => _ctx.$router.push('/login'))\n    }, {\n      default: _withCtx(() => _cache[31] || (_cache[31] = [_createTextVNode(\"前往登录\")])),\n      _: 1 /* STABLE */,\n      __: [31]\n    })]),\n    _: 1 /* STABLE */\n  })])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_steps", "active", "$setup", "currentStep", "simple", "default", "_withCtx", "_component_el_step", "title", "_", "_createCommentVNode", "_hoisted_3", "_component_el_form", "ref", "model", "form", "rules", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "studentId", "_cache", "$event", "placeholder", "password", "type", "confirmPassword", "email", "phone", "_component_el_button", "onClick", "nextStep", "_createTextVNode", "__", "_ctx", "$router", "push", "_Fragment", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "size", "copyPublicKey", "_component_el_icon", "_component_DocumentCopy", "downloadPublicKey", "_component_Download", "keyPair", "public<PERSON>ey", "rows", "readonly", "_hoisted_9", "_hoisted_10", "_hoisted_11", "copyPrivateKey", "downloadPrivateKey", "privateKey", "_hoisted_12", "regenerateKeyPair", "prevStep", "_component_el_alert", "description", "closable", "_hoisted_13", "_hoisted_14", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "_toDisplayString", "_hoisted_15", "_component_el_checkbox", "agreement", "href", "_withModifiers", "args", "showTerms", "showPrivacy", "_hoisted_16", "loading", "disabled", "handleRegister", "_hoisted_17", "_hoisted_18", "_component_el_result", "icon", "extra"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Register.vue"], "sourcesContent": ["<template>\n  <div class=\"register-container\">\n    <div class=\"register-card\">\n      <div class=\"register-header\">\n        <h2>基于国密算法的图书馆自习室座位管理系统</h2>\n        <h3>用户注册</h3>\n      </div>\n\n      <el-steps :active=\"currentStep\" finish-status=\"success\" simple>\n        <el-step title=\"填写信息\" />\n        <el-step title=\"生成密钥\" />\n        <el-step title=\"完成注册\" />\n      </el-steps>\n\n      <!-- 步骤1：填写基本信息 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <el-form\n          ref=\"formRef\"\n          :model=\"form\"\n          :rules=\"rules\"\n          label-position=\"top\"\n        >\n          <el-form-item label=\"学号\" prop=\"studentId\">\n            <el-input v-model=\"form.studentId\" placeholder=\"请输入学号\" />\n          </el-form-item>\n\n          <el-form-item label=\"密码\" prop=\"password\">\n            <el-input\n              v-model=\"form.password\"\n              type=\"password\"\n              placeholder=\"请输入密码\"\n              show-password\n            />\n          </el-form-item>\n\n          <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\n            <el-input\n              v-model=\"form.confirmPassword\"\n              type=\"password\"\n              placeholder=\"请再次输入密码\"\n              show-password\n            />\n          </el-form-item>\n\n          <el-form-item label=\"邮箱\" prop=\"email\">\n            <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" />\n          </el-form-item>\n\n          <el-form-item label=\"手机号\" prop=\"phone\">\n            <el-input v-model=\"form.phone\" placeholder=\"请输入手机号\" />\n          </el-form-item>\n\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"nextStep\">下一步</el-button>\n            <el-button @click=\"$router.push('/login')\">返回登录</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <!-- 步骤2：生成SM2密钥对 -->\n      <div v-else-if=\"currentStep === 1\" class=\"step-content\">\n        <div class=\"key-generation\">\n          <h3>SM2密钥对生成</h3>\n          <p class=\"description\">\n            SM2密钥对用于安全登录和数据签名，请妥善保管您的私钥，公钥将上传至服务器。\n          </p>\n\n          <div class=\"key-box\">\n            <div class=\"key-label\">\n              <span>公钥</span>\n              <div class=\"key-actions-buttons\">\n                <el-button type=\"primary\" size=\"small\" @click=\"copyPublicKey\">\n                  <el-icon><DocumentCopy /></el-icon> 复制\n                </el-button>\n                <el-button\n                  type=\"success\"\n                  size=\"small\"\n                  @click=\"downloadPublicKey\"\n                >\n                  <el-icon><Download /></el-icon> 下载\n                </el-button>\n              </div>\n            </div>\n            <el-input\n              v-model=\"keyPair.publicKey\"\n              type=\"textarea\"\n              :rows=\"3\"\n              readonly\n            />\n          </div>\n\n          <div class=\"key-box\">\n            <div class=\"key-label\">\n              <span>私钥</span>\n              <div class=\"key-actions-buttons\">\n                <el-button type=\"primary\" size=\"small\" @click=\"copyPrivateKey\">\n                  <el-icon><DocumentCopy /></el-icon> 复制\n                </el-button>\n                <el-button\n                  type=\"success\"\n                  size=\"small\"\n                  @click=\"downloadPrivateKey\"\n                >\n                  <el-icon><Download /></el-icon> 下载\n                </el-button>\n              </div>\n            </div>\n            <el-input\n              v-model=\"keyPair.privateKey\"\n              type=\"textarea\"\n              :rows=\"3\"\n              readonly\n              show-password\n            />\n          </div>\n\n          <div class=\"key-actions\">\n            <el-button type=\"primary\" @click=\"regenerateKeyPair\"\n              >重新生成</el-button\n            >\n            <el-button type=\"success\" @click=\"nextStep\">下一步</el-button>\n            <el-button @click=\"prevStep\">上一步</el-button>\n          </div>\n\n          <el-alert\n            title=\"重要提示\"\n            type=\"warning\"\n            description=\"请务必保存您的私钥，私钥将不会存储在服务器上，丢失后无法找回。\"\n            show-icon\n            :closable=\"false\"\n            class=\"key-warning\"\n          />\n        </div>\n      </div>\n\n      <!-- 步骤3：完成注册 -->\n      <div v-else-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"register-confirm\">\n          <h3>确认注册信息</h3>\n\n          <el-descriptions :column=\"1\" border>\n            <el-descriptions-item label=\"学号\">{{\n              form.studentId\n            }}</el-descriptions-item>\n            <el-descriptions-item label=\"邮箱\">{{\n              form.email\n            }}</el-descriptions-item>\n            <el-descriptions-item label=\"手机号\">{{\n              form.phone\n            }}</el-descriptions-item>\n            <el-descriptions-item label=\"是否使用SM2密钥\"\n              >是</el-descriptions-item\n            >\n          </el-descriptions>\n\n          <div class=\"confirm-actions\">\n            <el-checkbox v-model=\"agreement\">\n              我已阅读并同意\n              <a href=\"#\" @click.prevent=\"showTerms\">《用户协议》</a>\n              和\n              <a href=\"#\" @click.prevent=\"showPrivacy\">《隐私政策》</a>\n            </el-checkbox>\n\n            <div class=\"button-group\">\n              <el-button\n                type=\"primary\"\n                :loading=\"loading\"\n                :disabled=\"!agreement\"\n                @click=\"handleRegister\"\n              >\n                提交注册\n              </el-button>\n              <el-button @click=\"prevStep\">上一步</el-button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 步骤4：注册成功 -->\n      <div v-else-if=\"currentStep === 3\" class=\"step-content\">\n        <div class=\"register-success\">\n          <el-result\n            icon=\"success\"\n            title=\"注册成功\"\n            sub-title=\"您已成功注册基于国密算法的图书馆自习室座位管理系统账号\"\n          >\n            <template #extra>\n              <el-button type=\"primary\" @click=\"$router.push('/login')\"\n                >前往登录</el-button\n              >\n            </template>\n          </el-result>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive } from \"vue\";\nimport { useStore } from \"vuex\";\n\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nimport { DocumentCopy, Download } from \"@element-plus/icons-vue\";\nimport { SM2Crypto, SM3Hasher } from \"@/utils/crypto\";\n\nexport default {\n  name: \"RegisterView\",\n  setup() {\n    const store = useStore();\n\n    const formRef = ref(null);\n    const currentStep = ref(0);\n    const loading = ref(false);\n    const agreement = ref(false);\n\n    // 注册表单\n    const form = reactive({\n      studentId: \"\",\n      password: \"\",\n      confirmPassword: \"\",\n      email: \"\",\n      phone: \"\",\n    });\n\n    // SM2密钥对\n    const keyPair = reactive({\n      publicKey: \"\",\n      privateKey: \"\",\n    });\n\n    // 表单验证规则\n    const validatePass = (rule, value, callback) => {\n      if (value === \"\") {\n        callback(new Error(\"请输入密码\"));\n      } else if (value.length < 6) {\n        callback(new Error(\"密码长度不能小于6位\"));\n      } else {\n        if (form.confirmPassword !== \"\") {\n          formRef.value.validateField(\"confirmPassword\");\n        }\n        callback();\n      }\n    };\n\n    const validatePass2 = (rule, value, callback) => {\n      if (value === \"\") {\n        callback(new Error(\"请再次输入密码\"));\n      } else if (value !== form.password) {\n        callback(new Error(\"两次输入密码不一致\"));\n      } else {\n        callback();\n      }\n    };\n\n    const rules = {\n      studentId: [\n        { required: true, message: \"请输入学号\", trigger: \"blur\" },\n        { pattern: /^\\d{11}$/, message: \"学号必须为11位数字\", trigger: \"blur\" },\n      ],\n      password: [{ validator: validatePass, trigger: \"blur\" }],\n      confirmPassword: [{ validator: validatePass2, trigger: \"blur\" }],\n      email: [\n        { required: true, message: \"请输入邮箱\", trigger: \"blur\" },\n        { type: \"email\", message: \"请输入正确的邮箱格式\", trigger: \"blur\" },\n      ],\n      phone: [\n        { required: true, message: \"请输入手机号\", trigger: \"blur\" },\n        {\n          pattern: /^1[3-9]\\d{9}$/,\n          message: \"请输入正确的手机号格式\",\n          trigger: \"blur\",\n        },\n      ],\n    };\n\n    // 生成SM2密钥对\n    const generateKeyPair = () => {\n      const newKeyPair = SM2Crypto.generateKeyPair();\n      keyPair.publicKey = newKeyPair.publicKey;\n      keyPair.privateKey = newKeyPair.privateKey;\n    };\n\n    // 重新生成SM2密钥对\n    const regenerateKeyPair = () => {\n      ElMessageBox.confirm(\n        \"重新生成密钥对将覆盖当前密钥，确定要继续吗？\",\n        \"提示\",\n        {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      )\n        .then(() => {\n          generateKeyPair();\n          ElMessage.success(\"密钥对已重新生成\");\n        })\n        .catch(() => {\n          // 用户取消操作\n        });\n    };\n\n    // 复制文本到剪贴板的通用函数\n    const copyToClipboard = (text, successMsg) => {\n      // 检查navigator.clipboard API是否可用\n      if (navigator.clipboard && navigator.clipboard.writeText) {\n        navigator.clipboard\n          .writeText(text)\n          .then(() => {\n            ElMessage.success(successMsg);\n          })\n          .catch(() => {\n            // 如果API调用失败，使用备用方法\n            fallbackCopyToClipboard(text, successMsg);\n          });\n      } else {\n        // 如果API不可用，使用备用方法\n        fallbackCopyToClipboard(text, successMsg);\n      }\n    };\n\n    // 备用复制方法（使用临时DOM元素）\n    const fallbackCopyToClipboard = (text, successMsg) => {\n      try {\n        // 创建临时textarea元素\n        const textArea = document.createElement(\"textarea\");\n        textArea.value = text;\n\n        // 设置样式使其不可见\n        textArea.style.position = \"fixed\";\n        textArea.style.left = \"-999999px\";\n        textArea.style.top = \"-999999px\";\n        document.body.appendChild(textArea);\n\n        // 选择文本并复制\n        textArea.focus();\n        textArea.select();\n        const successful = document.execCommand(\"copy\");\n\n        // 移除临时元素\n        document.body.removeChild(textArea);\n\n        if (successful) {\n          ElMessage.success(successMsg);\n        } else {\n          ElMessage.error(\"复制失败，请手动复制\");\n        }\n      } catch (err) {\n        ElMessage.error(\"复制失败，请手动复制\");\n      }\n    };\n\n    // 下载文本文件的通用函数\n    const downloadTextFile = (text, filename) => {\n      const blob = new Blob([text], { type: \"text/plain\" });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = filename;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n      ElMessage.success(`${filename} 已下载`);\n    };\n\n    // 复制公钥\n    const copyPublicKey = () => {\n      copyToClipboard(keyPair.publicKey, \"公钥已复制到剪贴板\");\n    };\n\n    // 复制私钥\n    const copyPrivateKey = () => {\n      copyToClipboard(keyPair.privateKey, \"私钥已复制到剪贴板\");\n    };\n\n    // 下载公钥\n    const downloadPublicKey = () => {\n      downloadTextFile(keyPair.publicKey, \"sm2_public_key.txt\");\n    };\n\n    // 下载私钥\n    const downloadPrivateKey = () => {\n      downloadTextFile(keyPair.privateKey, \"sm2_private_key.txt\");\n    };\n\n    // 下一步\n    const nextStep = async () => {\n      if (currentStep.value === 0) {\n        if (!formRef.value) return;\n\n        await formRef.value.validate(async (valid) => {\n          if (valid) {\n            currentStep.value++;\n\n            // 如果还没有生成密钥对，则生成\n            if (!keyPair.publicKey || !keyPair.privateKey) {\n              generateKeyPair();\n            }\n          }\n        });\n      } else {\n        currentStep.value++;\n      }\n    };\n\n    // 上一步\n    const prevStep = () => {\n      currentStep.value--;\n    };\n\n    // 显示用户协议\n    const showTerms = () => {\n      ElMessageBox.alert(\"这里是用户协议内容...\", \"用户协议\", {\n        confirmButtonText: \"确定\",\n      });\n    };\n\n    // 显示隐私政策\n    const showPrivacy = () => {\n      ElMessageBox.alert(\"这里是隐私政策内容...\", \"隐私政策\", {\n        confirmButtonText: \"确定\",\n      });\n    };\n\n    // 提交注册\n    const handleRegister = async () => {\n      try {\n        loading.value = true;\n\n        // 对密码进行SM3哈希\n        const hashedPassword = SM3Hasher.hash(form.password);\n\n        // 调用注册接口\n        await store.dispatch(\"user/register\", {\n          studentId: form.studentId,\n          password: hashedPassword,\n          email: form.email,\n          phone: form.phone,\n          publicKey: keyPair.publicKey,\n        });\n\n        // 注册成功，进入下一步\n        currentStep.value = 3;\n        ElMessage.success(\"注册成功\");\n      } catch (error) {\n        ElMessage.error(error.message || \"注册失败，请稍后重试\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    return {\n      formRef,\n      currentStep,\n      loading,\n      agreement,\n      form,\n      keyPair,\n      rules,\n      nextStep,\n      prevStep,\n      generateKeyPair,\n      regenerateKeyPair,\n      copyPublicKey,\n      copyPrivateKey,\n      downloadPublicKey,\n      downloadPrivateKey,\n      showTerms,\n      showPrivacy,\n      handleRegister,\n      DocumentCopy,\n      Download,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.register-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background-image: url(\"@/assets/background.jpg\");\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  position: relative;\n  padding: 20px;\n}\n\n/* 移除半透明白色遮罩层 */\n\n.register-card {\n  width: 500px;\n  padding: 30px;\n  background-color: rgba(255, 255, 255, 0.9);\n  border-radius: 8px;\n  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.2);\n  position: relative;\n  z-index: 1;\n}\n\n.register-header {\n  text-align: center;\n  margin-bottom: 30px;\n\n  h2 {\n    font-size: 22px;\n    color: #303133;\n    margin: 0 0 10px 0;\n    font-weight: 600;\n    line-height: 1.4;\n  }\n\n  h3 {\n    font-size: 18px;\n    color: #606266;\n    margin: 0;\n  }\n\n  /* 删除logo相关样式 */\n}\n\n.step-content {\n  margin-top: 30px;\n}\n\n.key-generation {\n  .description {\n    margin-bottom: 20px;\n    color: #606266;\n  }\n\n  .key-box {\n    margin-bottom: 20px;\n\n    .key-label {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 5px;\n      font-weight: bold;\n\n      .key-actions-buttons {\n        display: flex;\n        gap: 8px;\n      }\n    }\n  }\n\n  .key-actions {\n    display: flex;\n    gap: 10px;\n    margin-bottom: 20px;\n  }\n\n  .key-warning {\n    margin-top: 20px;\n  }\n}\n\n.register-confirm {\n  .confirm-actions {\n    margin-top: 30px;\n\n    .button-group {\n      margin-top: 20px;\n      display: flex;\n      gap: 10px;\n    }\n  }\n}\n\n.register-success {\n  padding: 20px 0;\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAe;;EAF9BC,GAAA;EAeoCD,KAAK,EAAC;;;EA6CDA,KAAK,EAAC;AAAc;;EAChDA,KAAK,EAAC;AAAgB;;EAMpBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAqB;;EAqB/BA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAqB;;EAsB/BA,KAAK,EAAC;AAAa;;EAoBOA,KAAK,EAAC;AAAc;;EAChDA,KAAK,EAAC;AAAkB;;EAkBtBA,KAAK,EAAC;AAAiB;;EAQrBA,KAAK,EAAC;AAAc;;EAgBIA,KAAK,EAAC;AAAc;;EAChDA,KAAK,EAAC;AAAkB;;;;;;;;;;;;;;;;uBAnLnCE,mBAAA,CAkMM,OAlMNC,UAkMM,GAjMJC,mBAAA,CAgMM,OAhMNC,UAgMM,G,4BA/LJD,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAiB,IAC1BI,mBAAA,CAA4B,YAAxB,qBAAmB,GACvBA,mBAAA,CAAa,YAAT,MAAI,E,sBAGVE,YAAA,CAIWC,mBAAA;IAJAC,MAAM,EAAEC,MAAA,CAAAC,WAAW;IAAE,eAAa,EAAC,SAAS;IAACC,MAAM,EAAN;;IAR9DC,OAAA,EAAAC,QAAA,CASQ,MAAwB,CAAxBP,YAAA,CAAwBQ,kBAAA;MAAfC,KAAK,EAAC;IAAM,IACrBT,YAAA,CAAwBQ,kBAAA;MAAfC,KAAK,EAAC;IAAM,IACrBT,YAAA,CAAwBQ,kBAAA;MAAfC,KAAK,EAAC;IAAM,G;IAX7BC,CAAA;iCAcMC,mBAAA,gBAAmB,EACRR,MAAA,CAAAC,WAAW,U,cAAtBR,mBAAA,CA0CM,OA1CNgB,UA0CM,GAzCJZ,YAAA,CAwCUa,kBAAA;IAvCRC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEZ,MAAA,CAAAa,IAAI;IACXC,KAAK,EAAEd,MAAA,CAAAc,KAAK;IACb,gBAAc,EAAC;;IApBzBX,OAAA,EAAAC,QAAA,CAsBU,MAEe,CAFfP,YAAA,CAEekB,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;MAtBxCd,OAAA,EAAAC,QAAA,CAuBY,MAAyD,CAAzDP,YAAA,CAAyDqB,mBAAA;QAvBrEC,UAAA,EAuB+BnB,MAAA,CAAAa,IAAI,CAACO,SAAS;QAvB7C,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAuB+BtB,MAAA,CAAAa,IAAI,CAACO,SAAS,GAAAE,MAAA;QAAEC,WAAW,EAAC;;MAvB3DhB,CAAA;QA0BUV,YAAA,CAOekB,uBAAA;MAPDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;MA1BxCd,OAAA,EAAAC,QAAA,CA2BY,MAKE,CALFP,YAAA,CAKEqB,mBAAA;QAhCdC,UAAA,EA4BuBnB,MAAA,CAAAa,IAAI,CAACW,QAAQ;QA5BpC,uBAAAH,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA4BuBtB,MAAA,CAAAa,IAAI,CAACW,QAAQ,GAAAF,MAAA;QACtBG,IAAI,EAAC,UAAU;QACfF,WAAW,EAAC,OAAO;QACnB,eAAa,EAAb;;MA/BdhB,CAAA;QAmCUV,YAAA,CAOekB,uBAAA;MAPDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MAnC1Cd,OAAA,EAAAC,QAAA,CAoCY,MAKE,CALFP,YAAA,CAKEqB,mBAAA;QAzCdC,UAAA,EAqCuBnB,MAAA,CAAAa,IAAI,CAACa,eAAe;QArC3C,uBAAAL,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAqCuBtB,MAAA,CAAAa,IAAI,CAACa,eAAe,GAAAJ,MAAA;QAC7BG,IAAI,EAAC,UAAU;QACfF,WAAW,EAAC,SAAS;QACrB,eAAa,EAAb;;MAxCdhB,CAAA;QA4CUV,YAAA,CAEekB,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;MA5CxCd,OAAA,EAAAC,QAAA,CA6CY,MAAqD,CAArDP,YAAA,CAAqDqB,mBAAA;QA7CjEC,UAAA,EA6C+BnB,MAAA,CAAAa,IAAI,CAACc,KAAK;QA7CzC,uBAAAN,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA6C+BtB,MAAA,CAAAa,IAAI,CAACc,KAAK,GAAAL,MAAA;QAAEC,WAAW,EAAC;;MA7CvDhB,CAAA;QAgDUV,YAAA,CAEekB,uBAAA;MAFDC,KAAK,EAAC,KAAK;MAACC,IAAI,EAAC;;MAhDzCd,OAAA,EAAAC,QAAA,CAiDY,MAAsD,CAAtDP,YAAA,CAAsDqB,mBAAA;QAjDlEC,UAAA,EAiD+BnB,MAAA,CAAAa,IAAI,CAACe,KAAK;QAjDzC,uBAAAP,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAiD+BtB,MAAA,CAAAa,IAAI,CAACe,KAAK,GAAAN,MAAA;QAAEC,WAAW,EAAC;;MAjDvDhB,CAAA;QAoDUV,YAAA,CAGekB,uBAAA;MAvDzBZ,OAAA,EAAAC,QAAA,CAqDY,MAA2D,CAA3DP,YAAA,CAA2DgC,oBAAA;QAAhDJ,IAAI,EAAC,SAAS;QAAEK,OAAK,EAAE9B,MAAA,CAAA+B;;QArD9C5B,OAAA,EAAAC,QAAA,CAqDwD,MAAGiB,MAAA,SAAAA,MAAA,QArD3DW,gBAAA,CAqDwD,KAAG,E;QArD3DzB,CAAA;QAAA0B,EAAA;sCAsDYpC,YAAA,CAA2DgC,oBAAA;QAA/CC,OAAK,EAAAT,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEY,IAAA,CAAAC,OAAO,CAACC,IAAI;;QAtD3CjC,OAAA,EAAAC,QAAA,CAsDuD,MAAIiB,MAAA,SAAAA,MAAA,QAtD3DW,gBAAA,CAsDuD,MAAI,E;QAtD3DzB,CAAA;QAAA0B,EAAA;;MAAA1B,CAAA;;IAAAA,CAAA;6CA4DsBP,MAAA,CAAAC,WAAW,U,cAA3BR,mBAAA,CAyEM4C,SAAA;IArIZ7C,GAAA;EAAA,IA2DMgB,mBAAA,kBAAqB,EACrBb,mBAAA,CAyEM,OAzEN2C,UAyEM,GAxEJ3C,mBAAA,CAuEM,OAvEN4C,UAuEM,G,4BAtEJ5C,mBAAA,CAAiB,YAAb,UAAQ,sB,4BACZA,mBAAA,CAEI;IAFDJ,KAAK,EAAC;EAAa,GAAC,0CAEvB,sBAEAI,mBAAA,CAsBM,OAtBN6C,UAsBM,GArBJ7C,mBAAA,CAcM,OAdN8C,UAcM,G,4BAbJ9C,mBAAA,CAAe,cAAT,IAAE,sBACRA,mBAAA,CAWM,OAXN+C,UAWM,GAVJ7C,YAAA,CAEYgC,oBAAA;IAFDJ,IAAI,EAAC,SAAS;IAACkB,IAAI,EAAC,OAAO;IAAEb,OAAK,EAAE9B,MAAA,CAAA4C;;IAvE/DzC,OAAA,EAAAC,QAAA,CAwEkB,MAAmC,CAAnCP,YAAA,CAAmCgD,kBAAA;MAxErD1C,OAAA,EAAAC,QAAA,CAwE2B,MAAgB,CAAhBP,YAAA,CAAgBiD,uBAAA,E;MAxE3CvC,CAAA;oCAAAyB,gBAAA,CAwEqD,MACrC,G;IAzEhBzB,CAAA;IAAA0B,EAAA;kCA0EgBpC,YAAA,CAMYgC,oBAAA;IALVJ,IAAI,EAAC,SAAS;IACdkB,IAAI,EAAC,OAAO;IACXb,OAAK,EAAE9B,MAAA,CAAA+C;;IA7E1B5C,OAAA,EAAAC,QAAA,CA+EkB,MAA+B,CAA/BP,YAAA,CAA+BgD,kBAAA;MA/EjD1C,OAAA,EAAAC,QAAA,CA+E2B,MAAY,CAAZP,YAAA,CAAYmD,mBAAA,E;MA/EvCzC,CAAA;oCAAAyB,gBAAA,CA+EiD,MACjC,G;IAhFhBzB,CAAA;IAAA0B,EAAA;sCAmFYpC,YAAA,CAKEqB,mBAAA;IAxFdC,UAAA,EAoFuBnB,MAAA,CAAAiD,OAAO,CAACC,SAAS;IApFxC,uBAAA7B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAoFuBtB,MAAA,CAAAiD,OAAO,CAACC,SAAS,GAAA5B,MAAA;IAC1BG,IAAI,EAAC,UAAU;IACd0B,IAAI,EAAE,CAAC;IACRC,QAAQ,EAAR;6CAIJzD,mBAAA,CAuBM,OAvBN0D,UAuBM,GAtBJ1D,mBAAA,CAcM,OAdN2D,WAcM,G,4BAbJ3D,mBAAA,CAAe,cAAT,IAAE,sBACRA,mBAAA,CAWM,OAXN4D,WAWM,GAVJ1D,YAAA,CAEYgC,oBAAA;IAFDJ,IAAI,EAAC,SAAS;IAACkB,IAAI,EAAC,OAAO;IAAEb,OAAK,EAAE9B,MAAA,CAAAwD;;IA/F/DrD,OAAA,EAAAC,QAAA,CAgGkB,MAAmC,CAAnCP,YAAA,CAAmCgD,kBAAA;MAhGrD1C,OAAA,EAAAC,QAAA,CAgG2B,MAAgB,CAAhBP,YAAA,CAAgBiD,uBAAA,E;MAhG3CvC,CAAA;oCAAAyB,gBAAA,CAgGqD,MACrC,G;IAjGhBzB,CAAA;IAAA0B,EAAA;kCAkGgBpC,YAAA,CAMYgC,oBAAA;IALVJ,IAAI,EAAC,SAAS;IACdkB,IAAI,EAAC,OAAO;IACXb,OAAK,EAAE9B,MAAA,CAAAyD;;IArG1BtD,OAAA,EAAAC,QAAA,CAuGkB,MAA+B,CAA/BP,YAAA,CAA+BgD,kBAAA;MAvGjD1C,OAAA,EAAAC,QAAA,CAuG2B,MAAY,CAAZP,YAAA,CAAYmD,mBAAA,E;MAvGvCzC,CAAA;oCAAAyB,gBAAA,CAuGiD,MACjC,G;IAxGhBzB,CAAA;IAAA0B,EAAA;sCA2GYpC,YAAA,CAMEqB,mBAAA;IAjHdC,UAAA,EA4GuBnB,MAAA,CAAAiD,OAAO,CAACS,UAAU;IA5GzC,uBAAArC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA4GuBtB,MAAA,CAAAiD,OAAO,CAACS,UAAU,GAAApC,MAAA;IAC3BG,IAAI,EAAC,UAAU;IACd0B,IAAI,EAAE,CAAC;IACRC,QAAQ,EAAR,EAAQ;IACR,eAAa,EAAb;6CAIJzD,mBAAA,CAMM,OANNgE,WAMM,GALJ9D,YAAA,CAECgC,oBAAA;IAFUJ,IAAI,EAAC,SAAS;IAAEK,OAAK,EAAE9B,MAAA,CAAA4D;;IArH9CzD,OAAA,EAAAC,QAAA,CAsHe,MAAIiB,MAAA,SAAAA,MAAA,QAtHnBW,gBAAA,CAsHe,MAAI,E;IAtHnBzB,CAAA;IAAA0B,EAAA;kCAwHYpC,YAAA,CAA2DgC,oBAAA;IAAhDJ,IAAI,EAAC,SAAS;IAAEK,OAAK,EAAE9B,MAAA,CAAA+B;;IAxH9C5B,OAAA,EAAAC,QAAA,CAwHwD,MAAGiB,MAAA,SAAAA,MAAA,QAxH3DW,gBAAA,CAwHwD,KAAG,E;IAxH3DzB,CAAA;IAAA0B,EAAA;kCAyHYpC,YAAA,CAA4CgC,oBAAA;IAAhCC,OAAK,EAAE9B,MAAA,CAAA6D;EAAQ;IAzHvC1D,OAAA,EAAAC,QAAA,CAyHyC,MAAGiB,MAAA,SAAAA,MAAA,QAzH5CW,gBAAA,CAyHyC,KAAG,E;IAzH5CzB,CAAA;IAAA0B,EAAA;oCA4HUpC,YAAA,CAOEiE,mBAAA;IANAxD,KAAK,EAAC,MAAM;IACZmB,IAAI,EAAC,SAAS;IACdsC,WAAW,EAAC,iCAAiC;IAC7C,WAAS,EAAT,EAAS;IACRC,QAAQ,EAAE,KAAK;IAChBzE,KAAK,EAAC;6DAMIS,MAAA,CAAAC,WAAW,U,cAA3BR,mBAAA,CAwCM4C,SAAA;IAhLZ7C,GAAA;EAAA,IAuIMgB,mBAAA,cAAiB,EACjBb,mBAAA,CAwCM,OAxCNsE,WAwCM,GAvCJtE,mBAAA,CAsCM,OAtCNuE,WAsCM,G,4BArCJvE,mBAAA,CAAe,YAAX,QAAM,sBAEVE,YAAA,CAakBsE,0BAAA;IAbAC,MAAM,EAAE,CAAC;IAAEC,MAAM,EAAN;;IA5IvClE,OAAA,EAAAC,QAAA,CA6IY,MAEyB,CAFzBP,YAAA,CAEyByE,+BAAA;MAFHtD,KAAK,EAAC;IAAI;MA7I5Cb,OAAA,EAAAC,QAAA,CA6I6C,MAE/B,CA/Id4B,gBAAA,CAAAuC,gBAAA,CA8IcvE,MAAA,CAAAa,IAAI,CAACO,SAAS,iB;MA9I5Bb,CAAA;QAgJYV,YAAA,CAEyByE,+BAAA;MAFHtD,KAAK,EAAC;IAAI;MAhJ5Cb,OAAA,EAAAC,QAAA,CAgJ6C,MAE/B,CAlJd4B,gBAAA,CAAAuC,gBAAA,CAiJcvE,MAAA,CAAAa,IAAI,CAACc,KAAK,iB;MAjJxBpB,CAAA;QAmJYV,YAAA,CAEyByE,+BAAA;MAFHtD,KAAK,EAAC;IAAK;MAnJ7Cb,OAAA,EAAAC,QAAA,CAmJ8C,MAEhC,CArJd4B,gBAAA,CAAAuC,gBAAA,CAoJcvE,MAAA,CAAAa,IAAI,CAACe,KAAK,iB;MApJxBrB,CAAA;QAsJYV,YAAA,CAECyE,+BAAA;MAFqBtD,KAAK,EAAC;IAAW;MAtJnDb,OAAA,EAAAC,QAAA,CAuJe,MAACiB,MAAA,SAAAA,MAAA,QAvJhBW,gBAAA,CAuJe,GAAC,E;MAvJhBzB,CAAA;MAAA0B,EAAA;;IAAA1B,CAAA;MA2JUZ,mBAAA,CAmBM,OAnBN6E,WAmBM,GAlBJ3E,YAAA,CAKc4E,sBAAA;IAjK1BtD,UAAA,EA4JkCnB,MAAA,CAAA0E,SAAS;IA5J3C,uBAAArD,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA4JkCtB,MAAA,CAAA0E,SAAS,GAAApD,MAAA;;IA5J3CnB,OAAA,EAAAC,QAAA,CA4J6C,MAE/B,C,4BA9Jd4B,gBAAA,CA4J6C,WAE/B,IAAArC,mBAAA,CAAiD;MAA9CgF,IAAI,EAAC,GAAG;MAAE7C,OAAK,EAAAT,MAAA,QAAAA,MAAA,MA9JhCuD,cAAA,KAAAC,IAAA,KA8J0C7E,MAAA,CAAA8E,SAAA,IAAA9E,MAAA,CAAA8E,SAAA,IAAAD,IAAA,CAAS;OAAE,QAAM,G,4BA9J3D7C,gBAAA,CA8J+D,KAEjD,IAAArC,mBAAA,CAAmD;MAAhDgF,IAAI,EAAC,GAAG;MAAE7C,OAAK,EAAAT,MAAA,QAAAA,MAAA,MAhKhCuD,cAAA,KAAAC,IAAA,KAgK0C7E,MAAA,CAAA+E,WAAA,IAAA/E,MAAA,CAAA+E,WAAA,IAAAF,IAAA,CAAW;OAAE,QAAM,E;IAhK7DtE,CAAA;IAAA0B,EAAA;qCAmKYtC,mBAAA,CAUM,OAVNqF,WAUM,GATJnF,YAAA,CAOYgC,oBAAA;IANVJ,IAAI,EAAC,SAAS;IACbwD,OAAO,EAAEjF,MAAA,CAAAiF,OAAO;IAChBC,QAAQ,GAAGlF,MAAA,CAAA0E,SAAS;IACpB5C,OAAK,EAAE9B,MAAA,CAAAmF;;IAxKxBhF,OAAA,EAAAC,QAAA,CAyKe,MAEDiB,MAAA,SAAAA,MAAA,QA3KdW,gBAAA,CAyKe,QAED,E;IA3KdzB,CAAA;IAAA0B,EAAA;yDA4KcpC,YAAA,CAA4CgC,oBAAA;IAAhCC,OAAK,EAAE9B,MAAA,CAAA6D;EAAQ;IA5KzC1D,OAAA,EAAAC,QAAA,CA4K2C,MAAGiB,MAAA,SAAAA,MAAA,QA5K9CW,gBAAA,CA4K2C,KAAG,E;IA5K9CzB,CAAA;IAAA0B,EAAA;6FAmLsBjC,MAAA,CAAAC,WAAW,U,cAA3BR,mBAAA,CAcM4C,SAAA;IAjMZ7C,GAAA;EAAA,IAkLMgB,mBAAA,cAAiB,EACjBb,mBAAA,CAcM,OAdNyF,WAcM,GAbJzF,mBAAA,CAYM,OAZN0F,WAYM,GAXJxF,YAAA,CAUYyF,oBAAA;IATVC,IAAI,EAAC,SAAS;IACdjF,KAAK,EAAC,MAAM;IACZ,WAAS,EAAC;;IAECkF,KAAK,EAAApF,QAAA,CACd,MAEC,CAFDP,YAAA,CAECgC,oBAAA;MAFUJ,IAAI,EAAC,SAAS;MAAEK,OAAK,EAAAT,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEY,IAAA,CAAAC,OAAO,CAACC,IAAI;;MA3L5DjC,OAAA,EAAAC,QAAA,CA4LiB,MAAIiB,MAAA,SAAAA,MAAA,QA5LrBW,gBAAA,CA4LiB,MAAI,E;MA5LrBzB,CAAA;MAAA0B,EAAA;;IAAA1B,CAAA;6DAAAC,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}