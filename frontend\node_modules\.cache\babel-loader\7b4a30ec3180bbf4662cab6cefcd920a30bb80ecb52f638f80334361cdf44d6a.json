{"ast": null, "code": "import Steps from './src/steps2.mjs';\nimport Step from './src/item2.mjs';\nexport { stepProps } from './src/item.mjs';\nexport { stepsEmits, stepsProps } from './src/steps.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElSteps = withInstall(Steps, {\n  Step\n});\nconst ElStep = withNoopInstall(Step);\nexport { ElStep, ElSteps, ElSteps as default };", "map": {"version": 3, "names": ["ElSteps", "withInstall", "Steps", "Step", "ElStep", "withNoopInstall"], "sources": ["../../../../../packages/components/steps/index.ts"], "sourcesContent": ["import { withInstall, withNoopInstall } from '@element-plus/utils'\n\nimport Steps from './src/steps.vue'\nimport Step from './src/item.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSteps: SFCWithInstall<typeof Steps> & {\n  Step: typeof Step\n} = withInstall(Steps, {\n  Step,\n})\nexport default ElSteps\nexport const ElStep: SFCWithInstall<typeof Step> = withNoopInstall(Step)\n\nexport * from './src/item'\nexport * from './src/steps'\n"], "mappings": ";;;;;AAGY,MAACA,OAAO,GAAGC,WAAW,CAACC,KAAK,EAAE;EACxCC;AACF,CAAC;AAEW,MAACC,MAAM,GAAGC,eAAe,CAACF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}