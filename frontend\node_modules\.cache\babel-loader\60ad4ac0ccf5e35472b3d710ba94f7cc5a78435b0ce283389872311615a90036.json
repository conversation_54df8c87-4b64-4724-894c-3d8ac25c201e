{"ast": null, "code": "import { computed } from 'vue';\nimport { TinyColor } from '@ctrl/tinycolor';\nfunction useMenuColor(props) {\n  const menuBarColor = computed(() => {\n    const color = props.backgroundColor;\n    return color ? new TinyColor(color).shade(20).toString() : \"\";\n  });\n  return menuBarColor;\n}\nexport { useMenuColor as default };", "map": {"version": 3, "names": ["useMenuColor", "props", "menuBarColor", "computed", "color", "backgroundColor", "TinyColor", "shade", "toString"], "sources": ["../../../../../../packages/components/menu/src/use-menu-color.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { TinyColor } from '@ctrl/tinycolor'\n\nimport type { MenuProps } from './menu'\n\nexport default function useMenuColor(props: MenuProps) {\n  const menuBarColor = computed(() => {\n    const color = props.backgroundColor\n    return color ? new TinyColor(color).shade(20).toString() : ''\n  })\n  return menuBarColor\n}\n"], "mappings": ";;AAEe,SAASA,YAAYA,CAACC,KAAK,EAAE;EAC1C,MAAMC,YAAY,GAAGC,QAAQ,CAAC,MAAM;IAClC,MAAMC,KAAK,GAAGH,KAAK,CAACI,eAAe;IACnC,OAAOD,KAAK,GAAG,IAAIE,SAAS,CAACF,KAAK,CAAC,CAACG,KAAK,CAAC,EAAE,CAAC,CAACC,QAAQ,EAAE,GAAG,EAAE;EACjE,CAAG,CAAC;EACF,OAAON,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}