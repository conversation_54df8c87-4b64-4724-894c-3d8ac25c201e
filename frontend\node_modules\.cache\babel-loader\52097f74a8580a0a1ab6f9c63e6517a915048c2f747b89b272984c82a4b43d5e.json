{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst linkProps = buildProps({\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"warning\", \"info\", \"danger\", \"default\"],\n    default: \"default\"\n  },\n  underline: {\n    type: [Boolean, String],\n    values: [true, false, \"always\", \"never\", \"hover\"],\n    default: \"hover\"\n  },\n  disabled: Boolean,\n  href: {\n    type: String,\n    default: \"\"\n  },\n  target: {\n    type: String,\n    default: \"_self\"\n  },\n  icon: {\n    type: iconPropType\n  }\n});\nconst linkEmits = {\n  click: evt => evt instanceof MouseEvent\n};\nexport { linkEmits, linkProps };", "map": {"version": 3, "names": ["linkProps", "buildProps", "type", "String", "values", "default", "underline", "Boolean", "disabled", "href", "target", "icon", "iconPropType", "linkEmits", "click", "evt", "MouseEvent"], "sources": ["../../../../../../packages/components/link/src/link.ts"], "sourcesContent": ["import { buildProps, iconPropType } from '@element-plus/utils'\nimport type { ExtractPropTypes, PropType } from 'vue'\nimport type Link from './link.vue'\n\nexport const linkProps = buildProps({\n  /**\n   * @description type\n   */\n  type: {\n    type: String,\n    values: ['primary', 'success', 'warning', 'info', 'danger', 'default'],\n    default: 'default',\n  },\n  /**\n   * @description when underlines should appear\n   */\n  underline: {\n    type: [Boolean, String],\n    values: [true, false, 'always', 'never', 'hover'],\n    default: 'hover',\n  },\n  /**\n   * @description whether the component is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description same as native hyperlink's `href`\n   */\n  href: { type: String, default: '' },\n  /**\n   * @description same as native hyperlink's `target`\n   */\n  target: {\n    type: String as PropType<'_blank' | '_parent' | '_self' | '_top' | string>,\n    default: '_self',\n  },\n  /**\n   * @description icon component\n   */\n  icon: {\n    type: iconPropType,\n  },\n} as const)\nexport type LinkProps = ExtractPropTypes<typeof linkProps>\n\nexport const linkEmits = {\n  click: (evt: MouseEvent) => evt instanceof MouseEvent,\n}\nexport type LinkEmits = typeof linkEmits\n\nexport type LinkInstance = InstanceType<typeof Link> & unknown\n"], "mappings": ";;AACY,MAACA,SAAS,GAAGC,UAAU,CAAC;EAClCC,IAAI,EAAE;IACJA,IAAI,EAAEC,MAAM;IACZC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC;IACtEC,OAAO,EAAE;EACb,CAAG;EACDC,SAAS,EAAE;IACTJ,IAAI,EAAE,CAACK,OAAO,EAAEJ,MAAM,CAAC;IACvBC,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;IACjDC,OAAO,EAAE;EACb,CAAG;EACDG,QAAQ,EAAED,OAAO;EACjBE,IAAI,EAAE;IAAEP,IAAI,EAAEC,MAAM;IAAEE,OAAO,EAAE;EAAE,CAAE;EACnCK,MAAM,EAAE;IACNR,IAAI,EAAEC,MAAM;IACZE,OAAO,EAAE;EACb,CAAG;EACDM,IAAI,EAAE;IACJT,IAAI,EAAEU;EACV;AACA,CAAC;AACW,MAACC,SAAS,GAAG;EACvBC,KAAK,EAAGC,GAAG,IAAKA,GAAG,YAAYC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}