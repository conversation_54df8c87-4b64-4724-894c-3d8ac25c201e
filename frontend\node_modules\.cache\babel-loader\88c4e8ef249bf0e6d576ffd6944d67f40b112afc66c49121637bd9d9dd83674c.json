{"ast": null, "code": "const checkboxGroupContextKey = Symbol(\"checkboxGroupContextKey\");\nexport { checkboxGroupContextKey };", "map": {"version": 3, "names": ["checkboxGroupContextKey", "Symbol"], "sources": ["../../../../../../packages/components/checkbox/src/constants.ts"], "sourcesContent": ["import type { InjectionKey, ToRefs, WritableComputedRef } from 'vue'\nimport type { CheckboxGroupProps } from './checkbox-group'\n\ntype CheckboxGroupContext = {\n  modelValue?: WritableComputedRef<any>\n  changeEvent?: (...args: any) => any\n} & ToRefs<\n  Pick<\n    CheckboxGroupProps,\n    'size' | 'min' | 'max' | 'disabled' | 'validateEvent' | 'fill' | 'textColor'\n  >\n>\n\nexport const checkboxGroupContextKey: InjectionKey<CheckboxGroupContext> =\n  Symbol('checkboxGroupContextKey')\n"], "mappings": "AAAY,MAACA,uBAAuB,GAAGC,MAAM,CAAC,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}