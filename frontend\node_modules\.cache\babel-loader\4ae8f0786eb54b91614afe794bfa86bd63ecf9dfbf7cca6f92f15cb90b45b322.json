{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, getCurrentInstance, ref, computed, unref, onMounted, onUpdated, onActivated, resolveDynamicComponent, h, Fragment, nextTick } from 'vue';\nimport { useEventListener, isClient } from '@vueuse/core';\nimport { useCache } from '../hooks/use-cache.mjs';\nimport useWheel from '../hooks/use-wheel.mjs';\nimport ScrollBar from '../components/scrollbar.mjs';\nimport { isHorizontal, getRTLOffsetType, getScrollDir } from '../utils.mjs';\nimport { virtualizedListProps } from '../props.mjs';\nimport { ITEM_RENDER_EVT, SCROLL_EVT, HORIZONTAL, RTL, RTL_OFFSET_POS_ASC, RTL_OFFSET_NAG, BACKWARD, FORWARD, AUTO_ALIGNMENT, RTL_OFFSET_POS_DESC } from '../defaults.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isNumber } from '../../../../utils/types.mjs';\nimport { isString, hasOwn } from '@vue/shared';\nconst createList = ({\n  name,\n  getOffset,\n  getItemSize,\n  getItemOffset,\n  getEstimatedTotalSize,\n  getStartIndexForOffset,\n  getStopIndexForStartIndex,\n  initCache,\n  clearCache,\n  validateProps\n}) => {\n  return defineComponent({\n    name: name != null ? name : \"ElVirtualList\",\n    props: virtualizedListProps,\n    emits: [ITEM_RENDER_EVT, SCROLL_EVT],\n    setup(props, {\n      emit,\n      expose\n    }) {\n      validateProps(props);\n      const instance = getCurrentInstance();\n      const ns = useNamespace(\"vl\");\n      const dynamicSizeCache = ref(initCache(props, instance));\n      const getItemStyleCache = useCache();\n      const windowRef = ref();\n      const innerRef = ref();\n      const scrollbarRef = ref();\n      const states = ref({\n        isScrolling: false,\n        scrollDir: \"forward\",\n        scrollOffset: isNumber(props.initScrollOffset) ? props.initScrollOffset : 0,\n        updateRequested: false,\n        isScrollbarDragging: false,\n        scrollbarAlwaysOn: props.scrollbarAlwaysOn\n      });\n      const itemsToRender = computed(() => {\n        const {\n          total,\n          cache\n        } = props;\n        const {\n          isScrolling,\n          scrollDir,\n          scrollOffset\n        } = unref(states);\n        if (total === 0) {\n          return [0, 0, 0, 0];\n        }\n        const startIndex = getStartIndexForOffset(props, scrollOffset, unref(dynamicSizeCache));\n        const stopIndex = getStopIndexForStartIndex(props, startIndex, scrollOffset, unref(dynamicSizeCache));\n        const cacheBackward = !isScrolling || scrollDir === BACKWARD ? Math.max(1, cache) : 1;\n        const cacheForward = !isScrolling || scrollDir === FORWARD ? Math.max(1, cache) : 1;\n        return [Math.max(0, startIndex - cacheBackward), Math.max(0, Math.min(total - 1, stopIndex + cacheForward)), startIndex, stopIndex];\n      });\n      const estimatedTotalSize = computed(() => getEstimatedTotalSize(props, unref(dynamicSizeCache)));\n      const _isHorizontal = computed(() => isHorizontal(props.layout));\n      const windowStyle = computed(() => [{\n        position: \"relative\",\n        [`overflow-${_isHorizontal.value ? \"x\" : \"y\"}`]: \"scroll\",\n        WebkitOverflowScrolling: \"touch\",\n        willChange: \"transform\"\n      }, {\n        direction: props.direction,\n        height: isNumber(props.height) ? `${props.height}px` : props.height,\n        width: isNumber(props.width) ? `${props.width}px` : props.width\n      }, props.style]);\n      const innerStyle = computed(() => {\n        const size = unref(estimatedTotalSize);\n        const horizontal = unref(_isHorizontal);\n        return {\n          height: horizontal ? \"100%\" : `${size}px`,\n          pointerEvents: unref(states).isScrolling ? \"none\" : void 0,\n          width: horizontal ? `${size}px` : \"100%\"\n        };\n      });\n      const clientSize = computed(() => _isHorizontal.value ? props.width : props.height);\n      const {\n        onWheel\n      } = useWheel({\n        atStartEdge: computed(() => states.value.scrollOffset <= 0),\n        atEndEdge: computed(() => states.value.scrollOffset >= estimatedTotalSize.value),\n        layout: computed(() => props.layout)\n      }, offset => {\n        var _a, _b;\n        (_b = (_a = scrollbarRef.value).onMouseUp) == null ? void 0 : _b.call(_a);\n        scrollTo(Math.min(states.value.scrollOffset + offset, estimatedTotalSize.value - clientSize.value));\n      });\n      useEventListener(windowRef, \"wheel\", onWheel, {\n        passive: false\n      });\n      const emitEvents = () => {\n        const {\n          total\n        } = props;\n        if (total > 0) {\n          const [cacheStart, cacheEnd, visibleStart, visibleEnd] = unref(itemsToRender);\n          emit(ITEM_RENDER_EVT, cacheStart, cacheEnd, visibleStart, visibleEnd);\n        }\n        const {\n          scrollDir,\n          scrollOffset,\n          updateRequested\n        } = unref(states);\n        emit(SCROLL_EVT, scrollDir, scrollOffset, updateRequested);\n      };\n      const scrollVertically = e => {\n        const {\n          clientHeight,\n          scrollHeight,\n          scrollTop\n        } = e.currentTarget;\n        const _states = unref(states);\n        if (_states.scrollOffset === scrollTop) {\n          return;\n        }\n        const scrollOffset = Math.max(0, Math.min(scrollTop, scrollHeight - clientHeight));\n        states.value = {\n          ..._states,\n          isScrolling: true,\n          scrollDir: getScrollDir(_states.scrollOffset, scrollOffset),\n          scrollOffset,\n          updateRequested: false\n        };\n        nextTick(resetIsScrolling);\n      };\n      const scrollHorizontally = e => {\n        const {\n          clientWidth,\n          scrollLeft,\n          scrollWidth\n        } = e.currentTarget;\n        const _states = unref(states);\n        if (_states.scrollOffset === scrollLeft) {\n          return;\n        }\n        const {\n          direction\n        } = props;\n        let scrollOffset = scrollLeft;\n        if (direction === RTL) {\n          switch (getRTLOffsetType()) {\n            case RTL_OFFSET_NAG:\n              {\n                scrollOffset = -scrollLeft;\n                break;\n              }\n            case RTL_OFFSET_POS_DESC:\n              {\n                scrollOffset = scrollWidth - clientWidth - scrollLeft;\n                break;\n              }\n          }\n        }\n        scrollOffset = Math.max(0, Math.min(scrollOffset, scrollWidth - clientWidth));\n        states.value = {\n          ..._states,\n          isScrolling: true,\n          scrollDir: getScrollDir(_states.scrollOffset, scrollOffset),\n          scrollOffset,\n          updateRequested: false\n        };\n        nextTick(resetIsScrolling);\n      };\n      const onScroll = e => {\n        unref(_isHorizontal) ? scrollHorizontally(e) : scrollVertically(e);\n        emitEvents();\n      };\n      const onScrollbarScroll = (distanceToGo, totalSteps) => {\n        const offset = (estimatedTotalSize.value - clientSize.value) / totalSteps * distanceToGo;\n        scrollTo(Math.min(estimatedTotalSize.value - clientSize.value, offset));\n      };\n      const scrollTo = offset => {\n        offset = Math.max(offset, 0);\n        if (offset === unref(states).scrollOffset) {\n          return;\n        }\n        states.value = {\n          ...unref(states),\n          scrollOffset: offset,\n          scrollDir: getScrollDir(unref(states).scrollOffset, offset),\n          updateRequested: true\n        };\n        nextTick(resetIsScrolling);\n      };\n      const scrollToItem = (idx, alignment = AUTO_ALIGNMENT) => {\n        const {\n          scrollOffset\n        } = unref(states);\n        idx = Math.max(0, Math.min(idx, props.total - 1));\n        scrollTo(getOffset(props, idx, alignment, scrollOffset, unref(dynamicSizeCache)));\n      };\n      const getItemStyle = idx => {\n        const {\n          direction,\n          itemSize,\n          layout\n        } = props;\n        const itemStyleCache = getItemStyleCache.value(clearCache && itemSize, clearCache && layout, clearCache && direction);\n        let style;\n        if (hasOwn(itemStyleCache, String(idx))) {\n          style = itemStyleCache[idx];\n        } else {\n          const offset = getItemOffset(props, idx, unref(dynamicSizeCache));\n          const size = getItemSize(props, idx, unref(dynamicSizeCache));\n          const horizontal = unref(_isHorizontal);\n          const isRtl = direction === RTL;\n          const offsetHorizontal = horizontal ? offset : 0;\n          itemStyleCache[idx] = style = {\n            position: \"absolute\",\n            left: isRtl ? void 0 : `${offsetHorizontal}px`,\n            right: isRtl ? `${offsetHorizontal}px` : void 0,\n            top: !horizontal ? `${offset}px` : 0,\n            height: !horizontal ? `${size}px` : \"100%\",\n            width: horizontal ? `${size}px` : \"100%\"\n          };\n        }\n        return style;\n      };\n      const resetIsScrolling = () => {\n        states.value.isScrolling = false;\n        nextTick(() => {\n          getItemStyleCache.value(-1, null, null);\n        });\n      };\n      const resetScrollTop = () => {\n        const window = windowRef.value;\n        if (window) {\n          window.scrollTop = 0;\n        }\n      };\n      onMounted(() => {\n        if (!isClient) return;\n        const {\n          initScrollOffset\n        } = props;\n        const windowElement = unref(windowRef);\n        if (isNumber(initScrollOffset) && windowElement) {\n          if (unref(_isHorizontal)) {\n            windowElement.scrollLeft = initScrollOffset;\n          } else {\n            windowElement.scrollTop = initScrollOffset;\n          }\n        }\n        emitEvents();\n      });\n      onUpdated(() => {\n        const {\n          direction,\n          layout\n        } = props;\n        const {\n          scrollOffset,\n          updateRequested\n        } = unref(states);\n        const windowElement = unref(windowRef);\n        if (updateRequested && windowElement) {\n          if (layout === HORIZONTAL) {\n            if (direction === RTL) {\n              switch (getRTLOffsetType()) {\n                case RTL_OFFSET_NAG:\n                  {\n                    windowElement.scrollLeft = -scrollOffset;\n                    break;\n                  }\n                case RTL_OFFSET_POS_ASC:\n                  {\n                    windowElement.scrollLeft = scrollOffset;\n                    break;\n                  }\n                default:\n                  {\n                    const {\n                      clientWidth,\n                      scrollWidth\n                    } = windowElement;\n                    windowElement.scrollLeft = scrollWidth - clientWidth - scrollOffset;\n                    break;\n                  }\n              }\n            } else {\n              windowElement.scrollLeft = scrollOffset;\n            }\n          } else {\n            windowElement.scrollTop = scrollOffset;\n          }\n        }\n      });\n      onActivated(() => {\n        unref(windowRef).scrollTop = unref(states).scrollOffset;\n      });\n      const api = {\n        ns,\n        clientSize,\n        estimatedTotalSize,\n        windowStyle,\n        windowRef,\n        innerRef,\n        innerStyle,\n        itemsToRender,\n        scrollbarRef,\n        states,\n        getItemStyle,\n        onScroll,\n        onScrollbarScroll,\n        onWheel,\n        scrollTo,\n        scrollToItem,\n        resetScrollTop\n      };\n      expose({\n        windowRef,\n        innerRef,\n        getItemStyleCache,\n        scrollTo,\n        scrollToItem,\n        resetScrollTop,\n        states\n      });\n      return api;\n    },\n    render(ctx) {\n      var _a;\n      const {\n        $slots,\n        className,\n        clientSize,\n        containerElement,\n        data,\n        getItemStyle,\n        innerElement,\n        itemsToRender,\n        innerStyle,\n        layout,\n        total,\n        onScroll,\n        onScrollbarScroll,\n        states,\n        useIsScrolling,\n        windowStyle,\n        ns\n      } = ctx;\n      const [start, end] = itemsToRender;\n      const Container = resolveDynamicComponent(containerElement);\n      const Inner = resolveDynamicComponent(innerElement);\n      const children = [];\n      if (total > 0) {\n        for (let i = start; i <= end; i++) {\n          children.push(h(Fragment, {\n            key: i\n          }, (_a = $slots.default) == null ? void 0 : _a.call($slots, {\n            data,\n            index: i,\n            isScrolling: useIsScrolling ? states.isScrolling : void 0,\n            style: getItemStyle(i)\n          })));\n        }\n      }\n      const InnerNode = [h(Inner, {\n        style: innerStyle,\n        ref: \"innerRef\"\n      }, !isString(Inner) ? {\n        default: () => children\n      } : children)];\n      const scrollbar = h(ScrollBar, {\n        ref: \"scrollbarRef\",\n        clientSize,\n        layout,\n        onScroll: onScrollbarScroll,\n        ratio: clientSize * 100 / this.estimatedTotalSize,\n        scrollFrom: states.scrollOffset / (this.estimatedTotalSize - clientSize),\n        total\n      });\n      const listContainer = h(Container, {\n        class: [ns.e(\"window\"), className],\n        style: windowStyle,\n        onScroll,\n        ref: \"windowRef\",\n        key: 0\n      }, !isString(Container) ? {\n        default: () => [InnerNode]\n      } : [InnerNode]);\n      return h(\"div\", {\n        key: 0,\n        class: [ns.e(\"wrapper\"), states.scrollbarAlwaysOn ? \"always-on\" : \"\"]\n      }, [listContainer, scrollbar]);\n    }\n  });\n};\nexport { createList as default };", "map": {"version": 3, "names": ["createList", "name", "getOffset", "getItemSize", "getItemOffset", "getEstimatedTotalSize", "getStartIndexForOffset", "getStopIndexForStartIndex", "initCache", "clearCache", "validateProps", "defineComponent", "props", "virtualizedListProps", "emits", "ITEM_RENDER_EVT", "SCROLL_EVT", "setup", "emit", "expose", "instance", "getCurrentInstance", "ns", "useNamespace", "dynamicSizeCache", "ref", "getItemStyleCache", "useCache", "windowRef", "innerRef", "scrollbarRef", "states", "isScrolling", "scrollDir", "scrollOffset", "isNumber", "initScrollOffset", "updateRequested", "isScrollbarDragging", "scrollbarAlwaysOn", "itemsToRender", "computed", "total", "cache", "unref", "startIndex", "stopIndex", "cacheBackward", "BACKWARD", "Math", "max", "cacheForward", "FORWARD", "min", "estimatedTotalSize", "_isHorizontal", "isHorizontal", "layout", "windowStyle", "position", "value", "WebkitOverflowScrolling", "<PERSON><PERSON><PERSON><PERSON>", "direction", "height", "width", "style", "innerStyle", "size", "horizontal", "pointerEvents", "clientSize", "onWheel", "useWheel", "atStartEdge", "atEndEdge", "offset", "_a", "_b", "onMouseUp", "call", "scrollTo", "useEventListener", "passive", "emitEvents", "cacheStart", "cacheEnd", "visibleStart", "visibleEnd", "scrollVertically", "e", "clientHeight", "scrollHeight", "scrollTop", "currentTarget", "_states", "getScrollDir", "nextTick", "resetIsScrolling", "scrollHorizontally", "clientWidth", "scrollLeft", "scrollWidth", "RTL", "getRTLOffsetType", "RTL_OFFSET_NAG", "RTL_OFFSET_POS_DESC", "onScroll", "onScrollbarScroll", "distanceToGo", "totalSteps", "scrollToItem", "idx", "alignment", "AUTO_ALIGNMENT", "getItemStyle", "itemSize", "itemStyleCache", "hasOwn", "String", "isRtl", "offsetHorizontal", "left", "right", "top", "resetScrollTop", "window", "onMounted", "isClient", "windowElement", "onUpdated", "HORIZONTAL", "RTL_OFFSET_POS_ASC", "onActivated", "api", "render", "ctx", "$slots", "className", "containerElement", "data", "innerElement", "useIsScrolling", "start", "end", "Container", "resolveDynamicComponent", "Inner", "children", "i", "push", "h", "Fragment", "key", "default", "index", "InnerNode", "isString", "scrollbar", "<PERSON><PERSON>Bar", "ratio", "scrollFrom", "listContainer", "class"], "sources": ["../../../../../../../packages/components/virtual-list/src/builders/build-list.ts"], "sourcesContent": ["import {\n  Fragment,\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  h,\n  nextTick,\n  onActivated,\n  onMounted,\n  onUpdated,\n  ref,\n  resolveDynamicComponent,\n  unref,\n} from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { hasOwn, isClient, isNumber, isString } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useCache } from '../hooks/use-cache'\nimport useWheel from '../hooks/use-wheel'\nimport Scrollbar from '../components/scrollbar'\nimport { getRTLOffsetType, getScrollDir, isHorizontal } from '../utils'\nimport { virtualizedListProps } from '../props'\nimport {\n  AUTO_ALIGNMENT,\n  BACKWARD,\n  FORWARD,\n  HORIZONTAL,\n  ITEM_RENDER_EVT,\n  RTL,\n  RTL_OFFSET_NAG,\n  RTL_OFFSET_POS_ASC,\n  RTL_OFFSET_POS_DESC,\n  SCROLL_EVT,\n} from '../defaults'\n\nimport type { CSSProperties, Slot, VNode, VNodeChild } from 'vue'\nimport type { Alignment, ListConstructorProps } from '../types'\nimport type { VirtualizedListProps } from '../props'\n\nconst createList = ({\n  name,\n  getOffset,\n  getItemSize,\n  getItemOffset,\n  getEstimatedTotalSize,\n  getStartIndexForOffset,\n  getStopIndexForStartIndex,\n  initCache,\n  clearCache,\n  validateProps,\n}: ListConstructorProps<VirtualizedListProps>) => {\n  return defineComponent({\n    name: name ?? 'ElVirtualList',\n    props: virtualizedListProps,\n    emits: [ITEM_RENDER_EVT, SCROLL_EVT],\n    setup(props, { emit, expose }) {\n      validateProps(props)\n      const instance = getCurrentInstance()!\n\n      const ns = useNamespace('vl')\n\n      const dynamicSizeCache = ref(initCache(props, instance))\n\n      const getItemStyleCache = useCache<CSSProperties>()\n      // refs\n      // here windowRef and innerRef can be type of HTMLElement\n      // or user defined component type, depends on the type passed\n      // by user\n      const windowRef = ref<HTMLElement>()\n      const innerRef = ref<HTMLElement>()\n      const scrollbarRef = ref()\n      const states = ref({\n        isScrolling: false,\n        scrollDir: 'forward',\n        scrollOffset: isNumber(props.initScrollOffset)\n          ? props.initScrollOffset\n          : 0,\n        updateRequested: false,\n        isScrollbarDragging: false,\n        scrollbarAlwaysOn: props.scrollbarAlwaysOn,\n      })\n\n      // computed\n      const itemsToRender = computed(() => {\n        const { total, cache } = props\n        const { isScrolling, scrollDir, scrollOffset } = unref(states)\n\n        if (total === 0) {\n          return [0, 0, 0, 0]\n        }\n\n        const startIndex = getStartIndexForOffset(\n          props,\n          scrollOffset,\n          unref(dynamicSizeCache)\n        )\n        const stopIndex = getStopIndexForStartIndex(\n          props,\n          startIndex,\n          scrollOffset,\n          unref(dynamicSizeCache)\n        )\n\n        const cacheBackward =\n          !isScrolling || scrollDir === BACKWARD ? Math.max(1, cache) : 1\n        const cacheForward =\n          !isScrolling || scrollDir === FORWARD ? Math.max(1, cache) : 1\n\n        return [\n          Math.max(0, startIndex - cacheBackward),\n          Math.max(0, Math.min(total! - 1, stopIndex + cacheForward)),\n          startIndex,\n          stopIndex,\n        ]\n      })\n\n      const estimatedTotalSize = computed(() =>\n        getEstimatedTotalSize(props, unref(dynamicSizeCache))\n      )\n\n      const _isHorizontal = computed(() => isHorizontal(props.layout))\n\n      const windowStyle = computed(() => [\n        {\n          position: 'relative',\n          [`overflow-${_isHorizontal.value ? 'x' : 'y'}`]: 'scroll',\n          WebkitOverflowScrolling: 'touch',\n          willChange: 'transform',\n        },\n        {\n          direction: props.direction,\n          height: isNumber(props.height) ? `${props.height}px` : props.height,\n          width: isNumber(props.width) ? `${props.width}px` : props.width,\n        },\n        props.style,\n      ])\n\n      const innerStyle = computed(() => {\n        const size = unref(estimatedTotalSize)\n        const horizontal = unref(_isHorizontal)\n        return {\n          height: horizontal ? '100%' : `${size}px`,\n          pointerEvents: unref(states).isScrolling ? 'none' : undefined,\n          width: horizontal ? `${size}px` : '100%',\n        }\n      })\n\n      const clientSize = computed(() =>\n        _isHorizontal.value ? props.width : props.height\n      )\n\n      // methods\n      const { onWheel } = useWheel(\n        {\n          atStartEdge: computed(() => states.value.scrollOffset <= 0),\n          atEndEdge: computed(\n            () => states.value.scrollOffset >= estimatedTotalSize.value\n          ),\n          layout: computed(() => props.layout),\n        },\n        (offset) => {\n          ;(\n            scrollbarRef.value as {\n              onMouseUp: () => void\n            }\n          ).onMouseUp?.()\n          scrollTo(\n            Math.min(\n              states.value.scrollOffset + offset,\n              estimatedTotalSize.value - (clientSize.value as number)\n            )\n          )\n        }\n      )\n\n      useEventListener(windowRef, 'wheel', onWheel, {\n        passive: false,\n      })\n\n      const emitEvents = () => {\n        const { total } = props\n\n        if (total! > 0) {\n          const [cacheStart, cacheEnd, visibleStart, visibleEnd] =\n            unref(itemsToRender)\n          emit(ITEM_RENDER_EVT, cacheStart, cacheEnd, visibleStart, visibleEnd)\n        }\n\n        const { scrollDir, scrollOffset, updateRequested } = unref(states)\n        emit(SCROLL_EVT, scrollDir, scrollOffset, updateRequested)\n      }\n\n      const scrollVertically = (e: Event) => {\n        const { clientHeight, scrollHeight, scrollTop } =\n          e.currentTarget as HTMLElement\n        const _states = unref(states)\n        if (_states.scrollOffset === scrollTop) {\n          return\n        }\n\n        const scrollOffset = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        )\n\n        states.value = {\n          ..._states,\n          isScrolling: true,\n          scrollDir: getScrollDir(_states.scrollOffset, scrollOffset),\n          scrollOffset,\n          updateRequested: false,\n        }\n\n        nextTick(resetIsScrolling)\n      }\n\n      const scrollHorizontally = (e: Event) => {\n        const { clientWidth, scrollLeft, scrollWidth } =\n          e.currentTarget as HTMLElement\n        const _states = unref(states)\n\n        if (_states.scrollOffset === scrollLeft) {\n          return\n        }\n\n        const { direction } = props\n\n        let scrollOffset = scrollLeft\n\n        if (direction === RTL) {\n          // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n          // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n          // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n          // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n          switch (getRTLOffsetType()) {\n            case RTL_OFFSET_NAG: {\n              scrollOffset = -scrollLeft\n              break\n            }\n            case RTL_OFFSET_POS_DESC: {\n              scrollOffset = scrollWidth - clientWidth - scrollLeft\n              break\n            }\n          }\n        }\n\n        scrollOffset = Math.max(\n          0,\n          Math.min(scrollOffset, scrollWidth - clientWidth)\n        )\n\n        states.value = {\n          ..._states,\n          isScrolling: true,\n          scrollDir: getScrollDir(_states.scrollOffset, scrollOffset),\n          scrollOffset,\n          updateRequested: false,\n        }\n\n        nextTick(resetIsScrolling)\n      }\n\n      const onScroll = (e: Event) => {\n        unref(_isHorizontal) ? scrollHorizontally(e) : scrollVertically(e)\n        emitEvents()\n      }\n\n      const onScrollbarScroll = (distanceToGo: number, totalSteps: number) => {\n        const offset =\n          ((estimatedTotalSize.value - (clientSize.value as number)) /\n            totalSteps) *\n          distanceToGo\n        scrollTo(\n          Math.min(\n            estimatedTotalSize.value - (clientSize.value as number),\n            offset\n          )\n        )\n      }\n\n      const scrollTo = (offset: number) => {\n        offset = Math.max(offset, 0)\n\n        if (offset === unref(states).scrollOffset) {\n          return\n        }\n\n        states.value = {\n          ...unref(states),\n          scrollOffset: offset,\n          scrollDir: getScrollDir(unref(states).scrollOffset, offset),\n          updateRequested: true,\n        }\n\n        nextTick(resetIsScrolling)\n      }\n\n      const scrollToItem = (\n        idx: number,\n        alignment: Alignment = AUTO_ALIGNMENT\n      ) => {\n        const { scrollOffset } = unref(states)\n\n        idx = Math.max(0, Math.min(idx, props.total! - 1))\n        scrollTo(\n          getOffset(\n            props,\n            idx,\n            alignment,\n            scrollOffset,\n            unref(dynamicSizeCache)\n          )\n        )\n      }\n\n      const getItemStyle = (idx: number) => {\n        const { direction, itemSize, layout } = props\n\n        const itemStyleCache = getItemStyleCache.value(\n          clearCache && itemSize,\n          clearCache && layout,\n          clearCache && direction\n        )\n\n        let style: CSSProperties\n        if (hasOwn(itemStyleCache, String(idx))) {\n          style = itemStyleCache[idx]\n        } else {\n          const offset = getItemOffset(props, idx, unref(dynamicSizeCache))\n          const size = getItemSize(props, idx, unref(dynamicSizeCache))\n          const horizontal = unref(_isHorizontal)\n\n          const isRtl = direction === RTL\n          const offsetHorizontal = horizontal ? offset : 0\n          itemStyleCache[idx] = style = {\n            position: 'absolute',\n            left: isRtl ? undefined : `${offsetHorizontal}px`,\n            right: isRtl ? `${offsetHorizontal}px` : undefined,\n            top: !horizontal ? `${offset}px` : 0,\n            height: !horizontal ? `${size}px` : '100%',\n            width: horizontal ? `${size}px` : '100%',\n          }\n        }\n\n        return style\n      }\n\n      // TODO: perf optimization here, reset isScrolling with debounce.\n\n      const resetIsScrolling = () => {\n        states.value.isScrolling = false\n        nextTick(() => {\n          getItemStyleCache.value(-1, null, null)\n        })\n      }\n\n      const resetScrollTop = () => {\n        const window = windowRef.value\n        if (window) {\n          window.scrollTop = 0\n        }\n      }\n\n      // life cycles\n      onMounted(() => {\n        if (!isClient) return\n        const { initScrollOffset } = props\n        const windowElement = unref(windowRef)\n        if (isNumber(initScrollOffset) && windowElement) {\n          if (unref(_isHorizontal)) {\n            windowElement.scrollLeft = initScrollOffset\n          } else {\n            windowElement.scrollTop = initScrollOffset\n          }\n        }\n\n        emitEvents()\n      })\n\n      onUpdated(() => {\n        const { direction, layout } = props\n        const { scrollOffset, updateRequested } = unref(states)\n        const windowElement = unref(windowRef)\n\n        if (updateRequested && windowElement) {\n          if (layout === HORIZONTAL) {\n            if (direction === RTL) {\n              // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n              // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n              // So we need to determine which browser behavior we're dealing with, and mimic it.\n              switch (getRTLOffsetType()) {\n                case RTL_OFFSET_NAG: {\n                  windowElement.scrollLeft = -scrollOffset\n                  break\n                }\n                case RTL_OFFSET_POS_ASC: {\n                  windowElement.scrollLeft = scrollOffset\n                  break\n                }\n                default: {\n                  const { clientWidth, scrollWidth } = windowElement\n                  windowElement.scrollLeft =\n                    scrollWidth - clientWidth - scrollOffset\n                  break\n                }\n              }\n            } else {\n              windowElement.scrollLeft = scrollOffset\n            }\n          } else {\n            windowElement.scrollTop = scrollOffset\n          }\n        }\n      })\n\n      onActivated(() => {\n        unref(windowRef)!.scrollTop = unref(states).scrollOffset\n      })\n\n      const api = {\n        ns,\n        clientSize,\n        estimatedTotalSize,\n        windowStyle,\n        windowRef,\n        innerRef,\n        innerStyle,\n        itemsToRender,\n        scrollbarRef,\n        states,\n        getItemStyle,\n        onScroll,\n        onScrollbarScroll,\n        onWheel,\n        scrollTo,\n        scrollToItem,\n        resetScrollTop,\n      }\n\n      expose({\n        windowRef,\n        innerRef,\n        getItemStyleCache,\n        scrollTo,\n        scrollToItem,\n        resetScrollTop,\n        states,\n      })\n\n      return api\n    },\n\n    render(ctx: any) {\n      const {\n        $slots,\n        className,\n        clientSize,\n        containerElement,\n        data,\n        getItemStyle,\n        innerElement,\n        itemsToRender,\n        innerStyle,\n        layout,\n        total,\n        onScroll,\n        onScrollbarScroll,\n        states,\n        useIsScrolling,\n        windowStyle,\n        ns,\n      } = ctx\n\n      const [start, end] = itemsToRender\n\n      const Container = resolveDynamicComponent(containerElement)\n      const Inner = resolveDynamicComponent(innerElement)\n\n      const children = [] as VNodeChild[]\n\n      if (total > 0) {\n        for (let i = start; i <= end; i++) {\n          children.push(\n            h(\n              Fragment,\n              { key: i },\n              ($slots.default as Slot)?.({\n                data,\n                index: i,\n                isScrolling: useIsScrolling ? states.isScrolling : undefined,\n                style: getItemStyle(i),\n              })\n            )\n          )\n        }\n      }\n\n      const InnerNode = [\n        h(\n          Inner as VNode,\n          {\n            style: innerStyle,\n            ref: 'innerRef',\n          },\n          !isString(Inner)\n            ? {\n                default: () => children,\n              }\n            : children\n        ),\n      ]\n\n      const scrollbar = h(Scrollbar, {\n        ref: 'scrollbarRef',\n        clientSize,\n        layout,\n        onScroll: onScrollbarScroll,\n        ratio: (clientSize * 100) / this.estimatedTotalSize,\n        scrollFrom:\n          states.scrollOffset / (this.estimatedTotalSize - clientSize),\n        total,\n      })\n\n      const listContainer = h(\n        Container as VNode,\n        {\n          class: [ns.e('window'), className],\n          style: windowStyle,\n          onScroll,\n          ref: 'windowRef',\n          key: 0,\n        },\n        !isString(Container) ? { default: () => [InnerNode] } : [InnerNode]\n      )\n\n      return h(\n        'div',\n        {\n          key: 0,\n          class: [ns.e('wrapper'), states.scrollbarAlwaysOn ? 'always-on' : ''],\n        },\n        [listContainer, scrollbar]\n      )\n    },\n  })\n}\n\nexport default createList\n"], "mappings": ";;;;;;;;;;;;AAkCK,MAACA,UAAU,GAAGA,CAAC;EAClBC,IAAI;EACJC,SAAS;EACTC,WAAW;EACXC,aAAa;EACbC,qBAAqB;EACrBC,sBAAsB;EACtBC,yBAAyB;EACzBC,SAAS;EACTC,UAAU;EACVC;AACF,CAAC,KAAK;EACJ,OAAOC,eAAe,CAAC;IACrBV,IAAI,EAAEA,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAG,eAAe;IAC3CW,KAAK,EAAEC,oBAAoB;IAC3BC,KAAK,EAAE,CAACC,eAAe,EAAEC,UAAU,CAAC;IACpCC,KAAKA,CAACL,KAAK,EAAE;MAAEM,IAAI;MAAEC;IAAM,CAAE,EAAE;MAC7BT,aAAa,CAACE,KAAK,CAAC;MACpB,MAAMQ,QAAQ,GAAGC,kBAAkB,EAAE;MACrC,MAAMC,EAAE,GAAGC,YAAY,CAAC,IAAI,CAAC;MAC7B,MAAMC,gBAAgB,GAAGC,GAAG,CAACjB,SAAS,CAACI,KAAK,EAAEQ,QAAQ,CAAC,CAAC;MACxD,MAAMM,iBAAiB,GAAGC,QAAQ,EAAE;MACpC,MAAMC,SAAS,GAAGH,GAAG,EAAE;MACvB,MAAMI,QAAQ,GAAGJ,GAAG,EAAE;MACtB,MAAMK,YAAY,GAAGL,GAAG,EAAE;MAC1B,MAAMM,MAAM,GAAGN,GAAG,CAAC;QACjBO,WAAW,EAAE,KAAK;QAClBC,SAAS,EAAE,SAAS;QACpBC,YAAY,EAAEC,QAAQ,CAACvB,KAAK,CAACwB,gBAAgB,CAAC,GAAGxB,KAAK,CAACwB,gBAAgB,GAAG,CAAC;QAC3EC,eAAe,EAAE,KAAK;QACtBC,mBAAmB,EAAE,KAAK;QAC1BC,iBAAiB,EAAE3B,KAAK,CAAC2B;MACjC,CAAO,CAAC;MACF,MAAMC,aAAa,GAAGC,QAAQ,CAAC,MAAM;QACnC,MAAM;UAAEC,KAAK;UAAEC;QAAK,CAAE,GAAG/B,KAAK;QAC9B,MAAM;UAAEoB,WAAW;UAAEC,SAAS;UAAEC;QAAY,CAAE,GAAGU,KAAK,CAACb,MAAM,CAAC;QAC9D,IAAIW,KAAK,KAAK,CAAC,EAAE;UACf,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC7B;QACQ,MAAMG,UAAU,GAAGvC,sBAAsB,CAACM,KAAK,EAAEsB,YAAY,EAAEU,KAAK,CAACpB,gBAAgB,CAAC,CAAC;QACvF,MAAMsB,SAAS,GAAGvC,yBAAyB,CAACK,KAAK,EAAEiC,UAAU,EAAEX,YAAY,EAAEU,KAAK,CAACpB,gBAAgB,CAAC,CAAC;QACrG,MAAMuB,aAAa,GAAG,CAACf,WAAW,IAAIC,SAAS,KAAKe,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,KAAK,CAAC,GAAG,CAAC;QACrF,MAAMQ,YAAY,GAAG,CAACnB,WAAW,IAAIC,SAAS,KAAKmB,OAAO,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,KAAK,CAAC,GAAG,CAAC;QACnF,OAAO,CACLM,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,UAAU,GAAGE,aAAa,CAAC,EACvCE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACI,GAAG,CAACX,KAAK,GAAG,CAAC,EAAEI,SAAS,GAAGK,YAAY,CAAC,CAAC,EAC1DN,UAAU,EACVC,SAAS,CACV;MACT,CAAO,CAAC;MACF,MAAMQ,kBAAkB,GAAGb,QAAQ,CAAC,MAAMpC,qBAAqB,CAACO,KAAK,EAAEgC,KAAK,CAACpB,gBAAgB,CAAC,CAAC,CAAC;MAChG,MAAM+B,aAAa,GAAGd,QAAQ,CAAC,MAAMe,YAAY,CAAC5C,KAAK,CAAC6C,MAAM,CAAC,CAAC;MAChE,MAAMC,WAAW,GAAGjB,QAAQ,CAAC,MAAM,CACjC;QACEkB,QAAQ,EAAE,UAAU;QACpB,CAAC,YAAYJ,aAAa,CAACK,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,QAAQ;QACzDC,uBAAuB,EAAE,OAAO;QAChCC,UAAU,EAAE;MACtB,CAAS,EACD;QACEC,SAAS,EAAEnD,KAAK,CAACmD,SAAS;QAC1BC,MAAM,EAAE7B,QAAQ,CAACvB,KAAK,CAACoD,MAAM,CAAC,GAAG,GAAGpD,KAAK,CAACoD,MAAM,IAAI,GAAGpD,KAAK,CAACoD,MAAM;QACnEC,KAAK,EAAE9B,QAAQ,CAACvB,KAAK,CAACqD,KAAK,CAAC,GAAG,GAAGrD,KAAK,CAACqD,KAAK,IAAI,GAAGrD,KAAK,CAACqD;MACpE,CAAS,EACDrD,KAAK,CAACsD,KAAK,CACZ,CAAC;MACF,MAAMC,UAAU,GAAG1B,QAAQ,CAAC,MAAM;QAChC,MAAM2B,IAAI,GAAGxB,KAAK,CAACU,kBAAkB,CAAC;QACtC,MAAMe,UAAU,GAAGzB,KAAK,CAACW,aAAa,CAAC;QACvC,OAAO;UACLS,MAAM,EAAEK,UAAU,GAAG,MAAM,GAAG,GAAGD,IAAI,IAAI;UACzCE,aAAa,EAAE1B,KAAK,CAACb,MAAM,CAAC,CAACC,WAAW,GAAG,MAAM,GAAG,KAAK,CAAC;UAC1DiC,KAAK,EAAEI,UAAU,GAAG,GAAGD,IAAI,IAAI,GAAG;QAC5C,CAAS;MACT,CAAO,CAAC;MACF,MAAMG,UAAU,GAAG9B,QAAQ,CAAC,MAAMc,aAAa,CAACK,KAAK,GAAGhD,KAAK,CAACqD,KAAK,GAAGrD,KAAK,CAACoD,MAAM,CAAC;MACnF,MAAM;QAAEQ;MAAO,CAAE,GAAGC,QAAQ,CAAC;QAC3BC,WAAW,EAAEjC,QAAQ,CAAC,MAAMV,MAAM,CAAC6B,KAAK,CAAC1B,YAAY,IAAI,CAAC,CAAC;QAC3DyC,SAAS,EAAElC,QAAQ,CAAC,MAAMV,MAAM,CAAC6B,KAAK,CAAC1B,YAAY,IAAIoB,kBAAkB,CAACM,KAAK,CAAC;QAChFH,MAAM,EAAEhB,QAAQ,CAAC,MAAM7B,KAAK,CAAC6C,MAAM;MAC3C,CAAO,EAAGmB,MAAM,IAAK;QACb,IAAIC,EAAE,EAAEC,EAAE;QAEV,CAACA,EAAE,GAAG,CAACD,EAAE,GAAG/C,YAAY,CAAC8B,KAAK,EAAEmB,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAACH,EAAE,CAAC;QACzEI,QAAQ,CAAChC,IAAI,CAACI,GAAG,CAACtB,MAAM,CAAC6B,KAAK,CAAC1B,YAAY,GAAG0C,MAAM,EAAEtB,kBAAkB,CAACM,KAAK,GAAGW,UAAU,CAACX,KAAK,CAAC,CAAC;MAC3G,CAAO,CAAC;MACFsB,gBAAgB,CAACtD,SAAS,EAAE,OAAO,EAAE4C,OAAO,EAAE;QAC5CW,OAAO,EAAE;MACjB,CAAO,CAAC;MACF,MAAMC,UAAU,GAAGA,CAAA,KAAM;QACvB,MAAM;UAAE1C;QAAK,CAAE,GAAG9B,KAAK;QACvB,IAAI8B,KAAK,GAAG,CAAC,EAAE;UACb,MAAM,CAAC2C,UAAU,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,UAAU,CAAC,GAAG5C,KAAK,CAACJ,aAAa,CAAC;UAC7EtB,IAAI,CAACH,eAAe,EAAEsE,UAAU,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,UAAU,CAAC;QAC/E;QACQ,MAAM;UAAEvD,SAAS;UAAEC,YAAY;UAAEG;QAAe,CAAE,GAAGO,KAAK,CAACb,MAAM,CAAC;QAClEb,IAAI,CAACF,UAAU,EAAEiB,SAAS,EAAEC,YAAY,EAAEG,eAAe,CAAC;MAClE,CAAO;MACD,MAAMoD,gBAAgB,GAAIC,CAAC,IAAK;QAC9B,MAAM;UAAEC,YAAY;UAAEC,YAAY;UAAEC;QAAS,CAAE,GAAGH,CAAC,CAACI,aAAa;QACjE,MAAMC,OAAO,GAAGnD,KAAK,CAACb,MAAM,CAAC;QAC7B,IAAIgE,OAAO,CAAC7D,YAAY,KAAK2D,SAAS,EAAE;UACtC;QACV;QACQ,MAAM3D,YAAY,GAAGe,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACI,GAAG,CAACwC,SAAS,EAAED,YAAY,GAAGD,YAAY,CAAC,CAAC;QAClF5D,MAAM,CAAC6B,KAAK,GAAG;UACb,GAAGmC,OAAO;UACV/D,WAAW,EAAE,IAAI;UACjBC,SAAS,EAAE+D,YAAY,CAACD,OAAO,CAAC7D,YAAY,EAAEA,YAAY,CAAC;UAC3DA,YAAY;UACZG,eAAe,EAAE;QAC3B,CAAS;QACD4D,QAAQ,CAACC,gBAAgB,CAAC;MAClC,CAAO;MACD,MAAMC,kBAAkB,GAAIT,CAAC,IAAK;QAChC,MAAM;UAAEU,WAAW;UAAEC,UAAU;UAAEC;QAAW,CAAE,GAAGZ,CAAC,CAACI,aAAa;QAChE,MAAMC,OAAO,GAAGnD,KAAK,CAACb,MAAM,CAAC;QAC7B,IAAIgE,OAAO,CAAC7D,YAAY,KAAKmE,UAAU,EAAE;UACvC;QACV;QACQ,MAAM;UAAEtC;QAAS,CAAE,GAAGnD,KAAK;QAC3B,IAAIsB,YAAY,GAAGmE,UAAU;QAC7B,IAAItC,SAAS,KAAKwC,GAAG,EAAE;UACrB,QAAQC,gBAAgB,EAAE;YACxB,KAAKC,cAAc;cAAE;gBACnBvE,YAAY,GAAG,CAACmE,UAAU;gBAC1B;cACd;YACY,KAAKK,mBAAmB;cAAE;gBACxBxE,YAAY,GAAGoE,WAAW,GAAGF,WAAW,GAAGC,UAAU;gBACrD;cACd;UACA;QACA;QACQnE,YAAY,GAAGe,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACI,GAAG,CAACnB,YAAY,EAAEoE,WAAW,GAAGF,WAAW,CAAC,CAAC;QAC7ErE,MAAM,CAAC6B,KAAK,GAAG;UACb,GAAGmC,OAAO;UACV/D,WAAW,EAAE,IAAI;UACjBC,SAAS,EAAE+D,YAAY,CAACD,OAAO,CAAC7D,YAAY,EAAEA,YAAY,CAAC;UAC3DA,YAAY;UACZG,eAAe,EAAE;QAC3B,CAAS;QACD4D,QAAQ,CAACC,gBAAgB,CAAC;MAClC,CAAO;MACD,MAAMS,QAAQ,GAAIjB,CAAC,IAAK;QACtB9C,KAAK,CAACW,aAAa,CAAC,GAAG4C,kBAAkB,CAACT,CAAC,CAAC,GAAGD,gBAAgB,CAACC,CAAC,CAAC;QAClEN,UAAU,EAAE;MACpB,CAAO;MACD,MAAMwB,iBAAiB,GAAGA,CAACC,YAAY,EAAEC,UAAU,KAAK;QACtD,MAAMlC,MAAM,GAAG,CAACtB,kBAAkB,CAACM,KAAK,GAAGW,UAAU,CAACX,KAAK,IAAIkD,UAAU,GAAGD,YAAY;QACxF5B,QAAQ,CAAChC,IAAI,CAACI,GAAG,CAACC,kBAAkB,CAACM,KAAK,GAAGW,UAAU,CAACX,KAAK,EAAEgB,MAAM,CAAC,CAAC;MAC/E,CAAO;MACD,MAAMK,QAAQ,GAAIL,MAAM,IAAK;QAC3BA,MAAM,GAAG3B,IAAI,CAACC,GAAG,CAAC0B,MAAM,EAAE,CAAC,CAAC;QAC5B,IAAIA,MAAM,KAAKhC,KAAK,CAACb,MAAM,CAAC,CAACG,YAAY,EAAE;UACzC;QACV;QACQH,MAAM,CAAC6B,KAAK,GAAG;UACb,GAAGhB,KAAK,CAACb,MAAM,CAAC;UAChBG,YAAY,EAAE0C,MAAM;UACpB3C,SAAS,EAAE+D,YAAY,CAACpD,KAAK,CAACb,MAAM,CAAC,CAACG,YAAY,EAAE0C,MAAM,CAAC;UAC3DvC,eAAe,EAAE;QAC3B,CAAS;QACD4D,QAAQ,CAACC,gBAAgB,CAAC;MAClC,CAAO;MACD,MAAMa,YAAY,GAAGA,CAACC,GAAG,EAAEC,SAAS,GAAGC,cAAc,KAAK;QACxD,MAAM;UAAEhF;QAAY,CAAE,GAAGU,KAAK,CAACb,MAAM,CAAC;QACtCiF,GAAG,GAAG/D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACI,GAAG,CAAC2D,GAAG,EAAEpG,KAAK,CAAC8B,KAAK,GAAG,CAAC,CAAC,CAAC;QACjDuC,QAAQ,CAAC/E,SAAS,CAACU,KAAK,EAAEoG,GAAG,EAAEC,SAAS,EAAE/E,YAAY,EAAEU,KAAK,CAACpB,gBAAgB,CAAC,CAAC,CAAC;MACzF,CAAO;MACD,MAAM2F,YAAY,GAAIH,GAAG,IAAK;QAC5B,MAAM;UAAEjD,SAAS;UAAEqD,QAAQ;UAAE3D;QAAM,CAAE,GAAG7C,KAAK;QAC7C,MAAMyG,cAAc,GAAG3F,iBAAiB,CAACkC,KAAK,CAACnD,UAAU,IAAI2G,QAAQ,EAAE3G,UAAU,IAAIgD,MAAM,EAAEhD,UAAU,IAAIsD,SAAS,CAAC;QACrH,IAAIG,KAAK;QACT,IAAIoD,MAAM,CAACD,cAAc,EAAEE,MAAM,CAACP,GAAG,CAAC,CAAC,EAAE;UACvC9C,KAAK,GAAGmD,cAAc,CAACL,GAAG,CAAC;QACrC,CAAS,MAAM;UACL,MAAMpC,MAAM,GAAGxE,aAAa,CAACQ,KAAK,EAAEoG,GAAG,EAAEpE,KAAK,CAACpB,gBAAgB,CAAC,CAAC;UACjE,MAAM4C,IAAI,GAAGjE,WAAW,CAACS,KAAK,EAAEoG,GAAG,EAAEpE,KAAK,CAACpB,gBAAgB,CAAC,CAAC;UAC7D,MAAM6C,UAAU,GAAGzB,KAAK,CAACW,aAAa,CAAC;UACvC,MAAMiE,KAAK,GAAGzD,SAAS,KAAKwC,GAAG;UAC/B,MAAMkB,gBAAgB,GAAGpD,UAAU,GAAGO,MAAM,GAAG,CAAC;UAChDyC,cAAc,CAACL,GAAG,CAAC,GAAG9C,KAAK,GAAG;YAC5BP,QAAQ,EAAE,UAAU;YACpB+D,IAAI,EAAEF,KAAK,GAAG,KAAK,CAAC,GAAG,GAAGC,gBAAgB,IAAI;YAC9CE,KAAK,EAAEH,KAAK,GAAG,GAAGC,gBAAgB,IAAI,GAAG,KAAK,CAAC;YAC/CG,GAAG,EAAE,CAACvD,UAAU,GAAG,GAAGO,MAAM,IAAI,GAAG,CAAC;YACpCZ,MAAM,EAAE,CAACK,UAAU,GAAG,GAAGD,IAAI,IAAI,GAAG,MAAM;YAC1CH,KAAK,EAAEI,UAAU,GAAG,GAAGD,IAAI,IAAI,GAAG;UAC9C,CAAW;QACX;QACQ,OAAOF,KAAK;MACpB,CAAO;MACD,MAAMgC,gBAAgB,GAAGA,CAAA,KAAM;QAC7BnE,MAAM,CAAC6B,KAAK,CAAC5B,WAAW,GAAG,KAAK;QAChCiE,QAAQ,CAAC,MAAM;UACbvE,iBAAiB,CAACkC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;QACjD,CAAS,CAAC;MACV,CAAO;MACD,MAAMiE,cAAc,GAAGA,CAAA,KAAM;QAC3B,MAAMC,MAAM,GAAGlG,SAAS,CAACgC,KAAK;QAC9B,IAAIkE,MAAM,EAAE;UACVA,MAAM,CAACjC,SAAS,GAAG,CAAC;QAC9B;MACA,CAAO;MACDkC,SAAS,CAAC,MAAM;QACd,IAAI,CAACC,QAAQ,EACX;QACF,MAAM;UAAE5F;QAAgB,CAAE,GAAGxB,KAAK;QAClC,MAAMqH,aAAa,GAAGrF,KAAK,CAAChB,SAAS,CAAC;QACtC,IAAIO,QAAQ,CAACC,gBAAgB,CAAC,IAAI6F,aAAa,EAAE;UAC/C,IAAIrF,KAAK,CAACW,aAAa,CAAC,EAAE;YACxB0E,aAAa,CAAC5B,UAAU,GAAGjE,gBAAgB;UACvD,CAAW,MAAM;YACL6F,aAAa,CAACpC,SAAS,GAAGzD,gBAAgB;UACtD;QACA;QACQgD,UAAU,EAAE;MACpB,CAAO,CAAC;MACF8C,SAAS,CAAC,MAAM;QACd,MAAM;UAAEnE,SAAS;UAAEN;QAAM,CAAE,GAAG7C,KAAK;QACnC,MAAM;UAAEsB,YAAY;UAAEG;QAAe,CAAE,GAAGO,KAAK,CAACb,MAAM,CAAC;QACvD,MAAMkG,aAAa,GAAGrF,KAAK,CAAChB,SAAS,CAAC;QACtC,IAAIS,eAAe,IAAI4F,aAAa,EAAE;UACpC,IAAIxE,MAAM,KAAK0E,UAAU,EAAE;YACzB,IAAIpE,SAAS,KAAKwC,GAAG,EAAE;cACrB,QAAQC,gBAAgB,EAAE;gBACxB,KAAKC,cAAc;kBAAE;oBACnBwB,aAAa,CAAC5B,UAAU,GAAG,CAACnE,YAAY;oBACxC;kBAClB;gBACgB,KAAKkG,kBAAkB;kBAAE;oBACvBH,aAAa,CAAC5B,UAAU,GAAGnE,YAAY;oBACvC;kBAClB;gBACgB;kBAAS;oBACP,MAAM;sBAAEkE,WAAW;sBAAEE;oBAAW,CAAE,GAAG2B,aAAa;oBAClDA,aAAa,CAAC5B,UAAU,GAAGC,WAAW,GAAGF,WAAW,GAAGlE,YAAY;oBACnE;kBAClB;cACA;YACA,CAAa,MAAM;cACL+F,aAAa,CAAC5B,UAAU,GAAGnE,YAAY;YACrD;UACA,CAAW,MAAM;YACL+F,aAAa,CAACpC,SAAS,GAAG3D,YAAY;UAClD;QACA;MACA,CAAO,CAAC;MACFmG,WAAW,CAAC,MAAM;QAChBzF,KAAK,CAAChB,SAAS,CAAC,CAACiE,SAAS,GAAGjD,KAAK,CAACb,MAAM,CAAC,CAACG,YAAY;MAC/D,CAAO,CAAC;MACF,MAAMoG,GAAG,GAAG;QACVhH,EAAE;QACFiD,UAAU;QACVjB,kBAAkB;QAClBI,WAAW;QACX9B,SAAS;QACTC,QAAQ;QACRsC,UAAU;QACV3B,aAAa;QACbV,YAAY;QACZC,MAAM;QACNoF,YAAY;QACZR,QAAQ;QACRC,iBAAiB;QACjBpC,OAAO;QACPS,QAAQ;QACR8B,YAAY;QACZc;MACR,CAAO;MACD1G,MAAM,CAAC;QACLS,SAAS;QACTC,QAAQ;QACRH,iBAAiB;QACjBuD,QAAQ;QACR8B,YAAY;QACZc,cAAc;QACd9F;MACR,CAAO,CAAC;MACF,OAAOuG,GAAG;IAChB,CAAK;IACDC,MAAMA,CAACC,GAAG,EAAE;MACV,IAAI3D,EAAE;MACN,MAAM;QACJ4D,MAAM;QACNC,SAAS;QACTnE,UAAU;QACVoE,gBAAgB;QAChBC,IAAI;QACJzB,YAAY;QACZ0B,YAAY;QACZrG,aAAa;QACb2B,UAAU;QACVV,MAAM;QACNf,KAAK;QACLiE,QAAQ;QACRC,iBAAiB;QACjB7E,MAAM;QACN+G,cAAc;QACdpF,WAAW;QACXpC;MACR,CAAO,GAAGkH,GAAG;MACP,MAAM,CAACO,KAAK,EAAEC,GAAG,CAAC,GAAGxG,aAAa;MAClC,MAAMyG,SAAS,GAAGC,uBAAuB,CAACP,gBAAgB,CAAC;MAC3D,MAAMQ,KAAK,GAAGD,uBAAuB,CAACL,YAAY,CAAC;MACnD,MAAMO,QAAQ,GAAG,EAAE;MACnB,IAAI1G,KAAK,GAAG,CAAC,EAAE;QACb,KAAK,IAAI2G,CAAC,GAAGN,KAAK,EAAEM,CAAC,IAAIL,GAAG,EAAEK,CAAC,EAAE,EAAE;UACjCD,QAAQ,CAACE,IAAI,CAACC,CAAC,CAACC,QAAQ,EAAE;YAAEC,GAAG,EAAEJ;UAAC,CAAE,EAAE,CAACxE,EAAE,GAAG4D,MAAM,CAACiB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG7E,EAAE,CAACG,IAAI,CAACyD,MAAM,EAAE;YAC7FG,IAAI;YACJe,KAAK,EAAEN,CAAC;YACRrH,WAAW,EAAE8G,cAAc,GAAG/G,MAAM,CAACC,WAAW,GAAG,KAAK,CAAC;YACzDkC,KAAK,EAAEiD,YAAY,CAACkC,CAAC;UACjC,CAAW,CAAC,CAAC,CAAC;QACd;MACA;MACM,MAAMO,SAAS,GAAG,CAChBL,CAAC,CAACJ,KAAK,EAAE;QACPjF,KAAK,EAAEC,UAAU;QACjB1C,GAAG,EAAE;MACf,CAAS,EAAE,CAACoI,QAAQ,CAACV,KAAK,CAAC,GAAG;QACpBO,OAAO,EAAEA,CAAA,KAAMN;MACzB,CAAS,GAAGA,QAAQ,CAAC,CACd;MACD,MAAMU,SAAS,GAAGP,CAAC,CAACQ,SAAS,EAAE;QAC7BtI,GAAG,EAAE,cAAc;QACnB8C,UAAU;QACVd,MAAM;QACNkD,QAAQ,EAAEC,iBAAiB;QAC3BoD,KAAK,EAAEzF,UAAU,GAAG,GAAG,GAAG,IAAI,CAACjB,kBAAkB;QACjD2G,UAAU,EAAElI,MAAM,CAACG,YAAY,IAAI,IAAI,CAACoB,kBAAkB,GAAGiB,UAAU,CAAC;QACxE7B;MACR,CAAO,CAAC;MACF,MAAMwH,aAAa,GAAGX,CAAC,CAACN,SAAS,EAAE;QACjCkB,KAAK,EAAE,CAAC7I,EAAE,CAACoE,CAAC,CAAC,QAAQ,CAAC,EAAEgD,SAAS,CAAC;QAClCxE,KAAK,EAAER,WAAW;QAClBiD,QAAQ;QACRlF,GAAG,EAAE,WAAW;QAChBgI,GAAG,EAAE;MACb,CAAO,EAAE,CAACI,QAAQ,CAACZ,SAAS,CAAC,GAAG;QAAES,OAAO,EAAEA,CAAA,KAAM,CAACE,SAAS;MAAC,CAAE,GAAG,CAACA,SAAS,CAAC,CAAC;MACvE,OAAOL,CAAC,CAAC,KAAK,EAAE;QACdE,GAAG,EAAE,CAAC;QACNU,KAAK,EAAE,CAAC7I,EAAE,CAACoE,CAAC,CAAC,SAAS,CAAC,EAAE3D,MAAM,CAACQ,iBAAiB,GAAG,WAAW,GAAG,EAAE;MAC5E,CAAO,EAAE,CAAC2H,aAAa,EAAEJ,SAAS,CAAC,CAAC;IACpC;EACA,CAAG,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}