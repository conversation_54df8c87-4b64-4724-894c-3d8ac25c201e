{"ast": null, "code": "const defaultProps = {\n  store: {\n    required: true,\n    type: Object\n  },\n  stripe: Boolean,\n  tooltipEffect: String,\n  tooltipOptions: {\n    type: Object\n  },\n  context: {\n    default: () => ({}),\n    type: Object\n  },\n  rowClassName: [String, Function],\n  rowStyle: [Object, Function],\n  fixed: {\n    type: String,\n    default: \"\"\n  },\n  highlight: Boolean\n};\nexport { defaultProps as default };", "map": {"version": 3, "names": ["defaultProps", "store", "required", "type", "Object", "stripe", "Boolean", "tooltipEffect", "String", "tooltipOptions", "context", "default", "rowClassName", "Function", "rowStyle", "fixed", "highlight"], "sources": ["../../../../../../../packages/components/table/src/table-body/defaults.ts"], "sourcesContent": ["// @ts-nocheck\nimport type { PropType } from 'vue'\nimport type { Store } from '../store'\nimport type {\n  ColumnCls,\n  ColumnStyle,\n  DefaultRow,\n  Table,\n} from '../table/defaults'\nimport type { TableOverflowTooltipOptions } from '../util'\n\ninterface TableBodyProps<T> {\n  store: Store<T>\n  stripe?: boolean\n  context: Table<T>\n  rowClassName: ColumnCls<T>\n  rowStyle: ColumnStyle<T>\n  fixed: string\n  highlight: boolean\n  tooltipEffect?: string\n  tooltipOptions?: TableOverflowTooltipOptions\n}\n\nconst defaultProps = {\n  store: {\n    required: true,\n    type: Object as PropType<TableBodyProps<DefaultRow>['store']>,\n  },\n  stripe: Boolean,\n  tooltipEffect: String,\n  tooltipOptions: {\n    type: Object as PropType<TableBodyProps<DefaultRow>['tooltipOptions']>,\n  },\n  context: {\n    default: () => ({}),\n    type: Object as PropType<TableBodyProps<DefaultRow>['context']>,\n  },\n  rowClassName: [String, Function] as PropType<\n    TableBodyProps<DefaultRow>['rowClassName']\n  >,\n  rowStyle: [Object, Function] as PropType<\n    TableBodyProps<DefaultRow>['rowStyle']\n  >,\n  fixed: {\n    type: String,\n    default: '',\n  },\n  highlight: Boolean,\n}\n\nexport { TableBodyProps }\nexport default defaultProps\n"], "mappings": "AAAK,MAACA,YAAY,GAAG;EACnBC,KAAK,EAAE;IACLC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAEC;EACV,CAAG;EACDC,MAAM,EAAEC,OAAO;EACfC,aAAa,EAAEC,MAAM;EACrBC,cAAc,EAAE;IACdN,IAAI,EAAEC;EACV,CAAG;EACDM,OAAO,EAAE;IACPC,OAAO,EAAEA,CAAA,MAAO,EAAE,CAAC;IACnBR,IAAI,EAAEC;EACV,CAAG;EACDQ,YAAY,EAAE,CAACJ,MAAM,EAAEK,QAAQ,CAAC;EAChCC,QAAQ,EAAE,CAACV,MAAM,EAAES,QAAQ,CAAC;EAC5BE,KAAK,EAAE;IACLZ,IAAI,EAAEK,MAAM;IACZG,OAAO,EAAE;EACb,CAAG;EACDK,SAAS,EAAEV;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}