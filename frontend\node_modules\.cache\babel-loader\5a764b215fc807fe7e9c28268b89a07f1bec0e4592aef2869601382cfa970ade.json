{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst dividerProps = buildProps({\n  direction: {\n    type: String,\n    values: [\"horizontal\", \"vertical\"],\n    default: \"horizontal\"\n  },\n  contentPosition: {\n    type: String,\n    values: [\"left\", \"center\", \"right\"],\n    default: \"center\"\n  },\n  borderStyle: {\n    type: definePropType(String),\n    default: \"solid\"\n  }\n});\nexport { dividerProps };", "map": {"version": 3, "names": ["dividerProps", "buildProps", "direction", "type", "String", "values", "default", "contentPosition", "borderStyle", "definePropType"], "sources": ["../../../../../../packages/components/divider/src/divider.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Divider from './divider.vue'\n\nexport type BorderStyle = CSSStyleDeclaration['borderStyle']\n\nexport const dividerProps = buildProps({\n  /**\n   * @description Set divider's direction\n   */\n  direction: {\n    type: String,\n    values: ['horizontal', 'vertical'],\n    default: 'horizontal',\n  },\n  /**\n   * @description Set the style of divider\n   */\n  contentPosition: {\n    type: String,\n    values: ['left', 'center', 'right'],\n    default: 'center',\n  },\n  /**\n   * @description the position of the customized content on the divider line\n   */\n  borderStyle: {\n    type: definePropType<BorderStyle>(String),\n    default: 'solid',\n  },\n} as const)\nexport type DividerProps = ExtractPropTypes<typeof dividerProps>\n\nexport type DividerInstance = InstanceType<typeof Divider> & unknown\n"], "mappings": ";AACY,MAACA,YAAY,GAAGC,UAAU,CAAC;EACrCC,SAAS,EAAE;IACTC,IAAI,EAAEC,MAAM;IACZC,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;IAClCC,OAAO,EAAE;EACb,CAAG;EACDC,eAAe,EAAE;IACfJ,IAAI,EAAEC,MAAM;IACZC,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;IACnCC,OAAO,EAAE;EACb,CAAG;EACDE,WAAW,EAAE;IACXL,IAAI,EAAEM,cAAc,CAACL,MAAM,CAAC;IAC5BE,OAAO,EAAE;EACb;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}