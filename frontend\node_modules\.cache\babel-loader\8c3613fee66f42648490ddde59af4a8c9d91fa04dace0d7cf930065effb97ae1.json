{"ast": null, "code": "export { default } from './wrapperValue.js';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/value.js"], "sourcesContent": ["export { default } from './wrapperValue.js'\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}