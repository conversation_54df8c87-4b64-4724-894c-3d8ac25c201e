{"ast": null, "code": "import { isNil } from 'lodash-unified';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { CHANGE_EVENT, INPUT_EVENT, UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nconst inputNumberProps = buildProps({\n  id: {\n    type: String,\n    default: void 0\n  },\n  step: {\n    type: Number,\n    default: 1\n  },\n  stepStrictly: Boolean,\n  max: {\n    type: Number,\n    default: Number.POSITIVE_INFINITY\n  },\n  min: {\n    type: Number,\n    default: Number.NEGATIVE_INFINITY\n  },\n  modelValue: Number,\n  readonly: <PERSON><PERSON>an,\n  disabled: Boolean,\n  size: useSizeProp,\n  controls: {\n    type: <PERSON>olean,\n    default: true\n  },\n  controlsPosition: {\n    type: String,\n    default: \"\",\n    values: [\"\", \"right\"]\n  },\n  valueOnClear: {\n    type: [String, Number, null],\n    validator: val => val === null || isNumber(val) || [\"min\", \"max\"].includes(val),\n    default: null\n  },\n  name: String,\n  placeholder: String,\n  precision: {\n    type: Number,\n    validator: val => val >= 0 && val === Number.parseInt(`${val}`, 10)\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst inputNumberEmits = {\n  [CHANGE_EVENT]: (cur, prev) => prev !== cur,\n  blur: e => e instanceof FocusEvent,\n  focus: e => e instanceof FocusEvent,\n  [INPUT_EVENT]: val => isNumber(val) || isNil(val),\n  [UPDATE_MODEL_EVENT]: val => isNumber(val) || isNil(val)\n};\nexport { inputNumberEmits, inputNumberProps };", "map": {"version": 3, "names": ["inputNumberProps", "buildProps", "id", "type", "String", "default", "step", "Number", "stepStrictly", "Boolean", "max", "POSITIVE_INFINITY", "min", "NEGATIVE_INFINITY", "modelValue", "readonly", "disabled", "size", "useSizeProp", "controls", "controlsPosition", "values", "valueOnClear", "validator", "val", "isNumber", "includes", "name", "placeholder", "precision", "parseInt", "validateEvent", "useAriaProps", "inputNumberEmits", "CHANGE_EVENT", "cur", "prev", "blur", "e", "FocusEvent", "focus", "INPUT_EVENT", "isNil", "UPDATE_MODEL_EVENT"], "sources": ["../../../../../../packages/components/input-number/src/input-number.ts"], "sourcesContent": ["import { isNil } from 'lodash-unified'\nimport { useAriaProps, useSizeProp } from '@element-plus/hooks'\nimport { buildProps, isNumber } from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport type { ExtractPropTypes } from 'vue'\nimport type InputNumber from './input-number.vue'\n\nexport const inputNumberProps = buildProps({\n  /**\n   * @description same as `id` in native input\n   */\n  id: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description incremental step\n   */\n  step: {\n    type: Number,\n    default: 1,\n  },\n  /**\n   * @description whether input value can only be multiple of step\n   */\n  stepStrictly: Boolean,\n  /**\n   * @description the maximum allowed value\n   */\n  max: {\n    type: Number,\n    default: Number.POSITIVE_INFINITY,\n  },\n  /**\n   * @description the minimum allowed value\n   */\n  min: {\n    type: Number,\n    default: Number.NEGATIVE_INFINITY,\n  },\n  /**\n   * @description binding value\n   */\n  modelValue: Number,\n  /**\n   * @description same as `readonly` in native input\n   */\n  readonly: <PERSON>olean,\n  /**\n   * @description whether the component is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description size of the component\n   */\n  size: useSizeProp,\n  /**\n   * @description whether to enable the control buttons\n   */\n  controls: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description position of the control buttons\n   */\n  controlsPosition: {\n    type: String,\n    default: '',\n    values: ['', 'right'],\n  },\n  /**\n   * @description value should be set when input box is cleared\n   */\n  valueOnClear: {\n    type: [String, Number, null],\n    validator: (val: 'min' | 'max' | number | null) =>\n      val === null || isNumber(val) || ['min', 'max'].includes(val),\n    default: null,\n  },\n  /**\n   * @description same as `name` in native input\n   */\n  name: String,\n  /**\n   * @description same as `placeholder` in native input\n   */\n  placeholder: String,\n  /**\n   * @description precision of input value\n   */\n  precision: {\n    type: Number,\n    validator: (val: number) =>\n      val >= 0 && val === Number.parseInt(`${val}`, 10),\n  },\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  ...useAriaProps(['ariaLabel']),\n} as const)\nexport type InputNumberProps = ExtractPropTypes<typeof inputNumberProps>\n\nexport const inputNumberEmits = {\n  [CHANGE_EVENT]: (cur: number | undefined, prev: number | undefined) =>\n    prev !== cur,\n  blur: (e: FocusEvent) => e instanceof FocusEvent,\n  focus: (e: FocusEvent) => e instanceof FocusEvent,\n  [INPUT_EVENT]: (val: number | null | undefined) =>\n    isNumber(val) || isNil(val),\n  [UPDATE_MODEL_EVENT]: (val: number | undefined) =>\n    isNumber(val) || isNil(val),\n}\nexport type InputNumberEmits = typeof inputNumberEmits\n\nexport type InputNumberInstance = InstanceType<typeof InputNumber> & unknown\n"], "mappings": ";;;;;;AAQY,MAACA,gBAAgB,GAAGC,UAAU,CAAC;EACzCC,EAAE,EAAE;IACFC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE,KAAK;EAClB,CAAG;EACDC,IAAI,EAAE;IACJH,IAAI,EAAEI,MAAM;IACZF,OAAO,EAAE;EACb,CAAG;EACDG,YAAY,EAAEC,OAAO;EACrBC,GAAG,EAAE;IACHP,IAAI,EAAEI,MAAM;IACZF,OAAO,EAAEE,MAAM,CAACI;EACpB,CAAG;EACDC,GAAG,EAAE;IACHT,IAAI,EAAEI,MAAM;IACZF,OAAO,EAAEE,MAAM,CAACM;EACpB,CAAG;EACDC,UAAU,EAAEP,MAAM;EAClBQ,QAAQ,EAAEN,OAAO;EACjBO,QAAQ,EAAEP,OAAO;EACjBQ,IAAI,EAAEC,WAAW;EACjBC,QAAQ,EAAE;IACRhB,IAAI,EAAEM,OAAO;IACbJ,OAAO,EAAE;EACb,CAAG;EACDe,gBAAgB,EAAE;IAChBjB,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE,EAAE;IACXgB,MAAM,EAAE,CAAC,EAAE,EAAE,OAAO;EACxB,CAAG;EACDC,YAAY,EAAE;IACZnB,IAAI,EAAE,CAACC,MAAM,EAAEG,MAAM,EAAE,IAAI,CAAC;IAC5BgB,SAAS,EAAGC,GAAG,IAAKA,GAAG,KAAK,IAAI,IAAIC,QAAQ,CAACD,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAACE,QAAQ,CAACF,GAAG,CAAC;IACjFnB,OAAO,EAAE;EACb,CAAG;EACDsB,IAAI,EAAEvB,MAAM;EACZwB,WAAW,EAAExB,MAAM;EACnByB,SAAS,EAAE;IACT1B,IAAI,EAAEI,MAAM;IACZgB,SAAS,EAAGC,GAAG,IAAKA,GAAG,IAAI,CAAC,IAAIA,GAAG,KAAKjB,MAAM,CAACuB,QAAQ,CAAC,GAAGN,GAAG,EAAE,EAAE,EAAE;EACxE,CAAG;EACDO,aAAa,EAAE;IACb5B,IAAI,EAAEM,OAAO;IACbJ,OAAO,EAAE;EACb,CAAG;EACD,GAAG2B,YAAY,CAAC,CAAC,WAAW,CAAC;AAC/B,CAAC;AACW,MAACC,gBAAgB,GAAG;EAC9B,CAACC,YAAY,GAAG,CAACC,GAAG,EAAEC,IAAI,KAAKA,IAAI,KAAKD,GAAG;EAC3CE,IAAI,EAAGC,CAAC,IAAKA,CAAC,YAAYC,UAAU;EACpCC,KAAK,EAAGF,CAAC,IAAKA,CAAC,YAAYC,UAAU;EACrC,CAACE,WAAW,GAAIjB,GAAG,IAAKC,QAAQ,CAACD,GAAG,CAAC,IAAIkB,KAAK,CAAClB,GAAG,CAAC;EACnD,CAACmB,kBAAkB,GAAInB,GAAG,IAAKC,QAAQ,CAACD,GAAG,CAAC,IAAIkB,KAAK,CAAClB,GAAG;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}