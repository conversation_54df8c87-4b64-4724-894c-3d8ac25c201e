{"ast": null, "code": "import { NOOP } from '@vue/shared';\nconst withInstall = (main, extra) => {\n  main.install = app => {\n    for (const comp of [main, ...Object.values(extra != null ? extra : {})]) {\n      app.component(comp.name, comp);\n    }\n  };\n  if (extra) {\n    for (const [key, comp] of Object.entries(extra)) {\n      main[key] = comp;\n    }\n  }\n  return main;\n};\nconst withInstallFunction = (fn, name) => {\n  fn.install = app => {\n    fn._context = app._context;\n    app.config.globalProperties[name] = fn;\n  };\n  return fn;\n};\nconst withInstallDirective = (directive, name) => {\n  directive.install = app => {\n    app.directive(name, directive);\n  };\n  return directive;\n};\nconst withNoopInstall = component => {\n  component.install = NOOP;\n  return component;\n};\nexport { withInstall, withInstallDirective, withInstallFunction, withNoopInstall };", "map": {"version": 3, "names": ["withInstall", "main", "extra", "install", "app", "comp", "Object", "values", "component", "name", "key", "entries", "withInstallFunction", "fn", "_context", "config", "globalProperties", "withInstallDirective", "directive", "withNoopInstall", "NOOP"], "sources": ["../../../../../packages/utils/vue/install.ts"], "sourcesContent": ["import { NOOP } from '../functions'\n\nimport type { App, Directive } from 'vue'\nimport type { SFCInstallWithContext, SFCWithInstall } from './typescript'\n\nexport const withInstall = <T, E extends Record<string, any>>(\n  main: T,\n  extra?: E\n) => {\n  ;(main as SFCWithInstall<T>).install = (app): void => {\n    for (const comp of [main, ...Object.values(extra ?? {})]) {\n      app.component(comp.name, comp)\n    }\n  }\n\n  if (extra) {\n    for (const [key, comp] of Object.entries(extra)) {\n      ;(main as any)[key] = comp\n    }\n  }\n  return main as SFCWithInstall<T> & E\n}\n\nexport const withInstallFunction = <T>(fn: T, name: string) => {\n  ;(fn as SFCWithInstall<T>).install = (app: App) => {\n    ;(fn as SFCInstallWithContext<T>)._context = app._context\n    app.config.globalProperties[name] = fn\n  }\n\n  return fn as SFCInstallWithContext<T>\n}\n\nexport const withInstallDirective = <T extends Directive>(\n  directive: T,\n  name: string\n) => {\n  ;(directive as SFCWithInstall<T>).install = (app: App): void => {\n    app.directive(name, directive)\n  }\n\n  return directive as SFCWithInstall<T>\n}\n\nexport const withNoopInstall = <T>(component: T) => {\n  ;(component as SFCWithInstall<T>).install = NOOP\n\n  return component as SFCWithInstall<T>\n}\n"], "mappings": ";AACY,MAACA,WAAW,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;EAE1CD,IAAI,CAACE,OAAO,GAAIC,GAAG,IAAK;IACtB,KAAK,MAAMC,IAAI,IAAI,CAACJ,IAAI,EAAE,GAAGK,MAAM,CAACC,MAAM,CAACL,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,EAAE,CAAC,CAAC,EAAE;MACvEE,GAAG,CAACI,SAAS,CAACH,IAAI,CAACI,IAAI,EAAEJ,IAAI,CAAC;IACpC;EACA,CAAG;EACD,IAAIH,KAAK,EAAE;IACT,KAAK,MAAM,CAACQ,GAAG,EAAEL,IAAI,CAAC,IAAIC,MAAM,CAACK,OAAO,CAACT,KAAK,CAAC,EAAE;MAE/CD,IAAI,CAACS,GAAG,CAAC,GAAGL,IAAI;IACtB;EACA;EACE,OAAOJ,IAAI;AACb;AACY,MAACW,mBAAmB,GAAGA,CAACC,EAAE,EAAEJ,IAAI,KAAK;EAE/CI,EAAE,CAACV,OAAO,GAAIC,GAAG,IAAK;IAEpBS,EAAE,CAACC,QAAQ,GAAGV,GAAG,CAACU,QAAQ;IAC1BV,GAAG,CAACW,MAAM,CAACC,gBAAgB,CAACP,IAAI,CAAC,GAAGI,EAAE;EAC1C,CAAG;EACD,OAAOA,EAAE;AACX;AACY,MAACI,oBAAoB,GAAGA,CAACC,SAAS,EAAET,IAAI,KAAK;EAEvDS,SAAS,CAACf,OAAO,GAAIC,GAAG,IAAK;IAC3BA,GAAG,CAACc,SAAS,CAACT,IAAI,EAAES,SAAS,CAAC;EAClC,CAAG;EACD,OAAOA,SAAS;AAClB;AACY,MAACC,eAAe,GAAIX,SAAS,IAAK;EAE5CA,SAAS,CAACL,OAAO,GAAGiB,IAAI;EACxB,OAAOZ,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}