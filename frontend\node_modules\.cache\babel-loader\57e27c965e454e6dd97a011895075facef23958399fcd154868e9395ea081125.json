{"ast": null, "code": "import { defineComponent, inject, watch, computed, openBlock, createElementBlock, Fragment, unref, normalizeClass, createVNode, withCtx, createBlock, resolveDynamicComponent, createCommentVNode, createElementVNode, renderSlot, toDisplayString, renderList, mergeProps, createTextVNode } from 'vue';\nimport { omit } from 'lodash-unified';\nimport { ElButton } from '../../button/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { tourStepProps, tourStepEmits } from './step2.mjs';\nimport { tourKey } from './helper.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { CloseComponents } from '../../../utils/vue/icon.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTourStep\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tourStepProps,\n  emits: tourStepEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const {\n      Close\n    } = CloseComponents;\n    const {\n      t\n    } = useLocale();\n    const {\n      currentStep,\n      current,\n      total,\n      showClose,\n      closeIcon,\n      mergedType,\n      ns,\n      slots: tourSlots,\n      updateModelValue,\n      onClose: tourOnClose,\n      onFinish: tourOnFinish,\n      onChange\n    } = inject(tourKey);\n    watch(props, val => {\n      currentStep.value = val;\n    }, {\n      immediate: true\n    });\n    const mergedShowClose = computed(() => {\n      var _a;\n      return (_a = props.showClose) != null ? _a : showClose.value;\n    });\n    const mergedCloseIcon = computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.closeIcon) != null ? _a : closeIcon.value) != null ? _b : Close;\n    });\n    const filterButtonProps = btnProps => {\n      if (!btnProps) return;\n      return omit(btnProps, [\"children\", \"onClick\"]);\n    };\n    const onPrev = () => {\n      var _a, _b;\n      current.value -= 1;\n      if ((_a = props.prevButtonProps) == null ? void 0 : _a.onClick) {\n        (_b = props.prevButtonProps) == null ? void 0 : _b.onClick();\n      }\n      onChange();\n    };\n    const onNext = () => {\n      var _a;\n      if (current.value >= total.value - 1) {\n        onFinish();\n      } else {\n        current.value += 1;\n      }\n      if ((_a = props.nextButtonProps) == null ? void 0 : _a.onClick) {\n        props.nextButtonProps.onClick();\n      }\n      onChange();\n    };\n    const onFinish = () => {\n      onClose();\n      tourOnFinish();\n    };\n    const onClose = () => {\n      updateModelValue(false);\n      tourOnClose();\n      emit(\"close\");\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(Fragment, null, [unref(mergedShowClose) ? (openBlock(), createElementBlock(\"button\", {\n        key: 0,\n        \"aria-label\": \"Close\",\n        class: normalizeClass(unref(ns).e(\"closebtn\")),\n        type: \"button\",\n        onClick: onClose\n      }, [createVNode(unref(ElIcon), {\n        class: normalizeClass(unref(ns).e(\"close\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(mergedCloseIcon))))]),\n        _: 1\n      }, 8, [\"class\"])], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"header\", {\n        class: normalizeClass([unref(ns).e(\"header\"), {\n          \"show-close\": unref(showClose)\n        }])\n      }, [renderSlot(_ctx.$slots, \"header\", {}, () => [createElementVNode(\"span\", {\n        role: \"heading\",\n        class: normalizeClass(unref(ns).e(\"title\"))\n      }, toDisplayString(_ctx.title), 3)])], 2), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"body\"))\n      }, [renderSlot(_ctx.$slots, \"default\", {}, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.description), 1)])], 2), createElementVNode(\"footer\", {\n        class: normalizeClass(unref(ns).e(\"footer\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).b(\"indicators\"))\n      }, [unref(tourSlots).indicators ? (openBlock(), createBlock(resolveDynamicComponent(unref(tourSlots).indicators), {\n        key: 0,\n        current: unref(current),\n        total: unref(total)\n      }, null, 8, [\"current\", \"total\"])) : (openBlock(true), createElementBlock(Fragment, {\n        key: 1\n      }, renderList(unref(total), (item, index) => {\n        return openBlock(), createElementBlock(\"span\", {\n          key: item,\n          class: normalizeClass([unref(ns).b(\"indicator\"), index === unref(current) ? \"is-active\" : \"\"])\n        }, null, 2);\n      }), 128))], 2), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).b(\"buttons\"))\n      }, [unref(current) > 0 ? (openBlock(), createBlock(unref(ElButton), mergeProps({\n        key: 0,\n        size: \"small\",\n        type: unref(mergedType)\n      }, filterButtonProps(_ctx.prevButtonProps), {\n        onClick: onPrev\n      }), {\n        default: withCtx(() => {\n          var _a, _b;\n          return [createTextVNode(toDisplayString((_b = (_a = _ctx.prevButtonProps) == null ? void 0 : _a.children) != null ? _b : unref(t)(\"el.tour.previous\")), 1)];\n        }),\n        _: 1\n      }, 16, [\"type\"])) : createCommentVNode(\"v-if\", true), unref(current) <= unref(total) - 1 ? (openBlock(), createBlock(unref(ElButton), mergeProps({\n        key: 1,\n        size: \"small\",\n        type: unref(mergedType) === \"primary\" ? \"default\" : \"primary\"\n      }, filterButtonProps(_ctx.nextButtonProps), {\n        onClick: onNext\n      }), {\n        default: withCtx(() => {\n          var _a, _b;\n          return [createTextVNode(toDisplayString((_b = (_a = _ctx.nextButtonProps) == null ? void 0 : _a.children) != null ? _b : unref(current) === unref(total) - 1 ? unref(t)(\"el.tour.finish\") : unref(t)(\"el.tour.next\")), 1)];\n        }),\n        _: 1\n      }, 16, [\"type\"])) : createCommentVNode(\"v-if\", true)], 2)], 2)], 64);\n    };\n  }\n});\nvar TourStep = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"step.vue\"]]);\nexport { TourStep as default };", "map": {"version": 3, "names": ["name", "Close", "CloseComponents", "t", "useLocale", "currentStep", "current", "total", "showClose", "closeIcon", "mergedType", "ns", "slots", "tourSlots", "updateModelValue", "onClose", "tourOnClose", "onFinish", "tourOnFinish", "onChange", "inject", "tourKey", "watch", "props", "val", "value", "immediate", "mergedShowClose", "computed", "_a", "mergedCloseIcon", "_b", "filterButtonProps", "btnProps", "omit", "onPrev", "prevButtonProps", "onClick", "onNext", "nextButtonProps"], "sources": ["../../../../../../packages/components/tour/src/step.vue"], "sourcesContent": ["<template>\n  <button\n    v-if=\"mergedShowClose\"\n    aria-label=\"Close\"\n    :class=\"ns.e('closebtn')\"\n    type=\"button\"\n    @click=\"onClose\"\n  >\n    <el-icon :class=\"ns.e('close')\">\n      <component :is=\"mergedCloseIcon\" />\n    </el-icon>\n  </button>\n  <header :class=\"[ns.e('header'), { 'show-close': showClose }]\">\n    <slot name=\"header\">\n      <span role=\"heading\" :class=\"ns.e('title')\">\n        {{ title }}\n      </span>\n    </slot>\n  </header>\n  <div :class=\"ns.e('body')\">\n    <slot>\n      <span>{{ description }}</span>\n    </slot>\n  </div>\n  <footer :class=\"ns.e('footer')\">\n    <div :class=\"ns.b('indicators')\">\n      <component\n        :is=\"tourSlots.indicators\"\n        v-if=\"tourSlots.indicators\"\n        :current=\"current\"\n        :total=\"total\"\n      />\n      <template v-else>\n        <span\n          v-for=\"(item, index) in total\"\n          :key=\"item\"\n          :class=\"[ns.b('indicator'), index === current ? 'is-active' : '']\"\n        />\n      </template>\n    </div>\n    <div :class=\"ns.b('buttons')\">\n      <el-button\n        v-if=\"current > 0\"\n        size=\"small\"\n        :type=\"mergedType\"\n        v-bind=\"filterButtonProps(prevButtonProps)\"\n        @click=\"onPrev\"\n      >\n        {{ prevButtonProps?.children ?? t('el.tour.previous') }}\n      </el-button>\n      <el-button\n        v-if=\"current <= total - 1\"\n        size=\"small\"\n        :type=\"mergedType === 'primary' ? 'default' : 'primary'\"\n        v-bind=\"filterButtonProps(nextButtonProps)\"\n        @click=\"onNext\"\n      >\n        {{\n          nextButtonProps?.children ??\n          (current === total - 1 ? t('el.tour.finish') : t('el.tour.next'))\n        }}\n      </el-button>\n    </div>\n  </footer>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, watch } from 'vue'\nimport { omit } from 'lodash-unified'\nimport { ElButton } from '@element-plus/components/button'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { CloseComponents } from '@element-plus/utils'\nimport { useLocale } from '@element-plus/hooks'\nimport { tourStepEmits, tourStepProps } from './step'\nimport { tourKey } from './helper'\n\nimport type { TourBtnProps } from './types'\n\ndefineOptions({\n  name: 'ElTourStep',\n})\n\nconst props = defineProps(tourStepProps)\nconst emit = defineEmits(tourStepEmits)\n\nconst { Close } = CloseComponents\n\nconst { t } = useLocale()\n\nconst {\n  currentStep,\n  current,\n  total,\n  showClose,\n  closeIcon,\n  mergedType,\n  ns,\n  slots: tourSlots,\n  updateModelValue,\n  onClose: tourOnClose,\n  onFinish: tourOnFinish,\n  onChange,\n} = inject(tourKey)!\n\nwatch(\n  props,\n  (val) => {\n    currentStep.value = val\n  },\n  {\n    immediate: true,\n  }\n)\n\nconst mergedShowClose = computed(() => props.showClose ?? showClose.value)\nconst mergedCloseIcon = computed(\n  () => props.closeIcon ?? closeIcon.value ?? Close\n)\n\nconst filterButtonProps = (btnProps?: TourBtnProps) => {\n  if (!btnProps) return\n  return omit(btnProps, ['children', 'onClick'])\n}\n\nconst onPrev = () => {\n  current.value -= 1\n  if (props.prevButtonProps?.onClick) {\n    props.prevButtonProps?.onClick()\n  }\n  onChange()\n}\n\nconst onNext = () => {\n  if (current.value >= total.value - 1) {\n    onFinish()\n  } else {\n    current.value += 1\n  }\n  if (props.nextButtonProps?.onClick) {\n    props.nextButtonProps.onClick()\n  }\n  onChange()\n}\n\nconst onFinish = () => {\n  onClose()\n  tourOnFinish()\n}\n\nconst onClose = () => {\n  updateModelValue(false)\n  tourOnClose()\n  emit('close')\n}\n</script>\n"], "mappings": ";;;;;;;;;mCA8Ec;EACZA,IAAM;AACR;;;;;;;;;IAKM;MAAEC;IAAA,CAAU,GAAAC,eAAA;IAEZ;MAAEC;IAAE,IAAIC,SAAU;IAElB;MACJC,WAAA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACAC,SAAA;MACAC,UAAA;MACAC,EAAA;MACAC,KAAO,EAAAC,SAAA;MACPC,gBAAA;MACAC,OAAS,EAAAC,WAAA;MACTC,QAAU,EAAAC,YAAA;MACVC;IAAA,CACF,GAAIC,MAAA,CAAOC,OAAO;IAElBC,KAAA,CAAAC,KAAA,EAAAC,GAAA;MACEnB,WAAA,CAAAoB,KAAA,GAAAD,GAAA;IAAA,GACC;MACCE,SAAA;IAAoB,CACtB;IACA,MAAAC,eAAA,GAAAC,QAAA;MAAA,IACaC,EAAA;MACb,QAAAA,EAAA,GAAAN,KAAA,CAAAf,SAAA,YAAAqB,EAAA,GAAArB,SAAA,CAAAiB,KAAA;IAAA,CACF;IAEA,MAAMK,eAAA,GAAkBF,QAAS,OAAM;MACvC,IAAMC,EAAkB,EAAAE,EAAA;MACtB,OAAM,CAAAA,EAAA,GAAmB,CAAAF,EAAA,GAAAN,KAAA,CAAAd,SAAA,KAAmB,OAAAoB,EAAA,GAAApB,SAAA,CAAAgB,KAAA,YAAAM,EAAA,GAAA9B,KAAA;IAAA,CAC9C;IAEM,MAAA+B,iBAAA,GAAqBC,QAA4B;MACrD,IAAI,CAACA,QAAU,EACf;MACF,OAAAC,IAAA,CAAAD,QAAA;IAEA;IACE,MAAAE,MAAiB,GAAAA,CAAA;MACb,IAAAN,EAAA,EAAAE,EAAM;MACRzB,OAAA,CAAMmB,KAAA;MACR,KAAAI,EAAA,GAAAN,KAAA,CAAAa,eAAA,qBAAAP,EAAA,CAAAQ,OAAA;QACS,CAAAN,EAAA,GAAAR,KAAA,CAAAa,eAAA,qBAAAL,EAAA,CAAAM,OAAA;MAAA;MAGXlB,QAAA;IACE;IACW,MAAAmB,MAAA,GAAAA,CAAA;MACX,IAAOT,EAAA;MACL,IAAAvB,OAAiB,CAAAmB,KAAA,IAAAlB,KAAA,CAAAkB,KAAA;QACnBR,QAAA;MACA,CAAI;QACFX,OAAA,CAAAmB,KAAA;MAA8B;MAEvB,KAAAI,EAAA,GAAAN,KAAA,CAAAgB,eAAA,qBAAAV,EAAA,CAAAQ,OAAA;QACXd,KAAA,CAAAgB,eAAA,CAAAF,OAAA;MAEA;MACUlB,QAAA;IACR,CAAa;IACf,MAAAF,QAAA,GAAAA,CAAA;MAEAF,OAAA;MACEG,YAAA;IACA,CAAY;IACZ,MAAAH,OAAY,GAAAA,CAAA;MACdD,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}