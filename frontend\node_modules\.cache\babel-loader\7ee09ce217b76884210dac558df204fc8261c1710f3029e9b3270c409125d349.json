{"ast": null, "code": "import Popper from './src/popper2.mjs';\nexport { default as <PERSON>PopperArrow } from './src/arrow2.mjs';\nexport { default as ElPopperTrigger } from './src/trigger2.mjs';\nexport { default as ElPopperContent } from './src/content2.mjs';\nexport { Effect, popperProps, roleTypes, usePopperProps } from './src/popper.mjs';\nexport { popperTriggerProps, usePopperTriggerProps } from './src/trigger.mjs';\nexport { popperContentEmits, popperContentProps, popperCoreConfigProps, usePopperContentEmits, usePopperContentProps, usePopperCoreConfigProps } from './src/content.mjs';\nexport { popperArrowProps, usePopperArrowProps } from './src/arrow.mjs';\nexport { POPPER_CONTENT_INJECTION_KEY, POPPER_INJECTION_KEY } from './src/constants.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElPopper = withInstall(Popper);\nexport { ElPopper, ElPopper as default };", "map": {"version": 3, "names": ["ElPopper", "withInstall", "<PERSON><PERSON>"], "sources": ["../../../../../packages/components/popper/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Popper from './src/popper.vue'\n\nimport ElPopperArrow from './src/arrow.vue'\nimport ElPopperTrigger from './src/trigger.vue'\nimport ElPopperContent from './src/content.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport { ElPopperArrow, ElPopperTrigger, ElPopperContent }\n\nexport const ElPopper: SFCWithInstall<typeof Popper> = withInstall(Popper)\nexport default ElPopper\n\nexport * from './src/popper'\nexport * from './src/trigger'\nexport * from './src/content'\nexport * from './src/arrow'\nexport * from './src/constants'\n\nexport type { Placement, Options } from '@popperjs/core'\n"], "mappings": ";;;;;;;;;;AAMY,MAACA,QAAQ,GAAGC,WAAW,CAACC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}