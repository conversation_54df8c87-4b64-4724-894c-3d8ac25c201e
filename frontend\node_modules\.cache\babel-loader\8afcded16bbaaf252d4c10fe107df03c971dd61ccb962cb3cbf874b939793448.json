{"ast": null, "code": "import { useTooltipContentProps } from './content.mjs';\nimport { useTooltipTriggerProps } from './trigger.mjs';\nimport { popperProps } from '../../popper/src/popper.mjs';\nimport { popperArrowProps } from '../../popper/src/arrow.mjs';\nimport { createModelToggleComposable } from '../../../hooks/use-model-toggle/index.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst {\n  useModelToggleProps: useTooltipModelToggleProps,\n  useModelToggleEmits: useTooltipModelToggleEmits,\n  useModelToggle: useTooltipModelToggle\n} = createModelToggleComposable(\"visible\");\nconst useTooltipProps = buildProps({\n  ...popperProps,\n  ...useTooltipModelToggleProps,\n  ...useTooltipContentProps,\n  ...useTooltipTriggerProps,\n  ...popperArrowProps,\n  showArrow: {\n    type: Boolean,\n    default: true\n  }\n});\nconst tooltipEmits = [...useTooltipModelToggleEmits, \"before-show\", \"before-hide\", \"show\", \"hide\", \"open\", \"close\"];\nexport { tooltipEmits, useTooltipModelToggle, useTooltipModelToggleEmits, useTooltipModelToggleProps, useTooltipProps };", "map": {"version": 3, "names": ["useModelToggleProps", "useTooltipModelToggleProps", "useModelToggleEmits", "useTooltipModelToggleEmits", "useModelToggle", "useTooltipModelToggle", "createModelToggleComposable", "useTooltipProps", "buildProps", "popperProps", "useTooltipContentProps", "useTooltipTriggerProps", "popperArrowProps", "showArrow", "type", "Boolean", "default", "tooltipEmits"], "sources": ["../../../../../../packages/components/tooltip/src/tooltip.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { createModelToggleComposable } from '@element-plus/hooks'\nimport { popperArrowProps, popperProps } from '@element-plus/components/popper'\nimport { useTooltipContentProps } from './content'\nimport { useTooltipTriggerProps } from './trigger'\nimport type Tooltip from './tooltip.vue'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const {\n  useModelToggleProps: useTooltipModelToggleProps,\n  useModelToggleEmits: useTooltipModelToggleEmits,\n  useModelToggle: useTooltipModelToggle,\n} = createModelToggleComposable('visible' as const)\n\nexport const useTooltipProps = buildProps({\n  ...popperProps,\n  ...useTooltipModelToggleProps,\n  ...useTooltipContentProps,\n  ...useTooltipTriggerProps,\n  ...popperArrowProps,\n  /**\n   * @description whether the tooltip content has an arrow\n   */\n  showArrow: {\n    type: Boolean,\n    default: true,\n  },\n})\n\nexport const tooltipEmits = [\n  ...useTooltipModelToggleEmits,\n  'before-show',\n  'before-hide',\n  'show',\n  'hide',\n  'open',\n  'close',\n]\n\nexport type ElTooltipProps = ExtractPropTypes<typeof useTooltipProps>\n\nexport type TooltipInstance = InstanceType<typeof Tooltip> & unknown\n"], "mappings": ";;;;;;AAKY,MAAC;EACXA,mBAAmB,EAAEC,0BAA0B;EAC/CC,mBAAmB,EAAEC,0BAA0B;EAC/CC,cAAc,EAAEC;AAClB,CAAC,GAAGC,2BAA2B,CAAC,SAAS;AAC7B,MAACC,eAAe,GAAGC,UAAU,CAAC;EACxC,GAAGC,WAAW;EACd,GAAGR,0BAA0B;EAC7B,GAAGS,sBAAsB;EACzB,GAAGC,sBAAsB;EACzB,GAAGC,gBAAgB;EACnBC,SAAS,EAAE;IACTC,IAAI,EAAEC,OAAO;IACbC,OAAO,EAAE;EACb;AACA,CAAC;AACW,MAACC,YAAY,GAAG,CAC1B,GAAGd,0BAA0B,EAC7B,aAAa,EACb,aAAa,EACb,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,CACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}