{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, getCurrentInstance, inject, ref, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, renderSlot, createBlock, withCtx, resolveDynamicComponent, toDisplayString } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { breadcrumbKey } from './constants.mjs';\nimport { breadcrumbItemProps } from './breadcrumb-item.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElBreadcrumbItem\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: breadcrumbItemProps,\n  setup(__props) {\n    const props = __props;\n    const instance = getCurrentInstance();\n    const breadcrumbContext = inject(breadcrumbKey, void 0);\n    const ns = useNamespace(\"breadcrumb\");\n    const router = instance.appContext.config.globalProperties.$router;\n    const link = ref();\n    const onClick = () => {\n      if (!props.to || !router) return;\n      props.replace ? router.replace(props.to) : router.push(props.to);\n    };\n    return (_ctx, _cache) => {\n      var _a, _b;\n      return openBlock(), createElementBlock(\"span\", {\n        class: normalizeClass(unref(ns).e(\"item\"))\n      }, [createElementVNode(\"span\", {\n        ref_key: \"link\",\n        ref: link,\n        class: normalizeClass([unref(ns).e(\"inner\"), unref(ns).is(\"link\", !!_ctx.to)]),\n        role: \"link\",\n        onClick\n      }, [renderSlot(_ctx.$slots, \"default\")], 2), ((_a = unref(breadcrumbContext)) == null ? void 0 : _a.separatorIcon) ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"separator\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(breadcrumbContext).separatorIcon)))]),\n        _: 1\n      }, 8, [\"class\"])) : (openBlock(), createElementBlock(\"span\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"separator\")),\n        role: \"presentation\"\n      }, toDisplayString((_b = unref(breadcrumbContext)) == null ? void 0 : _b.separator), 3))], 2);\n    };\n  }\n});\nvar BreadcrumbItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"breadcrumb-item.vue\"]]);\nexport { BreadcrumbItem as default };", "map": {"version": 3, "names": ["name", "instance", "getCurrentInstance", "breadcrumbContext", "inject", "breadcrumbKey", "ns", "useNamespace", "router", "appContext", "config", "globalProperties", "$router", "link", "ref", "onClick", "props", "to", "replace", "push"], "sources": ["../../../../../../packages/components/breadcrumb/src/breadcrumb-item.vue"], "sourcesContent": ["<template>\n  <span :class=\"ns.e('item')\">\n    <span\n      ref=\"link\"\n      :class=\"[ns.e('inner'), ns.is('link', !!to)]\"\n      role=\"link\"\n      @click=\"onClick\"\n    >\n      <slot />\n    </span>\n    <el-icon v-if=\"breadcrumbContext?.separatorIcon\" :class=\"ns.e('separator')\">\n      <component :is=\"breadcrumbContext.separatorIcon\" />\n    </el-icon>\n    <span v-else :class=\"ns.e('separator')\" role=\"presentation\">\n      {{ breadcrumbContext?.separator }}\n    </span>\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { getCurrentInstance, inject, ref } from 'vue'\nimport ElIcon from '@element-plus/components/icon'\nimport { useNamespace } from '@element-plus/hooks'\nimport { breadcrumbKey } from './constants'\nimport { breadcrumbItemProps } from './breadcrumb-item'\n\nimport type { Router } from 'vue-router'\n\ndefineOptions({\n  name: 'ElBreadcrumbItem',\n})\n\nconst props = defineProps(breadcrumbItemProps)\n\nconst instance = getCurrentInstance()!\nconst breadcrumbContext = inject(breadcrumbKey, undefined)\nconst ns = useNamespace('breadcrumb')\n\nconst router = instance.appContext.config.globalProperties.$router as Router\n\nconst link = ref<HTMLSpanElement>()\n\nconst onClick = () => {\n  if (!props.to || !router) return\n  props.replace ? router.replace(props.to) : router.push(props.to)\n}\n</script>\n"], "mappings": ";;;;;;;mCA4Bc;EACZA,IAAM;AACR;;;;;;IAIA,MAAMC,QAAA,GAAWC,kBAAmB;IAC9B,MAAAC,iBAAA,GAAoBC,MAAO,CAAAC,aAAA,EAAe,KAAS;IACnD,MAAAC,EAAA,GAAKC,YAAA,CAAa,YAAY;IAEpC,MAAMC,MAAS,GAAAP,QAAA,CAASQ,UAAW,CAAAC,MAAA,CAAOC,gBAAiB,CAAAC,OAAA;IAE3D,MAAMC,IAAA,GAAOC,GAAqB;IAElC,MAAMC,OAAA,GAAUA,CAAA,KAAM;MACpB,IAAI,CAACC,KAAA,CAAMC,EAAM,KAACT,MAAQ,EACpB;MACRQ,KAAA,CAAAE,OAAA,GAAAV,MAAA,CAAAU,OAAA,CAAAF,KAAA,CAAAC,EAAA,IAAAT,MAAA,CAAAW,IAAA,CAAAH,KAAA,CAAAC,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}