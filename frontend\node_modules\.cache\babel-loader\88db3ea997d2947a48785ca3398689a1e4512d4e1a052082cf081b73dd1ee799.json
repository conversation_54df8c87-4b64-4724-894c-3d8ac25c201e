{"ast": null, "code": "import Tabs from './src/tabs.mjs';\nexport { tabsEmits, tabsProps } from './src/tabs.mjs';\nimport TabPane from './src/tab-pane2.mjs';\nexport { tabBarProps } from './src/tab-bar.mjs';\nexport { tabNavEmits, tabNavProps } from './src/tab-nav.mjs';\nexport { tabPaneProps } from './src/tab-pane.mjs';\nexport { tabsRootContextKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElTabs = withInstall(Tabs, {\n  TabPane\n});\nconst ElTabPane = withNoopInstall(TabPane);\nexport { ElTabPane, ElTabs, ElTabs as default };", "map": {"version": 3, "names": ["ElTabs", "withInstall", "Tabs", "TabPane", "ElTabPane", "withNoopInstall"], "sources": ["../../../../../packages/components/tabs/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON>oopInstall } from '@element-plus/utils'\nimport Tabs from './src/tabs'\nimport TabPane from './src/tab-pane.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTabs: SFCWithInstall<typeof Tabs> & {\n  TabPane: typeof TabPane\n} = withInstall(Tabs, {\n  TabPane,\n})\nexport const ElTabPane: SFCWithInstall<typeof TabPane> =\n  withNoopInstall(TabPane)\nexport default ElTabs\n\nexport * from './src/tabs'\nexport * from './src/tab-bar'\nexport * from './src/tab-nav'\nexport * from './src/tab-pane'\nexport * from './src/constants'\n"], "mappings": ";;;;;;;;AAGY,MAACA,MAAM,GAAGC,WAAW,CAACC,IAAI,EAAE;EACtCC;AACF,CAAC;AACW,MAACC,SAAS,GAAGC,eAAe,CAACF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}