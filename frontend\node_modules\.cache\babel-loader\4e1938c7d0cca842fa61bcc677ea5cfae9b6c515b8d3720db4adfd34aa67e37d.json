{"ast": null, "code": "import { ref, reactive, computed, onMounted } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nimport { DocumentCopy, Download } from \"@element-plus/icons-vue\";\nimport { SM2Crypto, SM3Hasher } from \"@/utils/crypto\";\nexport default {\n  name: \"UserProfile\",\n  setup() {\n    const store = useStore();\n    const formRef = ref(null);\n    const passwordFormRef = ref(null);\n    const loading = ref(true);\n    const updating = ref(false);\n    const changingPassword = ref(false);\n    const editMode = ref(false);\n    const passwordDialogVisible = ref(false);\n    const keyDialogVisible = ref(false);\n\n    // 用户信息\n    const userInfo = computed(() => store.getters[\"user/userInfo\"]);\n\n    // 编辑表单\n    const form = reactive({\n      email: \"\",\n      phone: \"\"\n    });\n\n    // 修改密码表单\n    const passwordForm = reactive({\n      oldPassword: \"\",\n      newPassword: \"\",\n      confirmPassword: \"\"\n    });\n\n    // 新密钥对\n    const newKeyPair = reactive({\n      publicKey: \"\",\n      privateKey: \"\"\n    });\n\n    // 信誉分颜色\n    const creditScoreColor = computed(() => {\n      const score = userInfo.value.creditScore || 0;\n      if (score >= 90) return \"#67c23a\";\n      if (score >= 70) return \"#409eff\";\n      if (score >= 50) return \"#e6a23c\";\n      return \"#f56c6c\";\n    });\n\n    // 格式化信誉分\n    const formatCreditScore = percentage => {\n      return `${percentage}分`;\n    };\n\n    // 表单验证规则\n    const rules = {\n      email: [{\n        required: true,\n        message: \"请输入邮箱\",\n        trigger: \"blur\"\n      }, {\n        type: \"email\",\n        message: \"请输入正确的邮箱格式\",\n        trigger: \"blur\"\n      }],\n      phone: [{\n        required: true,\n        message: \"请输入手机号\",\n        trigger: \"blur\"\n      }, {\n        pattern: /^1[3-9]\\d{9}$/,\n        message: \"请输入正确的手机号格式\",\n        trigger: \"blur\"\n      }]\n    };\n\n    // 密码表单验证规则\n    const validatePass = (rule, value, callback) => {\n      if (value === \"\") {\n        callback(new Error(\"请输入新密码\"));\n      } else if (value.length < 6) {\n        callback(new Error(\"密码长度不能小于6位\"));\n      } else {\n        if (passwordForm.confirmPassword !== \"\") {\n          passwordFormRef.value.validateField(\"confirmPassword\");\n        }\n        callback();\n      }\n    };\n    const validatePass2 = (rule, value, callback) => {\n      if (value === \"\") {\n        callback(new Error(\"请再次输入新密码\"));\n      } else if (value !== passwordForm.newPassword) {\n        callback(new Error(\"两次输入密码不一致\"));\n      } else {\n        callback();\n      }\n    };\n    const passwordRules = {\n      oldPassword: [{\n        required: true,\n        message: \"请输入当前密码\",\n        trigger: \"blur\"\n      }],\n      newPassword: [{\n        validator: validatePass,\n        trigger: \"blur\"\n      }],\n      confirmPassword: [{\n        validator: validatePass2,\n        trigger: \"blur\"\n      }]\n    };\n\n    // 获取用户信息\n    const getUserInfo = async () => {\n      try {\n        loading.value = true;\n        await store.dispatch(\"user/getUserInfo\");\n\n        // 填充表单\n        form.email = userInfo.value.email || \"\";\n        form.phone = userInfo.value.phone || \"\";\n      } catch (error) {\n        ElMessage.error(\"获取用户信息失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 更新个人信息\n    const updateProfile = async () => {\n      if (!formRef.value) return;\n      await formRef.value.validate(async valid => {\n        if (valid) {\n          try {\n            updating.value = true;\n            await store.dispatch(\"user/updateProfile\", form);\n            ElMessage.success(\"个人信息更新成功\");\n            editMode.value = false;\n          } catch (error) {\n            ElMessage.error(error.message || \"更新失败\");\n          } finally {\n            updating.value = false;\n          }\n        }\n      });\n    };\n\n    // 显示修改密码对话框\n    const showChangePasswordDialog = () => {\n      passwordDialogVisible.value = true;\n      passwordForm.oldPassword = \"\";\n      passwordForm.newPassword = \"\";\n      passwordForm.confirmPassword = \"\";\n    };\n\n    // 修改密码\n    const changePassword = async () => {\n      if (!passwordFormRef.value) return;\n      await passwordFormRef.value.validate(async valid => {\n        if (valid) {\n          try {\n            changingPassword.value = true;\n\n            // 对密码进行SM3哈希\n            const oldPasswordHash = SM3Hasher.hash(passwordForm.oldPassword);\n            const newPasswordHash = SM3Hasher.hash(passwordForm.newPassword);\n            await store.dispatch(\"user/changePassword\", {\n              oldPassword: oldPasswordHash,\n              newPassword: newPasswordHash\n            });\n            ElMessage.success(\"密码修改成功\");\n            passwordDialogVisible.value = false;\n          } catch (error) {\n            ElMessage.error(error.message || \"密码修改失败\");\n          } finally {\n            changingPassword.value = false;\n          }\n        }\n      });\n    };\n\n    // 显示密钥管理对话框\n    const showKeyManagementDialog = () => {\n      keyDialogVisible.value = true;\n      newKeyPair.publicKey = \"\";\n      newKeyPair.privateKey = \"\";\n    };\n\n    // 生成新密钥对\n    const generateNewKeyPair = () => {\n      const keyPair = SM2Crypto.generateKeyPair();\n      newKeyPair.publicKey = keyPair.publicKey;\n      newKeyPair.privateKey = keyPair.privateKey;\n    };\n\n    // 复制文本到剪贴板的通用函数\n    const copyToClipboard = (text, successMsg) => {\n      // 检查navigator.clipboard API是否可用\n      if (navigator.clipboard && navigator.clipboard.writeText) {\n        navigator.clipboard.writeText(text).then(() => {\n          ElMessage.success(successMsg);\n        }).catch(() => {\n          // 如果API调用失败，使用备用方法\n          fallbackCopyToClipboard(text, successMsg);\n        });\n      } else {\n        // 如果API不可用，使用备用方法\n        fallbackCopyToClipboard(text, successMsg);\n      }\n    };\n\n    // 备用复制方法（使用临时DOM元素）\n    const fallbackCopyToClipboard = (text, successMsg) => {\n      try {\n        // 创建临时textarea元素\n        const textArea = document.createElement(\"textarea\");\n        textArea.value = text;\n\n        // 设置样式使其不可见\n        textArea.style.position = \"fixed\";\n        textArea.style.left = \"-999999px\";\n        textArea.style.top = \"-999999px\";\n        document.body.appendChild(textArea);\n\n        // 选择文本并复制\n        textArea.focus();\n        textArea.select();\n        const successful = document.execCommand(\"copy\");\n\n        // 移除临时元素\n        document.body.removeChild(textArea);\n        if (successful) {\n          ElMessage.success(successMsg);\n        } else {\n          ElMessage.error(\"复制失败，请手动复制\");\n        }\n      } catch (err) {\n        ElMessage.error(\"复制失败，请手动复制\");\n      }\n    };\n\n    // 下载文本文件的通用函数\n    const downloadTextFile = (text, filename) => {\n      const blob = new Blob([text], {\n        type: \"text/plain\"\n      });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = filename;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n      ElMessage.success(`${filename} 已下载`);\n    };\n\n    // 复制公钥\n    const copyPublicKey = () => {\n      copyToClipboard(newKeyPair.publicKey, \"公钥已复制到剪贴板\");\n    };\n\n    // 复制私钥\n    const copyPrivateKey = () => {\n      copyToClipboard(newKeyPair.privateKey, \"私钥已复制到剪贴板\");\n    };\n\n    // 下载公钥\n    const downloadPublicKey = () => {\n      downloadTextFile(newKeyPair.publicKey, \"sm2_public_key.txt\");\n    };\n\n    // 下载私钥\n    const downloadPrivateKey = () => {\n      downloadTextFile(newKeyPair.privateKey, \"sm2_private_key.txt\");\n    };\n\n    // 保存新公钥到服务器\n    const saveNewPublicKey = async () => {\n      try {\n        await store.dispatch(\"user/updatePublicKey\", {\n          publicKey: newKeyPair.publicKey\n        });\n        ElMessage.success(\"公钥更新成功\");\n        await getUserInfo();\n      } catch (error) {\n        ElMessage.error(error.message || \"公钥更新失败\");\n      }\n    };\n\n    // 移除公钥\n    const removePublicKey = async () => {\n      try {\n        await ElMessageBox.confirm(\"移除公钥后将无法使用SM2证书登录，确定要继续吗？\", \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        });\n        await store.dispatch(\"user/removePublicKey\");\n        ElMessage.success(\"公钥已移除\");\n        await getUserInfo();\n      } catch (error) {\n        if (error !== \"cancel\") {\n          ElMessage.error(error.message || \"移除公钥失败\");\n        }\n      }\n    };\n    onMounted(() => {\n      getUserInfo();\n    });\n    return {\n      formRef,\n      passwordFormRef,\n      loading,\n      updating,\n      changingPassword,\n      editMode,\n      userInfo,\n      form,\n      rules,\n      passwordDialogVisible,\n      passwordForm,\n      passwordRules,\n      keyDialogVisible,\n      newKeyPair,\n      creditScoreColor,\n      formatCreditScore,\n      updateProfile,\n      showChangePasswordDialog,\n      changePassword,\n      showKeyManagementDialog,\n      generateNewKeyPair,\n      copyPublicKey,\n      copyPrivateKey,\n      downloadPublicKey,\n      downloadPrivateKey,\n      saveNewPublicKey,\n      removePublicKey,\n      DocumentCopy,\n      Download\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "useStore", "ElMessage", "ElMessageBox", "DocumentCopy", "Download", "SM2Crypto", "SM3Hasher", "name", "setup", "store", "formRef", "passwordFormRef", "loading", "updating", "changingPassword", "editMode", "passwordDialogVisible", "keyDialogVisible", "userInfo", "getters", "form", "email", "phone", "passwordForm", "oldPassword", "newPassword", "confirmPassword", "newKeyPair", "public<PERSON>ey", "privateKey", "creditScoreColor", "score", "value", "creditScore", "formatCreditScore", "percentage", "rules", "required", "message", "trigger", "type", "pattern", "validatePass", "rule", "callback", "Error", "length", "validateField", "validatePass2", "passwordRules", "validator", "getUserInfo", "dispatch", "error", "updateProfile", "validate", "valid", "success", "showChangePasswordDialog", "changePassword", "oldPasswordHash", "hash", "newPasswordHash", "showKeyManagementDialog", "generateNewKeyPair", "keyPair", "generateKeyPair", "copyToClipboard", "text", "successMsg", "navigator", "clipboard", "writeText", "then", "catch", "fallbackCopyToClipboard", "textArea", "document", "createElement", "style", "position", "left", "top", "body", "append<PERSON><PERSON><PERSON>", "focus", "select", "successful", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "err", "downloadTextFile", "filename", "blob", "Blob", "url", "URL", "createObjectURL", "a", "href", "download", "click", "revokeObjectURL", "copyPublicKey", "copyPrivateKey", "downloadPublicKey", "downloadPrivateKey", "saveNewPublicKey", "removePublic<PERSON>ey", "confirm", "confirmButtonText", "cancelButtonText"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\UserProfile.vue"], "sourcesContent": ["<template>\n  <div class=\"user-profile\">\n    <el-card class=\"profile-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h3>个人信息</h3>\n          <el-button type=\"primary\" @click=\"editMode = !editMode\">\n            {{ editMode ? \"取消编辑\" : \"编辑信息\" }}\n          </el-button>\n        </div>\n      </template>\n\n      <div v-if=\"loading\" class=\"loading-container\">\n        <el-skeleton :rows=\"5\" animated />\n      </div>\n\n      <div v-else>\n        <el-form\n          ref=\"formRef\"\n          :model=\"form\"\n          :rules=\"rules\"\n          label-width=\"100px\"\n          :disabled=\"!editMode\"\n        >\n          <el-form-item label=\"学号\">\n            <el-input v-model=\"userInfo.studentIdHash\" disabled />\n            <small class=\"form-hint\">学号信息不可修改</small>\n          </el-form-item>\n\n          <el-form-item label=\"邮箱\" prop=\"email\">\n            <el-input v-model=\"form.email\" />\n          </el-form-item>\n\n          <el-form-item label=\"手机号\" prop=\"phone\">\n            <el-input v-model=\"form.phone\" />\n          </el-form-item>\n\n          <el-form-item label=\"信誉分\">\n            <el-progress\n              :percentage=\"userInfo.creditScore\"\n              :color=\"creditScoreColor\"\n              :format=\"formatCreditScore\"\n              :stroke-width=\"18\"\n            />\n            <small class=\"form-hint\">信誉分影响您的预约权限</small>\n          </el-form-item>\n\n          <el-form-item label=\"注册时间\">\n            <el-input v-model=\"userInfo.createdAt\" disabled />\n          </el-form-item>\n\n          <el-form-item label=\"最后登录\">\n            <el-input v-model=\"userInfo.lastLogin\" disabled />\n          </el-form-item>\n\n          <el-form-item v-if=\"editMode\">\n            <el-button\n              type=\"primary\"\n              @click=\"updateProfile\"\n              :loading=\"updating\"\n            >\n              保存修改\n            </el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n    </el-card>\n\n    <el-card class=\"profile-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h3>安全设置</h3>\n        </div>\n      </template>\n\n      <div class=\"security-options\">\n        <div class=\"security-item\">\n          <div class=\"security-info\">\n            <h4>修改密码</h4>\n            <p>定期修改密码可以提高账号安全性</p>\n          </div>\n          <el-button @click=\"showChangePasswordDialog\">修改</el-button>\n        </div>\n\n        <div class=\"security-item\">\n          <div class=\"security-info\">\n            <h4>SM2密钥管理</h4>\n            <p>管理您的SM2密钥对，用于安全登录和数据签名</p>\n          </div>\n          <el-button @click=\"showKeyManagementDialog\">管理</el-button>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 修改密码对话框 -->\n    <el-dialog v-model=\"passwordDialogVisible\" title=\"修改密码\" width=\"400px\">\n      <el-form\n        ref=\"passwordFormRef\"\n        :model=\"passwordForm\"\n        :rules=\"passwordRules\"\n        label-width=\"100px\"\n      >\n        <el-form-item label=\"当前密码\" prop=\"oldPassword\">\n          <el-input\n            v-model=\"passwordForm.oldPassword\"\n            type=\"password\"\n            show-password\n          />\n        </el-form-item>\n\n        <el-form-item label=\"新密码\" prop=\"newPassword\">\n          <el-input\n            v-model=\"passwordForm.newPassword\"\n            type=\"password\"\n            show-password\n          />\n        </el-form-item>\n\n        <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\n          <el-input\n            v-model=\"passwordForm.confirmPassword\"\n            type=\"password\"\n            show-password\n          />\n        </el-form-item>\n      </el-form>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"passwordDialogVisible = false\">取消</el-button>\n          <el-button\n            type=\"primary\"\n            @click=\"changePassword\"\n            :loading=\"changingPassword\"\n          >\n            确认修改\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- SM2密钥管理对话框 -->\n    <el-dialog v-model=\"keyDialogVisible\" title=\"SM2密钥管理\" width=\"500px\">\n      <div class=\"key-management\">\n        <div class=\"key-status\">\n          <h4>当前状态</h4>\n          <p>\n            <el-tag :type=\"userInfo.hasPublicKey ? 'success' : 'danger'\">\n              {{ userInfo.hasPublicKey ? \"已设置SM2密钥\" : \"未设置SM2密钥\" }}\n            </el-tag>\n            <span v-if=\"userInfo.publicKeyExpires\">\n              (过期时间: {{ userInfo.publicKeyExpires }})\n            </span>\n          </p>\n        </div>\n\n        <div class=\"key-actions\">\n          <el-button type=\"primary\" @click=\"generateNewKeyPair\"\n            >生成新密钥对</el-button\n          >\n          <el-button\n            type=\"danger\"\n            @click=\"removePublicKey\"\n            :disabled=\"!userInfo.hasPublicKey\"\n          >\n            移除公钥\n          </el-button>\n        </div>\n\n        <div v-if=\"newKeyPair.publicKey\" class=\"new-key-pair\">\n          <h4>新生成的密钥对</h4>\n\n          <div class=\"key-box\">\n            <div class=\"key-label\">\n              <span>公钥</span>\n              <div class=\"key-actions-buttons\">\n                <el-button type=\"primary\" size=\"small\" @click=\"copyPublicKey\">\n                  <el-icon><DocumentCopy /></el-icon> 复制\n                </el-button>\n                <el-button type=\"success\" size=\"small\" @click=\"downloadPublicKey\">\n                  <el-icon><Download /></el-icon> 下载\n                </el-button>\n              </div>\n            </div>\n            <el-input\n              v-model=\"newKeyPair.publicKey\"\n              type=\"textarea\"\n              :rows=\"3\"\n              readonly\n            />\n          </div>\n\n          <div class=\"key-box\">\n            <div class=\"key-label\">\n              <span>私钥</span>\n              <div class=\"key-actions-buttons\">\n                <el-button type=\"primary\" size=\"small\" @click=\"copyPrivateKey\">\n                  <el-icon><DocumentCopy /></el-icon> 复制\n                </el-button>\n                <el-button type=\"success\" size=\"small\" @click=\"downloadPrivateKey\">\n                  <el-icon><Download /></el-icon> 下载\n                </el-button>\n              </div>\n            </div>\n            <el-input\n              v-model=\"newKeyPair.privateKey\"\n              type=\"textarea\"\n              :rows=\"3\"\n              readonly\n              show-password\n            />\n          </div>\n\n          <el-alert\n            title=\"重要提示\"\n            type=\"warning\"\n            description=\"请务必保存您的私钥，私钥将不会存储在服务器上，丢失后无法找回。\"\n            show-icon\n            :closable=\"false\"\n            class=\"key-warning\"\n          />\n\n          <div class=\"save-key-action\">\n            <el-button type=\"success\" @click=\"saveNewPublicKey\"\n              >保存新公钥到服务器</el-button\n            >\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nimport { DocumentCopy, Download } from \"@element-plus/icons-vue\";\nimport { SM2Crypto, SM3Hasher } from \"@/utils/crypto\";\n\nexport default {\n  name: \"UserProfile\",\n  setup() {\n    const store = useStore();\n    const formRef = ref(null);\n    const passwordFormRef = ref(null);\n\n    const loading = ref(true);\n    const updating = ref(false);\n    const changingPassword = ref(false);\n    const editMode = ref(false);\n\n    const passwordDialogVisible = ref(false);\n    const keyDialogVisible = ref(false);\n\n    // 用户信息\n    const userInfo = computed(() => store.getters[\"user/userInfo\"]);\n\n    // 编辑表单\n    const form = reactive({\n      email: \"\",\n      phone: \"\",\n    });\n\n    // 修改密码表单\n    const passwordForm = reactive({\n      oldPassword: \"\",\n      newPassword: \"\",\n      confirmPassword: \"\",\n    });\n\n    // 新密钥对\n    const newKeyPair = reactive({\n      publicKey: \"\",\n      privateKey: \"\",\n    });\n\n    // 信誉分颜色\n    const creditScoreColor = computed(() => {\n      const score = userInfo.value.creditScore || 0;\n      if (score >= 90) return \"#67c23a\";\n      if (score >= 70) return \"#409eff\";\n      if (score >= 50) return \"#e6a23c\";\n      return \"#f56c6c\";\n    });\n\n    // 格式化信誉分\n    const formatCreditScore = (percentage) => {\n      return `${percentage}分`;\n    };\n\n    // 表单验证规则\n    const rules = {\n      email: [\n        { required: true, message: \"请输入邮箱\", trigger: \"blur\" },\n        { type: \"email\", message: \"请输入正确的邮箱格式\", trigger: \"blur\" },\n      ],\n      phone: [\n        { required: true, message: \"请输入手机号\", trigger: \"blur\" },\n        {\n          pattern: /^1[3-9]\\d{9}$/,\n          message: \"请输入正确的手机号格式\",\n          trigger: \"blur\",\n        },\n      ],\n    };\n\n    // 密码表单验证规则\n    const validatePass = (rule, value, callback) => {\n      if (value === \"\") {\n        callback(new Error(\"请输入新密码\"));\n      } else if (value.length < 6) {\n        callback(new Error(\"密码长度不能小于6位\"));\n      } else {\n        if (passwordForm.confirmPassword !== \"\") {\n          passwordFormRef.value.validateField(\"confirmPassword\");\n        }\n        callback();\n      }\n    };\n\n    const validatePass2 = (rule, value, callback) => {\n      if (value === \"\") {\n        callback(new Error(\"请再次输入新密码\"));\n      } else if (value !== passwordForm.newPassword) {\n        callback(new Error(\"两次输入密码不一致\"));\n      } else {\n        callback();\n      }\n    };\n\n    const passwordRules = {\n      oldPassword: [\n        { required: true, message: \"请输入当前密码\", trigger: \"blur\" },\n      ],\n      newPassword: [{ validator: validatePass, trigger: \"blur\" }],\n      confirmPassword: [{ validator: validatePass2, trigger: \"blur\" }],\n    };\n\n    // 获取用户信息\n    const getUserInfo = async () => {\n      try {\n        loading.value = true;\n        await store.dispatch(\"user/getUserInfo\");\n\n        // 填充表单\n        form.email = userInfo.value.email || \"\";\n        form.phone = userInfo.value.phone || \"\";\n      } catch (error) {\n        ElMessage.error(\"获取用户信息失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 更新个人信息\n    const updateProfile = async () => {\n      if (!formRef.value) return;\n\n      await formRef.value.validate(async (valid) => {\n        if (valid) {\n          try {\n            updating.value = true;\n            await store.dispatch(\"user/updateProfile\", form);\n            ElMessage.success(\"个人信息更新成功\");\n            editMode.value = false;\n          } catch (error) {\n            ElMessage.error(error.message || \"更新失败\");\n          } finally {\n            updating.value = false;\n          }\n        }\n      });\n    };\n\n    // 显示修改密码对话框\n    const showChangePasswordDialog = () => {\n      passwordDialogVisible.value = true;\n      passwordForm.oldPassword = \"\";\n      passwordForm.newPassword = \"\";\n      passwordForm.confirmPassword = \"\";\n    };\n\n    // 修改密码\n    const changePassword = async () => {\n      if (!passwordFormRef.value) return;\n\n      await passwordFormRef.value.validate(async (valid) => {\n        if (valid) {\n          try {\n            changingPassword.value = true;\n\n            // 对密码进行SM3哈希\n            const oldPasswordHash = SM3Hasher.hash(passwordForm.oldPassword);\n            const newPasswordHash = SM3Hasher.hash(passwordForm.newPassword);\n\n            await store.dispatch(\"user/changePassword\", {\n              oldPassword: oldPasswordHash,\n              newPassword: newPasswordHash,\n            });\n\n            ElMessage.success(\"密码修改成功\");\n            passwordDialogVisible.value = false;\n          } catch (error) {\n            ElMessage.error(error.message || \"密码修改失败\");\n          } finally {\n            changingPassword.value = false;\n          }\n        }\n      });\n    };\n\n    // 显示密钥管理对话框\n    const showKeyManagementDialog = () => {\n      keyDialogVisible.value = true;\n      newKeyPair.publicKey = \"\";\n      newKeyPair.privateKey = \"\";\n    };\n\n    // 生成新密钥对\n    const generateNewKeyPair = () => {\n      const keyPair = SM2Crypto.generateKeyPair();\n      newKeyPair.publicKey = keyPair.publicKey;\n      newKeyPair.privateKey = keyPair.privateKey;\n    };\n\n    // 复制文本到剪贴板的通用函数\n    const copyToClipboard = (text, successMsg) => {\n      // 检查navigator.clipboard API是否可用\n      if (navigator.clipboard && navigator.clipboard.writeText) {\n        navigator.clipboard\n          .writeText(text)\n          .then(() => {\n            ElMessage.success(successMsg);\n          })\n          .catch(() => {\n            // 如果API调用失败，使用备用方法\n            fallbackCopyToClipboard(text, successMsg);\n          });\n      } else {\n        // 如果API不可用，使用备用方法\n        fallbackCopyToClipboard(text, successMsg);\n      }\n    };\n\n    // 备用复制方法（使用临时DOM元素）\n    const fallbackCopyToClipboard = (text, successMsg) => {\n      try {\n        // 创建临时textarea元素\n        const textArea = document.createElement(\"textarea\");\n        textArea.value = text;\n\n        // 设置样式使其不可见\n        textArea.style.position = \"fixed\";\n        textArea.style.left = \"-999999px\";\n        textArea.style.top = \"-999999px\";\n        document.body.appendChild(textArea);\n\n        // 选择文本并复制\n        textArea.focus();\n        textArea.select();\n        const successful = document.execCommand(\"copy\");\n\n        // 移除临时元素\n        document.body.removeChild(textArea);\n\n        if (successful) {\n          ElMessage.success(successMsg);\n        } else {\n          ElMessage.error(\"复制失败，请手动复制\");\n        }\n      } catch (err) {\n        ElMessage.error(\"复制失败，请手动复制\");\n      }\n    };\n\n    // 下载文本文件的通用函数\n    const downloadTextFile = (text, filename) => {\n      const blob = new Blob([text], { type: \"text/plain\" });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = filename;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n      ElMessage.success(`${filename} 已下载`);\n    };\n\n    // 复制公钥\n    const copyPublicKey = () => {\n      copyToClipboard(newKeyPair.publicKey, \"公钥已复制到剪贴板\");\n    };\n\n    // 复制私钥\n    const copyPrivateKey = () => {\n      copyToClipboard(newKeyPair.privateKey, \"私钥已复制到剪贴板\");\n    };\n\n    // 下载公钥\n    const downloadPublicKey = () => {\n      downloadTextFile(newKeyPair.publicKey, \"sm2_public_key.txt\");\n    };\n\n    // 下载私钥\n    const downloadPrivateKey = () => {\n      downloadTextFile(newKeyPair.privateKey, \"sm2_private_key.txt\");\n    };\n\n    // 保存新公钥到服务器\n    const saveNewPublicKey = async () => {\n      try {\n        await store.dispatch(\"user/updatePublicKey\", {\n          publicKey: newKeyPair.publicKey,\n        });\n\n        ElMessage.success(\"公钥更新成功\");\n        await getUserInfo();\n      } catch (error) {\n        ElMessage.error(error.message || \"公钥更新失败\");\n      }\n    };\n\n    // 移除公钥\n    const removePublicKey = async () => {\n      try {\n        await ElMessageBox.confirm(\n          \"移除公钥后将无法使用SM2证书登录，确定要继续吗？\",\n          \"警告\",\n          {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          }\n        );\n\n        await store.dispatch(\"user/removePublicKey\");\n        ElMessage.success(\"公钥已移除\");\n        await getUserInfo();\n      } catch (error) {\n        if (error !== \"cancel\") {\n          ElMessage.error(error.message || \"移除公钥失败\");\n        }\n      }\n    };\n\n    onMounted(() => {\n      getUserInfo();\n    });\n\n    return {\n      formRef,\n      passwordFormRef,\n      loading,\n      updating,\n      changingPassword,\n      editMode,\n      userInfo,\n      form,\n      rules,\n      passwordDialogVisible,\n      passwordForm,\n      passwordRules,\n      keyDialogVisible,\n      newKeyPair,\n      creditScoreColor,\n      formatCreditScore,\n      updateProfile,\n      showChangePasswordDialog,\n      changePassword,\n      showKeyManagementDialog,\n      generateNewKeyPair,\n      copyPublicKey,\n      copyPrivateKey,\n      downloadPublicKey,\n      downloadPrivateKey,\n      saveNewPublicKey,\n      removePublicKey,\n      DocumentCopy,\n      Download,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.user-profile {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.profile-card {\n  margin-bottom: 20px;\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    h3 {\n      margin: 0;\n    }\n  }\n}\n\n.loading-container {\n  padding: 20px 0;\n}\n\n.form-hint {\n  display: block;\n  margin-top: 5px;\n  color: #909399;\n  font-size: 12px;\n}\n\n.security-options {\n  .security-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 15px 0;\n    border-bottom: 1px solid #ebeef5;\n\n    &:last-child {\n      border-bottom: none;\n    }\n\n    .security-info {\n      h4 {\n        margin: 0 0 5px 0;\n      }\n\n      p {\n        margin: 0;\n        color: #606266;\n        font-size: 14px;\n      }\n    }\n  }\n}\n\n.key-management {\n  .key-status {\n    margin-bottom: 20px;\n\n    h4 {\n      margin: 0 0 10px 0;\n    }\n  }\n\n  .key-actions {\n    display: flex;\n    gap: 10px;\n    margin-bottom: 20px;\n  }\n\n  .new-key-pair {\n    margin-top: 20px;\n\n    h4 {\n      margin: 0 0 10px 0;\n    }\n\n    .key-box {\n      margin-bottom: 15px;\n\n      .key-label {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 5px;\n        font-weight: bold;\n      }\n    }\n\n    .key-warning {\n      margin: 15px 0;\n    }\n\n    .save-key-action {\n      margin-top: 20px;\n      text-align: center;\n    }\n  }\n}\n</style>\n"], "mappings": "AA0OA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAK;AACxD,SAASC,QAAO,QAAS,MAAM;AAC/B,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAc;AACtD,SAASC,YAAY,EAAEC,QAAO,QAAS,yBAAyB;AAChE,SAASC,SAAS,EAAEC,SAAQ,QAAS,gBAAgB;AAErD,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIT,QAAQ,CAAC,CAAC;IACxB,MAAMU,OAAM,GAAId,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMe,eAAc,GAAIf,GAAG,CAAC,IAAI,CAAC;IAEjC,MAAMgB,OAAM,GAAIhB,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMiB,QAAO,GAAIjB,GAAG,CAAC,KAAK,CAAC;IAC3B,MAAMkB,gBAAe,GAAIlB,GAAG,CAAC,KAAK,CAAC;IACnC,MAAMmB,QAAO,GAAInB,GAAG,CAAC,KAAK,CAAC;IAE3B,MAAMoB,qBAAoB,GAAIpB,GAAG,CAAC,KAAK,CAAC;IACxC,MAAMqB,gBAAe,GAAIrB,GAAG,CAAC,KAAK,CAAC;;IAEnC;IACA,MAAMsB,QAAO,GAAIpB,QAAQ,CAAC,MAAMW,KAAK,CAACU,OAAO,CAAC,eAAe,CAAC,CAAC;;IAE/D;IACA,MAAMC,IAAG,GAAIvB,QAAQ,CAAC;MACpBwB,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE;IACT,CAAC,CAAC;;IAEF;IACA,MAAMC,YAAW,GAAI1B,QAAQ,CAAC;MAC5B2B,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE;IACnB,CAAC,CAAC;;IAEF;IACA,MAAMC,UAAS,GAAI9B,QAAQ,CAAC;MAC1B+B,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE;IACd,CAAC,CAAC;;IAEF;IACA,MAAMC,gBAAe,GAAIhC,QAAQ,CAAC,MAAM;MACtC,MAAMiC,KAAI,GAAIb,QAAQ,CAACc,KAAK,CAACC,WAAU,IAAK,CAAC;MAC7C,IAAIF,KAAI,IAAK,EAAE,EAAE,OAAO,SAAS;MACjC,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,SAAS;MACjC,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,SAAS;MACjC,OAAO,SAAS;IAClB,CAAC,CAAC;;IAEF;IACA,MAAMG,iBAAgB,GAAKC,UAAU,IAAK;MACxC,OAAO,GAAGA,UAAU,GAAG;IACzB,CAAC;;IAED;IACA,MAAMC,KAAI,GAAI;MACZf,KAAK,EAAE,CACL;QAAEgB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,IAAI,EAAE,OAAO;QAAEF,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,CAAC,CAC1D;MACDjB,KAAK,EAAE,CACL;QAAEe,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QACEE,OAAO,EAAE,eAAe;QACxBH,OAAO,EAAE,aAAa;QACtBC,OAAO,EAAE;MACX,CAAC;IAEL,CAAC;;IAED;IACA,MAAMG,YAAW,GAAIA,CAACC,IAAI,EAAEX,KAAK,EAAEY,QAAQ,KAAK;MAC9C,IAAIZ,KAAI,KAAM,EAAE,EAAE;QAChBY,QAAQ,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;MAC/B,OAAO,IAAIb,KAAK,CAACc,MAAK,GAAI,CAAC,EAAE;QAC3BF,QAAQ,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;MACnC,OAAO;QACL,IAAItB,YAAY,CAACG,eAAc,KAAM,EAAE,EAAE;UACvCf,eAAe,CAACqB,KAAK,CAACe,aAAa,CAAC,iBAAiB,CAAC;QACxD;QACAH,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC;IAED,MAAMI,aAAY,GAAIA,CAACL,IAAI,EAAEX,KAAK,EAAEY,QAAQ,KAAK;MAC/C,IAAIZ,KAAI,KAAM,EAAE,EAAE;QAChBY,QAAQ,CAAC,IAAIC,KAAK,CAAC,UAAU,CAAC,CAAC;MACjC,OAAO,IAAIb,KAAI,KAAMT,YAAY,CAACE,WAAW,EAAE;QAC7CmB,QAAQ,CAAC,IAAIC,KAAK,CAAC,WAAW,CAAC,CAAC;MAClC,OAAO;QACLD,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC;IAED,MAAMK,aAAY,GAAI;MACpBzB,WAAW,EAAE,CACX;QAAEa,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,CACxD;MACDd,WAAW,EAAE,CAAC;QAAEyB,SAAS,EAAER,YAAY;QAAEH,OAAO,EAAE;MAAO,CAAC,CAAC;MAC3Db,eAAe,EAAE,CAAC;QAAEwB,SAAS,EAAEF,aAAa;QAAET,OAAO,EAAE;MAAO,CAAC;IACjE,CAAC;;IAED;IACA,MAAMY,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFvC,OAAO,CAACoB,KAAI,GAAI,IAAI;QACpB,MAAMvB,KAAK,CAAC2C,QAAQ,CAAC,kBAAkB,CAAC;;QAExC;QACAhC,IAAI,CAACC,KAAI,GAAIH,QAAQ,CAACc,KAAK,CAACX,KAAI,IAAK,EAAE;QACvCD,IAAI,CAACE,KAAI,GAAIJ,QAAQ,CAACc,KAAK,CAACV,KAAI,IAAK,EAAE;MACzC,EAAE,OAAO+B,KAAK,EAAE;QACdpD,SAAS,CAACoD,KAAK,CAAC,UAAU,CAAC;MAC7B,UAAU;QACRzC,OAAO,CAACoB,KAAI,GAAI,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAMsB,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI,CAAC5C,OAAO,CAACsB,KAAK,EAAE;MAEpB,MAAMtB,OAAO,CAACsB,KAAK,CAACuB,QAAQ,CAAC,MAAOC,KAAK,IAAK;QAC5C,IAAIA,KAAK,EAAE;UACT,IAAI;YACF3C,QAAQ,CAACmB,KAAI,GAAI,IAAI;YACrB,MAAMvB,KAAK,CAAC2C,QAAQ,CAAC,oBAAoB,EAAEhC,IAAI,CAAC;YAChDnB,SAAS,CAACwD,OAAO,CAAC,UAAU,CAAC;YAC7B1C,QAAQ,CAACiB,KAAI,GAAI,KAAK;UACxB,EAAE,OAAOqB,KAAK,EAAE;YACdpD,SAAS,CAACoD,KAAK,CAACA,KAAK,CAACf,OAAM,IAAK,MAAM,CAAC;UAC1C,UAAU;YACRzB,QAAQ,CAACmB,KAAI,GAAI,KAAK;UACxB;QACF;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAM0B,wBAAuB,GAAIA,CAAA,KAAM;MACrC1C,qBAAqB,CAACgB,KAAI,GAAI,IAAI;MAClCT,YAAY,CAACC,WAAU,GAAI,EAAE;MAC7BD,YAAY,CAACE,WAAU,GAAI,EAAE;MAC7BF,YAAY,CAACG,eAAc,GAAI,EAAE;IACnC,CAAC;;IAED;IACA,MAAMiC,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI,CAAChD,eAAe,CAACqB,KAAK,EAAE;MAE5B,MAAMrB,eAAe,CAACqB,KAAK,CAACuB,QAAQ,CAAC,MAAOC,KAAK,IAAK;QACpD,IAAIA,KAAK,EAAE;UACT,IAAI;YACF1C,gBAAgB,CAACkB,KAAI,GAAI,IAAI;;YAE7B;YACA,MAAM4B,eAAc,GAAItD,SAAS,CAACuD,IAAI,CAACtC,YAAY,CAACC,WAAW,CAAC;YAChE,MAAMsC,eAAc,GAAIxD,SAAS,CAACuD,IAAI,CAACtC,YAAY,CAACE,WAAW,CAAC;YAEhE,MAAMhB,KAAK,CAAC2C,QAAQ,CAAC,qBAAqB,EAAE;cAC1C5B,WAAW,EAAEoC,eAAe;cAC5BnC,WAAW,EAAEqC;YACf,CAAC,CAAC;YAEF7D,SAAS,CAACwD,OAAO,CAAC,QAAQ,CAAC;YAC3BzC,qBAAqB,CAACgB,KAAI,GAAI,KAAK;UACrC,EAAE,OAAOqB,KAAK,EAAE;YACdpD,SAAS,CAACoD,KAAK,CAACA,KAAK,CAACf,OAAM,IAAK,QAAQ,CAAC;UAC5C,UAAU;YACRxB,gBAAgB,CAACkB,KAAI,GAAI,KAAK;UAChC;QACF;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAM+B,uBAAsB,GAAIA,CAAA,KAAM;MACpC9C,gBAAgB,CAACe,KAAI,GAAI,IAAI;MAC7BL,UAAU,CAACC,SAAQ,GAAI,EAAE;MACzBD,UAAU,CAACE,UAAS,GAAI,EAAE;IAC5B,CAAC;;IAED;IACA,MAAMmC,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMC,OAAM,GAAI5D,SAAS,CAAC6D,eAAe,CAAC,CAAC;MAC3CvC,UAAU,CAACC,SAAQ,GAAIqC,OAAO,CAACrC,SAAS;MACxCD,UAAU,CAACE,UAAS,GAAIoC,OAAO,CAACpC,UAAU;IAC5C,CAAC;;IAED;IACA,MAAMsC,eAAc,GAAIA,CAACC,IAAI,EAAEC,UAAU,KAAK;MAC5C;MACA,IAAIC,SAAS,CAACC,SAAQ,IAAKD,SAAS,CAACC,SAAS,CAACC,SAAS,EAAE;QACxDF,SAAS,CAACC,SAAQ,CACfC,SAAS,CAACJ,IAAI,EACdK,IAAI,CAAC,MAAM;UACVxE,SAAS,CAACwD,OAAO,CAACY,UAAU,CAAC;QAC/B,CAAC,EACAK,KAAK,CAAC,MAAM;UACX;UACAC,uBAAuB,CAACP,IAAI,EAAEC,UAAU,CAAC;QAC3C,CAAC,CAAC;MACN,OAAO;QACL;QACAM,uBAAuB,CAACP,IAAI,EAAEC,UAAU,CAAC;MAC3C;IACF,CAAC;;IAED;IACA,MAAMM,uBAAsB,GAAIA,CAACP,IAAI,EAAEC,UAAU,KAAK;MACpD,IAAI;QACF;QACA,MAAMO,QAAO,GAAIC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;QACnDF,QAAQ,CAAC5C,KAAI,GAAIoC,IAAI;;QAErB;QACAQ,QAAQ,CAACG,KAAK,CAACC,QAAO,GAAI,OAAO;QACjCJ,QAAQ,CAACG,KAAK,CAACE,IAAG,GAAI,WAAW;QACjCL,QAAQ,CAACG,KAAK,CAACG,GAAE,GAAI,WAAW;QAChCL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,QAAQ,CAAC;;QAEnC;QACAA,QAAQ,CAACS,KAAK,CAAC,CAAC;QAChBT,QAAQ,CAACU,MAAM,CAAC,CAAC;QACjB,MAAMC,UAAS,GAAIV,QAAQ,CAACW,WAAW,CAAC,MAAM,CAAC;;QAE/C;QACAX,QAAQ,CAACM,IAAI,CAACM,WAAW,CAACb,QAAQ,CAAC;QAEnC,IAAIW,UAAU,EAAE;UACdtF,SAAS,CAACwD,OAAO,CAACY,UAAU,CAAC;QAC/B,OAAO;UACLpE,SAAS,CAACoD,KAAK,CAAC,YAAY,CAAC;QAC/B;MACF,EAAE,OAAOqC,GAAG,EAAE;QACZzF,SAAS,CAACoD,KAAK,CAAC,YAAY,CAAC;MAC/B;IACF,CAAC;;IAED;IACA,MAAMsC,gBAAe,GAAIA,CAACvB,IAAI,EAAEwB,QAAQ,KAAK;MAC3C,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAAC,CAAC1B,IAAI,CAAC,EAAE;QAAE5B,IAAI,EAAE;MAAa,CAAC,CAAC;MACrD,MAAMuD,GAAE,GAAIC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACrC,MAAMK,CAAA,GAAIrB,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCoB,CAAC,CAACC,IAAG,GAAIJ,GAAG;MACZG,CAAC,CAACE,QAAO,GAAIR,QAAQ;MACrBf,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACc,CAAC,CAAC;MAC5BA,CAAC,CAACG,KAAK,CAAC,CAAC;MACTxB,QAAQ,CAACM,IAAI,CAACM,WAAW,CAACS,CAAC,CAAC;MAC5BF,GAAG,CAACM,eAAe,CAACP,GAAG,CAAC;MACxB9F,SAAS,CAACwD,OAAO,CAAC,GAAGmC,QAAQ,MAAM,CAAC;IACtC,CAAC;;IAED;IACA,MAAMW,aAAY,GAAIA,CAAA,KAAM;MAC1BpC,eAAe,CAACxC,UAAU,CAACC,SAAS,EAAE,WAAW,CAAC;IACpD,CAAC;;IAED;IACA,MAAM4E,cAAa,GAAIA,CAAA,KAAM;MAC3BrC,eAAe,CAACxC,UAAU,CAACE,UAAU,EAAE,WAAW,CAAC;IACrD,CAAC;;IAED;IACA,MAAM4E,iBAAgB,GAAIA,CAAA,KAAM;MAC9Bd,gBAAgB,CAAChE,UAAU,CAACC,SAAS,EAAE,oBAAoB,CAAC;IAC9D,CAAC;;IAED;IACA,MAAM8E,kBAAiB,GAAIA,CAAA,KAAM;MAC/Bf,gBAAgB,CAAChE,UAAU,CAACE,UAAU,EAAE,qBAAqB,CAAC;IAChE,CAAC;;IAED;IACA,MAAM8E,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMlG,KAAK,CAAC2C,QAAQ,CAAC,sBAAsB,EAAE;UAC3CxB,SAAS,EAAED,UAAU,CAACC;QACxB,CAAC,CAAC;QAEF3B,SAAS,CAACwD,OAAO,CAAC,QAAQ,CAAC;QAC3B,MAAMN,WAAW,CAAC,CAAC;MACrB,EAAE,OAAOE,KAAK,EAAE;QACdpD,SAAS,CAACoD,KAAK,CAACA,KAAK,CAACf,OAAM,IAAK,QAAQ,CAAC;MAC5C;IACF,CAAC;;IAED;IACA,MAAMsE,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAM1G,YAAY,CAAC2G,OAAO,CACxB,2BAA2B,EAC3B,IAAI,EACJ;UACEC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBvE,IAAI,EAAE;QACR,CACF,CAAC;QAED,MAAM/B,KAAK,CAAC2C,QAAQ,CAAC,sBAAsB,CAAC;QAC5CnD,SAAS,CAACwD,OAAO,CAAC,OAAO,CAAC;QAC1B,MAAMN,WAAW,CAAC,CAAC;MACrB,EAAE,OAAOE,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBpD,SAAS,CAACoD,KAAK,CAACA,KAAK,CAACf,OAAM,IAAK,QAAQ,CAAC;QAC5C;MACF;IACF,CAAC;IAEDvC,SAAS,CAAC,MAAM;MACdoD,WAAW,CAAC,CAAC;IACf,CAAC,CAAC;IAEF,OAAO;MACLzC,OAAO;MACPC,eAAe;MACfC,OAAO;MACPC,QAAQ;MACRC,gBAAgB;MAChBC,QAAQ;MACRG,QAAQ;MACRE,IAAI;MACJgB,KAAK;MACLpB,qBAAqB;MACrBO,YAAY;MACZ0B,aAAa;MACbhC,gBAAgB;MAChBU,UAAU;MACVG,gBAAgB;MAChBI,iBAAiB;MACjBoB,aAAa;MACbI,wBAAwB;MACxBC,cAAc;MACdI,uBAAuB;MACvBC,kBAAkB;MAClBuC,aAAa;MACbC,cAAc;MACdC,iBAAiB;MACjBC,kBAAkB;MAClBC,gBAAgB;MAChBC,eAAe;MACfzG,YAAY;MACZC;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}