{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { usePropsAlias } from './use-props-alias.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../../constants/event.mjs';\nconst useMove = (props, checkedState, emit) => {\n  const propsAlias = usePropsAlias(props);\n  const _emit = (value, direction, movedKeys) => {\n    emit(UPDATE_MODEL_EVENT, value);\n    emit(CHANGE_EVENT, value, direction, movedKeys);\n  };\n  const addToLeft = () => {\n    const currentValue = props.modelValue.slice();\n    checkedState.rightChecked.forEach(item => {\n      const index = currentValue.indexOf(item);\n      if (index > -1) {\n        currentValue.splice(index, 1);\n      }\n    });\n    _emit(currentValue, \"left\", checkedState.rightChecked);\n  };\n  const addToRight = () => {\n    let currentValue = props.modelValue.slice();\n    const itemsToBeMoved = props.data.filter(item => {\n      const itemKey = item[propsAlias.value.key];\n      return checkedState.leftChecked.includes(itemKey) && !props.modelValue.includes(itemKey);\n    }).map(item => item[propsAlias.value.key]);\n    currentValue = props.targetOrder === \"unshift\" ? itemsToBeMoved.concat(currentValue) : currentValue.concat(itemsToBeMoved);\n    if (props.targetOrder === \"original\") {\n      currentValue = props.data.filter(item => currentValue.includes(item[propsAlias.value.key])).map(item => item[propsAlias.value.key]);\n    }\n    _emit(currentValue, \"right\", checkedState.leftChecked);\n  };\n  return {\n    addToLeft,\n    addToRight\n  };\n};\nexport { useMove };", "map": {"version": 3, "names": ["useMove", "props", "checkedState", "emit", "props<PERSON><PERSON><PERSON>", "usePropsAlias", "_emit", "value", "direction", "<PERSON><PERSON><PERSON><PERSON>", "UPDATE_MODEL_EVENT", "CHANGE_EVENT", "addToLeft", "currentValue", "modelValue", "slice", "rightChecked", "for<PERSON>ach", "item", "index", "indexOf", "splice", "addToRight", "itemsToBeMoved", "data", "filter", "itemKey", "key", "leftChecked", "includes", "map", "targetOrder", "concat"], "sources": ["../../../../../../../packages/components/transfer/src/composables/use-move.ts"], "sourcesContent": ["import { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { usePropsAlias } from './use-props-alias'\n\nimport type { SetupContext } from 'vue'\nimport type {\n  TransferCheckedState,\n  TransferDataItem,\n  TransferDirection,\n  TransferEmits,\n  TransferKey,\n  TransferProps,\n} from '../transfer'\n\nexport const useMove = (\n  props: TransferProps,\n  checkedState: TransferCheckedState,\n  emit: SetupContext<TransferEmits>['emit']\n) => {\n  const propsAlias = usePropsAlias(props)\n\n  const _emit = (\n    value: TransferKey[],\n    direction: TransferDirection,\n    movedKeys: TransferKey[]\n  ) => {\n    emit(UPDATE_MODEL_EVENT, value)\n    emit(CHANGE_EVENT, value, direction, movedKeys)\n  }\n\n  const addToLeft = () => {\n    const currentValue = props.modelValue.slice()\n\n    checkedState.rightChecked.forEach((item) => {\n      const index = currentValue.indexOf(item)\n      if (index > -1) {\n        currentValue.splice(index, 1)\n      }\n    })\n    _emit(currentValue, 'left', checkedState.rightChecked)\n  }\n\n  const addToRight = () => {\n    let currentValue = props.modelValue.slice()\n\n    const itemsToBeMoved = props.data\n      .filter((item: TransferDataItem) => {\n        const itemKey = item[propsAlias.value.key]\n        return (\n          checkedState.leftChecked.includes(itemKey) &&\n          !props.modelValue.includes(itemKey)\n        )\n      })\n      .map((item) => item[propsAlias.value.key])\n\n    currentValue =\n      props.targetOrder === 'unshift'\n        ? itemsToBeMoved.concat(currentValue)\n        : currentValue.concat(itemsToBeMoved)\n\n    if (props.targetOrder === 'original') {\n      currentValue = props.data\n        .filter((item) => currentValue.includes(item[propsAlias.value.key]))\n        .map((item) => item[propsAlias.value.key])\n    }\n\n    _emit(currentValue, 'right', checkedState.leftChecked)\n  }\n\n  return {\n    addToLeft,\n    addToRight,\n  }\n}\n"], "mappings": ";;;;;;AAEY,MAACA,OAAO,GAAGA,CAACC,KAAK,EAAEC,YAAY,EAAEC,IAAI,KAAK;EACpD,MAAMC,UAAU,GAAGC,aAAa,CAACJ,KAAK,CAAC;EACvC,MAAMK,KAAK,GAAGA,CAACC,KAAK,EAAEC,SAAS,EAAEC,SAAS,KAAK;IAC7CN,IAAI,CAACO,kBAAkB,EAAEH,KAAK,CAAC;IAC/BJ,IAAI,CAACQ,YAAY,EAAEJ,KAAK,EAAEC,SAAS,EAAEC,SAAS,CAAC;EACnD,CAAG;EACD,MAAMG,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMC,YAAY,GAAGZ,KAAK,CAACa,UAAU,CAACC,KAAK,EAAE;IAC7Cb,YAAY,CAACc,YAAY,CAACC,OAAO,CAAEC,IAAI,IAAK;MAC1C,MAAMC,KAAK,GAAGN,YAAY,CAACO,OAAO,CAACF,IAAI,CAAC;MACxC,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QACdN,YAAY,CAACQ,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACrC;IACA,CAAK,CAAC;IACFb,KAAK,CAACO,YAAY,EAAE,MAAM,EAAEX,YAAY,CAACc,YAAY,CAAC;EAC1D,CAAG;EACD,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIT,YAAY,GAAGZ,KAAK,CAACa,UAAU,CAACC,KAAK,EAAE;IAC3C,MAAMQ,cAAc,GAAGtB,KAAK,CAACuB,IAAI,CAACC,MAAM,CAAEP,IAAI,IAAK;MACjD,MAAMQ,OAAO,GAAGR,IAAI,CAACd,UAAU,CAACG,KAAK,CAACoB,GAAG,CAAC;MAC1C,OAAOzB,YAAY,CAAC0B,WAAW,CAACC,QAAQ,CAACH,OAAO,CAAC,IAAI,CAACzB,KAAK,CAACa,UAAU,CAACe,QAAQ,CAACH,OAAO,CAAC;IAC9F,CAAK,CAAC,CAACI,GAAG,CAAEZ,IAAI,IAAKA,IAAI,CAACd,UAAU,CAACG,KAAK,CAACoB,GAAG,CAAC,CAAC;IAC5Cd,YAAY,GAAGZ,KAAK,CAAC8B,WAAW,KAAK,SAAS,GAAGR,cAAc,CAACS,MAAM,CAACnB,YAAY,CAAC,GAAGA,YAAY,CAACmB,MAAM,CAACT,cAAc,CAAC;IAC1H,IAAItB,KAAK,CAAC8B,WAAW,KAAK,UAAU,EAAE;MACpClB,YAAY,GAAGZ,KAAK,CAACuB,IAAI,CAACC,MAAM,CAAEP,IAAI,IAAKL,YAAY,CAACgB,QAAQ,CAACX,IAAI,CAACd,UAAU,CAACG,KAAK,CAACoB,GAAG,CAAC,CAAC,CAAC,CAACG,GAAG,CAAEZ,IAAI,IAAKA,IAAI,CAACd,UAAU,CAACG,KAAK,CAACoB,GAAG,CAAC,CAAC;IAC7I;IACIrB,KAAK,CAACO,YAAY,EAAE,OAAO,EAAEX,YAAY,CAAC0B,WAAW,CAAC;EAC1D,CAAG;EACD,OAAO;IACLhB,SAAS;IACTU;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}