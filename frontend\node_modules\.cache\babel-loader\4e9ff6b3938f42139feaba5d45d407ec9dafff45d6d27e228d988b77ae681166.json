{"ast": null, "code": "import { ref, onMounted } from \"vue\";\nexport default {\n  name: \"Dashboard\",\n  setup() {\n    const availableSeats = ref(0);\n    const myReservations = ref(0);\n    const creditScore = ref(100);\n    onMounted(async () => {\n      try {\n        // 这里应该是从后端获取数据\n        // 暂时使用模拟数据\n        availableSeats.value = 120;\n        myReservations.value = 2;\n        creditScore.value = 95;\n      } catch (error) {\n        console.error(\"获取数据失败:\", error);\n      }\n    });\n    return {\n      availableSeats,\n      myReservations,\n      creditScore\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "name", "setup", "availableSeats", "myReservations", "creditScore", "value", "error", "console"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <el-card class=\"welcome-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h2>欢迎使用图书馆自习室管理系统</h2>\n        </div>\n      </template>\n      <div class=\"dashboard-content\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-statistic title=\"当前可用座位\" :value=\"availableSeats\" />\n          </el-col>\n          <el-col :span=\"8\">\n            <el-statistic title=\"我的预约\" :value=\"myReservations\" />\n          </el-col>\n          <el-col :span=\"8\">\n            <el-statistic title=\"我的信誉分\" :value=\"creditScore\" />\n          </el-col>\n        </el-row>\n\n        <div class=\"quick-actions\">\n          <h3>快捷操作</h3>\n          <el-row :gutter=\"20\">\n            <el-col :span=\"8\">\n              <el-button\n                type=\"primary\"\n                @click=\"$router.push('/seat/reservation')\"\n              >\n                预约座位\n              </el-button>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-button\n                type=\"info\"\n                @click=\"$router.push('/user/reservations')\"\n              >\n                查看我的预约\n              </el-button>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-button type=\"success\" @click=\"$router.push('/seat/map')\">\n                查看座位地图\n              </el-button>\n            </el-col>\n          </el-row>\n        </div>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted } from \"vue\";\n\nexport default {\n  name: \"Dashboard\",\n  setup() {\n    const availableSeats = ref(0);\n    const myReservations = ref(0);\n    const creditScore = ref(100);\n\n    onMounted(async () => {\n      try {\n        // 这里应该是从后端获取数据\n        // 暂时使用模拟数据\n        availableSeats.value = 120;\n        myReservations.value = 2;\n        creditScore.value = 95;\n      } catch (error) {\n        console.error(\"获取数据失败:\", error);\n      }\n    });\n\n    return {\n      availableSeats,\n      myReservations,\n      creditScore,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard-container {\n  padding: 20px;\n}\n\n.welcome-card {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.dashboard-content {\n  padding: 20px 0;\n}\n\n.quick-actions {\n  margin-top: 40px;\n\n  h3 {\n    margin-bottom: 20px;\n  }\n\n  .el-button {\n    width: 100%;\n  }\n}\n</style>\n"], "mappings": "AAqDA,SAASA,GAAG,EAAEC,SAAQ,QAAS,KAAK;AAEpC,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,KAAKA,CAAA,EAAG;IACN,MAAMC,cAAa,GAAIJ,GAAG,CAAC,CAAC,CAAC;IAC7B,MAAMK,cAAa,GAAIL,GAAG,CAAC,CAAC,CAAC;IAC7B,MAAMM,WAAU,GAAIN,GAAG,CAAC,GAAG,CAAC;IAE5BC,SAAS,CAAC,YAAY;MACpB,IAAI;QACF;QACA;QACAG,cAAc,CAACG,KAAI,GAAI,GAAG;QAC1BF,cAAc,CAACE,KAAI,GAAI,CAAC;QACxBD,WAAW,CAACC,KAAI,GAAI,EAAE;MACxB,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MACjC;IACF,CAAC,CAAC;IAEF,OAAO;MACLJ,cAAc;MACdC,cAAc;MACdC;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}