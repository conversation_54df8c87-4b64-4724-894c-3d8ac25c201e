{"ast": null, "code": "const GAP = 4;\nconst BAR_MAP = {\n  vertical: {\n    offset: \"offsetHeight\",\n    scroll: \"scrollTop\",\n    scrollSize: \"scrollHeight\",\n    size: \"height\",\n    key: \"vertical\",\n    axis: \"Y\",\n    client: \"clientY\",\n    direction: \"top\"\n  },\n  horizontal: {\n    offset: \"offsetWidth\",\n    scroll: \"scrollLeft\",\n    scrollSize: \"scrollWidth\",\n    size: \"width\",\n    key: \"horizontal\",\n    axis: \"X\",\n    client: \"clientX\",\n    direction: \"left\"\n  }\n};\nconst renderThumbStyle = ({\n  move,\n  size,\n  bar\n}) => ({\n  [bar.size]: size,\n  transform: `translate${bar.axis}(${move}%)`\n});\nexport { BAR_MAP, GAP, renderThumbStyle };", "map": {"version": 3, "names": ["GAP", "BAR_MAP", "vertical", "offset", "scroll", "scrollSize", "size", "key", "axis", "client", "direction", "horizontal", "renderThumbStyle", "move", "bar", "transform"], "sources": ["../../../../../../packages/components/scrollbar/src/util.ts"], "sourcesContent": ["import type { CSSProperties } from 'vue'\nimport type { ThumbProps } from './thumb'\n\nexport const GAP = 4 // top 2 + bottom 2 of bar instance\n\nexport const BAR_MAP = {\n  vertical: {\n    offset: 'offsetHeight',\n    scroll: 'scrollTop',\n    scrollSize: 'scrollHeight',\n    size: 'height',\n    key: 'vertical',\n    axis: 'Y',\n    client: 'clientY',\n    direction: 'top',\n  },\n  horizontal: {\n    offset: 'offsetWidth',\n    scroll: 'scrollLeft',\n    scrollSize: 'scrollWidth',\n    size: 'width',\n    key: 'horizontal',\n    axis: 'X',\n    client: 'clientX',\n    direction: 'left',\n  },\n} as const\n\nexport const renderThumbStyle = ({\n  move,\n  size,\n  bar,\n}: Pick<ThumbProps, 'move' | 'size'> & {\n  bar: typeof BAR_MAP[keyof typeof BAR_MAP]\n}): CSSProperties => ({\n  [bar.size]: size,\n  transform: `translate${bar.axis}(${move}%)`,\n})\n"], "mappings": "AAAY,MAACA,GAAG,GAAG;AACP,MAACC,OAAO,GAAG;EACrBC,QAAQ,EAAE;IACRC,MAAM,EAAE,cAAc;IACtBC,MAAM,EAAE,WAAW;IACnBC,UAAU,EAAE,cAAc;IAC1BC,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,UAAU;IACfC,IAAI,EAAE,GAAG;IACTC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE;EACf,CAAG;EACDC,UAAU,EAAE;IACVR,MAAM,EAAE,aAAa;IACrBC,MAAM,EAAE,YAAY;IACpBC,UAAU,EAAE,aAAa;IACzBC,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,YAAY;IACjBC,IAAI,EAAE,GAAG;IACTC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE;EACf;AACA;AACY,MAACE,gBAAgB,GAAGA,CAAC;EAC/BC,IAAI;EACJP,IAAI;EACJQ;AACF,CAAC,MAAM;EACL,CAACA,GAAG,CAACR,IAAI,GAAGA,IAAI;EAChBS,SAAS,EAAE,YAAYD,GAAG,CAACN,IAAI,IAAIK,IAAI;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}