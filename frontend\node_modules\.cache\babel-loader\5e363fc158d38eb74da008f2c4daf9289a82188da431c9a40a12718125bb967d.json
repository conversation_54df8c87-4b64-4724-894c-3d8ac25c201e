{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isNumber, isStringNumber } from '../types.mjs';\nimport { isClient } from '@vueuse/core';\nimport { camelize, isObject, isString } from '@vue/shared';\nimport { entriesOf, keysOf } from '../objects.mjs';\nimport { debugWarn } from '../error.mjs';\nconst SCOPE = \"utils/dom/style\";\nconst classNameToArray = (cls = \"\") => cls.split(\" \").filter(item => !!item.trim());\nconst hasClass = (el, cls) => {\n  if (!el || !cls) return false;\n  if (cls.includes(\" \")) throw new Error(\"className should not contain space.\");\n  return el.classList.contains(cls);\n};\nconst addClass = (el, cls) => {\n  if (!el || !cls.trim()) return;\n  el.classList.add(...classNameToArray(cls));\n};\nconst removeClass = (el, cls) => {\n  if (!el || !cls.trim()) return;\n  el.classList.remove(...classNameToArray(cls));\n};\nconst getStyle = (element, styleName) => {\n  var _a;\n  if (!isClient || !element || !styleName) return \"\";\n  let key = camelize(styleName);\n  if (key === \"float\") key = \"cssFloat\";\n  try {\n    const style = element.style[key];\n    if (style) return style;\n    const computed = (_a = document.defaultView) == null ? void 0 : _a.getComputedStyle(element, \"\");\n    return computed ? computed[key] : \"\";\n  } catch (e) {\n    return element.style[key];\n  }\n};\nconst setStyle = (element, styleName, value) => {\n  if (!element || !styleName) return;\n  if (isObject(styleName)) {\n    entriesOf(styleName).forEach(([prop, value2]) => setStyle(element, prop, value2));\n  } else {\n    const key = camelize(styleName);\n    element.style[key] = value;\n  }\n};\nconst removeStyle = (element, style) => {\n  if (!element || !style) return;\n  if (isObject(style)) {\n    keysOf(style).forEach(prop => removeStyle(element, prop));\n  } else {\n    setStyle(element, style, \"\");\n  }\n};\nfunction addUnit(value, defaultUnit = \"px\") {\n  if (!value) return \"\";\n  if (isNumber(value) || isStringNumber(value)) {\n    return `${value}${defaultUnit}`;\n  } else if (isString(value)) {\n    return value;\n  }\n  debugWarn(SCOPE, \"binding value must be a string or number\");\n}\nexport { addClass, addUnit, classNameToArray, getStyle, hasClass, removeClass, removeStyle, setStyle };", "map": {"version": 3, "names": ["SCOPE", "classNameToArray", "cls", "split", "filter", "item", "trim", "hasClass", "el", "includes", "Error", "classList", "contains", "addClass", "add", "removeClass", "remove", "getStyle", "element", "styleName", "_a", "isClient", "key", "camelize", "style", "computed", "document", "defaultView", "getComputedStyle", "e", "setStyle", "value", "isObject", "entriesOf", "for<PERSON>ach", "prop", "value2", "removeStyle", "keysOf", "addUnit", "defaultUnit", "isNumber", "isStringNumber", "isString", "debugWarn"], "sources": ["../../../../../packages/utils/dom/style.ts"], "sourcesContent": ["import { isNumber, isObject, isString, isStringNumber } from '../types'\nimport { isClient } from '../browser'\nimport { camelize } from '../strings'\nimport { entriesOf, keysOf } from '../objects'\nimport { debugWarn } from '../error'\nimport type { CSSProperties } from 'vue'\n\nconst SCOPE = 'utils/dom/style'\n\nexport const classNameToArray = (cls = '') =>\n  cls.split(' ').filter((item) => !!item.trim())\n\nexport const hasClass = (el: Element, cls: string): boolean => {\n  if (!el || !cls) return false\n  if (cls.includes(' ')) throw new Error('className should not contain space.')\n  return el.classList.contains(cls)\n}\n\nexport const addClass = (el: Element, cls: string) => {\n  if (!el || !cls.trim()) return\n  el.classList.add(...classNameToArray(cls))\n}\n\nexport const removeClass = (el: Element, cls: string) => {\n  if (!el || !cls.trim()) return\n  el.classList.remove(...classNameToArray(cls))\n}\n\nexport const getStyle = (\n  element: HTMLElement,\n  styleName: keyof CSSProperties\n): string => {\n  if (!isClient || !element || !styleName) return ''\n\n  let key = camelize(styleName)\n  if (key === 'float') key = 'cssFloat'\n  try {\n    const style = (element.style as any)[key]\n    if (style) return style\n    const computed: any = document.defaultView?.getComputedStyle(element, '')\n    return computed ? computed[key] : ''\n  } catch {\n    return (element.style as any)[key]\n  }\n}\n\nexport const setStyle = (\n  element: HTMLElement,\n  styleName: CSSProperties | keyof CSSProperties,\n  value?: string | number\n) => {\n  if (!element || !styleName) return\n\n  if (isObject(styleName)) {\n    entriesOf(styleName).forEach(([prop, value]) =>\n      setStyle(element, prop, value)\n    )\n  } else {\n    const key: any = camelize(styleName)\n    element.style[key] = value as any\n  }\n}\n\nexport const removeStyle = (\n  element: HTMLElement,\n  style: CSSProperties | keyof CSSProperties\n) => {\n  if (!element || !style) return\n\n  if (isObject(style)) {\n    keysOf(style).forEach((prop) => removeStyle(element, prop))\n  } else {\n    setStyle(element, style, '')\n  }\n}\n\nexport function addUnit(value?: string | number, defaultUnit = 'px') {\n  if (!value) return ''\n  if (isNumber(value) || isStringNumber(value)) {\n    return `${value}${defaultUnit}`\n  } else if (isString(value)) {\n    return value\n  }\n  debugWarn(SCOPE, 'binding value must be a string or number')\n}\n"], "mappings": ";;;;;;;;AAKA,MAAMA,KAAK,GAAG,iBAAiB;AACnB,MAACC,gBAAgB,GAAGA,CAACC,GAAG,GAAG,EAAE,KAAKA,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAEC,IAAI,IAAK,CAAC,CAACA,IAAI,CAACC,IAAI,EAAE;AAC/E,MAACC,QAAQ,GAAGA,CAACC,EAAE,EAAEN,GAAG,KAAK;EACnC,IAAI,CAACM,EAAE,IAAI,CAACN,GAAG,EACb,OAAO,KAAK;EACd,IAAIA,GAAG,CAACO,QAAQ,CAAC,GAAG,CAAC,EACnB,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;EACxD,OAAOF,EAAE,CAACG,SAAS,CAACC,QAAQ,CAACV,GAAG,CAAC;AACnC;AACY,MAACW,QAAQ,GAAGA,CAACL,EAAE,EAAEN,GAAG,KAAK;EACnC,IAAI,CAACM,EAAE,IAAI,CAACN,GAAG,CAACI,IAAI,EAAE,EACpB;EACFE,EAAE,CAACG,SAAS,CAACG,GAAG,CAAC,GAAGb,gBAAgB,CAACC,GAAG,CAAC,CAAC;AAC5C;AACY,MAACa,WAAW,GAAGA,CAACP,EAAE,EAAEN,GAAG,KAAK;EACtC,IAAI,CAACM,EAAE,IAAI,CAACN,GAAG,CAACI,IAAI,EAAE,EACpB;EACFE,EAAE,CAACG,SAAS,CAACK,MAAM,CAAC,GAAGf,gBAAgB,CAACC,GAAG,CAAC,CAAC;AAC/C;AACY,MAACe,QAAQ,GAAGA,CAACC,OAAO,EAAEC,SAAS,KAAK;EAC9C,IAAIC,EAAE;EACN,IAAI,CAACC,QAAQ,IAAI,CAACH,OAAO,IAAI,CAACC,SAAS,EACrC,OAAO,EAAE;EACX,IAAIG,GAAG,GAAGC,QAAQ,CAACJ,SAAS,CAAC;EAC7B,IAAIG,GAAG,KAAK,OAAO,EACjBA,GAAG,GAAG,UAAU;EAClB,IAAI;IACF,MAAME,KAAK,GAAGN,OAAO,CAACM,KAAK,CAACF,GAAG,CAAC;IAChC,IAAIE,KAAK,EACP,OAAOA,KAAK;IACd,MAAMC,QAAQ,GAAG,CAACL,EAAE,GAAGM,QAAQ,CAACC,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACQ,gBAAgB,CAACV,OAAO,EAAE,EAAE,CAAC;IAChG,OAAOO,QAAQ,GAAGA,QAAQ,CAACH,GAAG,CAAC,GAAG,EAAE;EACxC,CAAG,CAAC,OAAOO,CAAC,EAAE;IACV,OAAOX,OAAO,CAACM,KAAK,CAACF,GAAG,CAAC;EAC7B;AACA;AACY,MAACQ,QAAQ,GAAGA,CAACZ,OAAO,EAAEC,SAAS,EAAEY,KAAK,KAAK;EACrD,IAAI,CAACb,OAAO,IAAI,CAACC,SAAS,EACxB;EACF,IAAIa,QAAQ,CAACb,SAAS,CAAC,EAAE;IACvBc,SAAS,CAACd,SAAS,CAAC,CAACe,OAAO,CAAC,CAAC,CAACC,IAAI,EAAEC,MAAM,CAAC,KAAKN,QAAQ,CAACZ,OAAO,EAAEiB,IAAI,EAAEC,MAAM,CAAC,CAAC;EACrF,CAAG,MAAM;IACL,MAAMd,GAAG,GAAGC,QAAQ,CAACJ,SAAS,CAAC;IAC/BD,OAAO,CAACM,KAAK,CAACF,GAAG,CAAC,GAAGS,KAAK;EAC9B;AACA;AACY,MAACM,WAAW,GAAGA,CAACnB,OAAO,EAAEM,KAAK,KAAK;EAC7C,IAAI,CAACN,OAAO,IAAI,CAACM,KAAK,EACpB;EACF,IAAIQ,QAAQ,CAACR,KAAK,CAAC,EAAE;IACnBc,MAAM,CAACd,KAAK,CAAC,CAACU,OAAO,CAAEC,IAAI,IAAKE,WAAW,CAACnB,OAAO,EAAEiB,IAAI,CAAC,CAAC;EAC/D,CAAG,MAAM;IACLL,QAAQ,CAACZ,OAAO,EAAEM,KAAK,EAAE,EAAE,CAAC;EAChC;AACA;AACO,SAASe,OAAOA,CAACR,KAAK,EAAES,WAAW,GAAG,IAAI,EAAE;EACjD,IAAI,CAACT,KAAK,EACR,OAAO,EAAE;EACX,IAAIU,QAAQ,CAACV,KAAK,CAAC,IAAIW,cAAc,CAACX,KAAK,CAAC,EAAE;IAC5C,OAAO,GAAGA,KAAK,GAAGS,WAAW,EAAE;EACnC,CAAG,MAAM,IAAIG,QAAQ,CAACZ,KAAK,CAAC,EAAE;IAC1B,OAAOA,KAAK;EAChB;EACEa,SAAS,CAAC5C,KAAK,EAAE,0CAA0C,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}