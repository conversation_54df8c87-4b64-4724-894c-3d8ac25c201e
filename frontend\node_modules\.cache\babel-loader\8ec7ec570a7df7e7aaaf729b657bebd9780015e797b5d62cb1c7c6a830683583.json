{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\n/**\n * 国密算法工具类\n * 提供SM2/SM3/SM4加解密功能\n */\nimport { sm2, sm3, sm4 } from \"sm-crypto\";\n\n/**\n * SM3哈希工具类\n */\nexport class SM3Hasher {\n  /**\n   * 计算SM3哈希值\n   * @param {string} data - 待哈希数据\n   * @returns {string} - 哈希值(十六进制字符串)\n   */\n  static hash(data) {\n    return sm3(data);\n  }\n\n  /**\n   * 使用盐值和多轮迭代计算SM3哈希\n   * @param {string} data - 待哈希数据\n   * @param {string} salt - 盐值(十六进制字符串)，如果为null则随机生成\n   * @param {number} iterations - 迭代次数\n   * @returns {Object} - 包含哈希值、盐值和迭代次数的对象\n   */\n  static hashWithSalt(data, salt = null, iterations = 10000) {\n    // 如果没有提供盐值，生成随机盐值\n    if (!salt) {\n      salt = this.generateRandomHex(32);\n    }\n\n    // 初始哈希值为数据和盐值的组合\n    let value = data + salt;\n\n    // 多轮迭代哈希\n    for (let i = 0; i < iterations; i++) {\n      value = sm3(value);\n    }\n    return {\n      hash: value,\n      salt: salt,\n      iterations: iterations\n    };\n  }\n\n  /**\n   * 验证数据的哈希值是否匹配\n   * @param {string} data - 待验证数据\n   * @param {string} hashValue - 哈希值(十六进制字符串)\n   * @param {string} salt - 盐值(十六进制字符串)\n   * @param {number} iterations - 迭代次数\n   * @returns {boolean} - 是否匹配\n   */\n  static verify(data, hashValue, salt, iterations = 10000) {\n    const result = this.hashWithSalt(data, salt, iterations);\n    return result.hash === hashValue;\n  }\n\n  /**\n   * 生成随机十六进制字符串\n   * @param {number} length - 字节长度\n   * @returns {string} - 十六进制字符串\n   */\n  static generateRandomHex(length) {\n    const bytes = new Uint8Array(length);\n    window.crypto.getRandomValues(bytes);\n    return Array.from(bytes).map(b => b.toString(16).padStart(2, \"0\")).join(\"\");\n  }\n}\n\n/**\n * SM4加解密工具类\n */\nexport class SM4Crypto {\n  /**\n   * 生成随机SM4密钥\n   * @returns {string} - 密钥(十六进制字符串)\n   */\n  static generateKey() {\n    return SM3Hasher.generateRandomHex(16);\n  }\n\n  /**\n   * SM4加密\n   * @param {string} key - SM4密钥(十六进制字符串)\n   * @param {string} data - 待加密数据\n   * @param {string} iv - 初始向量(十六进制字符串)，如果为null则随机生成\n   * @param {string} mode - 加密模式，支持'cbc'和'ecb'\n   * @returns {Object} - 包含密文和IV的对象\n   */\n  static encrypt(key, data, iv = null, mode = \"cbc\") {\n    try {\n      // 如果没有提供IV，生成随机IV\n      if (!iv && mode.toLowerCase() === \"cbc\") {\n        iv = SM3Hasher.generateRandomHex(16);\n      }\n      let ciphertext;\n      if (mode.toLowerCase() === \"cbc\") {\n        // CBC模式加密\n        ciphertext = sm4.encrypt(data, key, {\n          mode: \"cbc\",\n          iv: iv\n        });\n        return {\n          ciphertext: ciphertext,\n          iv: iv\n        };\n      } else if (mode.toLowerCase() === \"ecb\") {\n        // ECB模式加密\n        ciphertext = sm4.encrypt(data, key);\n        return {\n          ciphertext: ciphertext\n        };\n      } else {\n        throw new Error(`不支持的加密模式: ${mode}`);\n      }\n    } catch (error) {\n      console.error(\"SM4加密失败:\", error);\n      throw new Error(\"SM4加密操作失败\");\n    }\n  }\n\n  /**\n   * SM4解密\n   * @param {string} key - SM4密钥(十六进制字符串)\n   * @param {string} ciphertext - 密文(十六进制字符串)\n   * @param {string} iv - 初始向量(十六进制字符串)，CBC模式必须提供\n   * @param {string} mode - 解密模式，支持'cbc'和'ecb'\n   * @returns {string} - 解密结果\n   */\n  static decrypt(key, ciphertext, iv = null, mode = \"cbc\") {\n    try {\n      if (mode.toLowerCase() === \"cbc\") {\n        if (!iv) {\n          throw new Error(\"CBC模式解密必须提供IV\");\n        }\n        // CBC模式解密\n        return sm4.decrypt(ciphertext, key, {\n          mode: \"cbc\",\n          iv: iv\n        });\n      } else if (mode.toLowerCase() === \"ecb\") {\n        // ECB模式解密\n        return sm4.decrypt(ciphertext, key);\n      } else {\n        throw new Error(`不支持的解密模式: ${mode}`);\n      }\n    } catch (error) {\n      console.error(\"SM4解密失败:\", error);\n      throw new Error(\"SM4解密操作失败\");\n    }\n  }\n}\n\n/**\n * SM2加解密和签名工具类\n */\nexport class SM2Crypto {\n  /**\n   * 生成SM2密钥对\n   * @returns {Object} - 包含公钥和私钥的对象\n   */\n  static generateKeyPair() {\n    try {\n      return sm2.generateKeyPairHex();\n    } catch (error) {\n      console.error(\"生成SM2密钥对失败:\", error);\n      throw new Error(\"生成SM2密钥对失败\");\n    }\n  }\n\n  /**\n   * SM2加密\n   * @param {string} publicKey - SM2公钥(十六进制字符串)\n   * @param {string} data - 待加密数据\n   * @returns {string} - 加密结果(十六进制字符串)\n   */\n  static encrypt(publicKey, data) {\n    try {\n      return sm2.doEncrypt(data, publicKey, 1); // 使用C1C3C2模式\n    } catch (error) {\n      console.error(\"SM2加密失败:\", error);\n      throw new Error(\"SM2加密操作失败\");\n    }\n  }\n\n  /**\n   * SM2解密\n   * @param {string} privateKey - SM2私钥(十六进制字符串)\n   * @param {string} encryptedData - 加密数据(十六进制字符串)\n   * @returns {string} - 解密结果\n   */\n  static decrypt(privateKey, encryptedData) {\n    try {\n      return sm2.doDecrypt(encryptedData, privateKey, 1); // 使用C1C3C2模式\n    } catch (error) {\n      console.error(\"SM2解密失败:\", error);\n      throw new Error(\"SM2解密操作失败\");\n    }\n  }\n\n  /**\n   * SM2签名\n   * @param {string} privateKey - SM2私钥(十六进制字符串)\n   * @param {string} data - 待签名数据\n   * @returns {string} - 签名(十六进制字符串)\n   */\n  static sign(privateKey, data) {\n    try {\n      return sm2.doSignature(data, privateKey, {\n        der: true,\n        // 使用DER编码\n        hash: true // 对消息做SM3哈希\n      });\n    } catch (error) {\n      console.error(\"SM2签名失败:\", error);\n      throw new Error(\"SM2签名操作失败\");\n    }\n  }\n\n  /**\n   * SM2验签\n   * @param {string} publicKey - SM2公钥(十六进制字符串)\n   * @param {string} data - 原始数据\n   * @param {string} signature - 签名(十六进制字符串)\n   * @returns {boolean} - 验证结果\n   */\n  static verify(publicKey, data, signature) {\n    try {\n      return sm2.doVerifySignature(data, signature, publicKey, {\n        der: true,\n        // 使用DER编码\n        hash: true // 对消息做SM3哈希\n      });\n    } catch (error) {\n      console.error(\"SM2验签失败:\", error);\n      return false;\n    }\n  }\n\n  /**\n   * 从私钥获取公钥\n   * @param {string} privateKey - SM2私钥(十六进制字符串)\n   * @returns {string} - 公钥(十六进制字符串)\n   */\n  static getPublicKeyFromPrivate(privateKey) {\n    try {\n      return sm2.getPublicKeyFromPrivateKey(privateKey);\n    } catch (error) {\n      console.error(\"从私钥获取公钥失败:\", error);\n      throw new Error(\"获取公钥失败\");\n    }\n  }\n}", "map": {"version": 3, "names": ["sm2", "sm3", "sm4", "SM3Hasher", "hash", "data", "hashWithSalt", "salt", "iterations", "generateRandomHex", "value", "i", "verify", "hashValue", "result", "length", "bytes", "Uint8Array", "window", "crypto", "getRandomValues", "Array", "from", "map", "b", "toString", "padStart", "join", "SM4Crypto", "<PERSON><PERSON>ey", "encrypt", "key", "iv", "mode", "toLowerCase", "ciphertext", "Error", "error", "console", "decrypt", "SM2Crypto", "generateKeyPair", "generateKeyPairHex", "public<PERSON>ey", "doEncrypt", "privateKey", "encryptedData", "doDecrypt", "sign", "doSignature", "der", "signature", "doVerifySignature", "getPublicKeyFromPrivate", "getPublicKeyFromPrivateKey"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/src/utils/crypto.js"], "sourcesContent": ["/**\n * 国密算法工具类\n * 提供SM2/SM3/SM4加解密功能\n */\nimport { sm2, sm3, sm4 } from \"sm-crypto\";\n\n/**\n * SM3哈希工具类\n */\nexport class SM3Hasher {\n  /**\n   * 计算SM3哈希值\n   * @param {string} data - 待哈希数据\n   * @returns {string} - 哈希值(十六进制字符串)\n   */\n  static hash(data) {\n    return sm3(data);\n  }\n\n  /**\n   * 使用盐值和多轮迭代计算SM3哈希\n   * @param {string} data - 待哈希数据\n   * @param {string} salt - 盐值(十六进制字符串)，如果为null则随机生成\n   * @param {number} iterations - 迭代次数\n   * @returns {Object} - 包含哈希值、盐值和迭代次数的对象\n   */\n  static hashWithSalt(data, salt = null, iterations = 10000) {\n    // 如果没有提供盐值，生成随机盐值\n    if (!salt) {\n      salt = this.generateRandomHex(32);\n    }\n\n    // 初始哈希值为数据和盐值的组合\n    let value = data + salt;\n\n    // 多轮迭代哈希\n    for (let i = 0; i < iterations; i++) {\n      value = sm3(value);\n    }\n\n    return {\n      hash: value,\n      salt: salt,\n      iterations: iterations,\n    };\n  }\n\n  /**\n   * 验证数据的哈希值是否匹配\n   * @param {string} data - 待验证数据\n   * @param {string} hashValue - 哈希值(十六进制字符串)\n   * @param {string} salt - 盐值(十六进制字符串)\n   * @param {number} iterations - 迭代次数\n   * @returns {boolean} - 是否匹配\n   */\n  static verify(data, hashValue, salt, iterations = 10000) {\n    const result = this.hashWithSalt(data, salt, iterations);\n    return result.hash === hashValue;\n  }\n\n  /**\n   * 生成随机十六进制字符串\n   * @param {number} length - 字节长度\n   * @returns {string} - 十六进制字符串\n   */\n  static generateRandomHex(length) {\n    const bytes = new Uint8Array(length);\n    window.crypto.getRandomValues(bytes);\n    return Array.from(bytes)\n      .map((b) => b.toString(16).padStart(2, \"0\"))\n      .join(\"\");\n  }\n}\n\n/**\n * SM4加解密工具类\n */\nexport class SM4Crypto {\n  /**\n   * 生成随机SM4密钥\n   * @returns {string} - 密钥(十六进制字符串)\n   */\n  static generateKey() {\n    return SM3Hasher.generateRandomHex(16);\n  }\n\n  /**\n   * SM4加密\n   * @param {string} key - SM4密钥(十六进制字符串)\n   * @param {string} data - 待加密数据\n   * @param {string} iv - 初始向量(十六进制字符串)，如果为null则随机生成\n   * @param {string} mode - 加密模式，支持'cbc'和'ecb'\n   * @returns {Object} - 包含密文和IV的对象\n   */\n  static encrypt(key, data, iv = null, mode = \"cbc\") {\n    try {\n      // 如果没有提供IV，生成随机IV\n      if (!iv && mode.toLowerCase() === \"cbc\") {\n        iv = SM3Hasher.generateRandomHex(16);\n      }\n\n      let ciphertext;\n      if (mode.toLowerCase() === \"cbc\") {\n        // CBC模式加密\n        ciphertext = sm4.encrypt(data, key, {\n          mode: \"cbc\",\n          iv: iv,\n        });\n        return {\n          ciphertext: ciphertext,\n          iv: iv,\n        };\n      } else if (mode.toLowerCase() === \"ecb\") {\n        // ECB模式加密\n        ciphertext = sm4.encrypt(data, key);\n        return {\n          ciphertext: ciphertext,\n        };\n      } else {\n        throw new Error(`不支持的加密模式: ${mode}`);\n      }\n    } catch (error) {\n      console.error(\"SM4加密失败:\", error);\n      throw new Error(\"SM4加密操作失败\");\n    }\n  }\n\n  /**\n   * SM4解密\n   * @param {string} key - SM4密钥(十六进制字符串)\n   * @param {string} ciphertext - 密文(十六进制字符串)\n   * @param {string} iv - 初始向量(十六进制字符串)，CBC模式必须提供\n   * @param {string} mode - 解密模式，支持'cbc'和'ecb'\n   * @returns {string} - 解密结果\n   */\n  static decrypt(key, ciphertext, iv = null, mode = \"cbc\") {\n    try {\n      if (mode.toLowerCase() === \"cbc\") {\n        if (!iv) {\n          throw new Error(\"CBC模式解密必须提供IV\");\n        }\n        // CBC模式解密\n        return sm4.decrypt(ciphertext, key, {\n          mode: \"cbc\",\n          iv: iv,\n        });\n      } else if (mode.toLowerCase() === \"ecb\") {\n        // ECB模式解密\n        return sm4.decrypt(ciphertext, key);\n      } else {\n        throw new Error(`不支持的解密模式: ${mode}`);\n      }\n    } catch (error) {\n      console.error(\"SM4解密失败:\", error);\n      throw new Error(\"SM4解密操作失败\");\n    }\n  }\n}\n\n/**\n * SM2加解密和签名工具类\n */\nexport class SM2Crypto {\n  /**\n   * 生成SM2密钥对\n   * @returns {Object} - 包含公钥和私钥的对象\n   */\n  static generateKeyPair() {\n    try {\n      return sm2.generateKeyPairHex();\n    } catch (error) {\n      console.error(\"生成SM2密钥对失败:\", error);\n      throw new Error(\"生成SM2密钥对失败\");\n    }\n  }\n\n  /**\n   * SM2加密\n   * @param {string} publicKey - SM2公钥(十六进制字符串)\n   * @param {string} data - 待加密数据\n   * @returns {string} - 加密结果(十六进制字符串)\n   */\n  static encrypt(publicKey, data) {\n    try {\n      return sm2.doEncrypt(data, publicKey, 1); // 使用C1C3C2模式\n    } catch (error) {\n      console.error(\"SM2加密失败:\", error);\n      throw new Error(\"SM2加密操作失败\");\n    }\n  }\n\n  /**\n   * SM2解密\n   * @param {string} privateKey - SM2私钥(十六进制字符串)\n   * @param {string} encryptedData - 加密数据(十六进制字符串)\n   * @returns {string} - 解密结果\n   */\n  static decrypt(privateKey, encryptedData) {\n    try {\n      return sm2.doDecrypt(encryptedData, privateKey, 1); // 使用C1C3C2模式\n    } catch (error) {\n      console.error(\"SM2解密失败:\", error);\n      throw new Error(\"SM2解密操作失败\");\n    }\n  }\n\n  /**\n   * SM2签名\n   * @param {string} privateKey - SM2私钥(十六进制字符串)\n   * @param {string} data - 待签名数据\n   * @returns {string} - 签名(十六进制字符串)\n   */\n  static sign(privateKey, data) {\n    try {\n      return sm2.doSignature(data, privateKey, {\n        der: true, // 使用DER编码\n        hash: true, // 对消息做SM3哈希\n      });\n    } catch (error) {\n      console.error(\"SM2签名失败:\", error);\n      throw new Error(\"SM2签名操作失败\");\n    }\n  }\n\n  /**\n   * SM2验签\n   * @param {string} publicKey - SM2公钥(十六进制字符串)\n   * @param {string} data - 原始数据\n   * @param {string} signature - 签名(十六进制字符串)\n   * @returns {boolean} - 验证结果\n   */\n  static verify(publicKey, data, signature) {\n    try {\n      return sm2.doVerifySignature(data, signature, publicKey, {\n        der: true, // 使用DER编码\n        hash: true, // 对消息做SM3哈希\n      });\n    } catch (error) {\n      console.error(\"SM2验签失败:\", error);\n      return false;\n    }\n  }\n\n  /**\n   * 从私钥获取公钥\n   * @param {string} privateKey - SM2私钥(十六进制字符串)\n   * @returns {string} - 公钥(十六进制字符串)\n   */\n  static getPublicKeyFromPrivate(privateKey) {\n    try {\n      return sm2.getPublicKeyFromPrivateKey(privateKey);\n    } catch (error) {\n      console.error(\"从私钥获取公钥失败:\", error);\n      throw new Error(\"获取公钥失败\");\n    }\n  }\n}\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA,SAASA,GAAG,EAAEC,GAAG,EAAEC,GAAG,QAAQ,WAAW;;AAEzC;AACA;AACA;AACA,OAAO,MAAMC,SAAS,CAAC;EACrB;AACF;AACA;AACA;AACA;EACE,OAAOC,IAAIA,CAACC,IAAI,EAAE;IAChB,OAAOJ,GAAG,CAACI,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,YAAYA,CAACD,IAAI,EAAEE,IAAI,GAAG,IAAI,EAAEC,UAAU,GAAG,KAAK,EAAE;IACzD;IACA,IAAI,CAACD,IAAI,EAAE;MACTA,IAAI,GAAG,IAAI,CAACE,iBAAiB,CAAC,EAAE,CAAC;IACnC;;IAEA;IACA,IAAIC,KAAK,GAAGL,IAAI,GAAGE,IAAI;;IAEvB;IACA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,EAAEG,CAAC,EAAE,EAAE;MACnCD,KAAK,GAAGT,GAAG,CAACS,KAAK,CAAC;IACpB;IAEA,OAAO;MACLN,IAAI,EAAEM,KAAK;MACXH,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOI,MAAMA,CAACP,IAAI,EAAEQ,SAAS,EAAEN,IAAI,EAAEC,UAAU,GAAG,KAAK,EAAE;IACvD,MAAMM,MAAM,GAAG,IAAI,CAACR,YAAY,CAACD,IAAI,EAAEE,IAAI,EAAEC,UAAU,CAAC;IACxD,OAAOM,MAAM,CAACV,IAAI,KAAKS,SAAS;EAClC;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOJ,iBAAiBA,CAACM,MAAM,EAAE;IAC/B,MAAMC,KAAK,GAAG,IAAIC,UAAU,CAACF,MAAM,CAAC;IACpCG,MAAM,CAACC,MAAM,CAACC,eAAe,CAACJ,KAAK,CAAC;IACpC,OAAOK,KAAK,CAACC,IAAI,CAACN,KAAK,CAAC,CACrBO,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAC3CC,IAAI,CAAC,EAAE,CAAC;EACb;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,CAAC;EACrB;AACF;AACA;AACA;EACE,OAAOC,WAAWA,CAAA,EAAG;IACnB,OAAO1B,SAAS,CAACM,iBAAiB,CAAC,EAAE,CAAC;EACxC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOqB,OAAOA,CAACC,GAAG,EAAE1B,IAAI,EAAE2B,EAAE,GAAG,IAAI,EAAEC,IAAI,GAAG,KAAK,EAAE;IACjD,IAAI;MACF;MACA,IAAI,CAACD,EAAE,IAAIC,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;QACvCF,EAAE,GAAG7B,SAAS,CAACM,iBAAiB,CAAC,EAAE,CAAC;MACtC;MAEA,IAAI0B,UAAU;MACd,IAAIF,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;QAChC;QACAC,UAAU,GAAGjC,GAAG,CAAC4B,OAAO,CAACzB,IAAI,EAAE0B,GAAG,EAAE;UAClCE,IAAI,EAAE,KAAK;UACXD,EAAE,EAAEA;QACN,CAAC,CAAC;QACF,OAAO;UACLG,UAAU,EAAEA,UAAU;UACtBH,EAAE,EAAEA;QACN,CAAC;MACH,CAAC,MAAM,IAAIC,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;QACvC;QACAC,UAAU,GAAGjC,GAAG,CAAC4B,OAAO,CAACzB,IAAI,EAAE0B,GAAG,CAAC;QACnC,OAAO;UACLI,UAAU,EAAEA;QACd,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,aAAaH,IAAI,EAAE,CAAC;MACtC;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,MAAM,IAAID,KAAK,CAAC,WAAW,CAAC;IAC9B;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOG,OAAOA,CAACR,GAAG,EAAEI,UAAU,EAAEH,EAAE,GAAG,IAAI,EAAEC,IAAI,GAAG,KAAK,EAAE;IACvD,IAAI;MACF,IAAIA,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;QAChC,IAAI,CAACF,EAAE,EAAE;UACP,MAAM,IAAII,KAAK,CAAC,eAAe,CAAC;QAClC;QACA;QACA,OAAOlC,GAAG,CAACqC,OAAO,CAACJ,UAAU,EAAEJ,GAAG,EAAE;UAClCE,IAAI,EAAE,KAAK;UACXD,EAAE,EAAEA;QACN,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIC,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;QACvC;QACA,OAAOhC,GAAG,CAACqC,OAAO,CAACJ,UAAU,EAAEJ,GAAG,CAAC;MACrC,CAAC,MAAM;QACL,MAAM,IAAIK,KAAK,CAAC,aAAaH,IAAI,EAAE,CAAC;MACtC;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,MAAM,IAAID,KAAK,CAAC,WAAW,CAAC;IAC9B;EACF;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMI,SAAS,CAAC;EACrB;AACF;AACA;AACA;EACE,OAAOC,eAAeA,CAAA,EAAG;IACvB,IAAI;MACF,OAAOzC,GAAG,CAAC0C,kBAAkB,CAAC,CAAC;IACjC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,MAAM,IAAID,KAAK,CAAC,YAAY,CAAC;IAC/B;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAON,OAAOA,CAACa,SAAS,EAAEtC,IAAI,EAAE;IAC9B,IAAI;MACF,OAAOL,GAAG,CAAC4C,SAAS,CAACvC,IAAI,EAAEsC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,MAAM,IAAID,KAAK,CAAC,WAAW,CAAC;IAC9B;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOG,OAAOA,CAACM,UAAU,EAAEC,aAAa,EAAE;IACxC,IAAI;MACF,OAAO9C,GAAG,CAAC+C,SAAS,CAACD,aAAa,EAAED,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,MAAM,IAAID,KAAK,CAAC,WAAW,CAAC;IAC9B;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOY,IAAIA,CAACH,UAAU,EAAExC,IAAI,EAAE;IAC5B,IAAI;MACF,OAAOL,GAAG,CAACiD,WAAW,CAAC5C,IAAI,EAAEwC,UAAU,EAAE;QACvCK,GAAG,EAAE,IAAI;QAAE;QACX9C,IAAI,EAAE,IAAI,CAAE;MACd,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,MAAM,IAAID,KAAK,CAAC,WAAW,CAAC;IAC9B;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOxB,MAAMA,CAAC+B,SAAS,EAAEtC,IAAI,EAAE8C,SAAS,EAAE;IACxC,IAAI;MACF,OAAOnD,GAAG,CAACoD,iBAAiB,CAAC/C,IAAI,EAAE8C,SAAS,EAAER,SAAS,EAAE;QACvDO,GAAG,EAAE,IAAI;QAAE;QACX9C,IAAI,EAAE,IAAI,CAAE;MACd,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOgB,uBAAuBA,CAACR,UAAU,EAAE;IACzC,IAAI;MACF,OAAO7C,GAAG,CAACsD,0BAA0B,CAACT,UAAU,CAAC;IACnD,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,MAAM,IAAID,KAAK,CAAC,QAAQ,CAAC;IAC3B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}