{"ast": null, "code": "import { defineComponent, computed, unref, withDirectives, openBlock, createElementBlock, normalizeClass, normalizeStyle, vShow, createCommentVNode, renderSlot } from 'vue';\nimport { carouselItemProps } from './carousel-item.mjs';\nimport { useCarouselItem } from './use-carousel-item.mjs';\nimport { CAROUSEL_ITEM_NAME } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: CAROUSEL_ITEM_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: carouselItemProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"carousel\");\n    const {\n      carouselItemRef,\n      active,\n      animating,\n      hover,\n      inStage,\n      isVertical,\n      translate,\n      isCardType,\n      scale,\n      ready,\n      handleItemClick\n    } = useCarouselItem(props);\n    const itemKls = computed(() => [ns.e(\"item\"), ns.is(\"active\", active.value), ns.is(\"in-stage\", inStage.value), ns.is(\"hover\", hover.value), ns.is(\"animating\", animating.value), {\n      [ns.em(\"item\", \"card\")]: isCardType.value,\n      [ns.em(\"item\", \"card-vertical\")]: isCardType.value && isVertical.value\n    }]);\n    const itemStyle = computed(() => {\n      const translateType = `translate${unref(isVertical) ? \"Y\" : \"X\"}`;\n      const _translate = `${translateType}(${unref(translate)}px)`;\n      const _scale = `scale(${unref(scale)})`;\n      const transform = [_translate, _scale].join(\" \");\n      return {\n        transform\n      };\n    });\n    return (_ctx, _cache) => {\n      return withDirectives((openBlock(), createElementBlock(\"div\", {\n        ref_key: \"carouselItemRef\",\n        ref: carouselItemRef,\n        class: normalizeClass(unref(itemKls)),\n        style: normalizeStyle(unref(itemStyle)),\n        onClick: unref(handleItemClick)\n      }, [unref(isCardType) ? withDirectives((openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"mask\"))\n      }, null, 2)), [[vShow, !unref(active)]]) : createCommentVNode(\"v-if\", true), renderSlot(_ctx.$slots, \"default\")], 14, [\"onClick\"])), [[vShow, unref(ready)]]);\n    };\n  }\n});\nvar CarouselItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"carousel-item.vue\"]]);\nexport { CarouselItem as default };", "map": {"version": 3, "names": ["name", "CAROUSEL_ITEM_NAME", "ns", "useNamespace", "carouselItemRef", "active", "animating", "hover", "inStage", "isVertical", "translate", "isCardType", "scale", "ready", "handleItemClick", "useCarouselItem", "props", "itemKls", "computed", "e", "is", "value", "em", "itemStyle", "translateType", "unref", "_translate", "_scale", "transform", "join"], "sources": ["../../../../../../packages/components/carousel/src/carousel-item.vue"], "sourcesContent": ["<template>\n  <div\n    v-show=\"ready\"\n    ref=\"carouselItemRef\"\n    :class=\"itemKls\"\n    :style=\"itemStyle\"\n    @click=\"handleItemClick\"\n  >\n    <div v-if=\"isCardType\" v-show=\"!active\" :class=\"ns.e('mask')\" />\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, unref } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { carouselItemProps } from './carousel-item'\nimport { useCarouselItem } from './use-carousel-item'\nimport { CAROUSEL_ITEM_NAME } from './constants'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: CAROUSEL_ITEM_NAME,\n})\n\nconst props = defineProps(carouselItemProps)\nconst ns = useNamespace('carousel')\n\n// inject\nconst {\n  carouselItemRef,\n  active,\n  animating,\n  hover,\n  inStage,\n  isVertical,\n  translate,\n  isCardType,\n  scale,\n  ready,\n  handleItemClick,\n} = useCarouselItem(props)\n\nconst itemKls = computed(() => [\n  ns.e('item'),\n  ns.is('active', active.value),\n  ns.is('in-stage', inStage.value),\n  ns.is('hover', hover.value),\n  ns.is('animating', animating.value),\n  {\n    [ns.em('item', 'card')]: isCardType.value,\n    [ns.em('item', 'card-vertical')]: isCardType.value && isVertical.value,\n  },\n])\n\nconst itemStyle = computed<CSSProperties>(() => {\n  const translateType = `translate${unref(isVertical) ? 'Y' : 'X'}`\n  const _translate = `${translateType}(${unref(translate)}px)`\n  const _scale = `scale(${unref(scale)})`\n  const transform = [_translate, _scale].join(' ')\n\n  return {\n    transform,\n  }\n})\n</script>\n"], "mappings": ";;;;;;mCAsBc;EACZA,IAAM,EAAAC;AACR;;;;;;IAGM,MAAAC,EAAA,GAAKC,YAAA,CAAa,UAAU;IAG5B;MACJC,eAAA;MACAC,MAAA;MACAC,SAAA;MACAC,KAAA;MACAC,OAAA;MACAC,UAAA;MACAC,SAAA;MACAC,UAAA;MACAC,KAAA;MACAC,KAAA;MACAC;IAAA,CACF,GAAIC,eAAA,CAAgBC,KAAK;IAEnB,MAAAC,OAAA,GAAUC,QAAA,CAAS,MAAM,CAC7BhB,EAAA,CAAGiB,CAAA,CAAE,MAAM,GACXjB,EAAG,CAAAkB,EAAA,CAAG,QAAU,EAAAf,MAAA,CAAOgB,KAAK,GAC5BnB,EAAG,CAAAkB,EAAA,CAAG,UAAY,EAAAZ,OAAA,CAAQa,KAAK,GAC/BnB,EAAG,CAAAkB,EAAA,CAAG,OAAS,EAAAb,KAAA,CAAMc,KAAK,GAC1BnB,EAAG,CAAAkB,EAAA,CAAG,WAAa,EAAAd,SAAA,CAAUe,KAAK,GAClC;MACE,CAACnB,EAAG,CAAAoB,EAAA,CAAG,QAAQ,MAAM,CAAC,GAAGX,UAAW,CAAAU,KAAA;MACpC,CAACnB,EAAA,CAAGoB,EAAG,SAAQ,eAAe,CAAC,GAAGX,UAAW,CAAAU,KAAA,IAASZ,UAAW,CAAAY;IAAA,CACnE,CACD;IAEK,MAAAE,SAAA,GAAYL,QAAA,CAAwB,MAAM;MAC9C,MAAMM,aAAA,GAAgB,YAAYC,KAAA,CAAMhB,UAAU,IAAI,MAAM,GAAG;MAC/D,MAAMiB,UAAA,GAAa,GAAGF,aAAa,IAAIC,KAAA,CAAMf,SAAS,CAAC;MACvD,MAAMiB,MAAS,YAASF,KAAM,CAAAb,KAAK,CAAC;MACpC,MAAMgB,SAAA,GAAY,CAACF,UAAA,EAAYC,MAAM,EAAEE,IAAA,CAAK,GAAG;MAExC;QACLD;MAAA,CACF;IAAA,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}