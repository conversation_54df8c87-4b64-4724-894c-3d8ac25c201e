{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, normalizeStyle, renderSlot, createVNode, toDisplayString, createCommentVNode } from 'vue';\nimport ImgEmpty from './img-empty.mjs';\nimport { emptyProps } from './empty.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nconst __default__ = defineComponent({\n  name: \"ElEmpty\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: emptyProps,\n  setup(__props) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"empty\");\n    const emptyDescription = computed(() => props.description || t(\"el.table.emptyText\"));\n    const imageStyle = computed(() => ({\n      width: addUnit(props.imageSize)\n    }));\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(ns).b())\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"image\")),\n        style: normalizeStyle(unref(imageStyle))\n      }, [_ctx.image ? (openBlock(), createElementBlock(\"img\", {\n        key: 0,\n        src: _ctx.image,\n        ondragstart: \"return false\"\n      }, null, 8, [\"src\"])) : renderSlot(_ctx.$slots, \"image\", {\n        key: 1\n      }, () => [createVNode(ImgEmpty)])], 6), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"description\"))\n      }, [_ctx.$slots.description ? renderSlot(_ctx.$slots, \"description\", {\n        key: 0\n      }) : (openBlock(), createElementBlock(\"p\", {\n        key: 1\n      }, toDisplayString(unref(emptyDescription)), 1))], 2), _ctx.$slots.default ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"bottom\"))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2)) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar Empty = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"empty.vue\"]]);\nexport { Empty as default };", "map": {"version": 3, "names": ["name", "t", "useLocale", "ns", "useNamespace", "emptyDescription", "computed", "props", "description", "imageStyle", "width", "addUnit", "imageSize", "_ctx", "_cache", "openBlock", "createElementBlock"], "sources": ["../../../../../../packages/components/empty/src/empty.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <div :class=\"ns.e('image')\" :style=\"imageStyle\">\n      <img v-if=\"image\" :src=\"image\" ondragstart=\"return false\" />\n      <slot v-else name=\"image\">\n        <img-empty />\n      </slot>\n    </div>\n    <div :class=\"ns.e('description')\">\n      <slot v-if=\"$slots.description\" name=\"description\" />\n      <p v-else>{{ emptyDescription }}</p>\n    </div>\n    <div v-if=\"$slots.default\" :class=\"ns.e('bottom')\">\n      <slot />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { addUnit } from '@element-plus/utils'\nimport ImgEmpty from './img-empty.vue'\nimport { emptyProps } from './empty'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElEmpty',\n})\n\nconst props = defineProps(emptyProps)\n\nconst { t } = useLocale()\nconst ns = useNamespace('empty')\nconst emptyDescription = computed(\n  () => props.description || t('el.table.emptyText')\n)\nconst imageStyle = computed<CSSProperties>(() => ({\n  width: addUnit(props.imageSize),\n}))\n</script>\n"], "mappings": ";;;;;;;mCA2Bc;EACZA,IAAM;AACR;;;;;;IAIM;MAAEC;IAAE,IAAIC,SAAU;IAClB,MAAAC,EAAA,GAAKC,YAAA,CAAa,OAAO;IAC/B,MAAMC,gBAAmB,GAAAC,QAAA,OAAAC,KAAA,CAAAC,WAAA,IAAAP,CAAA;IAAA,MACjBQ,UAAqB,GAAAH,QAAA,QAAsB;MACnDI,KAAA,EAAAC,OAAA,CAAAJ,KAAA,CAAAK,SAAA;IACA,CAAM;IACJ,QAAAC,IAAe,EAAAC,MAAA,KAAe;MAC9B,OAAAC,SAAA,IAAAC,kBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}