{"ast": null, "code": "import { watch, unref, nextTick } from 'vue';\nconst useDelayedRender = ({\n  indicator,\n  intermediateIndicator,\n  shouldSetIntermediate = () => true,\n  beforeShow,\n  afterShow,\n  afterHide,\n  beforeHide\n}) => {\n  watch(() => unref(indicator), val => {\n    if (val) {\n      beforeShow == null ? void 0 : beforeShow();\n      nextTick(() => {\n        if (!unref(indicator)) return;\n        if (shouldSetIntermediate(\"show\")) {\n          intermediateIndicator.value = true;\n        }\n      });\n    } else {\n      beforeHide == null ? void 0 : beforeHide();\n      nextTick(() => {\n        if (unref(indicator)) return;\n        if (shouldSetIntermediate(\"hide\")) {\n          intermediateIndicator.value = false;\n        }\n      });\n    }\n  });\n  watch(() => intermediateIndicator.value, val => {\n    if (val) {\n      afterShow == null ? void 0 : afterShow();\n    } else {\n      afterHide == null ? void 0 : afterHide();\n    }\n  });\n};\nexport { useDelayedRender };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indicator", "intermediateIndicator", "shouldSetIntermediate", "beforeShow", "afterShow", "afterHide", "beforeHide", "watch", "unref", "val", "nextTick", "value"], "sources": ["../../../../../packages/hooks/use-intermediate-render/index.ts"], "sourcesContent": ["import { nextTick, unref, watch } from 'vue'\n\nimport type { Ref } from 'vue'\n\nexport type UseDelayedRenderProps = {\n  indicator: Ref<boolean>\n  intermediateIndicator: Ref<boolean>\n  shouldSetIntermediate?: (step: 'show' | 'hide') => boolean\n  beforeShow?: () => void\n  beforeHide?: () => void\n  afterShow?: () => void\n  afterHide?: () => void\n}\n\nexport const useDelayedRender = ({\n  indicator,\n  intermediateIndicator,\n  shouldSetIntermediate = () => true,\n  beforeShow,\n  afterShow,\n  afterHide,\n  beforeHide,\n}: UseDelayedRenderProps) => {\n  watch(\n    () => unref(indicator),\n    (val) => {\n      if (val) {\n        beforeShow?.()\n        nextTick(() => {\n          if (!unref(indicator)) return\n          if (shouldSetIntermediate('show')) {\n            intermediateIndicator.value = true\n          }\n        })\n      } else {\n        beforeHide?.()\n        nextTick(() => {\n          if (unref(indicator)) return\n\n          if (shouldSetIntermediate('hide')) {\n            intermediateIndicator.value = false\n          }\n        })\n      }\n    }\n  )\n\n  // because we don't always set the value ourselves, so that we\n  // simply watch the value's state, then invoke the corresponding hook.\n  watch(\n    () => intermediateIndicator.value,\n    (val) => {\n      if (val) {\n        afterShow?.()\n      } else {\n        afterHide?.()\n      }\n    }\n  )\n}\n"], "mappings": ";AACY,MAACA,gBAAgB,GAAGA,CAAC;EAC/BC,SAAS;EACTC,qBAAqB;EACrBC,qBAAqB,GAAGA,CAAA,KAAM,IAAI;EAClCC,UAAU;EACVC,SAAS;EACTC,SAAS;EACTC;AACF,CAAC,KAAK;EACJC,KAAK,CAAC,MAAMC,KAAK,CAACR,SAAS,CAAC,EAAGS,GAAG,IAAK;IACrC,IAAIA,GAAG,EAAE;MACPN,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,EAAE;MAC1CO,QAAQ,CAAC,MAAM;QACb,IAAI,CAACF,KAAK,CAACR,SAAS,CAAC,EACnB;QACF,IAAIE,qBAAqB,CAAC,MAAM,CAAC,EAAE;UACjCD,qBAAqB,CAACU,KAAK,GAAG,IAAI;QAC5C;MACA,CAAO,CAAC;IACR,CAAK,MAAM;MACLL,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,EAAE;MAC1CI,QAAQ,CAAC,MAAM;QACb,IAAIF,KAAK,CAACR,SAAS,CAAC,EAClB;QACF,IAAIE,qBAAqB,CAAC,MAAM,CAAC,EAAE;UACjCD,qBAAqB,CAACU,KAAK,GAAG,KAAK;QAC7C;MACA,CAAO,CAAC;IACR;EACA,CAAG,CAAC;EACFJ,KAAK,CAAC,MAAMN,qBAAqB,CAACU,KAAK,EAAGF,GAAG,IAAK;IAChD,IAAIA,GAAG,EAAE;MACPL,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,EAAE;IAC9C,CAAK,MAAM;MACLC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,EAAE;IAC9C;EACA,CAAG,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}