{"ast": null, "code": "import { defineComponent, inject, openBlock, createElementBlock, normalizeStyle, normalizeClass, withModifiers, renderSlot, createElementVNode, toDisplayString } from 'vue';\nimport { useOption } from './useOption.mjs';\nimport { useProps } from './useProps.mjs';\nimport { OptionProps, optionEmits } from './defaults.mjs';\nimport { selectV2InjectionKey } from './token.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst _sfc_main = defineComponent({\n  props: OptionProps,\n  emits: optionEmits,\n  setup(props, {\n    emit\n  }) {\n    const select = inject(selectV2InjectionKey);\n    const ns = useNamespace(\"select\");\n    const {\n      hoverItem,\n      selectOptionClick\n    } = useOption(props, {\n      emit\n    });\n    const {\n      getLabel\n    } = useProps(select.props);\n    return {\n      ns,\n      hoverItem,\n      selectOptionClick,\n      getLabel\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"li\", {\n    \"aria-selected\": _ctx.selected,\n    style: normalizeStyle(_ctx.style),\n    class: normalizeClass([_ctx.ns.be(\"dropdown\", \"item\"), _ctx.ns.is(\"selected\", _ctx.selected), _ctx.ns.is(\"disabled\", _ctx.disabled), _ctx.ns.is(\"created\", _ctx.created), _ctx.ns.is(\"hovering\", _ctx.hovering)]),\n    onMousemove: _ctx.hoverItem,\n    onClick: withModifiers(_ctx.selectOptionClick, [\"stop\"])\n  }, [renderSlot(_ctx.$slots, \"default\", {\n    item: _ctx.item,\n    index: _ctx.index,\n    disabled: _ctx.disabled\n  }, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.getLabel(_ctx.item)), 1)])], 46, [\"aria-selected\", \"onMousemove\", \"onClick\"]);\n}\nvar OptionItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"option-item.vue\"]]);\nexport { OptionItem as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "props", "OptionProps", "emits", "optionEmits", "setup", "emit", "select", "inject", "selectV2InjectionKey", "ns", "useNamespace", "hoverItem", "selectOptionClick", "useOption", "get<PERSON><PERSON><PERSON>", "useProps", "createElementBlock", "_ctx", "selected", "style", "normalizeStyle", "class", "normalizeClass", "be", "is", "disabled", "created", "hovering", "onMousemove", "onClick", "withModifiers", "renderSlot", "$slots", "item", "index", "createElementVNode", "toDisplayString", "OptionItem", "_export_sfc", "_sfc_render"], "sources": ["../../../../../../packages/components/select-v2/src/option-item.vue"], "sourcesContent": ["<template>\n  <li\n    :aria-selected=\"selected\"\n    :style=\"style\"\n    :class=\"[\n      ns.be('dropdown', 'item'),\n      ns.is('selected', selected),\n      ns.is('disabled', disabled),\n      ns.is('created', created),\n      ns.is('hovering', hovering),\n    ]\"\n    @mousemove=\"hoverItem\"\n    @click.stop=\"selectOptionClick\"\n  >\n    <slot :item=\"item\" :index=\"index\" :disabled=\"disabled\">\n      <span>{{ getLabel(item) }}</span>\n    </slot>\n  </li>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useOption } from './useOption'\nimport { useProps } from './useProps'\nimport { OptionProps, optionEmits } from './defaults'\nimport { selectV2InjectionKey } from './token'\n\nexport default defineComponent({\n  props: OptionProps,\n  emits: optionEmits,\n  setup(props, { emit }) {\n    const select = inject(selectV2InjectionKey)!\n    const ns = useNamespace('select')\n    const { hoverItem, selectOptionClick } = useOption(props, { emit })\n    const { getLabel } = useProps(select.props)\n\n    return {\n      ns,\n      hoverItem,\n      selectOptionClick,\n      getLabel,\n    }\n  },\n})\n</script>\n"], "mappings": ";;;;;;;AA4BA,MAAKA,SAAA,GAAaC,eAAa;EAC7BC,KAAO,EAAAC,WAAA;EACPC,KAAO,EAAAC,WAAA;EACPC,KAAMA,CAAAJ,KAAA,EAAO;IAAEK;EAAA,CAAQ;IACf,MAAAC,MAAA,GAASC,MAAA,CAAOC,oBAAoB;IACpC,MAAAC,EAAA,GAAKC,YAAA,CAAa,QAAQ;IAC1B;MAAEC,SAAA;MAAWC;IAAkB,IAAIC,SAAA,CAAUb,KAAO;MAAEK;IAAA,CAAM;IAClE,MAAM;MAAES;IAAA,CAAa,GAAAC,QAAA,CAAST,MAAA,CAAON,KAAK;IAEnC;MACLS,EAAA;MACAE,SAAA;MACAC,iBAAA;MACAE;IAAA,CACF;EAAA;AAEJ,CAAC;;sBA3CCE,kBAgBK;IAfF,eAAe,EAAAC,IAAA,CAAAC,QAAA;IACfC,KAAA,EAAKC,cAAA,CAAEH,IAAK,CAAAE,KAAA;IACZE,KAAK,EAAAC,cAAA,EAAUL,IAAA,CAAAR,EAAA,CAAGc,EAAE,sBAA4BN,IAAA,CAAAR,EAAA,CAAGe,EAAE,aAAaP,IAAQ,CAAAC,QAAA,GAASD,IAAA,CAAAR,EAAA,CAAGe,EAAE,aAAaP,IAAQ,CAAAQ,QAAA,GAASR,IAAA,CAAAR,EAAA,CAAGe,EAAE,YAAYP,IAAO,CAAAS,OAAA,GAAST,IAAA,CAAAR,EAAA,CAAGe,EAAE,aAAaP,IAAQ,CAAAU,QAAA;IAOjLC,WAAW,EAAAX,IAAA,CAAAN,SAAA;IACXkB,OAAA,EAAKC,aAAA,CAAOb,IAAiB,CAAAL,iBAAA;EAAA,IAE9BmB,UAEO,CAAAd,IAAA,CAAAe,MAAA;IAFAC,IAAM,EAAAhB,IAAA,CAAAgB,IAAA;IAAOC,KAAO,EAAAjB,IAAA,CAAAiB,KAAA;IAAQT,QAAU,EAAAR,IAAA,CAAAQ;EAAA,GAA7C,MAEO,CADLU,kBAAA,eAAAC,eAAA,CAAAnB,IAAA,CAAAH,QAAA,CAAAG,IAAA,CAAAgB,IAAA,OAAiC;AAAA;AAAX,IAAAI,UAAA,kBAAAC,WAAA,CAAAxC,SAAA,cAAAyC,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}