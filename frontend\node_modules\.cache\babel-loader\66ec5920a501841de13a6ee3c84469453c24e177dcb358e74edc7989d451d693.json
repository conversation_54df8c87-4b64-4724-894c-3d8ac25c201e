{"ast": null, "code": "import { computed } from 'vue';\nimport { TinyColor } from '@ctrl/tinycolor';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nfunction darken(color, amount = 20) {\n  return color.mix(\"#141414\", amount).toString();\n}\nfunction useButtonCustomStyle(props) {\n  const _disabled = useFormDisabled();\n  const ns = useNamespace(\"button\");\n  return computed(() => {\n    let styles = {};\n    let buttonColor = props.color;\n    if (buttonColor) {\n      const match = buttonColor.match(/var\\((.*?)\\)/);\n      if (match) {\n        buttonColor = window.getComputedStyle(window.document.documentElement).getPropertyValue(match[1]);\n      }\n      const color = new TinyColor(buttonColor);\n      const activeBgColor = props.dark ? color.tint(20).toString() : darken(color, 20);\n      if (props.plain) {\n        styles = ns.cssVarBlock({\n          \"bg-color\": props.dark ? darken(color, 90) : color.tint(90).toString(),\n          \"text-color\": buttonColor,\n          \"border-color\": props.dark ? darken(color, 50) : color.tint(50).toString(),\n          \"hover-text-color\": `var(${ns.cssVarName(\"color-white\")})`,\n          \"hover-bg-color\": buttonColor,\n          \"hover-border-color\": buttonColor,\n          \"active-bg-color\": activeBgColor,\n          \"active-text-color\": `var(${ns.cssVarName(\"color-white\")})`,\n          \"active-border-color\": activeBgColor\n        });\n        if (_disabled.value) {\n          styles[ns.cssVarBlockName(\"disabled-bg-color\")] = props.dark ? darken(color, 90) : color.tint(90).toString();\n          styles[ns.cssVarBlockName(\"disabled-text-color\")] = props.dark ? darken(color, 50) : color.tint(50).toString();\n          styles[ns.cssVarBlockName(\"disabled-border-color\")] = props.dark ? darken(color, 80) : color.tint(80).toString();\n        }\n      } else {\n        const hoverBgColor = props.dark ? darken(color, 30) : color.tint(30).toString();\n        const textColor = color.isDark() ? `var(${ns.cssVarName(\"color-white\")})` : `var(${ns.cssVarName(\"color-black\")})`;\n        styles = ns.cssVarBlock({\n          \"bg-color\": buttonColor,\n          \"text-color\": textColor,\n          \"border-color\": buttonColor,\n          \"hover-bg-color\": hoverBgColor,\n          \"hover-text-color\": textColor,\n          \"hover-border-color\": hoverBgColor,\n          \"active-bg-color\": activeBgColor,\n          \"active-border-color\": activeBgColor\n        });\n        if (_disabled.value) {\n          const disabledButtonColor = props.dark ? darken(color, 50) : color.tint(50).toString();\n          styles[ns.cssVarBlockName(\"disabled-bg-color\")] = disabledButtonColor;\n          styles[ns.cssVarBlockName(\"disabled-text-color\")] = props.dark ? \"rgba(255, 255, 255, 0.5)\" : `var(${ns.cssVarName(\"color-white\")})`;\n          styles[ns.cssVarBlockName(\"disabled-border-color\")] = disabledButtonColor;\n        }\n      }\n    }\n    return styles;\n  });\n}\nexport { darken, useButtonCustomStyle };", "map": {"version": 3, "names": ["darken", "color", "amount", "mix", "toString", "useButtonCustomStyle", "props", "_disabled", "useFormDisabled", "ns", "useNamespace", "computed", "styles", "buttonColor", "match", "window", "getComputedStyle", "document", "documentElement", "getPropertyValue", "TinyColor", "activeBgColor", "dark", "tint", "plain", "cssVarBlock", "cssVarName", "value", "cssVarBlockName", "hoverBgColor", "textColor", "isDark", "disabledButtonColor"], "sources": ["../../../../../../packages/components/button/src/button-custom.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { TinyColor } from '@ctrl/tinycolor'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport type { ButtonProps } from './button'\n\nexport function darken(color: TinyColor, amount = 20) {\n  return color.mix('#141414', amount).toString()\n}\n\nexport function useButtonCustomStyle(props: ButtonProps) {\n  const _disabled = useFormDisabled()\n  const ns = useNamespace('button')\n\n  // calculate hover & active color by custom color\n  // only work when custom color\n  return computed(() => {\n    let styles: Record<string, string> = {}\n\n    let buttonColor = props.color\n\n    if (buttonColor) {\n      const match = (buttonColor as string).match(/var\\((.*?)\\)/)\n      if (match) {\n        buttonColor = window\n          .getComputedStyle(window.document.documentElement)\n          .getPropertyValue(match[1])\n      }\n      const color = new TinyColor(buttonColor)\n      const activeBgColor = props.dark\n        ? color.tint(20).toString()\n        : darken(color, 20)\n\n      if (props.plain) {\n        styles = ns.cssVarBlock({\n          'bg-color': props.dark\n            ? darken(color, 90)\n            : color.tint(90).toString(),\n          'text-color': buttonColor,\n          'border-color': props.dark\n            ? darken(color, 50)\n            : color.tint(50).toString(),\n          'hover-text-color': `var(${ns.cssVarName('color-white')})`,\n          'hover-bg-color': buttonColor,\n          'hover-border-color': buttonColor,\n          'active-bg-color': activeBgColor,\n          'active-text-color': `var(${ns.cssVarName('color-white')})`,\n          'active-border-color': activeBgColor,\n        })\n\n        if (_disabled.value) {\n          styles[ns.cssVarBlockName('disabled-bg-color')] = props.dark\n            ? darken(color, 90)\n            : color.tint(90).toString()\n          styles[ns.cssVarBlockName('disabled-text-color')] = props.dark\n            ? darken(color, 50)\n            : color.tint(50).toString()\n          styles[ns.cssVarBlockName('disabled-border-color')] = props.dark\n            ? darken(color, 80)\n            : color.tint(80).toString()\n        }\n      } else {\n        const hoverBgColor = props.dark\n          ? darken(color, 30)\n          : color.tint(30).toString()\n        const textColor = color.isDark()\n          ? `var(${ns.cssVarName('color-white')})`\n          : `var(${ns.cssVarName('color-black')})`\n        styles = ns.cssVarBlock({\n          'bg-color': buttonColor,\n          'text-color': textColor,\n          'border-color': buttonColor,\n          'hover-bg-color': hoverBgColor,\n          'hover-text-color': textColor,\n          'hover-border-color': hoverBgColor,\n          'active-bg-color': activeBgColor,\n          'active-border-color': activeBgColor,\n        })\n\n        if (_disabled.value) {\n          const disabledButtonColor = props.dark\n            ? darken(color, 50)\n            : color.tint(50).toString()\n          styles[ns.cssVarBlockName('disabled-bg-color')] = disabledButtonColor\n          styles[ns.cssVarBlockName('disabled-text-color')] = props.dark\n            ? 'rgba(255, 255, 255, 0.5)'\n            : `var(${ns.cssVarName('color-white')})`\n          styles[ns.cssVarBlockName('disabled-border-color')] =\n            disabledButtonColor\n        }\n      }\n    }\n\n    return styles\n  })\n}\n"], "mappings": ";;;;AAIO,SAASA,MAAMA,CAACC,KAAK,EAAEC,MAAM,GAAG,EAAE,EAAE;EACzC,OAAOD,KAAK,CAACE,GAAG,CAAC,SAAS,EAAED,MAAM,CAAC,CAACE,QAAQ,EAAE;AAChD;AACO,SAASC,oBAAoBA,CAACC,KAAK,EAAE;EAC1C,MAAMC,SAAS,GAAGC,eAAe,EAAE;EACnC,MAAMC,EAAE,GAAGC,YAAY,CAAC,QAAQ,CAAC;EACjC,OAAOC,QAAQ,CAAC,MAAM;IACpB,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,WAAW,GAAGP,KAAK,CAACL,KAAK;IAC7B,IAAIY,WAAW,EAAE;MACf,MAAMC,KAAK,GAAGD,WAAW,CAACC,KAAK,CAAC,cAAc,CAAC;MAC/C,IAAIA,KAAK,EAAE;QACTD,WAAW,GAAGE,MAAM,CAACC,gBAAgB,CAACD,MAAM,CAACE,QAAQ,CAACC,eAAe,CAAC,CAACC,gBAAgB,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC;MACzG;MACM,MAAMb,KAAK,GAAG,IAAImB,SAAS,CAACP,WAAW,CAAC;MACxC,MAAMQ,aAAa,GAAGf,KAAK,CAACgB,IAAI,GAAGrB,KAAK,CAACsB,IAAI,CAAC,EAAE,CAAC,CAACnB,QAAQ,EAAE,GAAGJ,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC;MAChF,IAAIK,KAAK,CAACkB,KAAK,EAAE;QACfZ,MAAM,GAAGH,EAAE,CAACgB,WAAW,CAAC;UACtB,UAAU,EAAEnB,KAAK,CAACgB,IAAI,GAAGtB,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,GAAGA,KAAK,CAACsB,IAAI,CAAC,EAAE,CAAC,CAACnB,QAAQ,EAAE;UACtE,YAAY,EAAES,WAAW;UACzB,cAAc,EAAEP,KAAK,CAACgB,IAAI,GAAGtB,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,GAAGA,KAAK,CAACsB,IAAI,CAAC,EAAE,CAAC,CAACnB,QAAQ,EAAE;UAC1E,kBAAkB,EAAE,OAAOK,EAAE,CAACiB,UAAU,CAAC,aAAa,CAAC,GAAG;UAC1D,gBAAgB,EAAEb,WAAW;UAC7B,oBAAoB,EAAEA,WAAW;UACjC,iBAAiB,EAAEQ,aAAa;UAChC,mBAAmB,EAAE,OAAOZ,EAAE,CAACiB,UAAU,CAAC,aAAa,CAAC,GAAG;UAC3D,qBAAqB,EAAEL;QACjC,CAAS,CAAC;QACF,IAAId,SAAS,CAACoB,KAAK,EAAE;UACnBf,MAAM,CAACH,EAAE,CAACmB,eAAe,CAAC,mBAAmB,CAAC,CAAC,GAAGtB,KAAK,CAACgB,IAAI,GAAGtB,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,GAAGA,KAAK,CAACsB,IAAI,CAAC,EAAE,CAAC,CAACnB,QAAQ,EAAE;UAC5GQ,MAAM,CAACH,EAAE,CAACmB,eAAe,CAAC,qBAAqB,CAAC,CAAC,GAAGtB,KAAK,CAACgB,IAAI,GAAGtB,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,GAAGA,KAAK,CAACsB,IAAI,CAAC,EAAE,CAAC,CAACnB,QAAQ,EAAE;UAC9GQ,MAAM,CAACH,EAAE,CAACmB,eAAe,CAAC,uBAAuB,CAAC,CAAC,GAAGtB,KAAK,CAACgB,IAAI,GAAGtB,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,GAAGA,KAAK,CAACsB,IAAI,CAAC,EAAE,CAAC,CAACnB,QAAQ,EAAE;QAC1H;MACA,CAAO,MAAM;QACL,MAAMyB,YAAY,GAAGvB,KAAK,CAACgB,IAAI,GAAGtB,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,GAAGA,KAAK,CAACsB,IAAI,CAAC,EAAE,CAAC,CAACnB,QAAQ,EAAE;QAC/E,MAAM0B,SAAS,GAAG7B,KAAK,CAAC8B,MAAM,EAAE,GAAG,OAAOtB,EAAE,CAACiB,UAAU,CAAC,aAAa,CAAC,GAAG,GAAG,OAAOjB,EAAE,CAACiB,UAAU,CAAC,aAAa,CAAC,GAAG;QAClHd,MAAM,GAAGH,EAAE,CAACgB,WAAW,CAAC;UACtB,UAAU,EAAEZ,WAAW;UACvB,YAAY,EAAEiB,SAAS;UACvB,cAAc,EAAEjB,WAAW;UAC3B,gBAAgB,EAAEgB,YAAY;UAC9B,kBAAkB,EAAEC,SAAS;UAC7B,oBAAoB,EAAED,YAAY;UAClC,iBAAiB,EAAER,aAAa;UAChC,qBAAqB,EAAEA;QACjC,CAAS,CAAC;QACF,IAAId,SAAS,CAACoB,KAAK,EAAE;UACnB,MAAMK,mBAAmB,GAAG1B,KAAK,CAACgB,IAAI,GAAGtB,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,GAAGA,KAAK,CAACsB,IAAI,CAAC,EAAE,CAAC,CAACnB,QAAQ,EAAE;UACtFQ,MAAM,CAACH,EAAE,CAACmB,eAAe,CAAC,mBAAmB,CAAC,CAAC,GAAGI,mBAAmB;UACrEpB,MAAM,CAACH,EAAE,CAACmB,eAAe,CAAC,qBAAqB,CAAC,CAAC,GAAGtB,KAAK,CAACgB,IAAI,GAAG,0BAA0B,GAAG,OAAOb,EAAE,CAACiB,UAAU,CAAC,aAAa,CAAC,GAAG;UACpId,MAAM,CAACH,EAAE,CAACmB,eAAe,CAAC,uBAAuB,CAAC,CAAC,GAAGI,mBAAmB;QACnF;MACA;IACA;IACI,OAAOpB,MAAM;EACjB,CAAG,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}