{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst stepProps = buildProps({\n  title: {\n    type: String,\n    default: \"\"\n  },\n  icon: {\n    type: iconPropType\n  },\n  description: {\n    type: String,\n    default: \"\"\n  },\n  status: {\n    type: String,\n    values: [\"\", \"wait\", \"process\", \"finish\", \"error\", \"success\"],\n    default: \"\"\n  }\n});\nexport { stepProps };", "map": {"version": 3, "names": ["stepProps", "buildProps", "title", "type", "String", "default", "icon", "iconPropType", "description", "status", "values"], "sources": ["../../../../../../packages/components/steps/src/item.ts"], "sourcesContent": ["import { buildProps, iconPropType } from '@element-plus/utils'\nimport type Step from './item.vue'\nimport type { ExtractPropTypes } from 'vue'\n\nexport const stepProps = buildProps({\n  /**\n   * @description step title\n   */\n  title: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description step custom icon. Icons can be passed via named slot as well\n   */\n  icon: {\n    type: iconPropType,\n  },\n  /**\n   * @description step description\n   */\n  description: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description current status. It will be automatically set by Steps if not configured.\n   */\n  status: {\n    type: String,\n    values: ['', 'wait', 'process', 'finish', 'error', 'success'],\n    default: '',\n  },\n} as const)\n\nexport type StepProps = ExtractPropTypes<typeof stepProps>\n\nexport type StepInstance = InstanceType<typeof Step> & unknown\n"], "mappings": ";;AACY,MAACA,SAAS,GAAGC,UAAU,CAAC;EAClCC,KAAK,EAAE;IACLC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,IAAI,EAAE;IACJH,IAAI,EAAEI;EACV,CAAG;EACDC,WAAW,EAAE;IACXL,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDI,MAAM,EAAE;IACNN,IAAI,EAAEC,MAAM;IACZM,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;IAC7DL,OAAO,EAAE;EACb;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}