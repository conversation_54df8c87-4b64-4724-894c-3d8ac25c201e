{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { warn } from 'vue';\nimport { fromPairs } from 'lodash-unified';\nimport { isObject, hasOwn } from '@vue/shared';\nconst epPropKey = \"__epPropKey\";\nconst definePropType = val => val;\nconst isEpProp = val => isObject(val) && !!val[epPropKey];\nconst buildProp = (prop, key) => {\n  if (!isObject(prop) || isEpProp(prop)) return prop;\n  const {\n    values,\n    required,\n    default: defaultValue,\n    type,\n    validator\n  } = prop;\n  const _validator = values || validator ? val => {\n    let valid = false;\n    let allowedValues = [];\n    if (values) {\n      allowedValues = Array.from(values);\n      if (hasOwn(prop, \"default\")) {\n        allowedValues.push(defaultValue);\n      }\n      valid || (valid = allowedValues.includes(val));\n    }\n    if (validator) valid || (valid = validator(val));\n    if (!valid && allowedValues.length > 0) {\n      const allowValuesText = [...new Set(allowedValues)].map(value => JSON.stringify(value)).join(\", \");\n      warn(`Invalid prop: validation failed${key ? ` for prop \"${key}\"` : \"\"}. Expected one of [${allowValuesText}], got value ${JSON.stringify(val)}.`);\n    }\n    return valid;\n  } : void 0;\n  const epProp = {\n    type,\n    required: !!required,\n    validator: _validator,\n    [epPropKey]: true\n  };\n  if (hasOwn(prop, \"default\")) epProp.default = defaultValue;\n  return epProp;\n};\nconst buildProps = props => fromPairs(Object.entries(props).map(([key, option]) => [key, buildProp(option, key)]));\nexport { buildProp, buildProps, definePropType, epPropKey, isEpProp };", "map": {"version": 3, "names": ["epPropKey", "definePropType", "val", "isEpProp", "isObject", "buildProp", "prop", "key", "values", "required", "default", "defaultValue", "type", "validator", "_validator", "valid", "<PERSON><PERSON><PERSON><PERSON>", "Array", "from", "hasOwn", "push", "includes", "length", "allowValuesText", "Set", "map", "value", "JSON", "stringify", "join", "warn", "epProp", "buildProps", "props", "fromPairs", "Object", "entries", "option"], "sources": ["../../../../../../packages/utils/vue/props/runtime.ts"], "sourcesContent": ["import { warn } from 'vue'\nimport { fromPairs } from 'lodash-unified'\nimport { isObject } from '../../types'\nimport { hasOwn } from '../../objects'\n\nimport type { PropType } from 'vue'\nimport type {\n  EpProp,\n  EpPropConvert,\n  EpPropFinalized,\n  EpPropInput,\n  EpPropMergeType,\n  IfEpProp,\n  IfNativePropType,\n  NativePropType,\n} from './types'\n\nexport const epPropKey = '__epPropKey'\n\nexport const definePropType = <T>(val: any): PropType<T> => val\n\nexport const isEpProp = (val: unknown): val is EpProp<any, any, any> =>\n  isObject(val) && !!(val as any)[epPropKey]\n\n/**\n * @description Build prop. It can better optimize prop types\n * @description 生成 prop，能更好地优化类型\n * @example\n  // limited options\n  // the type will be PropType<'light' | 'dark'>\n  buildProp({\n    type: String,\n    values: ['light', 'dark'],\n  } as const)\n  * @example\n  // limited options and other types\n  // the type will be PropType<'small' | 'large' | number>\n  buildProp({\n    type: [String, Number],\n    values: ['small', 'large'],\n    validator: (val: unknown): val is number => typeof val === 'number',\n  } as const)\n  @link see more: https://github.com/element-plus/element-plus/pull/3341\n */\nexport const buildProp = <\n  Type = never,\n  Value = never,\n  Validator = never,\n  Default extends EpPropMergeType<Type, Value, Validator> = never,\n  Required extends boolean = false\n>(\n  prop: EpPropInput<Type, Value, Validator, Default, Required>,\n  key?: string\n): EpPropFinalized<Type, Value, Validator, Default, Required> => {\n  // filter native prop type and nested prop, e.g `null`, `undefined` (from `buildProps`)\n  if (!isObject(prop) || isEpProp(prop)) return prop as any\n\n  const { values, required, default: defaultValue, type, validator } = prop\n\n  const _validator =\n    values || validator\n      ? (val: unknown) => {\n          let valid = false\n          let allowedValues: unknown[] = []\n\n          if (values) {\n            allowedValues = Array.from(values)\n            if (hasOwn(prop, 'default')) {\n              allowedValues.push(defaultValue)\n            }\n            valid ||= allowedValues.includes(val)\n          }\n          if (validator) valid ||= validator(val)\n\n          if (!valid && allowedValues.length > 0) {\n            const allowValuesText = [...new Set(allowedValues)]\n              .map((value) => JSON.stringify(value))\n              .join(', ')\n            warn(\n              `Invalid prop: validation failed${\n                key ? ` for prop \"${key}\"` : ''\n              }. Expected one of [${allowValuesText}], got value ${JSON.stringify(\n                val\n              )}.`\n            )\n          }\n          return valid\n        }\n      : undefined\n\n  const epProp: any = {\n    type,\n    required: !!required,\n    validator: _validator,\n    [epPropKey]: true,\n  }\n  if (hasOwn(prop, 'default')) epProp.default = defaultValue\n  return epProp\n}\n\nexport const buildProps = <\n  Props extends Record<\n    string,\n    | { [epPropKey]: true }\n    | NativePropType\n    | EpPropInput<any, any, any, any, any>\n  >\n>(\n  props: Props\n): {\n  [K in keyof Props]: IfEpProp<\n    Props[K],\n    Props[K],\n    IfNativePropType<Props[K], Props[K], EpPropConvert<Props[K]>>\n  >\n} =>\n  fromPairs(\n    Object.entries(props).map(([key, option]) => [\n      key,\n      buildProp(option as any, key),\n    ])\n  ) as any\n"], "mappings": ";;;;;;;;;;;;;AAIY,MAACA,SAAS,GAAG;AACb,MAACC,cAAc,GAAIC,GAAG,IAAKA,GAAA;AAC3B,MAACC,QAAQ,GAAID,GAAG,IAAKE,QAAQ,CAACF,GAAG,CAAC,IAAI,CAAC,CAACA,GAAG,CAACF,SAAS;AACrD,MAACK,SAAS,GAAGA,CAACC,IAAI,EAAEC,GAAG,KAAK;EACtC,IAAI,CAACH,QAAQ,CAACE,IAAI,CAAC,IAAIH,QAAQ,CAACG,IAAI,CAAC,EACnC,OAAOA,IAAI;EACb,MAAM;IAAEE,MAAM;IAAEC,QAAQ;IAAEC,OAAO,EAAEC,YAAY;IAAEC,IAAI;IAAEC;EAAS,CAAE,GAAGP,IAAI;EACzE,MAAMQ,UAAU,GAAGN,MAAM,IAAIK,SAAS,GAAIX,GAAG,IAAK;IAChD,IAAIa,KAAK,GAAG,KAAK;IACjB,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIR,MAAM,EAAE;MACVQ,aAAa,GAAGC,KAAK,CAACC,IAAI,CAACV,MAAM,CAAC;MAClC,IAAIW,MAAM,CAACb,IAAI,EAAE,SAAS,CAAC,EAAE;QAC3BU,aAAa,CAACI,IAAI,CAACT,YAAY,CAAC;MACxC;MACMI,KAAK,KAAKA,KAAK,GAAGC,aAAa,CAACK,QAAQ,CAACnB,GAAG,CAAC,CAAC;IACpD;IACI,IAAIW,SAAS,EACXE,KAAK,KAAKA,KAAK,GAAGF,SAAS,CAACX,GAAG,CAAC,CAAC;IACnC,IAAI,CAACa,KAAK,IAAIC,aAAa,CAACM,MAAM,GAAG,CAAC,EAAE;MACtC,MAAMC,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACR,aAAa,CAAC,CAAC,CAACS,GAAG,CAAEC,KAAK,IAAKC,IAAI,CAACC,SAAS,CAACF,KAAK,CAAC,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC;MACpGC,IAAI,CAAC,kCAAkCvB,GAAG,GAAG,cAAcA,GAAG,GAAG,GAAG,EAAE,sBAAsBgB,eAAe,gBAAgBI,IAAI,CAACC,SAAS,CAAC1B,GAAG,CAAC,GAAG,CAAC;IACxJ;IACI,OAAOa,KAAK;EAChB,CAAG,GAAG,KAAK,CAAC;EACV,MAAMgB,MAAM,GAAG;IACbnB,IAAI;IACJH,QAAQ,EAAE,CAAC,CAACA,QAAQ;IACpBI,SAAS,EAAEC,UAAU;IACrB,CAACd,SAAS,GAAG;EACjB,CAAG;EACD,IAAImB,MAAM,CAACb,IAAI,EAAE,SAAS,CAAC,EACzByB,MAAM,CAACrB,OAAO,GAAGC,YAAY;EAC/B,OAAOoB,MAAM;AACf;AACY,MAACC,UAAU,GAAIC,KAAK,IAAKC,SAAS,CAACC,MAAM,CAACC,OAAO,CAACH,KAAK,CAAC,CAACR,GAAG,CAAC,CAAC,CAAClB,GAAG,EAAE8B,MAAM,CAAC,KAAK,CAC1F9B,GAAG,EACHF,SAAS,CAACgC,MAAM,EAAE9B,GAAG,CAAC,CACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}