{"ast": null, "code": "import Dropdown from './src/dropdown2.mjs';\nimport DropdownItem from './src/dropdown-item.mjs';\nimport DropdownMenu from './src/dropdown-menu.mjs';\nexport { DROPDOWN_COLLECTION_INJECTION_KEY, DROPDOWN_COLLECTION_ITEM_INJECTION_KEY, ElCollection, ElCollectionItem, FIRST_KEYS, FIRST_LAST_KEYS, LAST_KEYS, dropdownItemProps, dropdownMenuProps, dropdownProps } from './src/dropdown.mjs';\nexport { DROPDOWN_INJECTION_KEY } from './src/tokens.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElDropdown = withInstall(Dropdown, {\n  DropdownItem,\n  DropdownMenu\n});\nconst ElDropdownItem = withNoopInstall(DropdownItem);\nconst ElDropdownMenu = withNoopInstall(DropdownMenu);\nexport { ElDropdown, ElDropdownItem, ElDropdownMenu, ElDropdown as default };", "map": {"version": 3, "names": ["ElDropdown", "withInstall", "Dropdown", "DropdownItem", "DropdownMenu", "ElDropdownItem", "withNoopInstall", "ElDropdownMenu"], "sources": ["../../../../../packages/components/dropdown/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\n\nimport Dropdown from './src/dropdown.vue'\nimport DropdownItem from './src/dropdown-item.vue'\nimport DropdownMenu from './src/dropdown-menu.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElDropdown: SFCWithInstall<typeof Dropdown> & {\n  DropdownItem: typeof DropdownItem\n  DropdownMenu: typeof DropdownMenu\n} = withInstall(Dropdown, {\n  DropdownItem,\n  DropdownMenu,\n})\nexport default ElDropdown\nexport const ElDropdownItem: SFCWithInstall<typeof DropdownItem> =\n  withNoopInstall(DropdownItem)\nexport const ElDropdownMenu: SFCWithInstall<typeof DropdownMenu> =\n  withNoopInstall(DropdownMenu)\nexport * from './src/dropdown'\nexport * from './src/instance'\nexport * from './src/tokens'\n"], "mappings": ";;;;;;AAIY,MAACA,UAAU,GAAGC,WAAW,CAACC,QAAQ,EAAE;EAC9CC,YAAY;EACZC;AACF,CAAC;AAEW,MAACC,cAAc,GAAGC,eAAe,CAACH,YAAY;AAC9C,MAACI,cAAc,GAAGD,eAAe,CAACF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}