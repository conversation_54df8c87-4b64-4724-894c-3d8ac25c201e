{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, unref, renderSlot, createTextVNode, toDisplayString, createCommentVNode, createElementVNode, normalizeStyle } from 'vue';\nimport { cardProps } from './card.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElCard\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: cardProps,\n  setup(__props) {\n    const ns = useNamespace(\"card\");\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(), unref(ns).is(`${_ctx.shadow}-shadow`)])\n      }, [_ctx.$slots.header || _ctx.header ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass([unref(ns).e(\"header\"), _ctx.headerClass])\n      }, [renderSlot(_ctx.$slots, \"header\", {}, () => [createTextVNode(toDisplayString(_ctx.header), 1)])], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass([unref(ns).e(\"body\"), _ctx.bodyClass]),\n        style: normalizeStyle(_ctx.bodyStyle)\n      }, [renderSlot(_ctx.$slots, \"default\")], 6), _ctx.$slots.footer || _ctx.footer ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass([unref(ns).e(\"footer\"), _ctx.footerClass])\n      }, [renderSlot(_ctx.$slots, \"footer\", {}, () => [createTextVNode(toDisplayString(_ctx.footer), 1)])], 2)) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar Card = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"card.vue\"]]);\nexport { Card as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace"], "sources": ["../../../../../../packages/components/card/src/card.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.b(), ns.is(`${shadow}-shadow`)]\">\n    <div v-if=\"$slots.header || header\" :class=\"[ns.e('header'), headerClass]\">\n      <slot name=\"header\">{{ header }}</slot>\n    </div>\n    <div :class=\"[ns.e('body'), bodyClass]\" :style=\"bodyStyle\">\n      <slot />\n    </div>\n    <div v-if=\"$slots.footer || footer\" :class=\"[ns.e('footer'), footerClass]\">\n      <slot name=\"footer\">{{ footer }}</slot>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useNamespace } from '@element-plus/hooks'\nimport { cardProps } from './card'\n\ndefineOptions({\n  name: 'ElCard',\n})\n\ndefineProps(cardProps)\n\nconst ns = useNamespace('card')\n</script>\n"], "mappings": ";;;;mCAkBc;EACZA,IAAM;AACR;;;;;IAIM,MAAAC,EAAA,GAAKC,YAAA,CAAa,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}