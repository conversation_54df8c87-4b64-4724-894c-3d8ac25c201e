{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isFirefox } from '../../../utils/browser.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nlet hiddenTextarea = void 0;\nconst HIDDEN_STYLE = {\n  height: \"0\",\n  visibility: \"hidden\",\n  overflow: isFirefox() ? \"\" : \"hidden\",\n  position: \"absolute\",\n  \"z-index\": \"-1000\",\n  top: \"0\",\n  right: \"0\"\n};\nconst CONTEXT_STYLE = [\"letter-spacing\", \"line-height\", \"padding-top\", \"padding-bottom\", \"font-family\", \"font-weight\", \"font-size\", \"text-rendering\", \"text-transform\", \"width\", \"text-indent\", \"padding-left\", \"padding-right\", \"border-width\", \"box-sizing\"];\nfunction calculateNodeStyling(targetElement) {\n  const style = window.getComputedStyle(targetElement);\n  const boxSizing = style.getPropertyValue(\"box-sizing\");\n  const paddingSize = Number.parseFloat(style.getPropertyValue(\"padding-bottom\")) + Number.parseFloat(style.getPropertyValue(\"padding-top\"));\n  const borderSize = Number.parseFloat(style.getPropertyValue(\"border-bottom-width\")) + Number.parseFloat(style.getPropertyValue(\"border-top-width\"));\n  const contextStyle = CONTEXT_STYLE.map(name => [name, style.getPropertyValue(name)]);\n  return {\n    contextStyle,\n    paddingSize,\n    borderSize,\n    boxSizing\n  };\n}\nfunction calcTextareaHeight(targetElement, minRows = 1, maxRows) {\n  var _a;\n  if (!hiddenTextarea) {\n    hiddenTextarea = document.createElement(\"textarea\");\n    document.body.appendChild(hiddenTextarea);\n  }\n  const {\n    paddingSize,\n    borderSize,\n    boxSizing,\n    contextStyle\n  } = calculateNodeStyling(targetElement);\n  contextStyle.forEach(([key, value]) => hiddenTextarea == null ? void 0 : hiddenTextarea.style.setProperty(key, value));\n  Object.entries(HIDDEN_STYLE).forEach(([key, value]) => hiddenTextarea == null ? void 0 : hiddenTextarea.style.setProperty(key, value, \"important\"));\n  hiddenTextarea.value = targetElement.value || targetElement.placeholder || \"\";\n  let height = hiddenTextarea.scrollHeight;\n  const result = {};\n  if (boxSizing === \"border-box\") {\n    height = height + borderSize;\n  } else if (boxSizing === \"content-box\") {\n    height = height - paddingSize;\n  }\n  hiddenTextarea.value = \"\";\n  const singleRowHeight = hiddenTextarea.scrollHeight - paddingSize;\n  if (isNumber(minRows)) {\n    let minHeight = singleRowHeight * minRows;\n    if (boxSizing === \"border-box\") {\n      minHeight = minHeight + paddingSize + borderSize;\n    }\n    height = Math.max(minHeight, height);\n    result.minHeight = `${minHeight}px`;\n  }\n  if (isNumber(maxRows)) {\n    let maxHeight = singleRowHeight * maxRows;\n    if (boxSizing === \"border-box\") {\n      maxHeight = maxHeight + paddingSize + borderSize;\n    }\n    height = Math.min(maxHeight, height);\n  }\n  result.height = `${height}px`;\n  (_a = hiddenTextarea.parentNode) == null ? void 0 : _a.removeChild(hiddenTextarea);\n  hiddenTextarea = void 0;\n  return result;\n}\nexport { calcTextareaHeight };", "map": {"version": 3, "names": ["hiddenTextarea", "HIDDEN_STYLE", "height", "visibility", "overflow", "isFirefox", "position", "top", "right", "CONTEXT_STYLE", "calculateNodeStyling", "targetElement", "style", "window", "getComputedStyle", "boxSizing", "getPropertyValue", "paddingSize", "Number", "parseFloat", "borderSize", "contextStyle", "map", "name", "calcTextareaHeight", "minRows", "maxRows", "_a", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "for<PERSON>ach", "key", "value", "setProperty", "Object", "entries", "placeholder", "scrollHeight", "result", "singleRowHeight", "isNumber", "minHeight", "Math", "max", "maxHeight", "min", "parentNode", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../../../../packages/components/input/src/utils.ts"], "sourcesContent": ["import { isFirefox, isNumber } from '@element-plus/utils'\n\nlet hiddenTextarea: HTMLTextAreaElement | undefined = undefined\n\nconst HIDDEN_STYLE = {\n  height: '0',\n  visibility: 'hidden',\n  overflow: isFirefox() ? '' : 'hidden',\n  position: 'absolute',\n  'z-index': '-1000',\n  top: '0',\n  right: '0',\n}\n\nconst CONTEXT_STYLE = [\n  'letter-spacing',\n  'line-height',\n  'padding-top',\n  'padding-bottom',\n  'font-family',\n  'font-weight',\n  'font-size',\n  'text-rendering',\n  'text-transform',\n  'width',\n  'text-indent',\n  'padding-left',\n  'padding-right',\n  'border-width',\n  'box-sizing',\n]\n\ntype NodeStyle = {\n  contextStyle: string[][]\n  boxSizing: string\n  paddingSize: number\n  borderSize: number\n}\n\ntype TextAreaHeight = {\n  height: string\n  minHeight?: string\n}\n\nfunction calculateNodeStyling(targetElement: Element): NodeStyle {\n  const style = window.getComputedStyle(targetElement)\n\n  const boxSizing = style.getPropertyValue('box-sizing')\n\n  const paddingSize =\n    Number.parseFloat(style.getPropertyValue('padding-bottom')) +\n    Number.parseFloat(style.getPropertyValue('padding-top'))\n\n  const borderSize =\n    Number.parseFloat(style.getPropertyValue('border-bottom-width')) +\n    Number.parseFloat(style.getPropertyValue('border-top-width'))\n\n  const contextStyle = CONTEXT_STYLE.map((name) => [\n    name,\n    style.getPropertyValue(name),\n  ])\n\n  return { contextStyle, paddingSize, borderSize, boxSizing }\n}\n\nexport function calcTextareaHeight(\n  targetElement: HTMLTextAreaElement,\n  minRows = 1,\n  maxRows?: number\n): TextAreaHeight {\n  if (!hiddenTextarea) {\n    hiddenTextarea = document.createElement('textarea')\n    document.body.appendChild(hiddenTextarea)\n  }\n\n  const { paddingSize, borderSize, boxSizing, contextStyle } =\n    calculateNodeStyling(targetElement)\n\n  contextStyle.forEach(([key, value]) =>\n    hiddenTextarea?.style.setProperty(key, value)\n  )\n\n  Object.entries(HIDDEN_STYLE).forEach(([key, value]) =>\n    hiddenTextarea?.style.setProperty(key, value, 'important')\n  )\n\n  hiddenTextarea.value = targetElement.value || targetElement.placeholder || ''\n\n  let height = hiddenTextarea.scrollHeight\n  const result = {} as TextAreaHeight\n\n  if (boxSizing === 'border-box') {\n    height = height + borderSize\n  } else if (boxSizing === 'content-box') {\n    height = height - paddingSize\n  }\n\n  hiddenTextarea.value = ''\n  const singleRowHeight = hiddenTextarea.scrollHeight - paddingSize\n\n  if (isNumber(minRows)) {\n    let minHeight = singleRowHeight * minRows\n    if (boxSizing === 'border-box') {\n      minHeight = minHeight + paddingSize + borderSize\n    }\n    height = Math.max(minHeight, height)\n    result.minHeight = `${minHeight}px`\n  }\n  if (isNumber(maxRows)) {\n    let maxHeight = singleRowHeight * maxRows\n    if (boxSizing === 'border-box') {\n      maxHeight = maxHeight + paddingSize + borderSize\n    }\n    height = Math.min(maxHeight, height)\n  }\n  result.height = `${height}px`\n  hiddenTextarea.parentNode?.removeChild(hiddenTextarea)\n  hiddenTextarea = undefined\n\n  return result\n}\n"], "mappings": ";;;;AACA,IAAIA,cAAc,GAAG,KAAK,CAAC;AAC3B,MAAMC,YAAY,GAAG;EACnBC,MAAM,EAAE,GAAG;EACXC,UAAU,EAAE,QAAQ;EACpBC,QAAQ,EAAEC,SAAS,EAAE,GAAG,EAAE,GAAG,QAAQ;EACrCC,QAAQ,EAAE,UAAU;EACpB,SAAS,EAAE,OAAO;EAClBC,GAAG,EAAE,GAAG;EACRC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,aAAa,GAAG,CACpB,gBAAgB,EAChB,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,aAAa,EACb,aAAa,EACb,WAAW,EACX,gBAAgB,EAChB,gBAAgB,EAChB,OAAO,EACP,aAAa,EACb,cAAc,EACd,eAAe,EACf,cAAc,EACd,YAAY,CACb;AACD,SAASC,oBAAoBA,CAACC,aAAa,EAAE;EAC3C,MAAMC,KAAK,GAAGC,MAAM,CAACC,gBAAgB,CAACH,aAAa,CAAC;EACpD,MAAMI,SAAS,GAAGH,KAAK,CAACI,gBAAgB,CAAC,YAAY,CAAC;EACtD,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU,CAACP,KAAK,CAACI,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,GAAGE,MAAM,CAACC,UAAU,CAACP,KAAK,CAACI,gBAAgB,CAAC,aAAa,CAAC,CAAC;EAC1I,MAAMI,UAAU,GAAGF,MAAM,CAACC,UAAU,CAACP,KAAK,CAACI,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,GAAGE,MAAM,CAACC,UAAU,CAACP,KAAK,CAACI,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;EACnJ,MAAMK,YAAY,GAAGZ,aAAa,CAACa,GAAG,CAAEC,IAAI,IAAK,CAC/CA,IAAI,EACJX,KAAK,CAACI,gBAAgB,CAACO,IAAI,CAAC,CAC7B,CAAC;EACF,OAAO;IAAEF,YAAY;IAAEJ,WAAW;IAAEG,UAAU;IAAEL;EAAS,CAAE;AAC7D;AACO,SAASS,kBAAkBA,CAACb,aAAa,EAAEc,OAAO,GAAG,CAAC,EAAEC,OAAO,EAAE;EACtE,IAAIC,EAAE;EACN,IAAI,CAAC3B,cAAc,EAAE;IACnBA,cAAc,GAAG4B,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;IACnDD,QAAQ,CAACE,IAAI,CAACC,WAAW,CAAC/B,cAAc,CAAC;EAC7C;EACE,MAAM;IAAEiB,WAAW;IAAEG,UAAU;IAAEL,SAAS;IAAEM;EAAY,CAAE,GAAGX,oBAAoB,CAACC,aAAa,CAAC;EAChGU,YAAY,CAACW,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAKlC,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACY,KAAK,CAACuB,WAAW,CAACF,GAAG,EAAEC,KAAK,CAAC,CAAC;EACtHE,MAAM,CAACC,OAAO,CAACpC,YAAY,CAAC,CAAC+B,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAKlC,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACY,KAAK,CAACuB,WAAW,CAACF,GAAG,EAAEC,KAAK,EAAE,WAAW,CAAC,CAAC;EACnJlC,cAAc,CAACkC,KAAK,GAAGvB,aAAa,CAACuB,KAAK,IAAIvB,aAAa,CAAC2B,WAAW,IAAI,EAAE;EAC7E,IAAIpC,MAAM,GAAGF,cAAc,CAACuC,YAAY;EACxC,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIzB,SAAS,KAAK,YAAY,EAAE;IAC9Bb,MAAM,GAAGA,MAAM,GAAGkB,UAAU;EAChC,CAAG,MAAM,IAAIL,SAAS,KAAK,aAAa,EAAE;IACtCb,MAAM,GAAGA,MAAM,GAAGe,WAAW;EACjC;EACEjB,cAAc,CAACkC,KAAK,GAAG,EAAE;EACzB,MAAMO,eAAe,GAAGzC,cAAc,CAACuC,YAAY,GAAGtB,WAAW;EACjE,IAAIyB,QAAQ,CAACjB,OAAO,CAAC,EAAE;IACrB,IAAIkB,SAAS,GAAGF,eAAe,GAAGhB,OAAO;IACzC,IAAIV,SAAS,KAAK,YAAY,EAAE;MAC9B4B,SAAS,GAAGA,SAAS,GAAG1B,WAAW,GAAGG,UAAU;IACtD;IACIlB,MAAM,GAAG0C,IAAI,CAACC,GAAG,CAACF,SAAS,EAAEzC,MAAM,CAAC;IACpCsC,MAAM,CAACG,SAAS,GAAG,GAAGA,SAAS,IAAI;EACvC;EACE,IAAID,QAAQ,CAAChB,OAAO,CAAC,EAAE;IACrB,IAAIoB,SAAS,GAAGL,eAAe,GAAGf,OAAO;IACzC,IAAIX,SAAS,KAAK,YAAY,EAAE;MAC9B+B,SAAS,GAAGA,SAAS,GAAG7B,WAAW,GAAGG,UAAU;IACtD;IACIlB,MAAM,GAAG0C,IAAI,CAACG,GAAG,CAACD,SAAS,EAAE5C,MAAM,CAAC;EACxC;EACEsC,MAAM,CAACtC,MAAM,GAAG,GAAGA,MAAM,IAAI;EAC7B,CAACyB,EAAE,GAAG3B,cAAc,CAACgD,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGrB,EAAE,CAACsB,WAAW,CAACjD,cAAc,CAAC;EAClFA,cAAc,GAAG,KAAK,CAAC;EACvB,OAAOwC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}