{"ast": null, "code": "import { getCurrentInstance, inject, unref } from 'vue';\nimport { isClient, computedEager } from '@vueuse/core';\nimport { useGetDerivedNamespace } from '../use-namespace/index.mjs';\nimport { debugWarn } from '../../utils/error.mjs';\nconst defaultIdInjection = {\n  prefix: Math.floor(Math.random() * 1e4),\n  current: 0\n};\nconst ID_INJECTION_KEY = Symbol(\"elIdInjection\");\nconst useIdInjection = () => {\n  return getCurrentInstance() ? inject(ID_INJECTION_KEY, defaultIdInjection) : defaultIdInjection;\n};\nconst useId = deterministicId => {\n  const idInjection = useIdInjection();\n  if (!isClient && idInjection === defaultIdInjection) {\n    debugWarn(\"IdInjection\", `Looks like you are using server rendering, you must provide a id provider to ensure the hydration process to be succeed\nusage: app.provide(ID_INJECTION_KEY, {\n  prefix: number,\n  current: number,\n})`);\n  }\n  const namespace = useGetDerivedNamespace();\n  const idRef = computedEager(() => unref(deterministicId) || `${namespace.value}-id-${idInjection.prefix}-${idInjection.current++}`);\n  return idRef;\n};\nexport { ID_INJECTION_KEY, useId, useIdInjection };", "map": {"version": 3, "names": ["defaultIdInjection", "prefix", "Math", "floor", "random", "current", "ID_INJECTION_KEY", "Symbol", "useIdInjection", "getCurrentInstance", "inject", "useId", "deterministicId", "idInjection", "isClient", "debugWarn", "namespace", "useGetDerivedNamespace", "idRef", "computedEager", "unref", "value"], "sources": ["../../../../../packages/hooks/use-id/index.ts"], "sourcesContent": ["import { getCurrentInstance, inject, unref } from 'vue'\nimport { type MaybeRef, computedEager } from '@vueuse/core'\nimport { debugWarn, isClient } from '@element-plus/utils'\nimport { useGetDerivedNamespace } from '../use-namespace'\n\nimport type { InjectionKey, Ref } from 'vue'\n\nexport type ElIdInjectionContext = {\n  prefix: number\n  current: number\n}\n\nconst defaultIdInjection = {\n  prefix: Math.floor(Math.random() * 10000),\n  current: 0,\n}\n\nexport const ID_INJECTION_KEY: InjectionKey<ElIdInjectionContext> =\n  Symbol('elIdInjection')\n\nexport const useIdInjection = (): ElIdInjectionContext => {\n  return getCurrentInstance()\n    ? inject(ID_INJECTION_KEY, defaultIdInjection)\n    : defaultIdInjection\n}\n\nexport const useId = (deterministicId?: MaybeRef<string>): Ref<string> => {\n  const idInjection = useIdInjection()\n  if (!isClient && idInjection === defaultIdInjection) {\n    debugWarn(\n      'IdInjection',\n      `Looks like you are using server rendering, you must provide a id provider to ensure the hydration process to be succeed\nusage: app.provide(ID_INJECTION_KEY, {\n  prefix: number,\n  current: number,\n})`\n    )\n  }\n\n  const namespace = useGetDerivedNamespace()\n\n  // NOTE: Here we use `computedEager` to calculate the id value immediately, avoiding inconsistent id generation due to the lazy feature of `computed` when server rendering.\n  const idRef = computedEager(\n    () =>\n      unref(deterministicId) ||\n      `${namespace.value}-id-${idInjection.prefix}-${idInjection.current++}`\n  )\n\n  return idRef\n}\n"], "mappings": ";;;;AAIA,MAAMA,kBAAkB,GAAG;EACzBC,MAAM,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,GAAG,CAAC;EACvCC,OAAO,EAAE;AACX,CAAC;AACW,MAACC,gBAAgB,GAAGC,MAAM,CAAC,eAAe;AAC1C,MAACC,cAAc,GAAGA,CAAA,KAAM;EAClC,OAAOC,kBAAkB,EAAE,GAAGC,MAAM,CAACJ,gBAAgB,EAAEN,kBAAkB,CAAC,GAAGA,kBAAkB;AACjG;AACY,MAACW,KAAK,GAAIC,eAAe,IAAK;EACxC,MAAMC,WAAW,GAAGL,cAAc,EAAE;EACpC,IAAI,CAACM,QAAQ,IAAID,WAAW,KAAKb,kBAAkB,EAAE;IACnDe,SAAS,CAAC,aAAa,EAAE;AAC7B;AACA;AACA;AACA,GAAG,CAAC;EACJ;EACE,MAAMC,SAAS,GAAGC,sBAAsB,EAAE;EAC1C,MAAMC,KAAK,GAAGC,aAAa,CAAC,MAAMC,KAAK,CAACR,eAAe,CAAC,IAAI,GAAGI,SAAS,CAACK,KAAK,OAAOR,WAAW,CAACZ,MAAM,IAAIY,WAAW,CAACR,OAAO,EAAE,EAAE,CAAC;EACnI,OAAOa,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}