{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, createElementVNode, renderSlot, createBlock, withModifiers, withCtx, createVNode, createCommentVNode, Transition } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { Close } from '@element-plus/icons-vue';\nimport { tagProps, tagEmits } from './tag.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTag\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tagProps,\n  emits: tagEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const tagSize = useFormSize();\n    const ns = useNamespace(\"tag\");\n    const containerKls = computed(() => {\n      const {\n        type,\n        hit,\n        effect,\n        closable,\n        round\n      } = props;\n      return [ns.b(), ns.is(\"closable\", closable), ns.m(type || \"primary\"), ns.m(tagSize.value), ns.m(effect), ns.is(\"hit\", hit), ns.is(\"round\", round)];\n    });\n    const handleClose = event => {\n      emit(\"close\", event);\n    };\n    const handleClick = event => {\n      emit(\"click\", event);\n    };\n    const handleVNodeMounted = vnode => {\n      var _a, _b, _c;\n      if ((_c = (_b = (_a = vnode == null ? void 0 : vnode.component) == null ? void 0 : _a.subTree) == null ? void 0 : _b.component) == null ? void 0 : _c.bum) {\n        vnode.component.subTree.component.bum = null;\n      }\n    };\n    return (_ctx, _cache) => {\n      return _ctx.disableTransitions ? (openBlock(), createElementBlock(\"span\", {\n        key: 0,\n        class: normalizeClass(unref(containerKls)),\n        style: normalizeStyle({\n          backgroundColor: _ctx.color\n        }),\n        onClick: handleClick\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass(unref(ns).e(\"content\"))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2), _ctx.closable ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"close\")),\n        onClick: withModifiers(handleClose, [\"stop\"])\n      }, {\n        default: withCtx(() => [createVNode(unref(Close))]),\n        _: 1\n      }, 8, [\"class\", \"onClick\"])) : createCommentVNode(\"v-if\", true)], 6)) : (openBlock(), createBlock(Transition, {\n        key: 1,\n        name: `${unref(ns).namespace.value}-zoom-in-center`,\n        appear: \"\",\n        onVnodeMounted: handleVNodeMounted\n      }, {\n        default: withCtx(() => [createElementVNode(\"span\", {\n          class: normalizeClass(unref(containerKls)),\n          style: normalizeStyle({\n            backgroundColor: _ctx.color\n          }),\n          onClick: handleClick\n        }, [createElementVNode(\"span\", {\n          class: normalizeClass(unref(ns).e(\"content\"))\n        }, [renderSlot(_ctx.$slots, \"default\")], 2), _ctx.closable ? (openBlock(), createBlock(unref(ElIcon), {\n          key: 0,\n          class: normalizeClass(unref(ns).e(\"close\")),\n          onClick: withModifiers(handleClose, [\"stop\"])\n        }, {\n          default: withCtx(() => [createVNode(unref(Close))]),\n          _: 1\n        }, 8, [\"class\", \"onClick\"])) : createCommentVNode(\"v-if\", true)], 6)]),\n        _: 3\n      }, 8, [\"name\"]));\n    };\n  }\n});\nvar Tag = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tag.vue\"]]);\nexport { Tag as default };", "map": {"version": 3, "names": ["name", "tagSize", "useFormSize", "ns", "useNamespace", "containerKls", "computed", "type", "hit", "effect", "closable", "round", "props", "b", "is", "m", "value", "handleClose", "event", "emit", "handleClick", "handleVNodeMounted", "vnode", "_a", "_b", "_c", "component", "subTree", "bum"], "sources": ["../../../../../../packages/components/tag/src/tag.vue"], "sourcesContent": ["<template>\n  <span\n    v-if=\"disableTransitions\"\n    :class=\"containerKls\"\n    :style=\"{ backgroundColor: color }\"\n    @click=\"handleClick\"\n  >\n    <span :class=\"ns.e('content')\">\n      <slot />\n    </span>\n    <el-icon v-if=\"closable\" :class=\"ns.e('close')\" @click.stop=\"handleClose\">\n      <Close />\n    </el-icon>\n  </span>\n  <transition\n    v-else\n    :name=\"`${ns.namespace.value}-zoom-in-center`\"\n    appear\n    @vue:mounted=\"handleVNodeMounted\"\n  >\n    <span\n      :class=\"containerKls\"\n      :style=\"{ backgroundColor: color }\"\n      @click=\"handleClick\"\n    >\n      <span :class=\"ns.e('content')\">\n        <slot />\n      </span>\n      <el-icon v-if=\"closable\" :class=\"ns.e('close')\" @click.stop=\"handleClose\">\n        <Close />\n      </el-icon>\n    </span>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport ElIcon from '@element-plus/components/icon'\nimport { Close } from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormSize } from '@element-plus/components/form'\n\nimport { tagEmits, tagProps } from './tag'\nimport type { VNode } from 'vue'\n\ndefineOptions({\n  name: 'ElTag',\n})\nconst props = defineProps(tagProps)\nconst emit = defineEmits(tagEmits)\n\nconst tagSize = useFormSize()\nconst ns = useNamespace('tag')\nconst containerKls = computed(() => {\n  const { type, hit, effect, closable, round } = props\n  return [\n    ns.b(),\n    ns.is('closable', closable),\n    ns.m(type || 'primary'),\n    ns.m(tagSize.value),\n    ns.m(effect),\n    ns.is('hit', hit),\n    ns.is('round', round),\n  ]\n})\n\n// methods\nconst handleClose = (event: MouseEvent) => {\n  emit('close', event)\n}\n\nconst handleClick = (event: MouseEvent) => {\n  emit('click', event)\n}\n\nconst handleVNodeMounted = (vnode: VNode) => {\n  // @ts-ignore\n  if (vnode?.component?.subTree?.component?.bum) {\n    // @ts-ignore\n    vnode.component.subTree.component.bum = null\n  }\n}\n</script>\n"], "mappings": ";;;;;;;mCA6Cc;EACZA,IAAM;AACR;;;;;;;;;IAIA,MAAMC,OAAA,GAAUC,WAAY;IACtB,MAAAC,EAAA,GAAKC,YAAA,CAAa,KAAK;IACvB,MAAAC,YAAA,GAAeC,QAAA,CAAS,MAAM;MAClC,MAAM;QAAEC,IAAM;QAAAC,GAAA;QAAKC,MAAQ;QAAAC,QAAA;QAAUC;MAAA,CAAU,GAAAC,KAAA;MACxC,QACLT,EAAA,CAAGU,CAAE,IACLV,EAAA,CAAGW,EAAG,aAAYJ,QAAQ,GAC1BP,EAAA,CAAGY,CAAE,CAAAR,IAAA,IAAQ,SAAS,GACtBJ,EAAA,CAAGY,CAAE,CAAAd,OAAA,CAAQe,KAAK,GAClBb,EAAA,CAAGY,CAAA,CAAEN,MAAM,GACXN,EAAA,CAAGW,EAAG,QAAON,GAAG,GAChBL,EAAA,CAAGW,EAAG,UAASH,KAAK,EACtB;IAAA,CACD;IAGK,MAAAM,WAAA,GAAeC,KAAsB;MACzCC,IAAA,CAAK,SAASD,KAAK;IAAA,CACrB;IAEM,MAAAE,WAAA,GAAeF,KAAsB;MACzCC,IAAA,CAAK,SAASD,KAAK;IAAA,CACrB;IAEM,MAAAG,kBAAA,GAAsBC,KAAiB;MAE3C,IAAIC,EAAO,EAAAC,EAAA,EAAAC,EAAA;MAEH,KAAAA,EAAA,IAAAD,EAAA,IAAAD,EAAkB,GAAAD,KAAA,WAAgB,SAAAA,KAAA,CAAAI,SAAA,qBAAAH,EAAA,CAAAI,OAAA,qBAAAH,EAAA,CAAAE,SAAA,qBAAAD,EAAA,CAAAG,GAAA;QAC1CN,KAAA,CAAAI,SAAA,CAAAC,OAAA,CAAAD,SAAA,CAAAE,GAAA;MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}