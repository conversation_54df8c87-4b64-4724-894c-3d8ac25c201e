{"ast": null, "code": "import { shallowRef, ref, computed, nextTick } from 'vue';\nimport { useFormItem } from '../../../form/src/hooks/use-form-item.mjs';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT, CHANGE_EVENT } from '../../../../constants/event.mjs';\nconst useSlide = (props, initData, emit) => {\n  const {\n    form: elForm,\n    formItem: elFormItem\n  } = useFormItem();\n  const slider = shallowRef();\n  const firstButton = ref();\n  const secondButton = ref();\n  const buttonRefs = {\n    firstButton,\n    secondButton\n  };\n  const sliderDisabled = computed(() => {\n    return props.disabled || (elForm == null ? void 0 : elForm.disabled) || false;\n  });\n  const minValue = computed(() => {\n    return Math.min(initData.firstValue, initData.secondValue);\n  });\n  const maxValue = computed(() => {\n    return Math.max(initData.firstValue, initData.secondValue);\n  });\n  const barSize = computed(() => {\n    return props.range ? `${100 * (maxValue.value - minValue.value) / (props.max - props.min)}%` : `${100 * (initData.firstValue - props.min) / (props.max - props.min)}%`;\n  });\n  const barStart = computed(() => {\n    return props.range ? `${100 * (minValue.value - props.min) / (props.max - props.min)}%` : \"0%\";\n  });\n  const runwayStyle = computed(() => {\n    return props.vertical ? {\n      height: props.height\n    } : {};\n  });\n  const barStyle = computed(() => {\n    return props.vertical ? {\n      height: barSize.value,\n      bottom: barStart.value\n    } : {\n      width: barSize.value,\n      left: barStart.value\n    };\n  });\n  const resetSize = () => {\n    if (slider.value) {\n      initData.sliderSize = slider.value[`client${props.vertical ? \"Height\" : \"Width\"}`];\n    }\n  };\n  const getButtonRefByPercent = percent => {\n    const targetValue = props.min + percent * (props.max - props.min) / 100;\n    if (!props.range) {\n      return firstButton;\n    }\n    let buttonRefName;\n    if (Math.abs(minValue.value - targetValue) < Math.abs(maxValue.value - targetValue)) {\n      buttonRefName = initData.firstValue < initData.secondValue ? \"firstButton\" : \"secondButton\";\n    } else {\n      buttonRefName = initData.firstValue > initData.secondValue ? \"firstButton\" : \"secondButton\";\n    }\n    return buttonRefs[buttonRefName];\n  };\n  const setPosition = percent => {\n    const buttonRef = getButtonRefByPercent(percent);\n    buttonRef.value.setPosition(percent);\n    return buttonRef;\n  };\n  const setFirstValue = firstValue => {\n    initData.firstValue = firstValue != null ? firstValue : props.min;\n    _emit(props.range ? [minValue.value, maxValue.value] : firstValue != null ? firstValue : props.min);\n  };\n  const setSecondValue = secondValue => {\n    initData.secondValue = secondValue;\n    if (props.range) {\n      _emit([minValue.value, maxValue.value]);\n    }\n  };\n  const _emit = val => {\n    emit(UPDATE_MODEL_EVENT, val);\n    emit(INPUT_EVENT, val);\n  };\n  const emitChange = async () => {\n    await nextTick();\n    emit(CHANGE_EVENT, props.range ? [minValue.value, maxValue.value] : props.modelValue);\n  };\n  const handleSliderPointerEvent = event => {\n    var _a, _b, _c, _d, _e, _f;\n    if (sliderDisabled.value || initData.dragging) return;\n    resetSize();\n    let newPercent = 0;\n    if (props.vertical) {\n      const clientY = (_c = (_b = (_a = event.touches) == null ? void 0 : _a.item(0)) == null ? void 0 : _b.clientY) != null ? _c : event.clientY;\n      const sliderOffsetBottom = slider.value.getBoundingClientRect().bottom;\n      newPercent = (sliderOffsetBottom - clientY) / initData.sliderSize * 100;\n    } else {\n      const clientX = (_f = (_e = (_d = event.touches) == null ? void 0 : _d.item(0)) == null ? void 0 : _e.clientX) != null ? _f : event.clientX;\n      const sliderOffsetLeft = slider.value.getBoundingClientRect().left;\n      newPercent = (clientX - sliderOffsetLeft) / initData.sliderSize * 100;\n    }\n    if (newPercent < 0 || newPercent > 100) return;\n    return setPosition(newPercent);\n  };\n  const onSliderWrapperPrevent = event => {\n    var _a, _b;\n    if (((_a = buttonRefs[\"firstButton\"].value) == null ? void 0 : _a.dragging) || ((_b = buttonRefs[\"secondButton\"].value) == null ? void 0 : _b.dragging)) {\n      event.preventDefault();\n    }\n  };\n  const onSliderDown = async event => {\n    const buttonRef = handleSliderPointerEvent(event);\n    if (buttonRef) {\n      await nextTick();\n      buttonRef.value.onButtonDown(event);\n    }\n  };\n  const onSliderClick = event => {\n    const buttonRef = handleSliderPointerEvent(event);\n    if (buttonRef) {\n      emitChange();\n    }\n  };\n  const onSliderMarkerDown = position => {\n    if (sliderDisabled.value || initData.dragging) return;\n    const buttonRef = setPosition(position);\n    if (buttonRef) {\n      emitChange();\n    }\n  };\n  return {\n    elFormItem,\n    slider,\n    firstButton,\n    secondButton,\n    sliderDisabled,\n    minValue,\n    maxValue,\n    runwayStyle,\n    barStyle,\n    resetSize,\n    setPosition,\n    emitChange,\n    onSliderWrapperPrevent,\n    onSliderClick,\n    onSliderDown,\n    onSliderMarkerDown,\n    setFirstValue,\n    setSecondValue\n  };\n};\nexport { useSlide };", "map": {"version": 3, "names": ["useSlide", "props", "initData", "emit", "form", "elForm", "formItem", "elFormItem", "useFormItem", "slider", "shallowRef", "firstButton", "ref", "second<PERSON><PERSON>on", "buttonRefs", "sliderDisabled", "computed", "disabled", "minValue", "Math", "min", "firstValue", "secondValue", "maxValue", "max", "barSize", "range", "value", "barStart", "runwayStyle", "vertical", "height", "barStyle", "bottom", "width", "left", "resetSize", "sliderSize", "getButtonRefByPercent", "percent", "targetValue", "buttonRefName", "abs", "setPosition", "buttonRef", "setFirstValue", "_emit", "setSecondValue", "val", "UPDATE_MODEL_EVENT", "INPUT_EVENT", "emitChange", "nextTick", "CHANGE_EVENT", "modelValue", "handleSliderPointerEvent", "event", "_a", "_b", "_c", "_d", "_e", "_f", "dragging", "newPercent", "clientY", "touches", "item", "sliderOffsetBottom", "getBoundingClientRect", "clientX", "sliderOffsetLeft", "onSliderWrapperPrevent", "preventDefault", "onSliderDown", "onButtonDown", "onSliderClick", "onSliderMarkerDown", "position"], "sources": ["../../../../../../../packages/components/slider/src/composables/use-slide.ts"], "sourcesContent": ["import { computed, nextTick, ref, shallowRef } from 'vue'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { useFormItem } from '@element-plus/components/form'\nimport type { CSSProperties, Ref, SetupContext } from 'vue'\nimport type { Arrayable } from '@element-plus/utils'\nimport type { SliderEmits, SliderInitData, SliderProps } from '../slider'\nimport type { ButtonRefs, SliderButtonInstance } from '../button'\n\nexport const useSlide = (\n  props: SliderProps,\n  initData: SliderInitData,\n  emit: SetupContext<SliderEmits>['emit']\n) => {\n  const { form: elForm, formItem: elFormItem } = useFormItem()\n\n  const slider = shallowRef<HTMLElement>()\n\n  const firstButton = ref<SliderButtonInstance>()\n\n  const secondButton = ref<SliderButtonInstance>()\n\n  const buttonRefs: ButtonRefs = {\n    firstButton,\n    secondButton,\n  }\n\n  const sliderDisabled = computed(() => {\n    return props.disabled || elForm?.disabled || false\n  })\n\n  const minValue = computed(() => {\n    return Math.min(initData.firstValue, initData.secondValue)\n  })\n\n  const maxValue = computed(() => {\n    return Math.max(initData.firstValue, initData.secondValue)\n  })\n\n  const barSize = computed(() => {\n    return props.range\n      ? `${\n          (100 * (maxValue.value - minValue.value)) / (props.max - props.min)\n        }%`\n      : `${\n          (100 * (initData.firstValue - props.min)) / (props.max - props.min)\n        }%`\n  })\n\n  const barStart = computed(() => {\n    return props.range\n      ? `${(100 * (minValue.value - props.min)) / (props.max - props.min)}%`\n      : '0%'\n  })\n\n  const runwayStyle = computed<CSSProperties>(() => {\n    return props.vertical ? { height: props.height } : {}\n  })\n\n  const barStyle = computed<CSSProperties>(() => {\n    return props.vertical\n      ? {\n          height: barSize.value,\n          bottom: barStart.value,\n        }\n      : {\n          width: barSize.value,\n          left: barStart.value,\n        }\n  })\n\n  const resetSize = () => {\n    if (slider.value) {\n      initData.sliderSize =\n        slider.value[`client${props.vertical ? 'Height' : 'Width'}`]\n    }\n  }\n\n  const getButtonRefByPercent = (\n    percent: number\n  ): Ref<SliderButtonInstance | undefined> => {\n    const targetValue = props.min + (percent * (props.max - props.min)) / 100\n    if (!props.range) {\n      return firstButton\n    }\n    let buttonRefName: 'firstButton' | 'secondButton'\n    if (\n      Math.abs(minValue.value - targetValue) <\n      Math.abs(maxValue.value - targetValue)\n    ) {\n      buttonRefName =\n        initData.firstValue < initData.secondValue\n          ? 'firstButton'\n          : 'secondButton'\n    } else {\n      buttonRefName =\n        initData.firstValue > initData.secondValue\n          ? 'firstButton'\n          : 'secondButton'\n    }\n    return buttonRefs[buttonRefName]\n  }\n\n  const setPosition = (\n    percent: number\n  ): Ref<SliderButtonInstance | undefined> => {\n    const buttonRef = getButtonRefByPercent(percent)\n    buttonRef.value!.setPosition(percent)\n    return buttonRef\n  }\n\n  const setFirstValue = (firstValue: number | undefined) => {\n    initData.firstValue = firstValue ?? props.min\n    _emit(\n      props.range ? [minValue.value, maxValue.value] : firstValue ?? props.min\n    )\n  }\n\n  const setSecondValue = (secondValue: number) => {\n    initData.secondValue = secondValue\n\n    if (props.range) {\n      _emit([minValue.value, maxValue.value])\n    }\n  }\n\n  const _emit = (val: Arrayable<number>) => {\n    emit(UPDATE_MODEL_EVENT, val)\n    emit(INPUT_EVENT, val)\n  }\n\n  const emitChange = async () => {\n    await nextTick()\n    emit(\n      CHANGE_EVENT,\n      props.range ? [minValue.value, maxValue.value] : props.modelValue\n    )\n  }\n\n  const handleSliderPointerEvent = (\n    event: MouseEvent | TouchEvent\n  ): Ref<SliderButtonInstance | undefined> | undefined => {\n    if (sliderDisabled.value || initData.dragging) return\n    resetSize()\n    let newPercent = 0\n    if (props.vertical) {\n      const clientY =\n        (event as TouchEvent).touches?.item(0)?.clientY ??\n        (event as MouseEvent).clientY\n      const sliderOffsetBottom = slider.value!.getBoundingClientRect().bottom\n      newPercent = ((sliderOffsetBottom - clientY) / initData.sliderSize) * 100\n    } else {\n      const clientX =\n        (event as TouchEvent).touches?.item(0)?.clientX ??\n        (event as MouseEvent).clientX\n      const sliderOffsetLeft = slider.value!.getBoundingClientRect().left\n      newPercent = ((clientX - sliderOffsetLeft) / initData.sliderSize) * 100\n    }\n    if (newPercent < 0 || newPercent > 100) return\n    return setPosition(newPercent)\n  }\n\n  const onSliderWrapperPrevent = (event: TouchEvent) => {\n    if (\n      buttonRefs['firstButton'].value?.dragging ||\n      buttonRefs['secondButton'].value?.dragging\n    ) {\n      event.preventDefault()\n    }\n  }\n\n  const onSliderDown = async (event: MouseEvent | TouchEvent) => {\n    const buttonRef = handleSliderPointerEvent(event)\n    if (buttonRef) {\n      await nextTick()\n      buttonRef.value!.onButtonDown(event)\n    }\n  }\n\n  const onSliderClick = (event: MouseEvent | TouchEvent) => {\n    const buttonRef = handleSliderPointerEvent(event)\n    if (buttonRef) {\n      emitChange()\n    }\n  }\n\n  const onSliderMarkerDown = (position: number) => {\n    if (sliderDisabled.value || initData.dragging) return\n    const buttonRef = setPosition(position)\n    if (buttonRef) {\n      emitChange()\n    }\n  }\n\n  return {\n    elFormItem,\n    slider,\n    firstButton,\n    secondButton,\n    sliderDisabled,\n    minValue,\n    maxValue,\n    runwayStyle,\n    barStyle,\n    resetSize,\n    setPosition,\n    emitChange,\n    onSliderWrapperPrevent,\n    onSliderClick,\n    onSliderDown,\n    onSliderMarkerDown,\n    setFirstValue,\n    setSecondValue,\n  }\n}\n"], "mappings": ";;;AAOY,MAACA,QAAQ,GAAGA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,KAAK;EACjD,MAAM;IAAEC,IAAI,EAAEC,MAAM;IAAEC,QAAQ,EAAEC;EAAU,CAAE,GAAGC,WAAW,EAAE;EAC5D,MAAMC,MAAM,GAAGC,UAAU,EAAE;EAC3B,MAAMC,WAAW,GAAGC,GAAG,EAAE;EACzB,MAAMC,YAAY,GAAGD,GAAG,EAAE;EAC1B,MAAME,UAAU,GAAG;IACjBH,WAAW;IACXE;EACJ,CAAG;EACD,MAAME,cAAc,GAAGC,QAAQ,CAAC,MAAM;IACpC,OAAOf,KAAK,CAACgB,QAAQ,KAAKZ,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACY,QAAQ,CAAC,IAAI,KAAK;EACjF,CAAG,CAAC;EACF,MAAMC,QAAQ,GAAGF,QAAQ,CAAC,MAAM;IAC9B,OAAOG,IAAI,CAACC,GAAG,CAAClB,QAAQ,CAACmB,UAAU,EAAEnB,QAAQ,CAACoB,WAAW,CAAC;EAC9D,CAAG,CAAC;EACF,MAAMC,QAAQ,GAAGP,QAAQ,CAAC,MAAM;IAC9B,OAAOG,IAAI,CAACK,GAAG,CAACtB,QAAQ,CAACmB,UAAU,EAAEnB,QAAQ,CAACoB,WAAW,CAAC;EAC9D,CAAG,CAAC;EACF,MAAMG,OAAO,GAAGT,QAAQ,CAAC,MAAM;IAC7B,OAAOf,KAAK,CAACyB,KAAK,GAAG,GAAG,GAAG,IAAIH,QAAQ,CAACI,KAAK,GAAGT,QAAQ,CAACS,KAAK,CAAC,IAAI1B,KAAK,CAACuB,GAAG,GAAGvB,KAAK,CAACmB,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,IAAIlB,QAAQ,CAACmB,UAAU,GAAGpB,KAAK,CAACmB,GAAG,CAAC,IAAInB,KAAK,CAACuB,GAAG,GAAGvB,KAAK,CAACmB,GAAG,CAAC,GAAG;EAC1K,CAAG,CAAC;EACF,MAAMQ,QAAQ,GAAGZ,QAAQ,CAAC,MAAM;IAC9B,OAAOf,KAAK,CAACyB,KAAK,GAAG,GAAG,GAAG,IAAIR,QAAQ,CAACS,KAAK,GAAG1B,KAAK,CAACmB,GAAG,CAAC,IAAInB,KAAK,CAACuB,GAAG,GAAGvB,KAAK,CAACmB,GAAG,CAAC,GAAG,GAAG,IAAI;EAClG,CAAG,CAAC;EACF,MAAMS,WAAW,GAAGb,QAAQ,CAAC,MAAM;IACjC,OAAOf,KAAK,CAAC6B,QAAQ,GAAG;MAAEC,MAAM,EAAE9B,KAAK,CAAC8B;IAAM,CAAE,GAAG,EAAE;EACzD,CAAG,CAAC;EACF,MAAMC,QAAQ,GAAGhB,QAAQ,CAAC,MAAM;IAC9B,OAAOf,KAAK,CAAC6B,QAAQ,GAAG;MACtBC,MAAM,EAAEN,OAAO,CAACE,KAAK;MACrBM,MAAM,EAAEL,QAAQ,CAACD;IACvB,CAAK,GAAG;MACFO,KAAK,EAAET,OAAO,CAACE,KAAK;MACpBQ,IAAI,EAAEP,QAAQ,CAACD;IACrB,CAAK;EACL,CAAG,CAAC;EACF,MAAMS,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI3B,MAAM,CAACkB,KAAK,EAAE;MAChBzB,QAAQ,CAACmC,UAAU,GAAG5B,MAAM,CAACkB,KAAK,CAAC,SAAS1B,KAAK,CAAC6B,QAAQ,GAAG,QAAQ,GAAG,OAAO,EAAE,CAAC;IACxF;EACA,CAAG;EACD,MAAMQ,qBAAqB,GAAIC,OAAO,IAAK;IACzC,MAAMC,WAAW,GAAGvC,KAAK,CAACmB,GAAG,GAAGmB,OAAO,IAAItC,KAAK,CAACuB,GAAG,GAAGvB,KAAK,CAACmB,GAAG,CAAC,GAAG,GAAG;IACvE,IAAI,CAACnB,KAAK,CAACyB,KAAK,EAAE;MAChB,OAAOf,WAAW;IACxB;IACI,IAAI8B,aAAa;IACjB,IAAItB,IAAI,CAACuB,GAAG,CAACxB,QAAQ,CAACS,KAAK,GAAGa,WAAW,CAAC,GAAGrB,IAAI,CAACuB,GAAG,CAACnB,QAAQ,CAACI,KAAK,GAAGa,WAAW,CAAC,EAAE;MACnFC,aAAa,GAAGvC,QAAQ,CAACmB,UAAU,GAAGnB,QAAQ,CAACoB,WAAW,GAAG,aAAa,GAAG,cAAc;IACjG,CAAK,MAAM;MACLmB,aAAa,GAAGvC,QAAQ,CAACmB,UAAU,GAAGnB,QAAQ,CAACoB,WAAW,GAAG,aAAa,GAAG,cAAc;IACjG;IACI,OAAOR,UAAU,CAAC2B,aAAa,CAAC;EACpC,CAAG;EACD,MAAME,WAAW,GAAIJ,OAAO,IAAK;IAC/B,MAAMK,SAAS,GAAGN,qBAAqB,CAACC,OAAO,CAAC;IAChDK,SAAS,CAACjB,KAAK,CAACgB,WAAW,CAACJ,OAAO,CAAC;IACpC,OAAOK,SAAS;EACpB,CAAG;EACD,MAAMC,aAAa,GAAIxB,UAAU,IAAK;IACpCnB,QAAQ,CAACmB,UAAU,GAAGA,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGpB,KAAK,CAACmB,GAAG;IACjE0B,KAAK,CAAC7C,KAAK,CAACyB,KAAK,GAAG,CAACR,QAAQ,CAACS,KAAK,EAAEJ,QAAQ,CAACI,KAAK,CAAC,GAAGN,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGpB,KAAK,CAACmB,GAAG,CAAC;EACvG,CAAG;EACD,MAAM2B,cAAc,GAAIzB,WAAW,IAAK;IACtCpB,QAAQ,CAACoB,WAAW,GAAGA,WAAW;IAClC,IAAIrB,KAAK,CAACyB,KAAK,EAAE;MACfoB,KAAK,CAAC,CAAC5B,QAAQ,CAACS,KAAK,EAAEJ,QAAQ,CAACI,KAAK,CAAC,CAAC;IAC7C;EACA,CAAG;EACD,MAAMmB,KAAK,GAAIE,GAAG,IAAK;IACrB7C,IAAI,CAAC8C,kBAAkB,EAAED,GAAG,CAAC;IAC7B7C,IAAI,CAAC+C,WAAW,EAAEF,GAAG,CAAC;EAC1B,CAAG;EACD,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,QAAQ,EAAE;IAChBjD,IAAI,CAACkD,YAAY,EAAEpD,KAAK,CAACyB,KAAK,GAAG,CAACR,QAAQ,CAACS,KAAK,EAAEJ,QAAQ,CAACI,KAAK,CAAC,GAAG1B,KAAK,CAACqD,UAAU,CAAC;EACzF,CAAG;EACD,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAC1B,IAAI/C,cAAc,CAACY,KAAK,IAAIzB,QAAQ,CAAC6D,QAAQ,EAC3C;IACF3B,SAAS,EAAE;IACX,IAAI4B,UAAU,GAAG,CAAC;IAClB,IAAI/D,KAAK,CAAC6B,QAAQ,EAAE;MAClB,MAAMmC,OAAO,GAAG,CAACN,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGD,KAAK,CAACU,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGT,EAAE,CAACU,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGT,EAAE,CAACO,OAAO,KAAK,IAAI,GAAGN,EAAE,GAAGH,KAAK,CAACS,OAAO;MAC3I,MAAMG,kBAAkB,GAAG3D,MAAM,CAACkB,KAAK,CAAC0C,qBAAqB,EAAE,CAACpC,MAAM;MACtE+B,UAAU,GAAG,CAACI,kBAAkB,GAAGH,OAAO,IAAI/D,QAAQ,CAACmC,UAAU,GAAG,GAAG;IAC7E,CAAK,MAAM;MACL,MAAMiC,OAAO,GAAG,CAACR,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGJ,KAAK,CAACU,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGN,EAAE,CAACO,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGN,EAAE,CAACS,OAAO,KAAK,IAAI,GAAGR,EAAE,GAAGN,KAAK,CAACc,OAAO;MAC3I,MAAMC,gBAAgB,GAAG9D,MAAM,CAACkB,KAAK,CAAC0C,qBAAqB,EAAE,CAAClC,IAAI;MAClE6B,UAAU,GAAG,CAACM,OAAO,GAAGC,gBAAgB,IAAIrE,QAAQ,CAACmC,UAAU,GAAG,GAAG;IAC3E;IACI,IAAI2B,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,GAAG,EACpC;IACF,OAAOrB,WAAW,CAACqB,UAAU,CAAC;EAClC,CAAG;EACD,MAAMQ,sBAAsB,GAAIhB,KAAK,IAAK;IACxC,IAAIC,EAAE,EAAEC,EAAE;IACV,IAAI,CAAC,CAACD,EAAE,GAAG3C,UAAU,CAAC,aAAa,CAAC,CAACa,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8B,EAAE,CAACM,QAAQ,MAAM,CAACL,EAAE,GAAG5C,UAAU,CAAC,cAAc,CAAC,CAACa,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG+B,EAAE,CAACK,QAAQ,CAAC,EAAE;MACvJP,KAAK,CAACiB,cAAc,EAAE;IAC5B;EACA,CAAG;EACD,MAAMC,YAAY,GAAG,MAAOlB,KAAK,IAAK;IACpC,MAAMZ,SAAS,GAAGW,wBAAwB,CAACC,KAAK,CAAC;IACjD,IAAIZ,SAAS,EAAE;MACb,MAAMQ,QAAQ,EAAE;MAChBR,SAAS,CAACjB,KAAK,CAACgD,YAAY,CAACnB,KAAK,CAAC;IACzC;EACA,CAAG;EACD,MAAMoB,aAAa,GAAIpB,KAAK,IAAK;IAC/B,MAAMZ,SAAS,GAAGW,wBAAwB,CAACC,KAAK,CAAC;IACjD,IAAIZ,SAAS,EAAE;MACbO,UAAU,EAAE;IAClB;EACA,CAAG;EACD,MAAM0B,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAI/D,cAAc,CAACY,KAAK,IAAIzB,QAAQ,CAAC6D,QAAQ,EAC3C;IACF,MAAMnB,SAAS,GAAGD,WAAW,CAACmC,QAAQ,CAAC;IACvC,IAAIlC,SAAS,EAAE;MACbO,UAAU,EAAE;IAClB;EACA,CAAG;EACD,OAAO;IACL5C,UAAU;IACVE,MAAM;IACNE,WAAW;IACXE,YAAY;IACZE,cAAc;IACdG,QAAQ;IACRK,QAAQ;IACRM,WAAW;IACXG,QAAQ;IACRI,SAAS;IACTO,WAAW;IACXQ,UAAU;IACVqB,sBAAsB;IACtBI,aAAa;IACbF,YAAY;IACZG,kBAAkB;IAClBhC,aAAa;IACbE;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}