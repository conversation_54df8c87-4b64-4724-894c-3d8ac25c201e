{"ast": null, "code": "import { makeInstaller } from './make-installer.mjs';\nimport Components from './component.mjs';\nimport Plugins from './plugin.mjs';\nvar installer = makeInstaller([...Components, ...Plugins]);\nexport { installer as default };", "map": {"version": 3, "names": ["installer", "makeInstaller", "Components", "Plugins"], "sources": ["../../../packages/element-plus/defaults.ts"], "sourcesContent": ["import { makeInstaller } from './make-installer'\nimport Components from './component'\nimport Plugins from './plugin'\n\nexport default makeInstaller([...Components, ...Plugins])\n"], "mappings": ";;;AAGA,IAAAA,SAAA,GAAeC,aAAa,CAAC,CAAC,GAAGC,UAAU,EAAE,GAAGC,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}