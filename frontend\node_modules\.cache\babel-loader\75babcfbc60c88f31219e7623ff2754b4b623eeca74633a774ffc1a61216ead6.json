{"ast": null, "code": "import Divider from './src/divider2.mjs';\nexport { dividerProps } from './src/divider.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElDivider = withInstall(Divider);\nexport { ElDivider, ElDivider as default };", "map": {"version": 3, "names": ["ElDivider", "withInstall", "Divider"], "sources": ["../../../../../packages/components/divider/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Divider from './src/divider.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElDivider: SFCWithInstall<typeof Divider> = withInstall(Divider)\nexport default ElDivider\n\nexport * from './src/divider'\n"], "mappings": ";;;AAEY,MAACA,SAAS,GAAGC,WAAW,CAACC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}