{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { componentSizes } from '../../../constants/size.mjs';\nimport { isArray, isString } from '@vue/shared';\nimport { isBoolean } from '../../../utils/types.mjs';\nconst formMetaProps = buildProps({\n  size: {\n    type: String,\n    values: componentSizes\n  },\n  disabled: Boolean\n});\nconst formProps = buildProps({\n  ...formMetaProps,\n  model: Object,\n  rules: {\n    type: definePropType(Object)\n  },\n  labelPosition: {\n    type: String,\n    values: [\"left\", \"right\", \"top\"],\n    default: \"right\"\n  },\n  requireAsteriskPosition: {\n    type: String,\n    values: [\"left\", \"right\"],\n    default: \"left\"\n  },\n  labelWidth: {\n    type: [String, Number],\n    default: \"\"\n  },\n  labelSuffix: {\n    type: String,\n    default: \"\"\n  },\n  inline: <PERSON>olean,\n  inlineMessage: <PERSON>olean,\n  statusIcon: <PERSON>olean,\n  showMessage: {\n    type: Boolean,\n    default: true\n  },\n  validateOnRuleChange: {\n    type: Boolean,\n    default: true\n  },\n  hideRequiredAsterisk: <PERSON>ole<PERSON>,\n  scrollToError: <PERSON>olean,\n  scrollIntoViewOptions: {\n    type: [Object, Boolean]\n  }\n});\nconst formEmits = {\n  validate: (prop, isValid, message) => (isArray(prop) || isString(prop)) && isBoolean(isValid) && isString(message)\n};\nexport { formEmits, formMetaProps, formProps };", "map": {"version": 3, "names": ["formMetaProps", "buildProps", "size", "type", "String", "values", "componentSizes", "disabled", "Boolean", "formProps", "model", "Object", "rules", "definePropType", "labelPosition", "default", "requireAsteriskPosition", "labelWidth", "Number", "labelSuffix", "inline", "inlineMessage", "statusIcon", "showMessage", "validateOnRuleChange", "hideRequiredAsterisk", "scrollToError", "scrollIntoViewOptions", "formEmits", "validate", "prop", "<PERSON><PERSON><PERSON><PERSON>", "message", "isArray", "isString", "isBoolean"], "sources": ["../../../../../../packages/components/form/src/form.ts"], "sourcesContent": ["import { componentSizes } from '@element-plus/constants'\nimport {\n  buildProps,\n  definePropType,\n  isArray,\n  isBoolean,\n  isString,\n} from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { FormItemProp } from './form-item'\nimport type { FormRules } from './types'\n\nexport const formMetaProps = buildProps({\n  /**\n   * @description Control the size of components in this form.\n   */\n  size: {\n    type: String,\n    values: componentSizes,\n  },\n  /**\n   * @description Whether to disable all components in this form. If set to `true`, it will override the `disabled` prop of the inner component.\n   */\n  disabled: Boolean,\n} as const)\n\nexport const formProps = buildProps({\n  ...formMetaProps,\n  /**\n   * @description Data of form component.\n   */\n  model: Object,\n  /**\n   * @description Validation rules of form.\n   */\n  rules: {\n    type: definePropType<FormRules>(Object),\n  },\n  /**\n   * @description Position of label. If set to `'left'` or `'right'`, `label-width` prop is also required.\n   */\n  labelPosition: {\n    type: String,\n    values: ['left', 'right', 'top'],\n    default: 'right',\n  },\n  /**\n   * @description Position of asterisk.\n   */\n  requireAsteriskPosition: {\n    type: String,\n    values: ['left', 'right'],\n    default: 'left',\n  },\n  /**\n   * @description Width of label, e.g. `'50px'`. All its direct child form items will inherit this value. `auto` is supported.\n   */\n  labelWidth: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description Suffix of the label.\n   */\n  labelSuffix: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description Whether the form is inline.\n   */\n  inline: Boolean,\n  /**\n   * @description Whether to display the error message inline with the form item.\n   */\n  inlineMessage: Boolean,\n  /**\n   * @description Whether to display an icon indicating the validation result.\n   */\n  statusIcon: Boolean,\n  /**\n   * @description Whether to show the error message.\n   */\n  showMessage: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description Whether to trigger validation when the `rules` prop is changed.\n   */\n  validateOnRuleChange: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description Whether to hide required fields should have a red asterisk (star) beside their labels.\n   */\n  hideRequiredAsterisk: Boolean,\n  /**\n   * @description When validation fails, scroll to the first error form entry.\n   */\n  scrollToError: Boolean,\n  /**\n   * @description When validation fails, it scrolls to the first error item based on the scrollIntoView option.\n   */\n  scrollIntoViewOptions: {\n    type: [Object, Boolean],\n  },\n} as const)\nexport type FormProps = ExtractPropTypes<typeof formProps>\nexport type FormMetaProps = ExtractPropTypes<typeof formMetaProps>\n\nexport const formEmits = {\n  validate: (prop: FormItemProp, isValid: boolean, message: string) =>\n    (isArray(prop) || isString(prop)) &&\n    isBoolean(isValid) &&\n    isString(message),\n}\nexport type FormEmits = typeof formEmits\n"], "mappings": ";;;;AAQY,MAACA,aAAa,GAAGC,UAAU,CAAC;EACtCC,IAAI,EAAE;IACJC,IAAI,EAAEC,MAAM;IACZC,MAAM,EAAEC;EACZ,CAAG;EACDC,QAAQ,EAAEC;AACZ,CAAC;AACW,MAACC,SAAS,GAAGR,UAAU,CAAC;EAClC,GAAGD,aAAa;EAChBU,KAAK,EAAEC,MAAM;EACbC,KAAK,EAAE;IACLT,IAAI,EAAEU,cAAc,CAACF,MAAM;EAC/B,CAAG;EACDG,aAAa,EAAE;IACbX,IAAI,EAAEC,MAAM;IACZC,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;IAChCU,OAAO,EAAE;EACb,CAAG;EACDC,uBAAuB,EAAE;IACvBb,IAAI,EAAEC,MAAM;IACZC,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;IACzBU,OAAO,EAAE;EACb,CAAG;EACDE,UAAU,EAAE;IACVd,IAAI,EAAE,CAACC,MAAM,EAAEc,MAAM,CAAC;IACtBH,OAAO,EAAE;EACb,CAAG;EACDI,WAAW,EAAE;IACXhB,IAAI,EAAEC,MAAM;IACZW,OAAO,EAAE;EACb,CAAG;EACDK,MAAM,EAAEZ,OAAO;EACfa,aAAa,EAAEb,OAAO;EACtBc,UAAU,EAAEd,OAAO;EACnBe,WAAW,EAAE;IACXpB,IAAI,EAAEK,OAAO;IACbO,OAAO,EAAE;EACb,CAAG;EACDS,oBAAoB,EAAE;IACpBrB,IAAI,EAAEK,OAAO;IACbO,OAAO,EAAE;EACb,CAAG;EACDU,oBAAoB,EAAEjB,OAAO;EAC7BkB,aAAa,EAAElB,OAAO;EACtBmB,qBAAqB,EAAE;IACrBxB,IAAI,EAAE,CAACQ,MAAM,EAAEH,OAAO;EAC1B;AACA,CAAC;AACW,MAACoB,SAAS,GAAG;EACvBC,QAAQ,EAAEA,CAACC,IAAI,EAAEC,OAAO,EAAEC,OAAO,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,IAAII,QAAQ,CAACJ,IAAI,CAAC,KAAKK,SAAS,CAACJ,OAAO,CAAC,IAAIG,QAAQ,CAACF,OAAO;AACnH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}