{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { ref, shallowRef, watch, computed, nextTick } from 'vue';\nimport { TreeOptionsEnum, NODE_CLICK, NODE_DROP, CURRENT_CHANGE, NODE_EXPAND, NODE_COLLAPSE } from '../virtual-tree.mjs';\nimport { useCheck } from './useCheck.mjs';\nimport { useFilter } from './useFilter.mjs';\nimport { isObject } from '@vue/shared';\nfunction useTree(props, emit) {\n  const expandedKeySet = ref(new Set(props.defaultExpandedKeys));\n  const currentKey = ref();\n  const tree = shallowRef();\n  const listRef = ref();\n  watch(() => props.currentNodeKey, key => {\n    currentKey.value = key;\n  }, {\n    immediate: true\n  });\n  watch(() => props.data, data => {\n    setData(data);\n  }, {\n    immediate: true\n  });\n  const {\n    isIndeterminate,\n    isChecked,\n    toggleCheckbox,\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys\n  } = useCheck(props, tree);\n  const {\n    doFilter,\n    hiddenNodeKeySet,\n    isForceHiddenExpandIcon\n  } = useFilter(props, tree);\n  const valueKey = computed(() => {\n    var _a;\n    return ((_a = props.props) == null ? void 0 : _a.value) || TreeOptionsEnum.KEY;\n  });\n  const childrenKey = computed(() => {\n    var _a;\n    return ((_a = props.props) == null ? void 0 : _a.children) || TreeOptionsEnum.CHILDREN;\n  });\n  const disabledKey = computed(() => {\n    var _a;\n    return ((_a = props.props) == null ? void 0 : _a.disabled) || TreeOptionsEnum.DISABLED;\n  });\n  const labelKey = computed(() => {\n    var _a;\n    return ((_a = props.props) == null ? void 0 : _a.label) || TreeOptionsEnum.LABEL;\n  });\n  const flattenTree = computed(() => {\n    var _a;\n    const expandedKeys = expandedKeySet.value;\n    const hiddenKeys = hiddenNodeKeySet.value;\n    const flattenNodes = [];\n    const nodes = ((_a = tree.value) == null ? void 0 : _a.treeNodes) || [];\n    const stack = [];\n    for (let i = nodes.length - 1; i >= 0; --i) {\n      stack.push(nodes[i]);\n    }\n    while (stack.length) {\n      const node = stack.pop();\n      if (hiddenKeys.has(node.key)) continue;\n      flattenNodes.push(node);\n      if (node.children && expandedKeys.has(node.key)) {\n        for (let i = node.children.length - 1; i >= 0; --i) {\n          stack.push(node.children[i]);\n        }\n      }\n    }\n    return flattenNodes;\n  });\n  const isNotEmpty = computed(() => {\n    return flattenTree.value.length > 0;\n  });\n  function createTree(data) {\n    const treeNodeMap = /* @__PURE__ */new Map();\n    const levelTreeNodeMap = /* @__PURE__ */new Map();\n    let maxLevel = 1;\n    function traverse(nodes, level = 1, parent = void 0) {\n      var _a;\n      const siblings = [];\n      for (const rawNode of nodes) {\n        const value = getKey(rawNode);\n        const node = {\n          level,\n          key: value,\n          data: rawNode\n        };\n        node.label = getLabel(rawNode);\n        node.parent = parent;\n        const children = getChildren(rawNode);\n        node.disabled = getDisabled(rawNode);\n        node.isLeaf = !children || children.length === 0;\n        if (children && children.length) {\n          node.children = traverse(children, level + 1, node);\n        }\n        siblings.push(node);\n        treeNodeMap.set(value, node);\n        if (!levelTreeNodeMap.has(level)) {\n          levelTreeNodeMap.set(level, []);\n        }\n        (_a = levelTreeNodeMap.get(level)) == null ? void 0 : _a.push(node);\n      }\n      if (level > maxLevel) {\n        maxLevel = level;\n      }\n      return siblings;\n    }\n    const treeNodes = traverse(data);\n    return {\n      treeNodeMap,\n      levelTreeNodeMap,\n      maxLevel,\n      treeNodes\n    };\n  }\n  function filter(query) {\n    const keys = doFilter(query);\n    if (keys) {\n      expandedKeySet.value = keys;\n    }\n  }\n  function getChildren(node) {\n    return node[childrenKey.value];\n  }\n  function getKey(node) {\n    if (!node) {\n      return \"\";\n    }\n    return node[valueKey.value];\n  }\n  function getDisabled(node) {\n    return node[disabledKey.value];\n  }\n  function getLabel(node) {\n    return node[labelKey.value];\n  }\n  function toggleExpand(node) {\n    const expandedKeys = expandedKeySet.value;\n    if (expandedKeys.has(node.key)) {\n      collapseNode(node);\n    } else {\n      expandNode(node);\n    }\n  }\n  function setExpandedKeys(keys) {\n    const expandedKeys = /* @__PURE__ */new Set();\n    const nodeMap = tree.value.treeNodeMap;\n    keys.forEach(k => {\n      let node = nodeMap.get(k);\n      while (node && !expandedKeys.has(node.key)) {\n        expandedKeys.add(node.key);\n        node = node.parent;\n      }\n    });\n    expandedKeySet.value = expandedKeys;\n  }\n  function handleNodeClick(node, e) {\n    emit(NODE_CLICK, node.data, node, e);\n    handleCurrentChange(node);\n    if (props.expandOnClickNode) {\n      toggleExpand(node);\n    }\n    if (props.showCheckbox && (props.checkOnClickNode || node.isLeaf && props.checkOnClickLeaf) && !node.disabled) {\n      toggleCheckbox(node, !isChecked(node), true);\n    }\n  }\n  function handleNodeDrop(node, e) {\n    emit(NODE_DROP, node.data, node, e);\n  }\n  function handleCurrentChange(node) {\n    if (!isCurrent(node)) {\n      currentKey.value = node.key;\n      emit(CURRENT_CHANGE, node.data, node);\n    }\n  }\n  function handleNodeCheck(node, checked) {\n    toggleCheckbox(node, checked);\n  }\n  function expandNode(node) {\n    const keySet = expandedKeySet.value;\n    if (tree.value && props.accordion) {\n      const {\n        treeNodeMap\n      } = tree.value;\n      keySet.forEach(key => {\n        const treeNode = treeNodeMap.get(key);\n        if (node && node.level === (treeNode == null ? void 0 : treeNode.level)) {\n          keySet.delete(key);\n        }\n      });\n    }\n    keySet.add(node.key);\n    emit(NODE_EXPAND, node.data, node);\n  }\n  function collapseNode(node) {\n    expandedKeySet.value.delete(node.key);\n    emit(NODE_COLLAPSE, node.data, node);\n  }\n  function isExpanded(node) {\n    return expandedKeySet.value.has(node.key);\n  }\n  function isDisabled(node) {\n    return !!node.disabled;\n  }\n  function isCurrent(node) {\n    const current = currentKey.value;\n    return current !== void 0 && current === node.key;\n  }\n  function getCurrentNode() {\n    var _a, _b;\n    if (!currentKey.value) return void 0;\n    return (_b = (_a = tree.value) == null ? void 0 : _a.treeNodeMap.get(currentKey.value)) == null ? void 0 : _b.data;\n  }\n  function getCurrentKey() {\n    return currentKey.value;\n  }\n  function setCurrentKey(key) {\n    currentKey.value = key;\n  }\n  function setData(data) {\n    nextTick(() => tree.value = createTree(data));\n  }\n  function getNode(data) {\n    var _a;\n    const key = isObject(data) ? getKey(data) : data;\n    return (_a = tree.value) == null ? void 0 : _a.treeNodeMap.get(key);\n  }\n  function scrollToNode(key, strategy = \"auto\") {\n    const node = getNode(key);\n    if (node && listRef.value) {\n      listRef.value.scrollToItem(flattenTree.value.indexOf(node), strategy);\n    }\n  }\n  function scrollTo(offset) {\n    var _a;\n    (_a = listRef.value) == null ? void 0 : _a.scrollTo(offset);\n  }\n  return {\n    tree,\n    flattenTree,\n    isNotEmpty,\n    listRef,\n    getKey,\n    getChildren,\n    toggleExpand,\n    toggleCheckbox,\n    isExpanded,\n    isChecked,\n    isIndeterminate,\n    isDisabled,\n    isCurrent,\n    isForceHiddenExpandIcon,\n    handleNodeClick,\n    handleNodeDrop,\n    handleNodeCheck,\n    getCurrentNode,\n    getCurrentKey,\n    setCurrentKey,\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys,\n    filter,\n    setData,\n    getNode,\n    expandNode,\n    collapseNode,\n    setExpandedKeys,\n    scrollToNode,\n    scrollTo\n  };\n}\nexport { useTree };", "map": {"version": 3, "names": ["useTree", "props", "emit", "expandedKeySet", "ref", "Set", "defaultExpandedKeys", "current<PERSON><PERSON>", "tree", "shallowRef", "listRef", "watch", "currentNodeKey", "key", "value", "immediate", "data", "setData", "isIndeterminate", "isChecked", "toggleCheckbox", "getChe<PERSON><PERSON>eys", "getCheckedNodes", "getHalfCheckedKeys", "getHalfCheckedNodes", "setChecked", "set<PERSON><PERSON><PERSON><PERSON>eys", "useCheck", "<PERSON><PERSON><PERSON><PERSON>", "hiddenNodeKeySet", "isForceHiddenExpandIcon", "useFilter", "valueKey", "computed", "_a", "TreeOptionsEnum", "KEY", "<PERSON><PERSON><PERSON>", "children", "CHILDREN", "<PERSON><PERSON><PERSON>", "disabled", "DISABLED", "labelKey", "label", "LABEL", "flattenTree", "expandedKeys", "hiddenKeys", "flattenNodes", "nodes", "treeNodes", "stack", "i", "length", "push", "node", "pop", "has", "isNotEmpty", "createTree", "treeNodeMap", "Map", "levelTreeNodeMap", "maxLevel", "traverse", "level", "parent", "siblings", "rawNode", "<PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getDisabled", "<PERSON><PERSON><PERSON><PERSON>", "set", "get", "filter", "query", "keys", "toggleExpand", "collapseNode", "expandNode", "setExpandedKeys", "nodeMap", "for<PERSON>ach", "k", "add", "handleNodeClick", "e", "NODE_CLICK", "handleCurrentChange", "expandOnClickNode", "showCheckbox", "checkOnClickNode", "checkOnClickLeaf", "handleNodeDrop", "NODE_DROP", "isCurrent", "CURRENT_CHANGE", "handleNodeCheck", "checked", "keySet", "accordion", "treeNode", "delete", "NODE_EXPAND", "NODE_COLLAPSE", "isExpanded", "isDisabled", "current", "getCurrentNode", "_b", "get<PERSON><PERSON><PERSON><PERSON>ey", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "nextTick", "getNode", "isObject", "scrollToNode", "strategy", "scrollToItem", "indexOf", "scrollTo", "offset"], "sources": ["../../../../../../../packages/components/tree-v2/src/composables/useTree.ts"], "sourcesContent": ["import { computed, nextTick, ref, shallowRef, watch } from 'vue'\nimport { isObject } from '@element-plus/utils'\nimport {\n  CURRENT_CHANGE,\n  NODE_CLICK,\n  NODE_COLLAPSE,\n  NODE_DROP,\n  NODE_EXPAND,\n  TreeOptionsEnum,\n} from '../virtual-tree'\nimport { useCheck } from './useCheck'\nimport { useFilter } from './useFilter'\nimport type {\n  FixedSizeList,\n  Alignment as ScrollStrategy,\n} from '@element-plus/components/virtual-list'\nimport type { SetupContext } from 'vue'\nimport type { treeEmits } from '../virtual-tree'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\nimport type {\n  Tree,\n  TreeData,\n  TreeKey,\n  TreeNode,\n  TreeNodeData,\n  TreeProps,\n} from '../types'\n\nexport function useTree(\n  props: TreeProps,\n  emit: SetupContext<typeof treeEmits>['emit']\n) {\n  const expandedKeySet = ref<Set<TreeKey>>(new Set(props.defaultExpandedKeys))\n  const currentKey = ref<TreeKey | undefined>()\n  const tree = shallowRef<Tree | undefined>()\n  const listRef = ref<typeof FixedSizeList | undefined>()\n\n  watch(\n    () => props.currentNodeKey,\n    (key) => {\n      currentKey.value = key\n    },\n    {\n      immediate: true,\n    }\n  )\n\n  watch(\n    () => props.data,\n    (data: TreeData) => {\n      setData(data)\n    },\n    {\n      immediate: true,\n    }\n  )\n\n  const {\n    isIndeterminate,\n    isChecked,\n    toggleCheckbox,\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys,\n  } = useCheck(props, tree)\n\n  const { doFilter, hiddenNodeKeySet, isForceHiddenExpandIcon } = useFilter(\n    props,\n    tree\n  )\n\n  const valueKey = computed(() => {\n    return props.props?.value || TreeOptionsEnum.KEY\n  })\n  const childrenKey = computed(() => {\n    return props.props?.children || TreeOptionsEnum.CHILDREN\n  })\n  const disabledKey = computed(() => {\n    return props.props?.disabled || TreeOptionsEnum.DISABLED\n  })\n  const labelKey = computed(() => {\n    return props.props?.label || TreeOptionsEnum.LABEL\n  })\n\n  const flattenTree = computed(() => {\n    const expandedKeys = expandedKeySet.value\n    const hiddenKeys = hiddenNodeKeySet.value\n    const flattenNodes: TreeNode[] = []\n    const nodes = tree.value?.treeNodes || []\n\n    const stack: TreeNode[] = []\n    for (let i = nodes.length - 1; i >= 0; --i) {\n      stack.push(nodes[i])\n    }\n    while (stack.length) {\n      const node = stack.pop()!\n      if (hiddenKeys.has(node.key)) continue\n\n      flattenNodes.push(node)\n      if (node.children && expandedKeys.has(node.key)) {\n        for (let i = node.children.length - 1; i >= 0; --i) {\n          stack.push(node.children[i])\n        }\n      }\n    }\n\n    return flattenNodes\n  })\n\n  const isNotEmpty = computed(() => {\n    return flattenTree.value.length > 0\n  })\n\n  function createTree(data: TreeData): Tree {\n    const treeNodeMap: Map<TreeKey, TreeNode> = new Map()\n    const levelTreeNodeMap: Map<number, TreeNode[]> = new Map()\n    let maxLevel = 1\n    function traverse(\n      nodes: TreeData,\n      level = 1,\n      parent: TreeNode | undefined = undefined\n    ) {\n      const siblings: TreeNode[] = []\n      for (const rawNode of nodes) {\n        const value = getKey(rawNode)\n        const node: TreeNode = {\n          level,\n          key: value,\n          data: rawNode,\n        }\n        node.label = getLabel(rawNode)\n        node.parent = parent\n        const children = getChildren(rawNode)\n        node.disabled = getDisabled(rawNode)\n        node.isLeaf = !children || children.length === 0\n        if (children && children.length) {\n          node.children = traverse(children, level + 1, node)\n        }\n        siblings.push(node)\n        treeNodeMap.set(value, node)\n        if (!levelTreeNodeMap.has(level)) {\n          levelTreeNodeMap.set(level, [])\n        }\n        levelTreeNodeMap.get(level)?.push(node)\n      }\n      if (level > maxLevel) {\n        maxLevel = level\n      }\n      return siblings\n    }\n    const treeNodes: TreeNode[] = traverse(data)\n    return {\n      treeNodeMap,\n      levelTreeNodeMap,\n      maxLevel,\n      treeNodes,\n    }\n  }\n\n  function filter(query: string) {\n    const keys = doFilter(query)\n    if (keys) {\n      expandedKeySet.value = keys\n    }\n  }\n\n  function getChildren(node: TreeNodeData): TreeNodeData[] {\n    return node[childrenKey.value]\n  }\n\n  function getKey(node: TreeNodeData): TreeKey {\n    if (!node) {\n      return ''\n    }\n    return node[valueKey.value]\n  }\n\n  function getDisabled(node: TreeNodeData): boolean {\n    return node[disabledKey.value]\n  }\n\n  function getLabel(node: TreeNodeData): string {\n    return node[labelKey.value]\n  }\n\n  function toggleExpand(node: TreeNode) {\n    const expandedKeys = expandedKeySet.value\n    if (expandedKeys.has(node.key)) {\n      collapseNode(node)\n    } else {\n      expandNode(node)\n    }\n  }\n\n  function setExpandedKeys(keys: TreeKey[]) {\n    const expandedKeys = new Set<TreeKey>()\n    const nodeMap = tree.value!.treeNodeMap\n\n    keys.forEach((k) => {\n      let node = nodeMap.get(k)\n      while (node && !expandedKeys.has(node.key)) {\n        expandedKeys.add(node.key)\n        node = node.parent\n      }\n    })\n\n    expandedKeySet.value = expandedKeys\n  }\n\n  function handleNodeClick(node: TreeNode, e: MouseEvent) {\n    emit(NODE_CLICK, node.data, node, e)\n    handleCurrentChange(node)\n    if (props.expandOnClickNode) {\n      toggleExpand(node)\n    }\n    if (\n      props.showCheckbox &&\n      (props.checkOnClickNode || (node.isLeaf && props.checkOnClickLeaf)) &&\n      !node.disabled\n    ) {\n      toggleCheckbox(node, !isChecked(node), true)\n    }\n  }\n\n  function handleNodeDrop(node: TreeNode, e: DragEvent) {\n    emit(NODE_DROP, node.data, node, e)\n  }\n\n  function handleCurrentChange(node: TreeNode) {\n    if (!isCurrent(node)) {\n      currentKey.value = node.key\n      emit(CURRENT_CHANGE, node.data, node)\n    }\n  }\n\n  function handleNodeCheck(node: TreeNode, checked: CheckboxValueType) {\n    toggleCheckbox(node, checked)\n  }\n\n  function expandNode(node: TreeNode) {\n    const keySet = expandedKeySet.value\n    if (tree.value && props.accordion) {\n      // whether only one node among the same level can be expanded at one time\n      const { treeNodeMap } = tree.value\n      keySet.forEach((key) => {\n        const treeNode = treeNodeMap.get(key)\n        if (node && node.level === treeNode?.level) {\n          keySet.delete(key)\n        }\n      })\n    }\n    keySet.add(node.key)\n    emit(NODE_EXPAND, node.data, node)\n  }\n\n  function collapseNode(node: TreeNode) {\n    expandedKeySet.value.delete(node.key)\n    emit(NODE_COLLAPSE, node.data, node)\n  }\n\n  function isExpanded(node: TreeNode): boolean {\n    return expandedKeySet.value.has(node.key)\n  }\n\n  function isDisabled(node: TreeNode): boolean {\n    return !!node.disabled\n  }\n\n  function isCurrent(node: TreeNode): boolean {\n    const current = currentKey.value\n    return current !== undefined && current === node.key\n  }\n\n  function getCurrentNode(): TreeNodeData | undefined {\n    if (!currentKey.value) return undefined\n    return tree.value?.treeNodeMap.get(currentKey.value)?.data\n  }\n\n  function getCurrentKey(): TreeKey | undefined {\n    return currentKey.value\n  }\n\n  function setCurrentKey(key: TreeKey): void {\n    currentKey.value = key\n  }\n\n  function setData(data: TreeData) {\n    nextTick(() => (tree.value = createTree(data)))\n  }\n\n  function getNode(data: TreeKey | TreeNodeData) {\n    const key = isObject(data) ? getKey(data) : data\n    return tree.value?.treeNodeMap.get(key)\n  }\n\n  function scrollToNode(key: TreeKey, strategy: ScrollStrategy = 'auto') {\n    const node = getNode(key)\n    if (node && listRef.value) {\n      listRef.value.scrollToItem(flattenTree.value.indexOf(node), strategy)\n    }\n  }\n\n  function scrollTo(offset: number) {\n    listRef.value?.scrollTo(offset)\n  }\n\n  return {\n    tree,\n    flattenTree,\n    isNotEmpty,\n    listRef,\n    getKey,\n    getChildren,\n    toggleExpand,\n    toggleCheckbox,\n    isExpanded,\n    isChecked,\n    isIndeterminate,\n    isDisabled,\n    isCurrent,\n    isForceHiddenExpandIcon,\n    handleNodeClick,\n    handleNodeDrop,\n    handleNodeCheck,\n    // expose\n    getCurrentNode,\n    getCurrentKey,\n    setCurrentKey,\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys,\n    filter,\n    setData,\n    getNode,\n    expandNode,\n    collapseNode,\n    setExpandedKeys,\n    scrollToNode,\n    scrollTo,\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAYO,SAASA,OAAOA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACnC,MAAMC,cAAc,GAAGC,GAAG,CAAC,IAAIC,GAAG,CAACJ,KAAK,CAACK,mBAAmB,CAAC,CAAC;EAC9D,MAAMC,UAAU,GAAGH,GAAG,EAAE;EACxB,MAAMI,IAAI,GAAGC,UAAU,EAAE;EACzB,MAAMC,OAAO,GAAGN,GAAG,EAAE;EACrBO,KAAK,CAAC,MAAMV,KAAK,CAACW,cAAc,EAAGC,GAAG,IAAK;IACzCN,UAAU,CAACO,KAAK,GAAGD,GAAG;EAC1B,CAAG,EAAE;IACDE,SAAS,EAAE;EACf,CAAG,CAAC;EACFJ,KAAK,CAAC,MAAMV,KAAK,CAACe,IAAI,EAAGA,IAAI,IAAK;IAChCC,OAAO,CAACD,IAAI,CAAC;EACjB,CAAG,EAAE;IACDD,SAAS,EAAE;EACf,CAAG,CAAC;EACF,MAAM;IACJG,eAAe;IACfC,SAAS;IACTC,cAAc;IACdC,cAAc;IACdC,eAAe;IACfC,kBAAkB;IAClBC,mBAAmB;IACnBC,UAAU;IACVC;EACJ,CAAG,GAAGC,QAAQ,CAAC1B,KAAK,EAAEO,IAAI,CAAC;EACzB,MAAM;IAAEoB,QAAQ;IAAEC,gBAAgB;IAAEC;EAAuB,CAAE,GAAGC,SAAS,CAAC9B,KAAK,EAAEO,IAAI,CAAC;EACtF,MAAMwB,QAAQ,GAAGC,QAAQ,CAAC,MAAM;IAC9B,IAAIC,EAAE;IACN,OAAO,CAAC,CAACA,EAAE,GAAGjC,KAAK,CAACA,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiC,EAAE,CAACpB,KAAK,KAAKqB,eAAe,CAACC,GAAG;EAClF,CAAG,CAAC;EACF,MAAMC,WAAW,GAAGJ,QAAQ,CAAC,MAAM;IACjC,IAAIC,EAAE;IACN,OAAO,CAAC,CAACA,EAAE,GAAGjC,KAAK,CAACA,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiC,EAAE,CAACI,QAAQ,KAAKH,eAAe,CAACI,QAAQ;EAC1F,CAAG,CAAC;EACF,MAAMC,WAAW,GAAGP,QAAQ,CAAC,MAAM;IACjC,IAAIC,EAAE;IACN,OAAO,CAAC,CAACA,EAAE,GAAGjC,KAAK,CAACA,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiC,EAAE,CAACO,QAAQ,KAAKN,eAAe,CAACO,QAAQ;EAC1F,CAAG,CAAC;EACF,MAAMC,QAAQ,GAAGV,QAAQ,CAAC,MAAM;IAC9B,IAAIC,EAAE;IACN,OAAO,CAAC,CAACA,EAAE,GAAGjC,KAAK,CAACA,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiC,EAAE,CAACU,KAAK,KAAKT,eAAe,CAACU,KAAK;EACpF,CAAG,CAAC;EACF,MAAMC,WAAW,GAAGb,QAAQ,CAAC,MAAM;IACjC,IAAIC,EAAE;IACN,MAAMa,YAAY,GAAG5C,cAAc,CAACW,KAAK;IACzC,MAAMkC,UAAU,GAAGnB,gBAAgB,CAACf,KAAK;IACzC,MAAMmC,YAAY,GAAG,EAAE;IACvB,MAAMC,KAAK,GAAG,CAAC,CAAChB,EAAE,GAAG1B,IAAI,CAACM,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoB,EAAE,CAACiB,SAAS,KAAK,EAAE;IACvE,MAAMC,KAAK,GAAG,EAAE;IAChB,KAAK,IAAIC,CAAC,GAAGH,KAAK,CAACI,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC1CD,KAAK,CAACG,IAAI,CAACL,KAAK,CAACG,CAAC,CAAC,CAAC;IAC1B;IACI,OAAOD,KAAK,CAACE,MAAM,EAAE;MACnB,MAAME,IAAI,GAAGJ,KAAK,CAACK,GAAG,EAAE;MACxB,IAAIT,UAAU,CAACU,GAAG,CAACF,IAAI,CAAC3C,GAAG,CAAC,EAC1B;MACFoC,YAAY,CAACM,IAAI,CAACC,IAAI,CAAC;MACvB,IAAIA,IAAI,CAAClB,QAAQ,IAAIS,YAAY,CAACW,GAAG,CAACF,IAAI,CAAC3C,GAAG,CAAC,EAAE;QAC/C,KAAK,IAAIwC,CAAC,GAAGG,IAAI,CAAClB,QAAQ,CAACgB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;UAClDD,KAAK,CAACG,IAAI,CAACC,IAAI,CAAClB,QAAQ,CAACe,CAAC,CAAC,CAAC;QACtC;MACA;IACA;IACI,OAAOJ,YAAY;EACvB,CAAG,CAAC;EACF,MAAMU,UAAU,GAAG1B,QAAQ,CAAC,MAAM;IAChC,OAAOa,WAAW,CAAChC,KAAK,CAACwC,MAAM,GAAG,CAAC;EACvC,CAAG,CAAC;EACF,SAASM,UAAUA,CAAC5C,IAAI,EAAE;IACxB,MAAM6C,WAAW,kBAAmB,IAAIC,GAAG,EAAE;IAC7C,MAAMC,gBAAgB,kBAAmB,IAAID,GAAG,EAAE;IAClD,IAAIE,QAAQ,GAAG,CAAC;IAChB,SAASC,QAAQA,CAACf,KAAK,EAAEgB,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,KAAK,CAAC,EAAE;MACnD,IAAIjC,EAAE;MACN,MAAMkC,QAAQ,GAAG,EAAE;MACnB,KAAK,MAAMC,OAAO,IAAInB,KAAK,EAAE;QAC3B,MAAMpC,KAAK,GAAGwD,MAAM,CAACD,OAAO,CAAC;QAC7B,MAAMb,IAAI,GAAG;UACXU,KAAK;UACLrD,GAAG,EAAEC,KAAK;UACVE,IAAI,EAAEqD;QAChB,CAAS;QACDb,IAAI,CAACZ,KAAK,GAAG2B,QAAQ,CAACF,OAAO,CAAC;QAC9Bb,IAAI,CAACW,MAAM,GAAGA,MAAM;QACpB,MAAM7B,QAAQ,GAAGkC,WAAW,CAACH,OAAO,CAAC;QACrCb,IAAI,CAACf,QAAQ,GAAGgC,WAAW,CAACJ,OAAO,CAAC;QACpCb,IAAI,CAACkB,MAAM,GAAG,CAACpC,QAAQ,IAAIA,QAAQ,CAACgB,MAAM,KAAK,CAAC;QAChD,IAAIhB,QAAQ,IAAIA,QAAQ,CAACgB,MAAM,EAAE;UAC/BE,IAAI,CAAClB,QAAQ,GAAG2B,QAAQ,CAAC3B,QAAQ,EAAE4B,KAAK,GAAG,CAAC,EAAEV,IAAI,CAAC;QAC7D;QACQY,QAAQ,CAACb,IAAI,CAACC,IAAI,CAAC;QACnBK,WAAW,CAACc,GAAG,CAAC7D,KAAK,EAAE0C,IAAI,CAAC;QAC5B,IAAI,CAACO,gBAAgB,CAACL,GAAG,CAACQ,KAAK,CAAC,EAAE;UAChCH,gBAAgB,CAACY,GAAG,CAACT,KAAK,EAAE,EAAE,CAAC;QACzC;QACQ,CAAChC,EAAE,GAAG6B,gBAAgB,CAACa,GAAG,CAACV,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGhC,EAAE,CAACqB,IAAI,CAACC,IAAI,CAAC;MAC3E;MACM,IAAIU,KAAK,GAAGF,QAAQ,EAAE;QACpBA,QAAQ,GAAGE,KAAK;MACxB;MACM,OAAOE,QAAQ;IACrB;IACI,MAAMjB,SAAS,GAAGc,QAAQ,CAACjD,IAAI,CAAC;IAChC,OAAO;MACL6C,WAAW;MACXE,gBAAgB;MAChBC,QAAQ;MACRb;IACN,CAAK;EACL;EACE,SAAS0B,MAAMA,CAACC,KAAK,EAAE;IACrB,MAAMC,IAAI,GAAGnD,QAAQ,CAACkD,KAAK,CAAC;IAC5B,IAAIC,IAAI,EAAE;MACR5E,cAAc,CAACW,KAAK,GAAGiE,IAAI;IACjC;EACA;EACE,SAASP,WAAWA,CAAChB,IAAI,EAAE;IACzB,OAAOA,IAAI,CAACnB,WAAW,CAACvB,KAAK,CAAC;EAClC;EACE,SAASwD,MAAMA,CAACd,IAAI,EAAE;IACpB,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,EAAE;IACf;IACI,OAAOA,IAAI,CAACxB,QAAQ,CAAClB,KAAK,CAAC;EAC/B;EACE,SAAS2D,WAAWA,CAACjB,IAAI,EAAE;IACzB,OAAOA,IAAI,CAAChB,WAAW,CAAC1B,KAAK,CAAC;EAClC;EACE,SAASyD,QAAQA,CAACf,IAAI,EAAE;IACtB,OAAOA,IAAI,CAACb,QAAQ,CAAC7B,KAAK,CAAC;EAC/B;EACE,SAASkE,YAAYA,CAACxB,IAAI,EAAE;IAC1B,MAAMT,YAAY,GAAG5C,cAAc,CAACW,KAAK;IACzC,IAAIiC,YAAY,CAACW,GAAG,CAACF,IAAI,CAAC3C,GAAG,CAAC,EAAE;MAC9BoE,YAAY,CAACzB,IAAI,CAAC;IACxB,CAAK,MAAM;MACL0B,UAAU,CAAC1B,IAAI,CAAC;IACtB;EACA;EACE,SAAS2B,eAAeA,CAACJ,IAAI,EAAE;IAC7B,MAAMhC,YAAY,kBAAmB,IAAI1C,GAAG,EAAE;IAC9C,MAAM+E,OAAO,GAAG5E,IAAI,CAACM,KAAK,CAAC+C,WAAW;IACtCkB,IAAI,CAACM,OAAO,CAAEC,CAAC,IAAK;MAClB,IAAI9B,IAAI,GAAG4B,OAAO,CAACR,GAAG,CAACU,CAAC,CAAC;MACzB,OAAO9B,IAAI,IAAI,CAACT,YAAY,CAACW,GAAG,CAACF,IAAI,CAAC3C,GAAG,CAAC,EAAE;QAC1CkC,YAAY,CAACwC,GAAG,CAAC/B,IAAI,CAAC3C,GAAG,CAAC;QAC1B2C,IAAI,GAAGA,IAAI,CAACW,MAAM;MAC1B;IACA,CAAK,CAAC;IACFhE,cAAc,CAACW,KAAK,GAAGiC,YAAY;EACvC;EACE,SAASyC,eAAeA,CAAChC,IAAI,EAAEiC,CAAC,EAAE;IAChCvF,IAAI,CAACwF,UAAU,EAAElC,IAAI,CAACxC,IAAI,EAAEwC,IAAI,EAAEiC,CAAC,CAAC;IACpCE,mBAAmB,CAACnC,IAAI,CAAC;IACzB,IAAIvD,KAAK,CAAC2F,iBAAiB,EAAE;MAC3BZ,YAAY,CAACxB,IAAI,CAAC;IACxB;IACI,IAAIvD,KAAK,CAAC4F,YAAY,KAAK5F,KAAK,CAAC6F,gBAAgB,IAAItC,IAAI,CAACkB,MAAM,IAAIzE,KAAK,CAAC8F,gBAAgB,CAAC,IAAI,CAACvC,IAAI,CAACf,QAAQ,EAAE;MAC7GrB,cAAc,CAACoC,IAAI,EAAE,CAACrC,SAAS,CAACqC,IAAI,CAAC,EAAE,IAAI,CAAC;IAClD;EACA;EACE,SAASwC,cAAcA,CAACxC,IAAI,EAAEiC,CAAC,EAAE;IAC/BvF,IAAI,CAAC+F,SAAS,EAAEzC,IAAI,CAACxC,IAAI,EAAEwC,IAAI,EAAEiC,CAAC,CAAC;EACvC;EACE,SAASE,mBAAmBA,CAACnC,IAAI,EAAE;IACjC,IAAI,CAAC0C,SAAS,CAAC1C,IAAI,CAAC,EAAE;MACpBjD,UAAU,CAACO,KAAK,GAAG0C,IAAI,CAAC3C,GAAG;MAC3BX,IAAI,CAACiG,cAAc,EAAE3C,IAAI,CAACxC,IAAI,EAAEwC,IAAI,CAAC;IAC3C;EACA;EACE,SAAS4C,eAAeA,CAAC5C,IAAI,EAAE6C,OAAO,EAAE;IACtCjF,cAAc,CAACoC,IAAI,EAAE6C,OAAO,CAAC;EACjC;EACE,SAASnB,UAAUA,CAAC1B,IAAI,EAAE;IACxB,MAAM8C,MAAM,GAAGnG,cAAc,CAACW,KAAK;IACnC,IAAIN,IAAI,CAACM,KAAK,IAAIb,KAAK,CAACsG,SAAS,EAAE;MACjC,MAAM;QAAE1C;MAAW,CAAE,GAAGrD,IAAI,CAACM,KAAK;MAClCwF,MAAM,CAACjB,OAAO,CAAExE,GAAG,IAAK;QACtB,MAAM2F,QAAQ,GAAG3C,WAAW,CAACe,GAAG,CAAC/D,GAAG,CAAC;QACrC,IAAI2C,IAAI,IAAIA,IAAI,CAACU,KAAK,MAAMsC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACtC,KAAK,CAAC,EAAE;UACvEoC,MAAM,CAACG,MAAM,CAAC5F,GAAG,CAAC;QAC5B;MACA,CAAO,CAAC;IACR;IACIyF,MAAM,CAACf,GAAG,CAAC/B,IAAI,CAAC3C,GAAG,CAAC;IACpBX,IAAI,CAACwG,WAAW,EAAElD,IAAI,CAACxC,IAAI,EAAEwC,IAAI,CAAC;EACtC;EACE,SAASyB,YAAYA,CAACzB,IAAI,EAAE;IAC1BrD,cAAc,CAACW,KAAK,CAAC2F,MAAM,CAACjD,IAAI,CAAC3C,GAAG,CAAC;IACrCX,IAAI,CAACyG,aAAa,EAAEnD,IAAI,CAACxC,IAAI,EAAEwC,IAAI,CAAC;EACxC;EACE,SAASoD,UAAUA,CAACpD,IAAI,EAAE;IACxB,OAAOrD,cAAc,CAACW,KAAK,CAAC4C,GAAG,CAACF,IAAI,CAAC3C,GAAG,CAAC;EAC7C;EACE,SAASgG,UAAUA,CAACrD,IAAI,EAAE;IACxB,OAAO,CAAC,CAACA,IAAI,CAACf,QAAQ;EAC1B;EACE,SAASyD,SAASA,CAAC1C,IAAI,EAAE;IACvB,MAAMsD,OAAO,GAAGvG,UAAU,CAACO,KAAK;IAChC,OAAOgG,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,KAAKtD,IAAI,CAAC3C,GAAG;EACrD;EACE,SAASkG,cAAcA,CAAA,EAAG;IACxB,IAAI7E,EAAE,EAAE8E,EAAE;IACV,IAAI,CAACzG,UAAU,CAACO,KAAK,EACnB,OAAO,KAAK,CAAC;IACf,OAAO,CAACkG,EAAE,GAAG,CAAC9E,EAAE,GAAG1B,IAAI,CAACM,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoB,EAAE,CAAC2B,WAAW,CAACe,GAAG,CAACrE,UAAU,CAACO,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkG,EAAE,CAAChG,IAAI;EACtH;EACE,SAASiG,aAAaA,CAAA,EAAG;IACvB,OAAO1G,UAAU,CAACO,KAAK;EAC3B;EACE,SAASoG,aAAaA,CAACrG,GAAG,EAAE;IAC1BN,UAAU,CAACO,KAAK,GAAGD,GAAG;EAC1B;EACE,SAASI,OAAOA,CAACD,IAAI,EAAE;IACrBmG,QAAQ,CAAC,MAAM3G,IAAI,CAACM,KAAK,GAAG8C,UAAU,CAAC5C,IAAI,CAAC,CAAC;EACjD;EACE,SAASoG,OAAOA,CAACpG,IAAI,EAAE;IACrB,IAAIkB,EAAE;IACN,MAAMrB,GAAG,GAAGwG,QAAQ,CAACrG,IAAI,CAAC,GAAGsD,MAAM,CAACtD,IAAI,CAAC,GAAGA,IAAI;IAChD,OAAO,CAACkB,EAAE,GAAG1B,IAAI,CAACM,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoB,EAAE,CAAC2B,WAAW,CAACe,GAAG,CAAC/D,GAAG,CAAC;EACvE;EACE,SAASyG,YAAYA,CAACzG,GAAG,EAAE0G,QAAQ,GAAG,MAAM,EAAE;IAC5C,MAAM/D,IAAI,GAAG4D,OAAO,CAACvG,GAAG,CAAC;IACzB,IAAI2C,IAAI,IAAI9C,OAAO,CAACI,KAAK,EAAE;MACzBJ,OAAO,CAACI,KAAK,CAAC0G,YAAY,CAAC1E,WAAW,CAAChC,KAAK,CAAC2G,OAAO,CAACjE,IAAI,CAAC,EAAE+D,QAAQ,CAAC;IAC3E;EACA;EACE,SAASG,QAAQA,CAACC,MAAM,EAAE;IACxB,IAAIzF,EAAE;IACN,CAACA,EAAE,GAAGxB,OAAO,CAACI,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoB,EAAE,CAACwF,QAAQ,CAACC,MAAM,CAAC;EAC/D;EACE,OAAO;IACLnH,IAAI;IACJsC,WAAW;IACXa,UAAU;IACVjD,OAAO;IACP4D,MAAM;IACNE,WAAW;IACXQ,YAAY;IACZ5D,cAAc;IACdwF,UAAU;IACVzF,SAAS;IACTD,eAAe;IACf2F,UAAU;IACVX,SAAS;IACTpE,uBAAuB;IACvB0D,eAAe;IACfQ,cAAc;IACdI,eAAe;IACfW,cAAc;IACdE,aAAa;IACbC,aAAa;IACb7F,cAAc;IACdC,eAAe;IACfC,kBAAkB;IAClBC,mBAAmB;IACnBC,UAAU;IACVC,cAAc;IACdmD,MAAM;IACN5D,OAAO;IACPmG,OAAO;IACPlC,UAAU;IACVD,YAAY;IACZE,eAAe;IACfmC,YAAY;IACZI;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}