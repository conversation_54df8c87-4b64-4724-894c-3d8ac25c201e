{"ast": null, "code": "/**\n * Copies the values of `source` to `array`.\n *\n * @private\n * @param {Array} source The array to copy values from.\n * @param {Array} [array=[]] The array to copy values to.\n * @returns {Array} Returns `array`.\n */\nfunction copyArray(source, array) {\n  var index = -1,\n    length = source.length;\n  array || (array = Array(length));\n  while (++index < length) {\n    array[index] = source[index];\n  }\n  return array;\n}\nexport default copyArray;", "map": {"version": 3, "names": ["copyArray", "source", "array", "index", "length", "Array"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/_copyArray.js"], "sourcesContent": ["/**\n * Copies the values of `source` to `array`.\n *\n * @private\n * @param {Array} source The array to copy values from.\n * @param {Array} [array=[]] The array to copy values to.\n * @returns {Array} Returns `array`.\n */\nfunction copyArray(source, array) {\n  var index = -1,\n      length = source.length;\n\n  array || (array = Array(length));\n  while (++index < length) {\n    array[index] = source[index];\n  }\n  return array;\n}\n\nexport default copyArray;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAChC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,MAAM,CAACG,MAAM;EAE1BF,KAAK,KAAKA,KAAK,GAAGG,KAAK,CAACD,MAAM,CAAC,CAAC;EAChC,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvBF,KAAK,CAACC,KAAK,CAAC,GAAGF,MAAM,CAACE,KAAK,CAAC;EAC9B;EACA,OAAOD,KAAK;AACd;AAEA,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}