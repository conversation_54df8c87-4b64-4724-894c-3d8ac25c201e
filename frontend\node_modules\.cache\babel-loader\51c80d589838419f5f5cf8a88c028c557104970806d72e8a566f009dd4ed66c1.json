{"ast": null, "code": "import { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../../utils/typescript.mjs';\nimport { componentSizes } from '../../../../constants/size.mjs';\nconst paginationSizesProps = buildProps({\n  pageSize: {\n    type: Number,\n    required: true\n  },\n  pageSizes: {\n    type: definePropType(Array),\n    default: () => mutable([10, 20, 30, 40, 50, 100])\n  },\n  popperClass: {\n    type: String\n  },\n  disabled: Boolean,\n  teleported: Boolean,\n  size: {\n    type: String,\n    values: componentSizes\n  },\n  appendSizeTo: String\n});\nexport { paginationSizesProps };", "map": {"version": 3, "names": ["paginationSizesProps", "buildProps", "pageSize", "type", "Number", "required", "pageSizes", "definePropType", "Array", "default", "mutable", "popperClass", "String", "disabled", "Boolean", "teleported", "size", "values", "componentSizes", "appendSizeTo"], "sources": ["../../../../../../../packages/components/pagination/src/components/sizes.ts"], "sourcesContent": ["import { buildProps, definePropType, mutable } from '@element-plus/utils'\nimport { componentSizes } from '@element-plus/constants'\nimport type { ExtractPropTypes } from 'vue'\nimport type Sizes from './sizes.vue'\n\nexport const paginationSizesProps = buildProps({\n  pageSize: {\n    type: Number,\n    required: true,\n  },\n  pageSizes: {\n    type: definePropType<number[]>(Array),\n    default: () => mutable([10, 20, 30, 40, 50, 100] as const),\n  },\n  popperClass: {\n    type: String,\n  },\n  disabled: Boolean,\n  teleported: Boolean,\n  size: {\n    type: String,\n    values: componentSizes,\n  },\n  appendSizeTo: String,\n} as const)\n\nexport type PaginationSizesProps = ExtractPropTypes<typeof paginationSizesProps>\n\nexport type SizesInstance = InstanceType<typeof Sizes> & unknown\n"], "mappings": ";;;AAEY,MAACA,oBAAoB,GAAGC,UAAU,CAAC;EAC7CC,QAAQ,EAAE;IACRC,IAAI,EAAEC,MAAM;IACZC,QAAQ,EAAE;EACd,CAAG;EACDC,SAAS,EAAE;IACTH,IAAI,EAAEI,cAAc,CAACC,KAAK,CAAC;IAC3BC,OAAO,EAAEA,CAAA,KAAMC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EACpD,CAAG;EACDC,WAAW,EAAE;IACXR,IAAI,EAAES;EACV,CAAG;EACDC,QAAQ,EAAEC,OAAO;EACjBC,UAAU,EAAED,OAAO;EACnBE,IAAI,EAAE;IACJb,IAAI,EAAES,MAAM;IACZK,MAAM,EAAEC;EACZ,CAAG;EACDC,YAAY,EAAEP;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}