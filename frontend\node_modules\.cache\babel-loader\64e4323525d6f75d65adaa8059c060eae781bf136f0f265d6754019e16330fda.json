{"ast": null, "code": "import { ElInfiniteScroll } from './components/infinite-scroll/index.mjs';\nimport { ElLoading } from './components/loading/index.mjs';\nimport { ElMessage } from './components/message/index.mjs';\nimport { ElMessageBox } from './components/message-box/index.mjs';\nimport { ElNotification } from './components/notification/index.mjs';\nimport { ElPopoverDirective } from './components/popover/index.mjs';\nvar Plugins = [ElInfiniteScroll, ElLoading, ElMessage, ElMessageBox, ElNotification, ElPopoverDirective];\nexport { Plugins as default };", "map": {"version": 3, "names": ["Plugins", "ElInfiniteScroll", "ElLoading", "ElMessage", "ElMessageBox", "ElNotification", "ElPopoverDirective"], "sources": ["../../../packages/element-plus/plugin.ts"], "sourcesContent": ["import { ElInfiniteScroll } from '@element-plus/components/infinite-scroll'\nimport { ElLoading } from '@element-plus/components/loading'\nimport { ElMessage } from '@element-plus/components/message'\nimport { ElMessageBox } from '@element-plus/components/message-box'\nimport { ElNotification } from '@element-plus/components/notification'\nimport { ElPopoverDirective } from '@element-plus/components/popover'\n\nimport type { Plugin } from 'vue'\n\nexport default [\n  ElInfiniteScroll,\n  ElLoading,\n  ElMessage,\n  ElMessageBox,\n  ElNotification,\n  ElPopoverDirective,\n] as Plugin[]\n"], "mappings": ";;;;;;AAMA,IAAAA,OAAA,GAAe,CACbC,gBAAgB,EAChBC,SAAS,EACTC,SAAS,EACTC,YAAY,EACZC,cAAc,EACdC,kBAAkB,CACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}