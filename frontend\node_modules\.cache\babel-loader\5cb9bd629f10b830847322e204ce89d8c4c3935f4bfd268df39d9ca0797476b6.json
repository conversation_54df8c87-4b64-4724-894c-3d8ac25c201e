{"ast": null, "code": "import CheckTag from './src/check-tag2.mjs';\nexport { checkTagEmits, checkTagProps } from './src/check-tag.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElCheckTag = withInstall(CheckTag);\nexport { ElCheckTag, ElCheckTag as default };", "map": {"version": 3, "names": ["ElCheckTag", "withInstall", "CheckTag"], "sources": ["../../../../../packages/components/check-tag/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport CheckTag from './src/check-tag.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCheckTag: SFCWithInstall<typeof CheckTag> = withInstall(CheckTag)\nexport default ElCheckTag\n\nexport * from './src/check-tag'\n"], "mappings": ";;;AAEY,MAACA,UAAU,GAAGC,WAAW,CAACC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}