{"ast": null, "code": "import { createVNode } from 'vue';\nconst Overlay = (props, {\n  slots\n}) => {\n  var _a;\n  return createVNode(\"div\", {\n    \"class\": props.class,\n    \"style\": props.style\n  }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n};\nOverlay.displayName = \"ElTableV2Overlay\";\nvar Overlay$1 = Overlay;\nexport { Overlay$1 as default };", "map": {"version": 3, "names": ["slots", "_a", "createVNode", "props", "class", "style", "default", "call", "Overlay$1", "Overlay"], "sources": ["../../../../../../../packages/components/table-v2/src/renderers/overlay.tsx"], "sourcesContent": ["import type { SimpleFunctionalComponent } from '../types'\n\nconst Overlay: SimpleFunctionalComponent = (props, { slots }) => {\n  return (\n    <div class={props.class} style={props.style}>\n      {slots.default?.()}\n    </div>\n  )\n}\n\nOverlay.displayName = 'ElTableV2Overlay'\n\nexport default Overlay\n"], "mappings": ";;EAEAA;AAAqD;EAAY,IAAAC,EAAA;EAC/D,OAAAC,WAAA;IAAA,OACc,EAAAC,KAAK,CAACC,KADpB;IAAA,SACkCD,KAAK,CAACE;GACnC,IAAAJ,EAAA,GAAMD,KAAA,CAAAM,OAFX,qBAAAL,EAAA,CAAAM,IAAA,CAAAP,KAAA;AAKD,CAND;;AAQA,IAAAQ,SAAA,GAAAC,OAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}