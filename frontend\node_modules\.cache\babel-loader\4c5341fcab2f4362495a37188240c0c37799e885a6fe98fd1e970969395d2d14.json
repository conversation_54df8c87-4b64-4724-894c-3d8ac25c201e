{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport createAggregator from './_createAggregator.js';\n\n/**\n * Creates an array of elements split into two groups, the first of which\n * contains elements `predicate` returns truthy for, the second of which\n * contains elements `predicate` returns falsey for. The predicate is\n * invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the array of grouped elements.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'age': 36, 'active': false },\n *   { 'user': 'fred',    'age': 40, 'active': true },\n *   { 'user': 'pebbles', 'age': 1,  'active': false }\n * ];\n *\n * _.partition(users, function(o) { return o.active; });\n * // => objects for [['fred'], ['barney', 'pebbles']]\n *\n * // The `_.matches` iteratee shorthand.\n * _.partition(users, { 'age': 1, 'active': false });\n * // => objects for [['pebbles'], ['barney', 'fred']]\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.partition(users, ['active', false]);\n * // => objects for [['barney', 'pebbles'], ['fred']]\n *\n * // The `_.property` iteratee shorthand.\n * _.partition(users, 'active');\n * // => objects for [['fred'], ['barney', 'pebbles']]\n */\nvar partition = createAggregator(function (result, value, key) {\n  result[key ? 0 : 1].push(value);\n}, function () {\n  return [[], []];\n});\nexport default partition;", "map": {"version": 3, "names": ["createAggregator", "partition", "result", "value", "key", "push"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/partition.js"], "sourcesContent": ["import createAggregator from './_createAggregator.js';\n\n/**\n * Creates an array of elements split into two groups, the first of which\n * contains elements `predicate` returns truthy for, the second of which\n * contains elements `predicate` returns falsey for. The predicate is\n * invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the array of grouped elements.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'age': 36, 'active': false },\n *   { 'user': 'fred',    'age': 40, 'active': true },\n *   { 'user': 'pebbles', 'age': 1,  'active': false }\n * ];\n *\n * _.partition(users, function(o) { return o.active; });\n * // => objects for [['fred'], ['barney', 'pebbles']]\n *\n * // The `_.matches` iteratee shorthand.\n * _.partition(users, { 'age': 1, 'active': false });\n * // => objects for [['pebbles'], ['barney', 'fred']]\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.partition(users, ['active', false]);\n * // => objects for [['barney', 'pebbles'], ['fred']]\n *\n * // The `_.property` iteratee shorthand.\n * _.partition(users, 'active');\n * // => objects for [['fred'], ['barney', 'pebbles']]\n */\nvar partition = createAggregator(function(result, value, key) {\n  result[key ? 0 : 1].push(value);\n}, function() { return [[], []]; });\n\nexport default partition;\n"], "mappings": ";AAAA,OAAOA,gBAAgB,MAAM,wBAAwB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAGD,gBAAgB,CAAC,UAASE,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAC5DF,MAAM,CAACE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAACC,IAAI,CAACF,KAAK,CAAC;AACjC,CAAC,EAAE,YAAW;EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC;AAAE,CAAC,CAAC;AAEnC,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}