{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, useSlots, provide, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, renderSlot, createTextVNode, toDisplayString, createCommentVNode, Fragment, renderList, createBlock } from 'vue';\nimport ElDescriptionsRow from './descriptions-row2.mjs';\nimport { descriptionsKey } from './token.mjs';\nimport { descriptionProps } from './description2.mjs';\nimport { COMPONENT_NAME } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { flattedChildren } from '../../../utils/vue/vnode.mjs';\nconst __default__ = defineComponent({\n  name: \"ElDescriptions\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: descriptionProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"descriptions\");\n    const descriptionsSize = useFormSize();\n    const slots = useSlots();\n    provide(descriptionsKey, props);\n    const descriptionKls = computed(() => [ns.b(), ns.m(descriptionsSize.value)]);\n    const filledNode = (node, span, count, isLast = false) => {\n      if (!node.props) {\n        node.props = {};\n      }\n      if (span > count) {\n        node.props.span = count;\n      }\n      if (isLast) {\n        node.props.span = span;\n      }\n      return node;\n    };\n    const getRows = () => {\n      if (!slots.default) return [];\n      const children = flattedChildren(slots.default()).filter(node => {\n        var _a;\n        return ((_a = node == null ? void 0 : node.type) == null ? void 0 : _a.name) === COMPONENT_NAME;\n      });\n      const rows = [];\n      let temp = [];\n      let count = props.column;\n      let totalSpan = 0;\n      const rowspanTemp = [];\n      children.forEach((node, index) => {\n        var _a, _b, _c;\n        const span = ((_a = node.props) == null ? void 0 : _a.span) || 1;\n        const rowspan = ((_b = node.props) == null ? void 0 : _b.rowspan) || 1;\n        const rowNo = rows.length;\n        rowspanTemp[rowNo] || (rowspanTemp[rowNo] = 0);\n        if (rowspan > 1) {\n          for (let i = 1; i < rowspan; i++) {\n            rowspanTemp[_c = rowNo + i] || (rowspanTemp[_c] = 0);\n            rowspanTemp[rowNo + i]++;\n            totalSpan++;\n          }\n        }\n        if (rowspanTemp[rowNo] > 0) {\n          count -= rowspanTemp[rowNo];\n          rowspanTemp[rowNo] = 0;\n        }\n        if (index < children.length - 1) {\n          totalSpan += span > count ? count : span;\n        }\n        if (index === children.length - 1) {\n          const lastSpan = props.column - totalSpan % props.column;\n          temp.push(filledNode(node, lastSpan, count, true));\n          rows.push(temp);\n          return;\n        }\n        if (span < count) {\n          count -= span;\n          temp.push(node);\n        } else {\n          temp.push(filledNode(node, span, count));\n          rows.push(temp);\n          count = props.column;\n          temp = [];\n        }\n      });\n      return rows;\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(descriptionKls))\n      }, [_ctx.title || _ctx.extra || _ctx.$slots.title || _ctx.$slots.extra ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"header\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"title\"))\n      }, [renderSlot(_ctx.$slots, \"title\", {}, () => [createTextVNode(toDisplayString(_ctx.title), 1)])], 2), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"extra\"))\n      }, [renderSlot(_ctx.$slots, \"extra\", {}, () => [createTextVNode(toDisplayString(_ctx.extra), 1)])], 2)], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"body\"))\n      }, [createElementVNode(\"table\", {\n        class: normalizeClass([unref(ns).e(\"table\"), unref(ns).is(\"bordered\", _ctx.border)])\n      }, [createElementVNode(\"tbody\", null, [(openBlock(true), createElementBlock(Fragment, null, renderList(getRows(), (row, _index) => {\n        return openBlock(), createBlock(ElDescriptionsRow, {\n          key: _index,\n          row\n        }, null, 8, [\"row\"]);\n      }), 128))])], 2)], 2)], 2);\n    };\n  }\n});\nvar Descriptions = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"description.vue\"]]);\nexport { Descriptions as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace", "descriptionsSize", "useFormSize", "slots", "useSlots", "provide", "<PERSON><PERSON><PERSON>", "props", "descriptionKls", "computed", "b", "m", "value", "filledNode", "node", "span", "count", "isLast", "getRows", "default", "children", "flatted<PERSON><PERSON><PERSON><PERSON>", "filter", "_a", "type", "COMPONENT_NAME", "rows", "temp", "column", "totalSpan", "rowspanTemp", "for<PERSON>ach", "index", "_b", "_c", "rowspan", "rowNo", "length", "i", "lastSpan", "push"], "sources": ["../../../../../../packages/components/descriptions/src/description.vue"], "sourcesContent": ["<template>\n  <div :class=\"descriptionKls\">\n    <div\n      v-if=\"title || extra || $slots.title || $slots.extra\"\n      :class=\"ns.e('header')\"\n    >\n      <div :class=\"ns.e('title')\">\n        <slot name=\"title\">{{ title }}</slot>\n      </div>\n      <div :class=\"ns.e('extra')\">\n        <slot name=\"extra\">{{ extra }}</slot>\n      </div>\n    </div>\n\n    <div :class=\"ns.e('body')\">\n      <table :class=\"[ns.e('table'), ns.is('bordered', border)]\">\n        <tbody>\n          <template v-for=\"(row, _index) in getRows()\" :key=\"_index\">\n            <el-descriptions-row :row=\"row\" />\n          </template>\n        </tbody>\n      </table>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, provide, useSlots } from 'vue'\nimport { flattedChildren } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormSize } from '@element-plus/components/form'\nimport ElDescriptionsRow from './descriptions-row.vue'\nimport { descriptionsKey } from './token'\nimport { descriptionProps } from './description'\nimport { COMPONENT_NAME } from './constants'\n\nimport type { IDescriptionsInject } from './descriptions.type'\nimport type { DescriptionItemVNode } from './description-item'\n\ndefineOptions({\n  name: 'ElDescriptions',\n})\n\nconst props = defineProps(descriptionProps)\n\nconst ns = useNamespace('descriptions')\n\nconst descriptionsSize = useFormSize()\n\nconst slots = useSlots()\n\nprovide(descriptionsKey, props as IDescriptionsInject)\n\nconst descriptionKls = computed(() => [ns.b(), ns.m(descriptionsSize.value)])\n\nconst filledNode = (\n  node: DescriptionItemVNode,\n  span: number,\n  count: number,\n  isLast = false\n) => {\n  if (!node.props) {\n    node.props = {}\n  }\n  if (span > count) {\n    node.props.span = count\n  }\n  if (isLast) {\n    // set the last span\n    node.props.span = span\n  }\n  return node\n}\n\nconst getRows = () => {\n  if (!slots.default) return []\n\n  const children = flattedChildren(slots.default()).filter(\n    (node): node is DescriptionItemVNode =>\n      (node as any)?.type?.name === COMPONENT_NAME\n  )\n  const rows: DescriptionItemVNode[][] = []\n  let temp: DescriptionItemVNode[] = []\n  let count = props.column\n  let totalSpan = 0 // all spans number of item\n  const rowspanTemp: number[] = [] // the number of row spans\n\n  children.forEach((node, index) => {\n    const span = node.props?.span || 1\n    const rowspan = node.props?.rowspan || 1\n    const rowNo = rows.length\n    rowspanTemp[rowNo] ||= 0\n\n    if (rowspan > 1) {\n      for (let i = 1; i < rowspan; i++) {\n        rowspanTemp[rowNo + i] ||= 0\n        rowspanTemp[rowNo + i]++\n        totalSpan++\n      }\n    }\n    if (rowspanTemp[rowNo] > 0) {\n      count -= rowspanTemp[rowNo]\n      rowspanTemp[rowNo] = 0\n    }\n    if (index < children.length - 1) {\n      totalSpan += span > count ? count : span\n    }\n\n    if (index === children.length - 1) {\n      // calculate the last item span\n      const lastSpan = props.column - (totalSpan % props.column)\n      temp.push(filledNode(node, lastSpan, count, true))\n      rows.push(temp)\n      return\n    }\n\n    if (span < count) {\n      count -= span\n      temp.push(node)\n    } else {\n      temp.push(filledNode(node, span, count))\n      rows.push(temp)\n      count = props.column\n      temp = []\n    }\n  })\n\n  return rows\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;mCAuCc;EACZA,IAAM;AACR;;;;;;IAIM,MAAAC,EAAA,GAAKC,YAAA,CAAa,cAAc;IAEtC,MAAMC,gBAAA,GAAmBC,WAAY;IAErC,MAAMC,KAAA,GAAQC,QAAS;IAEvBC,OAAA,CAAQC,eAAA,EAAiBC,KAA4B;IAErD,MAAMC,cAAiB,GAAAC,QAAA,CAAS,MAAM,CAACV,EAAG,CAAAW,CAAA,EAAK,EAAAX,EAAA,CAAGY,CAAE,CAAAV,gBAAA,CAAiBW,KAAK,CAAC,CAAC;IAE5E,MAAMC,UAAA,GAAaA,CACjBC,IAAA,EACAC,IACA,EAAAC,KAAA,EACAC,MAAA,GAAS,KACN;MACC,KAACH,IAAA,CAAKP,KAAO;QACfO,IAAA,CAAKP,KAAA,GAAQ,EAAC;MAAA;MAEhB,IAAIQ,IAAA,GAAOC,KAAO;QAChBF,IAAA,CAAKP,KAAA,CAAMQ,IAAO,GAAAC,KAAA;MAAA;MAEpB,IAAIC,MAAQ;QAEVH,IAAA,CAAKP,KAAA,CAAMQ,IAAO,GAAAA,IAAA;MAAA;MAEb,OAAAD,IAAA;IAAA,CACT;IAEA,MAAMI,OAAA,GAAUA,CAAA,KAAM;MACpB,IAAI,CAACf,KAAA,CAAMgB,OAAS,EAEpB,OAAiB;MAAiC,MAC/CC,QACgB,GAAAC,eAAe,CAAAlB,KAAA,CAAAgB,OAAA,IAAAG,MAAA,CAAAR,IAAA;QAClC,IAAAS,EAAA;QACA,SAAAA,EAAuC,GAACT,IAAA,oBAAAA,IAAA,CAAAU,IAAA,qBAAAD,EAAA,CAAAzB,IAAA,MAAA2B,cAAA;MACxC;MACA,MAAAC,IAAA,GAAkB;MAClB,IAAIC,IAAY;MAChB,IAAAX,KAAA,GAAAT,KAAA,CAAAqB,MAA+B;MAEtB,IAAAC,SAAA,IAAQ;MACT,MAAAC,WAAY;MACZV,QAAA,CAAAW,OAAA,EAAUjB,IAAK,EAAAkB,KAAA,KAAkB;QACvC,IAAAT,EAAM,EAAAU,EAAA,EAAAC,EAAQ;QACd,MAAAnB,IAAA,KAAAQ,EAAiB,GAAMT,IAAA,CAAAP,KAAA,qBAAAgB,EAAA,CAAAR,IAAA;QAEvB,MAAAoB,OAAA,GAAiB,EAAAF,EAAA,GAAAnB,IAAA,CAAAP,KAAA,qBAAA0B,EAAA,CAAAE,OAAA;QACf,MAAAC,KAAS,GAAIV,IAAG,CAAIW,MAAA;QACNP,WAAA,CAAAM,KAAA,MAAAN,WAAe,CAAAM,KAAA;QAC3B,IAAAD,OAAA;UACA,SAAAG,CAAA,MAAAA,CAAA,GAAAH,OAAA,EAAAG,CAAA;YACFR,WAAA,CAAAI,EAAA,GAAAE,KAAA,GAAAE,CAAA,MAAAR,WAAA,CAAAI,EAAA;YACFJ,WAAA,CAAAM,KAAA,GAAAE,CAAA;YACIT,SAAA;UACF;QACA;QACF,IAAAC,WAAA,CAAAM,KAAA;UACIpB,KAAA,IAAAc,WAAiB,CAAAM,KAAA;UACNN,WAAA,CAAAM,KAAA;QAAuB;QAGlC,IAAAJ,KAAA,GAAAZ,QAAmB,CAAAiB,MAAA,MAAY;UAEjCR,SAAiB,IAAAd,IAAA,GAAAC,KAAgB,GAAAA,KAAA,GAAAD,IAAA;QACjC;QACA,IAAAiB,KAAA,KAAcZ,QAAA,CAAAiB,MAAA;UACd,MAAAE,QAAA,GAAAhC,KAAA,CAAAqB,MAAA,GAAAC,SAAA,GAAAtB,KAAA,CAAAqB,MAAA;UACFD,IAAA,CAAAa,IAAA,CAAA3B,UAAA,CAAAC,IAAA,EAAAyB,QAAA,EAAAvB,KAAA;UAEAU,IAAA,CAAAc,IAAW,CAAOb,IAAA;UACP;QACT;QACF,IAAOZ,IAAA,GAAAC,KAAA;UACLA,KAAK,IAAKD,IAAA;UACVY,IAAA,CAAKa,IAAA,CAAK1B,IAAI;QACd;UACAa,IAAA,CAAAa,IAAQ,CAAA3B,UAAA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,KAAA;UACVU,IAAA,CAAAc,IAAA,CAAAb,IAAA;UACDX,KAAA,GAAAT,KAAA,CAAAqB,MAAA;UAEMD,IAAA;QAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}