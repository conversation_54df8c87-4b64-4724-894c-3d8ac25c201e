"""
国密算法工具类
提供SM2/SM3/SM4加解密功能
"""
import os
import base64
import secrets
import json
import logging
import binascii
from gmssl import sm2, sm3, sm4, func
from cryptography.hazmat.primitives import padding
from django.conf import settings

logger = logging.getLogger(__name__)

class SM3Hasher:
    """SM3哈希工具类"""

    @staticmethod
    def hash(data, encoding='utf-8'):
        """
        计算SM3哈希值

        参数:
            data: 待哈希数据(字符串或字节)
            encoding: 字符串编码方式

        返回:
            哈希值(十六进制字符串)
        """
        if isinstance(data, str):
            data = data.encode(encoding)

        # 将bytes转换为列表，因为sm3_hash函数期望接收一个列表
        data_list = list(data)
        hash_result = sm3.sm3_hash(data_list)
        return hash_result

    @staticmethod
    def hash_with_salt(data, salt=None, iterations=10000, encoding='utf-8'):
        """
        使用盐值和多轮迭代计算SM3哈希

        参数:
            data: 待哈希数据(字符串或字节)
            salt: 盐值(字符串或字节)，如果为None则随机生成
            iterations: 迭代次数
            encoding: 字符串编码方式

        返回:
            {
                'hash': 哈希值(十六进制字符串),
                'salt': 盐值(十六进制字符串),
                'iterations': 迭代次数
            }
        """
        if isinstance(data, str):
            data = data.encode(encoding)

        if salt is None:
            salt = secrets.token_bytes(16)
        elif isinstance(salt, str):
            if len(salt) % 2 == 0 and all(c in '0123456789abcdefABCDEF' for c in salt):
                # 如果是十六进制字符串，转换为字节
                salt = bytes.fromhex(salt)
            else:
                salt = salt.encode(encoding)

        # 初始哈希值为数据和盐值的组合
        value = data + salt

        # 多轮迭代哈希
        for _ in range(iterations):
            # 将bytes转换为列表，因为sm3_hash函数期望接收一个列表
            value_list = list(value)
            hash_result = sm3.sm3_hash(value_list)
            value = bytes.fromhex(hash_result)

        return {
            'hash': value.hex(),
            'salt': salt.hex(),
            'iterations': iterations
        }

    @staticmethod
    def verify(data, hash_value, salt, iterations=10000, encoding='utf-8'):
        """
        验证数据的哈希值是否匹配

        参数:
            data: 待验证数据(字符串或字节)
            hash_value: 哈希值(十六进制字符串)
            salt: 盐值(十六进制字符串)
            iterations: 迭代次数
            encoding: 字符串编码方式

        返回:
            是否匹配(布尔值)
        """
        if isinstance(salt, str):
            salt = bytes.fromhex(salt)

        result = SM3Hasher.hash_with_salt(data, salt, iterations, encoding)
        return secrets.compare_digest(result['hash'], hash_value)


class SM4Crypto:
    """SM4加解密工具类"""

    @staticmethod
    def generate_key():
        """
        生成随机SM4密钥

        返回:
            密钥(十六进制字符串)
        """
        return os.urandom(16).hex()

    @staticmethod
    def encrypt(key, data, iv=None, mode='cbc', encoding='utf-8'):
        """
        SM4加密

        参数:
            key: SM4密钥(十六进制字符串或字节)
            data: 待加密数据(字符串或字节)
            iv: 初始向量(十六进制字符串或字节)，如果为None则随机生成
            mode: 加密模式，支持'cbc'和'ecb'
            encoding: 字符串编码方式

        返回:
            加密结果(十六进制字符串，CBC模式包含IV+密文)
        """
        try:
            # 确保数据为字节
            if isinstance(data, str):
                data = data.encode(encoding)

            # 确保密钥为字节
            if isinstance(key, str):
                key = bytes.fromhex(key)

            # 创建SM4加密器
            sm4_crypt = sm4.CryptSM4()
            sm4_crypt.set_key(key, sm4.SM4_ENCRYPT)

            # 填充数据(PKCS7)
            padder = padding.PKCS7(128).padder()
            padded_data = padder.update(data) + padder.finalize()

            if mode.lower() == 'cbc':
                # 生成或转换IV
                if iv is None:
                    iv = os.urandom(16)
                elif isinstance(iv, str):
                    iv = bytes.fromhex(iv)

                # CBC模式加密
                ciphertext = sm4_crypt.crypt_cbc(iv, padded_data)
                # 返回IV+密文的十六进制字符串
                return (iv + ciphertext).hex()
            elif mode.lower() == 'ecb':
                # ECB模式加密
                ciphertext = sm4_crypt.crypt_ecb(padded_data)
                return ciphertext.hex()
            else:
                raise ValueError(f"不支持的加密模式: {mode}")
        except Exception as e:
            logger.error(f"SM4加密失败: {str(e)}")
            raise RuntimeError("SM4加密操作失败") from e

    @staticmethod
    def decrypt(key, encrypted_data, mode='cbc', encoding='utf-8'):
        """
        SM4解密

        参数:
            key: SM4密钥(十六进制字符串或字节)
            encrypted_data: 加密数据(十六进制字符串或字节，CBC模式包含IV+密文)
            mode: 解密模式，支持'cbc'和'ecb'
            encoding: 字符串编码方式

        返回:
            解密结果(字符串)
        """
        try:
            # 确保密钥为字节
            if isinstance(key, str):
                key = bytes.fromhex(key)

            # 确保加密数据为字节
            if isinstance(encrypted_data, str):
                encrypted_data = bytes.fromhex(encrypted_data)

            # 创建SM4解密器
            sm4_crypt = sm4.CryptSM4()
            sm4_crypt.set_key(key, sm4.SM4_DECRYPT)

            if mode.lower() == 'cbc':
                # 提取IV和密文
                iv = encrypted_data[:16]
                ciphertext = encrypted_data[16:]

                # CBC模式解密
                padded_data = sm4_crypt.crypt_cbc(iv, ciphertext)
            elif mode.lower() == 'ecb':
                # ECB模式解密
                padded_data = sm4_crypt.crypt_ecb(encrypted_data)
            else:
                raise ValueError(f"不支持的解密模式: {mode}")

            # 去除填充
            unpadder = padding.PKCS7(128).unpadder()
            data = unpadder.update(padded_data) + unpadder.finalize()

            # 返回解密后的字符串
            return data.decode(encoding)
        except Exception as e:
            logger.error(f"SM4解密失败: {str(e)}")
            raise RuntimeError("SM4解密操作失败") from e

    @staticmethod
    def encrypt_for_frontend(key, data, encoding='utf-8'):
        """
        适配前端sm-crypto库的SM4加密

        参数:
            key: SM4密钥(十六进制字符串或字节)
            data: 待加密数据(字符串或字节)
            encoding: 字符串编码方式

        返回:
            {
                'ciphertext': 密文(十六进制字符串),
                'iv': 初始向量(十六进制字符串)
            }
        """
        try:
            # 确保数据为字节
            if isinstance(data, str):
                data = data.encode(encoding)

            # 确保密钥为字节
            if isinstance(key, str):
                key = bytes.fromhex(key)

            # 生成IV
            iv = os.urandom(16)

            # 创建SM4加密器
            sm4_crypt = sm4.CryptSM4()
            sm4_crypt.set_key(key, sm4.SM4_ENCRYPT)

            # 填充数据(PKCS7)
            padder = padding.PKCS7(128).padder()
            padded_data = padder.update(data) + padder.finalize()

            # CBC模式加密
            ciphertext = sm4_crypt.crypt_cbc(iv, padded_data)

            # 返回分离的IV和密文
            return {
                'ciphertext': ciphertext.hex(),
                'iv': iv.hex()
            }
        except Exception as e:
            logger.error(f"SM4前端兼容加密失败: {str(e)}")
            raise RuntimeError("SM4加密操作失败") from e

    @staticmethod
    def decrypt_from_frontend(key, ciphertext, iv, encoding='utf-8'):
        """
        解密前端sm-crypto库加密的数据

        参数:
            key: SM4密钥(十六进制字符串或字节)
            ciphertext: 密文(十六进制字符串或字节)
            iv: 初始向量(十六进制字符串或字节)
            encoding: 字符串编码方式

        返回:
            解密结果(字符串)
        """
        try:
            # 确保密钥为字节
            if isinstance(key, str):
                key = bytes.fromhex(key)

            # 确保密文为字节
            if isinstance(ciphertext, str):
                ciphertext = bytes.fromhex(ciphertext)

            # 确保IV为字节
            if isinstance(iv, str):
                iv = bytes.fromhex(iv)

            # 创建SM4解密器
            sm4_crypt = sm4.CryptSM4()
            sm4_crypt.set_key(key, sm4.SM4_DECRYPT)

            # CBC模式解密
            padded_data = sm4_crypt.crypt_cbc(iv, ciphertext)

            # 去除填充
            unpadder = padding.PKCS7(128).unpadder()
            data = unpadder.update(padded_data) + unpadder.finalize()

            # 返回解密后的字符串
            return data.decode(encoding)
        except Exception as e:
            logger.error(f"SM4前端兼容解密失败: {str(e)}")
            raise RuntimeError("SM4解密操作失败") from e


class SM2Crypto:
    """SM2加解密和签名工具类"""

    @staticmethod
    def generate_key_pair():
        """
        生成SM2密钥对

        返回:
            {
                'private_key': 私钥(十六进制字符串),
                'public_key': 公钥(十六进制字符串)
            }
        """
        try:
            # 生成私钥
            private_key = func.random_hex(32)

            # 创建SM2对象并生成公钥
            sm2_crypt = sm2.CryptSM2(private_key=private_key, public_key=None)

            # 通过私钥计算公钥
            # 如果public_key属性为None，我们需要手动计算
            if sm2_crypt.public_key is None:
                # 使用gmssl库的内部方法计算公钥
                from gmssl.sm2 import default_ecc_table
                public_key_point = sm2_crypt._kg(int(private_key, 16), default_ecc_table['g'])
                public_key = '04' + format(public_key_point[0], '064x') + format(public_key_point[1], '064x')
            else:
                public_key = sm2_crypt.public_key.hex()

            return {
                'private_key': private_key,
                'public_key': public_key
            }
        except Exception as e:
            logger.error(f"生成SM2密钥对失败: {str(e)}")
            raise RuntimeError("生成SM2密钥对失败") from e

    @staticmethod
    def encrypt(public_key, data, encoding='utf-8'):
        """
        SM2加密

        参数:
            public_key: SM2公钥(十六进制字符串或字节)
            data: 待加密数据(字符串或字节)
            encoding: 字符串编码方式

        返回:
            加密结果(十六进制字符串)
        """
        try:
            if isinstance(public_key, str):
                public_key = bytes.fromhex(public_key)

            if isinstance(data, str):
                data = data.encode(encoding)

            sm2_crypt = sm2.CryptSM2(public_key=public_key, private_key=None)
            return sm2_crypt.encrypt(data).hex()
        except Exception as e:
            logger.error(f"SM2加密失败: {str(e)}")
            raise RuntimeError("SM2加密操作失败") from e

    @staticmethod
    def decrypt(private_key, encrypted_data, encoding='utf-8'):
        """
        SM2解密

        参数:
            private_key: SM2私钥(十六进制字符串)
            encrypted_data: 加密数据(十六进制字符串或字节)
            encoding: 字符串编码方式

        返回:
            解密结果(字符串)
        """
        try:
            if isinstance(encrypted_data, str):
                encrypted_data = bytes.fromhex(encrypted_data)

            sm2_crypt = sm2.CryptSM2(public_key=None, private_key=private_key)
            decrypted_data = sm2_crypt.decrypt(encrypted_data)
            return decrypted_data.decode(encoding)
        except Exception as e:
            logger.error(f"SM2解密失败: {str(e)}")
            raise RuntimeError("SM2解密操作失败") from e

    @staticmethod
    def sign(private_key, data, encoding='utf-8'):
        """
        SM2签名

        参数:
            private_key: SM2私钥(十六进制字符串)
            data: 待签名数据(字符串或字节)
            encoding: 字符串编码方式

        返回:
            签名(十六进制字符串)
        """
        try:
            if isinstance(data, str):
                data = data.encode(encoding)

            sm2_crypt = sm2.CryptSM2(public_key=None, private_key=private_key)
            random_hex_str = func.random_hex(32)
            return sm2_crypt.sign(data, random_hex_str).hex()
        except Exception as e:
            logger.error(f"SM2签名失败: {str(e)}")
            raise RuntimeError("SM2签名操作失败") from e

    @staticmethod
    def verify(public_key, data, signature, encoding='utf-8'):
        """
        SM2验签

        参数:
            public_key: SM2公钥(十六进制字符串或字节)
            data: 原始数据(字符串或字节)
            signature: 签名(十六进制字符串或字节)
            encoding: 字符串编码方式

        返回:
            验证结果(布尔值)
        """
        try:
            if isinstance(public_key, str):
                public_key = bytes.fromhex(public_key)

            if isinstance(data, str):
                data = data.encode(encoding)

            if isinstance(signature, str):
                signature = bytes.fromhex(signature)

            sm2_crypt = sm2.CryptSM2(public_key=public_key, private_key=None)
            return sm2_crypt.verify(signature, data)
        except Exception as e:
            logger.error(f"SM2验签失败: {str(e)}")
            return False

    @staticmethod
    def encrypt_for_frontend(public_key, data, encoding='utf-8'):
        """
        适配前端sm-crypto库的SM2加密

        参数:
            public_key: SM2公钥(十六进制字符串)
            data: 待加密数据(字符串或字节)
            encoding: 字符串编码方式

        返回:
            加密结果(十六进制字符串，C1C3C2格式)
        """
        try:
            if isinstance(public_key, str):
                public_key = bytes.fromhex(public_key)

            if isinstance(data, str):
                data = data.encode(encoding)

            sm2_crypt = sm2.CryptSM2(public_key=public_key, private_key=None)
            encrypted_data = sm2_crypt.encrypt(data)

            # 转换为C1C3C2格式 (前端sm-crypto默认格式)
            # 假设加密结果为C1C2C3格式，需要转换
            # C1: 前65字节
            # C2: 中间部分
            # C3: 最后32字节
            c1 = encrypted_data[:65]
            c3 = encrypted_data[-32:]
            c2 = encrypted_data[65:-32]

            # 返回C1C3C2格式
            return (c1 + c3 + c2).hex()
        except Exception as e:
            logger.error(f"SM2前端兼容加密失败: {str(e)}")
            raise RuntimeError("SM2加密操作失败") from e

    @staticmethod
    def decrypt_from_frontend(private_key, encrypted_data, encoding='utf-8'):
        """
        解密前端sm-crypto库加密的数据

        参数:
            private_key: SM2私钥(十六进制字符串)
            encrypted_data: 加密数据(十六进制字符串，C1C3C2格式)
            encoding: 字符串编码方式

        返回:
            解密结果(字符串)
        """
        try:
            if isinstance(encrypted_data, str):
                encrypted_data = bytes.fromhex(encrypted_data)

            # 转换为C1C2C3格式 (后端gmssl默认格式)
            # C1: 前65字节
            # C3: 接下来的32字节
            # C2: 剩余部分
            c1 = encrypted_data[:65]
            c3 = encrypted_data[65:97]
            c2 = encrypted_data[97:]

            # 重组为C1C2C3格式
            encrypted_data_c1c2c3 = c1 + c2 + c3

            sm2_crypt = sm2.CryptSM2(public_key=None, private_key=private_key)
            decrypted_data = sm2_crypt.decrypt(encrypted_data_c1c2c3)
            return decrypted_data.decode(encoding)
        except Exception as e:
            logger.error(f"SM2前端兼容解密失败: {str(e)}")
            raise RuntimeError("SM2解密操作失败") from e

    @staticmethod
    def get_public_key_from_private(private_key):
        """
        从私钥获取公钥

        参数:
            private_key: SM2私钥(十六进制字符串)

        返回:
            公钥(十六进制字符串)
        """
        try:
            sm2_crypt = sm2.CryptSM2(private_key=private_key, public_key=None)
            return sm2_crypt.public_key.hex()
        except Exception as e:
            logger.error(f"从私钥获取公钥失败: {str(e)}")
            raise RuntimeError("获取公钥失败") from e
