{"ast": null, "code": "import normalizeWheel from 'normalize-wheel-es';\nconst mousewheel = function (element, callback) {\n  if (element && element.addEventListener) {\n    const fn = function (event) {\n      const normalized = normalizeWheel(event);\n      callback && Reflect.apply(callback, this, [event, normalized]);\n    };\n    element.addEventListener(\"wheel\", fn, {\n      passive: true\n    });\n  }\n};\nconst Mousewheel = {\n  beforeMount(el, binding) {\n    mousewheel(el, binding.value);\n  }\n};\nexport { Mousewheel as default };", "map": {"version": 3, "names": ["mousewheel", "element", "callback", "addEventListener", "fn", "event", "normalized", "normalizeWheel", "Reflect", "apply", "passive", "Mousewheel", "beforeMount", "el", "binding", "value"], "sources": ["../../../../../packages/directives/mousewheel/index.ts"], "sourcesContent": ["import normalizeWheel from 'normalize-wheel-es'\n\nimport type { DirectiveBinding, ObjectDirective } from 'vue'\nimport type { NormalizedWheelEvent } from 'normalize-wheel-es'\n\nconst mousewheel = function (\n  element: HTMLElement,\n  callback: (e: WheelEvent, normalized: NormalizedWheelEvent) => void\n) {\n  if (element && element.addEventListener) {\n    const fn = function (this: HTMLElement, event: WheelEvent) {\n      const normalized = normalizeWheel(event)\n      callback && Reflect.apply(callback, this, [event, normalized])\n    }\n    element.addEventListener('wheel', fn, { passive: true })\n  }\n}\n\nconst Mousewheel: ObjectDirective = {\n  beforeMount(el: HTMLElement, binding: DirectiveBinding) {\n    mousewheel(el, binding.value)\n  },\n}\n\nexport default Mousewheel\n"], "mappings": ";AACA,MAAMA,UAAU,GAAG,SAAAA,CAASC,OAAO,EAAEC,QAAQ,EAAE;EAC7C,IAAID,OAAO,IAAIA,OAAO,CAACE,gBAAgB,EAAE;IACvC,MAAMC,EAAE,GAAG,SAAAA,CAASC,KAAK,EAAE;MACzB,MAAMC,UAAU,GAAGC,cAAc,CAACF,KAAK,CAAC;MACxCH,QAAQ,IAAIM,OAAO,CAACC,KAAK,CAACP,QAAQ,EAAE,IAAI,EAAE,CAACG,KAAK,EAAEC,UAAU,CAAC,CAAC;IACpE,CAAK;IACDL,OAAO,CAACE,gBAAgB,CAAC,OAAO,EAAEC,EAAE,EAAE;MAAEM,OAAO,EAAE;IAAI,CAAE,CAAC;EAC5D;AACA,CAAC;AACI,MAACC,UAAU,GAAG;EACjBC,WAAWA,CAACC,EAAE,EAAEC,OAAO,EAAE;IACvBd,UAAU,CAACa,EAAE,EAAEC,OAAO,CAACC,KAAK,CAAC;EACjC;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}