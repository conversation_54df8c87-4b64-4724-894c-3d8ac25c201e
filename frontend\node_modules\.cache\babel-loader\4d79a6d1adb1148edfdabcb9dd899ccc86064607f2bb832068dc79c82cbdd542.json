{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { isArray } from '@vue/shared';\nconst uploadDraggerProps = buildProps({\n  disabled: {\n    type: Boolean,\n    default: false\n  }\n});\nconst uploadDraggerEmits = {\n  file: file => isArray(file)\n};\nexport { uploadDraggerEmits, uploadDraggerProps };", "map": {"version": 3, "names": ["uploadDraggerProps", "buildProps", "disabled", "type", "Boolean", "default", "uploadDraggerEmits", "file", "isArray"], "sources": ["../../../../../../packages/components/upload/src/upload-dragger.ts"], "sourcesContent": ["import { buildProps, isArray } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type UploadDragger from './upload-dragger.vue'\n\nexport const uploadDraggerProps = buildProps({\n  disabled: {\n    type: Boolean,\n    default: false,\n  },\n} as const)\nexport type UploadDraggerProps = ExtractPropTypes<typeof uploadDraggerProps>\n\nexport const uploadDraggerEmits = {\n  file: (file: File[]) => isArray(file),\n}\nexport type UploadDraggerEmits = typeof uploadDraggerEmits\n\nexport type UploadDraggerInstance = InstanceType<typeof UploadDragger> & unknown\n"], "mappings": ";;AACY,MAACA,kBAAkB,GAAGC,UAAU,CAAC;EAC3CC,QAAQ,EAAE;IACRC,IAAI,EAAEC,OAAO;IACbC,OAAO,EAAE;EACb;AACA,CAAC;AACW,MAACC,kBAAkB,GAAG;EAChCC,IAAI,EAAGA,IAAI,IAAKC,OAAO,CAACD,IAAI;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}