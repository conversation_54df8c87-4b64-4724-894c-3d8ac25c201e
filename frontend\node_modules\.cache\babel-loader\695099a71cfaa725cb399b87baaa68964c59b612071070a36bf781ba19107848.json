{"ast": null, "code": "import { watch } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nconst usePreventGlobal = (indicator, evt, cb) => {\n  const prevent = e => {\n    if (cb(e)) e.stopImmediatePropagation();\n  };\n  let stop = void 0;\n  watch(() => indicator.value, val => {\n    if (val) {\n      stop = useEventListener(document, evt, prevent, true);\n    } else {\n      stop == null ? void 0 : stop();\n    }\n  }, {\n    immediate: true\n  });\n};\nexport { usePreventGlobal };", "map": {"version": 3, "names": ["usePreventGlobal", "indicator", "evt", "cb", "prevent", "e", "stopImmediatePropagation", "stop", "watch", "value", "val", "useEventListener", "document", "immediate"], "sources": ["../../../../../packages/hooks/use-prevent-global/index.ts"], "sourcesContent": ["import { watch } from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport type { Ref } from 'vue'\n\nexport const usePreventGlobal = <E extends keyof DocumentEventMap>(\n  indicator: Ref<boolean>,\n  evt: E,\n  cb: (e: DocumentEventMap[E]) => boolean\n) => {\n  const prevent = (e: DocumentEventMap[E]) => {\n    if (cb(e)) e.stopImmediatePropagation()\n  }\n  let stop: (() => void) | undefined = undefined\n  watch(\n    () => indicator.value,\n    (val) => {\n      if (val) {\n        stop = useEventListener(document, evt, prevent, true)\n      } else {\n        stop?.()\n      }\n    },\n    { immediate: true }\n  )\n}\n"], "mappings": ";;AAEY,MAACA,gBAAgB,GAAGA,CAACC,SAAS,EAAEC,GAAG,EAAEC,EAAE,KAAK;EACtD,MAAMC,OAAO,GAAIC,CAAC,IAAK;IACrB,IAAIF,EAAE,CAACE,CAAC,CAAC,EACPA,CAAC,CAACC,wBAAwB,EAAE;EAClC,CAAG;EACD,IAAIC,IAAI,GAAG,KAAK,CAAC;EACjBC,KAAK,CAAC,MAAMP,SAAS,CAACQ,KAAK,EAAGC,GAAG,IAAK;IACpC,IAAIA,GAAG,EAAE;MACPH,IAAI,GAAGI,gBAAgB,CAACC,QAAQ,EAAEV,GAAG,EAAEE,OAAO,EAAE,IAAI,CAAC;IAC3D,CAAK,MAAM;MACLG,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,EAAE;IACpC;EACA,CAAG,EAAE;IAAEM,SAAS,EAAE;EAAI,CAAE,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}