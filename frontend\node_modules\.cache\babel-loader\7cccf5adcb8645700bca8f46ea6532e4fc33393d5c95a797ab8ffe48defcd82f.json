{"ast": null, "code": "import http from \"./http\";\nexport default {\n  // 用户登录\n  login(studentId, password) {\n    return http.post(\"/auth/login\", {\n      studentId,\n      password\n    });\n  },\n  // 获取SM2挑战值\n  getSM2Challenge(studentId) {\n    return http.get(\"/auth/sm2-challenge\", {\n      params: {\n        studentId\n      }\n    });\n  },\n  // SM2证书登录\n  sm2Login(studentId, signature) {\n    return http.post(\"/auth/sm2-login\", {\n      studentId,\n      signature\n    });\n  },\n  // 用户注册\n  register(userData) {\n    return http.post(\"/auth/register\", userData);\n  },\n  // 退出登录\n  logout() {\n    return http.post(\"/auth/logout\");\n  },\n  // 获取用户信息\n  getUserInfo() {\n    return http.get(\"/user/profile\");\n  },\n  // 更新用户信息\n  updateProfile(userData) {\n    return http.put(\"/user/profile\", userData);\n  },\n  // 修改密码\n  changePassword(oldPassword, newPassword) {\n    return http.put(\"/user/password\", {\n      oldPassword,\n      newPassword\n    });\n  },\n  // 更新公钥\n  updatePublicKey(publicKey) {\n    return http.put(\"/user/public-key\", {\n      publicKey\n    });\n  },\n  // 移除公钥\n  removePublicKey() {\n    return http.delete(\"/user/public-key\");\n  },\n  // 获取信誉分记录\n  getCreditRecords(params) {\n    return http.get(\"/user/credit-records\", {\n      params\n    });\n  }\n};", "map": {"version": 3, "names": ["http", "login", "studentId", "password", "post", "getSM2Challenge", "get", "params", "sm2Login", "signature", "register", "userData", "logout", "getUserInfo", "updateProfile", "put", "changePassword", "oldPassword", "newPassword", "updatePublicKey", "public<PERSON>ey", "removePublic<PERSON>ey", "delete", "getCreditRecords"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/src/api/user.js"], "sourcesContent": ["import http from \"./http\";\n\nexport default {\n  // 用户登录\n  login(studentId, password) {\n    return http.post(\"/auth/login\", { studentId, password });\n  },\n\n  // 获取SM2挑战值\n  getSM2Challenge(studentId) {\n    return http.get(\"/auth/sm2-challenge\", { params: { studentId } });\n  },\n\n  // SM2证书登录\n  sm2Login(studentId, signature) {\n    return http.post(\"/auth/sm2-login\", { studentId, signature });\n  },\n\n  // 用户注册\n  register(userData) {\n    return http.post(\"/auth/register\", userData);\n  },\n\n  // 退出登录\n  logout() {\n    return http.post(\"/auth/logout\");\n  },\n\n  // 获取用户信息\n  getUserInfo() {\n    return http.get(\"/user/profile\");\n  },\n\n  // 更新用户信息\n  updateProfile(userData) {\n    return http.put(\"/user/profile\", userData);\n  },\n\n  // 修改密码\n  changePassword(oldPassword, newPassword) {\n    return http.put(\"/user/password\", { oldPassword, newPassword });\n  },\n\n  // 更新公钥\n  updatePublicKey(publicKey) {\n    return http.put(\"/user/public-key\", { publicKey });\n  },\n\n  // 移除公钥\n  removePublicKey() {\n    return http.delete(\"/user/public-key\");\n  },\n\n  // 获取信誉分记录\n  getCreditRecords(params) {\n    return http.get(\"/user/credit-records\", { params });\n  }\n};\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AAEzB,eAAe;EACb;EACAC,KAAKA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACzB,OAAOH,IAAI,CAACI,IAAI,CAAC,aAAa,EAAE;MAAEF,SAAS;MAAEC;IAAS,CAAC,CAAC;EAC1D,CAAC;EAED;EACAE,eAAeA,CAACH,SAAS,EAAE;IACzB,OAAOF,IAAI,CAACM,GAAG,CAAC,qBAAqB,EAAE;MAAEC,MAAM,EAAE;QAAEL;MAAU;IAAE,CAAC,CAAC;EACnE,CAAC;EAED;EACAM,QAAQA,CAACN,SAAS,EAAEO,SAAS,EAAE;IAC7B,OAAOT,IAAI,CAACI,IAAI,CAAC,iBAAiB,EAAE;MAAEF,SAAS;MAAEO;IAAU,CAAC,CAAC;EAC/D,CAAC;EAED;EACAC,QAAQA,CAACC,QAAQ,EAAE;IACjB,OAAOX,IAAI,CAACI,IAAI,CAAC,gBAAgB,EAAEO,QAAQ,CAAC;EAC9C,CAAC;EAED;EACAC,MAAMA,CAAA,EAAG;IACP,OAAOZ,IAAI,CAACI,IAAI,CAAC,cAAc,CAAC;EAClC,CAAC;EAED;EACAS,WAAWA,CAAA,EAAG;IACZ,OAAOb,IAAI,CAACM,GAAG,CAAC,eAAe,CAAC;EAClC,CAAC;EAED;EACAQ,aAAaA,CAACH,QAAQ,EAAE;IACtB,OAAOX,IAAI,CAACe,GAAG,CAAC,eAAe,EAAEJ,QAAQ,CAAC;EAC5C,CAAC;EAED;EACAK,cAAcA,CAACC,WAAW,EAAEC,WAAW,EAAE;IACvC,OAAOlB,IAAI,CAACe,GAAG,CAAC,gBAAgB,EAAE;MAAEE,WAAW;MAAEC;IAAY,CAAC,CAAC;EACjE,CAAC;EAED;EACAC,eAAeA,CAACC,SAAS,EAAE;IACzB,OAAOpB,IAAI,CAACe,GAAG,CAAC,kBAAkB,EAAE;MAAEK;IAAU,CAAC,CAAC;EACpD,CAAC;EAED;EACAC,eAAeA,CAAA,EAAG;IAChB,OAAOrB,IAAI,CAACsB,MAAM,CAAC,kBAAkB,CAAC;EACxC,CAAC;EAED;EACAC,gBAAgBA,CAAChB,MAAM,EAAE;IACvB,OAAOP,IAAI,CAACM,GAAG,CAAC,sBAAsB,EAAE;MAAEC;IAAO,CAAC,CAAC;EACrD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}