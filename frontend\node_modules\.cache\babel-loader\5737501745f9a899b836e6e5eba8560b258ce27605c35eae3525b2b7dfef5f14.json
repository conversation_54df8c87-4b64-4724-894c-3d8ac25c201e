{"ast": null, "code": "import Backtop from './src/backtop2.mjs';\nexport { backtopEmits, backtopProps } from './src/backtop.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElBacktop = withInstall(Backtop);\nexport { ElBacktop, ElBacktop as default };", "map": {"version": 3, "names": ["ElBacktop", "withInstall", "Backtop"], "sources": ["../../../../../packages/components/backtop/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Backtop from './src/backtop.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElBacktop: SFCWithInstall<typeof Backtop> = withInstall(Backtop)\nexport default ElBacktop\n\nexport * from './src/backtop'\nexport type { BacktopInstance } from './src/instance'\n"], "mappings": ";;;AAEY,MAACA,SAAS,GAAGC,WAAW,CAACC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}