{"ast": null, "code": "import { computed } from 'vue';\nimport useMenuColor from './use-menu-color.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst useMenuCssVar = (props, level) => {\n  const ns = useNamespace(\"menu\");\n  return computed(() => ns.cssVarBlock({\n    \"text-color\": props.textColor || \"\",\n    \"hover-text-color\": props.textColor || \"\",\n    \"bg-color\": props.backgroundColor || \"\",\n    \"hover-bg-color\": useMenuColor(props).value || \"\",\n    \"active-color\": props.activeTextColor || \"\",\n    level: `${level}`\n  }));\n};\nexport { useMenuCssVar };", "map": {"version": 3, "names": ["useMenuCssVar", "props", "level", "ns", "useNamespace", "computed", "cssVarBlock", "textColor", "backgroundColor", "useMenuColor", "value", "activeTextColor"], "sources": ["../../../../../../packages/components/menu/src/use-menu-css-var.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport useMenuColor from './use-menu-color'\n\nimport type { MenuProps } from './menu'\n\nexport const useMenuCssVar = (props: MenuProps, level: number) => {\n  const ns = useNamespace('menu')\n  return computed(() =>\n    ns.cssVarBlock({\n      'text-color': props.textColor || '',\n      'hover-text-color': props.textColor || '',\n      'bg-color': props.backgroundColor || '',\n      'hover-bg-color': useMenuColor(props).value || '',\n      'active-color': props.activeTextColor || '',\n      level: `${level}`,\n    })\n  )\n}\n"], "mappings": ";;;AAGY,MAACA,aAAa,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EAC7C,MAAMC,EAAE,GAAGC,YAAY,CAAC,MAAM,CAAC;EAC/B,OAAOC,QAAQ,CAAC,MAAMF,EAAE,CAACG,WAAW,CAAC;IACnC,YAAY,EAAEL,KAAK,CAACM,SAAS,IAAI,EAAE;IACnC,kBAAkB,EAAEN,KAAK,CAACM,SAAS,IAAI,EAAE;IACzC,UAAU,EAAEN,KAAK,CAACO,eAAe,IAAI,EAAE;IACvC,gBAAgB,EAAEC,YAAY,CAACR,KAAK,CAAC,CAACS,KAAK,IAAI,EAAE;IACjD,cAAc,EAAET,KAAK,CAACU,eAAe,IAAI,EAAE;IAC3CT,KAAK,EAAE,GAAGA,KAAK;EACnB,CAAG,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}