{"ast": null, "code": "import { tourContentProps } from './content2.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { teleportProps } from '../../teleport/src/teleport2.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isBoolean, isNumber } from '../../../utils/types.mjs';\nconst tourProps = buildProps({\n  modelValue: Boolean,\n  current: {\n    type: Number,\n    default: 0\n  },\n  showArrow: {\n    type: Boolean,\n    default: true\n  },\n  showClose: {\n    type: Boolean,\n    default: true\n  },\n  closeIcon: {\n    type: iconPropType\n  },\n  placement: tourContentProps.placement,\n  contentStyle: {\n    type: definePropType([Object])\n  },\n  mask: {\n    type: definePropType([<PERSON>olean, Object]),\n    default: true\n  },\n  gap: {\n    type: definePropType(Object),\n    default: () => ({\n      offset: 6,\n      radius: 2\n    })\n  },\n  zIndex: {\n    type: Number\n  },\n  scrollIntoViewOptions: {\n    type: definePropType([Boolean, Object]),\n    default: () => ({\n      block: \"center\"\n    })\n  },\n  type: {\n    type: definePropType(String)\n  },\n  appendTo: {\n    type: teleportProps.to.type,\n    default: \"body\"\n  },\n  closeOnPressEscape: {\n    type: Boolean,\n    default: true\n  },\n  targetAreaClickable: {\n    type: Boolean,\n    default: true\n  }\n});\nconst tourEmits = {\n  [UPDATE_MODEL_EVENT]: value => isBoolean(value),\n  [\"update:current\"]: current => isNumber(current),\n  close: current => isNumber(current),\n  finish: () => true,\n  change: current => isNumber(current)\n};\nexport { tourEmits, tourProps };", "map": {"version": 3, "names": ["tourProps", "buildProps", "modelValue", "Boolean", "current", "type", "Number", "default", "showArrow", "showClose", "closeIcon", "iconPropType", "placement", "tourContentProps", "contentStyle", "definePropType", "Object", "mask", "gap", "offset", "radius", "zIndex", "scrollIntoViewOptions", "block", "String", "appendTo", "teleportProps", "to", "closeOnPressEscape", "targetAreaClickable", "tourEmits", "UPDATE_MODEL_EVENT", "value", "isBoolean", "isNumber", "close", "finish", "change"], "sources": ["../../../../../../packages/components/tour/src/tour.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  iconPropType,\n  isBoolean,\n  isNumber,\n} from '@element-plus/utils'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { teleportProps } from '@element-plus/components/teleport'\nimport { tourContentProps } from './content'\nimport type { CSSProperties, ExtractPropTypes } from 'vue'\nimport type Tour from './tour.vue'\nimport type { TourGap, TourMask } from './types'\n\nexport const tourProps = buildProps({\n  /**\n   * @description open tour\n   */\n  modelValue: Boolean,\n  /**\n   * @description what is the current step\n   */\n  current: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description whether to show the arrow\n   */\n  showArrow: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether to show a close button\n   */\n  showClose: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description custom close icon\n   */\n  closeIcon: {\n    type: iconPropType,\n  },\n  /**\n   * @description position of the guide card relative to the target element\n   */\n  placement: tourContentProps.placement,\n  /**\n   * @description custom style for content\n   */\n  contentStyle: {\n    type: definePropType<CSSProperties>([Object]),\n  },\n  /**\n   * @description whether to enable masking, change mask style and fill color by pass custom props\n   */\n  mask: {\n    type: definePropType<TourMask>([Boolean, Object]),\n    default: true,\n  },\n  /**\n   * @description transparent gap between mask and target\n   */\n  gap: {\n    type: definePropType<TourGap>(Object),\n    default: () => ({\n      offset: 6,\n      radius: 2,\n    }),\n  },\n  /**\n   * @description tour's zIndex\n   */\n  zIndex: {\n    type: Number,\n  },\n  /**\n   * @description support pass custom scrollIntoView options\n   */\n  scrollIntoViewOptions: {\n    type: definePropType<boolean | ScrollIntoViewOptions>([Boolean, Object]),\n    default: () => ({\n      block: 'center',\n    }),\n  },\n  /**\n   * @description type, affects the background color and text color\n   */\n  type: {\n    type: definePropType<'default' | 'primary'>(String),\n  },\n  /**\n   * @description which element the TourContent appends to\n   */\n  appendTo: {\n    type: teleportProps.to.type,\n    default: 'body',\n  },\n  /**\n   * @description whether the Tour can be closed by pressing ESC\n   */\n  closeOnPressEscape: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether the target element can be clickable, when using mask\n   */\n  targetAreaClickable: {\n    type: Boolean,\n    default: true,\n  },\n})\n\nexport type TourProps = ExtractPropTypes<typeof tourProps>\nexport type TourInstance = InstanceType<typeof Tour> & unknown\n\nexport const tourEmits = {\n  [UPDATE_MODEL_EVENT]: (value: boolean) => isBoolean(value),\n  ['update:current']: (current: number) => isNumber(current),\n  close: (current: number) => isNumber(current),\n  finish: () => true,\n  change: (current: number) => isNumber(current),\n}\nexport type TourEmits = typeof tourEmits\n"], "mappings": ";;;;;;AAUY,MAACA,SAAS,GAAGC,UAAU,CAAC;EAClCC,UAAU,EAAEC,OAAO;EACnBC,OAAO,EAAE;IACPC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,SAAS,EAAE;IACTH,IAAI,EAAEF,OAAO;IACbI,OAAO,EAAE;EACb,CAAG;EACDE,SAAS,EAAE;IACTJ,IAAI,EAAEF,OAAO;IACbI,OAAO,EAAE;EACb,CAAG;EACDG,SAAS,EAAE;IACTL,IAAI,EAAEM;EACV,CAAG;EACDC,SAAS,EAAEC,gBAAgB,CAACD,SAAS;EACrCE,YAAY,EAAE;IACZT,IAAI,EAAEU,cAAc,CAAC,CAACC,MAAM,CAAC;EACjC,CAAG;EACDC,IAAI,EAAE;IACJZ,IAAI,EAAEU,cAAc,CAAC,CAACZ,OAAO,EAAEa,MAAM,CAAC,CAAC;IACvCT,OAAO,EAAE;EACb,CAAG;EACDW,GAAG,EAAE;IACHb,IAAI,EAAEU,cAAc,CAACC,MAAM,CAAC;IAC5BT,OAAO,EAAEA,CAAA,MAAO;MACdY,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE;IACd,CAAK;EACL,CAAG;EACDC,MAAM,EAAE;IACNhB,IAAI,EAAEC;EACV,CAAG;EACDgB,qBAAqB,EAAE;IACrBjB,IAAI,EAAEU,cAAc,CAAC,CAACZ,OAAO,EAAEa,MAAM,CAAC,CAAC;IACvCT,OAAO,EAAEA,CAAA,MAAO;MACdgB,KAAK,EAAE;IACb,CAAK;EACL,CAAG;EACDlB,IAAI,EAAE;IACJA,IAAI,EAAEU,cAAc,CAACS,MAAM;EAC/B,CAAG;EACDC,QAAQ,EAAE;IACRpB,IAAI,EAAEqB,aAAa,CAACC,EAAE,CAACtB,IAAI;IAC3BE,OAAO,EAAE;EACb,CAAG;EACDqB,kBAAkB,EAAE;IAClBvB,IAAI,EAAEF,OAAO;IACbI,OAAO,EAAE;EACb,CAAG;EACDsB,mBAAmB,EAAE;IACnBxB,IAAI,EAAEF,OAAO;IACbI,OAAO,EAAE;EACb;AACA,CAAC;AACW,MAACuB,SAAS,GAAG;EACvB,CAACC,kBAAkB,GAAIC,KAAK,IAAKC,SAAS,CAACD,KAAK,CAAC;EACjD,CAAC,gBAAgB,GAAI5B,OAAO,IAAK8B,QAAQ,CAAC9B,OAAO,CAAC;EAClD+B,KAAK,EAAG/B,OAAO,IAAK8B,QAAQ,CAAC9B,OAAO,CAAC;EACrCgC,MAAM,EAAEA,CAAA,KAAM,IAAI;EAClBC,MAAM,EAAGjC,OAAO,IAAK8B,QAAQ,CAAC9B,OAAO;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}