{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"app-layout\"\n};\nconst _hoisted_2 = {\n  class: \"main-container\"\n};\nconst _hoisted_3 = {\n  class: \"content-container\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_app_header = _resolveComponent(\"app-header\");\n  const _component_app_sidebar = _resolveComponent(\"app-sidebar\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  const _component_app_footer = _resolveComponent(\"app-footer\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_app_header), _createElementVNode(\"div\", _hoisted_2, [$setup.isLoggedIn ? (_openBlock(), _createBlock(_component_app_sidebar, {\n    key: 0\n  })) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_router_view)])]), _createVNode(_component_app_footer)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_app_header", "_createElementVNode", "_hoisted_2", "$setup", "isLoggedIn", "_createBlock", "_component_app_sidebar", "key", "_createCommentVNode", "_hoisted_3", "_component_router_view", "_component_app_footer"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Layout.vue"], "sourcesContent": ["<template>\n  <div class=\"app-layout\">\n    <app-header />\n    <div class=\"main-container\">\n      <app-sidebar v-if=\"isLoggedIn\" />\n      <div class=\"content-container\">\n        <router-view />\n      </div>\n    </div>\n    <app-footer />\n  </div>\n</template>\n\n<script>\nimport { computed } from \"vue\";\nimport { useStore } from \"vuex\";\nimport AppHeader from \"./Header.vue\";\nimport AppSidebar from \"./Sidebar.vue\";\nimport AppFooter from \"./Footer.vue\";\n\nexport default {\n  name: \"AppLayout\",\n  components: {\n    AppHeader,\n    AppSidebar,\n    AppFooter\n  },\n  setup() {\n    const store = useStore();\n    const isLoggedIn = computed(() => store.getters[\"user/isLoggedIn\"]);\n\n    return {\n      isLoggedIn\n    };\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-layout {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n}\n\n.main-container {\n  display: flex;\n  flex: 1;\n}\n\n.content-container {\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAmB;;;;;;uBAJlCC,mBAAA,CASM,OATNC,UASM,GARJC,YAAA,CAAcC,qBAAA,GACdC,mBAAA,CAKM,OALNC,UAKM,GAJeC,MAAA,CAAAC,UAAU,I,cAA7BC,YAAA,CAAiCC,sBAAA;IAJvCC,GAAA;EAAA,MAAAC,mBAAA,gBAKMP,mBAAA,CAEM,OAFNQ,UAEM,GADJV,YAAA,CAAeW,sBAAA,E,KAGnBX,YAAA,CAAcY,qBAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}