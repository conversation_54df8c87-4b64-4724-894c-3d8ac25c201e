{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { rangeArr } from '../../time-picker/src/utils.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isObject } from '@vue/shared';\nconst getPrevMonthLastDays = (date, count) => {\n  const lastDay = date.subtract(1, \"month\").endOf(\"month\").date();\n  return rangeArr(count).map((_, index) => lastDay - (count - index - 1));\n};\nconst getMonthDays = date => {\n  const days = date.daysInMonth();\n  return rangeArr(days).map((_, index) => index + 1);\n};\nconst toNestedArr = days => rangeArr(days.length / 7).map(index => {\n  const start = index * 7;\n  return days.slice(start, start + 7);\n});\nconst dateTableProps = buildProps({\n  selectedDay: {\n    type: definePropType(Object)\n  },\n  range: {\n    type: definePropType(Array)\n  },\n  date: {\n    type: definePropType(Object),\n    required: true\n  },\n  hideHeader: {\n    type: Boolean\n  }\n});\nconst dateTableEmits = {\n  pick: value => isObject(value)\n};\nexport { dateTableEmits, dateTableProps, getMonthDays, getPrevMonthLastDays, toNestedArr };", "map": {"version": 3, "names": ["getPrevMonthLastDays", "date", "count", "lastDay", "subtract", "endOf", "rangeArr", "map", "_", "index", "getMonthDays", "days", "daysInMonth", "toNestedArr", "length", "start", "slice", "dateTableProps", "buildProps", "selected<PERSON>ay", "type", "definePropType", "Object", "range", "Array", "required", "<PERSON><PERSON>ead<PERSON>", "Boolean", "dateTableEmits", "pick", "value", "isObject"], "sources": ["../../../../../../packages/components/calendar/src/date-table.ts"], "sourcesContent": ["import { buildProps, definePropType, isObject } from '@element-plus/utils'\nimport { rangeArr } from '@element-plus/components/time-picker'\nimport type { ExtractPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport type CalendarDateCellType = 'next' | 'prev' | 'current'\nexport type CalendarDateCell = {\n  text: number\n  type: CalendarDateCellType\n}\n\nexport const getPrevMonthLastDays = (date: Dayjs, count: number) => {\n  const lastDay = date.subtract(1, 'month').endOf('month').date()\n  return rangeArr(count).map((_, index) => lastDay - (count - index - 1))\n}\n\nexport const getMonthDays = (date: Dayjs) => {\n  const days = date.daysInMonth()\n  return rangeArr(days).map((_, index) => index + 1)\n}\n\nexport const toNestedArr = (days: CalendarDateCell[]) =>\n  rangeArr(days.length / 7).map((index) => {\n    const start = index * 7\n    return days.slice(start, start + 7)\n  })\n\nexport const dateTableProps = buildProps({\n  selectedDay: {\n    type: definePropType<Dayjs>(Object),\n  },\n  range: {\n    type: definePropType<[Dayjs, Dayjs]>(Array),\n  },\n  date: {\n    type: definePropType<Dayjs>(Object),\n    required: true,\n  },\n  hideHeader: {\n    type: Boolean,\n  },\n} as const)\nexport type DateTableProps = ExtractPropTypes<typeof dateTableProps>\n\nexport const dateTableEmits = {\n  pick: (value: Dayjs) => isObject(value),\n}\nexport type DateTableEmits = typeof dateTableEmits\n"], "mappings": ";;;;;AAEY,MAACA,oBAAoB,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;EACnD,MAAMC,OAAO,GAAGF,IAAI,CAACG,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAACJ,IAAI,EAAE;EAC/D,OAAOK,QAAQ,CAACJ,KAAK,CAAC,CAACK,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAKN,OAAO,IAAID,KAAK,GAAGO,KAAK,GAAG,CAAC,CAAC,CAAC;AACzE;AACY,MAACC,YAAY,GAAIT,IAAI,IAAK;EACpC,MAAMU,IAAI,GAAGV,IAAI,CAACW,WAAW,EAAE;EAC/B,OAAON,QAAQ,CAACK,IAAI,CAAC,CAACJ,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC;AACpD;AACY,MAACI,WAAW,GAAIF,IAAI,IAAKL,QAAQ,CAACK,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,CAACP,GAAG,CAAEE,KAAK,IAAK;EAC5E,MAAMM,KAAK,GAAGN,KAAK,GAAG,CAAC;EACvB,OAAOE,IAAI,CAACK,KAAK,CAACD,KAAK,EAAEA,KAAK,GAAG,CAAC,CAAC;AACrC,CAAC;AACW,MAACE,cAAc,GAAGC,UAAU,CAAC;EACvCC,WAAW,EAAE;IACXC,IAAI,EAAEC,cAAc,CAACC,MAAM;EAC/B,CAAG;EACDC,KAAK,EAAE;IACLH,IAAI,EAAEC,cAAc,CAACG,KAAK;EAC9B,CAAG;EACDvB,IAAI,EAAE;IACJmB,IAAI,EAAEC,cAAc,CAACC,MAAM,CAAC;IAC5BG,QAAQ,EAAE;EACd,CAAG;EACDC,UAAU,EAAE;IACVN,IAAI,EAAEO;EACV;AACA,CAAC;AACW,MAACC,cAAc,GAAG;EAC5BC,IAAI,EAAGC,KAAK,IAAKC,QAAQ,CAACD,KAAK;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}