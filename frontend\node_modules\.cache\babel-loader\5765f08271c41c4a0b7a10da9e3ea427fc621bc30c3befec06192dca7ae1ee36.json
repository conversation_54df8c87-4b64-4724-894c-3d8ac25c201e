{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, renderSlot, toDisplayString, createVNode, withCtx, createTextVNode, createCommentVNode, createSlots, normalizeProps, guardReactiveProps, Fragment, renderList, createBlock } from 'vue';\nimport { ElButtonGroup, ElButton } from '../../button/index.mjs';\nimport DateTable from './date-table2.mjs';\nimport { useCalendar } from './use-calendar.mjs';\nimport { calendarProps, calendarEmits } from './calendar.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nconst COMPONENT_NAME = \"ElCalendar\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: calendarProps,\n  emits: calendarEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"calendar\");\n    const {\n      calculateValidatedDateRange,\n      date,\n      pickDay,\n      realSelectedDay,\n      selectDate,\n      validatedRange\n    } = useCalendar(props, emit, COMPONENT_NAME);\n    const {\n      t\n    } = useLocale();\n    const i18nDate = computed(() => {\n      const pickedMonth = `el.datepicker.month${date.value.format(\"M\")}`;\n      return `${date.value.year()} ${t(\"el.datepicker.year\")} ${t(pickedMonth)}`;\n    });\n    expose({\n      selectedDay: realSelectedDay,\n      pickDay,\n      selectDate,\n      calculateValidatedDateRange\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(ns).b())\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"header\"))\n      }, [renderSlot(_ctx.$slots, \"header\", {\n        date: unref(i18nDate)\n      }, () => [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"title\"))\n      }, toDisplayString(unref(i18nDate)), 3), unref(validatedRange).length === 0 ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"button-group\"))\n      }, [createVNode(unref(ElButtonGroup), null, {\n        default: withCtx(() => [createVNode(unref(ElButton), {\n          size: \"small\",\n          onClick: $event => unref(selectDate)(\"prev-month\")\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(unref(t)(\"el.datepicker.prevMonth\")), 1)]),\n          _: 1\n        }, 8, [\"onClick\"]), createVNode(unref(ElButton), {\n          size: \"small\",\n          onClick: $event => unref(selectDate)(\"today\")\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(unref(t)(\"el.datepicker.today\")), 1)]),\n          _: 1\n        }, 8, [\"onClick\"]), createVNode(unref(ElButton), {\n          size: \"small\",\n          onClick: $event => unref(selectDate)(\"next-month\")\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(unref(t)(\"el.datepicker.nextMonth\")), 1)]),\n          _: 1\n        }, 8, [\"onClick\"])]),\n        _: 1\n      })], 2)) : createCommentVNode(\"v-if\", true)])], 2), unref(validatedRange).length === 0 ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"body\"))\n      }, [createVNode(DateTable, {\n        date: unref(date),\n        \"selected-day\": unref(realSelectedDay),\n        onPick: unref(pickDay)\n      }, createSlots({\n        _: 2\n      }, [_ctx.$slots[\"date-cell\"] ? {\n        name: \"date-cell\",\n        fn: withCtx(data => [renderSlot(_ctx.$slots, \"date-cell\", normalizeProps(guardReactiveProps(data)))])\n      } : void 0]), 1032, [\"date\", \"selected-day\", \"onPick\"])], 2)) : (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"body\"))\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(validatedRange), (range_, index) => {\n        return openBlock(), createBlock(DateTable, {\n          key: index,\n          date: range_[0],\n          \"selected-day\": unref(realSelectedDay),\n          range: range_,\n          \"hide-header\": index !== 0,\n          onPick: unref(pickDay)\n        }, createSlots({\n          _: 2\n        }, [_ctx.$slots[\"date-cell\"] ? {\n          name: \"date-cell\",\n          fn: withCtx(data => [renderSlot(_ctx.$slots, \"date-cell\", normalizeProps(guardReactiveProps(data)))])\n        } : void 0]), 1032, [\"date\", \"selected-day\", \"range\", \"hide-header\", \"onPick\"]);\n      }), 128))], 2))], 2);\n    };\n  }\n});\nvar Calendar = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"calendar.vue\"]]);\nexport { Calendar as default };", "map": {"version": 3, "names": ["name", "COMPONENT_NAME", "ns", "useNamespace", "calculateValidatedDateRange", "date", "pickDay", "realSelectedDay", "selectDate", "validated<PERSON><PERSON><PERSON>", "useCalendar", "props", "emit", "t", "useLocale", "i18nDate", "computed", "<PERSON><PERSON><PERSON><PERSON>", "value", "format", "year", "expose", "selected<PERSON>ay", "_ctx", "_cache", "openBlock", "createElementBlock", "class", "normalizeClass", "unref", "b"], "sources": ["../../../../../../packages/components/calendar/src/calendar.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <div :class=\"ns.e('header')\">\n      <slot name=\"header\" :date=\"i18nDate\">\n        <div :class=\"ns.e('title')\">{{ i18nDate }}</div>\n        <div v-if=\"validatedRange.length === 0\" :class=\"ns.e('button-group')\">\n          <el-button-group>\n            <el-button size=\"small\" @click=\"selectDate('prev-month')\">\n              {{ t('el.datepicker.prevMonth') }}\n            </el-button>\n            <el-button size=\"small\" @click=\"selectDate('today')\">\n              {{ t('el.datepicker.today') }}\n            </el-button>\n            <el-button size=\"small\" @click=\"selectDate('next-month')\">\n              {{ t('el.datepicker.nextMonth') }}\n            </el-button>\n          </el-button-group>\n        </div>\n      </slot>\n    </div>\n    <div v-if=\"validatedRange.length === 0\" :class=\"ns.e('body')\">\n      <date-table :date=\"date\" :selected-day=\"realSelectedDay\" @pick=\"pickDay\">\n        <template v-if=\"$slots['date-cell']\" #date-cell=\"data\">\n          <slot name=\"date-cell\" v-bind=\"data\" />\n        </template>\n      </date-table>\n    </div>\n    <div v-else :class=\"ns.e('body')\">\n      <date-table\n        v-for=\"(range_, index) in validatedRange\"\n        :key=\"index\"\n        :date=\"range_[0]\"\n        :selected-day=\"realSelectedDay\"\n        :range=\"range_\"\n        :hide-header=\"index !== 0\"\n        @pick=\"pickDay\"\n      >\n        <template v-if=\"$slots['date-cell']\" #date-cell=\"data\">\n          <slot name=\"date-cell\" v-bind=\"data\" />\n        </template>\n      </date-table>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { ElButton, ElButtonGroup } from '@element-plus/components/button'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\n\nimport DateTable from './date-table.vue'\nimport { useCalendar } from './use-calendar'\nimport { calendarEmits, calendarProps } from './calendar'\n\nconst ns = useNamespace('calendar')\n\nconst COMPONENT_NAME = 'ElCalendar'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(calendarProps)\nconst emit = defineEmits(calendarEmits)\n\nconst {\n  calculateValidatedDateRange,\n  date,\n  pickDay,\n  realSelectedDay,\n  selectDate,\n  validatedRange,\n} = useCalendar(props, emit, COMPONENT_NAME)\n\nconst { t } = useLocale()\n\nconst i18nDate = computed(() => {\n  const pickedMonth = `el.datepicker.month${date.value.format('M')}`\n  return `${date.value.year()} ${t('el.datepicker.year')} ${t(pickedMonth)}`\n})\n\ndefineExpose({\n  /** @description currently selected date */\n  selectedDay: realSelectedDay,\n  /** @description select a specific date */\n  pickDay,\n  /** @description select date */\n  selectDate,\n  /** @description Calculate the validate date range according to the start and end dates */\n  calculateValidatedDateRange,\n})\n</script>\n"], "mappings": ";;;;;;;;;mCAyDc;EACZA,IAAM,EAAAC;AACR;;;;;;;;;;IALM,MAAAC,EAAA,GAAKC,YAAA,CAAa,UAAU;IAU5B;MACJC,2BAAA;MACAC,IAAA;MACAC,OAAA;MACAC,eAAA;MACAC,UAAA;MACAC;IAAA,CACE,GAAAC,WAAA,CAAYC,KAAO,EAAAC,IAAA,EAAMX,cAAc;IAErC;MAAEY;IAAE,IAAIC,SAAU;IAElB,MAAAC,QAAA,GAAWC,QAAA,CAAS,MAAM;MAC9B,MAAMC,WAAA,GAAc,sBAAsBZ,IAAA,CAAKa,KAAM,CAAAC,MAAA,CAAO,GAAG,CAAC;MAChE,OAAO,GAAGd,IAAA,CAAKa,KAAM,CAAAE,IAAA,EAAM,IAAIP,CAAE,qBAAoB,CAAC,IAAIA,CAAE,CAAAI,WAAW,CAAC;IAAA,CACzE;IAEYI,MAAA;MAAAC,WAAA,EAAAf,eAAA;MAEXD,OAAa;MAAAE,UAAA;MAEbJ;IAAA;IAEA,QAAAmB,IAAA,EAAAC,MAAA;MAAA,OAAAC,SAAA,IAAAC,kBAAA;QAEAC,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA3B,EAAA,EAAA4B,CAAA;MAAA,CACD,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}