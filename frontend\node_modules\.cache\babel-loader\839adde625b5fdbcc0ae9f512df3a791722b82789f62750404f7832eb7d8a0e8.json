{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst cardProps = buildProps({\n  header: {\n    type: String,\n    default: \"\"\n  },\n  footer: {\n    type: String,\n    default: \"\"\n  },\n  bodyStyle: {\n    type: definePropType([String, Object, Array]),\n    default: \"\"\n  },\n  headerClass: String,\n  bodyClass: String,\n  footerClass: String,\n  shadow: {\n    type: String,\n    values: [\"always\", \"hover\", \"never\"],\n    default: \"always\"\n  }\n});\nexport { cardProps };", "map": {"version": 3, "names": ["cardProps", "buildProps", "header", "type", "String", "default", "footer", "bodyStyle", "definePropType", "Object", "Array", "headerClass", "bodyClass", "footerClass", "shadow", "values"], "sources": ["../../../../../../packages/components/card/src/card.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes, StyleValue } from 'vue'\n\nexport const cardProps = buildProps({\n  /**\n   * @description title of the card. Also accepts a DOM passed by `slot#header`\n   */\n  header: {\n    type: String,\n    default: '',\n  },\n  footer: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description CSS style of card body\n   */\n  bodyStyle: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n    default: '',\n  },\n  /**\n   * @description custom class name of card footer\n   */\n  headerClass: String,\n  /**\n   * @description custom class name of card body\n   */\n  bodyClass: String,\n  /**\n   * @description custom class name of card footer\n   */\n  footerClass: String,\n  /**\n   * @description when to show card shadows\n   */\n  shadow: {\n    type: String,\n    values: ['always', 'hover', 'never'],\n    default: 'always',\n  },\n} as const)\nexport type CardProps = ExtractPropTypes<typeof cardProps>\n"], "mappings": ";AACY,MAACA,SAAS,GAAGC,UAAU,CAAC;EAClCC,MAAM,EAAE;IACNC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,MAAM,EAAE;IACNH,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDE,SAAS,EAAE;IACTJ,IAAI,EAAEK,cAAc,CAAC,CAACJ,MAAM,EAAEK,MAAM,EAAEC,KAAK,CAAC,CAAC;IAC7CL,OAAO,EAAE;EACb,CAAG;EACDM,WAAW,EAAEP,MAAM;EACnBQ,SAAS,EAAER,MAAM;EACjBS,WAAW,EAAET,MAAM;EACnBU,MAAM,EAAE;IACNX,IAAI,EAAEC,MAAM;IACZW,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;IACpCV,OAAO,EAAE;EACb;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}