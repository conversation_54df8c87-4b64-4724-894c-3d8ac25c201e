{"ast": null, "code": "import { defineComponent, computed, h, renderSlot } from 'vue';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst spaceItemProps = buildProps({\n  prefixCls: {\n    type: String\n  }\n});\nconst SpaceItem = defineComponent({\n  name: \"ElSpaceItem\",\n  props: spaceItemProps,\n  setup(props, {\n    slots\n  }) {\n    const ns = useNamespace(\"space\");\n    const classes = computed(() => `${props.prefixCls || ns.b()}__item`);\n    return () => h(\"div\", {\n      class: classes.value\n    }, renderSlot(slots, \"default\"));\n  }\n});\nexport { SpaceItem as default, spaceItemProps };", "map": {"version": 3, "names": ["spaceItemProps", "buildProps", "prefixCls", "type", "String", "SpaceItem", "defineComponent", "name", "props", "setup", "slots", "ns", "useNamespace", "classes", "computed", "b", "h", "class", "value", "renderSlot"], "sources": ["../../../../../../packages/components/space/src/item.ts"], "sourcesContent": ["import { computed, defineComponent, h, renderSlot } from 'vue'\nimport { buildProps } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const spaceItemProps = buildProps({\n  prefixCls: {\n    type: String,\n  },\n} as const)\nexport type SpaceItemProps = ExtractPropTypes<typeof spaceItemProps>\n\nconst SpaceItem = defineComponent({\n  name: 'ElSpaceItem',\n\n  props: spaceItemProps,\n\n  setup(props, { slots }) {\n    const ns = useNamespace('space')\n\n    const classes = computed(() => `${props.prefixCls || ns.b()}__item`)\n\n    return () =>\n      h('div', { class: classes.value }, renderSlot(slots, 'default'))\n  },\n})\nexport type SpaceItemInstance = InstanceType<typeof SpaceItem> & unknown\n\nexport default SpaceItem\n"], "mappings": ";;;AAGY,MAACA,cAAc,GAAGC,UAAU,CAAC;EACvCC,SAAS,EAAE;IACTC,IAAI,EAAEC;EACV;AACA,CAAC;AACI,MAACC,SAAS,GAAGC,eAAe,CAAC;EAChCC,IAAI,EAAE,aAAa;EACnBC,KAAK,EAAER,cAAc;EACrBS,KAAKA,CAACD,KAAK,EAAE;IAAEE;EAAK,CAAE,EAAE;IACtB,MAAMC,EAAE,GAAGC,YAAY,CAAC,OAAO,CAAC;IAChC,MAAMC,OAAO,GAAGC,QAAQ,CAAC,MAAM,GAAGN,KAAK,CAACN,SAAS,IAAIS,EAAE,CAACI,CAAC,EAAE,QAAQ,CAAC;IACpE,OAAO,MAAMC,CAAC,CAAC,KAAK,EAAE;MAAEC,KAAK,EAAEJ,OAAO,CAACK;IAAK,CAAE,EAAEC,UAAU,CAACT,KAAK,EAAE,SAAS,CAAC,CAAC;EACjF;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}