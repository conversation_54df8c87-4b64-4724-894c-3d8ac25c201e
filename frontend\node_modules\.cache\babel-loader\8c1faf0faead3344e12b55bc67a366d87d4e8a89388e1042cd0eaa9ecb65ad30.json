{"ast": null, "code": "import { createApp } from \"vue\";\nimport App from \"./App.vue\";\nimport router from \"./router\";\nimport store from \"./store\";\nimport ElementPlus from \"element-plus\";\nimport \"element-plus/dist/index.css\";\nimport * as ElementPlusIconsVue from \"@element-plus/icons-vue\";\nimport \"./assets/styles/global.scss\";\nconst app = createApp(App);\n\n// 注册所有图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component);\n}\napp.use(store).use(router).use(ElementPlus).mount(\"#app\");", "map": {"version": 3, "names": ["createApp", "App", "router", "store", "ElementPlus", "ElementPlusIconsVue", "app", "key", "component", "Object", "entries", "use", "mount"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/src/main.js"], "sourcesContent": ["import { createApp } from \"vue\";\nimport App from \"./App.vue\";\nimport router from \"./router\";\nimport store from \"./store\";\nimport ElementPlus from \"element-plus\";\nimport \"element-plus/dist/index.css\";\nimport * as ElementPlusIconsVue from \"@element-plus/icons-vue\";\nimport \"./assets/styles/global.scss\";\n\nconst app = createApp(App);\n\n// 注册所有图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component);\n}\n\napp.use(store).use(router).use(ElementPlus).mount(\"#app\");\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAO,6BAA6B;AACpC,OAAO,KAAKC,mBAAmB,MAAM,yBAAyB;AAC9D,OAAO,6BAA6B;AAEpC,MAAMC,GAAG,GAAGN,SAAS,CAACC,GAAG,CAAC;;AAE1B;AACA,KAAK,MAAM,CAACM,GAAG,EAAEC,SAAS,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,mBAAmB,CAAC,EAAE;EAClEC,GAAG,CAACE,SAAS,CAACD,GAAG,EAAEC,SAAS,CAAC;AAC/B;AAEAF,GAAG,CAACK,GAAG,CAACR,KAAK,CAAC,CAACQ,GAAG,CAACT,MAAM,CAAC,CAACS,GAAG,CAACP,WAAW,CAAC,CAACQ,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}