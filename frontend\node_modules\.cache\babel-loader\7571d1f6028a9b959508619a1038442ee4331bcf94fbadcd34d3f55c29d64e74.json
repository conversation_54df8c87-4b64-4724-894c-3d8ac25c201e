{"ast": null, "code": "import Container from './src/container.mjs';\nimport Aside from './src/aside.mjs';\nimport Footer from './src/footer.mjs';\nimport Header from './src/header.mjs';\nimport Main from './src/main.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElContainer = withInstall(Container, {\n  Aside,\n  Footer,\n  Header,\n  Main\n});\nconst ElAside = withNoopInstall(Aside);\nconst ElFooter = withNoopInstall(Footer);\nconst ElHeader = withNoopInstall(Header);\nconst ElMain = withNoopInstall(Main);\nexport { ElAside, ElContainer, ElFooter, ElHeader, ElMain, ElContainer as default };", "map": {"version": 3, "names": ["ElC<PERSON><PERSON>", "withInstall", "Container", "Aside", "Footer", "Header", "Main", "ElAside", "withNoopInstall", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "sources": ["../../../../../packages/components/container/index.ts"], "sourcesContent": ["import { withInstall, withNoopInstall } from '@element-plus/utils'\n\nimport Container from './src/container.vue'\nimport Aside from './src/aside.vue'\nimport Footer from './src/footer.vue'\nimport Header from './src/header.vue'\nimport Main from './src/main.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElContainer: SFCWithInstall<typeof Container> & {\n  Aside: typeof Aside\n  Footer: typeof Footer\n  Header: typeof Header\n  Main: typeof Main\n} = withInstall(Container, {\n  Aside,\n  Footer,\n  Header,\n  Main,\n})\n\nexport default ElContainer\nexport const ElAside: SFCWithInstall<typeof Aside> = withNoopInstall(Aside)\nexport const ElFooter: SFCWithInstall<typeof Footer> = withNoopInstall(Footer)\nexport const ElHeader: SFCWithInstall<typeof Header> = withNoopInstall(Header)\nexport const ElMain: SFCWithInstall<typeof Main> = withNoopInstall(Main)\n\nexport type ContainerInstance = InstanceType<typeof Container> & unknown\nexport type AsideInstance = InstanceType<typeof Aside> & unknown\nexport type FooterInstance = InstanceType<typeof Footer> & unknown\nexport type HeaderInstance = InstanceType<typeof Header> & unknown\nexport type MainInstance = InstanceType<typeof Main> & unknown\n"], "mappings": ";;;;;;AAMY,MAACA,WAAW,GAAGC,WAAW,CAACC,SAAS,EAAE;EAChDC,KAAK;EACLC,MAAM;EACNC,MAAM;EACNC;AACF,CAAC;AAEW,MAACC,OAAO,GAAGC,eAAe,CAACL,KAAK;AAChC,MAACM,QAAQ,GAAGD,eAAe,CAACJ,MAAM;AAClC,MAACM,QAAQ,GAAGF,eAAe,CAACH,MAAM;AAClC,MAACM,MAAM,GAAGH,eAAe,CAACF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}