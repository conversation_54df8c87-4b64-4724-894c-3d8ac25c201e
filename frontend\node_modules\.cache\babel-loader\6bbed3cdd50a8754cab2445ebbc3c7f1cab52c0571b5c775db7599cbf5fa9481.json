{"ast": null, "code": "import baseDifference from './_baseDifference.js';\nimport baseRest from './_baseRest.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\n\n/**\n * Creates an array excluding all given values using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * **Note:** Unlike `_.pull`, this method returns a new array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {...*} [values] The values to exclude.\n * @returns {Array} Returns the new array of filtered values.\n * @see _.difference, _.xor\n * @example\n *\n * _.without([2, 1, 2, 3], 1, 2);\n * // => [3]\n */\nvar without = baseRest(function (array, values) {\n  return isArrayLikeObject(array) ? baseDifference(array, values) : [];\n});\nexport default without;", "map": {"version": 3, "names": ["baseDifference", "baseRest", "isArrayLikeObject", "without", "array", "values"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/without.js"], "sourcesContent": ["import baseDifference from './_baseDifference.js';\nimport baseRest from './_baseRest.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\n\n/**\n * Creates an array excluding all given values using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * **Note:** Unlike `_.pull`, this method returns a new array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {...*} [values] The values to exclude.\n * @returns {Array} Returns the new array of filtered values.\n * @see _.difference, _.xor\n * @example\n *\n * _.without([2, 1, 2, 3], 1, 2);\n * // => [3]\n */\nvar without = baseRest(function(array, values) {\n  return isArrayLikeObject(array)\n    ? baseDifference(array, values)\n    : [];\n});\n\nexport default without;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;AACjD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,iBAAiB,MAAM,wBAAwB;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAGF,QAAQ,CAAC,UAASG,KAAK,EAAEC,MAAM,EAAE;EAC7C,OAAOH,iBAAiB,CAACE,KAAK,CAAC,GAC3BJ,cAAc,CAACI,KAAK,EAAEC,MAAM,CAAC,GAC7B,EAAE;AACR,CAAC,CAAC;AAEF,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}