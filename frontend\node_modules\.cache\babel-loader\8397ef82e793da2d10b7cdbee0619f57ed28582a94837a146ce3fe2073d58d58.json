{"ast": null, "code": "import { getCurrentInstance, shallowRef, ref, watch, onMounted } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nimport { useProp } from '../use-prop/index.mjs';\nimport { isElement } from '../../utils/types.mjs';\nimport { isFunction } from '@vue/shared';\nfunction useFocusController(target, {\n  beforeFocus,\n  afterFocus,\n  beforeBlur,\n  afterBlur\n} = {}) {\n  const instance = getCurrentInstance();\n  const {\n    emit\n  } = instance;\n  const wrapperRef = shallowRef();\n  const disabled = useProp(\"disabled\");\n  const isFocused = ref(false);\n  const handleFocus = event => {\n    const cancelFocus = isFunction(beforeFocus) ? beforeFocus(event) : false;\n    if (cancelFocus || isFocused.value) return;\n    isFocused.value = true;\n    emit(\"focus\", event);\n    afterFocus == null ? void 0 : afterFocus();\n  };\n  const handleBlur = event => {\n    var _a;\n    const cancelBlur = isFunction(beforeBlur) ? beforeBlur(event) : false;\n    if (cancelBlur || event.relatedTarget && ((_a = wrapperRef.value) == null ? void 0 : _a.contains(event.relatedTarget))) return;\n    isFocused.value = false;\n    emit(\"blur\", event);\n    afterBlur == null ? void 0 : afterBlur();\n  };\n  const handleClick = () => {\n    var _a, _b;\n    if (((_a = wrapperRef.value) == null ? void 0 : _a.contains(document.activeElement)) && wrapperRef.value !== document.activeElement || disabled.value) return;\n    (_b = target.value) == null ? void 0 : _b.focus();\n  };\n  watch([wrapperRef, disabled], ([el, disabled2]) => {\n    if (!el) return;\n    if (disabled2) {\n      el.removeAttribute(\"tabindex\");\n    } else {\n      el.setAttribute(\"tabindex\", \"-1\");\n    }\n  });\n  useEventListener(wrapperRef, \"focus\", handleFocus, true);\n  useEventListener(wrapperRef, \"blur\", handleBlur, true);\n  useEventListener(wrapperRef, \"click\", handleClick, true);\n  if (process.env.NODE_ENV === \"test\") {\n    onMounted(() => {\n      const targetEl = isElement(target.value) ? target.value : document.querySelector(\"input,textarea\");\n      if (targetEl) {\n        useEventListener(targetEl, \"focus\", handleFocus, true);\n        useEventListener(targetEl, \"blur\", handleBlur, true);\n      }\n    });\n  }\n  return {\n    isFocused,\n    wrapperRef,\n    handleFocus,\n    handleBlur\n  };\n}\nexport { useFocusController };", "map": {"version": 3, "names": ["useFocusController", "target", "beforeFocus", "afterFocus", "beforeBlur", "after<PERSON><PERSON>r", "instance", "getCurrentInstance", "emit", "wrapperRef", "shallowRef", "disabled", "useProp", "isFocused", "ref", "handleFocus", "event", "cancelFocus", "isFunction", "value", "handleBlur", "_a", "cancelBlur", "relatedTarget", "contains", "handleClick", "_b", "document", "activeElement", "focus", "watch", "el", "disabled2", "removeAttribute", "setAttribute", "useEventListener", "process", "env", "NODE_ENV", "onMounted", "targetEl", "isElement", "querySelector"], "sources": ["../../../../../packages/hooks/use-focus-controller/index.ts"], "sourcesContent": ["import { getCurrentInstance, onMounted, ref, shallowRef, watch } from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { isElement, isFunction } from '@element-plus/utils'\nimport { useProp } from '../use-prop'\nimport type { ShallowRef } from 'vue'\n\ninterface UseFocusControllerOptions {\n  /**\n   * return true to cancel focus\n   * @param event FocusEvent\n   */\n  beforeFocus?: (event: FocusEvent) => boolean | undefined\n  afterFocus?: () => void\n  /**\n   * return true to cancel blur\n   * @param event FocusEvent\n   */\n  beforeBlur?: (event: FocusEvent) => boolean | undefined\n  afterBlur?: () => void\n}\n\nexport function useFocusController<T extends { focus: () => void }>(\n  target: ShallowRef<T | undefined>,\n  {\n    beforeFocus,\n    afterFocus,\n    beforeBlur,\n    afterBlur,\n  }: UseFocusControllerOptions = {}\n) {\n  const instance = getCurrentInstance()!\n  const { emit } = instance\n  const wrapperRef = shallowRef<HTMLElement>()\n  const disabled = useProp<boolean>('disabled')\n  const isFocused = ref(false)\n\n  const handleFocus = (event: FocusEvent) => {\n    const cancelFocus = isFunction(beforeFocus) ? beforeFocus(event) : false\n    if (cancelFocus || isFocused.value) return\n    isFocused.value = true\n    emit('focus', event)\n    afterFocus?.()\n  }\n\n  const handleBlur = (event: FocusEvent) => {\n    const cancelBlur = isFunction(beforeBlur) ? beforeBlur(event) : false\n    if (\n      cancelBlur ||\n      (event.relatedTarget &&\n        wrapperRef.value?.contains(event.relatedTarget as Node))\n    )\n      return\n\n    isFocused.value = false\n    emit('blur', event)\n    afterBlur?.()\n  }\n\n  const handleClick = () => {\n    if (\n      (wrapperRef.value?.contains(document.activeElement) &&\n        wrapperRef.value !== document.activeElement) ||\n      disabled.value\n    )\n      return\n\n    target.value?.focus()\n  }\n\n  watch([wrapperRef, disabled], ([el, disabled]) => {\n    if (!el) return\n    if (disabled) {\n      el.removeAttribute('tabindex')\n    } else {\n      el.setAttribute('tabindex', '-1')\n    }\n  })\n\n  useEventListener(wrapperRef, 'focus', handleFocus, true)\n  useEventListener(wrapperRef, 'blur', handleBlur, true)\n  useEventListener(wrapperRef, 'click', handleClick, true)\n\n  // only for test\n  if (process.env.NODE_ENV === 'test') {\n    onMounted(() => {\n      const targetEl = isElement(target.value)\n        ? target.value\n        : document.querySelector('input,textarea')\n\n      if (targetEl) {\n        useEventListener(targetEl, 'focus', handleFocus, true)\n        useEventListener(targetEl, 'blur', handleBlur, true)\n      }\n    })\n  }\n\n  return {\n    isFocused,\n    /** Avoid using wrapperRef and handleFocus/handleBlur together */\n    wrapperRef,\n    handleFocus,\n    handleBlur,\n  }\n}\n"], "mappings": ";;;;;AAIO,SAASA,kBAAkBA,CAACC,MAAM,EAAE;EACzCC,WAAW;EACXC,UAAU;EACVC,UAAU;EACVC;AACF,CAAC,GAAG,EAAE,EAAE;EACN,MAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,MAAM;IAAEC;EAAI,CAAE,GAAGF,QAAQ;EACzB,MAAMG,UAAU,GAAGC,UAAU,EAAE;EAC/B,MAAMC,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EACpC,MAAMC,SAAS,GAAGC,GAAG,CAAC,KAAK,CAAC;EAC5B,MAAMC,WAAW,GAAIC,KAAK,IAAK;IAC7B,MAAMC,WAAW,GAAGC,UAAU,CAAChB,WAAW,CAAC,GAAGA,WAAW,CAACc,KAAK,CAAC,GAAG,KAAK;IACxE,IAAIC,WAAW,IAAIJ,SAAS,CAACM,KAAK,EAChC;IACFN,SAAS,CAACM,KAAK,GAAG,IAAI;IACtBX,IAAI,CAAC,OAAO,EAAEQ,KAAK,CAAC;IACpBb,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,EAAE;EAC9C,CAAG;EACD,MAAMiB,UAAU,GAAIJ,KAAK,IAAK;IAC5B,IAAIK,EAAE;IACN,MAAMC,UAAU,GAAGJ,UAAU,CAACd,UAAU,CAAC,GAAGA,UAAU,CAACY,KAAK,CAAC,GAAG,KAAK;IACrE,IAAIM,UAAU,IAAIN,KAAK,CAACO,aAAa,KAAK,CAACF,EAAE,GAAGZ,UAAU,CAACU,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,EAAE,CAACG,QAAQ,CAACR,KAAK,CAACO,aAAa,CAAC,CAAC,EACpH;IACFV,SAAS,CAACM,KAAK,GAAG,KAAK;IACvBX,IAAI,CAAC,MAAM,EAAEQ,KAAK,CAAC;IACnBX,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,EAAE;EAC5C,CAAG;EACD,MAAMoB,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIJ,EAAE,EAAEK,EAAE;IACV,IAAI,CAAC,CAACL,EAAE,GAAGZ,UAAU,CAACU,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,EAAE,CAACG,QAAQ,CAACG,QAAQ,CAACC,aAAa,CAAC,KAAKnB,UAAU,CAACU,KAAK,KAAKQ,QAAQ,CAACC,aAAa,IAAIjB,QAAQ,CAACQ,KAAK,EACnJ;IACF,CAACO,EAAE,GAAGzB,MAAM,CAACkB,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGO,EAAE,CAACG,KAAK,EAAE;EACrD,CAAG;EACDC,KAAK,CAAC,CAACrB,UAAU,EAAEE,QAAQ,CAAC,EAAE,CAAC,CAACoB,EAAE,EAAEC,SAAS,CAAC,KAAK;IACjD,IAAI,CAACD,EAAE,EACL;IACF,IAAIC,SAAS,EAAE;MACbD,EAAE,CAACE,eAAe,CAAC,UAAU,CAAC;IACpC,CAAK,MAAM;MACLF,EAAE,CAACG,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;IACvC;EACA,CAAG,CAAC;EACFC,gBAAgB,CAAC1B,UAAU,EAAE,OAAO,EAAEM,WAAW,EAAE,IAAI,CAAC;EACxDoB,gBAAgB,CAAC1B,UAAU,EAAE,MAAM,EAAEW,UAAU,EAAE,IAAI,CAAC;EACtDe,gBAAgB,CAAC1B,UAAU,EAAE,OAAO,EAAEgB,WAAW,EAAE,IAAI,CAAC;EACxD,IAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;IACnCC,SAAS,CAAC,MAAM;MACd,MAAMC,QAAQ,GAAGC,SAAS,CAACxC,MAAM,CAACkB,KAAK,CAAC,GAAGlB,MAAM,CAACkB,KAAK,GAAGQ,QAAQ,CAACe,aAAa,CAAC,gBAAgB,CAAC;MAClG,IAAIF,QAAQ,EAAE;QACZL,gBAAgB,CAACK,QAAQ,EAAE,OAAO,EAAEzB,WAAW,EAAE,IAAI,CAAC;QACtDoB,gBAAgB,CAACK,QAAQ,EAAE,MAAM,EAAEpB,UAAU,EAAE,IAAI,CAAC;MAC5D;IACA,CAAK,CAAC;EACN;EACE,OAAO;IACLP,SAAS;IACTJ,UAAU;IACVM,WAAW;IACXK;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}