{"ast": null, "code": "import { computed, unref, ref, inject } from 'vue';\nimport { formContextKey, formItemContextKey } from '../constants.mjs';\nimport { useProp } from '../../../../hooks/use-prop/index.mjs';\nimport { useGlobalSize } from '../../../../hooks/use-size/index.mjs';\nconst useFormSize = (fallback, ignore = {}) => {\n  const emptyRef = ref(void 0);\n  const size = ignore.prop ? emptyRef : useProp(\"size\");\n  const globalConfig = ignore.global ? emptyRef : useGlobalSize();\n  const form = ignore.form ? {\n    size: void 0\n  } : inject(formContextKey, void 0);\n  const formItem = ignore.formItem ? {\n    size: void 0\n  } : inject(formItemContextKey, void 0);\n  return computed(() => size.value || unref(fallback) || (formItem == null ? void 0 : formItem.size) || (form == null ? void 0 : form.size) || globalConfig.value || \"\");\n};\nconst useFormDisabled = fallback => {\n  const disabled = useProp(\"disabled\");\n  const form = inject(formContextKey, void 0);\n  return computed(() => disabled.value || unref(fallback) || (form == null ? void 0 : form.disabled) || false);\n};\nconst useSize = useFormSize;\nconst useDisabled = useFormDisabled;\nexport { useDisabled, useFormDisabled, useFormSize, useSize };", "map": {"version": 3, "names": ["useFormSize", "fallback", "ignore", "emptyRef", "ref", "size", "prop", "useProp", "globalConfig", "global", "useGlobalSize", "form", "inject", "formContextKey", "formItem", "formItemContextKey", "computed", "value", "unref", "useFormDisabled", "disabled", "useSize", "useDisabled"], "sources": ["../../../../../../../packages/components/form/src/hooks/use-form-common-props.ts"], "sourcesContent": ["import { computed, inject, ref, unref } from 'vue'\nimport { useGlobalSize, useProp } from '@element-plus/hooks'\nimport { formContextKey, formItemContextKey } from '../constants'\n\nimport type { ComponentSize } from '@element-plus/constants'\nimport type { MaybeRef } from '@vueuse/core'\n\nexport const useFormSize = (\n  fallback?: MaybeRef<ComponentSize | undefined>,\n  ignore: Partial<Record<'prop' | 'form' | 'formItem' | 'global', boolean>> = {}\n) => {\n  const emptyRef = ref(undefined)\n\n  const size = ignore.prop ? emptyRef : useProp<ComponentSize>('size')\n  const globalConfig = ignore.global ? emptyRef : useGlobalSize()\n  const form = ignore.form\n    ? { size: undefined }\n    : inject(formContextKey, undefined)\n  const formItem = ignore.formItem\n    ? { size: undefined }\n    : inject(formItemContextKey, undefined)\n\n  return computed(\n    (): ComponentSize =>\n      size.value ||\n      unref(fallback) ||\n      formItem?.size ||\n      form?.size ||\n      globalConfig.value ||\n      ''\n  )\n}\n\nexport const useFormDisabled = (fallback?: MaybeRef<boolean | undefined>) => {\n  const disabled = useProp<boolean>('disabled')\n  const form = inject(formContextKey, undefined)\n  return computed(\n    () => disabled.value || unref(fallback) || form?.disabled || false\n  )\n}\n\n// These exports are used for preventing breaking changes\nexport const useSize = useFormSize\nexport const useDisabled = useFormDisabled\n"], "mappings": ";;;;AAGY,MAACA,WAAW,GAAGA,CAACC,QAAQ,EAAEC,MAAM,GAAG,EAAE,KAAK;EACpD,MAAMC,QAAQ,GAAGC,GAAG,CAAC,KAAK,CAAC,CAAC;EAC5B,MAAMC,IAAI,GAAGH,MAAM,CAACI,IAAI,GAAGH,QAAQ,GAAGI,OAAO,CAAC,MAAM,CAAC;EACrD,MAAMC,YAAY,GAAGN,MAAM,CAACO,MAAM,GAAGN,QAAQ,GAAGO,aAAa,EAAE;EAC/D,MAAMC,IAAI,GAAGT,MAAM,CAACS,IAAI,GAAG;IAAEN,IAAI,EAAE,KAAK;EAAC,CAAE,GAAGO,MAAM,CAACC,cAAc,EAAE,KAAK,CAAC,CAAC;EAC5E,MAAMC,QAAQ,GAAGZ,MAAM,CAACY,QAAQ,GAAG;IAAET,IAAI,EAAE,KAAK;EAAC,CAAE,GAAGO,MAAM,CAACG,kBAAkB,EAAE,KAAK,CAAC,CAAC;EACxF,OAAOC,QAAQ,CAAC,MAAMX,IAAI,CAACY,KAAK,IAAIC,KAAK,CAACjB,QAAQ,CAAC,KAAKa,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACT,IAAI,CAAC,KAAKM,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACN,IAAI,CAAC,IAAIG,YAAY,CAACS,KAAK,IAAI,EAAE,CAAC;AACxK;AACY,MAACE,eAAe,GAAIlB,QAAQ,IAAK;EAC3C,MAAMmB,QAAQ,GAAGb,OAAO,CAAC,UAAU,CAAC;EACpC,MAAMI,IAAI,GAAGC,MAAM,CAACC,cAAc,EAAE,KAAK,CAAC,CAAC;EAC3C,OAAOG,QAAQ,CAAC,MAAMI,QAAQ,CAACH,KAAK,IAAIC,KAAK,CAACjB,QAAQ,CAAC,KAAKU,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACS,QAAQ,CAAC,IAAI,KAAK,CAAC;AAC9G;AACY,MAACC,OAAO,GAAGrB,WAAA;AACX,MAACsB,WAAW,GAAGH,eAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}