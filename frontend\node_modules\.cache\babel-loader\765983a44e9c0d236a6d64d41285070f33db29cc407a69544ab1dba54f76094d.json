{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { componentSizes } from '../../../constants/size.mjs';\nconst textProps = buildProps({\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"info\", \"warning\", \"danger\", \"\"],\n    default: \"\"\n  },\n  size: {\n    type: String,\n    values: componentSizes,\n    default: \"\"\n  },\n  truncated: Boolean,\n  lineClamp: {\n    type: [String, Number]\n  },\n  tag: {\n    type: String,\n    default: \"span\"\n  }\n});\nexport { textProps };", "map": {"version": 3, "names": ["textProps", "buildProps", "type", "String", "values", "default", "size", "componentSizes", "truncated", "Boolean", "lineClamp", "Number", "tag"], "sources": ["../../../../../../packages/components/text/src/text.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { componentSizes } from '@element-plus/constants'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const textProps = buildProps({\n  /**\n   * @description text type\n   */\n  type: {\n    type: String,\n    values: ['primary', 'success', 'info', 'warning', 'danger', ''],\n    default: '',\n  },\n  /**\n   * @description text size\n   */\n  size: {\n    type: String,\n    values: componentSizes,\n    default: '',\n  },\n  /**\n   * @description render ellipsis\n   */\n  truncated: Boolean,\n  /**\n   * @description maximum lines\n   */\n  lineClamp: {\n    type: [String, Number],\n  },\n  /**\n   * @description custom element tag\n   */\n  tag: {\n    type: String,\n    default: 'span',\n  },\n} as const)\n\nexport type TextProps = ExtractPropTypes<typeof textProps>\n"], "mappings": ";;AAEY,MAACA,SAAS,GAAGC,UAAU,CAAC;EAClCC,IAAI,EAAE;IACJA,IAAI,EAAEC,MAAM;IACZC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,EAAE,CAAC;IAC/DC,OAAO,EAAE;EACb,CAAG;EACDC,IAAI,EAAE;IACJJ,IAAI,EAAEC,MAAM;IACZC,MAAM,EAAEG,cAAc;IACtBF,OAAO,EAAE;EACb,CAAG;EACDG,SAAS,EAAEC,OAAO;EAClBC,SAAS,EAAE;IACTR,IAAI,EAAE,CAACC,MAAM,EAAEQ,MAAM;EACzB,CAAG;EACDC,GAAG,EAAE;IACHV,IAAI,EAAEC,MAAM;IACZE,OAAO,EAAE;EACb;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}