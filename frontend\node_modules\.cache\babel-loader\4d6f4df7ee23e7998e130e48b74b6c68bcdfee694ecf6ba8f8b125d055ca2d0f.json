{"ast": null, "code": "require(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.map.js\");\nconst {\n  sm3,\n  hmac\n} = require('../sm2/sm3');\n\n/**\n * 补全16进制字符串\n */\nfunction leftPad(input, num) {\n  if (input.length >= num) return input;\n  return new Array(num - input.length + 1).join('0') + input;\n}\n\n/**\n * 字节数组转 16 进制串\n */\nfunction ArrayToHex(arr) {\n  return arr.map(item => {\n    item = item.toString(16);\n    return item.length === 1 ? '0' + item : item;\n  }).join('');\n}\n\n/**\n * 转成字节数组\n */\nfunction hexToArray(hexStr) {\n  const words = [];\n  let hexStrLength = hexStr.length;\n  if (hexStrLength % 2 !== 0) {\n    hexStr = leftPad(hexStr, hexStrLength + 1);\n  }\n  hexStrLength = hexStr.length;\n  for (let i = 0; i < hexStrLength; i += 2) {\n    words.push(parseInt(hexStr.substr(i, 2), 16));\n  }\n  return words;\n}\n\n/**\n * utf8 串转字节数组\n */\nfunction utf8ToArray(str) {\n  const arr = [];\n  for (let i = 0, len = str.length; i < len; i++) {\n    const point = str.codePointAt(i);\n    if (point <= 0x007f) {\n      // 单字节，标量值：00000000 00000000 0zzzzzzz\n      arr.push(point);\n    } else if (point <= 0x07ff) {\n      // 双字节，标量值：00000000 00000yyy yyzzzzzz\n      arr.push(0xc0 | point >>> 6); // 110yyyyy（0xc0-0xdf）\n      arr.push(0x80 | point & 0x3f); // 10zzzzzz（0x80-0xbf）\n    } else if (point <= 0xD7FF || point >= 0xE000 && point <= 0xFFFF) {\n      // 三字节：标量值：00000000 xxxxyyyy yyzzzzzz\n      arr.push(0xe0 | point >>> 12); // 1110xxxx（0xe0-0xef）\n      arr.push(0x80 | point >>> 6 & 0x3f); // 10yyyyyy（0x80-0xbf）\n      arr.push(0x80 | point & 0x3f); // 10zzzzzz（0x80-0xbf）\n    } else if (point >= 0x010000 && point <= 0x10FFFF) {\n      // 四字节：标量值：000wwwxx xxxxyyyy yyzzzzzz\n      i++;\n      arr.push(0xf0 | point >>> 18 & 0x1c); // 11110www（0xf0-0xf7）\n      arr.push(0x80 | point >>> 12 & 0x3f); // 10xxxxxx（0x80-0xbf）\n      arr.push(0x80 | point >>> 6 & 0x3f); // 10yyyyyy（0x80-0xbf）\n      arr.push(0x80 | point & 0x3f); // 10zzzzzz（0x80-0xbf）\n    } else {\n      // 五、六字节，暂时不支持\n      arr.push(point);\n      throw new Error('input is not supported');\n    }\n  }\n  return arr;\n}\nmodule.exports = function (input, options) {\n  input = typeof input === 'string' ? utf8ToArray(input) : Array.prototype.slice.call(input);\n  if (options) {\n    const mode = options.mode || 'hmac';\n    if (mode !== 'hmac') throw new Error('invalid mode');\n    let key = options.key;\n    if (!key) throw new Error('invalid key');\n    key = typeof key === 'string' ? hexToArray(key) : Array.prototype.slice.call(key);\n    return ArrayToHex(hmac(input, key));\n  }\n  return ArrayToHex(sm3(input));\n};", "map": {"version": 3, "names": ["sm3", "hmac", "require", "leftPad", "input", "num", "length", "Array", "join", "ArrayToHex", "arr", "map", "item", "toString", "hexToArray", "hexStr", "words", "hexStr<PERSON>ength", "i", "push", "parseInt", "substr", "utf8ToArray", "str", "len", "point", "codePointAt", "Error", "module", "exports", "options", "prototype", "slice", "call", "mode", "key"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/sm-crypto/src/sm3/index.js"], "sourcesContent": ["const {sm3, hmac} = require('../sm2/sm3')\n\n/**\n * 补全16进制字符串\n */\nfunction leftPad(input, num) {\n  if (input.length >= num) return input\n\n  return (new Array(num - input.length + 1)).join('0') + input\n}\n\n/**\n * 字节数组转 16 进制串\n */\nfunction ArrayToHex(arr) {\n  return arr.map(item => {\n    item = item.toString(16)\n    return item.length === 1 ? '0' + item : item\n  }).join('')\n}\n\n/**\n * 转成字节数组\n */\nfunction hexToArray(hexStr) {\n  const words = []\n  let hexStrLength = hexStr.length\n\n  if (hexStrLength % 2 !== 0) {\n    hexStr = leftPad(hexStr, hexStrLength + 1)\n  }\n\n  hexStrLength = hexStr.length\n\n  for (let i = 0; i < hexStrLength; i += 2) {\n    words.push(parseInt(hexStr.substr(i, 2), 16))\n  }\n  return words\n}\n\n/**\n * utf8 串转字节数组\n */\nfunction utf8ToArray(str) {\n  const arr = []\n\n  for (let i = 0, len = str.length; i < len; i++) {\n    const point = str.codePointAt(i)\n\n    if (point <= 0x007f) {\n      // 单字节，标量值：00000000 00000000 0zzzzzzz\n      arr.push(point)\n    } else if (point <= 0x07ff) {\n      // 双字节，标量值：00000000 00000yyy yyzzzzzz\n      arr.push(0xc0 | (point >>> 6)) // 110yyyyy（0xc0-0xdf）\n      arr.push(0x80 | (point & 0x3f)) // 10zzzzzz（0x80-0xbf）\n    } else if (point <= 0xD7FF || (point >= 0xE000 && point <= 0xFFFF)) {\n      // 三字节：标量值：00000000 xxxxyyyy yyzzzzzz\n      arr.push(0xe0 | (point >>> 12)) // 1110xxxx（0xe0-0xef）\n      arr.push(0x80 | ((point >>> 6) & 0x3f)) // 10yyyyyy（0x80-0xbf）\n      arr.push(0x80 | (point & 0x3f)) // 10zzzzzz（0x80-0xbf）\n    } else if (point >= 0x010000 && point <= 0x10FFFF) {\n      // 四字节：标量值：000wwwxx xxxxyyyy yyzzzzzz\n      i++\n      arr.push((0xf0 | (point >>> 18) & 0x1c)) // 11110www（0xf0-0xf7）\n      arr.push((0x80 | ((point >>> 12) & 0x3f))) // 10xxxxxx（0x80-0xbf）\n      arr.push((0x80 | ((point >>> 6) & 0x3f))) // 10yyyyyy（0x80-0xbf）\n      arr.push((0x80 | (point & 0x3f))) // 10zzzzzz（0x80-0xbf）\n    } else {\n      // 五、六字节，暂时不支持\n      arr.push(point)\n      throw new Error('input is not supported')\n    }\n  }\n\n  return arr\n}\n\nmodule.exports = function (input, options) {\n  input = typeof input === 'string' ? utf8ToArray(input) : Array.prototype.slice.call(input)\n\n  if (options) {\n    const mode = options.mode || 'hmac'\n    if (mode !== 'hmac') throw new Error('invalid mode')\n\n    let key = options.key\n    if (!key) throw new Error('invalid key')\n\n    key = typeof key === 'string' ? hexToArray(key) : Array.prototype.slice.call(key)\n    return ArrayToHex(hmac(input, key))\n  }\n\n  return ArrayToHex(sm3(input))\n}\n"], "mappings": ";;;AAAA,MAAM;EAACA,GAAG;EAAEC;AAAI,CAAC,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEzC;AACA;AACA;AACA,SAASC,OAAOA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC3B,IAAID,KAAK,CAACE,MAAM,IAAID,GAAG,EAAE,OAAOD,KAAK;EAErC,OAAQ,IAAIG,KAAK,CAACF,GAAG,GAAGD,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC,CAAEE,IAAI,CAAC,GAAG,CAAC,GAAGJ,KAAK;AAC9D;;AAEA;AACA;AACA;AACA,SAASK,UAAUA,CAACC,GAAG,EAAE;EACvB,OAAOA,GAAG,CAACC,GAAG,CAACC,IAAI,IAAI;IACrBA,IAAI,GAAGA,IAAI,CAACC,QAAQ,CAAC,EAAE,CAAC;IACxB,OAAOD,IAAI,CAACN,MAAM,KAAK,CAAC,GAAG,GAAG,GAAGM,IAAI,GAAGA,IAAI;EAC9C,CAAC,CAAC,CAACJ,IAAI,CAAC,EAAE,CAAC;AACb;;AAEA;AACA;AACA;AACA,SAASM,UAAUA,CAACC,MAAM,EAAE;EAC1B,MAAMC,KAAK,GAAG,EAAE;EAChB,IAAIC,YAAY,GAAGF,MAAM,CAACT,MAAM;EAEhC,IAAIW,YAAY,GAAG,CAAC,KAAK,CAAC,EAAE;IAC1BF,MAAM,GAAGZ,OAAO,CAACY,MAAM,EAAEE,YAAY,GAAG,CAAC,CAAC;EAC5C;EAEAA,YAAY,GAAGF,MAAM,CAACT,MAAM;EAE5B,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,EAAEC,CAAC,IAAI,CAAC,EAAE;IACxCF,KAAK,CAACG,IAAI,CAACC,QAAQ,CAACL,MAAM,CAACM,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAC/C;EACA,OAAOF,KAAK;AACd;;AAEA;AACA;AACA;AACA,SAASM,WAAWA,CAACC,GAAG,EAAE;EACxB,MAAMb,GAAG,GAAG,EAAE;EAEd,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEM,GAAG,GAAGD,GAAG,CAACjB,MAAM,EAAEY,CAAC,GAAGM,GAAG,EAAEN,CAAC,EAAE,EAAE;IAC9C,MAAMO,KAAK,GAAGF,GAAG,CAACG,WAAW,CAACR,CAAC,CAAC;IAEhC,IAAIO,KAAK,IAAI,MAAM,EAAE;MACnB;MACAf,GAAG,CAACS,IAAI,CAACM,KAAK,CAAC;IACjB,CAAC,MAAM,IAAIA,KAAK,IAAI,MAAM,EAAE;MAC1B;MACAf,GAAG,CAACS,IAAI,CAAC,IAAI,GAAIM,KAAK,KAAK,CAAE,CAAC,EAAC;MAC/Bf,GAAG,CAACS,IAAI,CAAC,IAAI,GAAIM,KAAK,GAAG,IAAK,CAAC,EAAC;IAClC,CAAC,MAAM,IAAIA,KAAK,IAAI,MAAM,IAAKA,KAAK,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAO,EAAE;MAClE;MACAf,GAAG,CAACS,IAAI,CAAC,IAAI,GAAIM,KAAK,KAAK,EAAG,CAAC,EAAC;MAChCf,GAAG,CAACS,IAAI,CAAC,IAAI,GAAKM,KAAK,KAAK,CAAC,GAAI,IAAK,CAAC,EAAC;MACxCf,GAAG,CAACS,IAAI,CAAC,IAAI,GAAIM,KAAK,GAAG,IAAK,CAAC,EAAC;IAClC,CAAC,MAAM,IAAIA,KAAK,IAAI,QAAQ,IAAIA,KAAK,IAAI,QAAQ,EAAE;MACjD;MACAP,CAAC,EAAE;MACHR,GAAG,CAACS,IAAI,CAAE,IAAI,GAAIM,KAAK,KAAK,EAAE,GAAI,IAAK,CAAC,EAAC;MACzCf,GAAG,CAACS,IAAI,CAAE,IAAI,GAAKM,KAAK,KAAK,EAAE,GAAI,IAAM,CAAC,EAAC;MAC3Cf,GAAG,CAACS,IAAI,CAAE,IAAI,GAAKM,KAAK,KAAK,CAAC,GAAI,IAAM,CAAC,EAAC;MAC1Cf,GAAG,CAACS,IAAI,CAAE,IAAI,GAAIM,KAAK,GAAG,IAAM,CAAC,EAAC;IACpC,CAAC,MAAM;MACL;MACAf,GAAG,CAACS,IAAI,CAACM,KAAK,CAAC;MACf,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;IAC3C;EACF;EAEA,OAAOjB,GAAG;AACZ;AAEAkB,MAAM,CAACC,OAAO,GAAG,UAAUzB,KAAK,EAAE0B,OAAO,EAAE;EACzC1B,KAAK,GAAG,OAAOA,KAAK,KAAK,QAAQ,GAAGkB,WAAW,CAAClB,KAAK,CAAC,GAAGG,KAAK,CAACwB,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC7B,KAAK,CAAC;EAE1F,IAAI0B,OAAO,EAAE;IACX,MAAMI,IAAI,GAAGJ,OAAO,CAACI,IAAI,IAAI,MAAM;IACnC,IAAIA,IAAI,KAAK,MAAM,EAAE,MAAM,IAAIP,KAAK,CAAC,cAAc,CAAC;IAEpD,IAAIQ,GAAG,GAAGL,OAAO,CAACK,GAAG;IACrB,IAAI,CAACA,GAAG,EAAE,MAAM,IAAIR,KAAK,CAAC,aAAa,CAAC;IAExCQ,GAAG,GAAG,OAAOA,GAAG,KAAK,QAAQ,GAAGrB,UAAU,CAACqB,GAAG,CAAC,GAAG5B,KAAK,CAACwB,SAAS,CAACC,KAAK,CAACC,IAAI,CAACE,GAAG,CAAC;IACjF,OAAO1B,UAAU,CAACR,IAAI,CAACG,KAAK,EAAE+B,GAAG,CAAC,CAAC;EACrC;EAEA,OAAO1B,UAAU,CAACT,GAAG,CAACI,KAAK,CAAC,CAAC;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}