{"ast": null, "code": "import { defineComponent, inject, computed, onMounted, watch, onBeforeUnmount, openBlock, createBlock, unref, mergeProps, withCtx, renderSlot, createCommentVNode } from 'vue';\nimport { isNil } from 'lodash-unified';\nimport { unrefElement } from '@vueuse/core';\nimport { POPPER_INJECTION_KEY } from './constants.mjs';\nimport { popperTriggerProps } from './trigger.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useForwardRef } from '../../../hooks/use-forward-ref/index.mjs';\nimport { isFocusable } from '../../../utils/dom/aria.mjs';\nimport { OnlyChild } from '../../slot/src/only-child.mjs';\nimport { isElement } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPopperTrigger\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: popperTriggerProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const {\n      role,\n      triggerRef\n    } = inject(POPPER_INJECTION_KEY, void 0);\n    useForwardRef(triggerRef);\n    const ariaControls = computed(() => {\n      return ariaHaspopup.value ? props.id : void 0;\n    });\n    const ariaDescribedby = computed(() => {\n      if (role && role.value === \"tooltip\") {\n        return props.open && props.id ? props.id : void 0;\n      }\n      return void 0;\n    });\n    const ariaHaspopup = computed(() => {\n      if (role && role.value !== \"tooltip\") {\n        return role.value;\n      }\n      return void 0;\n    });\n    const ariaExpanded = computed(() => {\n      return ariaHaspopup.value ? `${props.open}` : void 0;\n    });\n    let virtualTriggerAriaStopWatch = void 0;\n    const TRIGGER_ELE_EVENTS = [\"onMouseenter\", \"onMouseleave\", \"onClick\", \"onKeydown\", \"onFocus\", \"onBlur\", \"onContextmenu\"];\n    onMounted(() => {\n      watch(() => props.virtualRef, virtualEl => {\n        if (virtualEl) {\n          triggerRef.value = unrefElement(virtualEl);\n        }\n      }, {\n        immediate: true\n      });\n      watch(triggerRef, (el, prevEl) => {\n        virtualTriggerAriaStopWatch == null ? void 0 : virtualTriggerAriaStopWatch();\n        virtualTriggerAriaStopWatch = void 0;\n        if (isElement(el)) {\n          TRIGGER_ELE_EVENTS.forEach(eventName => {\n            var _a;\n            const handler = props[eventName];\n            if (handler) {\n              el.addEventListener(eventName.slice(2).toLowerCase(), handler);\n              (_a = prevEl == null ? void 0 : prevEl.removeEventListener) == null ? void 0 : _a.call(prevEl, eventName.slice(2).toLowerCase(), handler);\n            }\n          });\n          if (isFocusable(el)) {\n            virtualTriggerAriaStopWatch = watch([ariaControls, ariaDescribedby, ariaHaspopup, ariaExpanded], watches => {\n              [\"aria-controls\", \"aria-describedby\", \"aria-haspopup\", \"aria-expanded\"].forEach((key, idx) => {\n                isNil(watches[idx]) ? el.removeAttribute(key) : el.setAttribute(key, watches[idx]);\n              });\n            }, {\n              immediate: true\n            });\n          }\n        }\n        if (isElement(prevEl) && isFocusable(prevEl)) {\n          [\"aria-controls\", \"aria-describedby\", \"aria-haspopup\", \"aria-expanded\"].forEach(key => prevEl.removeAttribute(key));\n        }\n      }, {\n        immediate: true\n      });\n    });\n    onBeforeUnmount(() => {\n      virtualTriggerAriaStopWatch == null ? void 0 : virtualTriggerAriaStopWatch();\n      virtualTriggerAriaStopWatch = void 0;\n      if (triggerRef.value && isElement(triggerRef.value)) {\n        const el = triggerRef.value;\n        TRIGGER_ELE_EVENTS.forEach(eventName => {\n          const handler = props[eventName];\n          if (handler) {\n            el.removeEventListener(eventName.slice(2).toLowerCase(), handler);\n          }\n        });\n        triggerRef.value = void 0;\n      }\n    });\n    expose({\n      triggerRef\n    });\n    return (_ctx, _cache) => {\n      return !_ctx.virtualTriggering ? (openBlock(), createBlock(unref(OnlyChild), mergeProps({\n        key: 0\n      }, _ctx.$attrs, {\n        \"aria-controls\": unref(ariaControls),\n        \"aria-describedby\": unref(ariaDescribedby),\n        \"aria-expanded\": unref(ariaExpanded),\n        \"aria-haspopup\": unref(ariaHaspopup)\n      }), {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 16, [\"aria-controls\", \"aria-describedby\", \"aria-expanded\", \"aria-haspopup\"])) : createCommentVNode(\"v-if\", true);\n    };\n  }\n});\nvar ElPopperTrigger = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"trigger.vue\"]]);\nexport { ElPopperTrigger as default };", "map": {"version": 3, "names": ["name", "inheritAttrs", "role", "triggerRef", "inject", "POPPER_INJECTION_KEY", "useForwardRef", "ariaControls", "computed", "aria<PERSON><PERSON><PERSON><PERSON>", "value", "props", "id", "aria<PERSON><PERSON><PERSON><PERSON>", "open", "ariaExpanded", "virtualTriggerAriaStopWatch", "TRIGGER_ELE_EVENTS", "onMounted", "watch", "virtualRef", "virtualEl", "unrefElement", "immediate", "el", "prevEl", "isElement", "for<PERSON>ach", "eventName", "_a", "handler", "addEventListener", "slice", "toLowerCase", "removeEventListener", "call", "isFocusable", "watches", "key", "idx", "isNil", "removeAttribute", "setAttribute", "onBeforeUnmount", "expose", "_ctx", "_cache", "virtualTriggering", "openBlock", "createBlock", "unref", "<PERSON><PERSON><PERSON><PERSON>", "mergeProps", "$attrs", "default", "withCtx", "renderSlot", "$slots", "_", "createCommentVNode", "ElPopperTrigger", "_export_sfc", "_sfc_main"], "sources": ["../../../../../../packages/components/popper/src/trigger.vue"], "sourcesContent": ["<template>\n  <el-only-child\n    v-if=\"!virtualTriggering\"\n    v-bind=\"$attrs\"\n    :aria-controls=\"ariaControls\"\n    :aria-describedby=\"ariaDescribedby\"\n    :aria-expanded=\"ariaExpanded\"\n    :aria-haspopup=\"ariaHaspopup\"\n  >\n    <slot />\n  </el-only-child>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, onBeforeUnmount, onMounted, watch } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { unrefElement } from '@vueuse/core'\nimport { ElOnlyChild } from '@element-plus/components/slot'\nimport { useForwardRef } from '@element-plus/hooks'\nimport { isElement, isFocusable } from '@element-plus/utils'\nimport { POPPER_INJECTION_KEY } from './constants'\nimport { popperTriggerProps } from './trigger'\n\nimport type { WatchStopHandle } from 'vue'\n\ndefineOptions({\n  name: 'ElPopperTrigger',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(popperTriggerProps)\n\nconst { role, triggerRef } = inject(POPPER_INJECTION_KEY, undefined)!\n\nuseForwardRef(triggerRef)\n\nconst ariaControls = computed<string | undefined>(() => {\n  return ariaHaspopup.value ? props.id : undefined\n})\n\nconst ariaDescribedby = computed<string | undefined>(() => {\n  if (role && role.value === 'tooltip') {\n    return props.open && props.id ? props.id : undefined\n  }\n  return undefined\n})\n\nconst ariaHaspopup = computed<string | undefined>(() => {\n  if (role && role.value !== 'tooltip') {\n    return role.value\n  }\n  return undefined\n})\n\nconst ariaExpanded = computed<string | undefined>(() => {\n  return ariaHaspopup.value ? `${props.open}` : undefined\n})\n\nlet virtualTriggerAriaStopWatch: WatchStopHandle | undefined = undefined\n\nconst TRIGGER_ELE_EVENTS = [\n  'onMouseenter',\n  'onMouseleave',\n  'onClick',\n  'onKeydown',\n  'onFocus',\n  'onBlur',\n  'onContextmenu',\n] as const\n\nonMounted(() => {\n  watch(\n    () => props.virtualRef,\n    (virtualEl) => {\n      if (virtualEl) {\n        triggerRef.value = unrefElement(virtualEl as HTMLElement)\n      }\n    },\n    {\n      immediate: true,\n    }\n  )\n\n  watch(\n    triggerRef,\n    (el, prevEl) => {\n      virtualTriggerAriaStopWatch?.()\n      virtualTriggerAriaStopWatch = undefined\n      if (isElement(el)) {\n        TRIGGER_ELE_EVENTS.forEach((eventName) => {\n          const handler = props[eventName]\n          if (handler) {\n            ;(el as HTMLElement).addEventListener(\n              eventName.slice(2).toLowerCase(),\n              handler\n            )\n            ;(prevEl as HTMLElement)?.removeEventListener?.(\n              eventName.slice(2).toLowerCase(),\n              handler\n            )\n          }\n        })\n        if (isFocusable(el as HTMLElement)) {\n          virtualTriggerAriaStopWatch = watch(\n            [ariaControls, ariaDescribedby, ariaHaspopup, ariaExpanded],\n            (watches) => {\n              ;[\n                'aria-controls',\n                'aria-describedby',\n                'aria-haspopup',\n                'aria-expanded',\n              ].forEach((key, idx) => {\n                isNil(watches[idx])\n                  ? el.removeAttribute(key)\n                  : el.setAttribute(key, watches[idx]!)\n              })\n            },\n            { immediate: true }\n          )\n        }\n      }\n      if (isElement(prevEl) && isFocusable(prevEl as HTMLElement)) {\n        ;[\n          'aria-controls',\n          'aria-describedby',\n          'aria-haspopup',\n          'aria-expanded',\n        ].forEach((key) => prevEl.removeAttribute(key))\n      }\n    },\n    {\n      immediate: true,\n    }\n  )\n})\n\nonBeforeUnmount(() => {\n  virtualTriggerAriaStopWatch?.()\n  virtualTriggerAriaStopWatch = undefined\n  if (triggerRef.value && isElement(triggerRef.value)) {\n    const el = triggerRef.value as HTMLElement\n    TRIGGER_ELE_EVENTS.forEach((eventName) => {\n      const handler = props[eventName]\n      if (handler) {\n        el.removeEventListener(eventName.slice(2).toLowerCase(), handler)\n      }\n    })\n    triggerRef.value = undefined\n  }\n})\n\ndefineExpose({\n  /**\n   * @description trigger element\n   */\n  triggerRef,\n})\n</script>\n"], "mappings": ";;;;;;;;;;mCAyBc;EACZA,IAAM;EACNC,YAAc;AAChB;;;;;;;;IAIA,MAAM;MAAEC,IAAM;MAAAC;IAAA,CAAe,GAAAC,MAAA,CAAOC,oBAAA,EAAsB,KAAS;IAEnEC,aAAA,CAAcH,UAAU;IAElB,MAAAI,YAAA,GAAeC,QAAA,CAA6B,MAAM;MAC/C,OAAAC,YAAA,CAAaC,KAAQ,GAAAC,KAAA,CAAMC,EAAK;IAAA,CACxC;IAEK,MAAAC,eAAA,GAAkBL,QAAA,CAA6B,MAAM;MACrD,IAAAN,IAAA,IAAQA,IAAK,CAAAQ,KAAA,KAAU,SAAW;QACpC,OAAOC,KAAM,CAAAG,IAAA,IAAQH,KAAM,CAAAC,EAAA,GAAKD,KAAA,CAAMC,EAAK;MAAA;MAEtC;IAAA,CACR;IAEK,MAAAH,YAAA,GAAeD,QAAA,CAA6B,MAAM;MAClD,IAAAN,IAAA,IAAQA,IAAK,CAAAQ,KAAA,KAAU,SAAW;QACpC,OAAOR,IAAK,CAAAQ,KAAA;MAAA;MAEP;IAAA,CACR;IAEK,MAAAK,YAAA,GAAeP,QAAA,CAA6B,MAAM;MACtD,OAAOC,YAAa,CAAAC,KAAA,GAAQ,GAAGC,KAAA,CAAMG,IAAI,EAAK;IAAA,CAC/C;IAED,IAAIE,2BAA2D;IAE/D,MAAMC,kBAAqB,IACzB,gBACA,gBACA,WACA,aACA,WACA,UACA,gBACF;IAEAC,SAAA,CAAU,MAAM;MACdC,KAAA,OAAAR,KAAA,CAAAS,UAAA,EAAAC,SAAA;QACE,IAAAA,SAAY;UACGlB,UAAA,CAAAO,KAAA,GAAAY,YAAA,CAAAD,SAAA;QACb;MACE,CAAW;QACbE,SAAA;MAAA,CACF;MACAJ,KAAA,CAAAhB,UAAA,GAAAqB,EAAA,EAAAC,MAAA;QAAAT,2BACa,oBAAAA,2BAAA;QACbA,2BAAA;QACF,IAAAU,SAAA,CAAAF,EAAA;UAEAP,kBAAA,CAAAU,OAAA,CAAAC,SAAA;YACE,IAAAC,EAAA;YAAA,MACgBC,OAAA,GAAAnB,KAAA,CAAAiB,SAAA;YACgB,IAAAE,OAAA;cAE1BN,EAAA,CAAAO,gBAAe,CAAAH,SAAA,CAAAI,KAAA,IAAAC,WAAA,IAAAH,OAAA;cACE,CAAAD,EAAA,GAAAJ,MAAA,gBAAuB,IAAAA,MAAA,CAAAS,mBAAA,qBAAAL,EAAA,CAAAM,IAAA,CAAAV,MAAA,EAAAG,SAAA,CAAAI,KAAA,IAAAC,WAAA,IAAAH,OAAA;YACxC;UACA;UACE,IAAAM,WAAA,CAAAZ,EAAA;YAACR,2BAAoB,GAAAG,KAAA,EAAAZ,YAAA,EAAAM,eAAA,EAAAJ,YAAA,EAAAM,YAAA,GAAAsB,OAAA;cAEnB,CACF,iBACE,kBAAwB,iBACd,EACV,gBACF,CAAAV,OAAA,EAAAW,GAAA,EAAAC,GAAA;gBACFC,KAAA,CAAAH,OAAA,CAAAE,GAAA,KAAAf,EAAA,CAAAiB,eAAA,CAAAH,GAAA,IAAAd,EAAA,CAAAkB,YAAA,CAAAJ,GAAA,EAAAD,OAAA,CAAAE,GAAA;cAAA,CACD;YACD,CAAI;cAAAhB,SAAA,EAAY;YAAoB;UAClC;QAA8B;QAC8B,IAAAG,SAC7C,CAAAD,MAAA,KAAAW,WAAA,CAAAX,MAAA;UACV,CACC,iBACA,oBACA,iBACA,iBAAAE,OACA,CAAAW,GAAA,IAASb,MAAa,CAAAgB,eAAA,CAAAH,GAAA;QACtB;MAEsC;QAE1Cf,SAAA;MAAA,CACA;IAAkB,CACpB;IACFoB,eAAA;MACF3B,2BAAA,oBAAAA,2BAAA;MACAA,2BAAyB;MACvB,IAAAb,UAAA,CAAAO,KAAA,IAAAgB,SAAA,CAAAvB,UAAA,CAAAO,KAAA;QAAC,MAAAc,EAAA,GAAArB,UAAA,CAAAO,KAAA;QACCO,kBAAA,CAAAU,OAAA,CAAAC,SAAA;UACA,MAAAE,OAAA,GAAAnB,KAAA,CAAAiB,SAAA;UACA,IAAAE,OAAA;YACAN,EAAA,CAAAU,mBAAA,CAAAN,SAAA,CAAAI,KAAA,IAAAC,WAAA,IAAAH,OAAA;UAAA;QAC4C,CAChD;QACF3B,UAAA,CAAAO,KAAA;MAAA;IACA;IAEAkC,MAAA;MACFzC;IAAA,CACD;IAED,QAAA0C,IAAA,EAAAC,MAAsB;MACU,QAAAD,IAAA,CAAAE,iBAAA,IAAAC,SAAA,IAAAC,WAAA,CAAAC,KAAA,CAAAC,SAAA,GAAAC,UAAA;QAAAd,GAAA;MAAA,GAAAO,IAAA,CAAAQ,MAAA;QACA,iBAAAH,KAAA,CAAA3C,YAAA;QAC9B,kBAAe,EAAA2C,KAAmB,CAAArC,eAAA,CAAW;QAC3C,eAAsB,EAAAqC,KAAA,CAAAnC,YAAA;QACH,iBAAAmC,KAAA,CAAAzC,YAAuB;MACxC,CAAM;QACN6C,OAAa,EAAAC,OAAA,QACXC,UAAA,CAAAX,IAAA,CAAAY,MAAA,EAAuB,SAAU,EACnC;QACFC,CAAC;MACD,uBAAmB,4DAAAC,kBAAA;IAAA,CACrB;EAAA;AAGF,CAAa;AAAA,IAAAC,eAAA,kBAAAC,WAAA,CAAAC,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}