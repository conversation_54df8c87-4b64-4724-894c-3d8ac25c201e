{"ast": null, "code": "import { ref, computed, onMounted } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRoute } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { ArrowLeft } from \"@element-plus/icons-vue\";\nexport default {\n  name: \"ReservationDetail\",\n  setup() {\n    const store = useStore();\n    const route = useRoute();\n    const loading = ref(true);\n    const processing = ref(false);\n    const cancelDialogVisible = ref(false);\n\n    // 获取预约详情\n    const getReservationDetail = async () => {\n      try {\n        loading.value = true;\n        const reservationId = route.params.id;\n        if (!reservationId) {\n          ElMessage.error(\"预约ID不能为空\");\n          return;\n        }\n        await store.dispatch(\"seat/getReservationById\", reservationId);\n      } catch (error) {\n        ElMessage.error(\"获取预约详情失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 预约信息\n    const reservation = computed(() => {\n      return store.getters[\"seat/currentReservation\"];\n    });\n\n    // 获取预约状态类型\n    const getReservationStatusType = status => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getReservationStatusText = status => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化日期时间\n    const formatDateTime = dateTimeString => {\n      if (!dateTimeString) return \"\";\n      const date = new Date(dateTimeString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 计算时长\n    const calculateDuration = (startTime, endTime) => {\n      if (!startTime || !endTime) return \"\";\n      const start = new Date(startTime);\n      const end = new Date(endTime);\n      const diffMs = end - start;\n      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffMins = Math.floor(diffMs % (1000 * 60 * 60) / (1000 * 60));\n      return `${diffHrs}小时${diffMins}分钟`;\n    };\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n\n    // 处理签到\n    const handleCheckIn = async () => {\n      try {\n        processing.value = true;\n        await store.dispatch(\"seat/checkIn\", {\n          reservationId: reservation.value.id\n        });\n        ElMessage.success(\"签到成功\");\n        getReservationDetail();\n      } catch (error) {\n        ElMessage.error(error.message || \"签到失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 处理签退\n    const handleCheckOut = async () => {\n      try {\n        processing.value = true;\n        await store.dispatch(\"seat/checkOut\", {\n          reservationId: reservation.value.id\n        });\n        ElMessage.success(\"签退成功\");\n        getReservationDetail();\n      } catch (error) {\n        ElMessage.error(error.message || \"签退失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 处理取消预约\n    const handleCancel = () => {\n      cancelDialogVisible.value = true;\n    };\n\n    // 确认取消预约\n    const confirmCancel = async () => {\n      try {\n        processing.value = true;\n        await store.dispatch(\"seat/cancelReservation\", {\n          reservationId: reservation.value.id\n        });\n        ElMessage.success(\"预约已取消\");\n        cancelDialogVisible.value = false;\n        getReservationDetail();\n      } catch (error) {\n        ElMessage.error(error.message || \"取消预约失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n    onMounted(() => {\n      getReservationDetail();\n    });\n    return {\n      loading,\n      processing,\n      reservation,\n      cancelDialogVisible,\n      getReservationStatusType,\n      getReservationStatusText,\n      formatDateTime,\n      calculateDuration,\n      handleCheckIn,\n      handleCheckOut,\n      handleCancel,\n      confirmCancel,\n      ArrowLeft\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "useStore", "useRoute", "ElMessage", "ArrowLeft", "name", "setup", "store", "route", "loading", "processing", "cancelDialogVisible", "getReservationDetail", "value", "reservationId", "params", "id", "error", "dispatch", "reservation", "getters", "getReservationStatusType", "status", "getReservationStatusText", "formatDateTime", "dateTimeString", "date", "Date", "getFullYear", "padZero", "getMonth", "getDate", "getHours", "getMinutes", "calculateDuration", "startTime", "endTime", "start", "end", "diffMs", "diffHrs", "Math", "floor", "diffMins", "num", "handleCheckIn", "success", "message", "handleCheckOut", "handleCancel", "confirmCancel"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\ReservationDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"reservation-detail\">\n    <div class=\"page-header\">\n      <div class=\"header-left\">\n        <el-button @click=\"$router.back()\" :icon=\"ArrowLeft\">返回</el-button>\n        <h2>预约详情</h2>\n      </div>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"10\" animated />\n    </div>\n\n    <div v-else-if=\"!reservation\" class=\"empty-container\">\n      <el-empty description=\"未找到预约信息\" />\n    </div>\n\n    <div v-else class=\"detail-content\">\n      <el-card shadow=\"hover\" class=\"detail-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <h3>基本信息</h3>\n            <el-tag :type=\"getReservationStatusType(reservation.status)\">\n              {{ getReservationStatusText(reservation.status) }}\n            </el-tag>\n          </div>\n        </template>\n\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"预约号\">\n            {{ reservation.id }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"创建时间\">\n            {{ formatDateTime(reservation.created_at) }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"预约时间\" :span=\"2\">\n            {{ formatDateTime(reservation.start_time) }} 至\n            {{ formatDateTime(reservation.end_time) }}\n            <span class=\"duration\">\n              ({{ calculateDuration(reservation.start_time, reservation.end_time) }})\n            </span>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"签到时间\" v-if=\"reservation.check_in_time\">\n            {{ formatDateTime(reservation.check_in_time) }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"签退时间\" v-if=\"reservation.check_out_time\">\n            {{ formatDateTime(reservation.check_out_time) }}\n          </el-descriptions-item>\n          <el-descriptions-item\n            label=\"取消原因\"\n            v-if=\"reservation.status === 'cancelled' && reservation.cancel_reason\"\n            :span=\"2\"\n          >\n            {{ reservation.cancel_reason }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n\n      <el-card shadow=\"hover\" class=\"detail-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <h3>座位信息</h3>\n          </div>\n        </template>\n\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"自习室\">\n            {{ reservation.seat?.room?.name }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"位置\">\n            {{ reservation.seat?.room?.location }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"座位号\">\n            {{ reservation.seat?.seat_number }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"座位类型\">\n            <el-tag\n              v-if=\"reservation.seat?.is_window_seat\"\n              size=\"small\"\n              effect=\"plain\"\n              style=\"margin-right: 5px\"\n              >靠窗</el-tag\n            >\n            <el-tag v-if=\"reservation.seat?.is_power_outlet\" size=\"small\" effect=\"plain\"\n              >有电源</el-tag\n            >\n          </el-descriptions-item>\n          <el-descriptions-item label=\"座位描述\" :span=\"2\">\n            {{ reservation.seat?.description || \"无\" }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n\n      <div class=\"action-buttons\">\n        <template v-if=\"reservation.status === 'pending'\">\n          <el-button type=\"primary\" @click=\"handleCheckIn\">签到</el-button>\n          <el-button type=\"danger\" @click=\"handleCancel\">取消预约</el-button>\n        </template>\n\n        <template v-else-if=\"reservation.status === 'checked_in'\">\n          <el-button type=\"success\" @click=\"handleCheckOut\">签退</el-button>\n        </template>\n      </div>\n    </div>\n\n    <!-- 取消预约对话框 -->\n    <el-dialog v-model=\"cancelDialogVisible\" title=\"取消预约\" width=\"400px\">\n      <div class=\"cancel-dialog\">\n        <p>您确定要取消此预约吗？</p>\n        <p>座位号: {{ reservation?.seat?.seat_number }}</p>\n        <p>\n          预约时间: {{ formatDateTime(reservation?.start_time) }} -\n          {{ formatDateTime(reservation?.end_time) }}\n        </p>\n\n        <div class=\"cancel-warning\">\n          <el-alert\n            title=\"取消预约提示\"\n            type=\"warning\"\n            description=\"距离预约开始时间不足30分钟取消，可能会影响您的信誉分。\"\n            show-icon\n            :closable=\"false\"\n          />\n        </div>\n\n        <div class=\"dialog-footer\">\n          <el-button @click=\"cancelDialogVisible = false\">返回</el-button>\n          <el-button type=\"danger\" @click=\"confirmCancel\" :loading=\"processing\">确认取消</el-button>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRoute } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { ArrowLeft } from \"@element-plus/icons-vue\";\n\nexport default {\n  name: \"ReservationDetail\",\n  setup() {\n    const store = useStore();\n    const route = useRoute();\n\n    const loading = ref(true);\n    const processing = ref(false);\n    const cancelDialogVisible = ref(false);\n\n    // 获取预约详情\n    const getReservationDetail = async () => {\n      try {\n        loading.value = true;\n        const reservationId = route.params.id;\n\n        if (!reservationId) {\n          ElMessage.error(\"预约ID不能为空\");\n          return;\n        }\n\n        await store.dispatch(\"seat/getReservationById\", reservationId);\n      } catch (error) {\n        ElMessage.error(\"获取预约详情失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 预约信息\n    const reservation = computed(() => {\n      return store.getters[\"seat/currentReservation\"];\n    });\n\n    // 获取预约状态类型\n    const getReservationStatusType = status => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getReservationStatusText = status => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化日期时间\n    const formatDateTime = dateTimeString => {\n      if (!dateTimeString) return \"\";\n\n      const date = new Date(dateTimeString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(\n        date.getDate()\n      )} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 计算时长\n    const calculateDuration = (startTime, endTime) => {\n      if (!startTime || !endTime) return \"\";\n\n      const start = new Date(startTime);\n      const end = new Date(endTime);\n      const diffMs = end - start;\n      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\n\n      return `${diffHrs}小时${diffMins}分钟`;\n    };\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n\n    // 处理签到\n    const handleCheckIn = async () => {\n      try {\n        processing.value = true;\n\n        await store.dispatch(\"seat/checkIn\", {\n          reservationId: reservation.value.id,\n        });\n\n        ElMessage.success(\"签到成功\");\n        getReservationDetail();\n      } catch (error) {\n        ElMessage.error(error.message || \"签到失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 处理签退\n    const handleCheckOut = async () => {\n      try {\n        processing.value = true;\n\n        await store.dispatch(\"seat/checkOut\", {\n          reservationId: reservation.value.id,\n        });\n\n        ElMessage.success(\"签退成功\");\n        getReservationDetail();\n      } catch (error) {\n        ElMessage.error(error.message || \"签退失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 处理取消预约\n    const handleCancel = () => {\n      cancelDialogVisible.value = true;\n    };\n\n    // 确认取消预约\n    const confirmCancel = async () => {\n      try {\n        processing.value = true;\n\n        await store.dispatch(\"seat/cancelReservation\", {\n          reservationId: reservation.value.id,\n        });\n\n        ElMessage.success(\"预约已取消\");\n        cancelDialogVisible.value = false;\n        getReservationDetail();\n      } catch (error) {\n        ElMessage.error(error.message || \"取消预约失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    onMounted(() => {\n      getReservationDetail();\n    });\n\n    return {\n      loading,\n      processing,\n      reservation,\n      cancelDialogVisible,\n      getReservationStatusType,\n      getReservationStatusText,\n      formatDateTime,\n      calculateDuration,\n      handleCheckIn,\n      handleCheckOut,\n      handleCancel,\n      confirmCancel,\n      ArrowLeft,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.reservation-detail {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  .header-left {\n    display: flex;\n    align-items: center;\n    gap: 10px;\n\n    h2 {\n      margin: 0;\n    }\n  }\n}\n\n.loading-container,\n.empty-container {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.detail-content {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.detail-card {\n  margin-bottom: 20px;\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    h3 {\n      margin: 0;\n    }\n  }\n}\n\n.duration {\n  color: #909399;\n  margin-left: 10px;\n  font-size: 14px;\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  margin-top: 30px;\n}\n\n.cancel-dialog {\n  p {\n    margin: 10px 0;\n  }\n\n  .cancel-warning {\n    margin: 20px 0;\n  }\n\n  .dialog-footer {\n    margin-top: 20px;\n    display: flex;\n    justify-content: flex-end;\n    gap: 10px;\n  }\n}\n</style>\n"], "mappings": "AAuIA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAK;AAC9C,SAASC,QAAO,QAAS,MAAM;AAC/B,SAASC,QAAO,QAAS,YAAY;AACrC,SAASC,SAAQ,QAAS,cAAc;AACxC,SAASC,SAAQ,QAAS,yBAAyB;AAEnD,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIN,QAAQ,CAAC,CAAC;IACxB,MAAMO,KAAI,GAAIN,QAAQ,CAAC,CAAC;IAExB,MAAMO,OAAM,GAAIX,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMY,UAAS,GAAIZ,GAAG,CAAC,KAAK,CAAC;IAC7B,MAAMa,mBAAkB,GAAIb,GAAG,CAAC,KAAK,CAAC;;IAEtC;IACA,MAAMc,oBAAmB,GAAI,MAAAA,CAAA,KAAY;MACvC,IAAI;QACFH,OAAO,CAACI,KAAI,GAAI,IAAI;QACpB,MAAMC,aAAY,GAAIN,KAAK,CAACO,MAAM,CAACC,EAAE;QAErC,IAAI,CAACF,aAAa,EAAE;UAClBX,SAAS,CAACc,KAAK,CAAC,UAAU,CAAC;UAC3B;QACF;QAEA,MAAMV,KAAK,CAACW,QAAQ,CAAC,yBAAyB,EAAEJ,aAAa,CAAC;MAChE,EAAE,OAAOG,KAAK,EAAE;QACdd,SAAS,CAACc,KAAK,CAAC,UAAU,CAAC;MAC7B,UAAU;QACRR,OAAO,CAACI,KAAI,GAAI,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAMM,WAAU,GAAIpB,QAAQ,CAAC,MAAM;MACjC,OAAOQ,KAAK,CAACa,OAAO,CAAC,yBAAyB,CAAC;IACjD,CAAC,CAAC;;IAEF;IACA,MAAMC,wBAAuB,GAAIC,MAAK,IAAK;MACzC,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ,OAAO,SAAS;QAClB,KAAK,YAAY;UACf,OAAO,SAAS;QAClB,KAAK,WAAW;UACd,OAAO,MAAM;QACf,KAAK,WAAW;UACd,OAAO,QAAQ;QACjB,KAAK,SAAS;UACZ,OAAO,QAAQ;QACjB;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAMC,wBAAuB,GAAID,MAAK,IAAK;MACzC,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ,OAAO,KAAK;QACd,KAAK,YAAY;UACf,OAAO,KAAK;QACd,KAAK,WAAW;UACd,OAAO,KAAK;QACd,KAAK,WAAW;UACd,OAAO,KAAK;QACd,KAAK,SAAS;UACZ,OAAO,KAAK;QACd;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAME,cAAa,GAAIC,cAAa,IAAK;MACvC,IAAI,CAACA,cAAc,EAAE,OAAO,EAAE;MAE9B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,cAAc,CAAC;MACrC,OAAO,GAAGC,IAAI,CAACE,WAAW,CAAC,CAAC,IAAIC,OAAO,CAACH,IAAI,CAACI,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAID,OAAO,CACrEH,IAAI,CAACK,OAAO,CAAC,CACf,CAAC,IAAIF,OAAO,CAACH,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC,IAAIH,OAAO,CAACH,IAAI,CAACO,UAAU,CAAC,CAAC,CAAC,EAAE;IAC/D,CAAC;;IAED;IACA,MAAMC,iBAAgB,GAAIA,CAACC,SAAS,EAAEC,OAAO,KAAK;MAChD,IAAI,CAACD,SAAQ,IAAK,CAACC,OAAO,EAAE,OAAO,EAAE;MAErC,MAAMC,KAAI,GAAI,IAAIV,IAAI,CAACQ,SAAS,CAAC;MACjC,MAAMG,GAAE,GAAI,IAAIX,IAAI,CAACS,OAAO,CAAC;MAC7B,MAAMG,MAAK,GAAID,GAAE,GAAID,KAAK;MAC1B,MAAMG,OAAM,GAAIC,IAAI,CAACC,KAAK,CAACH,MAAK,IAAK,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC,CAAC;MACrD,MAAMI,QAAO,GAAIF,IAAI,CAACC,KAAK,CAAEH,MAAK,IAAK,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC,IAAK,IAAG,GAAI,EAAE,CAAC,CAAC;MAEtE,OAAO,GAAGC,OAAO,KAAKG,QAAQ,IAAI;IACpC,CAAC;;IAED;IACA,SAASd,OAAOA,CAACe,GAAG,EAAE;MACpB,OAAOA,GAAE,GAAI,EAAC,GAAI,IAAIA,GAAG,EAAC,GAAIA,GAAG;IACnC;;IAEA;IACA,MAAMC,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFnC,UAAU,CAACG,KAAI,GAAI,IAAI;QAEvB,MAAMN,KAAK,CAACW,QAAQ,CAAC,cAAc,EAAE;UACnCJ,aAAa,EAAEK,WAAW,CAACN,KAAK,CAACG;QACnC,CAAC,CAAC;QAEFb,SAAS,CAAC2C,OAAO,CAAC,MAAM,CAAC;QACzBlC,oBAAoB,CAAC,CAAC;MACxB,EAAE,OAAOK,KAAK,EAAE;QACdd,SAAS,CAACc,KAAK,CAACA,KAAK,CAAC8B,OAAM,IAAK,MAAM,CAAC;MAC1C,UAAU;QACRrC,UAAU,CAACG,KAAI,GAAI,KAAK;MAC1B;IACF,CAAC;;IAED;IACA,MAAMmC,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFtC,UAAU,CAACG,KAAI,GAAI,IAAI;QAEvB,MAAMN,KAAK,CAACW,QAAQ,CAAC,eAAe,EAAE;UACpCJ,aAAa,EAAEK,WAAW,CAACN,KAAK,CAACG;QACnC,CAAC,CAAC;QAEFb,SAAS,CAAC2C,OAAO,CAAC,MAAM,CAAC;QACzBlC,oBAAoB,CAAC,CAAC;MACxB,EAAE,OAAOK,KAAK,EAAE;QACdd,SAAS,CAACc,KAAK,CAACA,KAAK,CAAC8B,OAAM,IAAK,MAAM,CAAC;MAC1C,UAAU;QACRrC,UAAU,CAACG,KAAI,GAAI,KAAK;MAC1B;IACF,CAAC;;IAED;IACA,MAAMoC,YAAW,GAAIA,CAAA,KAAM;MACzBtC,mBAAmB,CAACE,KAAI,GAAI,IAAI;IAClC,CAAC;;IAED;IACA,MAAMqC,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFxC,UAAU,CAACG,KAAI,GAAI,IAAI;QAEvB,MAAMN,KAAK,CAACW,QAAQ,CAAC,wBAAwB,EAAE;UAC7CJ,aAAa,EAAEK,WAAW,CAACN,KAAK,CAACG;QACnC,CAAC,CAAC;QAEFb,SAAS,CAAC2C,OAAO,CAAC,OAAO,CAAC;QAC1BnC,mBAAmB,CAACE,KAAI,GAAI,KAAK;QACjCD,oBAAoB,CAAC,CAAC;MACxB,EAAE,OAAOK,KAAK,EAAE;QACdd,SAAS,CAACc,KAAK,CAACA,KAAK,CAAC8B,OAAM,IAAK,QAAQ,CAAC;MAC5C,UAAU;QACRrC,UAAU,CAACG,KAAI,GAAI,KAAK;MAC1B;IACF,CAAC;IAEDb,SAAS,CAAC,MAAM;MACdY,oBAAoB,CAAC,CAAC;IACxB,CAAC,CAAC;IAEF,OAAO;MACLH,OAAO;MACPC,UAAU;MACVS,WAAW;MACXR,mBAAmB;MACnBU,wBAAwB;MACxBE,wBAAwB;MACxBC,cAAc;MACdU,iBAAiB;MACjBW,aAAa;MACbG,cAAc;MACdC,YAAY;MACZC,aAAa;MACb9C;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}