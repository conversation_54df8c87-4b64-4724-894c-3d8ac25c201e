{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isVNode, Fragment, Text, Comment, openBlock, createBlock, createCommentVNode } from 'vue';\nimport { isArray, hasOwn, camelize } from '@vue/shared';\nimport { debugWarn } from '../error.mjs';\nconst SCOPE = \"utils/vue/vnode\";\nvar PatchFlags = /* @__PURE__ */(PatchFlags2 => {\n  PatchFlags2[PatchFlags2[\"TEXT\"] = 1] = \"TEXT\";\n  PatchFlags2[PatchFlags2[\"CLASS\"] = 2] = \"CLASS\";\n  PatchFlags2[PatchFlags2[\"STYLE\"] = 4] = \"STYLE\";\n  PatchFlags2[PatchFlags2[\"PROPS\"] = 8] = \"PROPS\";\n  PatchFlags2[PatchFlags2[\"FULL_PROPS\"] = 16] = \"FULL_PROPS\";\n  PatchFlags2[PatchFlags2[\"HYDRATE_EVENTS\"] = 32] = \"HYDRATE_EVENTS\";\n  PatchFlags2[PatchFlags2[\"STABLE_FRAGMENT\"] = 64] = \"STABLE_FRAGMENT\";\n  PatchFlags2[PatchFlags2[\"KEYED_FRAGMENT\"] = 128] = \"KEYED_FRAGMENT\";\n  PatchFlags2[PatchFlags2[\"UNKEYED_FRAGMENT\"] = 256] = \"UNKEYED_FRAGMENT\";\n  PatchFlags2[PatchFlags2[\"NEED_PATCH\"] = 512] = \"NEED_PATCH\";\n  PatchFlags2[PatchFlags2[\"DYNAMIC_SLOTS\"] = 1024] = \"DYNAMIC_SLOTS\";\n  PatchFlags2[PatchFlags2[\"HOISTED\"] = -1] = \"HOISTED\";\n  PatchFlags2[PatchFlags2[\"BAIL\"] = -2] = \"BAIL\";\n  return PatchFlags2;\n})(PatchFlags || {});\nfunction isFragment(node) {\n  return isVNode(node) && node.type === Fragment;\n}\nfunction isText(node) {\n  return isVNode(node) && node.type === Text;\n}\nfunction isComment(node) {\n  return isVNode(node) && node.type === Comment;\n}\nconst TEMPLATE = \"template\";\nfunction isTemplate(node) {\n  return isVNode(node) && node.type === TEMPLATE;\n}\nfunction isValidElementNode(node) {\n  return isVNode(node) && !isFragment(node) && !isComment(node);\n}\nfunction getChildren(node, depth) {\n  if (isComment(node)) return;\n  if (isFragment(node) || isTemplate(node)) {\n    return depth > 0 ? getFirstValidNode(node.children, depth - 1) : void 0;\n  }\n  return node;\n}\nconst getFirstValidNode = (nodes, maxDepth = 3) => {\n  if (isArray(nodes)) {\n    return getChildren(nodes[0], maxDepth);\n  } else {\n    return getChildren(nodes, maxDepth);\n  }\n};\nfunction renderIf(condition, ...args) {\n  return condition ? renderBlock(...args) : createCommentVNode(\"v-if\", true);\n}\nfunction renderBlock(...args) {\n  return openBlock(), createBlock(...args);\n}\nconst getNormalizedProps = node => {\n  if (!isVNode(node)) {\n    debugWarn(SCOPE, \"[getNormalizedProps] must be a VNode\");\n    return {};\n  }\n  const raw = node.props || {};\n  const type = (isVNode(node.type) ? node.type.props : void 0) || {};\n  const props = {};\n  Object.keys(type).forEach(key => {\n    if (hasOwn(type[key], \"default\")) {\n      props[key] = type[key].default;\n    }\n  });\n  Object.keys(raw).forEach(key => {\n    props[camelize(key)] = raw[key];\n  });\n  return props;\n};\nconst ensureOnlyChild = children => {\n  if (!isArray(children) || children.length > 1) {\n    throw new Error(\"expect to receive a single Vue element child\");\n  }\n  return children[0];\n};\nconst flattedChildren = children => {\n  const vNodes = isArray(children) ? children : [children];\n  const result = [];\n  vNodes.forEach(child => {\n    var _a;\n    if (isArray(child)) {\n      result.push(...flattedChildren(child));\n    } else if (isVNode(child) && ((_a = child.component) == null ? void 0 : _a.subTree)) {\n      result.push(child, ...flattedChildren(child.component.subTree));\n    } else if (isVNode(child) && isArray(child.children)) {\n      result.push(...flattedChildren(child.children));\n    } else if (isVNode(child) && child.shapeFlag === 2) {\n      result.push(...flattedChildren(child.type()));\n    } else {\n      result.push(child);\n    }\n  });\n  return result;\n};\nexport { PatchFlags, ensureOnlyChild, flattedChildren, getFirstValidNode, getNormalizedProps, isComment, isFragment, isTemplate, isText, isValidElementNode, renderBlock, renderIf };", "map": {"version": 3, "names": ["SCOPE", "PatchFlags", "PatchFlags2", "isFragment", "node", "isVNode", "type", "Fragment", "isText", "Text", "isComment", "Comment", "TEMPLATE", "isTemplate", "isValidElementNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "depth", "getFirstValidNode", "children", "nodes", "max<PERSON><PERSON><PERSON>", "isArray", "renderIf", "condition", "args", "renderBlock", "createCommentVNode", "openBlock", "createBlock", "getNormalizedProps", "debugWarn", "raw", "props", "Object", "keys", "for<PERSON>ach", "key", "hasOwn", "default", "camelize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "Error", "flatted<PERSON><PERSON><PERSON><PERSON>", "vNodes", "result", "child", "_a", "push", "component", "subTree", "shapeFlag"], "sources": ["../../../../../packages/utils/vue/vnode.ts"], "sourcesContent": ["import {\n  Comment,\n  Fragment,\n  Text,\n  createBlock,\n  createCommentVNode,\n  isVNode,\n  openBlock,\n} from 'vue'\nimport { camelize } from '../strings'\nimport { isArray } from '../types'\nimport { hasOwn } from '../objects'\nimport { debugWarn } from '../error'\nimport type {\n  VNode,\n  VNodeArrayChildren,\n  VNodeChild,\n  VNodeNormalizedChildren,\n} from 'vue'\n\nconst SCOPE = 'utils/vue/vnode'\n\nexport enum PatchFlags {\n  TEXT = 1,\n  CLASS = 2,\n  STYLE = 4,\n  PROPS = 8,\n  FULL_PROPS = 16,\n  HYDRATE_EVENTS = 32,\n  STABLE_FRAGMENT = 64,\n  KEYED_FRAGMENT = 128,\n  UNKEYED_FRAGMENT = 256,\n  NEED_PATCH = 512,\n  DYNAMIC_SLOTS = 1024,\n  HOISTED = -1,\n  BAIL = -2,\n}\n\nexport type VNodeChildAtom = Exclude<VNodeChild, Array<any>>\nexport type RawSlots = Exclude<\n  VNodeNormalized<PERSON>hildren,\n  Array<any> | null | string\n>\n\nexport function isFragment(node: VNode): boolean\nexport function isFragment(node: unknown): node is VNode\nexport function isFragment(node: unknown): node is VNode {\n  return isVNode(node) && node.type === Fragment\n}\n\nexport function isText(node: VNode): boolean\nexport function isText(node: unknown): node is VNode\nexport function isText(node: unknown): node is VNode {\n  return isVNode(node) && node.type === Text\n}\n\nexport function isComment(node: VNode): boolean\nexport function isComment(node: unknown): node is VNode\nexport function isComment(node: unknown): node is VNode {\n  return isVNode(node) && node.type === Comment\n}\n\nconst TEMPLATE = 'template'\nexport function isTemplate(node: VNode): boolean\nexport function isTemplate(node: unknown): node is VNode\nexport function isTemplate(node: unknown): node is VNode {\n  return isVNode(node) && node.type === TEMPLATE\n}\n\n/**\n * determine if the element is a valid element type rather than fragments and comment e.g. <template> v-if\n * @param node {VNode} node to be tested\n */\nexport function isValidElementNode(node: VNode): boolean\nexport function isValidElementNode(node: unknown): node is VNode\nexport function isValidElementNode(node: unknown): node is VNode {\n  return isVNode(node) && !isFragment(node) && !isComment(node)\n}\n\n/**\n * get a valid child node (not fragment nor comment)\n * @param node {VNode} node to be searched\n * @param depth {number} depth to be searched\n */\nfunction getChildren(\n  node: VNodeNormalizedChildren | VNodeChild,\n  depth: number\n): VNodeNormalizedChildren | VNodeChild {\n  if (isComment(node)) return\n  if (isFragment(node) || isTemplate(node)) {\n    return depth > 0 ? getFirstValidNode(node.children, depth - 1) : undefined\n  }\n  return node\n}\n\nexport const getFirstValidNode = (\n  nodes: VNodeNormalizedChildren,\n  maxDepth = 3\n) => {\n  if (isArray(nodes)) {\n    return getChildren(nodes[0], maxDepth)\n  } else {\n    return getChildren(nodes, maxDepth)\n  }\n}\n\nexport function renderIf(\n  condition: boolean,\n  ...args: Parameters<typeof createBlock>\n) {\n  return condition ? renderBlock(...args) : createCommentVNode('v-if', true)\n}\n\nexport function renderBlock(...args: Parameters<typeof createBlock>) {\n  return openBlock(), createBlock(...args)\n}\n\nexport const getNormalizedProps = (node: VNode) => {\n  if (!isVNode(node)) {\n    debugWarn(SCOPE, '[getNormalizedProps] must be a VNode')\n    return {}\n  }\n\n  const raw = node.props || {}\n  const type = (isVNode(node.type) ? node.type.props : undefined) || {}\n  const props: Record<string, any> = {}\n\n  Object.keys(type).forEach((key) => {\n    if (hasOwn(type[key], 'default')) {\n      props[key] = type[key].default\n    }\n  })\n\n  Object.keys(raw).forEach((key) => {\n    props[camelize(key)] = raw[key]\n  })\n\n  return props\n}\n\nexport const ensureOnlyChild = (children: VNodeArrayChildren | undefined) => {\n  if (!isArray(children) || children.length > 1) {\n    throw new Error('expect to receive a single Vue element child')\n  }\n  return children[0]\n}\n\nexport type FlattenVNodes = Array<VNodeChildAtom | RawSlots>\n\nexport const flattedChildren = (\n  children: FlattenVNodes | VNode | VNodeNormalizedChildren\n): FlattenVNodes => {\n  const vNodes = isArray(children) ? children : [children]\n  const result: FlattenVNodes = []\n\n  vNodes.forEach((child) => {\n    if (isArray(child)) {\n      result.push(...flattedChildren(child))\n    } else if (isVNode(child) && child.component?.subTree) {\n      result.push(child, ...flattedChildren(child.component.subTree))\n    } else if (isVNode(child) && isArray(child.children)) {\n      result.push(...flattedChildren(child.children))\n    } else if (isVNode(child) && child.shapeFlag === 2) {\n      // @ts-ignore\n      result.push(...flattedChildren(child.type()))\n    } else {\n      result.push(child)\n    }\n  })\n  return result\n}\n"], "mappings": ";;;;;;AAaA,MAAMA,KAAK,GAAG,iBAAiB;AACrB,IAACC,UAAU,kBAAmB,CAAEC,WAAW,IAAK;EACxDA,WAAW,CAACA,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC7CA,WAAW,CAACA,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAC/CA,WAAW,CAACA,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAC/CA,WAAW,CAACA,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAC/CA,WAAW,CAACA,WAAW,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY;EAC1DA,WAAW,CAACA,WAAW,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC,GAAG,gBAAgB;EAClEA,WAAW,CAACA,WAAW,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC,GAAG,iBAAiB;EACpEA,WAAW,CAACA,WAAW,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACnEA,WAAW,CAACA,WAAW,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,kBAAkB;EACvEA,WAAW,CAACA,WAAW,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EAC3DA,WAAW,CAACA,WAAW,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,GAAG,eAAe;EAClEA,WAAW,CAACA,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS;EACpDA,WAAW,CAACA,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM;EAC9C,OAAOA,WAAW;AACpB,CAAC,EAAED,UAAU,IAAI,EAAE;AACZ,SAASE,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAOC,OAAO,CAACD,IAAI,CAAC,IAAIA,IAAI,CAACE,IAAI,KAAKC,QAAQ;AAChD;AACO,SAASC,MAAMA,CAACJ,IAAI,EAAE;EAC3B,OAAOC,OAAO,CAACD,IAAI,CAAC,IAAIA,IAAI,CAACE,IAAI,KAAKG,IAAI;AAC5C;AACO,SAASC,SAASA,CAACN,IAAI,EAAE;EAC9B,OAAOC,OAAO,CAACD,IAAI,CAAC,IAAIA,IAAI,CAACE,IAAI,KAAKK,OAAO;AAC/C;AACA,MAAMC,QAAQ,GAAG,UAAU;AACpB,SAASC,UAAUA,CAACT,IAAI,EAAE;EAC/B,OAAOC,OAAO,CAACD,IAAI,CAAC,IAAIA,IAAI,CAACE,IAAI,KAAKM,QAAQ;AAChD;AACO,SAASE,kBAAkBA,CAACV,IAAI,EAAE;EACvC,OAAOC,OAAO,CAACD,IAAI,CAAC,IAAI,CAACD,UAAU,CAACC,IAAI,CAAC,IAAI,CAACM,SAAS,CAACN,IAAI,CAAC;AAC/D;AACA,SAASW,WAAWA,CAACX,IAAI,EAAEY,KAAK,EAAE;EAChC,IAAIN,SAAS,CAACN,IAAI,CAAC,EACjB;EACF,IAAID,UAAU,CAACC,IAAI,CAAC,IAAIS,UAAU,CAACT,IAAI,CAAC,EAAE;IACxC,OAAOY,KAAK,GAAG,CAAC,GAAGC,iBAAiB,CAACb,IAAI,CAACc,QAAQ,EAAEF,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;EAC3E;EACE,OAAOZ,IAAI;AACb;AACY,MAACa,iBAAiB,GAAGA,CAACE,KAAK,EAAEC,QAAQ,GAAG,CAAC,KAAK;EACxD,IAAIC,OAAO,CAACF,KAAK,CAAC,EAAE;IAClB,OAAOJ,WAAW,CAACI,KAAK,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC;EAC1C,CAAG,MAAM;IACL,OAAOL,WAAW,CAACI,KAAK,EAAEC,QAAQ,CAAC;EACvC;AACA;AACO,SAASE,QAAQA,CAACC,SAAS,EAAE,GAAGC,IAAI,EAAE;EAC3C,OAAOD,SAAS,GAAGE,WAAW,CAAC,GAAGD,IAAI,CAAC,GAAGE,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC;AAC5E;AACO,SAASD,WAAWA,CAAC,GAAGD,IAAI,EAAE;EACnC,OAAOG,SAAS,EAAE,EAAEC,WAAW,CAAC,GAAGJ,IAAI,CAAC;AAC1C;AACY,MAACK,kBAAkB,GAAIzB,IAAI,IAAK;EAC1C,IAAI,CAACC,OAAO,CAACD,IAAI,CAAC,EAAE;IAClB0B,SAAS,CAAC9B,KAAK,EAAE,sCAAsC,CAAC;IACxD,OAAO,EAAE;EACb;EACE,MAAM+B,GAAG,GAAG3B,IAAI,CAAC4B,KAAK,IAAI,EAAE;EAC5B,MAAM1B,IAAI,GAAG,CAACD,OAAO,CAACD,IAAI,CAACE,IAAI,CAAC,GAAGF,IAAI,CAACE,IAAI,CAAC0B,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;EAClE,MAAMA,KAAK,GAAG,EAAE;EAChBC,MAAM,CAACC,IAAI,CAAC5B,IAAI,CAAC,CAAC6B,OAAO,CAAEC,GAAG,IAAK;IACjC,IAAIC,MAAM,CAAC/B,IAAI,CAAC8B,GAAG,CAAC,EAAE,SAAS,CAAC,EAAE;MAChCJ,KAAK,CAACI,GAAG,CAAC,GAAG9B,IAAI,CAAC8B,GAAG,CAAC,CAACE,OAAO;IACpC;EACA,CAAG,CAAC;EACFL,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAACI,OAAO,CAAEC,GAAG,IAAK;IAChCJ,KAAK,CAACO,QAAQ,CAACH,GAAG,CAAC,CAAC,GAAGL,GAAG,CAACK,GAAG,CAAC;EACnC,CAAG,CAAC;EACF,OAAOJ,KAAK;AACd;AACY,MAACQ,eAAe,GAAItB,QAAQ,IAAK;EAC3C,IAAI,CAACG,OAAO,CAACH,QAAQ,CAAC,IAAIA,QAAQ,CAACuB,MAAM,GAAG,CAAC,EAAE;IAC7C,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACnE;EACE,OAAOxB,QAAQ,CAAC,CAAC,CAAC;AACpB;AACY,MAACyB,eAAe,GAAIzB,QAAQ,IAAK;EAC3C,MAAM0B,MAAM,GAAGvB,OAAO,CAACH,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;EACxD,MAAM2B,MAAM,GAAG,EAAE;EACjBD,MAAM,CAACT,OAAO,CAAEW,KAAK,IAAK;IACxB,IAAIC,EAAE;IACN,IAAI1B,OAAO,CAACyB,KAAK,CAAC,EAAE;MAClBD,MAAM,CAACG,IAAI,CAAC,GAAGL,eAAe,CAACG,KAAK,CAAC,CAAC;IAC5C,CAAK,MAAM,IAAIzC,OAAO,CAACyC,KAAK,CAAC,KAAK,CAACC,EAAE,GAAGD,KAAK,CAACG,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,OAAO,CAAC,EAAE;MACnFL,MAAM,CAACG,IAAI,CAACF,KAAK,EAAE,GAAGH,eAAe,CAACG,KAAK,CAACG,SAAS,CAACC,OAAO,CAAC,CAAC;IACrE,CAAK,MAAM,IAAI7C,OAAO,CAACyC,KAAK,CAAC,IAAIzB,OAAO,CAACyB,KAAK,CAAC5B,QAAQ,CAAC,EAAE;MACpD2B,MAAM,CAACG,IAAI,CAAC,GAAGL,eAAe,CAACG,KAAK,CAAC5B,QAAQ,CAAC,CAAC;IACrD,CAAK,MAAM,IAAIb,OAAO,CAACyC,KAAK,CAAC,IAAIA,KAAK,CAACK,SAAS,KAAK,CAAC,EAAE;MAClDN,MAAM,CAACG,IAAI,CAAC,GAAGL,eAAe,CAACG,KAAK,CAACxC,IAAI,EAAE,CAAC,CAAC;IACnD,CAAK,MAAM;MACLuC,MAAM,CAACG,IAAI,CAACF,KAAK,CAAC;IACxB;EACA,CAAG,CAAC;EACF,OAAOD,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}