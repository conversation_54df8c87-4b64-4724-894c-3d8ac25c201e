{"ast": null, "code": "import * as Vue from 'vue';\nvar isVue2 = false;\nvar isVue3 = true;\nvar Vue2 = undefined;\nfunction install() {}\nexport function set(target, key, val) {\n  if (Array.isArray(target)) {\n    target.length = Math.max(target.length, key);\n    target.splice(key, 1, val);\n    return val;\n  }\n  target[key] = val;\n  return val;\n}\nexport function del(target, key) {\n  if (Array.isArray(target)) {\n    target.splice(key, 1);\n    return;\n  }\n  delete target[key];\n}\nexport * from 'vue';\nexport { Vue, Vue2, isVue2, isVue3, install };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "isVue2", "isVue3", "Vue2", "undefined", "install", "set", "target", "key", "val", "Array", "isArray", "length", "Math", "max", "splice", "del"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/@vueuse/shared/node_modules/vue-demi/lib/index.mjs"], "sourcesContent": ["import * as Vue from 'vue'\n\nvar isVue2 = false\nvar isVue3 = true\nvar Vue2 = undefined\n\nfunction install() {}\n\nexport function set(target, key, val) {\n  if (Array.isArray(target)) {\n    target.length = Math.max(target.length, key)\n    target.splice(key, 1, val)\n    return val\n  }\n  target[key] = val\n  return val\n}\n\nexport function del(target, key) {\n  if (Array.isArray(target)) {\n    target.splice(key, 1)\n    return\n  }\n  delete target[key]\n}\n\nexport * from 'vue'\nexport {\n  Vue,\n  Vue2,\n  isVue2,\n  isVue3,\n  install,\n}\n"], "mappings": "AAAA,OAAO,KAAKA,GAAG,MAAM,KAAK;AAE1B,IAAIC,MAAM,GAAG,KAAK;AAClB,IAAIC,MAAM,GAAG,IAAI;AACjB,IAAIC,IAAI,GAAGC,SAAS;AAEpB,SAASC,OAAOA,CAAA,EAAG,CAAC;AAEpB,OAAO,SAASC,GAAGA,CAACC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACpC,IAAIC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;IACzBA,MAAM,CAACK,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACP,MAAM,CAACK,MAAM,EAAEJ,GAAG,CAAC;IAC5CD,MAAM,CAACQ,MAAM,CAACP,GAAG,EAAE,CAAC,EAAEC,GAAG,CAAC;IAC1B,OAAOA,GAAG;EACZ;EACAF,MAAM,CAACC,GAAG,CAAC,GAAGC,GAAG;EACjB,OAAOA,GAAG;AACZ;AAEA,OAAO,SAASO,GAAGA,CAACT,MAAM,EAAEC,GAAG,EAAE;EAC/B,IAAIE,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;IACzBA,MAAM,CAACQ,MAAM,CAACP,GAAG,EAAE,CAAC,CAAC;IACrB;EACF;EACA,OAAOD,MAAM,CAACC,GAAG,CAAC;AACpB;AAEA,cAAc,KAAK;AACnB,SACER,GAAG,EACHG,IAAI,EACJF,MAAM,EACNC,MAAM,EACNG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}