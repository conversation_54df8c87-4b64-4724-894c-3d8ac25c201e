{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst countdownProps = buildProps({\n  format: {\n    type: String,\n    default: \"HH:mm:ss\"\n  },\n  prefix: String,\n  suffix: String,\n  title: String,\n  value: {\n    type: definePropType([Number, Object]),\n    default: 0\n  },\n  valueStyle: {\n    type: definePropType([String, Object, Array])\n  }\n});\nconst countdownEmits = {\n  finish: () => true,\n  [CHANGE_EVENT]: value => isNumber(value)\n};\nexport { countdownEmits, countdownProps };", "map": {"version": 3, "names": ["countdownProps", "buildProps", "format", "type", "String", "default", "prefix", "suffix", "title", "value", "definePropType", "Number", "Object", "valueStyle", "Array", "countdownEmits", "finish", "CHANGE_EVENT", "isNumber"], "sources": ["../../../../../../packages/components/countdown/src/countdown.ts"], "sourcesContent": ["import { buildProps, definePropType, isNumber } from '@element-plus/utils'\nimport { CHANGE_EVENT } from '@element-plus/constants'\n\nimport type { ExtractPropTypes, StyleValue } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type Countdown from './countdown.vue'\n\nexport const countdownProps = buildProps({\n  /**\n   * @description Formatting the countdown display\n   */\n  format: {\n    type: String,\n    default: 'HH:mm:ss',\n  },\n  /**\n   * @description Sets the prefix of a countdown\n   */\n  prefix: String,\n  /**\n   * @description Sets the suffix of a countdown\n   */\n  suffix: String,\n  /**\n   * @description countdown titles\n   */\n  title: String,\n  /**\n   * @description target time\n   */\n  value: {\n    type: definePropType<number | Dayjs>([Number, Object]),\n    default: 0,\n  },\n  /**\n   * @description Styles countdown values\n   */\n  valueStyle: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n  },\n} as const)\nexport type CountdownProps = ExtractPropTypes<typeof countdownProps>\n\nexport const countdownEmits = {\n  finish: () => true,\n  [CHANGE_EVENT]: (value: number) => isNumber(value),\n}\nexport type CountdownEmits = typeof countdownEmits\n\nexport type CountdownInstance = InstanceType<typeof Countdown> & unknown\n"], "mappings": ";;;AAEY,MAACA,cAAc,GAAGC,UAAU,CAAC;EACvCC,MAAM,EAAE;IACNC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,MAAM,EAAEF,MAAM;EACdG,MAAM,EAAEH,MAAM;EACdI,KAAK,EAAEJ,MAAM;EACbK,KAAK,EAAE;IACLN,IAAI,EAAEO,cAAc,CAAC,CAACC,MAAM,EAAEC,MAAM,CAAC,CAAC;IACtCP,OAAO,EAAE;EACb,CAAG;EACDQ,UAAU,EAAE;IACVV,IAAI,EAAEO,cAAc,CAAC,CAACN,MAAM,EAAEQ,MAAM,EAAEE,KAAK,CAAC;EAChD;AACA,CAAC;AACW,MAACC,cAAc,GAAG;EAC5BC,MAAM,EAAEA,CAAA,KAAM,IAAI;EAClB,CAACC,YAAY,GAAIR,KAAK,IAAKS,QAAQ,CAACT,KAAK;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}