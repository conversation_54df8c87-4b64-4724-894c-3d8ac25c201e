{"ast": null, "code": "import { ref, getCurrentInstance, inject, computed } from 'vue';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nimport { isArray } from '@vue/shared';\nimport { UPDATE_MODEL_EVENT } from '../../../../constants/event.mjs';\nconst useCheckboxModel = props => {\n  const selfModel = ref(false);\n  const {\n    emit\n  } = getCurrentInstance();\n  const checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  const isGroup = computed(() => isUndefined(checkboxGroup) === false);\n  const isLimitExceeded = ref(false);\n  const model = computed({\n    get() {\n      var _a, _b;\n      return isGroup.value ? (_a = checkboxGroup == null ? void 0 : checkboxGroup.modelValue) == null ? void 0 : _a.value : (_b = props.modelValue) != null ? _b : selfModel.value;\n    },\n    set(val) {\n      var _a, _b;\n      if (isGroup.value && isArray(val)) {\n        isLimitExceeded.value = ((_a = checkboxGroup == null ? void 0 : checkboxGroup.max) == null ? void 0 : _a.value) !== void 0 && val.length > (checkboxGroup == null ? void 0 : checkboxGroup.max.value) && val.length > model.value.length;\n        isLimitExceeded.value === false && ((_b = checkboxGroup == null ? void 0 : checkboxGroup.changeEvent) == null ? void 0 : _b.call(checkboxGroup, val));\n      } else {\n        emit(UPDATE_MODEL_EVENT, val);\n        selfModel.value = val;\n      }\n    }\n  });\n  return {\n    model,\n    isGroup,\n    isLimitExceeded\n  };\n};\nexport { useCheckboxModel };", "map": {"version": 3, "names": ["useCheckboxModel", "props", "selfModel", "ref", "emit", "getCurrentInstance", "checkboxGroup", "inject", "checkboxGroupContextKey", "isGroup", "computed", "isUndefined", "isLimitExceeded", "model", "get", "_a", "_b", "value", "modelValue", "set", "val", "isArray", "max", "length", "changeEvent", "call", "UPDATE_MODEL_EVENT"], "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox-model.ts"], "sourcesContent": ["import { computed, getCurrentInstance, inject, ref } from 'vue'\nimport { isArray, isUndefined } from '@element-plus/utils'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { checkboxGroupContextKey } from '../constants'\n\nimport type { CheckboxProps } from '../checkbox'\n\nexport const useCheckboxModel = (props: CheckboxProps) => {\n  const selfModel = ref<unknown>(false)\n  const { emit } = getCurrentInstance()!\n  const checkboxGroup = inject(checkboxGroupContextKey, undefined)\n  const isGroup = computed(() => isUndefined(checkboxGroup) === false)\n  const isLimitExceeded = ref(false)\n  const model = computed({\n    get() {\n      return isGroup.value\n        ? checkboxGroup?.modelValue?.value\n        : props.modelValue ?? selfModel.value\n    },\n\n    set(val: unknown) {\n      if (isGroup.value && isArray(val)) {\n        isLimitExceeded.value =\n          checkboxGroup?.max?.value !== undefined &&\n          val.length > checkboxGroup?.max.value &&\n          val.length > model.value.length\n        isLimitExceeded.value === false && checkboxGroup?.changeEvent?.(val)\n      } else {\n        emit(UPDATE_MODEL_EVENT, val)\n        selfModel.value = val\n      }\n    },\n  })\n\n  return {\n    model,\n    isGroup,\n    isLimitExceeded,\n  }\n}\n\nexport type CheckboxModel = ReturnType<typeof useCheckboxModel>\n"], "mappings": ";;;;;AAIY,MAACA,gBAAgB,GAAIC,KAAK,IAAK;EACzC,MAAMC,SAAS,GAAGC,GAAG,CAAC,KAAK,CAAC;EAC5B,MAAM;IAAEC;EAAI,CAAE,GAAGC,kBAAkB,EAAE;EACrC,MAAMC,aAAa,GAAGC,MAAM,CAACC,uBAAuB,EAAE,KAAK,CAAC,CAAC;EAC7D,MAAMC,OAAO,GAAGC,QAAQ,CAAC,MAAMC,WAAW,CAACL,aAAa,CAAC,KAAK,KAAK,CAAC;EACpE,MAAMM,eAAe,GAAGT,GAAG,CAAC,KAAK,CAAC;EAClC,MAAMU,KAAK,GAAGH,QAAQ,CAAC;IACrBI,GAAGA,CAAA,EAAG;MACJ,IAAIC,EAAE,EAAEC,EAAE;MACV,OAAOP,OAAO,CAACQ,KAAK,GAAG,CAACF,EAAE,GAAGT,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACY,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,EAAE,CAACE,KAAK,GAAG,CAACD,EAAE,GAAGf,KAAK,CAACiB,UAAU,KAAK,IAAI,GAAGF,EAAE,GAAGd,SAAS,CAACe,KAAK;IAClL,CAAK;IACDE,GAAGA,CAACC,GAAG,EAAE;MACP,IAAIL,EAAE,EAAEC,EAAE;MACV,IAAIP,OAAO,CAACQ,KAAK,IAAII,OAAO,CAACD,GAAG,CAAC,EAAE;QACjCR,eAAe,CAACK,KAAK,GAAG,CAAC,CAACF,EAAE,GAAGT,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgB,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACE,KAAK,MAAM,KAAK,CAAC,IAAIG,GAAG,CAACG,MAAM,IAAIjB,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgB,GAAG,CAACL,KAAK,CAAC,IAAIG,GAAG,CAACG,MAAM,GAAGV,KAAK,CAACI,KAAK,CAACM,MAAM;QACxOX,eAAe,CAACK,KAAK,KAAK,KAAK,KAAK,CAACD,EAAE,GAAGV,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACkB,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGR,EAAE,CAACS,IAAI,CAACnB,aAAa,EAAEc,GAAG,CAAC,CAAC;MAC7J,CAAO,MAAM;QACLhB,IAAI,CAACsB,kBAAkB,EAAEN,GAAG,CAAC;QAC7BlB,SAAS,CAACe,KAAK,GAAGG,GAAG;MAC7B;IACA;EACA,CAAG,CAAC;EACF,OAAO;IACLP,KAAK;IACLJ,OAAO;IACPG;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}