{"ast": null, "code": "import baseSlice from './_baseSlice.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a slice of `array` with `n` elements dropped from the end.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {number} [n=1] The number of elements to drop.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.dropRight([1, 2, 3]);\n * // => [1, 2]\n *\n * _.dropRight([1, 2, 3], 2);\n * // => [1]\n *\n * _.dropRight([1, 2, 3], 5);\n * // => []\n *\n * _.dropRight([1, 2, 3], 0);\n * // => [1, 2, 3]\n */\nfunction dropRight(array, n, guard) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  n = guard || n === undefined ? 1 : toInteger(n);\n  n = length - n;\n  return baseSlice(array, 0, n < 0 ? 0 : n);\n}\nexport default dropRight;", "map": {"version": 3, "names": ["baseSlice", "toInteger", "dropRight", "array", "n", "guard", "length", "undefined"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/dropRight.js"], "sourcesContent": ["import baseSlice from './_baseSlice.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a slice of `array` with `n` elements dropped from the end.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {number} [n=1] The number of elements to drop.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.dropRight([1, 2, 3]);\n * // => [1, 2]\n *\n * _.dropRight([1, 2, 3], 2);\n * // => [1]\n *\n * _.dropRight([1, 2, 3], 5);\n * // => []\n *\n * _.dropRight([1, 2, 3], 0);\n * // => [1, 2, 3]\n */\nfunction dropRight(array, n, guard) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  n = (guard || n === undefined) ? 1 : toInteger(n);\n  n = length - n;\n  return baseSlice(array, 0, n < 0 ? 0 : n);\n}\n\nexport default dropRight;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAEC,CAAC,EAAEC,KAAK,EAAE;EAClC,IAAIC,MAAM,GAAGH,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACG,MAAM;EAC7C,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,EAAE;EACX;EACAF,CAAC,GAAIC,KAAK,IAAID,CAAC,KAAKG,SAAS,GAAI,CAAC,GAAGN,SAAS,CAACG,CAAC,CAAC;EACjDA,CAAC,GAAGE,MAAM,GAAGF,CAAC;EACd,OAAOJ,SAAS,CAACG,KAAK,EAAE,CAAC,EAAEC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC;AAC3C;AAEA,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}