{"ast": null, "code": "import { ref, nextTick } from 'vue';\nimport { isKorean } from '../../utils/i18n.mjs';\nfunction useComposition({\n  afterComposition,\n  emit\n}) {\n  const isComposing = ref(false);\n  const handleCompositionStart = event => {\n    emit == null ? void 0 : emit(\"compositionstart\", event);\n    isComposing.value = true;\n  };\n  const handleCompositionUpdate = event => {\n    var _a;\n    emit == null ? void 0 : emit(\"compositionupdate\", event);\n    const text = (_a = event.target) == null ? void 0 : _a.value;\n    const lastCharacter = text[text.length - 1] || \"\";\n    isComposing.value = !isKorean(lastCharacter);\n  };\n  const handleCompositionEnd = event => {\n    emit == null ? void 0 : emit(\"compositionend\", event);\n    if (isComposing.value) {\n      isComposing.value = false;\n      nextTick(() => afterComposition(event));\n    }\n  };\n  const handleComposition = event => {\n    event.type === \"compositionend\" ? handleCompositionEnd(event) : handleCompositionUpdate(event);\n  };\n  return {\n    isComposing,\n    handleComposition,\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd\n  };\n}\nexport { useComposition };", "map": {"version": 3, "names": ["useComposition", "afterComposition", "emit", "isComposing", "ref", "handleCompositionStart", "event", "value", "handleCompositionUpdate", "_a", "text", "target", "lastCharacter", "length", "isKorean", "handleCompositionEnd", "nextTick", "handleComposition", "type"], "sources": ["../../../../../packages/hooks/use-composition/index.ts"], "sourcesContent": ["import { nextTick, ref } from 'vue'\nimport { isKorean } from '@element-plus/utils'\n\ninterface UseCompositionOptions {\n  afterComposition: (event: CompositionEvent) => void\n  emit?: ((event: 'compositionstart', evt: CompositionEvent) => void) &\n    ((event: 'compositionupdate', evt: CompositionEvent) => void) &\n    ((event: 'compositionend', evt: CompositionEvent) => void)\n}\n\nexport function useComposition({\n  afterComposition,\n  emit,\n}: UseCompositionOptions) {\n  const isComposing = ref(false)\n\n  const handleCompositionStart = (event: CompositionEvent) => {\n    emit?.('compositionstart', event)\n    isComposing.value = true\n  }\n\n  const handleCompositionUpdate = (event: CompositionEvent) => {\n    emit?.('compositionupdate', event)\n    const text = (event.target as HTMLInputElement)?.value\n    const lastCharacter = text[text.length - 1] || ''\n    isComposing.value = !isKorean(lastCharacter)\n  }\n\n  const handleCompositionEnd = (event: CompositionEvent) => {\n    emit?.('compositionend', event)\n    if (isComposing.value) {\n      isComposing.value = false\n      nextTick(() => afterComposition(event))\n    }\n  }\n\n  const handleComposition = (event: CompositionEvent) => {\n    event.type === 'compositionend'\n      ? handleCompositionEnd(event)\n      : handleCompositionUpdate(event)\n  }\n\n  return {\n    isComposing,\n    handleComposition,\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd,\n  }\n}\n"], "mappings": ";;AAEO,SAASA,cAAcA,CAAC;EAC7BC,gBAAgB;EAChBC;AACF,CAAC,EAAE;EACD,MAAMC,WAAW,GAAGC,GAAG,CAAC,KAAK,CAAC;EAC9B,MAAMC,sBAAsB,GAAIC,KAAK,IAAK;IACxCJ,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC,kBAAkB,EAAEI,KAAK,CAAC;IACvDH,WAAW,CAACI,KAAK,GAAG,IAAI;EAC5B,CAAG;EACD,MAAMC,uBAAuB,GAAIF,KAAK,IAAK;IACzC,IAAIG,EAAE;IACNP,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC,mBAAmB,EAAEI,KAAK,CAAC;IACxD,MAAMI,IAAI,GAAG,CAACD,EAAE,GAAGH,KAAK,CAACK,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACF,KAAK;IAC5D,MAAMK,aAAa,GAAGF,IAAI,CAACA,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE;IACjDV,WAAW,CAACI,KAAK,GAAG,CAACO,QAAQ,CAACF,aAAa,CAAC;EAChD,CAAG;EACD,MAAMG,oBAAoB,GAAIT,KAAK,IAAK;IACtCJ,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC,gBAAgB,EAAEI,KAAK,CAAC;IACrD,IAAIH,WAAW,CAACI,KAAK,EAAE;MACrBJ,WAAW,CAACI,KAAK,GAAG,KAAK;MACzBS,QAAQ,CAAC,MAAMf,gBAAgB,CAACK,KAAK,CAAC,CAAC;IAC7C;EACA,CAAG;EACD,MAAMW,iBAAiB,GAAIX,KAAK,IAAK;IACnCA,KAAK,CAACY,IAAI,KAAK,gBAAgB,GAAGH,oBAAoB,CAACT,KAAK,CAAC,GAAGE,uBAAuB,CAACF,KAAK,CAAC;EAClG,CAAG;EACD,OAAO;IACLH,WAAW;IACXc,iBAAiB;IACjBZ,sBAAsB;IACtBG,uBAAuB;IACvBO;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}