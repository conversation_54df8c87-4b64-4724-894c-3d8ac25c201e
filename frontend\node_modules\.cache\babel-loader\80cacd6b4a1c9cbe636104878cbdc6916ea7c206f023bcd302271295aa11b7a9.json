{"ast": null, "code": "import { defineComponent, ref, computed, useAttrs, onMounted, onUpdated, openBlock, createBlock, resolveDynamicComponent, normalizeClass, unref, normalizeStyle, withCtx, renderSlot } from 'vue';\nimport { textProps } from './text.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isUndefined } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElText\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: textProps,\n  setup(__props) {\n    const props = __props;\n    const textRef = ref();\n    const textSize = useFormSize();\n    const ns = useNamespace(\"text\");\n    const textKls = computed(() => [ns.b(), ns.m(props.type), ns.m(textSize.value), ns.is(\"truncated\", props.truncated), ns.is(\"line-clamp\", !isUndefined(props.lineClamp))]);\n    const inheritTitle = useAttrs().title;\n    const bindTitle = () => {\n      var _a, _b, _c, _d, _e;\n      if (inheritTitle) return;\n      let shouldAddTitle = false;\n      const text = ((_a = textRef.value) == null ? void 0 : _a.textContent) || \"\";\n      if (props.truncated) {\n        const width = (_b = textRef.value) == null ? void 0 : _b.offsetWidth;\n        const scrollWidth = (_c = textRef.value) == null ? void 0 : _c.scrollWidth;\n        if (width && scrollWidth && scrollWidth > width) {\n          shouldAddTitle = true;\n        }\n      } else if (!isUndefined(props.lineClamp)) {\n        const height = (_d = textRef.value) == null ? void 0 : _d.offsetHeight;\n        const scrollHeight = (_e = textRef.value) == null ? void 0 : _e.scrollHeight;\n        if (height && scrollHeight && scrollHeight > height) {\n          shouldAddTitle = true;\n        }\n      }\n      if (shouldAddTitle) {\n        textRef.value.setAttribute(\"title\", text);\n      } else {\n        textRef.value.removeAttribute(\"title\");\n      }\n    };\n    onMounted(bindTitle);\n    onUpdated(bindTitle);\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), {\n        ref_key: \"textRef\",\n        ref: textRef,\n        class: normalizeClass(unref(textKls)),\n        style: normalizeStyle({\n          \"-webkit-line-clamp\": _ctx.lineClamp\n        })\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 8, [\"class\", \"style\"]);\n    };\n  }\n});\nvar Text = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"text.vue\"]]);\nexport { Text as default };", "map": {"version": 3, "names": ["name", "textRef", "ref", "textSize", "useFormSize", "ns", "useNamespace", "textKls", "computed", "b", "m", "props", "type", "value", "is", "truncated", "isUndefined", "lineClamp", "inheritTitle", "useAttrs", "title", "bindTitle", "_a", "_b", "_c", "_d", "_e", "shouldAddTitle", "text", "textContent", "width", "offsetWidth", "scrollWidth", "height", "offsetHeight", "scrollHeight", "setAttribute", "removeAttribute"], "sources": ["../../../../../../packages/components/text/src/text.vue"], "sourcesContent": ["<template>\n  <component\n    :is=\"tag\"\n    ref=\"textRef\"\n    :class=\"textKls\"\n    :style=\"{ '-webkit-line-clamp': lineClamp }\"\n  >\n    <slot />\n  </component>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, onMounted, onUpdated, ref, useAttrs } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormSize } from '@element-plus/components/form'\nimport { isUndefined } from '@element-plus/utils'\nimport { textProps } from './text'\n\ndefineOptions({\n  name: 'ElText',\n})\n\nconst props = defineProps(textProps)\nconst textRef = ref<HTMLElement>()\n\nconst textSize = useFormSize()\nconst ns = useNamespace('text')\n\nconst textKls = computed(() => [\n  ns.b(),\n  ns.m(props.type),\n  ns.m(textSize.value),\n  ns.is('truncated', props.truncated),\n  ns.is('line-clamp', !isUndefined(props.lineClamp)),\n])\n\nconst inheritTitle = useAttrs().title\n\nconst bindTitle = () => {\n  if (inheritTitle) return\n  let shouldAddTitle = false\n  const text = textRef.value?.textContent || ''\n  if (props.truncated) {\n    const width = textRef.value?.offsetWidth\n    const scrollWidth = textRef.value?.scrollWidth\n    if (width && scrollWidth && scrollWidth > width) {\n      shouldAddTitle = true\n    }\n  } else if (!isUndefined(props.lineClamp)) {\n    const height = textRef.value?.offsetHeight\n    const scrollHeight = textRef.value?.scrollHeight\n    if (height && scrollHeight && scrollHeight > height) {\n      shouldAddTitle = true\n    }\n  }\n  if (shouldAddTitle) {\n    textRef.value!.setAttribute('title', text)\n  } else {\n    textRef.value!.removeAttribute('title')\n  }\n}\n\nonMounted(bindTitle)\nonUpdated(bindTitle)\n</script>\n"], "mappings": ";;;;;;mCAkBc;EACZA,IAAM;AACR;;;;;;IAGA,MAAMC,OAAA,GAAUC,GAAiB;IAEjC,MAAMC,QAAA,GAAWC,WAAY;IACvB,MAAAC,EAAA,GAAKC,YAAA,CAAa,MAAM;IAExB,MAAAC,OAAA,GAAUC,QAAA,CAAS,MAAM,CAC7BH,EAAA,CAAGI,CAAE,IACLJ,EAAA,CAAGK,CAAE,CAAAC,KAAA,CAAMC,IAAI,GACfP,EAAA,CAAGK,CAAE,CAAAP,QAAA,CAASU,KAAK,GACnBR,EAAG,CAAAS,EAAA,CAAG,WAAa,EAAAH,KAAA,CAAMI,SAAS,GAClCV,EAAA,CAAGS,EAAG,eAAc,CAACE,WAAY,CAAAL,KAAA,CAAMM,SAAS,CAAC,EAClD;IAEK,MAAAC,YAAA,GAAeC,QAAA,EAAW,CAAAC,KAAA;IAEhC,MAAMC,SAAA,GAAYA,CAAA,KAAM;MACtB,IAAIC,EAAc,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;MAClB,IAAIR,YAAiB,EACf;MACN,IAAIS,cAAiB;MACb,MAAAC,IAAA,KAAAN,EAAA,GAAArB,OAAuB,CAAAY,KAAA,qBAAAS,EAAA,CAAAO,WAAA;MACvB,IAAAlB,KAAA,CAAAI,SAAA;QACF,MAAAe,KAAA,GAAwB,CAAAP,EAAA,GAAAtB,OAAA,CAAAY,KAAA,SAAc,GAAO,SAAAU,EAAA,CAAAQ,WAAA;QAC9B,MAAAC,WAAA,IAAAR,EAAA,GAAAvB,OAAA,CAAAY,KAAA,qBAAAW,EAAA,CAAAQ,WAAA;QACnB,IAAAF,KAAA,IAAAE,WAAA,IAAAA,WAAA,GAAAF,KAAA;UACSH,cAAa;QACtB;MACA,CAAM,WAAAX,WAAe,CAAAL,KAAA,CAAAM,SAAe;QAChC,MAAAgB,MAAA,GAA0B,CAAAR,EAAA,GAAAxB,OAAA,CAAAY,KAAA,YAAuB,SAAAY,EAAA,CAAAS,YAAA;QAClC,MAAAC,YAAA,IAAAT,EAAA,GAAAzB,OAAA,CAAAY,KAAA,qBAAAa,EAAA,CAAAS,YAAA;QACnB,IAAAF,MAAA,IAAAE,YAAA,IAAAA,YAAA,GAAAF,MAAA;UACFN,cAAA;QACA;MACE;MACF,IAAOA,cAAA;QACG1B,OAAA,CAAAY,KAAA,CAAOuB,YAAA,QAAuB,EAAAR,IAAA;MAAA,CACxC;QACF3B,OAAA,CAAAY,KAAA,CAAAwB,eAAA;MAEA;IACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}