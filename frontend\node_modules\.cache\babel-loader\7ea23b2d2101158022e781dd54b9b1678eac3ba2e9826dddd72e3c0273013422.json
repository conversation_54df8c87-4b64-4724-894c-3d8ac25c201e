{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isString, isArray } from '@vue/shared';\nconst menuItemProps = buildProps({\n  index: {\n    type: definePropType([String, null]),\n    default: null\n  },\n  route: {\n    type: definePropType([String, Object])\n  },\n  disabled: Boolean\n});\nconst menuItemEmits = {\n  click: item => isString(item.index) && isArray(item.indexPath)\n};\nexport { menuItemEmits, menuItemProps };", "map": {"version": 3, "names": ["menuItemProps", "buildProps", "index", "type", "definePropType", "String", "default", "route", "Object", "disabled", "Boolean", "menuItemEmits", "click", "item", "isString", "isArray", "indexPath"], "sources": ["../../../../../../packages/components/menu/src/menu-item.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isArray,\n  isString,\n} from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { RouteLocationRaw } from 'vue-router'\nimport type { MenuItemRegistered } from './types'\n\nexport const menuItemProps = buildProps({\n  /**\n   * @description unique identification\n   */\n  index: {\n    type: definePropType<string | null>([String, null]),\n    default: null,\n  },\n  /**\n   * @description Vue Router object\n   */\n  route: {\n    type: definePropType<RouteLocationRaw>([String, Object]),\n  },\n  /**\n   * @description whether disabled\n   */\n  disabled: Boolean,\n} as const)\nexport type MenuItemProps = ExtractPropTypes<typeof menuItemProps>\n\nexport const menuItemEmits = {\n  click: (item: MenuItemRegistered) =>\n    isString(item.index) && isArray(item.indexPath),\n}\nexport type MenuItemEmits = typeof menuItemEmits\n"], "mappings": ";;AAMY,MAACA,aAAa,GAAGC,UAAU,CAAC;EACtCC,KAAK,EAAE;IACLC,IAAI,EAAEC,cAAc,CAAC,CAACC,MAAM,EAAE,IAAI,CAAC,CAAC;IACpCC,OAAO,EAAE;EACb,CAAG;EACDC,KAAK,EAAE;IACLJ,IAAI,EAAEC,cAAc,CAAC,CAACC,MAAM,EAAEG,MAAM,CAAC;EACzC,CAAG;EACDC,QAAQ,EAAEC;AACZ,CAAC;AACW,MAACC,aAAa,GAAG;EAC3BC,KAAK,EAAGC,IAAI,IAAKC,QAAQ,CAACD,IAAI,CAACX,KAAK,CAAC,IAAIa,OAAO,CAACF,IAAI,CAACG,SAAS;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}