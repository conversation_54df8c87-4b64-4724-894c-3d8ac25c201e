{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode } from \"vue\";\nimport _imports_0 from '@/assets/logo.png';\nconst _hoisted_1 = {\n  class: \"app-header\"\n};\nconst _hoisted_2 = {\n  class: \"logo-container\"\n};\nconst _hoisted_3 = {\n  class: \"nav-container\"\n};\nconst _hoisted_4 = {\n  class: \"user-info\"\n};\nconst _hoisted_5 = {\n  class: \"credit-score\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  const _component_arrow_down = _resolveComponent(\"arrow-down\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_dropdown_item = _resolveComponent(\"el-dropdown-item\");\n  const _component_el_dropdown_menu = _resolveComponent(\"el-dropdown-menu\");\n  const _component_el_dropdown = _resolveComponent(\"el-dropdown\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"header\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_router_link, {\n    to: \"/\"\n  }, {\n    default: _withCtx(() => _cache[2] || (_cache[2] = [_createElementVNode(\"img\", {\n      src: _imports_0,\n      alt: \"图书馆自习室管理系统\",\n      class: \"logo\"\n    }, null, -1 /* HOISTED */), _createElementVNode(\"h1\", {\n      class: \"site-title\"\n    }, \"图书馆自习室管理系统\", -1 /* HOISTED */)])),\n    _: 1 /* STABLE */,\n    __: [2]\n  })]), _createElementVNode(\"div\", _hoisted_3, [$setup.isLoggedIn ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [_createVNode(_component_el_dropdown, {\n    trigger: \"click\",\n    onCommand: $setup.handleCommand\n  }, {\n    dropdown: _withCtx(() => [_createVNode(_component_el_dropdown_menu, null, {\n      default: _withCtx(() => [_createVNode(_component_el_dropdown_item, {\n        command: \"profile\"\n      }, {\n        default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"个人中心\")])),\n        _: 1 /* STABLE */,\n        __: [3]\n      }), _createVNode(_component_el_dropdown_item, {\n        command: \"reservations\"\n      }, {\n        default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"我的预约\")])),\n        _: 1 /* STABLE */,\n        __: [4]\n      }), _createVNode(_component_el_dropdown_item, {\n        command: \"records\"\n      }, {\n        default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\"操作记录\")])),\n        _: 1 /* STABLE */,\n        __: [5]\n      }), _createVNode(_component_el_dropdown_item, {\n        divided: \"\",\n        command: \"logout\"\n      }, {\n        default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"退出登录\")])),\n        _: 1 /* STABLE */,\n        __: [6]\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_4, [_createTextVNode(_toDisplayString($setup.userInfo.studentIdHash || \"用户\") + \" \", 1 /* TEXT */), _createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_arrow_down)]),\n      _: 1 /* STABLE */\n    })])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onCommand\"]), _createElementVNode(\"div\", _hoisted_5, [_cache[7] || (_cache[7] = _createTextVNode(\" 信誉分: \")), _createElementVNode(\"span\", {\n    class: _normalizeClass($setup.creditScoreClass)\n  }, _toDisplayString($setup.userInfo.creditScore || 100), 3 /* TEXT, CLASS */)])], 64 /* STABLE_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/login'))\n  }, {\n    default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\"登录\")])),\n    _: 1 /* STABLE */,\n    __: [8]\n  }), _createVNode(_component_el_button, {\n    onClick: _cache[1] || (_cache[1] = $event => _ctx.$router.push('/register'))\n  }, {\n    default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"注册\")])),\n    _: 1 /* STABLE */,\n    __: [9]\n  })], 64 /* STABLE_FRAGMENT */))])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_router_link", "to", "default", "_withCtx", "_cache", "src", "alt", "_", "__", "_hoisted_3", "$setup", "isLoggedIn", "_Fragment", "key", "_component_el_dropdown", "trigger", "onCommand", "handleCommand", "dropdown", "_component_el_dropdown_menu", "_component_el_dropdown_item", "command", "_createTextVNode", "divided", "_hoisted_4", "_toDisplayString", "userInfo", "studentIdHash", "_component_el_icon", "_component_arrow_down", "_hoisted_5", "_normalizeClass", "creditScoreClass", "creditScore", "_component_el_button", "type", "onClick", "$event", "_ctx", "$router", "push"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Header.vue"], "sourcesContent": ["<template>\n  <header class=\"app-header\">\n    <div class=\"logo-container\">\n      <router-link to=\"/\">\n        <img src=\"@/assets/logo.png\" alt=\"图书馆自习室管理系统\" class=\"logo\" />\n        <h1 class=\"site-title\">图书馆自习室管理系统</h1>\n      </router-link>\n    </div>\n    <div class=\"nav-container\">\n      <template v-if=\"isLoggedIn\">\n        <el-dropdown trigger=\"click\" @command=\"handleCommand\">\n          <span class=\"user-info\">\n            {{ userInfo.studentIdHash || \"用户\" }}\n            <el-icon><arrow-down /></el-icon>\n          </span>\n          <template #dropdown>\n            <el-dropdown-menu>\n              <el-dropdown-item command=\"profile\">个人中心</el-dropdown-item>\n              <el-dropdown-item command=\"reservations\">我的预约</el-dropdown-item>\n              <el-dropdown-item command=\"records\">操作记录</el-dropdown-item>\n              <el-dropdown-item divided command=\"logout\">退出登录</el-dropdown-item>\n            </el-dropdown-menu>\n          </template>\n        </el-dropdown>\n        <div class=\"credit-score\">\n          信誉分:\n          <span :class=\"creditScoreClass\">{{ userInfo.creditScore || 100 }}</span>\n        </div>\n      </template>\n      <template v-else>\n        <el-button type=\"primary\" @click=\"$router.push('/login')\">登录</el-button>\n        <el-button @click=\"$router.push('/register')\">注册</el-button>\n      </template>\n    </div>\n  </header>\n</template>\n\n<script>\nimport { computed } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRouter } from \"vue-router\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nimport { ArrowDown } from \"@element-plus/icons-vue\";\n\nexport default {\n  name: \"AppHeader\",\n  components: {\n    ArrowDown,\n  },\n  setup() {\n    const store = useStore();\n    const router = useRouter();\n\n    const isLoggedIn = computed(() => store.getters[\"user/isLoggedIn\"]);\n    const userInfo = computed(() => store.getters[\"user/userInfo\"]);\n\n    const creditScoreClass = computed(() => {\n      const score = userInfo.value.creditScore || 100;\n      if (score >= 90) return \"score-excellent\";\n      if (score >= 70) return \"score-good\";\n      if (score >= 50) return \"score-warning\";\n      return \"score-danger\";\n    });\n\n    const handleCommand = async command => {\n      switch (command) {\n        case \"profile\":\n          router.push(\"/user/profile\");\n          break;\n        case \"reservations\":\n          router.push(\"/user/reservations\");\n          break;\n        case \"records\":\n          router.push(\"/user/records\");\n          break;\n        case \"logout\":\n          try {\n            await ElMessageBox.confirm(\"确定要退出登录吗？\", \"提示\", {\n              confirmButtonText: \"确定\",\n              cancelButtonText: \"取消\",\n              type: \"warning\",\n            });\n            await store.dispatch(\"user/logout\");\n            ElMessage.success(\"退出登录成功\");\n            router.push(\"/login\");\n          } catch (error) {\n            // 用户取消操作\n          }\n          break;\n      }\n    };\n\n    return {\n      isLoggedIn,\n      userInfo,\n      creditScoreClass,\n      handleCommand,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 20px;\n  height: 60px;\n  background-color: #fff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 10;\n}\n\n.logo-container {\n  display: flex;\n  align-items: center;\n\n  a {\n    display: flex;\n    align-items: center;\n    text-decoration: none;\n    color: inherit;\n  }\n\n  .logo {\n    height: 40px;\n    margin-right: 10px;\n  }\n\n  .site-title {\n    font-size: 1.2rem;\n    font-weight: 600;\n    margin: 0;\n  }\n}\n\n.nav-container {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  font-weight: 500;\n\n  .el-icon {\n    margin-left: 5px;\n  }\n}\n\n.credit-score {\n  margin-left: 15px;\n  font-size: 0.9rem;\n\n  .score-excellent {\n    color: #67c23a;\n    font-weight: bold;\n  }\n\n  .score-good {\n    color: #409eff;\n    font-weight: bold;\n  }\n\n  .score-warning {\n    color: #e6a23c;\n    font-weight: bold;\n  }\n\n  .score-danger {\n    color: #f56c6c;\n    font-weight: bold;\n  }\n}\n</style>\n"], "mappings": ";;OAIaA,UAAuB;;EAH1BC,KAAK,EAAC;AAAY;;EACnBA,KAAK,EAAC;AAAgB;;EAMtBA,KAAK,EAAC;AAAe;;EAGdA,KAAK,EAAC;AAAW;;EAapBA,KAAK,EAAC;AAAc;;;;;;;;;uBAvB/BC,mBAAA,CAiCS,UAjCTC,UAiCS,GAhCPC,mBAAA,CAKM,OALNC,UAKM,GAJJC,YAAA,CAGcC,sBAAA;IAHDC,EAAE,EAAC;EAAG;IAHzBC,OAAA,EAAAC,QAAA,CAIQ,MAA6DC,MAAA,QAAAA,MAAA,OAA7DP,mBAAA,CAA6D;MAAxDQ,GAAuB,EAAvBZ,UAAuB;MAACa,GAAG,EAAC,YAAY;MAACZ,KAAK,EAAC;gCACpDG,mBAAA,CAAsC;MAAlCH,KAAK,EAAC;IAAY,GAAC,YAAU,oB;IALzCa,CAAA;IAAAC,EAAA;QAQIX,mBAAA,CAyBM,OAzBNY,UAyBM,GAxBYC,MAAA,CAAAC,UAAU,I,cAA1BhB,mBAAA,CAmBWiB,SAAA;IA5BjBC,GAAA;EAAA,IAUQd,YAAA,CAace,sBAAA;IAbDC,OAAO,EAAC,OAAO;IAAEC,SAAO,EAAEN,MAAA,CAAAO;;IAK1BC,QAAQ,EAAAf,QAAA,CACjB,MAKmB,CALnBJ,YAAA,CAKmBoB,2BAAA;MArB/BjB,OAAA,EAAAC,QAAA,CAiBc,MAA2D,CAA3DJ,YAAA,CAA2DqB,2BAAA;QAAzCC,OAAO,EAAC;MAAS;QAjBjDnB,OAAA,EAAAC,QAAA,CAiBkD,MAAIC,MAAA,QAAAA,MAAA,OAjBtDkB,gBAAA,CAiBkD,MAAI,E;QAjBtDf,CAAA;QAAAC,EAAA;UAkBcT,YAAA,CAAgEqB,2BAAA;QAA9CC,OAAO,EAAC;MAAc;QAlBtDnB,OAAA,EAAAC,QAAA,CAkBuD,MAAIC,MAAA,QAAAA,MAAA,OAlB3DkB,gBAAA,CAkBuD,MAAI,E;QAlB3Df,CAAA;QAAAC,EAAA;UAmBcT,YAAA,CAA2DqB,2BAAA;QAAzCC,OAAO,EAAC;MAAS;QAnBjDnB,OAAA,EAAAC,QAAA,CAmBkD,MAAIC,MAAA,QAAAA,MAAA,OAnBtDkB,gBAAA,CAmBkD,MAAI,E;QAnBtDf,CAAA;QAAAC,EAAA;UAoBcT,YAAA,CAAkEqB,2BAAA;QAAhDG,OAAO,EAAP,EAAO;QAACF,OAAO,EAAC;;QApBhDnB,OAAA,EAAAC,QAAA,CAoByD,MAAIC,MAAA,QAAAA,MAAA,OApB7DkB,gBAAA,CAoByD,MAAI,E;QApB7Df,CAAA;QAAAC,EAAA;;MAAAD,CAAA;;IAAAL,OAAA,EAAAC,QAAA,CAWU,MAGO,CAHPN,mBAAA,CAGO,QAHP2B,UAGO,GAdjBF,gBAAA,CAAAG,gBAAA,CAYef,MAAA,CAAAgB,QAAQ,CAACC,aAAa,YAAW,GACpC,iBAAA5B,YAAA,CAAiC6B,kBAAA;MAb7C1B,OAAA,EAAAC,QAAA,CAaqB,MAAc,CAAdJ,YAAA,CAAc8B,qBAAA,E;MAbnCtB,CAAA;;IAAAA,CAAA;oCAwBQV,mBAAA,CAGM,OAHNiC,UAGM,G,0BA3BdR,gBAAA,CAwBkC,QAExB,IAAAzB,mBAAA,CAAwE;IAAjEH,KAAK,EA1BtBqC,eAAA,CA0BwBrB,MAAA,CAAAsB,gBAAgB;sBAAKtB,MAAA,CAAAgB,QAAQ,CAACO,WAAW,+B,gDAG3DtC,mBAAA,CAGWiB,SAAA;IAhCjBC,GAAA;EAAA,IA8BQd,YAAA,CAAwEmC,oBAAA;IAA7DC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAAhC,MAAA,QAAAA,MAAA,MAAAiC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;IA9BtDtC,OAAA,EAAAC,QAAA,CA8BkE,MAAEC,MAAA,QAAAA,MAAA,OA9BpEkB,gBAAA,CA8BkE,IAAE,E;IA9BpEf,CAAA;IAAAC,EAAA;MA+BQT,YAAA,CAA4DmC,oBAAA;IAAhDE,OAAK,EAAAhC,MAAA,QAAAA,MAAA,MAAAiC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;IA/BvCtC,OAAA,EAAAC,QAAA,CA+BsD,MAAEC,MAAA,QAAAA,MAAA,OA/BxDkB,gBAAA,CA+BsD,IAAE,E;IA/BxDf,CAAA;IAAAC,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}