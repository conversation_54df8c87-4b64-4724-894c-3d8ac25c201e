{"ast": null, "code": "import { defineComponent, createVNode } from 'vue';\nimport { autoResizerProps } from '../auto-resizer.mjs';\nimport { useAutoResize } from '../composables/use-auto-resize.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nconst AutoResizer = defineComponent({\n  name: \"ElAutoResizer\",\n  props: autoResizerProps,\n  setup(props, {\n    slots\n  }) {\n    const ns = useNamespace(\"auto-resizer\");\n    const {\n      height,\n      width,\n      sizer\n    } = useAutoResize(props);\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return () => {\n      var _a;\n      return createVNode(\"div\", {\n        \"ref\": sizer,\n        \"class\": ns.b(),\n        \"style\": style\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots, {\n        height: height.value,\n        width: width.value\n      })]);\n    };\n  }\n});\nvar AutoResizer$1 = AutoResizer;\nexport { AutoResizer$1 as default };", "map": {"version": 3, "names": ["AutoResizer", "defineComponent", "name", "props", "autoResizerProps", "slots", "ns", "useNamespace", "height", "width", "sizer", "useAutoResize", "style", "_a", "createVNode", "b", "default", "call", "value", "AutoResizer$1"], "sources": ["../../../../../../../packages/components/table-v2/src/components/auto-resizer.tsx"], "sourcesContent": ["import { defineComponent } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { autoResizerProps } from '../auto-resizer'\nimport { useAutoResize } from '../composables'\n\nconst AutoResizer = defineComponent({\n  name: 'ElA<PERSON>Resizer',\n  props: autoResizerProps,\n  setup(props, { slots }) {\n    const ns = useNamespace('auto-resizer')\n    const { height, width, sizer } = useAutoResize(props)\n    const style = {\n      width: '100%',\n      height: '100%',\n    }\n\n    return () => {\n      return (\n        <div ref={sizer} class={ns.b()} style={style}>\n          {slots.default?.({\n            height: height.value,\n            width: width.value,\n          })}\n        </div>\n      )\n    }\n  },\n})\n\nexport default AutoResizer\n"], "mappings": ";;;;AAKA,MAAMA,WAAW,GAAGC,eAAe,CAAC;EAClCC,IAAI,EAAE,eAD4B;EAElCC,KAAK,EAAEC,gBAF2B;;IAG7BC;EAAU;IAAS,MAAAC,EAAA,GAAAC,YAAA;IACtB,MAAM;MACAC,MAAA;MAAEC,KAAF;MAAUC;IAAO,IAAAC,aAAA,CAAAR,KAAA;IAAjB,MAA2BS,KAAA;MACjCH,KAAA,QAAc;MACZD,MAAA,EADY;IAEZ;IAFY,OAAd;MAKA,IAAAK,EAAa;MACX,OAAAC,WAAA;QAAA,OACYJ,KADZ;QAAA,OAC0B,EAAAJ,EAAE,CAACS,CAAH,EAD1B;QAAA,OACyC,EAAAH;OACpC,IAAAC,EAAA,GAAMR,KAAA,CAAAW,OAAU,qBAAAH,EAAA,CAAAI,IAAA,CAAAZ,KAAA;QACfG,MAAM,EAAEA,MAAM,CAACU,KADA;QAEfT,KAAK,EAAEA,KAAK,CAACS;MAFE,CAAhB,CAFL;KADF;EAUD;;AArBiC,IAAAC,aAAA,GAApCnB,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}