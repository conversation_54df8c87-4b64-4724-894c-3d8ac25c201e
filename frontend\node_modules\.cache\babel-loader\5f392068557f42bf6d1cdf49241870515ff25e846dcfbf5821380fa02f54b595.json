{"ast": null, "code": "import createCaseFirst from './_createCaseFirst.js';\n\n/**\n * Converts the first character of `string` to lower case.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the converted string.\n * @example\n *\n * _.lowerFirst('Fred');\n * // => 'fred'\n *\n * _.lowerFirst('FRED');\n * // => 'fRED'\n */\nvar lowerFirst = createCaseFirst('toLowerCase');\nexport default lowerFirst;", "map": {"version": 3, "names": ["createCaseFirst", "lowerFirst"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/lowerFirst.js"], "sourcesContent": ["import createCaseFirst from './_createCaseFirst.js';\n\n/**\n * Converts the first character of `string` to lower case.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the converted string.\n * @example\n *\n * _.lowerFirst('Fred');\n * // => 'fred'\n *\n * _.lowerFirst('FRED');\n * // => 'fRED'\n */\nvar lowerFirst = createCaseFirst('toLowerCase');\n\nexport default lowerFirst;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAGD,eAAe,CAAC,aAAa,CAAC;AAE/C,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}