{"ast": null, "code": "import { columns, dataType, fixedDataType, requiredNumber, classType, styleType } from './common.mjs';\nimport { tableV2HeaderProps } from './header.mjs';\nimport { tableV2RowProps } from './row.mjs';\nimport { virtualizedListProps, virtualizedGridProps } from '../../virtual-list/src/props.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst tableV2GridProps = buildProps({\n  columns,\n  data: dataType,\n  fixedData: fixedDataType,\n  estimatedRowHeight: tableV2RowProps.estimatedRowHeight,\n  width: requiredNumber,\n  height: requiredNumber,\n  headerWidth: requiredNumber,\n  headerHeight: tableV2HeaderProps.headerHeight,\n  bodyWidth: requiredNumber,\n  rowHeight: requiredNumber,\n  cache: virtualizedListProps.cache,\n  useIsScrolling: Boolean,\n  scrollbarAlwaysOn: virtualizedGridProps.scrollbarAlwaysOn,\n  scrollbarStartGap: virtualizedGridProps.scrollbarStartGap,\n  scrollbarEndGap: virtualizedGridProps.scrollbarEndGap,\n  class: classType,\n  style: styleType,\n  containerStyle: styleType,\n  getRowHeight: {\n    type: definePropType(Function),\n    required: true\n  },\n  rowKey: tableV2RowProps.rowKey,\n  onRowsRendered: {\n    type: definePropType(Function)\n  },\n  onScroll: {\n    type: definePropType(Function)\n  }\n});\nexport { tableV2GridProps };", "map": {"version": 3, "names": ["tableV2GridProps", "buildProps", "columns", "data", "dataType", "fixedData", "fixedDataType", "estimatedRowHeight", "tableV2RowProps", "width", "requiredNumber", "height", "headerWidth", "headerHeight", "tableV2HeaderProps", "bodyWidth", "rowHeight", "cache", "virtualizedListProps", "useIsScrolling", "Boolean", "scrollbarAlwaysOn", "virtualizedGridProps", "scrollbarStartGap", "scrollbarEndGap", "class", "classType", "style", "styleType", "containerStyle", "getRowHeight", "type", "definePropType", "Function", "required", "<PERSON><PERSON><PERSON>", "onRowsRendered", "onScroll"], "sources": ["../../../../../../packages/components/table-v2/src/grid.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport {\n  virtualizedGridProps,\n  virtualizedListProps,\n} from '@element-plus/components/virtual-list'\nimport {\n  classType,\n  columns,\n  dataType,\n  fixedDataType,\n  requiredNumber,\n  styleType,\n} from './common'\nimport { tableV2HeaderProps } from './header'\nimport { tableV2RowProps } from './row'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { ItemSize } from '@element-plus/components/virtual-list'\n\nexport type onRowRenderedParams = {\n  rowCacheStart: number\n  rowCacheEnd: number\n  rowVisibleStart: number\n  rowVisibleEnd: number\n}\n\nexport const tableV2GridProps = buildProps({\n  columns,\n  data: dataType,\n  fixedData: fixedDataType,\n  estimatedRowHeight: tableV2RowProps.estimatedRowHeight,\n\n  /**\n   * Size related attributes\n   */\n  width: requiredNumber,\n  height: requiredNumber,\n\n  headerWidth: requiredNumber,\n  headerHeight: tableV2HeaderProps.headerHeight,\n\n  bodyWidth: requiredNumber,\n  rowHeight: requiredNumber,\n\n  /**\n   * Special attributes\n   */\n  cache: virtualizedListProps.cache,\n  useIsScrolling: Boolean,\n  scrollbarAlwaysOn: virtualizedGridProps.scrollbarAlwaysOn,\n  scrollbarStartGap: virtualizedGridProps.scrollbarStartGap,\n  scrollbarEndGap: virtualizedGridProps.scrollbarEndGap,\n\n  /**\n   * CSS attributes\n   */\n  class: classType,\n  style: styleType,\n  containerStyle: styleType,\n\n  getRowHeight: {\n    type: definePropType<ItemSize>(Function),\n    required: true,\n  },\n  rowKey: tableV2RowProps.rowKey,\n\n  /**\n   * Event handlers\n   */\n  onRowsRendered: {\n    type: definePropType<(params: onRowRenderedParams) => void>(Function),\n  },\n  onScroll: {\n    type: definePropType<(...args: any[]) => void>(Function),\n  },\n} as const)\n\nexport type TableV2GridProps = ExtractPropTypes<typeof tableV2GridProps>\n"], "mappings": ";;;;;AAeY,MAACA,gBAAgB,GAAGC,UAAU,CAAC;EACzCC,OAAO;EACPC,IAAI,EAAEC,QAAQ;EACdC,SAAS,EAAEC,aAAa;EACxBC,kBAAkB,EAAEC,eAAe,CAACD,kBAAkB;EACtDE,KAAK,EAAEC,cAAc;EACrBC,MAAM,EAAED,cAAc;EACtBE,WAAW,EAAEF,cAAc;EAC3BG,YAAY,EAAEC,kBAAkB,CAACD,YAAY;EAC7CE,SAAS,EAAEL,cAAc;EACzBM,SAAS,EAAEN,cAAc;EACzBO,KAAK,EAAEC,oBAAoB,CAACD,KAAK;EACjCE,cAAc,EAAEC,OAAO;EACvBC,iBAAiB,EAAEC,oBAAoB,CAACD,iBAAiB;EACzDE,iBAAiB,EAAED,oBAAoB,CAACC,iBAAiB;EACzDC,eAAe,EAAEF,oBAAoB,CAACE,eAAe;EACrDC,KAAK,EAAEC,SAAS;EAChBC,KAAK,EAAEC,SAAS;EAChBC,cAAc,EAAED,SAAS;EACzBE,YAAY,EAAE;IACZC,IAAI,EAAEC,cAAc,CAACC,QAAQ,CAAC;IAC9BC,QAAQ,EAAE;EACd,CAAG;EACDC,MAAM,EAAE3B,eAAe,CAAC2B,MAAM;EAC9BC,cAAc,EAAE;IACdL,IAAI,EAAEC,cAAc,CAACC,QAAQ;EACjC,CAAG;EACDI,QAAQ,EAAE;IACRN,IAAI,EAAEC,cAAc,CAACC,QAAQ;EACjC;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}