{"ast": null, "code": "import TimeSelect from './src/time-select2.mjs';\nexport { timeSelectProps } from './src/time-select.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElTimeSelect = withInstall(TimeSelect);\nexport { ElTimeSelect, ElTimeSelect as default };", "map": {"version": 3, "names": ["ElTimeSelect", "withInstall", "TimeSelect"], "sources": ["../../../../../packages/components/time-select/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport TimeSelect from './src/time-select.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTimeSelect: SFCWithInstall<typeof TimeSelect> =\n  withInstall(TimeSelect)\nexport default ElTimeSelect\n\nexport * from './src/time-select'\n"], "mappings": ";;;AAEY,MAACA,YAAY,GAAGC,WAAW,CAACC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}