{"ast": null, "code": "import { ref, provide } from 'vue';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isFunction } from '@vue/shared';\nimport { removeClass, addClass } from '../../../../utils/dom/style.mjs';\nconst dragEventsKey = Symbol(\"dragEvents\");\nfunction useDragNodeHandler({\n  props,\n  ctx,\n  el$,\n  dropIndicator$,\n  store\n}) {\n  const ns = useNamespace(\"tree\");\n  const dragState = ref({\n    showDropIndicator: false,\n    draggingNode: null,\n    dropNode: null,\n    allowDrop: true,\n    dropType: null\n  });\n  const treeNodeDragStart = ({\n    event,\n    treeNode\n  }) => {\n    if (isFunction(props.allowDrag) && !props.allowDrag(treeNode.node)) {\n      event.preventDefault();\n      return false;\n    }\n    event.dataTransfer.effectAllowed = \"move\";\n    try {\n      event.dataTransfer.setData(\"text/plain\", \"\");\n    } catch (e) {}\n    dragState.value.draggingNode = treeNode;\n    ctx.emit(\"node-drag-start\", treeNode.node, event);\n  };\n  const treeNodeDragOver = ({\n    event,\n    treeNode\n  }) => {\n    const dropNode = treeNode;\n    const oldDropNode = dragState.value.dropNode;\n    if (oldDropNode && oldDropNode.node.id !== dropNode.node.id) {\n      removeClass(oldDropNode.$el, ns.is(\"drop-inner\"));\n    }\n    const draggingNode = dragState.value.draggingNode;\n    if (!draggingNode || !dropNode) return;\n    let dropPrev = true;\n    let dropInner = true;\n    let dropNext = true;\n    let userAllowDropInner = true;\n    if (isFunction(props.allowDrop)) {\n      dropPrev = props.allowDrop(draggingNode.node, dropNode.node, \"prev\");\n      userAllowDropInner = dropInner = props.allowDrop(draggingNode.node, dropNode.node, \"inner\");\n      dropNext = props.allowDrop(draggingNode.node, dropNode.node, \"next\");\n    }\n    event.dataTransfer.dropEffect = dropInner || dropPrev || dropNext ? \"move\" : \"none\";\n    if ((dropPrev || dropInner || dropNext) && (oldDropNode == null ? void 0 : oldDropNode.node.id) !== dropNode.node.id) {\n      if (oldDropNode) {\n        ctx.emit(\"node-drag-leave\", draggingNode.node, oldDropNode.node, event);\n      }\n      ctx.emit(\"node-drag-enter\", draggingNode.node, dropNode.node, event);\n    }\n    if (dropPrev || dropInner || dropNext) {\n      dragState.value.dropNode = dropNode;\n    } else {\n      dragState.value.dropNode = null;\n    }\n    if (dropNode.node.nextSibling === draggingNode.node) {\n      dropNext = false;\n    }\n    if (dropNode.node.previousSibling === draggingNode.node) {\n      dropPrev = false;\n    }\n    if (dropNode.node.contains(draggingNode.node, false)) {\n      dropInner = false;\n    }\n    if (draggingNode.node === dropNode.node || draggingNode.node.contains(dropNode.node)) {\n      dropPrev = false;\n      dropInner = false;\n      dropNext = false;\n    }\n    const targetPosition = dropNode.$el.querySelector(`.${ns.be(\"node\", \"content\")}`).getBoundingClientRect();\n    const treePosition = el$.value.getBoundingClientRect();\n    let dropType;\n    const prevPercent = dropPrev ? dropInner ? 0.25 : dropNext ? 0.45 : 1 : -1;\n    const nextPercent = dropNext ? dropInner ? 0.75 : dropPrev ? 0.55 : 0 : 1;\n    let indicatorTop = -9999;\n    const distance = event.clientY - targetPosition.top;\n    if (distance < targetPosition.height * prevPercent) {\n      dropType = \"before\";\n    } else if (distance > targetPosition.height * nextPercent) {\n      dropType = \"after\";\n    } else if (dropInner) {\n      dropType = \"inner\";\n    } else {\n      dropType = \"none\";\n    }\n    const iconPosition = dropNode.$el.querySelector(`.${ns.be(\"node\", \"expand-icon\")}`).getBoundingClientRect();\n    const dropIndicator = dropIndicator$.value;\n    if (dropType === \"before\") {\n      indicatorTop = iconPosition.top - treePosition.top;\n    } else if (dropType === \"after\") {\n      indicatorTop = iconPosition.bottom - treePosition.top;\n    }\n    dropIndicator.style.top = `${indicatorTop}px`;\n    dropIndicator.style.left = `${iconPosition.right - treePosition.left}px`;\n    if (dropType === \"inner\") {\n      addClass(dropNode.$el, ns.is(\"drop-inner\"));\n    } else {\n      removeClass(dropNode.$el, ns.is(\"drop-inner\"));\n    }\n    dragState.value.showDropIndicator = dropType === \"before\" || dropType === \"after\";\n    dragState.value.allowDrop = dragState.value.showDropIndicator || userAllowDropInner;\n    dragState.value.dropType = dropType;\n    ctx.emit(\"node-drag-over\", draggingNode.node, dropNode.node, event);\n  };\n  const treeNodeDragEnd = event => {\n    const {\n      draggingNode,\n      dropType,\n      dropNode\n    } = dragState.value;\n    event.preventDefault();\n    if (event.dataTransfer) {\n      event.dataTransfer.dropEffect = \"move\";\n    }\n    if (draggingNode && dropNode) {\n      const draggingNodeCopy = {\n        data: draggingNode.node.data\n      };\n      if (dropType !== \"none\") {\n        draggingNode.node.remove();\n      }\n      if (dropType === \"before\") {\n        dropNode.node.parent.insertBefore(draggingNodeCopy, dropNode.node);\n      } else if (dropType === \"after\") {\n        dropNode.node.parent.insertAfter(draggingNodeCopy, dropNode.node);\n      } else if (dropType === \"inner\") {\n        dropNode.node.insertChild(draggingNodeCopy);\n      }\n      if (dropType !== \"none\") {\n        store.value.registerNode(draggingNodeCopy);\n        if (store.value.key) {\n          draggingNode.node.eachNode(node => {\n            var _a;\n            (_a = store.value.nodesMap[node.data[store.value.key]]) == null ? void 0 : _a.setChecked(node.checked, !store.value.checkStrictly);\n          });\n        }\n      }\n      removeClass(dropNode.$el, ns.is(\"drop-inner\"));\n      ctx.emit(\"node-drag-end\", draggingNode.node, dropNode.node, dropType, event);\n      if (dropType !== \"none\") {\n        ctx.emit(\"node-drop\", draggingNode.node, dropNode.node, dropType, event);\n      }\n    }\n    if (draggingNode && !dropNode) {\n      ctx.emit(\"node-drag-end\", draggingNode.node, null, dropType, event);\n    }\n    dragState.value.showDropIndicator = false;\n    dragState.value.draggingNode = null;\n    dragState.value.dropNode = null;\n    dragState.value.allowDrop = true;\n  };\n  provide(dragEventsKey, {\n    treeNodeDragStart,\n    treeNodeDragOver,\n    treeNodeDragEnd\n  });\n  return {\n    dragState\n  };\n}\nexport { dragEventsKey, useDragNodeHandler };", "map": {"version": 3, "names": ["dragEventsKey", "Symbol", "useDragNodeHandler", "props", "ctx", "el$", "dropIndicator$", "store", "ns", "useNamespace", "dragState", "ref", "showDropIndicator", "draggingNode", "dropNode", "allowDrop", "dropType", "treeNodeDragStart", "event", "treeNode", "isFunction", "allowDrag", "node", "preventDefault", "dataTransfer", "effectAllowed", "setData", "e", "value", "emit", "treeNodeDragOver", "oldDropNode", "id", "removeClass", "$el", "is", "dropPrev", "dropInner", "dropNext", "userAllowDropInner", "dropEffect", "nextS<PERSON>ling", "previousSibling", "contains", "targetPosition", "querySelector", "be", "getBoundingClientRect", "treePosition", "prevPercent", "nextPercent", "indicatorTop", "distance", "clientY", "top", "height", "iconPosition", "dropIndicator", "bottom", "style", "left", "right", "addClass", "treeNodeDragEnd", "draggingNodeCopy", "data", "remove", "parent", "insertBefore", "insertAfter", "<PERSON><PERSON><PERSON><PERSON>", "registerNode", "key", "eachNode", "_a", "nodesMap", "setChecked", "checked", "checkStrictly", "provide"], "sources": ["../../../../../../../packages/components/tree/src/model/useDragNode.ts"], "sourcesContent": ["// @ts-nocheck\nimport { provide, ref } from 'vue'\nimport { addClass, isFunction, removeClass } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport type { InjectionKey } from 'vue'\nimport type Node from './node'\nimport type { NodeDropType } from '../tree.type'\n\ninterface TreeNode {\n  node: Node\n  $el?: HTMLElement\n}\n\ninterface DragOptions {\n  event: DragEvent\n  treeNode: TreeNode\n}\n\nexport interface DragEvents {\n  treeNodeDragStart: (options: DragOptions) => void\n  treeNodeDragOver: (options: DragOptions) => void\n  treeNodeDragEnd: (event: DragEvent) => void\n}\n\nexport const dragEventsKey: InjectionKey<DragEvents> = Symbol('dragEvents')\n\nexport function useDragNodeHandler({ props, ctx, el$, dropIndicator$, store }) {\n  const ns = useNamespace('tree')\n  const dragState = ref({\n    showDropIndicator: false,\n    draggingNode: null,\n    dropNode: null,\n    allowDrop: true,\n    dropType: null,\n  })\n\n  const treeNodeDragStart = ({ event, treeNode }: DragOptions) => {\n    if (isFunction(props.allowDrag) && !props.allowDrag(treeNode.node)) {\n      event.preventDefault()\n      return false\n    }\n    event.dataTransfer.effectAllowed = 'move'\n\n    // wrap in try catch to address IE's error when first param is 'text/plain'\n    try {\n      // setData is required for draggable to work in FireFox\n      // the content has to be '' so dragging a node out of the tree won't open a new tab in FireFox\n      event.dataTransfer.setData('text/plain', '')\n    } catch {}\n    dragState.value.draggingNode = treeNode\n    ctx.emit('node-drag-start', treeNode.node, event)\n  }\n\n  const treeNodeDragOver = ({ event, treeNode }: DragOptions) => {\n    const dropNode = treeNode\n    const oldDropNode = dragState.value.dropNode\n    if (oldDropNode && oldDropNode.node.id !== dropNode.node.id) {\n      removeClass(oldDropNode.$el, ns.is('drop-inner'))\n    }\n    const draggingNode = dragState.value.draggingNode\n    if (!draggingNode || !dropNode) return\n\n    let dropPrev = true\n    let dropInner = true\n    let dropNext = true\n    let userAllowDropInner = true\n    if (isFunction(props.allowDrop)) {\n      dropPrev = props.allowDrop(draggingNode.node, dropNode.node, 'prev')\n      userAllowDropInner = dropInner = props.allowDrop(\n        draggingNode.node,\n        dropNode.node,\n        'inner'\n      )\n      dropNext = props.allowDrop(draggingNode.node, dropNode.node, 'next')\n    }\n    event.dataTransfer.dropEffect =\n      dropInner || dropPrev || dropNext ? 'move' : 'none'\n    if (\n      (dropPrev || dropInner || dropNext) &&\n      oldDropNode?.node.id !== dropNode.node.id\n    ) {\n      if (oldDropNode) {\n        ctx.emit('node-drag-leave', draggingNode.node, oldDropNode.node, event)\n      }\n      ctx.emit('node-drag-enter', draggingNode.node, dropNode.node, event)\n    }\n\n    if (dropPrev || dropInner || dropNext) {\n      dragState.value.dropNode = dropNode\n    } else {\n      // Reset dragState.value.dropNode to null when allowDrop is transfer from true to false.(For issue #14704)\n      dragState.value.dropNode = null\n    }\n\n    if (dropNode.node.nextSibling === draggingNode.node) {\n      dropNext = false\n    }\n    if (dropNode.node.previousSibling === draggingNode.node) {\n      dropPrev = false\n    }\n    if (dropNode.node.contains(draggingNode.node, false)) {\n      dropInner = false\n    }\n    if (\n      draggingNode.node === dropNode.node ||\n      draggingNode.node.contains(dropNode.node)\n    ) {\n      dropPrev = false\n      dropInner = false\n      dropNext = false\n    }\n\n    // find target node without children, just calc content node height\n    const targetPosition = dropNode.$el\n      .querySelector(`.${ns.be('node', 'content')}`)\n      .getBoundingClientRect()\n    const treePosition = el$.value.getBoundingClientRect()\n\n    let dropType: NodeDropType\n    const prevPercent = dropPrev ? (dropInner ? 0.25 : dropNext ? 0.45 : 1) : -1\n    const nextPercent = dropNext ? (dropInner ? 0.75 : dropPrev ? 0.55 : 0) : 1\n\n    let indicatorTop = -9999\n    const distance = event.clientY - targetPosition.top\n    if (distance < targetPosition.height * prevPercent) {\n      dropType = 'before'\n    } else if (distance > targetPosition.height * nextPercent) {\n      dropType = 'after'\n    } else if (dropInner) {\n      dropType = 'inner'\n    } else {\n      dropType = 'none'\n    }\n\n    const iconPosition = dropNode.$el\n      .querySelector(`.${ns.be('node', 'expand-icon')}`)\n      .getBoundingClientRect()\n    const dropIndicator = dropIndicator$.value\n    if (dropType === 'before') {\n      indicatorTop = iconPosition.top - treePosition.top\n    } else if (dropType === 'after') {\n      indicatorTop = iconPosition.bottom - treePosition.top\n    }\n    dropIndicator.style.top = `${indicatorTop}px`\n    dropIndicator.style.left = `${iconPosition.right - treePosition.left}px`\n\n    if (dropType === 'inner') {\n      addClass(dropNode.$el, ns.is('drop-inner'))\n    } else {\n      removeClass(dropNode.$el, ns.is('drop-inner'))\n    }\n\n    dragState.value.showDropIndicator =\n      dropType === 'before' || dropType === 'after'\n    dragState.value.allowDrop =\n      dragState.value.showDropIndicator || userAllowDropInner\n    dragState.value.dropType = dropType\n    ctx.emit('node-drag-over', draggingNode.node, dropNode.node, event)\n  }\n\n  const treeNodeDragEnd = (event: DragEvent) => {\n    const { draggingNode, dropType, dropNode } = dragState.value\n    event.preventDefault()\n\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1911486\n    if (event.dataTransfer) {\n      event.dataTransfer.dropEffect = 'move'\n    }\n\n    if (draggingNode && dropNode) {\n      const draggingNodeCopy = { data: draggingNode.node.data }\n      if (dropType !== 'none') {\n        draggingNode.node.remove()\n      }\n      if (dropType === 'before') {\n        dropNode.node.parent.insertBefore(draggingNodeCopy, dropNode.node)\n      } else if (dropType === 'after') {\n        dropNode.node.parent.insertAfter(draggingNodeCopy, dropNode.node)\n      } else if (dropType === 'inner') {\n        dropNode.node.insertChild(draggingNodeCopy)\n      }\n      if (dropType !== 'none') {\n        store.value.registerNode(draggingNodeCopy)\n        if (store.value.key) {\n          //restore checkbox state after dragging\n          draggingNode.node.eachNode((node) => {\n            store.value.nodesMap[node.data[store.value.key]]?.setChecked(\n              node.checked,\n              !store.value.checkStrictly\n            )\n          })\n        }\n      }\n\n      removeClass(dropNode.$el, ns.is('drop-inner'))\n\n      ctx.emit(\n        'node-drag-end',\n        draggingNode.node,\n        dropNode.node,\n        dropType,\n        event\n      )\n      if (dropType !== 'none') {\n        ctx.emit('node-drop', draggingNode.node, dropNode.node, dropType, event)\n      }\n    }\n    if (draggingNode && !dropNode) {\n      ctx.emit('node-drag-end', draggingNode.node, null, dropType, event)\n    }\n\n    dragState.value.showDropIndicator = false\n    dragState.value.draggingNode = null\n    dragState.value.dropNode = null\n    dragState.value.allowDrop = true\n  }\n\n  provide(dragEventsKey, {\n    treeNodeDragStart,\n    treeNodeDragOver,\n    treeNodeDragEnd,\n  })\n\n  return {\n    dragState,\n  }\n}\n"], "mappings": ";;;;AAGY,MAACA,aAAa,GAAGC,MAAM,CAAC,YAAY;AACzC,SAASC,kBAAkBA,CAAC;EAAEC,KAAK;EAAEC,GAAG;EAAEC,GAAG;EAAEC,cAAc;EAAEC;AAAK,CAAE,EAAE;EAC7E,MAAMC,EAAE,GAAGC,YAAY,CAAC,MAAM,CAAC;EAC/B,MAAMC,SAAS,GAAGC,GAAG,CAAC;IACpBC,iBAAiB,EAAE,KAAK;IACxBC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE;EACd,CAAG,CAAC;EACF,MAAMC,iBAAiB,GAAGA,CAAC;IAAEC,KAAK;IAAEC;EAAQ,CAAE,KAAK;IACjD,IAAIC,UAAU,CAACjB,KAAK,CAACkB,SAAS,CAAC,IAAI,CAAClB,KAAK,CAACkB,SAAS,CAACF,QAAQ,CAACG,IAAI,CAAC,EAAE;MAClEJ,KAAK,CAACK,cAAc,EAAE;MACtB,OAAO,KAAK;IAClB;IACIL,KAAK,CAACM,YAAY,CAACC,aAAa,GAAG,MAAM;IACzC,IAAI;MACFP,KAAK,CAACM,YAAY,CAACE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;IAClD,CAAK,CAAC,OAAOC,CAAC,EAAE,CAChB;IACIjB,SAAS,CAACkB,KAAK,CAACf,YAAY,GAAGM,QAAQ;IACvCf,GAAG,CAACyB,IAAI,CAAC,iBAAiB,EAAEV,QAAQ,CAACG,IAAI,EAAEJ,KAAK,CAAC;EACrD,CAAG;EACD,MAAMY,gBAAgB,GAAGA,CAAC;IAAEZ,KAAK;IAAEC;EAAQ,CAAE,KAAK;IAChD,MAAML,QAAQ,GAAGK,QAAQ;IACzB,MAAMY,WAAW,GAAGrB,SAAS,CAACkB,KAAK,CAACd,QAAQ;IAC5C,IAAIiB,WAAW,IAAIA,WAAW,CAACT,IAAI,CAACU,EAAE,KAAKlB,QAAQ,CAACQ,IAAI,CAACU,EAAE,EAAE;MAC3DC,WAAW,CAACF,WAAW,CAACG,GAAG,EAAE1B,EAAE,CAAC2B,EAAE,CAAC,YAAY,CAAC,CAAC;IACvD;IACI,MAAMtB,YAAY,GAAGH,SAAS,CAACkB,KAAK,CAACf,YAAY;IACjD,IAAI,CAACA,YAAY,IAAI,CAACC,QAAQ,EAC5B;IACF,IAAIsB,QAAQ,GAAG,IAAI;IACnB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,kBAAkB,GAAG,IAAI;IAC7B,IAAInB,UAAU,CAACjB,KAAK,CAACY,SAAS,CAAC,EAAE;MAC/BqB,QAAQ,GAAGjC,KAAK,CAACY,SAAS,CAACF,YAAY,CAACS,IAAI,EAAER,QAAQ,CAACQ,IAAI,EAAE,MAAM,CAAC;MACpEiB,kBAAkB,GAAGF,SAAS,GAAGlC,KAAK,CAACY,SAAS,CAACF,YAAY,CAACS,IAAI,EAAER,QAAQ,CAACQ,IAAI,EAAE,OAAO,CAAC;MAC3FgB,QAAQ,GAAGnC,KAAK,CAACY,SAAS,CAACF,YAAY,CAACS,IAAI,EAAER,QAAQ,CAACQ,IAAI,EAAE,MAAM,CAAC;IAC1E;IACIJ,KAAK,CAACM,YAAY,CAACgB,UAAU,GAAGH,SAAS,IAAID,QAAQ,IAAIE,QAAQ,GAAG,MAAM,GAAG,MAAM;IACnF,IAAI,CAACF,QAAQ,IAAIC,SAAS,IAAIC,QAAQ,KAAK,CAACP,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACT,IAAI,CAACU,EAAE,MAAMlB,QAAQ,CAACQ,IAAI,CAACU,EAAE,EAAE;MACpH,IAAID,WAAW,EAAE;QACf3B,GAAG,CAACyB,IAAI,CAAC,iBAAiB,EAAEhB,YAAY,CAACS,IAAI,EAAES,WAAW,CAACT,IAAI,EAAEJ,KAAK,CAAC;MAC/E;MACMd,GAAG,CAACyB,IAAI,CAAC,iBAAiB,EAAEhB,YAAY,CAACS,IAAI,EAAER,QAAQ,CAACQ,IAAI,EAAEJ,KAAK,CAAC;IAC1E;IACI,IAAIkB,QAAQ,IAAIC,SAAS,IAAIC,QAAQ,EAAE;MACrC5B,SAAS,CAACkB,KAAK,CAACd,QAAQ,GAAGA,QAAQ;IACzC,CAAK,MAAM;MACLJ,SAAS,CAACkB,KAAK,CAACd,QAAQ,GAAG,IAAI;IACrC;IACI,IAAIA,QAAQ,CAACQ,IAAI,CAACmB,WAAW,KAAK5B,YAAY,CAACS,IAAI,EAAE;MACnDgB,QAAQ,GAAG,KAAK;IACtB;IACI,IAAIxB,QAAQ,CAACQ,IAAI,CAACoB,eAAe,KAAK7B,YAAY,CAACS,IAAI,EAAE;MACvDc,QAAQ,GAAG,KAAK;IACtB;IACI,IAAItB,QAAQ,CAACQ,IAAI,CAACqB,QAAQ,CAAC9B,YAAY,CAACS,IAAI,EAAE,KAAK,CAAC,EAAE;MACpDe,SAAS,GAAG,KAAK;IACvB;IACI,IAAIxB,YAAY,CAACS,IAAI,KAAKR,QAAQ,CAACQ,IAAI,IAAIT,YAAY,CAACS,IAAI,CAACqB,QAAQ,CAAC7B,QAAQ,CAACQ,IAAI,CAAC,EAAE;MACpFc,QAAQ,GAAG,KAAK;MAChBC,SAAS,GAAG,KAAK;MACjBC,QAAQ,GAAG,KAAK;IACtB;IACI,MAAMM,cAAc,GAAG9B,QAAQ,CAACoB,GAAG,CAACW,aAAa,CAAC,IAAIrC,EAAE,CAACsC,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC,CAACC,qBAAqB,EAAE;IACzG,MAAMC,YAAY,GAAG3C,GAAG,CAACuB,KAAK,CAACmB,qBAAqB,EAAE;IACtD,IAAI/B,QAAQ;IACZ,MAAMiC,WAAW,GAAGb,QAAQ,GAAGC,SAAS,GAAG,IAAI,GAAGC,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1E,MAAMY,WAAW,GAAGZ,QAAQ,GAAGD,SAAS,GAAG,IAAI,GAAGD,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC;IACzE,IAAIe,YAAY,GAAG,CAAC,IAAI;IACxB,MAAMC,QAAQ,GAAGlC,KAAK,CAACmC,OAAO,GAAGT,cAAc,CAACU,GAAG;IACnD,IAAIF,QAAQ,GAAGR,cAAc,CAACW,MAAM,GAAGN,WAAW,EAAE;MAClDjC,QAAQ,GAAG,QAAQ;IACzB,CAAK,MAAM,IAAIoC,QAAQ,GAAGR,cAAc,CAACW,MAAM,GAAGL,WAAW,EAAE;MACzDlC,QAAQ,GAAG,OAAO;IACxB,CAAK,MAAM,IAAIqB,SAAS,EAAE;MACpBrB,QAAQ,GAAG,OAAO;IACxB,CAAK,MAAM;MACLA,QAAQ,GAAG,MAAM;IACvB;IACI,MAAMwC,YAAY,GAAG1C,QAAQ,CAACoB,GAAG,CAACW,aAAa,CAAC,IAAIrC,EAAE,CAACsC,EAAE,CAAC,MAAM,EAAE,aAAa,CAAC,EAAE,CAAC,CAACC,qBAAqB,EAAE;IAC3G,MAAMU,aAAa,GAAGnD,cAAc,CAACsB,KAAK;IAC1C,IAAIZ,QAAQ,KAAK,QAAQ,EAAE;MACzBmC,YAAY,GAAGK,YAAY,CAACF,GAAG,GAAGN,YAAY,CAACM,GAAG;IACxD,CAAK,MAAM,IAAItC,QAAQ,KAAK,OAAO,EAAE;MAC/BmC,YAAY,GAAGK,YAAY,CAACE,MAAM,GAAGV,YAAY,CAACM,GAAG;IAC3D;IACIG,aAAa,CAACE,KAAK,CAACL,GAAG,GAAG,GAAGH,YAAY,IAAI;IAC7CM,aAAa,CAACE,KAAK,CAACC,IAAI,GAAG,GAAGJ,YAAY,CAACK,KAAK,GAAGb,YAAY,CAACY,IAAI,IAAI;IACxE,IAAI5C,QAAQ,KAAK,OAAO,EAAE;MACxB8C,QAAQ,CAAChD,QAAQ,CAACoB,GAAG,EAAE1B,EAAE,CAAC2B,EAAE,CAAC,YAAY,CAAC,CAAC;IACjD,CAAK,MAAM;MACLF,WAAW,CAACnB,QAAQ,CAACoB,GAAG,EAAE1B,EAAE,CAAC2B,EAAE,CAAC,YAAY,CAAC,CAAC;IACpD;IACIzB,SAAS,CAACkB,KAAK,CAAChB,iBAAiB,GAAGI,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,OAAO;IACjFN,SAAS,CAACkB,KAAK,CAACb,SAAS,GAAGL,SAAS,CAACkB,KAAK,CAAChB,iBAAiB,IAAI2B,kBAAkB;IACnF7B,SAAS,CAACkB,KAAK,CAACZ,QAAQ,GAAGA,QAAQ;IACnCZ,GAAG,CAACyB,IAAI,CAAC,gBAAgB,EAAEhB,YAAY,CAACS,IAAI,EAAER,QAAQ,CAACQ,IAAI,EAAEJ,KAAK,CAAC;EACvE,CAAG;EACD,MAAM6C,eAAe,GAAI7C,KAAK,IAAK;IACjC,MAAM;MAAEL,YAAY;MAAEG,QAAQ;MAAEF;IAAQ,CAAE,GAAGJ,SAAS,CAACkB,KAAK;IAC5DV,KAAK,CAACK,cAAc,EAAE;IACtB,IAAIL,KAAK,CAACM,YAAY,EAAE;MACtBN,KAAK,CAACM,YAAY,CAACgB,UAAU,GAAG,MAAM;IAC5C;IACI,IAAI3B,YAAY,IAAIC,QAAQ,EAAE;MAC5B,MAAMkD,gBAAgB,GAAG;QAAEC,IAAI,EAAEpD,YAAY,CAACS,IAAI,CAAC2C;MAAI,CAAE;MACzD,IAAIjD,QAAQ,KAAK,MAAM,EAAE;QACvBH,YAAY,CAACS,IAAI,CAAC4C,MAAM,EAAE;MAClC;MACM,IAAIlD,QAAQ,KAAK,QAAQ,EAAE;QACzBF,QAAQ,CAACQ,IAAI,CAAC6C,MAAM,CAACC,YAAY,CAACJ,gBAAgB,EAAElD,QAAQ,CAACQ,IAAI,CAAC;MAC1E,CAAO,MAAM,IAAIN,QAAQ,KAAK,OAAO,EAAE;QAC/BF,QAAQ,CAACQ,IAAI,CAAC6C,MAAM,CAACE,WAAW,CAACL,gBAAgB,EAAElD,QAAQ,CAACQ,IAAI,CAAC;MACzE,CAAO,MAAM,IAAIN,QAAQ,KAAK,OAAO,EAAE;QAC/BF,QAAQ,CAACQ,IAAI,CAACgD,WAAW,CAACN,gBAAgB,CAAC;MACnD;MACM,IAAIhD,QAAQ,KAAK,MAAM,EAAE;QACvBT,KAAK,CAACqB,KAAK,CAAC2C,YAAY,CAACP,gBAAgB,CAAC;QAC1C,IAAIzD,KAAK,CAACqB,KAAK,CAAC4C,GAAG,EAAE;UACnB3D,YAAY,CAACS,IAAI,CAACmD,QAAQ,CAAEnD,IAAI,IAAK;YACnC,IAAIoD,EAAE;YACN,CAACA,EAAE,GAAGnE,KAAK,CAACqB,KAAK,CAAC+C,QAAQ,CAACrD,IAAI,CAAC2C,IAAI,CAAC1D,KAAK,CAACqB,KAAK,CAAC4C,GAAG,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,EAAE,CAACE,UAAU,CAACtD,IAAI,CAACuD,OAAO,EAAE,CAACtE,KAAK,CAACqB,KAAK,CAACkD,aAAa,CAAC;UAC9I,CAAW,CAAC;QACZ;MACA;MACM7C,WAAW,CAACnB,QAAQ,CAACoB,GAAG,EAAE1B,EAAE,CAAC2B,EAAE,CAAC,YAAY,CAAC,CAAC;MAC9C/B,GAAG,CAACyB,IAAI,CAAC,eAAe,EAAEhB,YAAY,CAACS,IAAI,EAAER,QAAQ,CAACQ,IAAI,EAAEN,QAAQ,EAAEE,KAAK,CAAC;MAC5E,IAAIF,QAAQ,KAAK,MAAM,EAAE;QACvBZ,GAAG,CAACyB,IAAI,CAAC,WAAW,EAAEhB,YAAY,CAACS,IAAI,EAAER,QAAQ,CAACQ,IAAI,EAAEN,QAAQ,EAAEE,KAAK,CAAC;MAChF;IACA;IACI,IAAIL,YAAY,IAAI,CAACC,QAAQ,EAAE;MAC7BV,GAAG,CAACyB,IAAI,CAAC,eAAe,EAAEhB,YAAY,CAACS,IAAI,EAAE,IAAI,EAAEN,QAAQ,EAAEE,KAAK,CAAC;IACzE;IACIR,SAAS,CAACkB,KAAK,CAAChB,iBAAiB,GAAG,KAAK;IACzCF,SAAS,CAACkB,KAAK,CAACf,YAAY,GAAG,IAAI;IACnCH,SAAS,CAACkB,KAAK,CAACd,QAAQ,GAAG,IAAI;IAC/BJ,SAAS,CAACkB,KAAK,CAACb,SAAS,GAAG,IAAI;EACpC,CAAG;EACDgE,OAAO,CAAC/E,aAAa,EAAE;IACrBiB,iBAAiB;IACjBa,gBAAgB;IAChBiC;EACJ,CAAG,CAAC;EACF,OAAO;IACLrD;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}