{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, renderSlot, createTextVNode, toDisplayString, createCommentVNode, createElementVNode, normalizeStyle } from 'vue';\nimport { statisticProps } from './statistic.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isFunction } from '@vue/shared';\nimport { isNumber } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElStatistic\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: statisticProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"statistic\");\n    const displayValue = computed(() => {\n      const {\n        value,\n        formatter,\n        precision,\n        decimalSeparator,\n        groupSeparator\n      } = props;\n      if (isFunction(formatter)) return formatter(value);\n      if (!isNumber(value) || Number.isNaN(value)) return value;\n      let [integer, decimal = \"\"] = String(value).split(\".\");\n      decimal = decimal.padEnd(precision, \"0\").slice(0, precision > 0 ? precision : 0);\n      integer = integer.replace(/\\B(?=(\\d{3})+(?!\\d))/g, groupSeparator);\n      return [integer, decimal].join(decimal ? decimalSeparator : \"\");\n    });\n    expose({\n      displayValue\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(ns).b())\n      }, [_ctx.$slots.title || _ctx.title ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"head\"))\n      }, [renderSlot(_ctx.$slots, \"title\", {}, () => [createTextVNode(toDisplayString(_ctx.title), 1)])], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"content\"))\n      }, [_ctx.$slots.prefix || _ctx.prefix ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"prefix\"))\n      }, [renderSlot(_ctx.$slots, \"prefix\", {}, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.prefix), 1)])], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"span\", {\n        class: normalizeClass(unref(ns).e(\"number\")),\n        style: normalizeStyle(_ctx.valueStyle)\n      }, toDisplayString(unref(displayValue)), 7), _ctx.$slots.suffix || _ctx.suffix ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"suffix\"))\n      }, [renderSlot(_ctx.$slots, \"suffix\", {}, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.suffix), 1)])], 2)) : createCommentVNode(\"v-if\", true)], 2)], 2);\n    };\n  }\n});\nvar Statistic = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"statistic.vue\"]]);\nexport { Statistic as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace", "displayValue", "computed", "value", "formatter", "precision", "decimalSeparator", "groupSeparator", "props", "isFunction", "isNumber", "Number", "isNaN", "integer", "decimal", "String", "split", "padEnd", "slice", "replace", "join", "expose", "_ctx", "_cache"], "sources": ["../../../../../../packages/components/statistic/src/statistic.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <div v-if=\"$slots.title || title\" :class=\"ns.e('head')\">\n      <slot name=\"title\">\n        {{ title }}\n      </slot>\n    </div>\n    <div :class=\"ns.e('content')\">\n      <div v-if=\"$slots.prefix || prefix\" :class=\"ns.e('prefix')\">\n        <slot name=\"prefix\">\n          <span>{{ prefix }}</span>\n        </slot>\n      </div>\n      <span :class=\"ns.e('number')\" :style=\"valueStyle\">\n        {{ displayValue }}\n      </span>\n      <div v-if=\"$slots.suffix || suffix\" :class=\"ns.e('suffix')\">\n        <slot name=\"suffix\">\n          <span>{{ suffix }}</span>\n        </slot>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isFunction, isNumber } from '@element-plus/utils'\nimport { statisticProps } from './statistic'\n\ndefineOptions({\n  name: 'ElStatistic',\n})\n\nconst props = defineProps(statisticProps)\nconst ns = useNamespace('statistic')\n\nconst displayValue = computed(() => {\n  const { value, formatter, precision, decimalSeparator, groupSeparator } =\n    props\n\n  if (isFunction(formatter)) return formatter(value)\n\n  // https://github.com/element-plus/element-plus/issues/17784\n  if (!isNumber(value) || Number.isNaN(value)) return value\n\n  let [integer, decimal = ''] = String(value).split('.')\n  decimal = decimal\n    .padEnd(precision, '0')\n    .slice(0, precision > 0 ? precision : 0)\n  integer = integer.replace(/\\B(?=(\\d{3})+(?!\\d))/g, groupSeparator)\n  return [integer, decimal].join(decimal ? decimalSeparator : '')\n})\n\ndefineExpose({\n  /**\n   * @description current display value\n   */\n  displayValue,\n})\n</script>\n"], "mappings": ";;;;;;mCA+Bc;EACZA,IAAM;AACR;;;;;;;;IAGM,MAAAC,EAAA,GAAKC,YAAA,CAAa,WAAW;IAE7B,MAAAC,YAAA,GAAeC,QAAA,CAAS,MAAM;MAClC,MAAM;QAAEC,KAAO;QAAAC,SAAA;QAAWC,SAAW;QAAAC,gBAAA;QAAkBC;MAAA,CACrD,GAAAC,KAAA;MAEF,IAAIC,UAAW,CAAAL,SAAS,CAAG,EAGvB,OAAAA,SAAe,CAAAD,KAAK;MAEpB,KAACO,QAAA,CAASP,KAAU,KAAAQ,MAAM,CAAOC,KAAA,CAAAT,KAAK,CAAE,EAClC,OAAAA,KAAA;MAGA,KAAAU,OAAA,EAAAC,OAAgB,SAAAC,MAAA,CAAAZ,KAAA,EAAAa,KAAA,IAAuC;MACjEF,OAAO,GAAUA,OAAA,CAAAG,MAAO,CAAAZ,SAAO,OAAUa,KAAA,IAAAb,SAAA,GAAqB,IAAAA,SAAA;MAC/DQ,OAAA,GAAAA,OAAA,CAAAM,OAAA,0BAAAZ,cAAA;MAEY,QAAAM,OAAA,EAAAC,OAAA,EAAAM,IAAA,CAAAN,OAAA,GAAAR,gBAAA;IAAA;IAAAe,MAAA;MAAApB;IAAA,CAIX;IACF,OAAC,CAAAqB,IAAA,EAAAC,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}