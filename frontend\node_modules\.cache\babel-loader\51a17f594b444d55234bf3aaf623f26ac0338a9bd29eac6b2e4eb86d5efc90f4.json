{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createBlock as _createBlock, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"reservation-detail\"\n};\nconst _hoisted_2 = {\n  class: \"page-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-left\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"loading-container\"\n};\nconst _hoisted_5 = {\n  key: 1,\n  class: \"empty-container\"\n};\nconst _hoisted_6 = {\n  key: 2,\n  class: \"detail-content\"\n};\nconst _hoisted_7 = {\n  class: \"card-header\"\n};\nconst _hoisted_8 = {\n  class: \"duration\"\n};\nconst _hoisted_9 = {\n  class: \"action-buttons\"\n};\nconst _hoisted_10 = {\n  class: \"cancel-dialog\"\n};\nconst _hoisted_11 = {\n  class: \"cancel-warning\"\n};\nconst _hoisted_12 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n    onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.back()),\n    icon: $setup.ArrowLeft\n  }, {\n    default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"返回\")])),\n    _: 1 /* STABLE */,\n    __: [3]\n  }, 8 /* PROPS */, [\"icon\"]), _cache[4] || (_cache[4] = _createElementVNode(\"h2\", null, \"预约详情\", -1 /* HOISTED */))])]), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createVNode(_component_el_skeleton, {\n    rows: 10,\n    animated: \"\"\n  })])) : !$setup.reservation ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_el_empty, {\n    description: \"未找到预约信息\"\n  })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createVNode(_component_el_card, {\n    shadow: \"hover\",\n    class: \"detail-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_cache[5] || (_cache[5] = _createElementVNode(\"h3\", null, \"基本信息\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n      type: $setup.getReservationStatusType($setup.reservation.status)\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getReservationStatusText($setup.reservation.status)), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_descriptions, {\n      column: 2,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"预约号\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.reservation.id), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"创建时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.reservation.created_at)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"预约时间\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.reservation.start_time)) + \" 至 \" + _toDisplayString($setup.formatDateTime($setup.reservation.end_time)) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_8, \" (\" + _toDisplayString($setup.calculateDuration($setup.reservation.start_time, $setup.reservation.end_time)) + \") \", 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), $setup.reservation.check_in_time ? (_openBlock(), _createBlock(_component_el_descriptions_item, {\n        key: 0,\n        label: \"签到时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.reservation.check_in_time)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), $setup.reservation.check_out_time ? (_openBlock(), _createBlock(_component_el_descriptions_item, {\n        key: 1,\n        label: \"签退时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.reservation.check_out_time)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), $setup.reservation.status === 'cancelled' && $setup.reservation.cancel_reason ? (_openBlock(), _createBlock(_component_el_descriptions_item, {\n        key: 2,\n        label: \"取消原因\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.reservation.cancel_reason), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_card, {\n    shadow: \"hover\",\n    class: \"detail-card\"\n  }, {\n    header: _withCtx(() => _cache[6] || (_cache[6] = [_createElementVNode(\"div\", {\n      class: \"card-header\"\n    }, [_createElementVNode(\"h3\", null, \"座位信息\")], -1 /* HOISTED */)])),\n    default: _withCtx(() => [_createVNode(_component_el_descriptions, {\n      column: 2,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"自习室\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.reservation.seat?.room?.name), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"位置\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.reservation.seat?.room?.location), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"座位号\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.reservation.seat?.seat_number), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"座位类型\"\n      }, {\n        default: _withCtx(() => [$setup.reservation.seat?.is_window_seat ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 0,\n          size: \"small\",\n          effect: \"plain\",\n          style: {\n            \"margin-right\": \"5px\"\n          }\n        }, {\n          default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\" 靠窗 \")])),\n          _: 1 /* STABLE */,\n          __: [7]\n        })) : _createCommentVNode(\"v-if\", true), $setup.reservation.seat?.is_power_outlet ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 1,\n          size: \"small\",\n          effect: \"plain\"\n        }, {\n          default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\" 有电源 \")])),\n          _: 1 /* STABLE */,\n          __: [8]\n        })) : _createCommentVNode(\"v-if\", true)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"座位描述\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.reservation.seat?.description || \"无\"), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_9, [$setup.reservation.status === 'pending' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleCheckIn\n  }, {\n    default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"签到\")])),\n    _: 1 /* STABLE */,\n    __: [9]\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    type: \"danger\",\n    onClick: $setup.handleCancel\n  }, {\n    default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"取消预约\")])),\n    _: 1 /* STABLE */,\n    __: [10]\n  }, 8 /* PROPS */, [\"onClick\"])], 64 /* STABLE_FRAGMENT */)) : $setup.reservation.status === 'checked_in' ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 1,\n    type: \"success\",\n    onClick: $setup.handleCheckOut\n  }, {\n    default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"签退\")])),\n    _: 1 /* STABLE */,\n    __: [11]\n  }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])])), _createCommentVNode(\" 取消预约对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.cancelDialogVisible,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.cancelDialogVisible = $event),\n    title: \"取消预约\",\n    width: \"400px\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_10, [_cache[14] || (_cache[14] = _createElementVNode(\"p\", null, \"您确定要取消此预约吗？\", -1 /* HOISTED */)), _createElementVNode(\"p\", null, \"座位号: \" + _toDisplayString($setup.reservation?.seat?.seat_number), 1 /* TEXT */), _createElementVNode(\"p\", null, \" 预约时间: \" + _toDisplayString($setup.formatDateTime($setup.reservation?.start_time)) + \" - \" + _toDisplayString($setup.formatDateTime($setup.reservation?.end_time)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_el_alert, {\n      title: \"取消预约提示\",\n      type: \"warning\",\n      description: \"距离预约开始时间不足30分钟取消，可能会影响您的信誉分。\",\n      \"show-icon\": \"\",\n      closable: false\n    })]), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_button, {\n      onClick: _cache[1] || (_cache[1] = $event => $setup.cancelDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"返回\")])),\n      _: 1 /* STABLE */,\n      __: [12]\n    }), _createVNode(_component_el_button, {\n      type: \"danger\",\n      onClick: $setup.confirmCancel,\n      loading: $setup.processing\n    }, {\n      default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"确认取消\")])),\n      _: 1 /* STABLE */,\n      __: [13]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_button", "onClick", "_cache", "$event", "_ctx", "$router", "back", "icon", "$setup", "ArrowLeft", "default", "_withCtx", "_createTextVNode", "_", "__", "loading", "_hoisted_4", "_component_el_skeleton", "rows", "animated", "reservation", "_hoisted_5", "_component_el_empty", "description", "_hoisted_6", "_component_el_card", "shadow", "header", "_hoisted_7", "_component_el_tag", "type", "getReservationStatusType", "status", "_toDisplayString", "getReservationStatusText", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "label", "id", "formatDateTime", "created_at", "span", "start_time", "end_time", "_hoisted_8", "calculateDuration", "check_in_time", "_createBlock", "_createCommentVNode", "check_out_time", "cancel_reason", "seat", "room", "name", "location", "seat_number", "is_window_seat", "size", "effect", "style", "is_power_outlet", "_hoisted_9", "_Fragment", "handleCheckIn", "handleCancel", "handleCheckOut", "_component_el_dialog", "modelValue", "cancelDialogVisible", "title", "width", "_hoisted_10", "_hoisted_11", "_component_el_alert", "closable", "_hoisted_12", "confirmCancel", "processing"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\ReservationDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"reservation-detail\">\n    <div class=\"page-header\">\n      <div class=\"header-left\">\n        <el-button @click=\"$router.back()\" :icon=\"ArrowLeft\">返回</el-button>\n        <h2>预约详情</h2>\n      </div>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"10\" animated />\n    </div>\n\n    <div v-else-if=\"!reservation\" class=\"empty-container\">\n      <el-empty description=\"未找到预约信息\" />\n    </div>\n\n    <div v-else class=\"detail-content\">\n      <el-card shadow=\"hover\" class=\"detail-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <h3>基本信息</h3>\n            <el-tag :type=\"getReservationStatusType(reservation.status)\">\n              {{ getReservationStatusText(reservation.status) }}\n            </el-tag>\n          </div>\n        </template>\n\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"预约号\">\n            {{ reservation.id }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"创建时间\">\n            {{ formatDateTime(reservation.created_at) }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"预约时间\" :span=\"2\">\n            {{ formatDateTime(reservation.start_time) }} 至\n            {{ formatDateTime(reservation.end_time) }}\n            <span class=\"duration\">\n              ({{ calculateDuration(reservation.start_time, reservation.end_time) }})\n            </span>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"签到时间\" v-if=\"reservation.check_in_time\">\n            {{ formatDateTime(reservation.check_in_time) }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"签退时间\" v-if=\"reservation.check_out_time\">\n            {{ formatDateTime(reservation.check_out_time) }}\n          </el-descriptions-item>\n          <el-descriptions-item\n            label=\"取消原因\"\n            v-if=\"reservation.status === 'cancelled' && reservation.cancel_reason\"\n            :span=\"2\"\n          >\n            {{ reservation.cancel_reason }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n\n      <el-card shadow=\"hover\" class=\"detail-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <h3>座位信息</h3>\n          </div>\n        </template>\n\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"自习室\">\n            {{ reservation.seat?.room?.name }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"位置\">\n            {{ reservation.seat?.room?.location }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"座位号\">\n            {{ reservation.seat?.seat_number }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"座位类型\">\n            <el-tag\n              v-if=\"reservation.seat?.is_window_seat\"\n              size=\"small\"\n              effect=\"plain\"\n              style=\"margin-right: 5px\"\n            >\n              靠窗\n            </el-tag>\n            <el-tag v-if=\"reservation.seat?.is_power_outlet\" size=\"small\" effect=\"plain\">\n              有电源\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"座位描述\" :span=\"2\">\n            {{ reservation.seat?.description || \"无\" }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n\n      <div class=\"action-buttons\">\n        <template v-if=\"reservation.status === 'pending'\">\n          <el-button type=\"primary\" @click=\"handleCheckIn\">签到</el-button>\n          <el-button type=\"danger\" @click=\"handleCancel\">取消预约</el-button>\n        </template>\n\n        <template v-else-if=\"reservation.status === 'checked_in'\">\n          <el-button type=\"success\" @click=\"handleCheckOut\">签退</el-button>\n        </template>\n      </div>\n    </div>\n\n    <!-- 取消预约对话框 -->\n    <el-dialog v-model=\"cancelDialogVisible\" title=\"取消预约\" width=\"400px\">\n      <div class=\"cancel-dialog\">\n        <p>您确定要取消此预约吗？</p>\n        <p>座位号: {{ reservation?.seat?.seat_number }}</p>\n        <p>\n          预约时间: {{ formatDateTime(reservation?.start_time) }} -\n          {{ formatDateTime(reservation?.end_time) }}\n        </p>\n\n        <div class=\"cancel-warning\">\n          <el-alert\n            title=\"取消预约提示\"\n            type=\"warning\"\n            description=\"距离预约开始时间不足30分钟取消，可能会影响您的信誉分。\"\n            show-icon\n            :closable=\"false\"\n          />\n        </div>\n\n        <div class=\"dialog-footer\">\n          <el-button @click=\"cancelDialogVisible = false\">返回</el-button>\n          <el-button type=\"danger\" @click=\"confirmCancel\" :loading=\"processing\">确认取消</el-button>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import { ref, computed, onMounted } from \"vue\";\n  import { useStore } from \"vuex\";\n  import { useRoute } from \"vue-router\";\n  import { ElMessage } from \"element-plus\";\n  import { ArrowLeft } from \"@element-plus/icons-vue\";\n\n  export default {\n    name: \"ReservationDetail\",\n    setup() {\n      const store = useStore();\n      const route = useRoute();\n\n      const loading = ref(true);\n      const processing = ref(false);\n      const cancelDialogVisible = ref(false);\n\n      // 获取预约详情\n      const getReservationDetail = async () => {\n        try {\n          loading.value = true;\n          const reservationId = route.params.id;\n\n          if (!reservationId) {\n            ElMessage.error(\"预约ID不能为空\");\n            return;\n          }\n\n          await store.dispatch(\"seat/getReservationById\", reservationId);\n        } catch (error) {\n          ElMessage.error(\"获取预约详情失败\");\n        } finally {\n          loading.value = false;\n        }\n      };\n\n      // 预约信息\n      const reservation = computed(() => {\n        return store.getters[\"seat/currentReservation\"];\n      });\n\n      // 获取预约状态类型\n      const getReservationStatusType = (status) => {\n        switch (status) {\n          case \"pending\":\n            return \"warning\";\n          case \"checked_in\":\n            return \"success\";\n          case \"completed\":\n            return \"info\";\n          case \"cancelled\":\n            return \"danger\";\n          case \"timeout\":\n            return \"danger\";\n          default:\n            return \"info\";\n        }\n      };\n\n      // 获取预约状态文本\n      const getReservationStatusText = (status) => {\n        switch (status) {\n          case \"pending\":\n            return \"待签到\";\n          case \"checked_in\":\n            return \"已签到\";\n          case \"completed\":\n            return \"已完成\";\n          case \"cancelled\":\n            return \"已取消\";\n          case \"timeout\":\n            return \"已超时\";\n          default:\n            return \"未知状态\";\n        }\n      };\n\n      // 格式化日期时间\n      const formatDateTime = (dateTimeString) => {\n        if (!dateTimeString) return \"\";\n\n        const date = new Date(dateTimeString);\n        return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(\n          date.getDate()\n        )} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n      };\n\n      // 计算时长\n      const calculateDuration = (startTime, endTime) => {\n        if (!startTime || !endTime) return \"\";\n\n        const start = new Date(startTime);\n        const end = new Date(endTime);\n        const diffMs = end - start;\n        const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));\n        const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\n\n        return `${diffHrs}小时${diffMins}分钟`;\n      };\n\n      // 补零\n      function padZero(num) {\n        return num < 10 ? `0${num}` : num;\n      }\n\n      // 处理签到\n      const handleCheckIn = async () => {\n        try {\n          processing.value = true;\n\n          await store.dispatch(\"seat/checkIn\", {\n            reservationId: reservation.value.id,\n          });\n\n          ElMessage.success(\"签到成功\");\n          getReservationDetail();\n        } catch (error) {\n          ElMessage.error(error.message || \"签到失败\");\n        } finally {\n          processing.value = false;\n        }\n      };\n\n      // 处理签退\n      const handleCheckOut = async () => {\n        try {\n          processing.value = true;\n\n          await store.dispatch(\"seat/checkOut\", {\n            reservationId: reservation.value.id,\n          });\n\n          ElMessage.success(\"签退成功\");\n          getReservationDetail();\n        } catch (error) {\n          ElMessage.error(error.message || \"签退失败\");\n        } finally {\n          processing.value = false;\n        }\n      };\n\n      // 处理取消预约\n      const handleCancel = () => {\n        cancelDialogVisible.value = true;\n      };\n\n      // 确认取消预约\n      const confirmCancel = async () => {\n        try {\n          processing.value = true;\n\n          await store.dispatch(\"seat/cancelReservation\", {\n            reservationId: reservation.value.id,\n          });\n\n          ElMessage.success(\"预约已取消\");\n          cancelDialogVisible.value = false;\n          getReservationDetail();\n        } catch (error) {\n          ElMessage.error(error.message || \"取消预约失败\");\n        } finally {\n          processing.value = false;\n        }\n      };\n\n      onMounted(() => {\n        getReservationDetail();\n      });\n\n      return {\n        loading,\n        processing,\n        reservation,\n        cancelDialogVisible,\n        getReservationStatusType,\n        getReservationStatusText,\n        formatDateTime,\n        calculateDuration,\n        handleCheckIn,\n        handleCheckOut,\n        handleCancel,\n        confirmCancel,\n        ArrowLeft,\n      };\n    },\n  };\n</script>\n\n<style lang=\"scss\" scoped>\n  .reservation-detail {\n    padding: 20px;\n  }\n\n  .page-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n\n    .header-left {\n      display: flex;\n      align-items: center;\n      gap: 10px;\n\n      h2 {\n        margin: 0;\n      }\n    }\n  }\n\n  .loading-container,\n  .empty-container {\n    padding: 40px 0;\n    text-align: center;\n  }\n\n  .detail-content {\n    max-width: 800px;\n    margin: 0 auto;\n  }\n\n  .detail-card {\n    margin-bottom: 20px;\n\n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      h3 {\n        margin: 0;\n      }\n    }\n  }\n\n  .duration {\n    color: #909399;\n    margin-left: 10px;\n    font-size: 14px;\n  }\n\n  .action-buttons {\n    display: flex;\n    justify-content: center;\n    gap: 20px;\n    margin-top: 30px;\n  }\n\n  .cancel-dialog {\n    p {\n      margin: 10px 0;\n    }\n\n    .cancel-warning {\n      margin: 20px 0;\n    }\n\n    .dialog-footer {\n      margin-top: 20px;\n      display: flex;\n      justify-content: flex-end;\n      gap: 10px;\n    }\n  }\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EAH9BC,GAAA;EASwBD,KAAK,EAAC;;;EAT9BC,GAAA;EAakCD,KAAK,EAAC;;;EAbxCC,GAAA;EAiBgBD,KAAK,EAAC;;;EAGPA,KAAK,EAAC;AAAa;;EAkBhBA,KAAK,EAAC;AAAU;;EAwDvBA,KAAK,EAAC;AAAgB;;EActBA,KAAK,EAAC;AAAe;;EAQnBA,KAAK,EAAC;AAAgB;;EAUtBA,KAAK,EAAC;AAAe;;;;;;;;;;;uBA7HhCE,mBAAA,CAmIM,OAnINC,UAmIM,GAlIJC,mBAAA,CAKM,OALNC,UAKM,GAJJD,mBAAA,CAGM,OAHNE,UAGM,GAFJC,YAAA,CAAmEC,oBAAA;IAAvDC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;IAAKC,IAAI,EAAEC,MAAA,CAAAC;;IAJlDC,OAAA,EAAAC,QAAA,CAI6D,MAAET,MAAA,QAAAA,MAAA,OAJ/DU,gBAAA,CAI6D,IAAE,E;IAJ/DC,CAAA;IAAAC,EAAA;yDAKQlB,mBAAA,CAAa,YAAT,MAAI,qB,KAIDY,MAAA,CAAAO,OAAO,I,cAAlBrB,mBAAA,CAEM,OAFNsB,UAEM,GADJjB,YAAA,CAAmCkB,sBAAA;IAArBC,IAAI,EAAE,EAAE;IAAEC,QAAQ,EAAR;WAGTX,MAAA,CAAAY,WAAW,I,cAA5B1B,mBAAA,CAEM,OAFN2B,UAEM,GADJtB,YAAA,CAAkCuB,mBAAA;IAAxBC,WAAW,EAAC;EAAS,G,oBAGjC7B,mBAAA,CAuFM,OAvFN8B,UAuFM,GAtFJzB,YAAA,CAsCU0B,kBAAA;IAtCDC,MAAM,EAAC,OAAO;IAAClC,KAAK,EAAC;;IACjBmC,MAAM,EAAAhB,QAAA,CACf,MAKM,CALNf,mBAAA,CAKM,OALNgC,UAKM,G,0BAJJhC,mBAAA,CAAa,YAAT,MAAI,sBACRG,YAAA,CAES8B,iBAAA;MAFAC,IAAI,EAAEtB,MAAA,CAAAuB,wBAAwB,CAACvB,MAAA,CAAAY,WAAW,CAACY,MAAM;;MAtBtEtB,OAAA,EAAAC,QAAA,CAuBc,MAAkD,CAvBhEC,gBAAA,CAAAqB,gBAAA,CAuBiBzB,MAAA,CAAA0B,wBAAwB,CAAC1B,MAAA,CAAAY,WAAW,CAACY,MAAM,kB;MAvB5DnB,CAAA;;IAAAH,OAAA,EAAAC,QAAA,CA4BQ,MA2BkB,CA3BlBZ,YAAA,CA2BkBoC,0BAAA;MA3BAC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAN;;MA5BrC3B,OAAA,EAAAC,QAAA,CA6BU,MAEuB,CAFvBZ,YAAA,CAEuBuC,+BAAA;QAFDC,KAAK,EAAC;MAAK;QA7B3C7B,OAAA,EAAAC,QAAA,CA8BY,MAAoB,CA9BhCC,gBAAA,CAAAqB,gBAAA,CA8BezB,MAAA,CAAAY,WAAW,CAACoB,EAAE,iB;QA9B7B3B,CAAA;UAgCUd,YAAA,CAEuBuC,+BAAA;QAFDC,KAAK,EAAC;MAAM;QAhC5C7B,OAAA,EAAAC,QAAA,CAiCY,MAA4C,CAjCxDC,gBAAA,CAAAqB,gBAAA,CAiCezB,MAAA,CAAAiC,cAAc,CAACjC,MAAA,CAAAY,WAAW,CAACsB,UAAU,kB;QAjCpD7B,CAAA;UAmCUd,YAAA,CAMuBuC,+BAAA;QANDC,KAAK,EAAC,MAAM;QAAEI,IAAI,EAAE;;QAnCpDjC,OAAA,EAAAC,QAAA,CAoCY,MAA4C,CApCxDC,gBAAA,CAAAqB,gBAAA,CAoCezB,MAAA,CAAAiC,cAAc,CAACjC,MAAA,CAAAY,WAAW,CAACwB,UAAU,KAAI,KAC5C,GAAAX,gBAAA,CAAGzB,MAAA,CAAAiC,cAAc,CAACjC,MAAA,CAAAY,WAAW,CAACyB,QAAQ,KAAI,GAC1C,iBAAAjD,mBAAA,CAEO,QAFPkD,UAEO,EAFgB,IACpB,GAAAb,gBAAA,CAAGzB,MAAA,CAAAuC,iBAAiB,CAACvC,MAAA,CAAAY,WAAW,CAACwB,UAAU,EAAEpC,MAAA,CAAAY,WAAW,CAACyB,QAAQ,KAAI,IACxE,gB;QAxCZhC,CAAA;UA0CmDL,MAAA,CAAAY,WAAW,CAAC4B,aAAa,I,cAAlEC,YAAA,CAEuBX,+BAAA;QA5CjC7C,GAAA;QA0CgC8C,KAAK,EAAC;;QA1CtC7B,OAAA,EAAAC,QAAA,CA2CY,MAA+C,CA3C3DC,gBAAA,CAAAqB,gBAAA,CA2CezB,MAAA,CAAAiC,cAAc,CAACjC,MAAA,CAAAY,WAAW,CAAC4B,aAAa,kB;QA3CvDnC,CAAA;YAAAqC,mBAAA,gBA6CmD1C,MAAA,CAAAY,WAAW,CAAC+B,cAAc,I,cAAnEF,YAAA,CAEuBX,+BAAA;QA/CjC7C,GAAA;QA6CgC8C,KAAK,EAAC;;QA7CtC7B,OAAA,EAAAC,QAAA,CA8CY,MAAgD,CA9C5DC,gBAAA,CAAAqB,gBAAA,CA8CezB,MAAA,CAAAiC,cAAc,CAACjC,MAAA,CAAAY,WAAW,CAAC+B,cAAc,kB;QA9CxDtC,CAAA;YAAAqC,mBAAA,gBAkDkB1C,MAAA,CAAAY,WAAW,CAACY,MAAM,oBAAoBxB,MAAA,CAAAY,WAAW,CAACgC,aAAa,I,cAFvEH,YAAA,CAMuBX,+BAAA;QAtDjC7C,GAAA;QAiDY8C,KAAK,EAAC,MAAM;QAEXI,IAAI,EAAE;;QAnDnBjC,OAAA,EAAAC,QAAA,CAqDY,MAA+B,CArD3CC,gBAAA,CAAAqB,gBAAA,CAqDezB,MAAA,CAAAY,WAAW,CAACgC,aAAa,iB;QArDxCvC,CAAA;YAAAqC,mBAAA,e;MAAArC,CAAA;;IAAAA,CAAA;MA0DMd,YAAA,CAkCU0B,kBAAA;IAlCDC,MAAM,EAAC,OAAO;IAAClC,KAAK,EAAC;;IACjBmC,MAAM,EAAAhB,QAAA,CACf,MAEMT,MAAA,QAAAA,MAAA,OAFNN,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAa,IACtBI,mBAAA,CAAa,YAAT,MAAI,E;IA7DpBc,OAAA,EAAAC,QAAA,CAiEQ,MA0BkB,CA1BlBZ,YAAA,CA0BkBoC,0BAAA;MA1BAC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAN;;MAjErC3B,OAAA,EAAAC,QAAA,CAkEU,MAEuB,CAFvBZ,YAAA,CAEuBuC,+BAAA;QAFDC,KAAK,EAAC;MAAK;QAlE3C7B,OAAA,EAAAC,QAAA,CAmEY,MAAkC,CAnE9CC,gBAAA,CAAAqB,gBAAA,CAmEezB,MAAA,CAAAY,WAAW,CAACiC,IAAI,EAAEC,IAAI,EAAEC,IAAI,iB;QAnE3C1C,CAAA;UAqEUd,YAAA,CAEuBuC,+BAAA;QAFDC,KAAK,EAAC;MAAI;QArE1C7B,OAAA,EAAAC,QAAA,CAsEY,MAAsC,CAtElDC,gBAAA,CAAAqB,gBAAA,CAsEezB,MAAA,CAAAY,WAAW,CAACiC,IAAI,EAAEC,IAAI,EAAEE,QAAQ,iB;QAtE/C3C,CAAA;UAwEUd,YAAA,CAEuBuC,+BAAA;QAFDC,KAAK,EAAC;MAAK;QAxE3C7B,OAAA,EAAAC,QAAA,CAyEY,MAAmC,CAzE/CC,gBAAA,CAAAqB,gBAAA,CAyEezB,MAAA,CAAAY,WAAW,CAACiC,IAAI,EAAEI,WAAW,iB;QAzE5C5C,CAAA;UA2EUd,YAAA,CAYuBuC,+BAAA;QAZDC,KAAK,EAAC;MAAM;QA3E5C7B,OAAA,EAAAC,QAAA,CA+FoB,MAQS,CA1BTH,MAAA,CAAAY,WAAW,CAACiC,IAAI,EAAEK,cAAc,I,cADxCT,YAAA,CAOSpB,iBAAA;UAnFrBpC,GAAA;UA8EckE,IAAI,EAAC,OAAO;UACZC,MAAM,EAAC,OAAO;UACdC,KAAyB,EAAzB;YAAA;UAAA;;UAhFdnD,OAAA,EAAAC,QAAA,CAiFa,MAEDT,MAAA,QAAAA,MAAA,OAnFZU,gBAAA,CAiFa,MAED,E;UAnFZC,CAAA;UAAAC,EAAA;cAAAoC,mBAAA,gBAoF0B1C,MAAA,CAAAY,WAAW,CAACiC,IAAI,EAAES,eAAe,I,cAA/Cb,YAAA,CAESpB,iBAAA;UAtFrBpC,GAAA;UAoF6DkE,IAAI,EAAC,OAAO;UAACC,MAAM,EAAC;;UApFjFlD,OAAA,EAAAC,QAAA,CAoFyF,MAE7ET,MAAA,QAAAA,MAAA,OAtFZU,gBAAA,CAoFyF,OAE7E,E;UAtFZC,CAAA;UAAAC,EAAA;cAAAoC,mBAAA,e;QAAArC,CAAA;UAwFUd,YAAA,CAEuBuC,+BAAA;QAFDC,KAAK,EAAC,MAAM;QAAEI,IAAI,EAAE;;QAxFpDjC,OAAA,EAAAC,QAAA,CAyFY,MAA0C,CAzFtDC,gBAAA,CAAAqB,gBAAA,CAyFezB,MAAA,CAAAY,WAAW,CAACiC,IAAI,EAAE9B,WAAW,wB;QAzF5CV,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;MA8FMjB,mBAAA,CASM,OATNmE,UASM,GARYvD,MAAA,CAAAY,WAAW,CAACY,MAAM,kB,cAAlCtC,mBAAA,CAGWsE,SAAA;IAlGnBvE,GAAA;EAAA,IAgGUM,YAAA,CAA+DC,oBAAA;IAApD8B,IAAI,EAAC,SAAS;IAAE7B,OAAK,EAAEO,MAAA,CAAAyD;;IAhG5CvD,OAAA,EAAAC,QAAA,CAgG2D,MAAET,MAAA,QAAAA,MAAA,OAhG7DU,gBAAA,CAgG2D,IAAE,E;IAhG7DC,CAAA;IAAAC,EAAA;kCAiGUf,YAAA,CAA+DC,oBAAA;IAApD8B,IAAI,EAAC,QAAQ;IAAE7B,OAAK,EAAEO,MAAA,CAAA0D;;IAjG3CxD,OAAA,EAAAC,QAAA,CAiGyD,MAAIT,MAAA,SAAAA,MAAA,QAjG7DU,gBAAA,CAiGyD,MAAI,E;IAjG7DC,CAAA;IAAAC,EAAA;gEAoG6BN,MAAA,CAAAY,WAAW,CAACY,MAAM,qB,cACrCiB,YAAA,CAAgEjD,oBAAA;IArG1EP,GAAA;IAqGqBqC,IAAI,EAAC,SAAS;IAAE7B,OAAK,EAAEO,MAAA,CAAA2D;;IArG5CzD,OAAA,EAAAC,QAAA,CAqG4D,MAAET,MAAA,SAAAA,MAAA,QArG9DU,gBAAA,CAqG4D,IAAE,E;IArG9DC,CAAA;IAAAC,EAAA;oCAAAoC,mBAAA,e,MA0GIA,mBAAA,aAAgB,EAChBnD,YAAA,CAwBYqE,oBAAA;IAnIhBC,UAAA,EA2GwB7D,MAAA,CAAA8D,mBAAmB;IA3G3C,uBAAApE,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA2GwBK,MAAA,CAAA8D,mBAAmB,GAAAnE,MAAA;IAAEoE,KAAK,EAAC,MAAM;IAACC,KAAK,EAAC;;IA3GhE9D,OAAA,EAAAC,QAAA,CA4GM,MAsBM,CAtBNf,mBAAA,CAsBM,OAtBN6E,WAsBM,G,4BArBJ7E,mBAAA,CAAkB,WAAf,aAAW,sBACdA,mBAAA,CAAgD,WAA7C,OAAK,GAAAqC,gBAAA,CAAGzB,MAAA,CAAAY,WAAW,EAAEiC,IAAI,EAAEI,WAAW,kBACzC7D,mBAAA,CAGI,WAHD,SACK,GAAAqC,gBAAA,CAAGzB,MAAA,CAAAiC,cAAc,CAACjC,MAAA,CAAAY,WAAW,EAAEwB,UAAU,KAAI,KACnD,GAAAX,gBAAA,CAAGzB,MAAA,CAAAiC,cAAc,CAACjC,MAAA,CAAAY,WAAW,EAAEyB,QAAQ,mBAGzCjD,mBAAA,CAQM,OARN8E,WAQM,GAPJ3E,YAAA,CAME4E,mBAAA;MALAJ,KAAK,EAAC,QAAQ;MACdzC,IAAI,EAAC,SAAS;MACdP,WAAW,EAAC,8BAA8B;MAC1C,WAAS,EAAT,EAAS;MACRqD,QAAQ,EAAE;UAIfhF,mBAAA,CAGM,OAHNiF,WAGM,GAFJ9E,YAAA,CAA8DC,oBAAA;MAAlDC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEK,MAAA,CAAA8D,mBAAmB;;MA/HhD5D,OAAA,EAAAC,QAAA,CA+H0D,MAAET,MAAA,SAAAA,MAAA,QA/H5DU,gBAAA,CA+H0D,IAAE,E;MA/H5DC,CAAA;MAAAC,EAAA;QAgIUf,YAAA,CAAsFC,oBAAA;MAA3E8B,IAAI,EAAC,QAAQ;MAAE7B,OAAK,EAAEO,MAAA,CAAAsE,aAAa;MAAG/D,OAAO,EAAEP,MAAA,CAAAuE;;MAhIpErE,OAAA,EAAAC,QAAA,CAgIgF,MAAIT,MAAA,SAAAA,MAAA,QAhIpFU,gBAAA,CAgIgF,MAAI,E;MAhIpFC,CAAA;MAAAC,EAAA;;IAAAD,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}