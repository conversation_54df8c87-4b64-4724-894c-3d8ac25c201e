{"ast": null, "code": "import { defineComponent, ref, reactive, computed, onMounted, watch, nextTick, provide, openBlock, createBlock, unref, withCtx, withDirectives, createElementBlock, withKeys, createElementVNode, normalizeClass, createVNode, createCommentVNode, createTextVNode, toDisplayString, mergeProps, normalizeStyle, vShow } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { ElButton } from '../../button/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { ElInput } from '../../input/index.mjs';\nimport { ArrowDown, Close } from '@element-plus/icons-vue';\nimport AlphaSlider from './components/alpha-slider.mjs';\nimport HueSlider from './components/hue-slider.mjs';\nimport Predefine from './components/predefine.mjs';\nimport SvPanel from './components/sv-panel.mjs';\nimport Color from './utils/color.mjs';\nimport { colorPickerProps, colorPickerEmits, colorPickerContextKey } from './color-picker.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport ClickOutside from '../../../directives/click-outside/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { useFormSize, useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useFocusController } from '../../../hooks/use-focus-controller/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nconst __default__ = defineComponent({\n  name: \"ElColorPicker\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: colorPickerProps,\n  emits: colorPickerEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"color\");\n    const {\n      formItem\n    } = useFormItem();\n    const colorSize = useFormSize();\n    const colorDisabled = useFormDisabled();\n    const {\n      inputId: buttonId,\n      isLabeledByFormItem\n    } = useFormItemInputId(props, {\n      formItemContext: formItem\n    });\n    const hue = ref();\n    const sv = ref();\n    const alpha = ref();\n    const popper = ref();\n    const triggerRef = ref();\n    const inputRef = ref();\n    const {\n      isFocused,\n      handleFocus,\n      handleBlur\n    } = useFocusController(triggerRef, {\n      beforeFocus() {\n        return colorDisabled.value;\n      },\n      beforeBlur(event) {\n        var _a;\n        return (_a = popper.value) == null ? void 0 : _a.isFocusInsideContent(event);\n      },\n      afterBlur() {\n        setShowPicker(false);\n        resetColor();\n      }\n    });\n    let shouldActiveChange = true;\n    const color = reactive(new Color({\n      enableAlpha: props.showAlpha,\n      format: props.colorFormat || \"\",\n      value: props.modelValue\n    }));\n    const showPicker = ref(false);\n    const showPanelColor = ref(false);\n    const customInput = ref(\"\");\n    const displayedColor = computed(() => {\n      if (!props.modelValue && !showPanelColor.value) {\n        return \"transparent\";\n      }\n      return displayedRgb(color, props.showAlpha);\n    });\n    const currentColor = computed(() => {\n      return !props.modelValue && !showPanelColor.value ? \"\" : color.value;\n    });\n    const buttonAriaLabel = computed(() => {\n      return !isLabeledByFormItem.value ? props.ariaLabel || t(\"el.colorpicker.defaultLabel\") : void 0;\n    });\n    const buttonAriaLabelledby = computed(() => {\n      return isLabeledByFormItem.value ? formItem == null ? void 0 : formItem.labelId : void 0;\n    });\n    const btnKls = computed(() => {\n      return [ns.b(\"picker\"), ns.is(\"disabled\", colorDisabled.value), ns.bm(\"picker\", colorSize.value), ns.is(\"focused\", isFocused.value)];\n    });\n    function displayedRgb(color2, showAlpha) {\n      if (!(color2 instanceof Color)) {\n        throw new TypeError(\"color should be instance of _color Class\");\n      }\n      const {\n        r,\n        g,\n        b\n      } = color2.toRgb();\n      return showAlpha ? `rgba(${r}, ${g}, ${b}, ${color2.get(\"alpha\") / 100})` : `rgb(${r}, ${g}, ${b})`;\n    }\n    function setShowPicker(value) {\n      showPicker.value = value;\n    }\n    const debounceSetShowPicker = debounce(setShowPicker, 100, {\n      leading: true\n    });\n    function show() {\n      if (colorDisabled.value) return;\n      setShowPicker(true);\n    }\n    function hide() {\n      debounceSetShowPicker(false);\n      resetColor();\n    }\n    function resetColor() {\n      nextTick(() => {\n        if (props.modelValue) {\n          color.fromString(props.modelValue);\n        } else {\n          color.value = \"\";\n          nextTick(() => {\n            showPanelColor.value = false;\n          });\n        }\n      });\n    }\n    function handleTrigger() {\n      if (colorDisabled.value) return;\n      if (showPicker.value) {\n        resetColor();\n      }\n      debounceSetShowPicker(!showPicker.value);\n    }\n    function handleConfirm() {\n      color.fromString(customInput.value);\n    }\n    function confirmValue() {\n      const value = color.value;\n      emit(UPDATE_MODEL_EVENT, value);\n      emit(CHANGE_EVENT, value);\n      if (props.validateEvent) {\n        formItem == null ? void 0 : formItem.validate(\"change\").catch(err => debugWarn(err));\n      }\n      debounceSetShowPicker(false);\n      nextTick(() => {\n        const newColor = new Color({\n          enableAlpha: props.showAlpha,\n          format: props.colorFormat || \"\",\n          value: props.modelValue\n        });\n        if (!color.compare(newColor)) {\n          resetColor();\n        }\n      });\n    }\n    function clear() {\n      debounceSetShowPicker(false);\n      emit(UPDATE_MODEL_EVENT, null);\n      emit(CHANGE_EVENT, null);\n      if (props.modelValue !== null && props.validateEvent) {\n        formItem == null ? void 0 : formItem.validate(\"change\").catch(err => debugWarn(err));\n      }\n      resetColor();\n    }\n    function handleClickOutside() {\n      if (!showPicker.value) return;\n      hide();\n      isFocused.value && focus();\n    }\n    function handleEsc(event) {\n      event.preventDefault();\n      event.stopPropagation();\n      setShowPicker(false);\n      resetColor();\n    }\n    function handleKeyDown(event) {\n      switch (event.code) {\n        case EVENT_CODE.enter:\n        case EVENT_CODE.numpadEnter:\n        case EVENT_CODE.space:\n          event.preventDefault();\n          event.stopPropagation();\n          show();\n          inputRef.value.focus();\n          break;\n        case EVENT_CODE.esc:\n          handleEsc(event);\n          break;\n      }\n    }\n    function focus() {\n      triggerRef.value.focus();\n    }\n    function blur() {\n      triggerRef.value.blur();\n    }\n    onMounted(() => {\n      if (props.modelValue) {\n        customInput.value = currentColor.value;\n      }\n    });\n    watch(() => props.modelValue, newVal => {\n      if (!newVal) {\n        showPanelColor.value = false;\n      } else if (newVal && newVal !== color.value) {\n        shouldActiveChange = false;\n        color.fromString(newVal);\n      }\n    });\n    watch(() => [props.colorFormat, props.showAlpha], () => {\n      color.enableAlpha = props.showAlpha;\n      color.format = props.colorFormat || color.format;\n      color.doOnChange();\n      emit(UPDATE_MODEL_EVENT, color.value);\n    });\n    watch(() => currentColor.value, val => {\n      customInput.value = val;\n      shouldActiveChange && emit(\"activeChange\", val);\n      shouldActiveChange = true;\n    });\n    watch(() => color.value, () => {\n      if (!props.modelValue && !showPanelColor.value) {\n        showPanelColor.value = true;\n      }\n    });\n    watch(() => showPicker.value, () => {\n      nextTick(() => {\n        var _a, _b, _c;\n        (_a = hue.value) == null ? void 0 : _a.update();\n        (_b = sv.value) == null ? void 0 : _b.update();\n        (_c = alpha.value) == null ? void 0 : _c.update();\n      });\n    });\n    provide(colorPickerContextKey, {\n      currentColor\n    });\n    expose({\n      color,\n      show,\n      hide,\n      focus,\n      blur\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTooltip), {\n        ref_key: \"popper\",\n        ref: popper,\n        visible: showPicker.value,\n        \"show-arrow\": false,\n        \"fallback-placements\": [\"bottom\", \"top\", \"right\", \"left\"],\n        offset: 0,\n        \"gpu-acceleration\": false,\n        \"popper-class\": [unref(ns).be(\"picker\", \"panel\"), unref(ns).b(\"dropdown\"), _ctx.popperClass],\n        \"stop-popper-mouse-event\": false,\n        effect: \"light\",\n        trigger: \"click\",\n        teleported: _ctx.teleported,\n        transition: `${unref(ns).namespace.value}-zoom-in-top`,\n        persistent: \"\",\n        onHide: $event => setShowPicker(false)\n      }, {\n        content: withCtx(() => [withDirectives((openBlock(), createElementBlock(\"div\", {\n          onKeydown: withKeys(handleEsc, [\"esc\"])\n        }, [createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).be(\"dropdown\", \"main-wrapper\"))\n        }, [createVNode(HueSlider, {\n          ref_key: \"hue\",\n          ref: hue,\n          class: \"hue-slider\",\n          color: unref(color),\n          vertical: \"\"\n        }, null, 8, [\"color\"]), createVNode(SvPanel, {\n          ref_key: \"sv\",\n          ref: sv,\n          color: unref(color)\n        }, null, 8, [\"color\"])], 2), _ctx.showAlpha ? (openBlock(), createBlock(AlphaSlider, {\n          key: 0,\n          ref_key: \"alpha\",\n          ref: alpha,\n          color: unref(color)\n        }, null, 8, [\"color\"])) : createCommentVNode(\"v-if\", true), _ctx.predefine ? (openBlock(), createBlock(Predefine, {\n          key: 1,\n          ref: \"predefine\",\n          \"enable-alpha\": _ctx.showAlpha,\n          color: unref(color),\n          colors: _ctx.predefine\n        }, null, 8, [\"enable-alpha\", \"color\", \"colors\"])) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).be(\"dropdown\", \"btns\"))\n        }, [createElementVNode(\"span\", {\n          class: normalizeClass(unref(ns).be(\"dropdown\", \"value\"))\n        }, [createVNode(unref(ElInput), {\n          ref_key: \"inputRef\",\n          ref: inputRef,\n          modelValue: customInput.value,\n          \"onUpdate:modelValue\": $event => customInput.value = $event,\n          \"validate-event\": false,\n          size: \"small\",\n          onKeyup: withKeys(handleConfirm, [\"enter\"]),\n          onBlur: handleConfirm\n        }, null, 8, [\"modelValue\", \"onUpdate:modelValue\", \"onKeyup\"])], 2), createVNode(unref(ElButton), {\n          class: normalizeClass(unref(ns).be(\"dropdown\", \"link-btn\")),\n          text: \"\",\n          size: \"small\",\n          onClick: clear\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(unref(t)(\"el.colorpicker.clear\")), 1)]),\n          _: 1\n        }, 8, [\"class\"]), createVNode(unref(ElButton), {\n          plain: \"\",\n          size: \"small\",\n          class: normalizeClass(unref(ns).be(\"dropdown\", \"btn\")),\n          onClick: confirmValue\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(unref(t)(\"el.colorpicker.confirm\")), 1)]),\n          _: 1\n        }, 8, [\"class\"])], 2)], 40, [\"onKeydown\"])), [[unref(ClickOutside), handleClickOutside, triggerRef.value]])]),\n        default: withCtx(() => [createElementVNode(\"div\", mergeProps({\n          id: unref(buttonId),\n          ref_key: \"triggerRef\",\n          ref: triggerRef\n        }, _ctx.$attrs, {\n          class: unref(btnKls),\n          role: \"button\",\n          \"aria-label\": unref(buttonAriaLabel),\n          \"aria-labelledby\": unref(buttonAriaLabelledby),\n          \"aria-description\": unref(t)(\"el.colorpicker.description\", {\n            color: _ctx.modelValue || \"\"\n          }),\n          \"aria-disabled\": unref(colorDisabled),\n          tabindex: unref(colorDisabled) ? void 0 : _ctx.tabindex,\n          onKeydown: handleKeyDown,\n          onFocus: unref(handleFocus),\n          onBlur: unref(handleBlur)\n        }), [unref(colorDisabled) ? (openBlock(), createElementBlock(\"div\", {\n          key: 0,\n          class: normalizeClass(unref(ns).be(\"picker\", \"mask\"))\n        }, null, 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).be(\"picker\", \"trigger\")),\n          onClick: handleTrigger\n        }, [createElementVNode(\"span\", {\n          class: normalizeClass([unref(ns).be(\"picker\", \"color\"), unref(ns).is(\"alpha\", _ctx.showAlpha)])\n        }, [createElementVNode(\"span\", {\n          class: normalizeClass(unref(ns).be(\"picker\", \"color-inner\")),\n          style: normalizeStyle({\n            backgroundColor: unref(displayedColor)\n          })\n        }, [withDirectives(createVNode(unref(ElIcon), {\n          class: normalizeClass([unref(ns).be(\"picker\", \"icon\"), unref(ns).is(\"icon-arrow-down\")])\n        }, {\n          default: withCtx(() => [createVNode(unref(ArrowDown))]),\n          _: 1\n        }, 8, [\"class\"]), [[vShow, _ctx.modelValue || showPanelColor.value]]), withDirectives(createVNode(unref(ElIcon), {\n          class: normalizeClass([unref(ns).be(\"picker\", \"empty\"), unref(ns).is(\"icon-close\")])\n        }, {\n          default: withCtx(() => [createVNode(unref(Close))]),\n          _: 1\n        }, 8, [\"class\"]), [[vShow, !_ctx.modelValue && !showPanelColor.value]])], 6)], 2)], 2)], 16, [\"id\", \"aria-label\", \"aria-labelledby\", \"aria-description\", \"aria-disabled\", \"tabindex\", \"onFocus\", \"onBlur\"])]),\n        _: 1\n      }, 8, [\"visible\", \"popper-class\", \"teleported\", \"transition\", \"onHide\"]);\n    };\n  }\n});\nvar ColorPicker = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"color-picker.vue\"]]);\nexport { ColorPicker as default };", "map": {"version": 3, "names": ["name", "t", "useLocale", "ns", "useNamespace", "formItem", "useFormItem", "colorSize", "useFormSize", "colorDisabled", "useFormDisabled", "inputId", "buttonId", "isLabeledByFormItem", "useFormItemInputId", "props", "formItemContext", "hue", "ref", "sv", "alpha", "popper", "triggerRef", "inputRef", "isFocused", "handleFocus", "handleBlur", "useFocusController", "beforeFocus", "value", "beforeBlur", "event", "_a", "isFocusInsideContent", "after<PERSON><PERSON>r", "setShowPicker", "resetColor", "shouldActiveChange", "color", "reactive", "Color", "enableAlpha", "showAlpha", "format", "colorFormat", "modelValue", "showPicker", "showPanelColor", "customInput", "displayedColor", "computed", "displayedRgb", "currentColor", "buttonAriaLabel", "aria<PERSON><PERSON><PERSON>", "button<PERSON>ria<PERSON><PERSON><PERSON><PERSON>", "labelId", "btnKls", "b", "is", "bm", "color2", "TypeError", "r", "g", "toRgb", "get", "debounceSetShowPicker", "debounce", "leading", "show", "hide", "nextTick", "fromString", "handleTrigger", "handleConfirm", "confirmValue", "emit", "UPDATE_MODEL_EVENT", "CHANGE_EVENT", "validateEvent", "validate", "catch", "err", "debugWarn", "newColor", "compare", "clear", "handleClickOutside", "focus", "handleEsc", "preventDefault", "stopPropagation", "handleKeyDown", "code", "EVENT_CODE", "enter", "numpadEnter", "space", "esc", "blur", "onMounted", "watch", "newVal", "doOnChange", "val", "_b", "_c", "update", "provide", "colorPickerContextKey", "expose", "_ctx", "_cache", "openBlock", "createBlock", "unref", "ElTooltip", "ref_key", "visible", "offset", "be", "popperClass", "effect", "trigger", "teleported", "transition", "namespace", "persistent", "onHide", "$event", "content", "withCtx", "withDirectives", "createElementBlock", "onKeydown", "<PERSON><PERSON><PERSON><PERSON>", "createElementVNode", "class", "normalizeClass", "createVNode", "HueSlider"], "sources": ["../../../../../../packages/components/color-picker/src/color-picker.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"popper\"\n    :visible=\"showPicker\"\n    :show-arrow=\"false\"\n    :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n    :offset=\"0\"\n    :gpu-acceleration=\"false\"\n    :popper-class=\"[ns.be('picker', 'panel'), ns.b('dropdown'), popperClass]\"\n    :stop-popper-mouse-event=\"false\"\n    effect=\"light\"\n    trigger=\"click\"\n    :teleported=\"teleported\"\n    :transition=\"`${ns.namespace.value}-zoom-in-top`\"\n    persistent\n    @hide=\"setShowPicker(false)\"\n  >\n    <template #content>\n      <div\n        v-click-outside:[triggerRef]=\"handleClickOutside\"\n        @keydown.esc=\"handleEsc\"\n      >\n        <div :class=\"ns.be('dropdown', 'main-wrapper')\">\n          <hue-slider ref=\"hue\" class=\"hue-slider\" :color=\"color\" vertical />\n          <sv-panel ref=\"sv\" :color=\"color\" />\n        </div>\n        <alpha-slider v-if=\"showAlpha\" ref=\"alpha\" :color=\"color\" />\n        <predefine\n          v-if=\"predefine\"\n          ref=\"predefine\"\n          :enable-alpha=\"showAlpha\"\n          :color=\"color\"\n          :colors=\"predefine\"\n        />\n        <div :class=\"ns.be('dropdown', 'btns')\">\n          <span :class=\"ns.be('dropdown', 'value')\">\n            <el-input\n              ref=\"inputRef\"\n              v-model=\"customInput\"\n              :validate-event=\"false\"\n              size=\"small\"\n              @keyup.enter=\"handleConfirm\"\n              @blur=\"handleConfirm\"\n            />\n          </span>\n          <el-button\n            :class=\"ns.be('dropdown', 'link-btn')\"\n            text\n            size=\"small\"\n            @click=\"clear\"\n          >\n            {{ t('el.colorpicker.clear') }}\n          </el-button>\n          <el-button\n            plain\n            size=\"small\"\n            :class=\"ns.be('dropdown', 'btn')\"\n            @click=\"confirmValue\"\n          >\n            {{ t('el.colorpicker.confirm') }}\n          </el-button>\n        </div>\n      </div>\n    </template>\n    <template #default>\n      <div\n        :id=\"buttonId\"\n        ref=\"triggerRef\"\n        v-bind=\"$attrs\"\n        :class=\"btnKls\"\n        role=\"button\"\n        :aria-label=\"buttonAriaLabel\"\n        :aria-labelledby=\"buttonAriaLabelledby\"\n        :aria-description=\"\n          t('el.colorpicker.description', { color: modelValue || '' })\n        \"\n        :aria-disabled=\"colorDisabled\"\n        :tabindex=\"colorDisabled ? undefined : tabindex\"\n        @keydown=\"handleKeyDown\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n      >\n        <div v-if=\"colorDisabled\" :class=\"ns.be('picker', 'mask')\" />\n        <div :class=\"ns.be('picker', 'trigger')\" @click=\"handleTrigger\">\n          <span :class=\"[ns.be('picker', 'color'), ns.is('alpha', showAlpha)]\">\n            <span\n              :class=\"ns.be('picker', 'color-inner')\"\n              :style=\"{\n                backgroundColor: displayedColor,\n              }\"\n            >\n              <el-icon\n                v-show=\"modelValue || showPanelColor\"\n                :class=\"[ns.be('picker', 'icon'), ns.is('icon-arrow-down')]\"\n              >\n                <arrow-down />\n              </el-icon>\n              <el-icon\n                v-show=\"!modelValue && !showPanelColor\"\n                :class=\"[ns.be('picker', 'empty'), ns.is('icon-close')]\"\n              >\n                <close />\n              </el-icon>\n            </span>\n          </span>\n        </div>\n      </div>\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  watch,\n} from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { ElButton } from '@element-plus/components/button'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { ClickOutside as vClickOutside } from '@element-plus/directives'\nimport { ElTooltip } from '@element-plus/components/tooltip'\nimport { ElInput } from '@element-plus/components/input'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\nimport {\n  useFocusController,\n  useLocale,\n  useNamespace,\n} from '@element-plus/hooks'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { debugWarn } from '@element-plus/utils'\nimport { ArrowDown, Close } from '@element-plus/icons-vue'\nimport AlphaSlider from './components/alpha-slider.vue'\nimport HueSlider from './components/hue-slider.vue'\nimport Predefine from './components/predefine.vue'\nimport SvPanel from './components/sv-panel.vue'\nimport Color from './utils/color'\nimport {\n  colorPickerContextKey,\n  colorPickerEmits,\n  colorPickerProps,\n} from './color-picker'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\ndefineOptions({\n  name: 'ElColorPicker',\n})\nconst props = defineProps(colorPickerProps)\nconst emit = defineEmits(colorPickerEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('color')\nconst { formItem } = useFormItem()\nconst colorSize = useFormSize()\nconst colorDisabled = useFormDisabled()\n\nconst { inputId: buttonId, isLabeledByFormItem } = useFormItemInputId(props, {\n  formItemContext: formItem,\n})\n\nconst hue = ref<InstanceType<typeof HueSlider>>()\nconst sv = ref<InstanceType<typeof SvPanel>>()\nconst alpha = ref<InstanceType<typeof AlphaSlider>>()\nconst popper = ref<TooltipInstance>()\nconst triggerRef = ref()\nconst inputRef = ref()\n\nconst { isFocused, handleFocus, handleBlur } = useFocusController(triggerRef, {\n  beforeFocus() {\n    return colorDisabled.value\n  },\n  beforeBlur(event) {\n    return popper.value?.isFocusInsideContent(event)\n  },\n  afterBlur() {\n    setShowPicker(false)\n    resetColor()\n  },\n})\n\n// active-change is used to prevent modelValue changes from triggering.\nlet shouldActiveChange = true\n\nconst color = reactive(\n  new Color({\n    enableAlpha: props.showAlpha,\n    format: props.colorFormat || '',\n    value: props.modelValue,\n  })\n) as Color\n\nconst showPicker = ref(false)\nconst showPanelColor = ref(false)\nconst customInput = ref('')\n\nconst displayedColor = computed(() => {\n  if (!props.modelValue && !showPanelColor.value) {\n    return 'transparent'\n  }\n  return displayedRgb(color, props.showAlpha)\n})\n\nconst currentColor = computed(() => {\n  return !props.modelValue && !showPanelColor.value ? '' : color.value\n})\n\nconst buttonAriaLabel = computed<string | undefined>(() => {\n  return !isLabeledByFormItem.value\n    ? props.ariaLabel || t('el.colorpicker.defaultLabel')\n    : undefined\n})\n\nconst buttonAriaLabelledby = computed<string | undefined>(() => {\n  return isLabeledByFormItem.value ? formItem?.labelId : undefined\n})\n\nconst btnKls = computed(() => {\n  return [\n    ns.b('picker'),\n    ns.is('disabled', colorDisabled.value),\n    ns.bm('picker', colorSize.value),\n    ns.is('focused', isFocused.value),\n  ]\n})\n\nfunction displayedRgb(color: Color, showAlpha: boolean) {\n  if (!(color instanceof Color)) {\n    throw new TypeError('color should be instance of _color Class')\n  }\n\n  const { r, g, b } = color.toRgb()\n  return showAlpha\n    ? `rgba(${r}, ${g}, ${b}, ${color.get('alpha') / 100})`\n    : `rgb(${r}, ${g}, ${b})`\n}\n\nfunction setShowPicker(value: boolean) {\n  showPicker.value = value\n}\n\nconst debounceSetShowPicker = debounce(setShowPicker, 100, { leading: true })\nfunction show() {\n  if (colorDisabled.value) return\n  setShowPicker(true)\n}\n\nfunction hide() {\n  debounceSetShowPicker(false)\n  resetColor()\n}\n\nfunction resetColor() {\n  nextTick(() => {\n    if (props.modelValue) {\n      color.fromString(props.modelValue)\n    } else {\n      color.value = ''\n      nextTick(() => {\n        showPanelColor.value = false\n      })\n    }\n  })\n}\n\nfunction handleTrigger() {\n  if (colorDisabled.value) return\n  if (showPicker.value) {\n    resetColor()\n  }\n  debounceSetShowPicker(!showPicker.value)\n}\n\nfunction handleConfirm() {\n  color.fromString(customInput.value)\n}\n\nfunction confirmValue() {\n  const value = color.value\n  emit(UPDATE_MODEL_EVENT, value)\n  emit(CHANGE_EVENT, value)\n  if (props.validateEvent) {\n    formItem?.validate('change').catch((err) => debugWarn(err))\n  }\n  debounceSetShowPicker(false)\n  // check if modelValue change, if not change, then reset color.\n  nextTick(() => {\n    const newColor = new Color({\n      enableAlpha: props.showAlpha,\n      format: props.colorFormat || '',\n      value: props.modelValue,\n    })\n    if (!color.compare(newColor)) {\n      resetColor()\n    }\n  })\n}\n\nfunction clear() {\n  debounceSetShowPicker(false)\n  emit(UPDATE_MODEL_EVENT, null)\n  emit(CHANGE_EVENT, null)\n  if (props.modelValue !== null && props.validateEvent) {\n    formItem?.validate('change').catch((err) => debugWarn(err))\n  }\n  resetColor()\n}\n\nfunction handleClickOutside() {\n  if (!showPicker.value) return\n  hide()\n  isFocused.value && focus()\n}\n\nfunction handleEsc(event: KeyboardEvent) {\n  event.preventDefault()\n  event.stopPropagation()\n  setShowPicker(false)\n  resetColor()\n}\n\nfunction handleKeyDown(event: KeyboardEvent) {\n  switch (event.code) {\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n    case EVENT_CODE.space:\n      event.preventDefault()\n      event.stopPropagation()\n      show()\n      inputRef.value.focus()\n      break\n    case EVENT_CODE.esc:\n      handleEsc(event)\n      break\n  }\n}\n\nfunction focus() {\n  triggerRef.value.focus()\n}\n\nfunction blur() {\n  triggerRef.value.blur()\n}\n\nonMounted(() => {\n  if (props.modelValue) {\n    customInput.value = currentColor.value\n  }\n})\n\nwatch(\n  () => props.modelValue,\n  (newVal) => {\n    if (!newVal) {\n      showPanelColor.value = false\n    } else if (newVal && newVal !== color.value) {\n      shouldActiveChange = false\n      color.fromString(newVal)\n    }\n  }\n)\n\nwatch(\n  () => [props.colorFormat, props.showAlpha],\n  () => {\n    color.enableAlpha = props.showAlpha\n    color.format = props.colorFormat || color.format\n    color.doOnChange()\n    emit(UPDATE_MODEL_EVENT, color.value)\n  }\n)\n\nwatch(\n  () => currentColor.value,\n  (val) => {\n    customInput.value = val\n    shouldActiveChange && emit('activeChange', val)\n    shouldActiveChange = true\n  }\n)\n\nwatch(\n  () => color.value,\n  () => {\n    if (!props.modelValue && !showPanelColor.value) {\n      showPanelColor.value = true\n    }\n  }\n)\n\nwatch(\n  () => showPicker.value,\n  () => {\n    nextTick(() => {\n      hue.value?.update()\n      sv.value?.update()\n      alpha.value?.update()\n    })\n  }\n)\n\nprovide(colorPickerContextKey, {\n  currentColor,\n})\n\ndefineExpose({\n  /**\n   * @description current color object\n   */\n  color,\n  /**\n   * @description manually show ColorPicker\n   */\n  show,\n  /**\n   * @description manually hide ColorPicker\n   */\n  hide,\n  /**\n   * @description focus the input element\n   */\n  focus,\n  /**\n   * @description blur the input element\n   */\n  blur,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;mCA6Jc;EACZA,IAAM;AACR;;;;;;;;;;IAIM;MAAEC;IAAE,IAAIC,SAAU;IAClB,MAAAC,EAAA,GAAKC,YAAA,CAAa,OAAO;IACzB;MAAEC;IAAS,IAAIC,WAAY;IACjC,MAAMC,SAAA,GAAYC,WAAY;IAC9B,MAAMC,aAAA,GAAgBC,eAAgB;IAEtC,MAAM;MAAEC,OAAS,EAAAC,QAAA;MAAUC;IAAoB,IAAIC,kBAAA,CAAmBC,KAAO;MAC3EC,eAAiB,EAAAX;IAAA,CAClB;IAED,MAAMY,GAAA,GAAMC,GAAoC;IAChD,MAAMC,EAAA,GAAKD,GAAkC;IAC7C,MAAME,KAAA,GAAQF,GAAsC;IACpD,MAAMG,MAAA,GAASH,GAAqB;IACpC,MAAMI,UAAA,GAAaJ,GAAI;IACvB,MAAMK,QAAA,GAAWL,GAAI;IAErB,MAAM;MAAEM,SAAW;MAAAC,WAAA;MAAaC;IAAW,IAAIC,kBAAA,CAAmBL,UAAY;MAC5EM,WAAcA,CAAA;QACZ,OAAOnB,aAAc,CAAAoB,KAAA;MAAA,CACvB;MACAC,WAAWC,KAAO;QACT,IAAAC,EAAA;QACT,QAAAA,EAAA,GAAAX,MAAA,CAAAQ,KAAA,qBAAAG,EAAA,CAAAC,oBAAA,CAAAF,KAAA;MAAA,CACY;MACVG,UAAA;QACWC,aAAA;QACbC,UAAA;MAAA;IAIF;IAEA,IAAAC,kBAAc;IAAA,MACRC,KAAM,GAAAC,QAAA,KAAAC,KAAA;MAAAC,WAAA,EAAA1B,KACW,CAAA2B,SAAA;MACnBC,MAAA,EAAA5B,KAAA,CAAA6B,WAA6B;MAAAf,KAAA,EAAAd,KAChB,CAAA8B;IAAA,EACf,CAAC;IACH,MAAAC,UAAA,GAAA5B,GAAA;IAEM,MAAA6B,cAAA,GAAiB7B,GAAK;IACtB,MAAA8B,WAAA,GAAA9B,GAAiB,IAAI;IACrB,MAAA+B,cAAc,GAAAC,QAAM;MAEpB,KAAAnC,KAAA,CAAA8B,UAAiB,KAAAE,cAAe,CAAAlB,KAAA;QACpC,OAAK,aAAoB;MACvB;MACF,OAAAsB,YAAA,CAAAb,KAAA,EAAAvB,KAAA,CAAA2B,SAAA;IACA,CAAO;IACT,MAACU,YAAA,GAAAF,QAAA;MAEK,QAAAnC,KAAA,CAAA8B,UAAA,IAAwB,CAAME,cAAA,CAAAlB,KAAA,QAAAS,KAAA,CAAAT,KAAA;IAClC;IACF,MAACwB,eAAA,GAAAH,QAAA;MAEK,QAAArC,mBAAA,CAAAgB,KAAqD,GAAAd,KAAA,CAAAuC,SAAA,IAAArD,CAAA;IACzD;IAGF,MAACsD,oBAAA,GAAAL,QAAA;MAEK,OAAArC,mBAAA,CAAuBgB,KAAA,GAAAxB,QAAmC,oBAAAA,QAAA,CAAAmD,OAAA;IAC9D,CAAO;IACT,MAACC,MAAA,GAAAP,QAAA;MAEK,QACG/C,EAAA,CAAAuD,CAAA,YACLvD,EAAA,CAAGwD,EAAE,CAAQ,YAAAlD,aAAA,CAAAoB,KAAA,GACb1B,EAAG,CAAAyD,EAAA,CAAG,QAAY,EAAArD,SAAA,CAAAsB,KAAA,CAAc,EAChC1B,EAAG,CAAAwD,EAAA,CAAG,SAAU,EAAAnC,SAAU,CAAKK,KAAA;IACC,CAClC;IACF,SAACsB,aAAAU,MAAA,EAAAnB,SAAA;MAEQ,MAAAmB,MAAA,YAAArB,KAA+C;QAClD,UAAAsB,SAAA,2CAA2B;MAC7B;MACF;QAAAC,CAAA;QAAAC,CAAA;QAAAN;MAAA,IAAAG,MAAA,CAAAI,KAAA;MAEA,OAAAvB,SAAgB,WAAUqB,CAAM,KAAAC,CAAA,KAAAN,CAAA,KAAAG,MAAA,CAAAK,GAAA,4BAAAH,CAAA,KAAAC,CAAA,KAAAN,CAAA;IAChC;IAGF,SAAAvB,cAAAN,KAAA;MAEAiB,UAAA,CAAAjB,KAAA,GAAAA,KAAuC;IACrC;IACF,MAAAsC,qBAAA,GAAAC,QAAA,CAAAjC,aAAA;MAAAkC,OAAA;IAAA;IAEA,SAAAC,KAAA;MACA,IAAA7D,aAAgB,CAAAoB,KAAA,EACd;MACAM,aAAA,CAAc,IAAI;IAAA;IAGpB,SAASoC,IAAOA,CAAA;MACdJ,qBAAA,CAAsB,KAAK;MAChB/B,UAAA;IAAA;IAGb,SAASA,UAAaA,CAAA;MACpBoC,QAAA,CAAS,MAAM;QACb,IAAIzD,KAAA,CAAM8B,UAAY;UACdP,KAAA,CAAAmC,UAAA,CAAW1D,KAAA,CAAM8B,UAAU;QAAA,CAC5B;UACLP,KAAA,CAAMT,KAAQ;UACd2C,QAAA,CAAS,MAAM;YACbzB,cAAA,CAAelB,KAAQ;UAAA,CACxB;QAAA;MACH,CACD;IAAA;IAGH,SAAS6C,aAAgBA,CAAA;MACvB,IAAIjE,aAAA,CAAcoB,KAAO,EACzB;MACa,IAAAiB,UAAA,CAAAjB,KAAA;QACbO,UAAA;MACA;MACF+B,qBAAA,EAAArB,UAAA,CAAAjB,KAAA;IAEA;IACQ,SAAA8C,cAAA;MACRrC,KAAA,CAAAmC,UAAA,CAAAzB,WAAA,CAAAnB,KAAA;IAEA;IACE,SAAA+C,YAAoBA,CAAA;MACpB,MAAA/C,KAAA,GAAAS,KAAA,CAAAT,KAAyB;MACzBgD,IAAA,CAAKC,kBAAmB,EAAAjD,KAAA;MACxBgD,IAAI,CAAAE,YAAqB,EAAAlD,KAAA;MACb,IAAAd,KAAA,CAAAiE,aAAA;QACZ3E,QAAA,oBAAAA,QAAA,CAAA4E,QAAA,WAAAC,KAAA,CAAAC,GAAA,IAAAC,SAAA,CAAAD,GAAA;MACA;MAEAhB,qBAAe;MACPK,QAAA;QAAqB,MAAAa,QAAA,GACN,IAAA7C,KAAA;UACnBC,WAAA,EAAA1B,KAA6B,CAAA2B,SAAA;UAC7BC,MAAA,EAAa5B,KAAA,CAAA6B,WAAA;UACdf,KAAA,EAAAd,KAAA,CAAA8B;QACD;QACa,KAAAP,KAAA,CAAAgD,OAAA,CAAAD,QAAA;UACbjD,UAAA;QAAA;MACD,CACH;IAEA;IACE,SAAAmD,MAAA;MACApB,qBAAA,MAA6B;MAC7BU,IAAA,CAAKC,kBAAkB;MACvBD,IAAI,CAAME,YAAA;MACE,IAAAhE,KAAA,CAAA8B,UAAA,SAAiB,IAAE9B,KAAO,CAAQiE,aAAA;QAC9C3E,QAAA,oBAAAA,QAAA,CAAA4E,QAAA,WAAAC,KAAA,CAAAC,GAAA,IAAAC,SAAA,CAAAD,GAAA;MACA;MACF/C,UAAA;IAEA;IACM,SAAAoD,kBAAmBA,CAAA;MAClB,KAAA1C,UAAA,CAAAjB,KAAA,EACL;MACF0C,IAAA;MAEA/C,SAAA,CAAAK,KAAA,IAAyC4D,KAAA;IACvC;IACA,SAAsBC,UAAA3D,KAAA;MACtBA,KAAA,CAAA4D,cAAmB;MACR5D,KAAA,CAAA6D,eAAA;MACbzD,aAAA;MAEAC,UAAA;IACE;IAAoB,SACbyD,aAAWA,CAAA9D,KAAA;MAAA,QACAA,KAAA,CAAA+D,IAAA;QAChB,KAAKC,UAAW,CAAAC,KAAA;QACd,KAAAD,UAAqB,CAAAE,WAAA;QACrB,KAAAF,UAAsB,CAAAG,KAAA;UACjBnE,KAAA,CAAA4D,cAAA;UACL5D,KAAA,CAAA6D,eAAqB;UACrBtB,IAAA;UAAA/C,QACc,CAAAM,KAAA,CAAA4D,KAAA;UACd;QACA,KAAAM,UAAA,CAAAI,GAAA;UACJT,SAAA,CAAA3D,KAAA;UACF;MAEA;IACE;IACF,SAAA0D,MAAA;MAEAnE,UAAgB,CAAAO,KAAA,CAAA4D,KAAA;IACd;IACF,SAAAW,KAAA;MAEA9E,UAAgB,CAAAO,KAAA,CAAAuE,IAAA;IACd;IACEC,SAAA,OAAY;MACd,IAAAtF,KAAA,CAAA8B,UAAA;QACDG,WAAA,CAAAnB,KAAA,GAAAuB,YAAA,CAAAvB,KAAA;MAED;IAAA,EACE;IAAYyE,KACA,OAAAvF,KAAA,CAAA8B,UAAA,EAAA0D,MAAA;MACV,KAAAA,MAAa;QACXxD,cAAA,CAAAlB,KAAuB;MAAA,OACd,IAAA0E,MAAA,IAAAA,MAAqB,KAAAjE,KAAA,CAAAT,KAAa;QACtBQ,kBAAA;QACrBC,KAAA,CAAAmC,UAAA,CAAA8B,MAAuB;MAAA;IACzB,CACF;IACFD,KAAA,QAAAvF,KAAA,CAAA6B,WAAA,EAAA7B,KAAA,CAAA2B,SAAA;MAEAJ,KAAA,CAAAG,WAAA,GAAA1B,KAAA,CAAA2B,SAAA;MACEJ,KAAM,CAAAK,MAAO,GAAA5B,KAAA,CAAA6B,WAAmB,IAASN,KAAA,CAAAK,MAAA;MACzCL,KAAM,CAAAkE,UAAA;MACJ3B,IAAA,CAAAC,kBAA0B,EAAAxC,KAAA,CAAAT,KAAA;IAC1B,CAAM;IACNyE,KAAA,OAAiBlD,YAAA,CAAAvB,KAAA,EAAA4E,GAAA;MACZzD,WAAA,CAAAnB,KAAA,GAAA4E,GAAA;MACPpE,kBAAA,IAAAwC,IAAA,iBAAA4B,GAAA;MACFpE,kBAAA;IAEA;IAAAiE,KAAA,OACqBhE,KAAA,CAAAT,KAAA;MACnB,IAAS,CAAAd,KAAA,CAAA8B,UAAA,KAAAE,cAAA,CAAAlB,KAAA;QACPkB,cAAoB,CAAAlB,KAAA;MACpB;IACA,CAAqB;IACvByE,KAAA,OAAAxD,UAAA,CAAAjB,KAAA;MACF2C,QAAA;QAEA,IAAAxC,EAAA,EAAA0E,EAAA,EAAAC,EAAA;QAAA,CAAA3E,EAAA,GACcf,GAAA,CAAAY,KAAA,qBAAAG,EAAA,CAAA4E,MAAA;QACN,CAAAF,EAAA,GAAAvF,EAAA,CAAAU,KAAA,qBAAA6E,EAAA,CAAAE,MAAA;QACJ,CAAAD,EAAA,GAAKvF,KAAM,CAAcS,KAAA,SAAC,YAAA8E,EAAA,CAAeC,MAAO;MAC9C;IAAuB,CACzB;IACFC,OAAA,CAAAC,qBAAA;MACF1D;IAEA;IAAA2D,MAAA;MAEEzE,KAAM;MACJgC,IAAA;MACEC,IAAA;MACAkB,KAAA;MACAW;IAAoB;IAExB,QAAAY,IAAA,EAAAC,MAAA;MACF,OAAAC,SAAA,IAAAC,WAAA,CAAAC,KAAA,CAAAC,SAAA;QAEAC,OAA+B;QAC7BpG,GAAA,EAAAG,MAAA;QACDkG,OAAA,EAAAzE,UAAA,CAAAjB,KAAA;QAEY;QAAA;QAAA2F,MAAA;QAAA;QAIX,iBAAAJ,KAAA,CAAAjH,EAAA,EAAAsH,EAAA,qBAAAL,KAAA,CAAAjH,EAAA,EAAAuD,CAAA,cAAAsD,IAAA,CAAAU,WAAA;QAAA;QAAAC,MAAA;QAAAC,OAAA;QAIAC,UAAA,EAAAb,IAAA,CAAAa,UAAA;QAAAC,UAAA,KAAAV,KAAA,CAAAjH,EAAA,EAAA4H,SAAA,CAAAlG,KAAA;QAAAmG,UAAA;QAAAC,MAAA,EAAAC,MAAA,IAAA/F,aAAA;MAAA,CAIA;QAAAgG,OAAA,EAAAC,OAAA,QAAAC,cAAA,EAAAnB,SAAA,IAAAoB,kBAAA;UAAAC,SAAA,EAAAC,QAAA,CAAA9C,SAAA;QAAA,CAIA,GAAA+C,kBAAA;UAAAC,KAAA,EAAAC,cAAA,CAAAvB,KAAA,CAAAjH,EAAA,EAAAsH,EAAA;QAAA,IAIAmB,WAAA,CAAAC,SAAA;UACDvB,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}