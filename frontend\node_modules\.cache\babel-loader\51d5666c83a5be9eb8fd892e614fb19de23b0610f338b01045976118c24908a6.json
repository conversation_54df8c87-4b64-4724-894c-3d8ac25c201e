{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { isBoolean } from '../../../utils/types.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nconst checkTagProps = buildProps({\n  checked: <PERSON><PERSON>an,\n  disabled: Boolean,\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"info\", \"warning\", \"danger\"],\n    default: \"primary\"\n  }\n});\nconst checkTagEmits = {\n  \"update:checked\": value => isBoolean(value),\n  [CHANGE_EVENT]: value => isBoolean(value)\n};\nexport { checkTagEmits, checkTagProps };", "map": {"version": 3, "names": ["checkTagProps", "buildProps", "checked", "Boolean", "disabled", "type", "String", "values", "default", "checkTagEmits", "value", "isBoolean", "CHANGE_EVENT"], "sources": ["../../../../../../packages/components/check-tag/src/check-tag.ts"], "sourcesContent": ["import { buildProps, isBoolean } from '@element-plus/utils'\nimport { CHANGE_EVENT } from '@element-plus/constants'\n\nimport type CheckTag from './check-tag.vue'\nimport type { ExtractPropTypes } from 'vue'\n\nexport const checkTagProps = buildProps({\n  /**\n   * @description is checked\n   */\n  checked: Boolean,\n  /**\n   * @description whether the check-tag is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description type of Tag\n   */\n  type: {\n    type: String,\n    values: ['primary', 'success', 'info', 'warning', 'danger'],\n    default: 'primary',\n  },\n} as const)\nexport type CheckTagProps = ExtractPropTypes<typeof checkTagProps>\n\nexport const checkTagEmits = {\n  'update:checked': (value: boolean) => isBoolean(value),\n  [CHANGE_EVENT]: (value: boolean) => isBoolean(value),\n}\nexport type CheckTagEmits = typeof checkTagEmits\n\nexport type CheckTagInstance = InstanceType<typeof CheckTag> & unknown\n"], "mappings": ";;;AAEY,MAACA,aAAa,GAAGC,UAAU,CAAC;EACtCC,OAAO,EAAEC,OAAO;EAChBC,QAAQ,EAAED,OAAO;EACjBE,IAAI,EAAE;IACJA,IAAI,EAAEC,MAAM;IACZC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC;IAC3DC,OAAO,EAAE;EACb;AACA,CAAC;AACW,MAACC,aAAa,GAAG;EAC3B,gBAAgB,EAAGC,KAAK,IAAKC,SAAS,CAACD,KAAK,CAAC;EAC7C,CAACE,YAAY,GAAIF,KAAK,IAAKC,SAAS,CAACD,KAAK;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}