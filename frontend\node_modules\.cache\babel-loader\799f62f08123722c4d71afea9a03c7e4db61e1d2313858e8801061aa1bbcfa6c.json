{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, inject, ref, openBlock, createElementBlock, normalizeClass, unref, withModifiers, renderSlot } from 'vue';\nimport { throwError } from '../../../utils/error.mjs';\nimport { uploadContextKey } from './constants.mjs';\nimport { uploadDraggerProps, uploadDraggerEmits } from './upload-dragger.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nconst COMPONENT_NAME = \"ElUploadDrag\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: uploadDraggerProps,\n  emits: uploadDraggerEmits,\n  setup(__props, {\n    emit\n  }) {\n    const uploaderContext = inject(uploadContextKey);\n    if (!uploaderContext) {\n      throwError(COMPONENT_NAME, \"usage: <el-upload><el-upload-dragger /></el-upload>\");\n    }\n    const ns = useNamespace(\"upload\");\n    const dragover = ref(false);\n    const disabled = useFormDisabled();\n    const onDrop = e => {\n      if (disabled.value) return;\n      dragover.value = false;\n      e.stopPropagation();\n      const files = Array.from(e.dataTransfer.files);\n      const items = e.dataTransfer.items || [];\n      files.forEach((file, index) => {\n        var _a;\n        const item = items[index];\n        const entry = (_a = item == null ? void 0 : item.webkitGetAsEntry) == null ? void 0 : _a.call(item);\n        if (entry) {\n          file.isDirectory = entry.isDirectory;\n        }\n      });\n      emit(\"file\", files);\n    };\n    const onDragover = () => {\n      if (!disabled.value) dragover.value = true;\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(\"dragger\"), unref(ns).is(\"dragover\", dragover.value)]),\n        onDrop: withModifiers(onDrop, [\"prevent\"]),\n        onDragover: withModifiers(onDragover, [\"prevent\"]),\n        onDragleave: withModifiers($event => dragover.value = false, [\"prevent\"])\n      }, [renderSlot(_ctx.$slots, \"default\")], 42, [\"onDrop\", \"onDragover\", \"onDragleave\"]);\n    };\n  }\n});\nvar UploadDragger = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"upload-dragger.vue\"]]);\nexport { UploadDragger as default };", "map": {"version": 3, "names": ["name", "COMPONENT_NAME", "uploaderContext", "inject", "uploadContextKey", "throwError", "ns", "useNamespace", "dragover", "ref", "disabled", "useFormDisabled", "onDrop", "e", "value", "stopPropagation", "files", "Array", "from", "dataTransfer", "items", "for<PERSON>ach", "file", "index", "_a", "item", "entry", "webkitGetAsEntry", "call", "isDirectory", "emit", "onDragover"], "sources": ["../../../../../../packages/components/upload/src/upload-dragger.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[ns.b('dragger'), ns.is('dragover', dragover)]\"\n    @drop.prevent=\"onDrop\"\n    @dragover.prevent=\"onDragover\"\n    @dragleave.prevent=\"dragover = false\"\n  >\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { inject, ref } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { throwError } from '@element-plus/utils/error'\nimport { uploadContextKey } from './constants'\nimport { uploadDraggerEmits, uploadDraggerProps } from './upload-dragger'\n\nimport type { UploadRawFile } from './upload'\n\nconst COMPONENT_NAME = 'ElUploadDrag'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\ndefineProps(uploadDraggerProps)\nconst emit = defineEmits(uploadDraggerEmits)\n\nconst uploaderContext = inject(uploadContextKey)\nif (!uploaderContext) {\n  throwError(\n    COMPONENT_NAME,\n    'usage: <el-upload><el-upload-dragger /></el-upload>'\n  )\n}\n\nconst ns = useNamespace('upload')\nconst dragover = ref(false)\nconst disabled = useFormDisabled()\n\nconst onDrop = (e: DragEvent) => {\n  if (disabled.value) return\n  dragover.value = false\n\n  e.stopPropagation()\n\n  const files = Array.from(e.dataTransfer!.files) as UploadRawFile[]\n  const items = e.dataTransfer!.items || []\n  files.forEach((file, index) => {\n    const item = items[index]\n    const entry = item?.webkitGetAsEntry?.()\n    if (entry) {\n      file.isDirectory = entry.isDirectory\n    }\n  })\n  emit('file', files)\n}\n\nconst onDragover = () => {\n  if (!disabled.value) dragover.value = true\n}\n</script>\n"], "mappings": ";;;;;;;;;;mCAuBc;EACZA,IAAM,EAAAC;AACR;;;;;;;;IAKM,MAAAC,eAAA,GAAkBC,MAAA,CAAOC,gBAAgB;IAC/C,IAAI,CAACF,eAAiB;MACpBG,UAAA,CAAAJ,cAAA;IAAA;IAEE,MAAAK,EAAA,GAAAC,YAAA;IACF,MAAAC,QAAA,GAAAC,GAAA;IACF,MAAAC,QAAA,GAAAC,eAAA;IAEM,MAAAC,MAAA,GAAAC,CAAA;MACA,IAAAH,QAAA,CAAAI,KAAA,EACN;MAEMN,QAAA,CAAAM,KAAU,GAAiB;MAC/BD,CAAA,CAAAE,eAAoB;MACpB,MAAAC,KAAiB,GAAAC,KAAA,CAAAC,IAAA,CAAAL,CAAA,CAAAM,YAAA,CAAAH,KAAA;MAEjB,MAAkBI,KAAA,GAAAP,CAAA,CAAAM,YAAA,CAAAC,KAAA;MAElBJ,KAAA,CAAMK,OAAQ,EAAAC,IAAA,EAAWC,KAAA;QACzB,IAAMC,EAAQ;QACR,MAAAC,IAAA,GAASL,KAAA,CAAMG,KAAU;QACvB,MAAAG,KAAA,IAAAF,EAAA,GAAkBC,IAAA,oBAAAA,IAAA,CAAAE,gBAAA,qBAAAH,EAAA,CAAAI,IAAA,CAAAH,IAAA;QAClB,IAAAC,KAAA;UACNJ,IAAW,CAAAO,WAAA,GAAAH,KAAA,CAAAG,WAAA;QACT;MAAyB,CAC3B;MACFC,IAAC,SAAAd,KAAA;IACD;IACF,MAAAe,UAAA,GAAAA,CAAA;MAEA,IAAM,CAAArB,QAAA,CAAAI,KAAmB,EACvBN,QAAK,CAAAM,KAAgB;IAAiB,CACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}