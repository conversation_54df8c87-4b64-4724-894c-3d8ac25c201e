{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, renderSlot } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElHeader\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    height: {\n      type: String,\n      default: null\n    }\n  },\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"header\");\n    const style = computed(() => {\n      return props.height ? ns.cssVarBlock({\n        height: props.height\n      }) : {};\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"header\", {\n        class: normalizeClass(unref(ns).b()),\n        style: normalizeStyle(unref(style))\n      }, [renderSlot(_ctx.$slots, \"default\")], 6);\n    };\n  }\n});\nvar Header = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"header.vue\"]]);\nexport { Header as default };", "map": {"version": 3, "names": ["name", "height", "props", "_ctx", "_cache", "openBlock", "createElementBlock", "class", "normalizeClass", "unref", "ns", "b"], "sources": ["../../../../../../packages/components/container/src/header.vue"], "sourcesContent": ["<template>\n  <header :class=\"ns.b()\" :style=\"style\">\n    <slot />\n  </header>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: '<PERSON>Header',\n})\n\nconst props = defineProps({\n  /**\n   * @description height of the header\n   */\n  height: {\n    type: String,\n    default: null,\n  },\n})\n\nconst ns = useNamespace('header')\nconst style = computed(() => {\n  return props.height\n    ? (ns.cssVarBlock({\n        height: props.height,\n      }) as CSSProperties)\n    : {}\n})\n</script>\n"], "mappings": ";;;mCAYc;EACZA,IAAM;AACR;;;;;;;;;;;;;;QAYMC,MAAA,EAAAC,KAAA,CAAAD;MACN,CAAM;IACJ,CAAO;IACa,QAAAE,IACN,EAAMC,MAAA;MAChB,OACCC,SAAA,IAAAC,kBAAA;QACNC,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAAC,EAAA,EAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}