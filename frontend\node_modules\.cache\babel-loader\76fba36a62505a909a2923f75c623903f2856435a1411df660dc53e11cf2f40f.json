{"ast": null, "code": "import { resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nexport function render(_ctx, _cache) {\n  const _component_router_view = _resolveComponent(\"router-view\");\n  return _openBlock(), _createBlock(_component_router_view);\n}", "map": {"version": 3, "names": ["_createBlock", "_component_router_view"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\App.vue"], "sourcesContent": ["<template>\n  <router-view />\n</template>\n\n<style lang=\"scss\">\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  height: 100vh;\n  margin: 0;\n  padding: 0;\n}\n\nhtml,\nbody {\n  margin: 0;\n  padding: 0;\n  height: 100%;\n}\n\n* {\n  box-sizing: border-box;\n}\n</style>\n"], "mappings": ";;;uBACEA,YAAA,CAAeC,sBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}