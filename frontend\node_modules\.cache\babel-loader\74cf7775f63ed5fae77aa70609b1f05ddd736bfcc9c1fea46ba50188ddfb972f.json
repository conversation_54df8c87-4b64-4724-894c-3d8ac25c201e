{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { numberInputToObject, rgbaToHex, rgbToHex, rgbToHsl, rgbToHsv } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { inputToRGB } from './format-input';\nimport { bound01, boundAlpha, clamp01 } from './util.js';\nvar TinyColor = /** @class */function () {\n  function TinyColor(color, opts) {\n    if (color === void 0) {\n      color = '';\n    }\n    if (opts === void 0) {\n      opts = {};\n    }\n    var _a;\n    // If input is already a tinycolor, return itself\n    if (color instanceof TinyColor) {\n      // eslint-disable-next-line no-constructor-return\n      return color;\n    }\n    if (typeof color === 'number') {\n      color = numberInputToObject(color);\n    }\n    this.originalInput = color;\n    var rgb = inputToRGB(color);\n    this.originalInput = color;\n    this.r = rgb.r;\n    this.g = rgb.g;\n    this.b = rgb.b;\n    this.a = rgb.a;\n    this.roundA = Math.round(100 * this.a) / 100;\n    this.format = (_a = opts.format) !== null && _a !== void 0 ? _a : rgb.format;\n    this.gradientType = opts.gradientType;\n    // Don't let the range of [0,255] come back in [0,1].\n    // Potentially lose a little bit of precision here, but will fix issues where\n    // .5 gets interpreted as half of the total, instead of half of 1\n    // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n    if (this.r < 1) {\n      this.r = Math.round(this.r);\n    }\n    if (this.g < 1) {\n      this.g = Math.round(this.g);\n    }\n    if (this.b < 1) {\n      this.b = Math.round(this.b);\n    }\n    this.isValid = rgb.ok;\n  }\n  TinyColor.prototype.isDark = function () {\n    return this.getBrightness() < 128;\n  };\n  TinyColor.prototype.isLight = function () {\n    return !this.isDark();\n  };\n  /**\n   * Returns the perceived brightness of the color, from 0-255.\n   */\n  TinyColor.prototype.getBrightness = function () {\n    // http://www.w3.org/TR/AERT#color-contrast\n    var rgb = this.toRgb();\n    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n  };\n  /**\n   * Returns the perceived luminance of a color, from 0-1.\n   */\n  TinyColor.prototype.getLuminance = function () {\n    // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n    var rgb = this.toRgb();\n    var R;\n    var G;\n    var B;\n    var RsRGB = rgb.r / 255;\n    var GsRGB = rgb.g / 255;\n    var BsRGB = rgb.b / 255;\n    if (RsRGB <= 0.03928) {\n      R = RsRGB / 12.92;\n    } else {\n      // eslint-disable-next-line prefer-exponentiation-operator\n      R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n    }\n    if (GsRGB <= 0.03928) {\n      G = GsRGB / 12.92;\n    } else {\n      // eslint-disable-next-line prefer-exponentiation-operator\n      G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n    }\n    if (BsRGB <= 0.03928) {\n      B = BsRGB / 12.92;\n    } else {\n      // eslint-disable-next-line prefer-exponentiation-operator\n      B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n    }\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  };\n  /**\n   * Returns the alpha value of a color, from 0-1.\n   */\n  TinyColor.prototype.getAlpha = function () {\n    return this.a;\n  };\n  /**\n   * Sets the alpha value on the current color.\n   *\n   * @param alpha - The new alpha value. The accepted range is 0-1.\n   */\n  TinyColor.prototype.setAlpha = function (alpha) {\n    this.a = boundAlpha(alpha);\n    this.roundA = Math.round(100 * this.a) / 100;\n    return this;\n  };\n  /**\n   * Returns whether the color is monochrome.\n   */\n  TinyColor.prototype.isMonochrome = function () {\n    var s = this.toHsl().s;\n    return s === 0;\n  };\n  /**\n   * Returns the object as a HSVA object.\n   */\n  TinyColor.prototype.toHsv = function () {\n    var hsv = rgbToHsv(this.r, this.g, this.b);\n    return {\n      h: hsv.h * 360,\n      s: hsv.s,\n      v: hsv.v,\n      a: this.a\n    };\n  };\n  /**\n   * Returns the hsva values interpolated into a string with the following format:\n   * \"hsva(xxx, xxx, xxx, xx)\".\n   */\n  TinyColor.prototype.toHsvString = function () {\n    var hsv = rgbToHsv(this.r, this.g, this.b);\n    var h = Math.round(hsv.h * 360);\n    var s = Math.round(hsv.s * 100);\n    var v = Math.round(hsv.v * 100);\n    return this.a === 1 ? \"hsv(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%)\") : \"hsva(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%, \").concat(this.roundA, \")\");\n  };\n  /**\n   * Returns the object as a HSLA object.\n   */\n  TinyColor.prototype.toHsl = function () {\n    var hsl = rgbToHsl(this.r, this.g, this.b);\n    return {\n      h: hsl.h * 360,\n      s: hsl.s,\n      l: hsl.l,\n      a: this.a\n    };\n  };\n  /**\n   * Returns the hsla values interpolated into a string with the following format:\n   * \"hsla(xxx, xxx, xxx, xx)\".\n   */\n  TinyColor.prototype.toHslString = function () {\n    var hsl = rgbToHsl(this.r, this.g, this.b);\n    var h = Math.round(hsl.h * 360);\n    var s = Math.round(hsl.s * 100);\n    var l = Math.round(hsl.l * 100);\n    return this.a === 1 ? \"hsl(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%)\") : \"hsla(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%, \").concat(this.roundA, \")\");\n  };\n  /**\n   * Returns the hex value of the color.\n   * @param allow3Char will shorten hex value to 3 char if possible\n   */\n  TinyColor.prototype.toHex = function (allow3Char) {\n    if (allow3Char === void 0) {\n      allow3Char = false;\n    }\n    return rgbToHex(this.r, this.g, this.b, allow3Char);\n  };\n  /**\n   * Returns the hex value of the color -with a # prefixed.\n   * @param allow3Char will shorten hex value to 3 char if possible\n   */\n  TinyColor.prototype.toHexString = function (allow3Char) {\n    if (allow3Char === void 0) {\n      allow3Char = false;\n    }\n    return '#' + this.toHex(allow3Char);\n  };\n  /**\n   * Returns the hex 8 value of the color.\n   * @param allow4Char will shorten hex value to 4 char if possible\n   */\n  TinyColor.prototype.toHex8 = function (allow4Char) {\n    if (allow4Char === void 0) {\n      allow4Char = false;\n    }\n    return rgbaToHex(this.r, this.g, this.b, this.a, allow4Char);\n  };\n  /**\n   * Returns the hex 8 value of the color -with a # prefixed.\n   * @param allow4Char will shorten hex value to 4 char if possible\n   */\n  TinyColor.prototype.toHex8String = function (allow4Char) {\n    if (allow4Char === void 0) {\n      allow4Char = false;\n    }\n    return '#' + this.toHex8(allow4Char);\n  };\n  /**\n   * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n   * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n   */\n  TinyColor.prototype.toHexShortString = function (allowShortChar) {\n    if (allowShortChar === void 0) {\n      allowShortChar = false;\n    }\n    return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n  };\n  /**\n   * Returns the object as a RGBA object.\n   */\n  TinyColor.prototype.toRgb = function () {\n    return {\n      r: Math.round(this.r),\n      g: Math.round(this.g),\n      b: Math.round(this.b),\n      a: this.a\n    };\n  };\n  /**\n   * Returns the RGBA values interpolated into a string with the following format:\n   * \"RGBA(xxx, xxx, xxx, xx)\".\n   */\n  TinyColor.prototype.toRgbString = function () {\n    var r = Math.round(this.r);\n    var g = Math.round(this.g);\n    var b = Math.round(this.b);\n    return this.a === 1 ? \"rgb(\".concat(r, \", \").concat(g, \", \").concat(b, \")\") : \"rgba(\".concat(r, \", \").concat(g, \", \").concat(b, \", \").concat(this.roundA, \")\");\n  };\n  /**\n   * Returns the object as a RGBA object.\n   */\n  TinyColor.prototype.toPercentageRgb = function () {\n    var fmt = function (x) {\n      return \"\".concat(Math.round(bound01(x, 255) * 100), \"%\");\n    };\n    return {\n      r: fmt(this.r),\n      g: fmt(this.g),\n      b: fmt(this.b),\n      a: this.a\n    };\n  };\n  /**\n   * Returns the RGBA relative values interpolated into a string\n   */\n  TinyColor.prototype.toPercentageRgbString = function () {\n    var rnd = function (x) {\n      return Math.round(bound01(x, 255) * 100);\n    };\n    return this.a === 1 ? \"rgb(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%)\") : \"rgba(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%, \").concat(this.roundA, \")\");\n  };\n  /**\n   * The 'real' name of the color -if there is one.\n   */\n  TinyColor.prototype.toName = function () {\n    if (this.a === 0) {\n      return 'transparent';\n    }\n    if (this.a < 1) {\n      return false;\n    }\n    var hex = '#' + rgbToHex(this.r, this.g, this.b, false);\n    for (var _i = 0, _a = Object.entries(names); _i < _a.length; _i++) {\n      var _b = _a[_i],\n        key = _b[0],\n        value = _b[1];\n      if (hex === value) {\n        return key;\n      }\n    }\n    return false;\n  };\n  TinyColor.prototype.toString = function (format) {\n    var formatSet = Boolean(format);\n    format = format !== null && format !== void 0 ? format : this.format;\n    var formattedString = false;\n    var hasAlpha = this.a < 1 && this.a >= 0;\n    var needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n    if (needsAlphaFormat) {\n      // Special case for \"transparent\", all other non-alpha formats\n      // will return rgba when there is transparency.\n      if (format === 'name' && this.a === 0) {\n        return this.toName();\n      }\n      return this.toRgbString();\n    }\n    if (format === 'rgb') {\n      formattedString = this.toRgbString();\n    }\n    if (format === 'prgb') {\n      formattedString = this.toPercentageRgbString();\n    }\n    if (format === 'hex' || format === 'hex6') {\n      formattedString = this.toHexString();\n    }\n    if (format === 'hex3') {\n      formattedString = this.toHexString(true);\n    }\n    if (format === 'hex4') {\n      formattedString = this.toHex8String(true);\n    }\n    if (format === 'hex8') {\n      formattedString = this.toHex8String();\n    }\n    if (format === 'name') {\n      formattedString = this.toName();\n    }\n    if (format === 'hsl') {\n      formattedString = this.toHslString();\n    }\n    if (format === 'hsv') {\n      formattedString = this.toHsvString();\n    }\n    return formattedString || this.toHexString();\n  };\n  TinyColor.prototype.toNumber = function () {\n    return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n  };\n  TinyColor.prototype.clone = function () {\n    return new TinyColor(this.toString());\n  };\n  /**\n   * Lighten the color a given amount. Providing 100 will always return white.\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.lighten = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    var hsl = this.toHsl();\n    hsl.l += amount / 100;\n    hsl.l = clamp01(hsl.l);\n    return new TinyColor(hsl);\n  };\n  /**\n   * Brighten the color a given amount, from 0 to 100.\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.brighten = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    var rgb = this.toRgb();\n    rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n    rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n    rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n    return new TinyColor(rgb);\n  };\n  /**\n   * Darken the color a given amount, from 0 to 100.\n   * Providing 100 will always return black.\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.darken = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    var hsl = this.toHsl();\n    hsl.l -= amount / 100;\n    hsl.l = clamp01(hsl.l);\n    return new TinyColor(hsl);\n  };\n  /**\n   * Mix the color with pure white, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return white.\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.tint = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    return this.mix('white', amount);\n  };\n  /**\n   * Mix the color with pure black, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return black.\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.shade = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    return this.mix('black', amount);\n  };\n  /**\n   * Desaturate the color a given amount, from 0 to 100.\n   * Providing 100 will is the same as calling greyscale\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.desaturate = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    var hsl = this.toHsl();\n    hsl.s -= amount / 100;\n    hsl.s = clamp01(hsl.s);\n    return new TinyColor(hsl);\n  };\n  /**\n   * Saturate the color a given amount, from 0 to 100.\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.saturate = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    var hsl = this.toHsl();\n    hsl.s += amount / 100;\n    hsl.s = clamp01(hsl.s);\n    return new TinyColor(hsl);\n  };\n  /**\n   * Completely desaturates a color into greyscale.\n   * Same as calling `desaturate(100)`\n   */\n  TinyColor.prototype.greyscale = function () {\n    return this.desaturate(100);\n  };\n  /**\n   * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n   * Values outside of this range will be wrapped into this range.\n   */\n  TinyColor.prototype.spin = function (amount) {\n    var hsl = this.toHsl();\n    var hue = (hsl.h + amount) % 360;\n    hsl.h = hue < 0 ? 360 + hue : hue;\n    return new TinyColor(hsl);\n  };\n  /**\n   * Mix the current color a given amount with another color, from 0 to 100.\n   * 0 means no mixing (return current color).\n   */\n  TinyColor.prototype.mix = function (color, amount) {\n    if (amount === void 0) {\n      amount = 50;\n    }\n    var rgb1 = this.toRgb();\n    var rgb2 = new TinyColor(color).toRgb();\n    var p = amount / 100;\n    var rgba = {\n      r: (rgb2.r - rgb1.r) * p + rgb1.r,\n      g: (rgb2.g - rgb1.g) * p + rgb1.g,\n      b: (rgb2.b - rgb1.b) * p + rgb1.b,\n      a: (rgb2.a - rgb1.a) * p + rgb1.a\n    };\n    return new TinyColor(rgba);\n  };\n  TinyColor.prototype.analogous = function (results, slices) {\n    if (results === void 0) {\n      results = 6;\n    }\n    if (slices === void 0) {\n      slices = 30;\n    }\n    var hsl = this.toHsl();\n    var part = 360 / slices;\n    var ret = [this];\n    for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results;) {\n      hsl.h = (hsl.h + part) % 360;\n      ret.push(new TinyColor(hsl));\n    }\n    return ret;\n  };\n  /**\n   * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n   */\n  TinyColor.prototype.complement = function () {\n    var hsl = this.toHsl();\n    hsl.h = (hsl.h + 180) % 360;\n    return new TinyColor(hsl);\n  };\n  TinyColor.prototype.monochromatic = function (results) {\n    if (results === void 0) {\n      results = 6;\n    }\n    var hsv = this.toHsv();\n    var h = hsv.h;\n    var s = hsv.s;\n    var v = hsv.v;\n    var res = [];\n    var modification = 1 / results;\n    while (results--) {\n      res.push(new TinyColor({\n        h: h,\n        s: s,\n        v: v\n      }));\n      v = (v + modification) % 1;\n    }\n    return res;\n  };\n  TinyColor.prototype.splitcomplement = function () {\n    var hsl = this.toHsl();\n    var h = hsl.h;\n    return [this, new TinyColor({\n      h: (h + 72) % 360,\n      s: hsl.s,\n      l: hsl.l\n    }), new TinyColor({\n      h: (h + 216) % 360,\n      s: hsl.s,\n      l: hsl.l\n    })];\n  };\n  /**\n   * Compute how the color would appear on a background\n   */\n  TinyColor.prototype.onBackground = function (background) {\n    var fg = this.toRgb();\n    var bg = new TinyColor(background).toRgb();\n    var alpha = fg.a + bg.a * (1 - fg.a);\n    return new TinyColor({\n      r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n      g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n      b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n      a: alpha\n    });\n  };\n  /**\n   * Alias for `polyad(3)`\n   */\n  TinyColor.prototype.triad = function () {\n    return this.polyad(3);\n  };\n  /**\n   * Alias for `polyad(4)`\n   */\n  TinyColor.prototype.tetrad = function () {\n    return this.polyad(4);\n  };\n  /**\n   * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n   * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n   */\n  TinyColor.prototype.polyad = function (n) {\n    var hsl = this.toHsl();\n    var h = hsl.h;\n    var result = [this];\n    var increment = 360 / n;\n    for (var i = 1; i < n; i++) {\n      result.push(new TinyColor({\n        h: (h + i * increment) % 360,\n        s: hsl.s,\n        l: hsl.l\n      }));\n    }\n    return result;\n  };\n  /**\n   * compare color vs current color\n   */\n  TinyColor.prototype.equals = function (color) {\n    return this.toRgbString() === new TinyColor(color).toRgbString();\n  };\n  return TinyColor;\n}();\nexport { TinyColor };\n// kept for backwards compatability with v1\nexport function tinycolor(color, opts) {\n  if (color === void 0) {\n    color = '';\n  }\n  if (opts === void 0) {\n    opts = {};\n  }\n  return new TinyColor(color, opts);\n}", "map": {"version": 3, "names": ["numberInputToObject", "rgbaToHex", "rgbToHex", "rgbToHsl", "rgbToHsv", "names", "inputToRGB", "bound01", "boundAlpha", "clamp01", "TinyColor", "color", "opts", "_a", "originalInput", "rgb", "r", "g", "b", "a", "roundA", "Math", "round", "format", "gradientType", "<PERSON><PERSON><PERSON><PERSON>", "ok", "prototype", "isDark", "getBrightness", "isLight", "toRgb", "getLuminance", "R", "G", "B", "RsRGB", "GsRGB", "BsRGB", "pow", "get<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "alpha", "isMonochrome", "s", "toHsl", "toHsv", "hsv", "h", "v", "toHsvString", "concat", "hsl", "l", "toHslString", "toHex", "allow3Char", "toHexString", "toHex8", "allow4Char", "toHex8String", "toHexShortString", "allowShortChar", "toRgbString", "toPercentageRgb", "fmt", "x", "toPercentageRgbString", "rnd", "to<PERSON>ame", "hex", "_i", "Object", "entries", "length", "_b", "key", "value", "toString", "formatSet", "Boolean", "formattedString", "has<PERSON><PERSON><PERSON>", "needsAlphaFormat", "startsWith", "toNumber", "clone", "lighten", "amount", "brighten", "max", "min", "darken", "tint", "mix", "shade", "desaturate", "saturate", "greyscale", "spin", "hue", "rgb1", "rgb2", "p", "rgba", "analogous", "results", "slices", "part", "ret", "push", "complement", "monochromatic", "res", "modification", "splitcomplement", "onBackground", "background", "fg", "bg", "triad", "polyad", "tetrad", "n", "result", "increment", "i", "equals", "tinycolor"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/@ctrl/tinycolor/dist/module/index.js"], "sourcesContent": ["import { numberInputToObject, rgbaToHex, rgbToHex, rgbToHsl, rgbToHsv } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { inputToRGB } from './format-input';\nimport { bound01, boundAlpha, clamp01 } from './util.js';\nvar TinyColor = /** @class */ (function () {\n    function TinyColor(color, opts) {\n        if (color === void 0) { color = ''; }\n        if (opts === void 0) { opts = {}; }\n        var _a;\n        // If input is already a tinycolor, return itself\n        if (color instanceof TinyColor) {\n            // eslint-disable-next-line no-constructor-return\n            return color;\n        }\n        if (typeof color === 'number') {\n            color = numberInputToObject(color);\n        }\n        this.originalInput = color;\n        var rgb = inputToRGB(color);\n        this.originalInput = color;\n        this.r = rgb.r;\n        this.g = rgb.g;\n        this.b = rgb.b;\n        this.a = rgb.a;\n        this.roundA = Math.round(100 * this.a) / 100;\n        this.format = (_a = opts.format) !== null && _a !== void 0 ? _a : rgb.format;\n        this.gradientType = opts.gradientType;\n        // Don't let the range of [0,255] come back in [0,1].\n        // Potentially lose a little bit of precision here, but will fix issues where\n        // .5 gets interpreted as half of the total, instead of half of 1\n        // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n        if (this.r < 1) {\n            this.r = Math.round(this.r);\n        }\n        if (this.g < 1) {\n            this.g = Math.round(this.g);\n        }\n        if (this.b < 1) {\n            this.b = Math.round(this.b);\n        }\n        this.isValid = rgb.ok;\n    }\n    TinyColor.prototype.isDark = function () {\n        return this.getBrightness() < 128;\n    };\n    TinyColor.prototype.isLight = function () {\n        return !this.isDark();\n    };\n    /**\n     * Returns the perceived brightness of the color, from 0-255.\n     */\n    TinyColor.prototype.getBrightness = function () {\n        // http://www.w3.org/TR/AERT#color-contrast\n        var rgb = this.toRgb();\n        return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n    };\n    /**\n     * Returns the perceived luminance of a color, from 0-1.\n     */\n    TinyColor.prototype.getLuminance = function () {\n        // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n        var rgb = this.toRgb();\n        var R;\n        var G;\n        var B;\n        var RsRGB = rgb.r / 255;\n        var GsRGB = rgb.g / 255;\n        var BsRGB = rgb.b / 255;\n        if (RsRGB <= 0.03928) {\n            R = RsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (GsRGB <= 0.03928) {\n            G = GsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (BsRGB <= 0.03928) {\n            B = BsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n        }\n        return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n    };\n    /**\n     * Returns the alpha value of a color, from 0-1.\n     */\n    TinyColor.prototype.getAlpha = function () {\n        return this.a;\n    };\n    /**\n     * Sets the alpha value on the current color.\n     *\n     * @param alpha - The new alpha value. The accepted range is 0-1.\n     */\n    TinyColor.prototype.setAlpha = function (alpha) {\n        this.a = boundAlpha(alpha);\n        this.roundA = Math.round(100 * this.a) / 100;\n        return this;\n    };\n    /**\n     * Returns whether the color is monochrome.\n     */\n    TinyColor.prototype.isMonochrome = function () {\n        var s = this.toHsl().s;\n        return s === 0;\n    };\n    /**\n     * Returns the object as a HSVA object.\n     */\n    TinyColor.prototype.toHsv = function () {\n        var hsv = rgbToHsv(this.r, this.g, this.b);\n        return { h: hsv.h * 360, s: hsv.s, v: hsv.v, a: this.a };\n    };\n    /**\n     * Returns the hsva values interpolated into a string with the following format:\n     * \"hsva(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHsvString = function () {\n        var hsv = rgbToHsv(this.r, this.g, this.b);\n        var h = Math.round(hsv.h * 360);\n        var s = Math.round(hsv.s * 100);\n        var v = Math.round(hsv.v * 100);\n        return this.a === 1 ? \"hsv(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%)\") : \"hsva(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a HSLA object.\n     */\n    TinyColor.prototype.toHsl = function () {\n        var hsl = rgbToHsl(this.r, this.g, this.b);\n        return { h: hsl.h * 360, s: hsl.s, l: hsl.l, a: this.a };\n    };\n    /**\n     * Returns the hsla values interpolated into a string with the following format:\n     * \"hsla(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHslString = function () {\n        var hsl = rgbToHsl(this.r, this.g, this.b);\n        var h = Math.round(hsl.h * 360);\n        var s = Math.round(hsl.s * 100);\n        var l = Math.round(hsl.l * 100);\n        return this.a === 1 ? \"hsl(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%)\") : \"hsla(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the hex value of the color.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHex = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return rgbToHex(this.r, this.g, this.b, allow3Char);\n    };\n    /**\n     * Returns the hex value of the color -with a # prefixed.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHexString = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return '#' + this.toHex(allow3Char);\n    };\n    /**\n     * Returns the hex 8 value of the color.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8 = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return rgbaToHex(this.r, this.g, this.b, this.a, allow4Char);\n    };\n    /**\n     * Returns the hex 8 value of the color -with a # prefixed.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8String = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return '#' + this.toHex8(allow4Char);\n    };\n    /**\n     * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n     * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n     */\n    TinyColor.prototype.toHexShortString = function (allowShortChar) {\n        if (allowShortChar === void 0) { allowShortChar = false; }\n        return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toRgb = function () {\n        return {\n            r: Math.round(this.r),\n            g: Math.round(this.g),\n            b: Math.round(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA values interpolated into a string with the following format:\n     * \"RGBA(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toRgbString = function () {\n        var r = Math.round(this.r);\n        var g = Math.round(this.g);\n        var b = Math.round(this.b);\n        return this.a === 1 ? \"rgb(\".concat(r, \", \").concat(g, \", \").concat(b, \")\") : \"rgba(\".concat(r, \", \").concat(g, \", \").concat(b, \", \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toPercentageRgb = function () {\n        var fmt = function (x) { return \"\".concat(Math.round(bound01(x, 255) * 100), \"%\"); };\n        return {\n            r: fmt(this.r),\n            g: fmt(this.g),\n            b: fmt(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA relative values interpolated into a string\n     */\n    TinyColor.prototype.toPercentageRgbString = function () {\n        var rnd = function (x) { return Math.round(bound01(x, 255) * 100); };\n        return this.a === 1\n            ? \"rgb(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%)\")\n            : \"rgba(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * The 'real' name of the color -if there is one.\n     */\n    TinyColor.prototype.toName = function () {\n        if (this.a === 0) {\n            return 'transparent';\n        }\n        if (this.a < 1) {\n            return false;\n        }\n        var hex = '#' + rgbToHex(this.r, this.g, this.b, false);\n        for (var _i = 0, _a = Object.entries(names); _i < _a.length; _i++) {\n            var _b = _a[_i], key = _b[0], value = _b[1];\n            if (hex === value) {\n                return key;\n            }\n        }\n        return false;\n    };\n    TinyColor.prototype.toString = function (format) {\n        var formatSet = Boolean(format);\n        format = format !== null && format !== void 0 ? format : this.format;\n        var formattedString = false;\n        var hasAlpha = this.a < 1 && this.a >= 0;\n        var needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n        if (needsAlphaFormat) {\n            // Special case for \"transparent\", all other non-alpha formats\n            // will return rgba when there is transparency.\n            if (format === 'name' && this.a === 0) {\n                return this.toName();\n            }\n            return this.toRgbString();\n        }\n        if (format === 'rgb') {\n            formattedString = this.toRgbString();\n        }\n        if (format === 'prgb') {\n            formattedString = this.toPercentageRgbString();\n        }\n        if (format === 'hex' || format === 'hex6') {\n            formattedString = this.toHexString();\n        }\n        if (format === 'hex3') {\n            formattedString = this.toHexString(true);\n        }\n        if (format === 'hex4') {\n            formattedString = this.toHex8String(true);\n        }\n        if (format === 'hex8') {\n            formattedString = this.toHex8String();\n        }\n        if (format === 'name') {\n            formattedString = this.toName();\n        }\n        if (format === 'hsl') {\n            formattedString = this.toHslString();\n        }\n        if (format === 'hsv') {\n            formattedString = this.toHsvString();\n        }\n        return formattedString || this.toHexString();\n    };\n    TinyColor.prototype.toNumber = function () {\n        return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n    };\n    TinyColor.prototype.clone = function () {\n        return new TinyColor(this.toString());\n    };\n    /**\n     * Lighten the color a given amount. Providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.lighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l += amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Brighten the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.brighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var rgb = this.toRgb();\n        rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n        rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n        rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n        return new TinyColor(rgb);\n    };\n    /**\n     * Darken the color a given amount, from 0 to 100.\n     * Providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.darken = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l -= amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the color with pure white, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.tint = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('white', amount);\n    };\n    /**\n     * Mix the color with pure black, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.shade = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('black', amount);\n    };\n    /**\n     * Desaturate the color a given amount, from 0 to 100.\n     * Providing 100 will is the same as calling greyscale\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.desaturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s -= amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Saturate the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.saturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s += amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Completely desaturates a color into greyscale.\n     * Same as calling `desaturate(100)`\n     */\n    TinyColor.prototype.greyscale = function () {\n        return this.desaturate(100);\n    };\n    /**\n     * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n     * Values outside of this range will be wrapped into this range.\n     */\n    TinyColor.prototype.spin = function (amount) {\n        var hsl = this.toHsl();\n        var hue = (hsl.h + amount) % 360;\n        hsl.h = hue < 0 ? 360 + hue : hue;\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the current color a given amount with another color, from 0 to 100.\n     * 0 means no mixing (return current color).\n     */\n    TinyColor.prototype.mix = function (color, amount) {\n        if (amount === void 0) { amount = 50; }\n        var rgb1 = this.toRgb();\n        var rgb2 = new TinyColor(color).toRgb();\n        var p = amount / 100;\n        var rgba = {\n            r: (rgb2.r - rgb1.r) * p + rgb1.r,\n            g: (rgb2.g - rgb1.g) * p + rgb1.g,\n            b: (rgb2.b - rgb1.b) * p + rgb1.b,\n            a: (rgb2.a - rgb1.a) * p + rgb1.a,\n        };\n        return new TinyColor(rgba);\n    };\n    TinyColor.prototype.analogous = function (results, slices) {\n        if (results === void 0) { results = 6; }\n        if (slices === void 0) { slices = 30; }\n        var hsl = this.toHsl();\n        var part = 360 / slices;\n        var ret = [this];\n        for (hsl.h = (hsl.h - ((part * results) >> 1) + 720) % 360; --results;) {\n            hsl.h = (hsl.h + part) % 360;\n            ret.push(new TinyColor(hsl));\n        }\n        return ret;\n    };\n    /**\n     * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n     */\n    TinyColor.prototype.complement = function () {\n        var hsl = this.toHsl();\n        hsl.h = (hsl.h + 180) % 360;\n        return new TinyColor(hsl);\n    };\n    TinyColor.prototype.monochromatic = function (results) {\n        if (results === void 0) { results = 6; }\n        var hsv = this.toHsv();\n        var h = hsv.h;\n        var s = hsv.s;\n        var v = hsv.v;\n        var res = [];\n        var modification = 1 / results;\n        while (results--) {\n            res.push(new TinyColor({ h: h, s: s, v: v }));\n            v = (v + modification) % 1;\n        }\n        return res;\n    };\n    TinyColor.prototype.splitcomplement = function () {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        return [\n            this,\n            new TinyColor({ h: (h + 72) % 360, s: hsl.s, l: hsl.l }),\n            new TinyColor({ h: (h + 216) % 360, s: hsl.s, l: hsl.l }),\n        ];\n    };\n    /**\n     * Compute how the color would appear on a background\n     */\n    TinyColor.prototype.onBackground = function (background) {\n        var fg = this.toRgb();\n        var bg = new TinyColor(background).toRgb();\n        var alpha = fg.a + bg.a * (1 - fg.a);\n        return new TinyColor({\n            r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n            g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n            b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n            a: alpha,\n        });\n    };\n    /**\n     * Alias for `polyad(3)`\n     */\n    TinyColor.prototype.triad = function () {\n        return this.polyad(3);\n    };\n    /**\n     * Alias for `polyad(4)`\n     */\n    TinyColor.prototype.tetrad = function () {\n        return this.polyad(4);\n    };\n    /**\n     * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n     * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n     */\n    TinyColor.prototype.polyad = function (n) {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        var result = [this];\n        var increment = 360 / n;\n        for (var i = 1; i < n; i++) {\n            result.push(new TinyColor({ h: (h + i * increment) % 360, s: hsl.s, l: hsl.l }));\n        }\n        return result;\n    };\n    /**\n     * compare color vs current color\n     */\n    TinyColor.prototype.equals = function (color) {\n        return this.toRgbString() === new TinyColor(color).toRgbString();\n    };\n    return TinyColor;\n}());\nexport { TinyColor };\n// kept for backwards compatability with v1\nexport function tinycolor(color, opts) {\n    if (color === void 0) { color = ''; }\n    if (opts === void 0) { opts = {}; }\n    return new TinyColor(color, opts);\n}\n"], "mappings": ";AAAA,SAASA,mBAAmB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,iBAAiB;AAC9F,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,OAAO,EAAEC,UAAU,EAAEC,OAAO,QAAQ,WAAW;AACxD,IAAIC,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAE;IAC5B,IAAID,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,EAAE;IAAE;IACpC,IAAIC,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG,CAAC,CAAC;IAAE;IAClC,IAAIC,EAAE;IACN;IACA,IAAIF,KAAK,YAAYD,SAAS,EAAE;MAC5B;MACA,OAAOC,KAAK;IAChB;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3BA,KAAK,GAAGX,mBAAmB,CAACW,KAAK,CAAC;IACtC;IACA,IAAI,CAACG,aAAa,GAAGH,KAAK;IAC1B,IAAII,GAAG,GAAGT,UAAU,CAACK,KAAK,CAAC;IAC3B,IAAI,CAACG,aAAa,GAAGH,KAAK;IAC1B,IAAI,CAACK,CAAC,GAAGD,GAAG,CAACC,CAAC;IACd,IAAI,CAACC,CAAC,GAAGF,GAAG,CAACE,CAAC;IACd,IAAI,CAACC,CAAC,GAAGH,GAAG,CAACG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGJ,GAAG,CAACI,CAAC;IACd,IAAI,CAACC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,IAAI,CAACH,CAAC,CAAC,GAAG,GAAG;IAC5C,IAAI,CAACI,MAAM,GAAG,CAACV,EAAE,GAAGD,IAAI,CAACW,MAAM,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGE,GAAG,CAACQ,MAAM;IAC5E,IAAI,CAACC,YAAY,GAAGZ,IAAI,CAACY,YAAY;IACrC;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACR,CAAC,GAAG,CAAC,EAAE;MACZ,IAAI,CAACA,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,CAAC,CAAC;IAC/B;IACA,IAAI,IAAI,CAACC,CAAC,GAAG,CAAC,EAAE;MACZ,IAAI,CAACA,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAC,IAAI,CAACL,CAAC,CAAC;IAC/B;IACA,IAAI,IAAI,CAACC,CAAC,GAAG,CAAC,EAAE;MACZ,IAAI,CAACA,CAAC,GAAGG,IAAI,CAACC,KAAK,CAAC,IAAI,CAACJ,CAAC,CAAC;IAC/B;IACA,IAAI,CAACO,OAAO,GAAGV,GAAG,CAACW,EAAE;EACzB;EACAhB,SAAS,CAACiB,SAAS,CAACC,MAAM,GAAG,YAAY;IACrC,OAAO,IAAI,CAACC,aAAa,CAAC,CAAC,GAAG,GAAG;EACrC,CAAC;EACDnB,SAAS,CAACiB,SAAS,CAACG,OAAO,GAAG,YAAY;IACtC,OAAO,CAAC,IAAI,CAACF,MAAM,CAAC,CAAC;EACzB,CAAC;EACD;AACJ;AACA;EACIlB,SAAS,CAACiB,SAAS,CAACE,aAAa,GAAG,YAAY;IAC5C;IACA,IAAId,GAAG,GAAG,IAAI,CAACgB,KAAK,CAAC,CAAC;IACtB,OAAO,CAAChB,GAAG,CAACC,CAAC,GAAG,GAAG,GAAGD,GAAG,CAACE,CAAC,GAAG,GAAG,GAAGF,GAAG,CAACG,CAAC,GAAG,GAAG,IAAI,IAAI;EAC3D,CAAC;EACD;AACJ;AACA;EACIR,SAAS,CAACiB,SAAS,CAACK,YAAY,GAAG,YAAY;IAC3C;IACA,IAAIjB,GAAG,GAAG,IAAI,CAACgB,KAAK,CAAC,CAAC;IACtB,IAAIE,CAAC;IACL,IAAIC,CAAC;IACL,IAAIC,CAAC;IACL,IAAIC,KAAK,GAAGrB,GAAG,CAACC,CAAC,GAAG,GAAG;IACvB,IAAIqB,KAAK,GAAGtB,GAAG,CAACE,CAAC,GAAG,GAAG;IACvB,IAAIqB,KAAK,GAAGvB,GAAG,CAACG,CAAC,GAAG,GAAG;IACvB,IAAIkB,KAAK,IAAI,OAAO,EAAE;MAClBH,CAAC,GAAGG,KAAK,GAAG,KAAK;IACrB,CAAC,MACI;MACD;MACAH,CAAC,GAAGZ,IAAI,CAACkB,GAAG,CAAC,CAACH,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;IAC9C;IACA,IAAIC,KAAK,IAAI,OAAO,EAAE;MAClBH,CAAC,GAAGG,KAAK,GAAG,KAAK;IACrB,CAAC,MACI;MACD;MACAH,CAAC,GAAGb,IAAI,CAACkB,GAAG,CAAC,CAACF,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;IAC9C;IACA,IAAIC,KAAK,IAAI,OAAO,EAAE;MAClBH,CAAC,GAAGG,KAAK,GAAG,KAAK;IACrB,CAAC,MACI;MACD;MACAH,CAAC,GAAGd,IAAI,CAACkB,GAAG,CAAC,CAACD,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;IAC9C;IACA,OAAO,MAAM,GAAGL,CAAC,GAAG,MAAM,GAAGC,CAAC,GAAG,MAAM,GAAGC,CAAC;EAC/C,CAAC;EACD;AACJ;AACA;EACIzB,SAAS,CAACiB,SAAS,CAACa,QAAQ,GAAG,YAAY;IACvC,OAAO,IAAI,CAACrB,CAAC;EACjB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIT,SAAS,CAACiB,SAAS,CAACc,QAAQ,GAAG,UAAUC,KAAK,EAAE;IAC5C,IAAI,CAACvB,CAAC,GAAGX,UAAU,CAACkC,KAAK,CAAC;IAC1B,IAAI,CAACtB,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,IAAI,CAACH,CAAC,CAAC,GAAG,GAAG;IAC5C,OAAO,IAAI;EACf,CAAC;EACD;AACJ;AACA;EACIT,SAAS,CAACiB,SAAS,CAACgB,YAAY,GAAG,YAAY;IAC3C,IAAIC,CAAC,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC,CAACD,CAAC;IACtB,OAAOA,CAAC,KAAK,CAAC;EAClB,CAAC;EACD;AACJ;AACA;EACIlC,SAAS,CAACiB,SAAS,CAACmB,KAAK,GAAG,YAAY;IACpC,IAAIC,GAAG,GAAG3C,QAAQ,CAAC,IAAI,CAACY,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC1C,OAAO;MAAE8B,CAAC,EAAED,GAAG,CAACC,CAAC,GAAG,GAAG;MAAEJ,CAAC,EAAEG,GAAG,CAACH,CAAC;MAAEK,CAAC,EAAEF,GAAG,CAACE,CAAC;MAAE9B,CAAC,EAAE,IAAI,CAACA;IAAE,CAAC;EAC5D,CAAC;EACD;AACJ;AACA;AACA;EACIT,SAAS,CAACiB,SAAS,CAACuB,WAAW,GAAG,YAAY;IAC1C,IAAIH,GAAG,GAAG3C,QAAQ,CAAC,IAAI,CAACY,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC1C,IAAI8B,CAAC,GAAG3B,IAAI,CAACC,KAAK,CAACyB,GAAG,CAACC,CAAC,GAAG,GAAG,CAAC;IAC/B,IAAIJ,CAAC,GAAGvB,IAAI,CAACC,KAAK,CAACyB,GAAG,CAACH,CAAC,GAAG,GAAG,CAAC;IAC/B,IAAIK,CAAC,GAAG5B,IAAI,CAACC,KAAK,CAACyB,GAAG,CAACE,CAAC,GAAG,GAAG,CAAC;IAC/B,OAAO,IAAI,CAAC9B,CAAC,KAAK,CAAC,GAAG,MAAM,CAACgC,MAAM,CAACH,CAAC,EAAE,IAAI,CAAC,CAACG,MAAM,CAACP,CAAC,EAAE,KAAK,CAAC,CAACO,MAAM,CAACF,CAAC,EAAE,IAAI,CAAC,GAAG,OAAO,CAACE,MAAM,CAACH,CAAC,EAAE,IAAI,CAAC,CAACG,MAAM,CAACP,CAAC,EAAE,KAAK,CAAC,CAACO,MAAM,CAACF,CAAC,EAAE,KAAK,CAAC,CAACE,MAAM,CAAC,IAAI,CAAC/B,MAAM,EAAE,GAAG,CAAC;EACtK,CAAC;EACD;AACJ;AACA;EACIV,SAAS,CAACiB,SAAS,CAACkB,KAAK,GAAG,YAAY;IACpC,IAAIO,GAAG,GAAGjD,QAAQ,CAAC,IAAI,CAACa,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC1C,OAAO;MAAE8B,CAAC,EAAEI,GAAG,CAACJ,CAAC,GAAG,GAAG;MAAEJ,CAAC,EAAEQ,GAAG,CAACR,CAAC;MAAES,CAAC,EAAED,GAAG,CAACC,CAAC;MAAElC,CAAC,EAAE,IAAI,CAACA;IAAE,CAAC;EAC5D,CAAC;EACD;AACJ;AACA;AACA;EACIT,SAAS,CAACiB,SAAS,CAAC2B,WAAW,GAAG,YAAY;IAC1C,IAAIF,GAAG,GAAGjD,QAAQ,CAAC,IAAI,CAACa,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC1C,IAAI8B,CAAC,GAAG3B,IAAI,CAACC,KAAK,CAAC8B,GAAG,CAACJ,CAAC,GAAG,GAAG,CAAC;IAC/B,IAAIJ,CAAC,GAAGvB,IAAI,CAACC,KAAK,CAAC8B,GAAG,CAACR,CAAC,GAAG,GAAG,CAAC;IAC/B,IAAIS,CAAC,GAAGhC,IAAI,CAACC,KAAK,CAAC8B,GAAG,CAACC,CAAC,GAAG,GAAG,CAAC;IAC/B,OAAO,IAAI,CAAClC,CAAC,KAAK,CAAC,GAAG,MAAM,CAACgC,MAAM,CAACH,CAAC,EAAE,IAAI,CAAC,CAACG,MAAM,CAACP,CAAC,EAAE,KAAK,CAAC,CAACO,MAAM,CAACE,CAAC,EAAE,IAAI,CAAC,GAAG,OAAO,CAACF,MAAM,CAACH,CAAC,EAAE,IAAI,CAAC,CAACG,MAAM,CAACP,CAAC,EAAE,KAAK,CAAC,CAACO,MAAM,CAACE,CAAC,EAAE,KAAK,CAAC,CAACF,MAAM,CAAC,IAAI,CAAC/B,MAAM,EAAE,GAAG,CAAC;EACtK,CAAC;EACD;AACJ;AACA;AACA;EACIV,SAAS,CAACiB,SAAS,CAAC4B,KAAK,GAAG,UAAUC,UAAU,EAAE;IAC9C,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MAAEA,UAAU,GAAG,KAAK;IAAE;IACjD,OAAOtD,QAAQ,CAAC,IAAI,CAACc,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAEsC,UAAU,CAAC;EACvD,CAAC;EACD;AACJ;AACA;AACA;EACI9C,SAAS,CAACiB,SAAS,CAAC8B,WAAW,GAAG,UAAUD,UAAU,EAAE;IACpD,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MAAEA,UAAU,GAAG,KAAK;IAAE;IACjD,OAAO,GAAG,GAAG,IAAI,CAACD,KAAK,CAACC,UAAU,CAAC;EACvC,CAAC;EACD;AACJ;AACA;AACA;EACI9C,SAAS,CAACiB,SAAS,CAAC+B,MAAM,GAAG,UAAUC,UAAU,EAAE;IAC/C,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MAAEA,UAAU,GAAG,KAAK;IAAE;IACjD,OAAO1D,SAAS,CAAC,IAAI,CAACe,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAEwC,UAAU,CAAC;EAChE,CAAC;EACD;AACJ;AACA;AACA;EACIjD,SAAS,CAACiB,SAAS,CAACiC,YAAY,GAAG,UAAUD,UAAU,EAAE;IACrD,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MAAEA,UAAU,GAAG,KAAK;IAAE;IACjD,OAAO,GAAG,GAAG,IAAI,CAACD,MAAM,CAACC,UAAU,CAAC;EACxC,CAAC;EACD;AACJ;AACA;AACA;EACIjD,SAAS,CAACiB,SAAS,CAACkC,gBAAgB,GAAG,UAAUC,cAAc,EAAE;IAC7D,IAAIA,cAAc,KAAK,KAAK,CAAC,EAAE;MAAEA,cAAc,GAAG,KAAK;IAAE;IACzD,OAAO,IAAI,CAAC3C,CAAC,KAAK,CAAC,GAAG,IAAI,CAACsC,WAAW,CAACK,cAAc,CAAC,GAAG,IAAI,CAACF,YAAY,CAACE,cAAc,CAAC;EAC9F,CAAC;EACD;AACJ;AACA;EACIpD,SAAS,CAACiB,SAAS,CAACI,KAAK,GAAG,YAAY;IACpC,OAAO;MACHf,CAAC,EAAEK,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,CAAC,CAAC;MACrBC,CAAC,EAAEI,IAAI,CAACC,KAAK,CAAC,IAAI,CAACL,CAAC,CAAC;MACrBC,CAAC,EAAEG,IAAI,CAACC,KAAK,CAAC,IAAI,CAACJ,CAAC,CAAC;MACrBC,CAAC,EAAE,IAAI,CAACA;IACZ,CAAC;EACL,CAAC;EACD;AACJ;AACA;AACA;EACIT,SAAS,CAACiB,SAAS,CAACoC,WAAW,GAAG,YAAY;IAC1C,IAAI/C,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,CAAC,CAAC;IAC1B,IAAIC,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAC,IAAI,CAACL,CAAC,CAAC;IAC1B,IAAIC,CAAC,GAAGG,IAAI,CAACC,KAAK,CAAC,IAAI,CAACJ,CAAC,CAAC;IAC1B,OAAO,IAAI,CAACC,CAAC,KAAK,CAAC,GAAG,MAAM,CAACgC,MAAM,CAACnC,CAAC,EAAE,IAAI,CAAC,CAACmC,MAAM,CAAClC,CAAC,EAAE,IAAI,CAAC,CAACkC,MAAM,CAACjC,CAAC,EAAE,GAAG,CAAC,GAAG,OAAO,CAACiC,MAAM,CAACnC,CAAC,EAAE,IAAI,CAAC,CAACmC,MAAM,CAAClC,CAAC,EAAE,IAAI,CAAC,CAACkC,MAAM,CAACjC,CAAC,EAAE,IAAI,CAAC,CAACiC,MAAM,CAAC,IAAI,CAAC/B,MAAM,EAAE,GAAG,CAAC;EAClK,CAAC;EACD;AACJ;AACA;EACIV,SAAS,CAACiB,SAAS,CAACqC,eAAe,GAAG,YAAY;IAC9C,IAAIC,GAAG,GAAG,SAAAA,CAAUC,CAAC,EAAE;MAAE,OAAO,EAAE,CAACf,MAAM,CAAC9B,IAAI,CAACC,KAAK,CAACf,OAAO,CAAC2D,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC;IAAE,CAAC;IACpF,OAAO;MACHlD,CAAC,EAAEiD,GAAG,CAAC,IAAI,CAACjD,CAAC,CAAC;MACdC,CAAC,EAAEgD,GAAG,CAAC,IAAI,CAAChD,CAAC,CAAC;MACdC,CAAC,EAAE+C,GAAG,CAAC,IAAI,CAAC/C,CAAC,CAAC;MACdC,CAAC,EAAE,IAAI,CAACA;IACZ,CAAC;EACL,CAAC;EACD;AACJ;AACA;EACIT,SAAS,CAACiB,SAAS,CAACwC,qBAAqB,GAAG,YAAY;IACpD,IAAIC,GAAG,GAAG,SAAAA,CAAUF,CAAC,EAAE;MAAE,OAAO7C,IAAI,CAACC,KAAK,CAACf,OAAO,CAAC2D,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;IAAE,CAAC;IACpE,OAAO,IAAI,CAAC/C,CAAC,KAAK,CAAC,GACb,MAAM,CAACgC,MAAM,CAACiB,GAAG,CAAC,IAAI,CAACpD,CAAC,CAAC,EAAE,KAAK,CAAC,CAACmC,MAAM,CAACiB,GAAG,CAAC,IAAI,CAACnD,CAAC,CAAC,EAAE,KAAK,CAAC,CAACkC,MAAM,CAACiB,GAAG,CAAC,IAAI,CAAClD,CAAC,CAAC,EAAE,IAAI,CAAC,GACtF,OAAO,CAACiC,MAAM,CAACiB,GAAG,CAAC,IAAI,CAACpD,CAAC,CAAC,EAAE,KAAK,CAAC,CAACmC,MAAM,CAACiB,GAAG,CAAC,IAAI,CAACnD,CAAC,CAAC,EAAE,KAAK,CAAC,CAACkC,MAAM,CAACiB,GAAG,CAAC,IAAI,CAAClD,CAAC,CAAC,EAAE,KAAK,CAAC,CAACiC,MAAM,CAAC,IAAI,CAAC/B,MAAM,EAAE,GAAG,CAAC;EAC3H,CAAC;EACD;AACJ;AACA;EACIV,SAAS,CAACiB,SAAS,CAAC0C,MAAM,GAAG,YAAY;IACrC,IAAI,IAAI,CAAClD,CAAC,KAAK,CAAC,EAAE;MACd,OAAO,aAAa;IACxB;IACA,IAAI,IAAI,CAACA,CAAC,GAAG,CAAC,EAAE;MACZ,OAAO,KAAK;IAChB;IACA,IAAImD,GAAG,GAAG,GAAG,GAAGpE,QAAQ,CAAC,IAAI,CAACc,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,KAAK,CAAC;IACvD,KAAK,IAAIqD,EAAE,GAAG,CAAC,EAAE1D,EAAE,GAAG2D,MAAM,CAACC,OAAO,CAACpE,KAAK,CAAC,EAAEkE,EAAE,GAAG1D,EAAE,CAAC6D,MAAM,EAAEH,EAAE,EAAE,EAAE;MAC/D,IAAII,EAAE,GAAG9D,EAAE,CAAC0D,EAAE,CAAC;QAAEK,GAAG,GAAGD,EAAE,CAAC,CAAC,CAAC;QAAEE,KAAK,GAAGF,EAAE,CAAC,CAAC,CAAC;MAC3C,IAAIL,GAAG,KAAKO,KAAK,EAAE;QACf,OAAOD,GAAG;MACd;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;EACDlE,SAAS,CAACiB,SAAS,CAACmD,QAAQ,GAAG,UAAUvD,MAAM,EAAE;IAC7C,IAAIwD,SAAS,GAAGC,OAAO,CAACzD,MAAM,CAAC;IAC/BA,MAAM,GAAGA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,IAAI,CAACA,MAAM;IACpE,IAAI0D,eAAe,GAAG,KAAK;IAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC/D,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,CAAC,IAAI,CAAC;IACxC,IAAIgE,gBAAgB,GAAG,CAACJ,SAAS,IAAIG,QAAQ,KAAK3D,MAAM,CAAC6D,UAAU,CAAC,KAAK,CAAC,IAAI7D,MAAM,KAAK,MAAM,CAAC;IAChG,IAAI4D,gBAAgB,EAAE;MAClB;MACA;MACA,IAAI5D,MAAM,KAAK,MAAM,IAAI,IAAI,CAACJ,CAAC,KAAK,CAAC,EAAE;QACnC,OAAO,IAAI,CAACkD,MAAM,CAAC,CAAC;MACxB;MACA,OAAO,IAAI,CAACN,WAAW,CAAC,CAAC;IAC7B;IACA,IAAIxC,MAAM,KAAK,KAAK,EAAE;MAClB0D,eAAe,GAAG,IAAI,CAAClB,WAAW,CAAC,CAAC;IACxC;IACA,IAAIxC,MAAM,KAAK,MAAM,EAAE;MACnB0D,eAAe,GAAG,IAAI,CAACd,qBAAqB,CAAC,CAAC;IAClD;IACA,IAAI5C,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,MAAM,EAAE;MACvC0D,eAAe,GAAG,IAAI,CAACxB,WAAW,CAAC,CAAC;IACxC;IACA,IAAIlC,MAAM,KAAK,MAAM,EAAE;MACnB0D,eAAe,GAAG,IAAI,CAACxB,WAAW,CAAC,IAAI,CAAC;IAC5C;IACA,IAAIlC,MAAM,KAAK,MAAM,EAAE;MACnB0D,eAAe,GAAG,IAAI,CAACrB,YAAY,CAAC,IAAI,CAAC;IAC7C;IACA,IAAIrC,MAAM,KAAK,MAAM,EAAE;MACnB0D,eAAe,GAAG,IAAI,CAACrB,YAAY,CAAC,CAAC;IACzC;IACA,IAAIrC,MAAM,KAAK,MAAM,EAAE;MACnB0D,eAAe,GAAG,IAAI,CAACZ,MAAM,CAAC,CAAC;IACnC;IACA,IAAI9C,MAAM,KAAK,KAAK,EAAE;MAClB0D,eAAe,GAAG,IAAI,CAAC3B,WAAW,CAAC,CAAC;IACxC;IACA,IAAI/B,MAAM,KAAK,KAAK,EAAE;MAClB0D,eAAe,GAAG,IAAI,CAAC/B,WAAW,CAAC,CAAC;IACxC;IACA,OAAO+B,eAAe,IAAI,IAAI,CAACxB,WAAW,CAAC,CAAC;EAChD,CAAC;EACD/C,SAAS,CAACiB,SAAS,CAAC0D,QAAQ,GAAG,YAAY;IACvC,OAAO,CAAChE,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,CAAC,CAAC,IAAI,EAAE,KAAKK,IAAI,CAACC,KAAK,CAAC,IAAI,CAACL,CAAC,CAAC,IAAI,CAAC,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAC,IAAI,CAACJ,CAAC,CAAC;EACtF,CAAC;EACDR,SAAS,CAACiB,SAAS,CAAC2D,KAAK,GAAG,YAAY;IACpC,OAAO,IAAI5E,SAAS,CAAC,IAAI,CAACoE,QAAQ,CAAC,CAAC,CAAC;EACzC,CAAC;EACD;AACJ;AACA;AACA;EACIpE,SAAS,CAACiB,SAAS,CAAC4D,OAAO,GAAG,UAAUC,MAAM,EAAE;IAC5C,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,EAAE;IAAE;IACtC,IAAIpC,GAAG,GAAG,IAAI,CAACP,KAAK,CAAC,CAAC;IACtBO,GAAG,CAACC,CAAC,IAAImC,MAAM,GAAG,GAAG;IACrBpC,GAAG,CAACC,CAAC,GAAG5C,OAAO,CAAC2C,GAAG,CAACC,CAAC,CAAC;IACtB,OAAO,IAAI3C,SAAS,CAAC0C,GAAG,CAAC;EAC7B,CAAC;EACD;AACJ;AACA;AACA;EACI1C,SAAS,CAACiB,SAAS,CAAC8D,QAAQ,GAAG,UAAUD,MAAM,EAAE;IAC7C,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,EAAE;IAAE;IACtC,IAAIzE,GAAG,GAAG,IAAI,CAACgB,KAAK,CAAC,CAAC;IACtBhB,GAAG,CAACC,CAAC,GAAGK,IAAI,CAACqE,GAAG,CAAC,CAAC,EAAErE,IAAI,CAACsE,GAAG,CAAC,GAAG,EAAE5E,GAAG,CAACC,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,EAAEkE,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7EzE,GAAG,CAACE,CAAC,GAAGI,IAAI,CAACqE,GAAG,CAAC,CAAC,EAAErE,IAAI,CAACsE,GAAG,CAAC,GAAG,EAAE5E,GAAG,CAACE,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,EAAEkE,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7EzE,GAAG,CAACG,CAAC,GAAGG,IAAI,CAACqE,GAAG,CAAC,CAAC,EAAErE,IAAI,CAACsE,GAAG,CAAC,GAAG,EAAE5E,GAAG,CAACG,CAAC,GAAGG,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,EAAEkE,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7E,OAAO,IAAI9E,SAAS,CAACK,GAAG,CAAC;EAC7B,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIL,SAAS,CAACiB,SAAS,CAACiE,MAAM,GAAG,UAAUJ,MAAM,EAAE;IAC3C,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,EAAE;IAAE;IACtC,IAAIpC,GAAG,GAAG,IAAI,CAACP,KAAK,CAAC,CAAC;IACtBO,GAAG,CAACC,CAAC,IAAImC,MAAM,GAAG,GAAG;IACrBpC,GAAG,CAACC,CAAC,GAAG5C,OAAO,CAAC2C,GAAG,CAACC,CAAC,CAAC;IACtB,OAAO,IAAI3C,SAAS,CAAC0C,GAAG,CAAC;EAC7B,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI1C,SAAS,CAACiB,SAAS,CAACkE,IAAI,GAAG,UAAUL,MAAM,EAAE;IACzC,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,EAAE;IAAE;IACtC,OAAO,IAAI,CAACM,GAAG,CAAC,OAAO,EAAEN,MAAM,CAAC;EACpC,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI9E,SAAS,CAACiB,SAAS,CAACoE,KAAK,GAAG,UAAUP,MAAM,EAAE;IAC1C,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,EAAE;IAAE;IACtC,OAAO,IAAI,CAACM,GAAG,CAAC,OAAO,EAAEN,MAAM,CAAC;EACpC,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI9E,SAAS,CAACiB,SAAS,CAACqE,UAAU,GAAG,UAAUR,MAAM,EAAE;IAC/C,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,EAAE;IAAE;IACtC,IAAIpC,GAAG,GAAG,IAAI,CAACP,KAAK,CAAC,CAAC;IACtBO,GAAG,CAACR,CAAC,IAAI4C,MAAM,GAAG,GAAG;IACrBpC,GAAG,CAACR,CAAC,GAAGnC,OAAO,CAAC2C,GAAG,CAACR,CAAC,CAAC;IACtB,OAAO,IAAIlC,SAAS,CAAC0C,GAAG,CAAC;EAC7B,CAAC;EACD;AACJ;AACA;AACA;EACI1C,SAAS,CAACiB,SAAS,CAACsE,QAAQ,GAAG,UAAUT,MAAM,EAAE;IAC7C,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,EAAE;IAAE;IACtC,IAAIpC,GAAG,GAAG,IAAI,CAACP,KAAK,CAAC,CAAC;IACtBO,GAAG,CAACR,CAAC,IAAI4C,MAAM,GAAG,GAAG;IACrBpC,GAAG,CAACR,CAAC,GAAGnC,OAAO,CAAC2C,GAAG,CAACR,CAAC,CAAC;IACtB,OAAO,IAAIlC,SAAS,CAAC0C,GAAG,CAAC;EAC7B,CAAC;EACD;AACJ;AACA;AACA;EACI1C,SAAS,CAACiB,SAAS,CAACuE,SAAS,GAAG,YAAY;IACxC,OAAO,IAAI,CAACF,UAAU,CAAC,GAAG,CAAC;EAC/B,CAAC;EACD;AACJ;AACA;AACA;EACItF,SAAS,CAACiB,SAAS,CAACwE,IAAI,GAAG,UAAUX,MAAM,EAAE;IACzC,IAAIpC,GAAG,GAAG,IAAI,CAACP,KAAK,CAAC,CAAC;IACtB,IAAIuD,GAAG,GAAG,CAAChD,GAAG,CAACJ,CAAC,GAAGwC,MAAM,IAAI,GAAG;IAChCpC,GAAG,CAACJ,CAAC,GAAGoD,GAAG,GAAG,CAAC,GAAG,GAAG,GAAGA,GAAG,GAAGA,GAAG;IACjC,OAAO,IAAI1F,SAAS,CAAC0C,GAAG,CAAC;EAC7B,CAAC;EACD;AACJ;AACA;AACA;EACI1C,SAAS,CAACiB,SAAS,CAACmE,GAAG,GAAG,UAAUnF,KAAK,EAAE6E,MAAM,EAAE;IAC/C,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,EAAE;IAAE;IACtC,IAAIa,IAAI,GAAG,IAAI,CAACtE,KAAK,CAAC,CAAC;IACvB,IAAIuE,IAAI,GAAG,IAAI5F,SAAS,CAACC,KAAK,CAAC,CAACoB,KAAK,CAAC,CAAC;IACvC,IAAIwE,CAAC,GAAGf,MAAM,GAAG,GAAG;IACpB,IAAIgB,IAAI,GAAG;MACPxF,CAAC,EAAE,CAACsF,IAAI,CAACtF,CAAC,GAAGqF,IAAI,CAACrF,CAAC,IAAIuF,CAAC,GAAGF,IAAI,CAACrF,CAAC;MACjCC,CAAC,EAAE,CAACqF,IAAI,CAACrF,CAAC,GAAGoF,IAAI,CAACpF,CAAC,IAAIsF,CAAC,GAAGF,IAAI,CAACpF,CAAC;MACjCC,CAAC,EAAE,CAACoF,IAAI,CAACpF,CAAC,GAAGmF,IAAI,CAACnF,CAAC,IAAIqF,CAAC,GAAGF,IAAI,CAACnF,CAAC;MACjCC,CAAC,EAAE,CAACmF,IAAI,CAACnF,CAAC,GAAGkF,IAAI,CAAClF,CAAC,IAAIoF,CAAC,GAAGF,IAAI,CAAClF;IACpC,CAAC;IACD,OAAO,IAAIT,SAAS,CAAC8F,IAAI,CAAC;EAC9B,CAAC;EACD9F,SAAS,CAACiB,SAAS,CAAC8E,SAAS,GAAG,UAAUC,OAAO,EAAEC,MAAM,EAAE;IACvD,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,CAAC;IAAE;IACvC,IAAIC,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,EAAE;IAAE;IACtC,IAAIvD,GAAG,GAAG,IAAI,CAACP,KAAK,CAAC,CAAC;IACtB,IAAI+D,IAAI,GAAG,GAAG,GAAGD,MAAM;IACvB,IAAIE,GAAG,GAAG,CAAC,IAAI,CAAC;IAChB,KAAKzD,GAAG,CAACJ,CAAC,GAAG,CAACI,GAAG,CAACJ,CAAC,IAAK4D,IAAI,GAAGF,OAAO,IAAK,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,EAAEA,OAAO,GAAG;MACpEtD,GAAG,CAACJ,CAAC,GAAG,CAACI,GAAG,CAACJ,CAAC,GAAG4D,IAAI,IAAI,GAAG;MAC5BC,GAAG,CAACC,IAAI,CAAC,IAAIpG,SAAS,CAAC0C,GAAG,CAAC,CAAC;IAChC;IACA,OAAOyD,GAAG;EACd,CAAC;EACD;AACJ;AACA;EACInG,SAAS,CAACiB,SAAS,CAACoF,UAAU,GAAG,YAAY;IACzC,IAAI3D,GAAG,GAAG,IAAI,CAACP,KAAK,CAAC,CAAC;IACtBO,GAAG,CAACJ,CAAC,GAAG,CAACI,GAAG,CAACJ,CAAC,GAAG,GAAG,IAAI,GAAG;IAC3B,OAAO,IAAItC,SAAS,CAAC0C,GAAG,CAAC;EAC7B,CAAC;EACD1C,SAAS,CAACiB,SAAS,CAACqF,aAAa,GAAG,UAAUN,OAAO,EAAE;IACnD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,CAAC;IAAE;IACvC,IAAI3D,GAAG,GAAG,IAAI,CAACD,KAAK,CAAC,CAAC;IACtB,IAAIE,CAAC,GAAGD,GAAG,CAACC,CAAC;IACb,IAAIJ,CAAC,GAAGG,GAAG,CAACH,CAAC;IACb,IAAIK,CAAC,GAAGF,GAAG,CAACE,CAAC;IACb,IAAIgE,GAAG,GAAG,EAAE;IACZ,IAAIC,YAAY,GAAG,CAAC,GAAGR,OAAO;IAC9B,OAAOA,OAAO,EAAE,EAAE;MACdO,GAAG,CAACH,IAAI,CAAC,IAAIpG,SAAS,CAAC;QAAEsC,CAAC,EAAEA,CAAC;QAAEJ,CAAC,EAAEA,CAAC;QAAEK,CAAC,EAAEA;MAAE,CAAC,CAAC,CAAC;MAC7CA,CAAC,GAAG,CAACA,CAAC,GAAGiE,YAAY,IAAI,CAAC;IAC9B;IACA,OAAOD,GAAG;EACd,CAAC;EACDvG,SAAS,CAACiB,SAAS,CAACwF,eAAe,GAAG,YAAY;IAC9C,IAAI/D,GAAG,GAAG,IAAI,CAACP,KAAK,CAAC,CAAC;IACtB,IAAIG,CAAC,GAAGI,GAAG,CAACJ,CAAC;IACb,OAAO,CACH,IAAI,EACJ,IAAItC,SAAS,CAAC;MAAEsC,CAAC,EAAE,CAACA,CAAC,GAAG,EAAE,IAAI,GAAG;MAAEJ,CAAC,EAAEQ,GAAG,CAACR,CAAC;MAAES,CAAC,EAAED,GAAG,CAACC;IAAE,CAAC,CAAC,EACxD,IAAI3C,SAAS,CAAC;MAAEsC,CAAC,EAAE,CAACA,CAAC,GAAG,GAAG,IAAI,GAAG;MAAEJ,CAAC,EAAEQ,GAAG,CAACR,CAAC;MAAES,CAAC,EAAED,GAAG,CAACC;IAAE,CAAC,CAAC,CAC5D;EACL,CAAC;EACD;AACJ;AACA;EACI3C,SAAS,CAACiB,SAAS,CAACyF,YAAY,GAAG,UAAUC,UAAU,EAAE;IACrD,IAAIC,EAAE,GAAG,IAAI,CAACvF,KAAK,CAAC,CAAC;IACrB,IAAIwF,EAAE,GAAG,IAAI7G,SAAS,CAAC2G,UAAU,CAAC,CAACtF,KAAK,CAAC,CAAC;IAC1C,IAAIW,KAAK,GAAG4E,EAAE,CAACnG,CAAC,GAAGoG,EAAE,CAACpG,CAAC,IAAI,CAAC,GAAGmG,EAAE,CAACnG,CAAC,CAAC;IACpC,OAAO,IAAIT,SAAS,CAAC;MACjBM,CAAC,EAAE,CAACsG,EAAE,CAACtG,CAAC,GAAGsG,EAAE,CAACnG,CAAC,GAAGoG,EAAE,CAACvG,CAAC,GAAGuG,EAAE,CAACpG,CAAC,IAAI,CAAC,GAAGmG,EAAE,CAACnG,CAAC,CAAC,IAAIuB,KAAK;MACnDzB,CAAC,EAAE,CAACqG,EAAE,CAACrG,CAAC,GAAGqG,EAAE,CAACnG,CAAC,GAAGoG,EAAE,CAACtG,CAAC,GAAGsG,EAAE,CAACpG,CAAC,IAAI,CAAC,GAAGmG,EAAE,CAACnG,CAAC,CAAC,IAAIuB,KAAK;MACnDxB,CAAC,EAAE,CAACoG,EAAE,CAACpG,CAAC,GAAGoG,EAAE,CAACnG,CAAC,GAAGoG,EAAE,CAACrG,CAAC,GAAGqG,EAAE,CAACpG,CAAC,IAAI,CAAC,GAAGmG,EAAE,CAACnG,CAAC,CAAC,IAAIuB,KAAK;MACnDvB,CAAC,EAAEuB;IACP,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;EACIhC,SAAS,CAACiB,SAAS,CAAC6F,KAAK,GAAG,YAAY;IACpC,OAAO,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;EACzB,CAAC;EACD;AACJ;AACA;EACI/G,SAAS,CAACiB,SAAS,CAAC+F,MAAM,GAAG,YAAY;IACrC,OAAO,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;EACzB,CAAC;EACD;AACJ;AACA;AACA;EACI/G,SAAS,CAACiB,SAAS,CAAC8F,MAAM,GAAG,UAAUE,CAAC,EAAE;IACtC,IAAIvE,GAAG,GAAG,IAAI,CAACP,KAAK,CAAC,CAAC;IACtB,IAAIG,CAAC,GAAGI,GAAG,CAACJ,CAAC;IACb,IAAI4E,MAAM,GAAG,CAAC,IAAI,CAAC;IACnB,IAAIC,SAAS,GAAG,GAAG,GAAGF,CAAC;IACvB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MACxBF,MAAM,CAACd,IAAI,CAAC,IAAIpG,SAAS,CAAC;QAAEsC,CAAC,EAAE,CAACA,CAAC,GAAG8E,CAAC,GAAGD,SAAS,IAAI,GAAG;QAAEjF,CAAC,EAAEQ,GAAG,CAACR,CAAC;QAAES,CAAC,EAAED,GAAG,CAACC;MAAE,CAAC,CAAC,CAAC;IACpF;IACA,OAAOuE,MAAM;EACjB,CAAC;EACD;AACJ;AACA;EACIlH,SAAS,CAACiB,SAAS,CAACoG,MAAM,GAAG,UAAUpH,KAAK,EAAE;IAC1C,OAAO,IAAI,CAACoD,WAAW,CAAC,CAAC,KAAK,IAAIrD,SAAS,CAACC,KAAK,CAAC,CAACoD,WAAW,CAAC,CAAC;EACpE,CAAC;EACD,OAAOrD,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,SAASA,SAAS;AAClB;AACA,OAAO,SAASsH,SAASA,CAACrH,KAAK,EAAEC,IAAI,EAAE;EACnC,IAAID,KAAK,KAAK,KAAK,CAAC,EAAE;IAAEA,KAAK,GAAG,EAAE;EAAE;EACpC,IAAIC,IAAI,KAAK,KAAK,CAAC,EAAE;IAAEA,IAAI,GAAG,CAAC,CAAC;EAAE;EAClC,OAAO,IAAIF,SAAS,CAACC,KAAK,EAAEC,IAAI,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}