{"ast": null, "code": "import { useTooltipContentProps } from '../../tooltip/src/content.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { NOOP, isString, isObject } from '@vue/shared';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nconst autocompleteProps = buildProps({\n  valueKey: {\n    type: String,\n    default: \"value\"\n  },\n  modelValue: {\n    type: [String, Number],\n    default: \"\"\n  },\n  debounce: {\n    type: Number,\n    default: 300\n  },\n  placement: {\n    type: definePropType(String),\n    values: [\"top\", \"top-start\", \"top-end\", \"bottom\", \"bottom-start\", \"bottom-end\"],\n    default: \"bottom-start\"\n  },\n  fetchSuggestions: {\n    type: definePropType([Function, Array]),\n    default: NOOP\n  },\n  popperClass: {\n    type: String,\n    default: \"\"\n  },\n  triggerOnFocus: {\n    type: Boolean,\n    default: true\n  },\n  selectWhenUnmatched: {\n    type: Boolean,\n    default: false\n  },\n  hideLoading: {\n    type: Boolean,\n    default: false\n  },\n  teleported: useTooltipContentProps.teleported,\n  appendTo: useTooltipContentProps.appendTo,\n  highlightFirstItem: {\n    type: Boolean,\n    default: false\n  },\n  fitInputWidth: {\n    type: Boolean,\n    default: false\n  },\n  clearable: {\n    type: Boolean,\n    default: false\n  },\n  disabled: {\n    type: Boolean,\n    default: false\n  },\n  name: String,\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst autocompleteEmits = {\n  [UPDATE_MODEL_EVENT]: value => isString(value),\n  [INPUT_EVENT]: value => isString(value),\n  [CHANGE_EVENT]: value => isString(value),\n  focus: evt => evt instanceof FocusEvent,\n  blur: evt => evt instanceof FocusEvent,\n  clear: () => true,\n  select: item => isObject(item)\n};\nexport { autocompleteEmits, autocompleteProps };", "map": {"version": 3, "names": ["autocompleteProps", "buildProps", "valueKey", "type", "String", "default", "modelValue", "Number", "debounce", "placement", "definePropType", "values", "fetchSuggestions", "Function", "Array", "NOOP", "popperClass", "triggerOnFocus", "Boolean", "selectWhenUnmatched", "hideLoading", "teleported", "useTooltipContentProps", "appendTo", "highlightFirstItem", "fitInputWidth", "clearable", "disabled", "name", "useAriaProps", "autocompleteEmits", "UPDATE_MODEL_EVENT", "value", "isString", "INPUT_EVENT", "CHANGE_EVENT", "focus", "evt", "FocusEvent", "blur", "clear", "select", "item", "isObject"], "sources": ["../../../../../../packages/components/autocomplete/src/autocomplete.ts"], "sourcesContent": ["import {\n  NOOP,\n  buildProps,\n  definePropType,\n  isObject,\n  isString,\n} from '@element-plus/utils'\nimport { useTooltipContentProps } from '@element-plus/components/tooltip'\nimport { useAriaProps } from '@element-plus/hooks'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Autocomplete from './autocomplete.vue'\nimport type { Placement } from '@element-plus/components/popper'\nimport type { Awaitable } from '@element-plus/utils'\n\nexport type AutocompleteData = Record<string, any>[]\nexport type AutocompleteFetchSuggestionsCallback = (\n  data: AutocompleteData\n) => void\nexport type AutocompleteFetchSuggestions =\n  | ((\n      queryString: string,\n      cb: AutocompleteFetchSuggestionsCallback\n    ) => Awaitable<AutocompleteData> | void)\n  | AutocompleteData\n\nexport const autocompleteProps = buildProps({\n  /**\n   * @description key name of the input suggestion object for display\n   */\n  valueKey: {\n    type: String,\n    default: 'value',\n  },\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description debounce delay when typing, in milliseconds\n   */\n  debounce: {\n    type: Number,\n    default: 300,\n  },\n  /**\n   * @description placement of the popup menu\n   */\n  placement: {\n    type: definePropType<Placement>(String),\n    values: [\n      'top',\n      'top-start',\n      'top-end',\n      'bottom',\n      'bottom-start',\n      'bottom-end',\n    ],\n    default: 'bottom-start',\n  },\n  /**\n   * @description a method to fetch input suggestions. When suggestions are ready, invoke `callback(data:[])` to return them to Autocomplete\n   */\n  fetchSuggestions: {\n    type: definePropType<AutocompleteFetchSuggestions>([Function, Array]),\n    default: NOOP,\n  },\n  /**\n   * @description custom class name for autocomplete's dropdown\n   */\n  popperClass: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description whether show suggestions when input focus\n   */\n  triggerOnFocus: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether to emit a `select` event on enter when there is no autocomplete match\n   */\n  selectWhenUnmatched: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description whether to hide the loading icon in remote search\n   */\n  hideLoading: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description whether select dropdown is teleported to the body\n   */\n  teleported: useTooltipContentProps.teleported,\n  /**\n   * @description which select dropdown appends to\n   */\n  appendTo: useTooltipContentProps.appendTo,\n  /**\n   * @description whether to highlight first item in remote search suggestions by default\n   */\n  highlightFirstItem: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description whether the width of the dropdown is the same as the input\n   */\n  fitInputWidth: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description whether to show clear button\n   */\n  clearable: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description whether to disable\n   */\n  disabled: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description same as `name` in native input\n   */\n  name: String,\n  ...useAriaProps(['ariaLabel']),\n} as const)\nexport type AutocompleteProps = ExtractPropTypes<typeof autocompleteProps>\n\nexport const autocompleteEmits = {\n  [UPDATE_MODEL_EVENT]: (value: string) => isString(value),\n  [INPUT_EVENT]: (value: string) => isString(value),\n  [CHANGE_EVENT]: (value: string) => isString(value),\n  focus: (evt: FocusEvent) => evt instanceof FocusEvent,\n  blur: (evt: FocusEvent) => evt instanceof FocusEvent,\n  clear: () => true,\n  select: (item: Record<string, any>) => isObject(item),\n}\nexport type AutocompleteEmits = typeof autocompleteEmits\n\nexport type AutocompleteInstance = InstanceType<typeof Autocomplete> & unknown\n"], "mappings": ";;;;;AAcY,MAACA,iBAAiB,GAAGC,UAAU,CAAC;EAC1CC,QAAQ,EAAE;IACRC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,UAAU,EAAE;IACVH,IAAI,EAAE,CAACC,MAAM,EAAEG,MAAM,CAAC;IACtBF,OAAO,EAAE;EACb,CAAG;EACDG,QAAQ,EAAE;IACRL,IAAI,EAAEI,MAAM;IACZF,OAAO,EAAE;EACb,CAAG;EACDI,SAAS,EAAE;IACTN,IAAI,EAAEO,cAAc,CAACN,MAAM,CAAC;IAC5BO,MAAM,EAAE,CACN,KAAK,EACL,WAAW,EACX,SAAS,EACT,QAAQ,EACR,cAAc,EACd,YAAY,CACb;IACDN,OAAO,EAAE;EACb,CAAG;EACDO,gBAAgB,EAAE;IAChBT,IAAI,EAAEO,cAAc,CAAC,CAACG,QAAQ,EAAEC,KAAK,CAAC,CAAC;IACvCT,OAAO,EAAEU;EACb,CAAG;EACDC,WAAW,EAAE;IACXb,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDY,cAAc,EAAE;IACdd,IAAI,EAAEe,OAAO;IACbb,OAAO,EAAE;EACb,CAAG;EACDc,mBAAmB,EAAE;IACnBhB,IAAI,EAAEe,OAAO;IACbb,OAAO,EAAE;EACb,CAAG;EACDe,WAAW,EAAE;IACXjB,IAAI,EAAEe,OAAO;IACbb,OAAO,EAAE;EACb,CAAG;EACDgB,UAAU,EAAEC,sBAAsB,CAACD,UAAU;EAC7CE,QAAQ,EAAED,sBAAsB,CAACC,QAAQ;EACzCC,kBAAkB,EAAE;IAClBrB,IAAI,EAAEe,OAAO;IACbb,OAAO,EAAE;EACb,CAAG;EACDoB,aAAa,EAAE;IACbtB,IAAI,EAAEe,OAAO;IACbb,OAAO,EAAE;EACb,CAAG;EACDqB,SAAS,EAAE;IACTvB,IAAI,EAAEe,OAAO;IACbb,OAAO,EAAE;EACb,CAAG;EACDsB,QAAQ,EAAE;IACRxB,IAAI,EAAEe,OAAO;IACbb,OAAO,EAAE;EACb,CAAG;EACDuB,IAAI,EAAExB,MAAM;EACZ,GAAGyB,YAAY,CAAC,CAAC,WAAW,CAAC;AAC/B,CAAC;AACW,MAACC,iBAAiB,GAAG;EAC/B,CAACC,kBAAkB,GAAIC,KAAK,IAAKC,QAAQ,CAACD,KAAK,CAAC;EAChD,CAACE,WAAW,GAAIF,KAAK,IAAKC,QAAQ,CAACD,KAAK,CAAC;EACzC,CAACG,YAAY,GAAIH,KAAK,IAAKC,QAAQ,CAACD,KAAK,CAAC;EAC1CI,KAAK,EAAGC,GAAG,IAAKA,GAAG,YAAYC,UAAU;EACzCC,IAAI,EAAGF,GAAG,IAAKA,GAAG,YAAYC,UAAU;EACxCE,KAAK,EAAEA,CAAA,KAAM,IAAI;EACjBC,MAAM,EAAGC,IAAI,IAAKC,QAAQ,CAACD,IAAI;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}