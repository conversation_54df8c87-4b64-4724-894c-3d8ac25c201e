{"ast": null, "code": "import baseRandom from './_baseRandom.js';\n\n/**\n * A specialized version of `_.sample` for arrays.\n *\n * @private\n * @param {Array} array The array to sample.\n * @returns {*} Returns the random element.\n */\nfunction arraySample(array) {\n  var length = array.length;\n  return length ? array[baseRandom(0, length - 1)] : undefined;\n}\nexport default arraySample;", "map": {"version": 3, "names": ["baseRandom", "arraySample", "array", "length", "undefined"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/_arraySample.js"], "sourcesContent": ["import baseRandom from './_baseRandom.js';\n\n/**\n * A specialized version of `_.sample` for arrays.\n *\n * @private\n * @param {Array} array The array to sample.\n * @returns {*} Returns the random element.\n */\nfunction arraySample(array) {\n  var length = array.length;\n  return length ? array[baseRandom(0, length - 1)] : undefined;\n}\n\nexport default arraySample;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;EACzB,OAAOA,MAAM,GAAGD,KAAK,CAACF,UAAU,CAAC,CAAC,EAAEG,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGC,SAAS;AAC9D;AAEA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}