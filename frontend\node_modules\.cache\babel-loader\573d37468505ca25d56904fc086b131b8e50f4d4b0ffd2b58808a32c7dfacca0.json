{"ast": null, "code": "import { defineComponent, useSlots, reactive, computed, toRefs, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, createVNode, isRef, withCtx, createTextVNode, toDisplayString, createBlock, createCommentVNode, withDirectives, Fragment, renderList, vShow, renderSlot } from 'vue';\nimport { ElCheckbox, ElCheckboxGroup } from '../../checkbox/index.mjs';\nimport { ElInput } from '../../input/index.mjs';\nimport { Search } from '@element-plus/icons-vue';\nimport { transferPanelProps, transferPanelEmits } from './transfer-panel2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { usePropsAlias } from './composables/use-props-alias.mjs';\nimport { useCheck } from './composables/use-check.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isEmpty } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTransferPanel\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: transferPanelProps,\n  emits: transferPanelEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const slots = useSlots();\n    const OptionContent = ({\n      option\n    }) => option;\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"transfer\");\n    const panelState = reactive({\n      checked: [],\n      allChecked: false,\n      query: \"\",\n      checkChangeByUser: true\n    });\n    const propsAlias = usePropsAlias(props);\n    const {\n      filteredData,\n      checkedSummary,\n      isIndeterminate,\n      handleAllCheckedChange\n    } = useCheck(props, panelState, emit);\n    const hasNoMatch = computed(() => !isEmpty(panelState.query) && isEmpty(filteredData.value));\n    const hasFooter = computed(() => !isEmpty(slots.default()[0].children));\n    const {\n      checked,\n      allChecked,\n      query\n    } = toRefs(panelState);\n    expose({\n      query\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(ns).b(\"panel\"))\n      }, [createElementVNode(\"p\", {\n        class: normalizeClass(unref(ns).be(\"panel\", \"header\"))\n      }, [createVNode(unref(ElCheckbox), {\n        modelValue: unref(allChecked),\n        \"onUpdate:modelValue\": $event => isRef(allChecked) ? allChecked.value = $event : null,\n        indeterminate: unref(isIndeterminate),\n        \"validate-event\": false,\n        onChange: unref(handleAllCheckedChange)\n      }, {\n        default: withCtx(() => [createTextVNode(toDisplayString(_ctx.title) + \" \", 1), createElementVNode(\"span\", null, toDisplayString(unref(checkedSummary)), 1)]),\n        _: 1\n      }, 8, [\"modelValue\", \"onUpdate:modelValue\", \"indeterminate\", \"onChange\"])], 2), createElementVNode(\"div\", {\n        class: normalizeClass([unref(ns).be(\"panel\", \"body\"), unref(ns).is(\"with-footer\", unref(hasFooter))])\n      }, [_ctx.filterable ? (openBlock(), createBlock(unref(ElInput), {\n        key: 0,\n        modelValue: unref(query),\n        \"onUpdate:modelValue\": $event => isRef(query) ? query.value = $event : null,\n        class: normalizeClass(unref(ns).be(\"panel\", \"filter\")),\n        size: \"default\",\n        placeholder: _ctx.placeholder,\n        \"prefix-icon\": unref(Search),\n        clearable: \"\",\n        \"validate-event\": false\n      }, null, 8, [\"modelValue\", \"onUpdate:modelValue\", \"class\", \"placeholder\", \"prefix-icon\"])) : createCommentVNode(\"v-if\", true), withDirectives(createVNode(unref(ElCheckboxGroup), {\n        modelValue: unref(checked),\n        \"onUpdate:modelValue\": $event => isRef(checked) ? checked.value = $event : null,\n        \"validate-event\": false,\n        class: normalizeClass([unref(ns).is(\"filterable\", _ctx.filterable), unref(ns).be(\"panel\", \"list\")])\n      }, {\n        default: withCtx(() => [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(filteredData), item => {\n          return openBlock(), createBlock(unref(ElCheckbox), {\n            key: item[unref(propsAlias).key],\n            class: normalizeClass(unref(ns).be(\"panel\", \"item\")),\n            value: item[unref(propsAlias).key],\n            disabled: item[unref(propsAlias).disabled],\n            \"validate-event\": false\n          }, {\n            default: withCtx(() => {\n              var _a;\n              return [createVNode(OptionContent, {\n                option: (_a = _ctx.optionRender) == null ? void 0 : _a.call(_ctx, item)\n              }, null, 8, [\"option\"])];\n            }),\n            _: 2\n          }, 1032, [\"class\", \"value\", \"disabled\"]);\n        }), 128))]),\n        _: 1\n      }, 8, [\"modelValue\", \"onUpdate:modelValue\", \"class\"]), [[vShow, !unref(hasNoMatch) && !unref(isEmpty)(_ctx.data)]]), withDirectives(createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).be(\"panel\", \"empty\"))\n      }, [renderSlot(_ctx.$slots, \"empty\", {}, () => [createTextVNode(toDisplayString(unref(hasNoMatch) ? unref(t)(\"el.transfer.noMatch\") : unref(t)(\"el.transfer.noData\")), 1)])], 2), [[vShow, unref(hasNoMatch) || unref(isEmpty)(_ctx.data)]])], 2), unref(hasFooter) ? (openBlock(), createElementBlock(\"p\", {\n        key: 0,\n        class: normalizeClass(unref(ns).be(\"panel\", \"footer\"))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2)) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar TransferPanel = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"transfer-panel.vue\"]]);\nexport { TransferPanel as default };", "map": {"version": 3, "names": ["name", "slots", "useSlots", "OptionContent", "option", "t", "useLocale", "ns", "useNamespace", "panelState", "reactive", "checked", "allChecked", "query", "checkChangeByUser", "props<PERSON><PERSON><PERSON>", "usePropsAlias", "props", "filteredData", "checkedSummary", "isIndeterminate", "handleAllCheckedChange", "useCheck", "emit", "hasNoMatch", "computed", "isEmpty", "value", "<PERSON><PERSON><PERSON>er", "default", "children", "toRefs", "expose", "_ctx", "_cache", "openBlock", "createElementBlock", "class", "normalizeClass", "unref", "b"], "sources": ["../../../../../../packages/components/transfer/src/transfer-panel.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b('panel')\">\n    <p :class=\"ns.be('panel', 'header')\">\n      <el-checkbox\n        v-model=\"allChecked\"\n        :indeterminate=\"isIndeterminate\"\n        :validate-event=\"false\"\n        @change=\"handleAllCheckedChange\"\n      >\n        {{ title }}\n        <span>{{ checkedSummary }}</span>\n      </el-checkbox>\n    </p>\n\n    <div :class=\"[ns.be('panel', 'body'), ns.is('with-footer', hasFooter)]\">\n      <el-input\n        v-if=\"filterable\"\n        v-model=\"query\"\n        :class=\"ns.be('panel', 'filter')\"\n        size=\"default\"\n        :placeholder=\"placeholder\"\n        :prefix-icon=\"Search\"\n        clearable\n        :validate-event=\"false\"\n      />\n      <el-checkbox-group\n        v-show=\"!hasNoMatch && !isEmpty(data)\"\n        v-model=\"checked\"\n        :validate-event=\"false\"\n        :class=\"[ns.is('filterable', filterable), ns.be('panel', 'list')]\"\n      >\n        <el-checkbox\n          v-for=\"item in filteredData\"\n          :key=\"item[propsAlias.key]\"\n          :class=\"ns.be('panel', 'item')\"\n          :value=\"item[propsAlias.key]\"\n          :disabled=\"item[propsAlias.disabled]\"\n          :validate-event=\"false\"\n        >\n          <option-content :option=\"optionRender?.(item)\" />\n        </el-checkbox>\n      </el-checkbox-group>\n      <div\n        v-show=\"hasNoMatch || isEmpty(data)\"\n        :class=\"ns.be('panel', 'empty')\"\n      >\n        <slot name=\"empty\">\n          {{ hasNoMatch ? t('el.transfer.noMatch') : t('el.transfer.noData') }}\n        </slot>\n      </div>\n    </div>\n    <p v-if=\"hasFooter\" :class=\"ns.be('panel', 'footer')\">\n      <slot />\n    </p>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, reactive, toRefs, useSlots } from 'vue'\nimport { isEmpty } from '@element-plus/utils'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { ElCheckbox, ElCheckboxGroup } from '@element-plus/components/checkbox'\nimport { ElInput } from '@element-plus/components/input'\nimport { Search } from '@element-plus/icons-vue'\nimport { transferPanelEmits, transferPanelProps } from './transfer-panel'\nimport { useCheck, usePropsAlias } from './composables'\n\nimport type { VNode } from 'vue'\nimport type { TransferPanelState } from './transfer-panel'\n\ndefineOptions({\n  name: 'ElTransferPanel',\n})\n\nconst props = defineProps(transferPanelProps)\nconst emit = defineEmits(transferPanelEmits)\nconst slots = useSlots()\n\nconst OptionContent = ({ option }: { option?: VNode | VNode[] }) => option\n\nconst { t } = useLocale()\nconst ns = useNamespace('transfer')\n\nconst panelState = reactive<TransferPanelState>({\n  checked: [],\n  allChecked: false,\n  query: '',\n  checkChangeByUser: true,\n})\n\nconst propsAlias = usePropsAlias(props)\n\nconst {\n  filteredData,\n  checkedSummary,\n  isIndeterminate,\n  handleAllCheckedChange,\n} = useCheck(props, panelState, emit)\n\nconst hasNoMatch = computed(\n  () => !isEmpty(panelState.query) && isEmpty(filteredData.value)\n)\n\nconst hasFooter = computed(() => !isEmpty(slots.default!()[0].children))\n\nconst { checked, allChecked, query } = toRefs(panelState)\n\ndefineExpose({\n  /** @description filter keyword */\n  query,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;mCAsEc;EACZA,IAAM;AACR;;;;;;;;;;IAIA,MAAMC,KAAA,GAAQC,QAAS;IAEvB,MAAMC,aAAgB,GAAAA,CAAC;MAAEC;IAAA,CAA2C,KAAAA,MAAA;IAE9D;MAAEC;IAAE,IAAIC,SAAU;IAClB,MAAAC,EAAA,GAAKC,YAAA,CAAa,UAAU;IAElC,MAAMC,UAAA,GAAaC,QAA6B;MAC9CC,OAAA,EAAS,EAAC;MACVC,UAAY;MACZC,KAAO;MACPC,iBAAmB;IAAA,CACpB;IAEK,MAAAC,UAAA,GAAaC,aAAA,CAAcC,KAAK;IAEhC;MACJC,YAAA;MACAC,cAAA;MACAC,eAAA;MACAC;IAAA,CACE,GAAAC,QAAA,CAASL,KAAO,EAAAR,UAAA,EAAYc,IAAI;IAEpC,MAAMC,UAAa,GAAAC,QAAA,QAAAC,OAAA,CAAAjB,UAAA,CAAAI,KAAA,KAAAa,OAAA,CAAAR,YAAA,CAAAS,KAAA;IACjB,MAAAC,SAAe,GAAAH,QAAA,OAAgB,CAAKC,OAAA,CAAAzB,KAAA,CAAA4B,OAAA,IAAqB,CAAK,CAAAC,QAAA;IAChE;MAAAnB,OAAA;MAAAC,UAAA;MAAAC;IAAA,IAAAkB,MAAA,CAAAtB,UAAA;IAEMuB,MAAA;MAENnB;IAEA,CAAa;IAAA,QAAAoB,IAAA,EAAAC,MAAA;MAEX,OAAAC,SAAA,IAAAC,kBAAA;QACDC,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAAhC,EAAA,EAAAiC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}