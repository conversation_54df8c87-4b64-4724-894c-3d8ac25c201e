{"ast": null, "code": "import { isClient, unrefElement } from '@vueuse/core';\nconst buildPopperOptions = (props, modifiers = []) => {\n  const {\n    placement,\n    strategy,\n    popperOptions\n  } = props;\n  const options = {\n    placement,\n    strategy,\n    ...popperOptions,\n    modifiers: [...genModifiers(props), ...modifiers]\n  };\n  deriveExtraModifiers(options, popperOptions == null ? void 0 : popperOptions.modifiers);\n  return options;\n};\nconst unwrapMeasurableEl = $el => {\n  if (!isClient) return;\n  return unrefElement($el);\n};\nfunction genModifiers(options) {\n  const {\n    offset,\n    gpuAcceleration,\n    fallbackPlacements\n  } = options;\n  return [{\n    name: \"offset\",\n    options: {\n      offset: [0, offset != null ? offset : 12]\n    }\n  }, {\n    name: \"preventOverflow\",\n    options: {\n      padding: {\n        top: 2,\n        bottom: 2,\n        left: 5,\n        right: 5\n      }\n    }\n  }, {\n    name: \"flip\",\n    options: {\n      padding: 5,\n      fallbackPlacements\n    }\n  }, {\n    name: \"computeStyles\",\n    options: {\n      gpuAcceleration\n    }\n  }];\n}\nfunction deriveExtraModifiers(options, modifiers) {\n  if (modifiers) {\n    options.modifiers = [...options.modifiers, ...(modifiers != null ? modifiers : [])];\n  }\n}\nexport { buildPopperOptions, unwrapMeasurableEl };", "map": {"version": 3, "names": ["buildPopperOptions", "props", "modifiers", "placement", "strategy", "popperOptions", "options", "genModifiers", "deriveExtraModifiers", "unwrapMeasurableEl", "$el", "isClient", "unrefElement", "offset", "gpuAcceleration", "fallbackPlacements", "name", "padding", "top", "bottom", "left", "right"], "sources": ["../../../../../../packages/components/popper/src/utils.ts"], "sourcesContent": ["import { unrefElement } from '@vueuse/core'\nimport { isClient } from '@element-plus/utils'\n\nimport type { ComponentPublicInstance } from 'vue'\nimport type { MaybeRef } from '@vueuse/core'\nimport type { Modifier } from '@popperjs/core'\nimport type { Measurable } from './constants'\nimport type { PopperCoreConfigProps } from './content'\n\nexport const buildPopperOptions = (\n  props: PopperCoreConfigProps,\n  modifiers: Modifier<any, any>[] = []\n) => {\n  const { placement, strategy, popperOptions } = props\n  const options = {\n    placement,\n    strategy,\n    ...popperOptions,\n    modifiers: [...genModifiers(props), ...modifiers],\n  }\n\n  deriveExtraModifiers(options, popperOptions?.modifiers)\n  return options\n}\n\nexport const unwrapMeasurableEl = (\n  $el: MaybeRef<Measurable | undefined | ComponentPublicInstance>\n) => {\n  if (!isClient) return\n  return unrefElement($el as HTMLElement)\n}\n\nfunction genModifiers(options: PopperCoreConfigProps) {\n  const { offset, gpuAcceleration, fallbackPlacements } = options\n  return [\n    {\n      name: 'offset',\n      options: {\n        offset: [0, offset ?? 12],\n      },\n    },\n    {\n      name: 'preventOverflow',\n      options: {\n        padding: {\n          top: 2,\n          bottom: 2,\n          left: 5,\n          right: 5,\n        },\n      },\n    },\n    {\n      name: 'flip',\n      options: {\n        padding: 5,\n        fallbackPlacements,\n      },\n    },\n    {\n      name: 'computeStyles',\n      options: {\n        gpuAcceleration,\n      },\n    },\n  ]\n}\n\nfunction deriveExtraModifiers(\n  options: any,\n  modifiers: PopperCoreConfigProps['popperOptions']['modifiers']\n) {\n  if (modifiers) {\n    options.modifiers = [...options.modifiers, ...(modifiers ?? [])]\n  }\n}\n"], "mappings": ";AAEY,MAACA,kBAAkB,GAAGA,CAACC,KAAK,EAAEC,SAAS,GAAG,EAAE,KAAK;EAC3D,MAAM;IAAEC,SAAS;IAAEC,QAAQ;IAAEC;EAAa,CAAE,GAAGJ,KAAK;EACpD,MAAMK,OAAO,GAAG;IACdH,SAAS;IACTC,QAAQ;IACR,GAAGC,aAAa;IAChBH,SAAS,EAAE,CAAC,GAAGK,YAAY,CAACN,KAAK,CAAC,EAAE,GAAGC,SAAS;EACpD,CAAG;EACDM,oBAAoB,CAACF,OAAO,EAAED,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACH,SAAS,CAAC;EACvF,OAAOI,OAAO;AAChB;AACY,MAACG,kBAAkB,GAAIC,GAAG,IAAK;EACzC,IAAI,CAACC,QAAQ,EACX;EACF,OAAOC,YAAY,CAACF,GAAG,CAAC;AAC1B;AACA,SAASH,YAAYA,CAACD,OAAO,EAAE;EAC7B,MAAM;IAAEO,MAAM;IAAEC,eAAe;IAAEC;EAAkB,CAAE,GAAGT,OAAO;EAC/D,OAAO,CACL;IACEU,IAAI,EAAE,QAAQ;IACdV,OAAO,EAAE;MACPO,MAAM,EAAE,CAAC,CAAC,EAAEA,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAG,EAAE;IAChD;EACA,CAAK,EACD;IACEG,IAAI,EAAE,iBAAiB;IACvBV,OAAO,EAAE;MACPW,OAAO,EAAE;QACPC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE;MACjB;IACA;EACA,CAAK,EACD;IACEL,IAAI,EAAE,MAAM;IACZV,OAAO,EAAE;MACPW,OAAO,EAAE,CAAC;MACVF;IACR;EACA,CAAK,EACD;IACEC,IAAI,EAAE,eAAe;IACrBV,OAAO,EAAE;MACPQ;IACR;EACA,CAAK,CACF;AACH;AACA,SAASN,oBAAoBA,CAACF,OAAO,EAAEJ,SAAS,EAAE;EAChD,IAAIA,SAAS,EAAE;IACbI,OAAO,CAACJ,SAAS,GAAG,CAAC,GAAGI,OAAO,CAACJ,SAAS,EAAE,IAAGA,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAG,EAAE,EAAC;EACrF;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}