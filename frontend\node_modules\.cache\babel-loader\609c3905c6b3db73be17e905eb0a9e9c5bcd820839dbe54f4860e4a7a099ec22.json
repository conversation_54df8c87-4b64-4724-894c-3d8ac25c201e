{"ast": null, "code": "import { defineComponent, computed, h } from 'vue';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isString } from '@vue/shared';\nconst sliderMarkerProps = buildProps({\n  mark: {\n    type: definePropType([String, Object]),\n    default: void 0\n  }\n});\nvar SliderMarker = defineComponent({\n  name: \"ElSliderMarker\",\n  props: sliderMarkerProps,\n  setup(props) {\n    const ns = useNamespace(\"slider\");\n    const label = computed(() => {\n      return isString(props.mark) ? props.mark : props.mark.label;\n    });\n    const style = computed(() => isString(props.mark) ? void 0 : props.mark.style);\n    return () => h(\"div\", {\n      class: ns.e(\"marks-text\"),\n      style: style.value\n    }, label.value);\n  }\n});\nexport { SliderMarker as default, sliderMarkerProps };", "map": {"version": 3, "names": ["sliderMarkerProps", "buildProps", "mark", "type", "definePropType", "String", "Object", "default", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defineComponent", "name", "props", "setup", "ns", "useNamespace", "label", "computed", "isString", "style", "h", "class", "e", "value"], "sources": ["../../../../../../packages/components/slider/src/marker.ts"], "sourcesContent": ["import { computed, defineComponent, h } from 'vue'\nimport { buildProps, definePropType, isString } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport type { CSSProperties, ExtractPropTypes } from 'vue'\n\nexport const sliderMarkerProps = buildProps({\n  mark: {\n    type: definePropType<\n      | string\n      | {\n          style: CSSProperties\n          label: any\n        }\n    >([String, Object]),\n    default: undefined,\n  },\n} as const)\nexport type SliderMarkerProps = ExtractPropTypes<typeof sliderMarkerProps>\n\nexport default defineComponent({\n  name: 'ElSliderMarker',\n  props: sliderMarkerProps,\n  setup(props) {\n    const ns = useNamespace('slider')\n    const label = computed(() => {\n      return isString(props.mark) ? props.mark : props.mark!.label\n    })\n    const style = computed(() =>\n      isString(props.mark) ? undefined : props.mark!.style\n    )\n\n    return () =>\n      h(\n        'div',\n        {\n          class: ns.e('marks-text'),\n          style: style.value,\n        },\n        label.value\n      )\n  },\n})\n"], "mappings": ";;;;AAGY,MAACA,iBAAiB,GAAGC,UAAU,CAAC;EAC1CC,IAAI,EAAE;IACJC,IAAI,EAAEC,cAAc,CAAC,CAACC,MAAM,EAAEC,MAAM,CAAC,CAAC;IACtCC,OAAO,EAAE,KAAK;EAClB;AACA,CAAC;AACD,IAAAC,YAAA,GAAeC,eAAe,CAAC;EAC7BC,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAEX,iBAAiB;EACxBY,KAAKA,CAACD,KAAK,EAAE;IACX,MAAME,EAAE,GAAGC,YAAY,CAAC,QAAQ,CAAC;IACjC,MAAMC,KAAK,GAAGC,QAAQ,CAAC,MAAM;MAC3B,OAAOC,QAAQ,CAACN,KAAK,CAACT,IAAI,CAAC,GAAGS,KAAK,CAACT,IAAI,GAAGS,KAAK,CAACT,IAAI,CAACa,KAAK;IACjE,CAAK,CAAC;IACF,MAAMG,KAAK,GAAGF,QAAQ,CAAC,MAAMC,QAAQ,CAACN,KAAK,CAACT,IAAI,CAAC,GAAG,KAAK,CAAC,GAAGS,KAAK,CAACT,IAAI,CAACgB,KAAK,CAAC;IAC9E,OAAO,MAAMC,CAAC,CAAC,KAAK,EAAE;MACpBC,KAAK,EAAEP,EAAE,CAACQ,CAAC,CAAC,YAAY,CAAC;MACzBH,KAAK,EAAEA,KAAK,CAACI;IACnB,CAAK,EAAEP,KAAK,CAACO,KAAK,CAAC;EACnB;AACA,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}