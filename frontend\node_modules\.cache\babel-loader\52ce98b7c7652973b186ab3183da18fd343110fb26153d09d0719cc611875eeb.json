{"ast": null, "code": "import Row from './src/row2.mjs';\nexport { RowAlign, RowJustify, rowProps } from './src/row.mjs';\nexport { rowContextKey } from './src/constants.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElRow = withInstall(Row);\nexport { ElRow, ElRow as default };", "map": {"version": 3, "names": ["ElRow", "withInstall", "Row"], "sources": ["../../../../../packages/components/row/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Row from './src/row.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElRow: SFCWithInstall<typeof Row> = withInstall(Row)\nexport default ElRow\n\nexport * from './src/row'\nexport * from './src/constants'\n"], "mappings": ";;;;AAEY,MAACA,KAAK,GAAGC,WAAW,CAACC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}