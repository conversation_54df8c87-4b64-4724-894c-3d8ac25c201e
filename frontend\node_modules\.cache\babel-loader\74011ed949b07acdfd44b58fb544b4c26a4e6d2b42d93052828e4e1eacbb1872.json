{"ast": null, "code": "import { defineComponent } from 'vue';\nimport { COMPONENT_NAME } from './constants.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst descriptionItemProps = buildProps({\n  label: {\n    type: String,\n    default: \"\"\n  },\n  span: {\n    type: Number,\n    default: 1\n  },\n  rowspan: {\n    type: Number,\n    default: 1\n  },\n  width: {\n    type: [String, Number],\n    default: \"\"\n  },\n  minWidth: {\n    type: [String, Number],\n    default: \"\"\n  },\n  labelWidth: {\n    type: [String, Number],\n    default: \"\"\n  },\n  align: {\n    type: String,\n    default: \"left\"\n  },\n  labelAlign: {\n    type: String,\n    default: \"\"\n  },\n  className: {\n    type: String,\n    default: \"\"\n  },\n  labelClassName: {\n    type: String,\n    default: \"\"\n  }\n});\nconst DescriptionItem = defineComponent({\n  name: COMPONENT_NAME,\n  props: descriptionItemProps\n});\nexport { DescriptionItem as default, descriptionItemProps };", "map": {"version": 3, "names": ["descriptionItemProps", "buildProps", "label", "type", "String", "default", "span", "Number", "rowspan", "width", "min<PERSON><PERSON><PERSON>", "labelWidth", "align", "labelAlign", "className", "labelClassName", "DescriptionItem", "defineComponent", "name", "COMPONENT_NAME", "props"], "sources": ["../../../../../../packages/components/descriptions/src/description-item.ts"], "sourcesContent": ["import { defineComponent } from 'vue'\nimport { buildProps } from '@element-plus/utils'\nimport { COMPONENT_NAME } from './constants'\n\nimport type { ExtractPropTypes, Slot, VNode } from 'vue'\n\nexport const descriptionItemProps = buildProps({\n  /**\n   * @description label text\n   */\n  label: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description colspan of column\n   */\n  span: {\n    type: Number,\n    default: 1,\n  },\n  /**\n   * @description the number of rows a cell should span\n   */\n  rowspan: {\n    type: Number,\n    default: 1,\n  },\n  /**\n   * @description column width, the width of the same column in different rows is set by the max value (If no `border`, width contains label and content)\n   */\n  width: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description column minimum width, columns with `width` has a fixed width, while columns with `min-width` has a width that is distributed in proportion (If no`border`, width contains label and content)\n   */\n  minWidth: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description column label width, if not set, it will be the same as the width of the column. Higher priority than the `label-width` of `Descriptions`\n   */\n  labelWidth: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description column content alignment (If no `border`, effective for both label and content)\n   */\n  align: {\n    type: String,\n    default: 'left',\n  },\n  /**\n   * @description column label alignment, if omitted, the value of the above `align` attribute will be applied (If no `border`, please use `align` attribute)\n   */\n  labelAlign: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description column content custom class name\n   */\n  className: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description column label custom class name\n   */\n  labelClassName: {\n    type: String,\n    default: '',\n  },\n})\n\nconst DescriptionItem = defineComponent({\n  name: COMPONENT_NAME,\n  props: descriptionItemProps,\n})\n\nexport default DescriptionItem\n\nexport type DescriptionItemProps = ExtractPropTypes<typeof descriptionItemProps>\nexport type DescriptionItemVNode = VNode & {\n  children: { [name: string]: Slot } | null\n  props: Partial<DescriptionItemProps> | null\n}\n"], "mappings": ";;;AAGY,MAACA,oBAAoB,GAAGC,UAAU,CAAC;EAC7CC,KAAK,EAAE;IACLC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,IAAI,EAAE;IACJH,IAAI,EAAEI,MAAM;IACZF,OAAO,EAAE;EACb,CAAG;EACDG,OAAO,EAAE;IACPL,IAAI,EAAEI,MAAM;IACZF,OAAO,EAAE;EACb,CAAG;EACDI,KAAK,EAAE;IACLN,IAAI,EAAE,CAACC,MAAM,EAAEG,MAAM,CAAC;IACtBF,OAAO,EAAE;EACb,CAAG;EACDK,QAAQ,EAAE;IACRP,IAAI,EAAE,CAACC,MAAM,EAAEG,MAAM,CAAC;IACtBF,OAAO,EAAE;EACb,CAAG;EACDM,UAAU,EAAE;IACVR,IAAI,EAAE,CAACC,MAAM,EAAEG,MAAM,CAAC;IACtBF,OAAO,EAAE;EACb,CAAG;EACDO,KAAK,EAAE;IACLT,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDQ,UAAU,EAAE;IACVV,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDS,SAAS,EAAE;IACTX,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDU,cAAc,EAAE;IACdZ,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb;AACA,CAAC;AACI,MAACW,eAAe,GAAGC,eAAe,CAAC;EACtCC,IAAI,EAAEC,cAAc;EACpBC,KAAK,EAAEpB;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}