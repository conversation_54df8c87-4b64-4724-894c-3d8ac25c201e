{"ast": null, "code": "import baseSlice from './_baseSlice.js';\n\n/**\n * Gets all but the first element of `array`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to query.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.tail([1, 2, 3]);\n * // => [2, 3]\n */\nfunction tail(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseSlice(array, 1, length) : [];\n}\nexport default tail;", "map": {"version": 3, "names": ["baseSlice", "tail", "array", "length"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/tail.js"], "sourcesContent": ["import baseSlice from './_baseSlice.js';\n\n/**\n * Gets all but the first element of `array`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to query.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.tail([1, 2, 3]);\n * // => [2, 3]\n */\nfunction tail(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseSlice(array, 1, length) : [];\n}\n\nexport default tail;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACC,KAAK,EAAE;EACnB,IAAIC,MAAM,GAAGD,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACC,MAAM;EAC7C,OAAOA,MAAM,GAAGH,SAAS,CAACE,KAAK,EAAE,CAAC,EAAEC,MAAM,CAAC,GAAG,EAAE;AAClD;AAEA,eAAeF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}