{"ast": null, "code": "import Watermark from './src/watermark2.mjs';\nexport { watermarkProps } from './src/watermark.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElWatermark = withInstall(Watermark);\nexport { ElWatermark, ElWatermark as default };", "map": {"version": 3, "names": ["ElWatermark", "withInstall", "Watermark"], "sources": ["../../../../../packages/components/watermark/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Watermark from './src/watermark.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElWatermark: SFCWithInstall<typeof Watermark> =\n  withInstall(Watermark)\nexport default ElWatermark\n\nexport * from './src/watermark'\n"], "mappings": ";;;AAEY,MAACA,WAAW,GAAGC,WAAW,CAACC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}