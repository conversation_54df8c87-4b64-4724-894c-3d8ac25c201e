{"ast": null, "code": "const elPaginationKey = Symbol(\"elPaginationKey\");\nexport { elPaginationKey };", "map": {"version": 3, "names": ["elPaginationKey", "Symbol"], "sources": ["../../../../../../packages/components/pagination/src/constants.ts"], "sourcesContent": ["import type { ComputedRef, InjectionKey, WritableComputedRef } from 'vue'\n\nexport interface ElPaginationContext {\n  currentPage?: WritableComputedRef<number>\n  pageCount?: ComputedRef<number>\n  disabled?: ComputedRef<boolean>\n  changeEvent?: (val: number) => void\n  handleSizeChange?: (val: number) => void\n}\n\nexport const elPaginationKey: InjectionKey<ElPaginationContext> =\n  Symbol('elPaginationKey')\n"], "mappings": "AAAY,MAACA,eAAe,GAAGC,MAAM,CAAC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}