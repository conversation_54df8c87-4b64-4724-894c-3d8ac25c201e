{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst scrollbarProps = buildProps({\n  height: {\n    type: [String, Number],\n    default: \"\"\n  },\n  maxHeight: {\n    type: [String, Number],\n    default: \"\"\n  },\n  native: {\n    type: Boolean,\n    default: false\n  },\n  wrapStyle: {\n    type: definePropType([String, Object, Array]),\n    default: \"\"\n  },\n  wrapClass: {\n    type: [String, Array],\n    default: \"\"\n  },\n  viewClass: {\n    type: [String, Array],\n    default: \"\"\n  },\n  viewStyle: {\n    type: [String, Array, Object],\n    default: \"\"\n  },\n  noresize: Boolean,\n  tag: {\n    type: String,\n    default: \"div\"\n  },\n  always: Boolean,\n  minSize: {\n    type: Number,\n    default: 20\n  },\n  tabindex: {\n    type: [String, Number],\n    default: void 0\n  },\n  id: String,\n  role: String,\n  ...useAriaProps([\"ariaLabel\", \"ariaOrientation\"])\n});\nconst scrollbarEmits = {\n  scroll: ({\n    scrollTop,\n    scrollLeft\n  }) => [scrollTop, scrollLeft].every(isNumber)\n};\nexport { scrollbarEmits, scrollbarProps };", "map": {"version": 3, "names": ["scrollbarProps", "buildProps", "height", "type", "String", "Number", "default", "maxHeight", "native", "Boolean", "wrapStyle", "definePropType", "Object", "Array", "wrapClass", "viewClass", "viewStyle", "noresize", "tag", "always", "minSize", "tabindex", "id", "role", "useAriaProps", "scrollbarEmits", "scroll", "scrollTop", "scrollLeft", "every", "isNumber"], "sources": ["../../../../../../packages/components/scrollbar/src/scrollbar.ts"], "sourcesContent": ["import { buildProps, definePropType, isNumber } from '@element-plus/utils'\nimport { useAriaProps } from '@element-plus/hooks'\nimport type { ExtractPropTypes, StyleValue } from 'vue'\nimport type Scrollbar from './scrollbar.vue'\n\nexport const scrollbarProps = buildProps({\n  /**\n   * @description height of scrollbar\n   */\n  height: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description max height of scrollbar\n   */\n  maxHeight: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description whether to use the native scrollbar\n   */\n  native: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description style of wrap\n   */\n  wrapStyle: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n    default: '',\n  },\n  /**\n   * @description class of wrap\n   */\n  wrapClass: {\n    type: [String, Array],\n    default: '',\n  },\n  /**\n   * @description class of view\n   */\n  viewClass: {\n    type: [String, Array],\n    default: '',\n  },\n  /**\n   * @description style of view\n   */\n  viewStyle: {\n    type: [String, Array, Object],\n    default: '',\n  },\n  /**\n   * @description do not respond to container size changes, if the container size does not change, it is better to set it to optimize performance\n   */\n  noresize: Boolean, // 如果 container 尺寸不会发生变化，最好设置它可以优化性能\n  /**\n   * @description element tag of the view\n   */\n  tag: {\n    type: String,\n    default: 'div',\n  },\n  /**\n   * @description always show\n   */\n  always: Boolean,\n  /**\n   * @description minimum size of scrollbar\n   */\n  minSize: {\n    type: Number,\n    default: 20,\n  },\n  /**\n   * @description Wrap tabindex\n   */\n  tabindex: {\n    type: [String, Number],\n    default: undefined,\n  },\n  /**\n   * @description id of view\n   */\n  id: String,\n  /**\n   * @description role of view\n   */\n  role: String,\n  ...useAriaProps(['ariaLabel', 'ariaOrientation']),\n} as const)\nexport type ScrollbarProps = ExtractPropTypes<typeof scrollbarProps>\n\nexport const scrollbarEmits = {\n  scroll: ({\n    scrollTop,\n    scrollLeft,\n  }: {\n    scrollTop: number\n    scrollLeft: number\n  }) => [scrollTop, scrollLeft].every(isNumber),\n}\nexport type ScrollbarEmits = typeof scrollbarEmits\n\nexport type ScrollbarInstance = InstanceType<typeof Scrollbar> & unknown\n"], "mappings": ";;;AAEY,MAACA,cAAc,GAAGC,UAAU,CAAC;EACvCC,MAAM,EAAE;IACNC,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;IACtBC,OAAO,EAAE;EACb,CAAG;EACDC,SAAS,EAAE;IACTJ,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;IACtBC,OAAO,EAAE;EACb,CAAG;EACDE,MAAM,EAAE;IACNL,IAAI,EAAEM,OAAO;IACbH,OAAO,EAAE;EACb,CAAG;EACDI,SAAS,EAAE;IACTP,IAAI,EAAEQ,cAAc,CAAC,CAACP,MAAM,EAAEQ,MAAM,EAAEC,KAAK,CAAC,CAAC;IAC7CP,OAAO,EAAE;EACb,CAAG;EACDQ,SAAS,EAAE;IACTX,IAAI,EAAE,CAACC,MAAM,EAAES,KAAK,CAAC;IACrBP,OAAO,EAAE;EACb,CAAG;EACDS,SAAS,EAAE;IACTZ,IAAI,EAAE,CAACC,MAAM,EAAES,KAAK,CAAC;IACrBP,OAAO,EAAE;EACb,CAAG;EACDU,SAAS,EAAE;IACTb,IAAI,EAAE,CAACC,MAAM,EAAES,KAAK,EAAED,MAAM,CAAC;IAC7BN,OAAO,EAAE;EACb,CAAG;EACDW,QAAQ,EAAER,OAAO;EACjBS,GAAG,EAAE;IACHf,IAAI,EAAEC,MAAM;IACZE,OAAO,EAAE;EACb,CAAG;EACDa,MAAM,EAAEV,OAAO;EACfW,OAAO,EAAE;IACPjB,IAAI,EAAEE,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDe,QAAQ,EAAE;IACRlB,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;IACtBC,OAAO,EAAE,KAAK;EAClB,CAAG;EACDgB,EAAE,EAAElB,MAAM;EACVmB,IAAI,EAAEnB,MAAM;EACZ,GAAGoB,YAAY,CAAC,CAAC,WAAW,EAAE,iBAAiB,CAAC;AAClD,CAAC;AACW,MAACC,cAAc,GAAG;EAC5BC,MAAM,EAAEA,CAAC;IACPC,SAAS;IACTC;EACJ,CAAG,KAAK,CAACD,SAAS,EAAEC,UAAU,CAAC,CAACC,KAAK,CAACC,QAAQ;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}