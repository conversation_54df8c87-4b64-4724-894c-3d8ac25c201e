{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, computed, provide } from 'vue';\nimport { collapseContextKey } from './constants.mjs';\nimport { castArray } from 'lodash-unified';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst useCollapse = (props, emit) => {\n  const activeNames = ref([]);\n  const computedActiveNames = computed(() => {\n    var _a;\n    const activeKeys = (_a = props.modelValue) != null ? _a : activeNames.value;\n    return castArray(activeKeys);\n  });\n  const setActiveNames = _activeNames => {\n    activeNames.value = _activeNames;\n    const value = props.accordion ? activeNames.value[0] : activeNames.value;\n    emit(UPDATE_MODEL_EVENT, value);\n    emit(CHANGE_EVENT, value);\n  };\n  const handleItemClick = name => {\n    if (props.accordion) {\n      setActiveNames([computedActiveNames.value[0] === name ? \"\" : name]);\n    } else {\n      const _activeNames = [...computedActiveNames.value];\n      const index = _activeNames.indexOf(name);\n      if (index > -1) {\n        _activeNames.splice(index, 1);\n      } else {\n        _activeNames.push(name);\n      }\n      setActiveNames(_activeNames);\n    }\n  };\n  provide(collapseContextKey, {\n    activeNames: computedActiveNames,\n    handleItemClick\n  });\n  return {\n    activeNames: computedActiveNames,\n    setActiveNames\n  };\n};\nconst useCollapseDOM = props => {\n  const ns = useNamespace(\"collapse\");\n  const rootKls = computed(() => [ns.b(), ns.b(`icon-position-${props.expandIconPosition}`)]);\n  return {\n    rootKls\n  };\n};\nexport { useCollapse, useCollapseDOM };", "map": {"version": 3, "names": ["useCollapse", "props", "emit", "activeNames", "ref", "computedActiveNames", "computed", "_a", "activeKeys", "modelValue", "value", "<PERSON><PERSON><PERSON><PERSON>", "setActiveNames", "_activeNames", "accordion", "UPDATE_MODEL_EVENT", "CHANGE_EVENT", "handleItemClick", "name", "index", "indexOf", "splice", "push", "provide", "collapseContextKey", "useCollapseDOM", "ns", "useNamespace", "rootKls", "b", "expandIconPosition"], "sources": ["../../../../../../packages/components/collapse/src/use-collapse.ts"], "sourcesContent": ["import { computed, provide, ref } from 'vue'\nimport { ensureArray } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { collapseContextKey } from './constants'\n\nimport type { SetupContext } from 'vue'\nimport type {\n  CollapseActiveName,\n  CollapseEmits,\n  CollapseProps,\n} from './collapse'\n\nexport const useCollapse = (\n  props: CollapseProps,\n  emit: SetupContext<CollapseEmits>['emit']\n) => {\n  const activeNames = ref<CollapseActiveName[]>([])\n\n  const computedActiveNames = computed(() => {\n    const activeKeys = props.modelValue ?? activeNames.value\n    return ensureArray(activeKeys)\n  })\n\n  const setActiveNames = (_activeNames: CollapseActiveName[]) => {\n    activeNames.value = _activeNames\n    const value = props.accordion ? activeNames.value[0] : activeNames.value\n    emit(UPDATE_MODEL_EVENT, value)\n    emit(CHANGE_EVENT, value)\n  }\n\n  const handleItemClick = (name: CollapseActiveName) => {\n    if (props.accordion) {\n      setActiveNames([computedActiveNames.value[0] === name ? '' : name])\n    } else {\n      const _activeNames = [...computedActiveNames.value]\n      const index = _activeNames.indexOf(name)\n\n      if (index > -1) {\n        _activeNames.splice(index, 1)\n      } else {\n        _activeNames.push(name)\n      }\n      setActiveNames(_activeNames)\n    }\n  }\n\n  provide(collapseContextKey, {\n    activeNames: computedActiveNames,\n    handleItemClick,\n  })\n  return {\n    activeNames: computedActiveNames,\n    setActiveNames,\n  }\n}\n\nexport const useCollapseDOM = (props: CollapseProps) => {\n  const ns = useNamespace('collapse')\n\n  const rootKls = computed(() => [\n    ns.b(),\n    ns.b(`icon-position-${props.expandIconPosition}`),\n  ])\n\n  return {\n    rootKls,\n  }\n}\n"], "mappings": ";;;;;;AAKY,MAACA,WAAW,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;EAC1C,MAAMC,WAAW,GAAGC,GAAG,CAAC,EAAE,CAAC;EAC3B,MAAMC,mBAAmB,GAAGC,QAAQ,CAAC,MAAM;IACzC,IAAIC,EAAE;IACN,MAAMC,UAAU,GAAG,CAACD,EAAE,GAAGN,KAAK,CAACQ,UAAU,KAAK,IAAI,GAAGF,EAAE,GAAGJ,WAAW,CAACO,KAAK;IAC3E,OAAOC,SAAW,CAACH,UAAU,CAAC;EAClC,CAAG,CAAC;EACF,MAAMI,cAAc,GAAIC,YAAY,IAAK;IACvCV,WAAW,CAACO,KAAK,GAAGG,YAAY;IAChC,MAAMH,KAAK,GAAGT,KAAK,CAACa,SAAS,GAAGX,WAAW,CAACO,KAAK,CAAC,CAAC,CAAC,GAAGP,WAAW,CAACO,KAAK;IACxER,IAAI,CAACa,kBAAkB,EAAEL,KAAK,CAAC;IAC/BR,IAAI,CAACc,YAAY,EAAEN,KAAK,CAAC;EAC7B,CAAG;EACD,MAAMO,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAIjB,KAAK,CAACa,SAAS,EAAE;MACnBF,cAAc,CAAC,CAACP,mBAAmB,CAACK,KAAK,CAAC,CAAC,CAAC,KAAKQ,IAAI,GAAG,EAAE,GAAGA,IAAI,CAAC,CAAC;IACzE,CAAK,MAAM;MACL,MAAML,YAAY,GAAG,CAAC,GAAGR,mBAAmB,CAACK,KAAK,CAAC;MACnD,MAAMS,KAAK,GAAGN,YAAY,CAACO,OAAO,CAACF,IAAI,CAAC;MACxC,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QACdN,YAAY,CAACQ,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACrC,CAAO,MAAM;QACLN,YAAY,CAACS,IAAI,CAACJ,IAAI,CAAC;MAC/B;MACMN,cAAc,CAACC,YAAY,CAAC;IAClC;EACA,CAAG;EACDU,OAAO,CAACC,kBAAkB,EAAE;IAC1BrB,WAAW,EAAEE,mBAAmB;IAChCY;EACJ,CAAG,CAAC;EACF,OAAO;IACLd,WAAW,EAAEE,mBAAmB;IAChCO;EACJ,CAAG;AACH;AACY,MAACa,cAAc,GAAIxB,KAAK,IAAK;EACvC,MAAMyB,EAAE,GAAGC,YAAY,CAAC,UAAU,CAAC;EACnC,MAAMC,OAAO,GAAGtB,QAAQ,CAAC,MAAM,CAC7BoB,EAAE,CAACG,CAAC,EAAE,EACNH,EAAE,CAACG,CAAC,CAAC,iBAAiB5B,KAAK,CAAC6B,kBAAkB,EAAE,CAAC,CAClD,CAAC;EACF,OAAO;IACLF;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}