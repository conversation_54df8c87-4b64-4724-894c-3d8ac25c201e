{"ast": null, "code": "import { createStore } from \"vuex\";\nimport user from \"./modules/user\";\nimport seat from \"./modules/seat\";\nexport default createStore({\n  state: {\n    appName: \"图书馆自习室管理系统\",\n    appVersion: \"1.0.0\",\n    loading: false,\n    error: null\n  },\n  getters: {\n    appName: state => state.appName,\n    appVersion: state => state.appVersion,\n    isLoading: state => state.loading,\n    error: state => state.error\n  },\n  mutations: {\n    SET_LOADING(state, loading) {\n      state.loading = loading;\n    },\n    SET_ERROR(state, error) {\n      state.error = error;\n    },\n    CLEAR_ERROR(state) {\n      state.error = null;\n    }\n  },\n  actions: {\n    setLoading({\n      commit\n    }, loading) {\n      commit(\"SET_LOADING\", loading);\n    },\n    setError({\n      commit\n    }, error) {\n      commit(\"SET_ERROR\", error);\n    },\n    clearError({\n      commit\n    }) {\n      commit(\"CLEAR_ERROR\");\n    }\n  },\n  modules: {\n    user,\n    seat\n  }\n});", "map": {"version": 3, "names": ["createStore", "user", "seat", "state", "appName", "appVersion", "loading", "error", "getters", "isLoading", "mutations", "SET_LOADING", "SET_ERROR", "CLEAR_ERROR", "actions", "setLoading", "commit", "setError", "clearError", "modules"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/src/store/index.js"], "sourcesContent": ["import { createStore } from \"vuex\";\nimport user from \"./modules/user\";\nimport seat from \"./modules/seat\";\n\nexport default createStore({\n  state: {\n    appName: \"图书馆自习室管理系统\",\n    appVersion: \"1.0.0\",\n    loading: false,\n    error: null,\n  },\n  getters: {\n    appName: (state) => state.appName,\n    appVersion: (state) => state.appVersion,\n    isLoading: (state) => state.loading,\n    error: (state) => state.error,\n  },\n  mutations: {\n    SET_LOADING(state, loading) {\n      state.loading = loading;\n    },\n    SET_ERROR(state, error) {\n      state.error = error;\n    },\n    CLEAR_ERROR(state) {\n      state.error = null;\n    },\n  },\n  actions: {\n    setLoading({ commit }, loading) {\n      commit(\"SET_LOADING\", loading);\n    },\n    setError({ commit }, error) {\n      commit(\"SET_ERROR\", error);\n    },\n    clearError({ commit }) {\n      commit(\"CLEAR_ERROR\");\n    },\n  },\n  modules: {\n    user,\n    seat,\n  },\n});\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,MAAM;AAClC,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,IAAI,MAAM,gBAAgB;AAEjC,eAAeF,WAAW,CAAC;EACzBG,KAAK,EAAE;IACLC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE,OAAO;IACnBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDC,OAAO,EAAE;IACPJ,OAAO,EAAGD,KAAK,IAAKA,KAAK,CAACC,OAAO;IACjCC,UAAU,EAAGF,KAAK,IAAKA,KAAK,CAACE,UAAU;IACvCI,SAAS,EAAGN,KAAK,IAAKA,KAAK,CAACG,OAAO;IACnCC,KAAK,EAAGJ,KAAK,IAAKA,KAAK,CAACI;EAC1B,CAAC;EACDG,SAAS,EAAE;IACTC,WAAWA,CAACR,KAAK,EAAEG,OAAO,EAAE;MAC1BH,KAAK,CAACG,OAAO,GAAGA,OAAO;IACzB,CAAC;IACDM,SAASA,CAACT,KAAK,EAAEI,KAAK,EAAE;MACtBJ,KAAK,CAACI,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDM,WAAWA,CAACV,KAAK,EAAE;MACjBA,KAAK,CAACI,KAAK,GAAG,IAAI;IACpB;EACF,CAAC;EACDO,OAAO,EAAE;IACPC,UAAUA,CAAC;MAAEC;IAAO,CAAC,EAAEV,OAAO,EAAE;MAC9BU,MAAM,CAAC,aAAa,EAAEV,OAAO,CAAC;IAChC,CAAC;IACDW,QAAQA,CAAC;MAAED;IAAO,CAAC,EAAET,KAAK,EAAE;MAC1BS,MAAM,CAAC,WAAW,EAAET,KAAK,CAAC;IAC5B,CAAC;IACDW,UAAUA,CAAC;MAAEF;IAAO,CAAC,EAAE;MACrBA,MAAM,CAAC,aAAa,CAAC;IACvB;EACF,CAAC;EACDG,OAAO,EAAE;IACPlB,IAAI;IACJC;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}