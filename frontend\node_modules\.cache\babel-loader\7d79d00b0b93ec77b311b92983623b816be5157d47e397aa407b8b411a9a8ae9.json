{"ast": null, "code": "import { ref, computed, onMounted } from \"vue\";\nimport { ElMessage } from \"element-plus\";\nexport default {\n  name: \"Dashboard\",\n  setup() {\n    const currentReservation = ref(null);\n    const creditScore = ref(100);\n    const creditScoreColor = computed(() => {\n      if (creditScore.value >= 90) return \"#67C23A\";\n      if (creditScore.value >= 70) return \"#E6A23C\";\n      return \"#F56C6C\";\n    });\n    const formatTime = timeString => {\n      if (!timeString) return \"\";\n      const date = new Date(timeString);\n      return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes()}`;\n    };\n    const fetchCurrentReservation = () => {\n      // 模拟获取当前预约数据\n      setTimeout(() => {\n        currentReservation.value = {\n          id: 1,\n          room_name: \"一号自习室\",\n          seat_number: \"A-12\",\n          start_time: \"2023-05-20T08:00:00\",\n          end_time: \"2023-05-20T12:00:00\",\n          status: \"active\"\n        };\n      }, 500);\n    };\n    const fetchCreditScore = () => {\n      // 模拟获取信誉分数据\n      setTimeout(() => {\n        creditScore.value = 95;\n      }, 500);\n    };\n    const checkIn = () => {\n      ElMessage.success(\"签到成功！\");\n    };\n    const cancelReservation = () => {\n      ElMessage.success(\"预约已取消！\");\n      currentReservation.value = null;\n    };\n    onMounted(() => {\n      fetchCurrentReservation();\n      fetchCreditScore();\n    });\n    return {\n      currentReservation,\n      creditScore,\n      creditScoreColor,\n      formatTime,\n      checkIn,\n      cancelReservation\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "ElMessage", "name", "setup", "currentReservation", "creditScore", "creditScoreColor", "value", "formatTime", "timeString", "date", "Date", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "fetchCurrentReservation", "setTimeout", "id", "room_name", "seat_number", "start_time", "end_time", "status", "fetchCreditScore", "checkIn", "success", "cancelReservation"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard\">\n    <h1>仪表盘</h1>\n    <div class=\"dashboard-content\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"8\">\n          <el-card class=\"box-card\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>当前预约</span>\n              </div>\n            </template>\n            <div class=\"card-content\">\n              <p v-if=\"!currentReservation\">暂无预约</p>\n              <div v-else>\n                <p>自习室: {{ currentReservation.room_name }}</p>\n                <p>座位号: {{ currentReservation.seat_number }}</p>\n                <p>开始时间: {{ formatTime(currentReservation.start_time) }}</p>\n                <p>结束时间: {{ formatTime(currentReservation.end_time) }}</p>\n                <el-button type=\"primary\" size=\"small\" @click=\"checkIn\">签到</el-button>\n                <el-button type=\"danger\" size=\"small\" @click=\"cancelReservation\">取消预约</el-button>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"8\">\n          <el-card class=\"box-card\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>信誉分</span>\n              </div>\n            </template>\n            <div class=\"card-content\">\n              <el-progress type=\"dashboard\" :percentage=\"creditScore\" :color=\"creditScoreColor\"></el-progress>\n              <p>当前信誉分: {{ creditScore }}</p>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"8\">\n          <el-card class=\"box-card\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>快速操作</span>\n              </div>\n            </template>\n            <div class=\"card-content\">\n              <el-button type=\"primary\" @click=\"$router.push('/seat/reservation')\">预约座位</el-button>\n              <el-button type=\"info\" @click=\"$router.push('/user/reservations')\">我的预约</el-button>\n              <el-button type=\"success\" @click=\"$router.push('/seat/map')\">座位地图</el-button>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from \"vue\";\nimport { ElMessage } from \"element-plus\";\n\nexport default {\n  name: \"Dashboard\",\n  setup() {\n    const currentReservation = ref(null);\n    const creditScore = ref(100);\n\n    const creditScoreColor = computed(() => {\n      if (creditScore.value >= 90) return \"#67C23A\";\n      if (creditScore.value >= 70) return \"#E6A23C\";\n      return \"#F56C6C\";\n    });\n\n    const formatTime = (timeString) => {\n      if (!timeString) return \"\";\n      const date = new Date(timeString);\n      return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes()}`;\n    };\n\n    const fetchCurrentReservation = () => {\n      // 模拟获取当前预约数据\n      setTimeout(() => {\n        currentReservation.value = {\n          id: 1,\n          room_name: \"一号自习室\",\n          seat_number: \"A-12\",\n          start_time: \"2023-05-20T08:00:00\",\n          end_time: \"2023-05-20T12:00:00\",\n          status: \"active\",\n        };\n      }, 500);\n    };\n\n    const fetchCreditScore = () => {\n      // 模拟获取信誉分数据\n      setTimeout(() => {\n        creditScore.value = 95;\n      }, 500);\n    };\n\n    const checkIn = () => {\n      ElMessage.success(\"签到成功！\");\n    };\n\n    const cancelReservation = () => {\n      ElMessage.success(\"预约已取消！\");\n      currentReservation.value = null;\n    };\n\n    onMounted(() => {\n      fetchCurrentReservation();\n      fetchCreditScore();\n    });\n\n    return {\n      currentReservation,\n      creditScore,\n      creditScoreColor,\n      formatTime,\n      checkIn,\n      cancelReservation,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard {\n  padding: 20px;\n\n  h1 {\n    margin-bottom: 20px;\n  }\n\n  .dashboard-content {\n    margin-top: 20px;\n  }\n\n  .box-card {\n    margin-bottom: 20px;\n  }\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n  }\n\n  .card-content {\n    min-height: 150px;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n\n    p {\n      margin: 5px 0;\n    }\n\n    .el-button {\n      margin: 5px;\n    }\n  }\n}\n</style>\n"], "mappings": "AA0DA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAK;AAC9C,SAASC,SAAQ,QAAS,cAAc;AAExC,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,KAAKA,CAAA,EAAG;IACN,MAAMC,kBAAiB,GAAIN,GAAG,CAAC,IAAI,CAAC;IACpC,MAAMO,WAAU,GAAIP,GAAG,CAAC,GAAG,CAAC;IAE5B,MAAMQ,gBAAe,GAAIP,QAAQ,CAAC,MAAM;MACtC,IAAIM,WAAW,CAACE,KAAI,IAAK,EAAE,EAAE,OAAO,SAAS;MAC7C,IAAIF,WAAW,CAACE,KAAI,IAAK,EAAE,EAAE,OAAO,SAAS;MAC7C,OAAO,SAAS;IAClB,CAAC,CAAC;IAEF,MAAMC,UAAS,GAAKC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAO,GAAGC,IAAI,CAACE,WAAW,CAAC,CAAC,IAAIF,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,IAAIH,IAAI,CAACI,OAAO,CAAC,CAAC,IAAIJ,IAAI,CAACK,QAAQ,CAAC,CAAC,IAAIL,IAAI,CAACM,UAAU,CAAC,CAAC,EAAE;IACjH,CAAC;IAED,MAAMC,uBAAsB,GAAIA,CAAA,KAAM;MACpC;MACAC,UAAU,CAAC,MAAM;QACfd,kBAAkB,CAACG,KAAI,GAAI;UACzBY,EAAE,EAAE,CAAC;UACLC,SAAS,EAAE,OAAO;UAClBC,WAAW,EAAE,MAAM;UACnBC,UAAU,EAAE,qBAAqB;UACjCC,QAAQ,EAAE,qBAAqB;UAC/BC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;IAED,MAAMC,gBAAe,GAAIA,CAAA,KAAM;MAC7B;MACAP,UAAU,CAAC,MAAM;QACfb,WAAW,CAACE,KAAI,GAAI,EAAE;MACxB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;IAED,MAAMmB,OAAM,GAAIA,CAAA,KAAM;MACpBzB,SAAS,CAAC0B,OAAO,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,MAAMC,iBAAgB,GAAIA,CAAA,KAAM;MAC9B3B,SAAS,CAAC0B,OAAO,CAAC,QAAQ,CAAC;MAC3BvB,kBAAkB,CAACG,KAAI,GAAI,IAAI;IACjC,CAAC;IAEDP,SAAS,CAAC,MAAM;MACdiB,uBAAuB,CAAC,CAAC;MACzBQ,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC;IAEF,OAAO;MACLrB,kBAAkB;MAClBC,WAAW;MACXC,gBAAgB;MAChBE,UAAU;MACVkB,OAAO;MACPE;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}