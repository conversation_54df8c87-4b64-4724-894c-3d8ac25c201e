{"ast": null, "code": "import { inject, ref, computed, nextTick, watch } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { useEventListener } from '@vueuse/core';\nimport { sliderContextKey } from '../constants.mjs';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../../constants/event.mjs';\nconst useTooltip = (props, formatTooltip, showTooltip) => {\n  const tooltip = ref();\n  const tooltipVisible = ref(false);\n  const enableFormat = computed(() => {\n    return formatTooltip.value instanceof Function;\n  });\n  const formatValue = computed(() => {\n    return enableFormat.value && formatTooltip.value(props.modelValue) || props.modelValue;\n  });\n  const displayTooltip = debounce(() => {\n    showTooltip.value && (tooltipVisible.value = true);\n  }, 50);\n  const hideTooltip = debounce(() => {\n    showTooltip.value && (tooltipVisible.value = false);\n  }, 50);\n  return {\n    tooltip,\n    tooltipVisible,\n    formatValue,\n    displayTooltip,\n    hideTooltip\n  };\n};\nconst useSliderButton = (props, initData, emit) => {\n  const {\n    disabled,\n    min,\n    max,\n    step,\n    showTooltip,\n    persistent,\n    precision,\n    sliderSize,\n    formatTooltip,\n    emitChange,\n    resetSize,\n    updateDragging\n  } = inject(sliderContextKey);\n  const {\n    tooltip,\n    tooltipVisible,\n    formatValue,\n    displayTooltip,\n    hideTooltip\n  } = useTooltip(props, formatTooltip, showTooltip);\n  const button = ref();\n  const currentPosition = computed(() => {\n    return `${(props.modelValue - min.value) / (max.value - min.value) * 100}%`;\n  });\n  const wrapperStyle = computed(() => {\n    return props.vertical ? {\n      bottom: currentPosition.value\n    } : {\n      left: currentPosition.value\n    };\n  });\n  const handleMouseEnter = () => {\n    initData.hovering = true;\n    displayTooltip();\n  };\n  const handleMouseLeave = () => {\n    initData.hovering = false;\n    if (!initData.dragging) {\n      hideTooltip();\n    }\n  };\n  const onButtonDown = event => {\n    if (disabled.value) return;\n    event.preventDefault();\n    onDragStart(event);\n    window.addEventListener(\"mousemove\", onDragging);\n    window.addEventListener(\"touchmove\", onDragging);\n    window.addEventListener(\"mouseup\", onDragEnd);\n    window.addEventListener(\"touchend\", onDragEnd);\n    window.addEventListener(\"contextmenu\", onDragEnd);\n    button.value.focus();\n  };\n  const incrementPosition = amount => {\n    if (disabled.value) return;\n    initData.newPosition = Number.parseFloat(currentPosition.value) + amount / (max.value - min.value) * 100;\n    setPosition(initData.newPosition);\n    emitChange();\n  };\n  const onLeftKeyDown = () => {\n    incrementPosition(-step.value);\n  };\n  const onRightKeyDown = () => {\n    incrementPosition(step.value);\n  };\n  const onPageDownKeyDown = () => {\n    incrementPosition(-step.value * 4);\n  };\n  const onPageUpKeyDown = () => {\n    incrementPosition(step.value * 4);\n  };\n  const onHomeKeyDown = () => {\n    if (disabled.value) return;\n    setPosition(0);\n    emitChange();\n  };\n  const onEndKeyDown = () => {\n    if (disabled.value) return;\n    setPosition(100);\n    emitChange();\n  };\n  const onKeyDown = event => {\n    let isPreventDefault = true;\n    switch (event.code) {\n      case EVENT_CODE.left:\n      case EVENT_CODE.down:\n        onLeftKeyDown();\n        break;\n      case EVENT_CODE.right:\n      case EVENT_CODE.up:\n        onRightKeyDown();\n        break;\n      case EVENT_CODE.home:\n        onHomeKeyDown();\n        break;\n      case EVENT_CODE.end:\n        onEndKeyDown();\n        break;\n      case EVENT_CODE.pageDown:\n        onPageDownKeyDown();\n        break;\n      case EVENT_CODE.pageUp:\n        onPageUpKeyDown();\n        break;\n      default:\n        isPreventDefault = false;\n        break;\n    }\n    isPreventDefault && event.preventDefault();\n  };\n  const getClientXY = event => {\n    let clientX;\n    let clientY;\n    if (event.type.startsWith(\"touch\")) {\n      clientY = event.touches[0].clientY;\n      clientX = event.touches[0].clientX;\n    } else {\n      clientY = event.clientY;\n      clientX = event.clientX;\n    }\n    return {\n      clientX,\n      clientY\n    };\n  };\n  const onDragStart = event => {\n    initData.dragging = true;\n    initData.isClick = true;\n    const {\n      clientX,\n      clientY\n    } = getClientXY(event);\n    if (props.vertical) {\n      initData.startY = clientY;\n    } else {\n      initData.startX = clientX;\n    }\n    initData.startPosition = Number.parseFloat(currentPosition.value);\n    initData.newPosition = initData.startPosition;\n  };\n  const onDragging = event => {\n    if (initData.dragging) {\n      initData.isClick = false;\n      displayTooltip();\n      resetSize();\n      let diff;\n      const {\n        clientX,\n        clientY\n      } = getClientXY(event);\n      if (props.vertical) {\n        initData.currentY = clientY;\n        diff = (initData.startY - initData.currentY) / sliderSize.value * 100;\n      } else {\n        initData.currentX = clientX;\n        diff = (initData.currentX - initData.startX) / sliderSize.value * 100;\n      }\n      initData.newPosition = initData.startPosition + diff;\n      setPosition(initData.newPosition);\n    }\n  };\n  const onDragEnd = () => {\n    if (initData.dragging) {\n      setTimeout(() => {\n        initData.dragging = false;\n        if (!initData.hovering) {\n          hideTooltip();\n        }\n        if (!initData.isClick) {\n          setPosition(initData.newPosition);\n        }\n        emitChange();\n      }, 0);\n      window.removeEventListener(\"mousemove\", onDragging);\n      window.removeEventListener(\"touchmove\", onDragging);\n      window.removeEventListener(\"mouseup\", onDragEnd);\n      window.removeEventListener(\"touchend\", onDragEnd);\n      window.removeEventListener(\"contextmenu\", onDragEnd);\n    }\n  };\n  const setPosition = async newPosition => {\n    if (newPosition === null || Number.isNaN(+newPosition)) return;\n    if (newPosition < 0) {\n      newPosition = 0;\n    } else if (newPosition > 100) {\n      newPosition = 100;\n    }\n    const lengthPerStep = 100 / ((max.value - min.value) / step.value);\n    const steps = Math.round(newPosition / lengthPerStep);\n    let value = steps * lengthPerStep * (max.value - min.value) * 0.01 + min.value;\n    value = Number.parseFloat(value.toFixed(precision.value));\n    if (value !== props.modelValue) {\n      emit(UPDATE_MODEL_EVENT, value);\n    }\n    if (!initData.dragging && props.modelValue !== initData.oldValue) {\n      initData.oldValue = props.modelValue;\n    }\n    await nextTick();\n    initData.dragging && displayTooltip();\n    tooltip.value.updatePopper();\n  };\n  watch(() => initData.dragging, val => {\n    updateDragging(val);\n  });\n  useEventListener(button, \"touchstart\", onButtonDown, {\n    passive: false\n  });\n  return {\n    disabled,\n    button,\n    tooltip,\n    tooltipVisible,\n    showTooltip,\n    persistent,\n    wrapperStyle,\n    formatValue,\n    handleMouseEnter,\n    handleMouseLeave,\n    onButtonDown,\n    onKeyDown,\n    setPosition\n  };\n};\nexport { useSliderButton };", "map": {"version": 3, "names": ["useTooltip", "props", "formatTooltip", "showTooltip", "tooltip", "ref", "tooltipVisible", "enableFormat", "computed", "value", "Function", "formatValue", "modelValue", "displayTooltip", "debounce", "hideTooltip", "useSliderButton", "initData", "emit", "disabled", "min", "max", "step", "persistent", "precision", "sliderSize", "emitChange", "resetSize", "updateDragging", "inject", "slider<PERSON><PERSON>xt<PERSON><PERSON>", "button", "currentPosition", "wrapperStyle", "vertical", "bottom", "left", "handleMouseEnter", "hovering", "handleMouseLeave", "dragging", "onButtonDown", "event", "preventDefault", "onDragStart", "window", "addEventListener", "onDragging", "onDragEnd", "focus", "incrementPosition", "amount", "newPosition", "Number", "parseFloat", "setPosition", "onLeftKeyDown", "onRightKeyDown", "onPageDownKeyDown", "onPageUpKeyDown", "onHomeKeyDown", "onEndKeyDown", "onKeyDown", "isPreventDefault", "code", "EVENT_CODE", "down", "right", "up", "home", "end", "pageDown", "pageUp", "getClientXY", "clientX", "clientY", "type", "startsWith", "touches", "isClick", "startY", "startX", "startPosition", "diff", "currentY", "currentX", "setTimeout", "removeEventListener", "isNaN", "lengthPerStep", "steps", "Math", "round", "toFixed", "UPDATE_MODEL_EVENT", "oldValue", "nextTick", "updatePopper", "watch", "val", "useEventListener", "passive"], "sources": ["../../../../../../../packages/components/slider/src/composables/use-slider-button.ts"], "sourcesContent": ["import { computed, inject, nextTick, ref, watch } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { useEventListener } from '@vueuse/core'\nimport { EVENT_CODE, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { sliderContextKey } from '../constants'\n\nimport type { CSSProperties, ComputedRef, Ref, SetupContext } from 'vue'\nimport type { SliderProps } from '../slider'\nimport type {\n  SliderButtonEmits,\n  SliderButtonInitData,\n  SliderButtonProps,\n} from '../button'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\nconst useTooltip = (\n  props: SliderButtonProps,\n  formatTooltip: Ref<SliderProps['formatTooltip']>,\n  showTooltip: Ref<SliderProps['showTooltip']>\n) => {\n  const tooltip = ref<TooltipInstance>()\n\n  const tooltipVisible = ref(false)\n\n  const enableFormat = computed(() => {\n    return formatTooltip.value instanceof Function\n  })\n\n  const formatValue = computed(() => {\n    return (\n      (enableFormat.value && formatTooltip.value!(props.modelValue)) ||\n      props.modelValue\n    )\n  })\n\n  const displayTooltip = debounce(() => {\n    showTooltip.value && (tooltipVisible.value = true)\n  }, 50)\n\n  const hideTooltip = debounce(() => {\n    showTooltip.value && (tooltipVisible.value = false)\n  }, 50)\n\n  return {\n    tooltip,\n    tooltipVisible,\n    formatValue,\n    displayTooltip,\n    hideTooltip,\n  }\n}\n\nexport const useSliderButton = (\n  props: SliderButtonProps,\n  initData: SliderButtonInitData,\n  emit: SetupContext<SliderButtonEmits>['emit']\n) => {\n  const {\n    disabled,\n    min,\n    max,\n    step,\n    showTooltip,\n    persistent,\n    precision,\n    sliderSize,\n    formatTooltip,\n    emitChange,\n    resetSize,\n    updateDragging,\n  } = inject(sliderContextKey)!\n\n  const { tooltip, tooltipVisible, formatValue, displayTooltip, hideTooltip } =\n    useTooltip(props, formatTooltip!, showTooltip)\n\n  const button = ref<HTMLDivElement>()\n\n  const currentPosition = computed(() => {\n    return `${\n      ((props.modelValue - min.value) / (max.value - min.value)) * 100\n    }%`\n  })\n\n  const wrapperStyle: ComputedRef<CSSProperties> = computed(() => {\n    return props.vertical\n      ? { bottom: currentPosition.value }\n      : { left: currentPosition.value }\n  })\n\n  const handleMouseEnter = () => {\n    initData.hovering = true\n    displayTooltip()\n  }\n\n  const handleMouseLeave = () => {\n    initData.hovering = false\n    if (!initData.dragging) {\n      hideTooltip()\n    }\n  }\n\n  const onButtonDown = (event: MouseEvent | TouchEvent) => {\n    if (disabled.value) return\n    event.preventDefault()\n    onDragStart(event)\n    window.addEventListener('mousemove', onDragging)\n    window.addEventListener('touchmove', onDragging)\n    window.addEventListener('mouseup', onDragEnd)\n    window.addEventListener('touchend', onDragEnd)\n    window.addEventListener('contextmenu', onDragEnd)\n    button.value!.focus()\n  }\n\n  const incrementPosition = (amount: number) => {\n    if (disabled.value) return\n    initData.newPosition =\n      Number.parseFloat(currentPosition.value) +\n      (amount / (max.value - min.value)) * 100\n    setPosition(initData.newPosition)\n    emitChange()\n  }\n\n  const onLeftKeyDown = () => {\n    incrementPosition(-step.value)\n  }\n\n  const onRightKeyDown = () => {\n    incrementPosition(step.value)\n  }\n\n  const onPageDownKeyDown = () => {\n    incrementPosition(-step.value * 4)\n  }\n\n  const onPageUpKeyDown = () => {\n    incrementPosition(step.value * 4)\n  }\n\n  const onHomeKeyDown = () => {\n    if (disabled.value) return\n    setPosition(0)\n    emitChange()\n  }\n\n  const onEndKeyDown = () => {\n    if (disabled.value) return\n    setPosition(100)\n    emitChange()\n  }\n\n  const onKeyDown = (event: KeyboardEvent) => {\n    let isPreventDefault = true\n\n    switch (event.code) {\n      case EVENT_CODE.left:\n      case EVENT_CODE.down:\n        onLeftKeyDown()\n        break\n      case EVENT_CODE.right:\n      case EVENT_CODE.up:\n        onRightKeyDown()\n        break\n      case EVENT_CODE.home:\n        onHomeKeyDown()\n        break\n      case EVENT_CODE.end:\n        onEndKeyDown()\n        break\n      case EVENT_CODE.pageDown:\n        onPageDownKeyDown()\n        break\n      case EVENT_CODE.pageUp:\n        onPageUpKeyDown()\n        break\n      default:\n        isPreventDefault = false\n        break\n    }\n\n    isPreventDefault && event.preventDefault()\n  }\n\n  const getClientXY = (event: MouseEvent | TouchEvent) => {\n    let clientX: number\n    let clientY: number\n    if (event.type.startsWith('touch')) {\n      clientY = (event as TouchEvent).touches[0].clientY\n      clientX = (event as TouchEvent).touches[0].clientX\n    } else {\n      clientY = (event as MouseEvent).clientY\n      clientX = (event as MouseEvent).clientX\n    }\n    return {\n      clientX,\n      clientY,\n    }\n  }\n\n  const onDragStart = (event: MouseEvent | TouchEvent) => {\n    initData.dragging = true\n    initData.isClick = true\n    const { clientX, clientY } = getClientXY(event)\n    if (props.vertical) {\n      initData.startY = clientY\n    } else {\n      initData.startX = clientX\n    }\n    initData.startPosition = Number.parseFloat(currentPosition.value)\n    initData.newPosition = initData.startPosition\n  }\n\n  const onDragging = (event: MouseEvent | TouchEvent) => {\n    if (initData.dragging) {\n      initData.isClick = false\n      displayTooltip()\n      resetSize()\n      let diff: number\n      const { clientX, clientY } = getClientXY(event)\n      if (props.vertical) {\n        initData.currentY = clientY\n        diff = ((initData.startY - initData.currentY) / sliderSize.value) * 100\n      } else {\n        initData.currentX = clientX\n        diff = ((initData.currentX - initData.startX) / sliderSize.value) * 100\n      }\n      initData.newPosition = initData.startPosition + diff\n      setPosition(initData.newPosition)\n    }\n  }\n\n  const onDragEnd = () => {\n    if (initData.dragging) {\n      /*\n       * 防止在 mouseup 后立即触发 click，导致滑块有几率产生一小段位移\n       * 不使用 preventDefault 是因为 mouseup 和 click 没有注册在同一个 DOM 上\n       */\n      setTimeout(() => {\n        initData.dragging = false\n        if (!initData.hovering) {\n          hideTooltip()\n        }\n        if (!initData.isClick) {\n          setPosition(initData.newPosition)\n        }\n        emitChange()\n      }, 0)\n      window.removeEventListener('mousemove', onDragging)\n      window.removeEventListener('touchmove', onDragging)\n      window.removeEventListener('mouseup', onDragEnd)\n      window.removeEventListener('touchend', onDragEnd)\n      window.removeEventListener('contextmenu', onDragEnd)\n    }\n  }\n\n  const setPosition = async (newPosition: number) => {\n    if (newPosition === null || Number.isNaN(+newPosition)) return\n    if (newPosition < 0) {\n      newPosition = 0\n    } else if (newPosition > 100) {\n      newPosition = 100\n    }\n    const lengthPerStep = 100 / ((max.value - min.value) / step.value)\n    const steps = Math.round(newPosition / lengthPerStep)\n    let value =\n      steps * lengthPerStep * (max.value - min.value) * 0.01 + min.value\n    value = Number.parseFloat(value.toFixed(precision.value))\n\n    if (value !== props.modelValue) {\n      emit(UPDATE_MODEL_EVENT, value)\n    }\n\n    if (!initData.dragging && props.modelValue !== initData.oldValue) {\n      initData.oldValue = props.modelValue\n    }\n\n    await nextTick()\n    initData.dragging && displayTooltip()\n    tooltip.value!.updatePopper()\n  }\n\n  watch(\n    () => initData.dragging,\n    (val) => {\n      updateDragging(val)\n    }\n  )\n\n  useEventListener(button, 'touchstart', onButtonDown, { passive: false })\n\n  return {\n    disabled,\n    button,\n    tooltip,\n    tooltipVisible,\n    showTooltip,\n    persistent,\n    wrapperStyle,\n    formatValue,\n    handleMouseEnter,\n    handleMouseLeave,\n    onButtonDown,\n    onKeyDown,\n    setPosition,\n  }\n}\n"], "mappings": ";;;;;;AAKA,MAAMA,UAAU,GAAGA,CAACC,KAAK,EAAEC,aAAa,EAAEC,WAAW,KAAK;EACxD,MAAMC,OAAO,GAAGC,GAAG,EAAE;EACrB,MAAMC,cAAc,GAAGD,GAAG,CAAC,KAAK,CAAC;EACjC,MAAME,YAAY,GAAGC,QAAQ,CAAC,MAAM;IAClC,OAAON,aAAa,CAACO,KAAK,YAAYC,QAAQ;EAClD,CAAG,CAAC;EACF,MAAMC,WAAW,GAAGH,QAAQ,CAAC,MAAM;IACjC,OAAOD,YAAY,CAACE,KAAK,IAAIP,aAAa,CAACO,KAAK,CAACR,KAAK,CAACW,UAAU,CAAC,IAAIX,KAAK,CAACW,UAAU;EAC1F,CAAG,CAAC;EACF,MAAMC,cAAc,GAAGC,QAAQ,CAAC,MAAM;IACpCX,WAAW,CAACM,KAAK,KAAKH,cAAc,CAACG,KAAK,GAAG,IAAI,CAAC;EACtD,CAAG,EAAE,EAAE,CAAC;EACN,MAAMM,WAAW,GAAGD,QAAQ,CAAC,MAAM;IACjCX,WAAW,CAACM,KAAK,KAAKH,cAAc,CAACG,KAAK,GAAG,KAAK,CAAC;EACvD,CAAG,EAAE,EAAE,CAAC;EACN,OAAO;IACLL,OAAO;IACPE,cAAc;IACdK,WAAW;IACXE,cAAc;IACdE;EACJ,CAAG;AACH,CAAC;AACW,MAACC,eAAe,GAAGA,CAACf,KAAK,EAAEgB,QAAQ,EAAEC,IAAI,KAAK;EACxD,MAAM;IACJC,QAAQ;IACRC,GAAG;IACHC,GAAG;IACHC,IAAI;IACJnB,WAAW;IACXoB,UAAU;IACVC,SAAS;IACTC,UAAU;IACVvB,aAAa;IACbwB,UAAU;IACVC,SAAS;IACTC;EACJ,CAAG,GAAGC,MAAM,CAACC,gBAAgB,CAAC;EAC5B,MAAM;IAAE1B,OAAO;IAAEE,cAAc;IAAEK,WAAW;IAAEE,cAAc;IAAEE;EAAW,CAAE,GAAGf,UAAU,CAACC,KAAK,EAAEC,aAAa,EAAEC,WAAW,CAAC;EAC3H,MAAM4B,MAAM,GAAG1B,GAAG,EAAE;EACpB,MAAM2B,eAAe,GAAGxB,QAAQ,CAAC,MAAM;IACrC,OAAO,GAAG,CAACP,KAAK,CAACW,UAAU,GAAGQ,GAAG,CAACX,KAAK,KAAKY,GAAG,CAACZ,KAAK,GAAGW,GAAG,CAACX,KAAK,CAAC,GAAG,GAAG,GAAG;EAC/E,CAAG,CAAC;EACF,MAAMwB,YAAY,GAAGzB,QAAQ,CAAC,MAAM;IAClC,OAAOP,KAAK,CAACiC,QAAQ,GAAG;MAAEC,MAAM,EAAEH,eAAe,CAACvB;IAAK,CAAE,GAAG;MAAE2B,IAAI,EAAEJ,eAAe,CAACvB;IAAK,CAAE;EAC/F,CAAG,CAAC;EACF,MAAM4B,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpB,QAAQ,CAACqB,QAAQ,GAAG,IAAI;IACxBzB,cAAc,EAAE;EACpB,CAAG;EACD,MAAM0B,gBAAgB,GAAGA,CAAA,KAAM;IAC7BtB,QAAQ,CAACqB,QAAQ,GAAG,KAAK;IACzB,IAAI,CAACrB,QAAQ,CAACuB,QAAQ,EAAE;MACtBzB,WAAW,EAAE;IACnB;EACA,CAAG;EACD,MAAM0B,YAAY,GAAIC,KAAK,IAAK;IAC9B,IAAIvB,QAAQ,CAACV,KAAK,EAChB;IACFiC,KAAK,CAACC,cAAc,EAAE;IACtBC,WAAW,CAACF,KAAK,CAAC;IAClBG,MAAM,CAACC,gBAAgB,CAAC,WAAW,EAAEC,UAAU,CAAC;IAChDF,MAAM,CAACC,gBAAgB,CAAC,WAAW,EAAEC,UAAU,CAAC;IAChDF,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEE,SAAS,CAAC;IAC7CH,MAAM,CAACC,gBAAgB,CAAC,UAAU,EAAEE,SAAS,CAAC;IAC9CH,MAAM,CAACC,gBAAgB,CAAC,aAAa,EAAEE,SAAS,CAAC;IACjDjB,MAAM,CAACtB,KAAK,CAACwC,KAAK,EAAE;EACxB,CAAG;EACD,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;IACpC,IAAIhC,QAAQ,CAACV,KAAK,EAChB;IACFQ,QAAQ,CAACmC,WAAW,GAAGC,MAAM,CAACC,UAAU,CAACtB,eAAe,CAACvB,KAAK,CAAC,GAAG0C,MAAM,IAAI9B,GAAG,CAACZ,KAAK,GAAGW,GAAG,CAACX,KAAK,CAAC,GAAG,GAAG;IACxG8C,WAAW,CAACtC,QAAQ,CAACmC,WAAW,CAAC;IACjC1B,UAAU,EAAE;EAChB,CAAG;EACD,MAAM8B,aAAa,GAAGA,CAAA,KAAM;IAC1BN,iBAAiB,CAAC,CAAC5B,IAAI,CAACb,KAAK,CAAC;EAClC,CAAG;EACD,MAAMgD,cAAc,GAAGA,CAAA,KAAM;IAC3BP,iBAAiB,CAAC5B,IAAI,CAACb,KAAK,CAAC;EACjC,CAAG;EACD,MAAMiD,iBAAiB,GAAGA,CAAA,KAAM;IAC9BR,iBAAiB,CAAC,CAAC5B,IAAI,CAACb,KAAK,GAAG,CAAC,CAAC;EACtC,CAAG;EACD,MAAMkD,eAAe,GAAGA,CAAA,KAAM;IAC5BT,iBAAiB,CAAC5B,IAAI,CAACb,KAAK,GAAG,CAAC,CAAC;EACrC,CAAG;EACD,MAAMmD,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIzC,QAAQ,CAACV,KAAK,EAChB;IACF8C,WAAW,CAAC,CAAC,CAAC;IACd7B,UAAU,EAAE;EAChB,CAAG;EACD,MAAMmC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI1C,QAAQ,CAACV,KAAK,EAChB;IACF8C,WAAW,CAAC,GAAG,CAAC;IAChB7B,UAAU,EAAE;EAChB,CAAG;EACD,MAAMoC,SAAS,GAAIpB,KAAK,IAAK;IAC3B,IAAIqB,gBAAgB,GAAG,IAAI;IAC3B,QAAQrB,KAAK,CAACsB,IAAI;MAChB,KAAKC,UAAU,CAAC7B,IAAI;MACpB,KAAK6B,UAAU,CAACC,IAAI;QAClBV,aAAa,EAAE;QACf;MACF,KAAKS,UAAU,CAACE,KAAK;MACrB,KAAKF,UAAU,CAACG,EAAE;QAChBX,cAAc,EAAE;QAChB;MACF,KAAKQ,UAAU,CAACI,IAAI;QAClBT,aAAa,EAAE;QACf;MACF,KAAKK,UAAU,CAACK,GAAG;QACjBT,YAAY,EAAE;QACd;MACF,KAAKI,UAAU,CAACM,QAAQ;QACtBb,iBAAiB,EAAE;QACnB;MACF,KAAKO,UAAU,CAACO,MAAM;QACpBb,eAAe,EAAE;QACjB;MACF;QACEI,gBAAgB,GAAG,KAAK;QACxB;IACR;IACIA,gBAAgB,IAAIrB,KAAK,CAACC,cAAc,EAAE;EAC9C,CAAG;EACD,MAAM8B,WAAW,GAAI/B,KAAK,IAAK;IAC7B,IAAIgC,OAAO;IACX,IAAIC,OAAO;IACX,IAAIjC,KAAK,CAACkC,IAAI,CAACC,UAAU,CAAC,OAAO,CAAC,EAAE;MAClCF,OAAO,GAAGjC,KAAK,CAACoC,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO;MAClCD,OAAO,GAAGhC,KAAK,CAACoC,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IACxC,CAAK,MAAM;MACLC,OAAO,GAAGjC,KAAK,CAACiC,OAAO;MACvBD,OAAO,GAAGhC,KAAK,CAACgC,OAAO;IAC7B;IACI,OAAO;MACLA,OAAO;MACPC;IACN,CAAK;EACL,CAAG;EACD,MAAM/B,WAAW,GAAIF,KAAK,IAAK;IAC7BzB,QAAQ,CAACuB,QAAQ,GAAG,IAAI;IACxBvB,QAAQ,CAAC8D,OAAO,GAAG,IAAI;IACvB,MAAM;MAAEL,OAAO;MAAEC;IAAO,CAAE,GAAGF,WAAW,CAAC/B,KAAK,CAAC;IAC/C,IAAIzC,KAAK,CAACiC,QAAQ,EAAE;MAClBjB,QAAQ,CAAC+D,MAAM,GAAGL,OAAO;IAC/B,CAAK,MAAM;MACL1D,QAAQ,CAACgE,MAAM,GAAGP,OAAO;IAC/B;IACIzD,QAAQ,CAACiE,aAAa,GAAG7B,MAAM,CAACC,UAAU,CAACtB,eAAe,CAACvB,KAAK,CAAC;IACjEQ,QAAQ,CAACmC,WAAW,GAAGnC,QAAQ,CAACiE,aAAa;EACjD,CAAG;EACD,MAAMnC,UAAU,GAAIL,KAAK,IAAK;IAC5B,IAAIzB,QAAQ,CAACuB,QAAQ,EAAE;MACrBvB,QAAQ,CAAC8D,OAAO,GAAG,KAAK;MACxBlE,cAAc,EAAE;MAChBc,SAAS,EAAE;MACX,IAAIwD,IAAI;MACR,MAAM;QAAET,OAAO;QAAEC;MAAO,CAAE,GAAGF,WAAW,CAAC/B,KAAK,CAAC;MAC/C,IAAIzC,KAAK,CAACiC,QAAQ,EAAE;QAClBjB,QAAQ,CAACmE,QAAQ,GAAGT,OAAO;QAC3BQ,IAAI,GAAG,CAAClE,QAAQ,CAAC+D,MAAM,GAAG/D,QAAQ,CAACmE,QAAQ,IAAI3D,UAAU,CAAChB,KAAK,GAAG,GAAG;MAC7E,CAAO,MAAM;QACLQ,QAAQ,CAACoE,QAAQ,GAAGX,OAAO;QAC3BS,IAAI,GAAG,CAAClE,QAAQ,CAACoE,QAAQ,GAAGpE,QAAQ,CAACgE,MAAM,IAAIxD,UAAU,CAAChB,KAAK,GAAG,GAAG;MAC7E;MACMQ,QAAQ,CAACmC,WAAW,GAAGnC,QAAQ,CAACiE,aAAa,GAAGC,IAAI;MACpD5B,WAAW,CAACtC,QAAQ,CAACmC,WAAW,CAAC;IACvC;EACA,CAAG;EACD,MAAMJ,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI/B,QAAQ,CAACuB,QAAQ,EAAE;MACrB8C,UAAU,CAAC,MAAM;QACfrE,QAAQ,CAACuB,QAAQ,GAAG,KAAK;QACzB,IAAI,CAACvB,QAAQ,CAACqB,QAAQ,EAAE;UACtBvB,WAAW,EAAE;QACvB;QACQ,IAAI,CAACE,QAAQ,CAAC8D,OAAO,EAAE;UACrBxB,WAAW,CAACtC,QAAQ,CAACmC,WAAW,CAAC;QAC3C;QACQ1B,UAAU,EAAE;MACpB,CAAO,EAAE,CAAC,CAAC;MACLmB,MAAM,CAAC0C,mBAAmB,CAAC,WAAW,EAAExC,UAAU,CAAC;MACnDF,MAAM,CAAC0C,mBAAmB,CAAC,WAAW,EAAExC,UAAU,CAAC;MACnDF,MAAM,CAAC0C,mBAAmB,CAAC,SAAS,EAAEvC,SAAS,CAAC;MAChDH,MAAM,CAAC0C,mBAAmB,CAAC,UAAU,EAAEvC,SAAS,CAAC;MACjDH,MAAM,CAAC0C,mBAAmB,CAAC,aAAa,EAAEvC,SAAS,CAAC;IAC1D;EACA,CAAG;EACD,MAAMO,WAAW,GAAG,MAAOH,WAAW,IAAK;IACzC,IAAIA,WAAW,KAAK,IAAI,IAAIC,MAAM,CAACmC,KAAK,CAAC,CAACpC,WAAW,CAAC,EACpD;IACF,IAAIA,WAAW,GAAG,CAAC,EAAE;MACnBA,WAAW,GAAG,CAAC;IACrB,CAAK,MAAM,IAAIA,WAAW,GAAG,GAAG,EAAE;MAC5BA,WAAW,GAAG,GAAG;IACvB;IACI,MAAMqC,aAAa,GAAG,GAAG,IAAI,CAACpE,GAAG,CAACZ,KAAK,GAAGW,GAAG,CAACX,KAAK,IAAIa,IAAI,CAACb,KAAK,CAAC;IAClE,MAAMiF,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACxC,WAAW,GAAGqC,aAAa,CAAC;IACrD,IAAIhF,KAAK,GAAGiF,KAAK,GAAGD,aAAa,IAAIpE,GAAG,CAACZ,KAAK,GAAGW,GAAG,CAACX,KAAK,CAAC,GAAG,IAAI,GAAGW,GAAG,CAACX,KAAK;IAC9EA,KAAK,GAAG4C,MAAM,CAACC,UAAU,CAAC7C,KAAK,CAACoF,OAAO,CAACrE,SAAS,CAACf,KAAK,CAAC,CAAC;IACzD,IAAIA,KAAK,KAAKR,KAAK,CAACW,UAAU,EAAE;MAC9BM,IAAI,CAAC4E,kBAAkB,EAAErF,KAAK,CAAC;IACrC;IACI,IAAI,CAACQ,QAAQ,CAACuB,QAAQ,IAAIvC,KAAK,CAACW,UAAU,KAAKK,QAAQ,CAAC8E,QAAQ,EAAE;MAChE9E,QAAQ,CAAC8E,QAAQ,GAAG9F,KAAK,CAACW,UAAU;IAC1C;IACI,MAAMoF,QAAQ,EAAE;IAChB/E,QAAQ,CAACuB,QAAQ,IAAI3B,cAAc,EAAE;IACrCT,OAAO,CAACK,KAAK,CAACwF,YAAY,EAAE;EAChC,CAAG;EACDC,KAAK,CAAC,MAAMjF,QAAQ,CAACuB,QAAQ,EAAG2D,GAAG,IAAK;IACtCvE,cAAc,CAACuE,GAAG,CAAC;EACvB,CAAG,CAAC;EACFC,gBAAgB,CAACrE,MAAM,EAAE,YAAY,EAAEU,YAAY,EAAE;IAAE4D,OAAO,EAAE;EAAK,CAAE,CAAC;EACxE,OAAO;IACLlF,QAAQ;IACRY,MAAM;IACN3B,OAAO;IACPE,cAAc;IACdH,WAAW;IACXoB,UAAU;IACVU,YAAY;IACZtB,WAAW;IACX0B,gBAAgB;IAChBE,gBAAgB;IAChBE,YAAY;IACZqB,SAAS;IACTP;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}