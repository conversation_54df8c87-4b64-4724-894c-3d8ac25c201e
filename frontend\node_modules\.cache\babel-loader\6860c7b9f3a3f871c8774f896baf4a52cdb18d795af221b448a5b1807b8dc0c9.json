{"ast": null, "code": "import { isClient } from '@vueuse/core';\nconst isInContainer = (el, container) => {\n  if (!isClient || !el || !container) return false;\n  const elRect = el.getBoundingClientRect();\n  let containerRect;\n  if (container instanceof Element) {\n    containerRect = container.getBoundingClientRect();\n  } else {\n    containerRect = {\n      top: 0,\n      right: window.innerWidth,\n      bottom: window.innerHeight,\n      left: 0\n    };\n  }\n  return elRect.top < containerRect.bottom && elRect.bottom > containerRect.top && elRect.right > containerRect.left && elRect.left < containerRect.right;\n};\nconst getOffsetTop = el => {\n  let offset = 0;\n  let parent = el;\n  while (parent) {\n    offset += parent.offsetTop;\n    parent = parent.offsetParent;\n  }\n  return offset;\n};\nconst getOffsetTopDistance = (el, containerEl) => {\n  return Math.abs(getOffsetTop(el) - getOffsetTop(containerEl));\n};\nconst getClientXY = event => {\n  let clientX;\n  let clientY;\n  if (event.type === \"touchend\") {\n    clientY = event.changedTouches[0].clientY;\n    clientX = event.changedTouches[0].clientX;\n  } else if (event.type.startsWith(\"touch\")) {\n    clientY = event.touches[0].clientY;\n    clientX = event.touches[0].clientX;\n  } else {\n    clientY = event.clientY;\n    clientX = event.clientX;\n  }\n  return {\n    clientX,\n    clientY\n  };\n};\nexport { getClientXY, getOffsetTop, getOffsetTopDistance, isInContainer };", "map": {"version": 3, "names": ["isInContainer", "el", "container", "isClient", "elRect", "getBoundingClientRect", "containerRect", "Element", "top", "right", "window", "innerWidth", "bottom", "innerHeight", "left", "getOffsetTop", "offset", "parent", "offsetTop", "offsetParent", "getOffsetTopDistance", "containerEl", "Math", "abs", "getClientXY", "event", "clientX", "clientY", "type", "changedTouches", "startsWith", "touches"], "sources": ["../../../../../packages/utils/dom/position.ts"], "sourcesContent": ["import { isClient } from '../browser'\n\nexport const isInContainer = (\n  el?: Element,\n  container?: Element | Window\n): boolean => {\n  if (!isClient || !el || !container) return false\n\n  const elRect = el.getBoundingClientRect()\n\n  let containerRect: Pick<DOMRect, 'top' | 'bottom' | 'left' | 'right'>\n  if (container instanceof Element) {\n    containerRect = container.getBoundingClientRect()\n  } else {\n    containerRect = {\n      top: 0,\n      right: window.innerWidth,\n      bottom: window.innerHeight,\n      left: 0,\n    }\n  }\n  return (\n    elRect.top < containerRect.bottom &&\n    elRect.bottom > containerRect.top &&\n    elRect.right > containerRect.left &&\n    elRect.left < containerRect.right\n  )\n}\n\nexport const getOffsetTop = (el: HTMLElement) => {\n  let offset = 0\n  let parent = el\n\n  while (parent) {\n    offset += parent.offsetTop\n    parent = parent.offsetParent as HTMLElement\n  }\n\n  return offset\n}\n\nexport const getOffsetTopDistance = (\n  el: HTMLElement,\n  containerEl: HTMLElement\n) => {\n  return Math.abs(getOffsetTop(el) - getOffsetTop(containerEl))\n}\n\nexport const getClientXY = (event: MouseEvent | TouchEvent) => {\n  let clientX: number\n  let clientY: number\n  if (event.type === 'touchend') {\n    clientY = (event as TouchEvent).changedTouches[0].clientY\n    clientX = (event as TouchEvent).changedTouches[0].clientX\n  } else if (event.type.startsWith('touch')) {\n    clientY = (event as TouchEvent).touches[0].clientY\n    clientX = (event as TouchEvent).touches[0].clientX\n  } else {\n    clientY = (event as MouseEvent).clientY\n    clientX = (event as MouseEvent).clientX\n  }\n  return {\n    clientX,\n    clientY,\n  }\n}\n"], "mappings": ";AACY,MAACA,aAAa,GAAGA,CAACC,EAAE,EAAEC,SAAS,KAAK;EAC9C,IAAI,CAACC,QAAQ,IAAI,CAACF,EAAE,IAAI,CAACC,SAAS,EAChC,OAAO,KAAK;EACd,MAAME,MAAM,GAAGH,EAAE,CAACI,qBAAqB,EAAE;EACzC,IAAIC,aAAa;EACjB,IAAIJ,SAAS,YAAYK,OAAO,EAAE;IAChCD,aAAa,GAAGJ,SAAS,CAACG,qBAAqB,EAAE;EACrD,CAAG,MAAM;IACLC,aAAa,GAAG;MACdE,GAAG,EAAE,CAAC;MACNC,KAAK,EAAEC,MAAM,CAACC,UAAU;MACxBC,MAAM,EAAEF,MAAM,CAACG,WAAW;MAC1BC,IAAI,EAAE;IACZ,CAAK;EACL;EACE,OAAOV,MAAM,CAACI,GAAG,GAAGF,aAAa,CAACM,MAAM,IAAIR,MAAM,CAACQ,MAAM,GAAGN,aAAa,CAACE,GAAG,IAAIJ,MAAM,CAACK,KAAK,GAAGH,aAAa,CAACQ,IAAI,IAAIV,MAAM,CAACU,IAAI,GAAGR,aAAa,CAACG,KAAK;AACzJ;AACY,MAACM,YAAY,GAAId,EAAE,IAAK;EAClC,IAAIe,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAGhB,EAAE;EACf,OAAOgB,MAAM,EAAE;IACbD,MAAM,IAAIC,MAAM,CAACC,SAAS;IAC1BD,MAAM,GAAGA,MAAM,CAACE,YAAY;EAChC;EACE,OAAOH,MAAM;AACf;AACY,MAACI,oBAAoB,GAAGA,CAACnB,EAAE,EAAEoB,WAAW,KAAK;EACvD,OAAOC,IAAI,CAACC,GAAG,CAACR,YAAY,CAACd,EAAE,CAAC,GAAGc,YAAY,CAACM,WAAW,CAAC,CAAC;AAC/D;AACY,MAACG,WAAW,GAAIC,KAAK,IAAK;EACpC,IAAIC,OAAO;EACX,IAAIC,OAAO;EACX,IAAIF,KAAK,CAACG,IAAI,KAAK,UAAU,EAAE;IAC7BD,OAAO,GAAGF,KAAK,CAACI,cAAc,CAAC,CAAC,CAAC,CAACF,OAAO;IACzCD,OAAO,GAAGD,KAAK,CAACI,cAAc,CAAC,CAAC,CAAC,CAACH,OAAO;EAC7C,CAAG,MAAM,IAAID,KAAK,CAACG,IAAI,CAACE,UAAU,CAAC,OAAO,CAAC,EAAE;IACzCH,OAAO,GAAGF,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAClCD,OAAO,GAAGD,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACL,OAAO;EACtC,CAAG,MAAM;IACLC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACvBD,OAAO,GAAGD,KAAK,CAACC,OAAO;EAC3B;EACE,OAAO;IACLA,OAAO;IACPC;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}