{"ast": null, "code": "import { shallowRef, ref, onMounted } from 'vue';\nimport { useThrottleFn, useEventListener } from '@vueuse/core';\nimport { throwError } from '../../../utils/error.mjs';\nconst useBackTop = (props, emit, componentName) => {\n  const el = shallowRef();\n  const container = shallowRef();\n  const visible = ref(false);\n  const handleScroll = () => {\n    if (el.value) visible.value = el.value.scrollTop >= props.visibilityHeight;\n  };\n  const handleClick = event => {\n    var _a;\n    (_a = el.value) == null ? void 0 : _a.scrollTo({\n      top: 0,\n      behavior: \"smooth\"\n    });\n    emit(\"click\", event);\n  };\n  const handleScrollThrottled = useThrottleFn(handleScroll, 300, true);\n  useEventListener(container, \"scroll\", handleScrollThrottled);\n  onMounted(() => {\n    var _a;\n    container.value = document;\n    el.value = document.documentElement;\n    if (props.target) {\n      el.value = (_a = document.querySelector(props.target)) != null ? _a : void 0;\n      if (!el.value) {\n        throwError(componentName, `target does not exist: ${props.target}`);\n      }\n      container.value = el.value;\n    }\n    handleScroll();\n  });\n  return {\n    visible,\n    handleClick\n  };\n};\nexport { useBackTop };", "map": {"version": 3, "names": ["useBackTop", "props", "emit", "componentName", "el", "shallowRef", "container", "visible", "ref", "handleScroll", "value", "scrollTop", "visibilityHeight", "handleClick", "event", "_a", "scrollTo", "top", "behavior", "handleScrollThrottled", "useThrottleFn", "useEventListener", "onMounted", "document", "documentElement", "target", "querySelector", "throwError"], "sources": ["../../../../../../packages/components/backtop/src/use-backtop.ts"], "sourcesContent": ["import { onMounted, ref, shallowRef } from 'vue'\nimport { useEventListener, useThrottleFn } from '@vueuse/core'\nimport { throwError } from '@element-plus/utils'\nimport type { SetupContext } from 'vue'\nimport type { BacktopEmits, BacktopProps } from './backtop'\n\nexport const useBackTop = (\n  props: BacktopProps,\n  emit: SetupContext<BacktopEmits>['emit'],\n  componentName: string\n) => {\n  const el = shallowRef<HTMLElement>()\n  const container = shallowRef<Document | HTMLElement>()\n  const visible = ref(false)\n\n  const handleScroll = () => {\n    if (el.value) visible.value = el.value.scrollTop >= props.visibilityHeight\n  }\n\n  const handleClick = (event: MouseEvent) => {\n    el.value?.scrollTo({ top: 0, behavior: 'smooth' })\n    emit('click', event)\n  }\n\n  const handleScrollThrottled = useThrottleFn(handleScroll, 300, true)\n\n  useEventListener(container, 'scroll', handleScrollThrottled)\n  onMounted(() => {\n    container.value = document\n    el.value = document.documentElement\n\n    if (props.target) {\n      el.value = document.querySelector<HTMLElement>(props.target) ?? undefined\n      if (!el.value) {\n        throwError(componentName, `target does not exist: ${props.target}`)\n      }\n      container.value = el.value\n    }\n    // Give visible an initial value, fix #13066\n    handleScroll()\n  })\n\n  return {\n    visible,\n    handleClick,\n  }\n}\n"], "mappings": ";;;AAGY,MAACA,UAAU,GAAGA,CAACC,KAAK,EAAEC,IAAI,EAAEC,aAAa,KAAK;EACxD,MAAMC,EAAE,GAAGC,UAAU,EAAE;EACvB,MAAMC,SAAS,GAAGD,UAAU,EAAE;EAC9B,MAAME,OAAO,GAAGC,GAAG,CAAC,KAAK,CAAC;EAC1B,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIL,EAAE,CAACM,KAAK,EACVH,OAAO,CAACG,KAAK,GAAGN,EAAE,CAACM,KAAK,CAACC,SAAS,IAAIV,KAAK,CAACW,gBAAgB;EAClE,CAAG;EACD,MAAMC,WAAW,GAAIC,KAAK,IAAK;IAC7B,IAAIC,EAAE;IACN,CAACA,EAAE,GAAGX,EAAE,CAACM,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGK,EAAE,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAQ,CAAE,CAAC;IAC9EhB,IAAI,CAAC,OAAO,EAAEY,KAAK,CAAC;EACxB,CAAG;EACD,MAAMK,qBAAqB,GAAGC,aAAa,CAACX,YAAY,EAAE,GAAG,EAAE,IAAI,CAAC;EACpEY,gBAAgB,CAACf,SAAS,EAAE,QAAQ,EAAEa,qBAAqB,CAAC;EAC5DG,SAAS,CAAC,MAAM;IACd,IAAIP,EAAE;IACNT,SAAS,CAACI,KAAK,GAAGa,QAAQ;IAC1BnB,EAAE,CAACM,KAAK,GAAGa,QAAQ,CAACC,eAAe;IACnC,IAAIvB,KAAK,CAACwB,MAAM,EAAE;MAChBrB,EAAE,CAACM,KAAK,GAAG,CAACK,EAAE,GAAGQ,QAAQ,CAACG,aAAa,CAACzB,KAAK,CAACwB,MAAM,CAAC,KAAK,IAAI,GAAGV,EAAE,GAAG,KAAK,CAAC;MAC5E,IAAI,CAACX,EAAE,CAACM,KAAK,EAAE;QACbiB,UAAU,CAACxB,aAAa,EAAE,0BAA0BF,KAAK,CAACwB,MAAM,EAAE,CAAC;MAC3E;MACMnB,SAAS,CAACI,KAAK,GAAGN,EAAE,CAACM,KAAK;IAChC;IACID,YAAY,EAAE;EAClB,CAAG,CAAC;EACF,OAAO;IACLF,OAAO;IACPM;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}