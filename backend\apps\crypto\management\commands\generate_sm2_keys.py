"""
生成SM2密钥对命令
"""
import os
import logging
from django.core.management.base import BaseCommand
from django.conf import settings
from utils.crypto import SM2Crypto

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '生成系统SM2密钥对'
    
    def handle(self, *args, **options):
        try:
            # 确保密钥目录存在
            keys_dir = os.path.dirname(settings.SM2_PUBLIC_KEY_PATH)
            if not os.path.exists(keys_dir):
                os.makedirs(keys_dir)
                self.stdout.write(self.style.SUCCESS(f'创建密钥目录: {keys_dir}'))
            
            # 检查是否已存在密钥
            if os.path.exists(settings.SM2_PUBLIC_KEY_PATH) and os.path.exists(settings.SM2_PRIVATE_KEY_PATH):
                overwrite = input('密钥文件已存在，是否覆盖? (y/n): ')
                if overwrite.lower() != 'y':
                    self.stdout.write(self.style.WARNING('操作已取消'))
                    return
            
            # 生成SM2密钥对
            key_pair = SM2Crypto.generate_key_pair()
            private_key = key_pair['private_key']
            public_key = key_pair['public_key']
            
            # 保存私钥
            with open(settings.SM2_PRIVATE_KEY_PATH, 'w') as f:
                f.write(private_key)
            
            # 保存公钥
            with open(settings.SM2_PUBLIC_KEY_PATH, 'w') as f:
                f.write(public_key)
            
            self.stdout.write(self.style.SUCCESS('SM2密钥对生成成功'))
            self.stdout.write(f'私钥保存路径: {settings.SM2_PRIVATE_KEY_PATH}')
            self.stdout.write(f'公钥保存路径: {settings.SM2_PUBLIC_KEY_PATH}')
            
            # 提示保护私钥
            self.stdout.write(self.style.WARNING('警告: 请妥善保管私钥文件，避免泄露!'))
            
        except Exception as e:
            logger.error(f"生成SM2密钥对失败: {str(e)}")
            self.stdout.write(self.style.ERROR(f'生成SM2密钥对失败: {str(e)}'))
