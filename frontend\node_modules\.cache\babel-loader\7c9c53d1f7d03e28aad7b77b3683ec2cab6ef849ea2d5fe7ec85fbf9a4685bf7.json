{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst breadcrumbItemProps = buildProps({\n  to: {\n    type: definePropType([String, Object]),\n    default: \"\"\n  },\n  replace: Boolean\n});\nexport { breadcrumbItemProps };", "map": {"version": 3, "names": ["breadcrumbItemProps", "buildProps", "to", "type", "definePropType", "String", "Object", "default", "replace", "Boolean"], "sources": ["../../../../../../packages/components/breadcrumb/src/breadcrumb-item.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type { RouteLocationRaw } from 'vue-router'\n\nexport const breadcrumbItemProps = buildProps({\n  /**\n   * @description target route of the link, same as `to` of `vue-router`\n   */\n  to: {\n    type: definePropType<RouteLocationRaw>([String, Object]),\n    default: '',\n  },\n  /**\n   * @description if `true`, the navigation will not leave a history record\n   */\n  replace: Boolean,\n} as const)\nexport type BreadcrumbItemProps = ExtractPropTypes<typeof breadcrumbItemProps>\n"], "mappings": ";AACY,MAACA,mBAAmB,GAAGC,UAAU,CAAC;EAC5CC,EAAE,EAAE;IACFC,IAAI,EAAEC,cAAc,CAAC,CAACC,MAAM,EAAEC,MAAM,CAAC,CAAC;IACtCC,OAAO,EAAE;EACb,CAAG;EACDC,OAAO,EAAEC;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}