{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { nextTick } from 'vue';\nimport { obtainAllFocusableElements } from '../../utils/dom/aria.mjs';\nimport { EVENT_CODE } from '../../constants/aria.mjs';\nconst FOCUSABLE_CHILDREN = \"_trap-focus-children\";\nconst TRAP_FOCUS_HANDLER = \"_trap-focus-handler\";\nconst FOCUS_STACK = [];\nconst FOCUS_HANDLER = e => {\n  var _a;\n  if (FOCUS_STACK.length === 0) return;\n  const focusableElement = FOCUS_STACK[FOCUS_STACK.length - 1][FOCUSABLE_CHILDREN];\n  if (focusableElement.length > 0 && e.code === EVENT_CODE.tab) {\n    if (focusableElement.length === 1) {\n      e.preventDefault();\n      if (document.activeElement !== focusableElement[0]) {\n        focusableElement[0].focus();\n      }\n      return;\n    }\n    const goingBackward = e.shiftKey;\n    const isFirst = e.target === focusableElement[0];\n    const isLast = e.target === focusableElement[focusableElement.length - 1];\n    if (isFirst && goingBackward) {\n      e.preventDefault();\n      focusableElement[focusableElement.length - 1].focus();\n    }\n    if (isLast && !goingBackward) {\n      e.preventDefault();\n      focusableElement[0].focus();\n    }\n    if (process.env.NODE_ENV === \"test\") {\n      const index = focusableElement.indexOf(e.target);\n      if (index !== -1) {\n        (_a = focusableElement[goingBackward ? index - 1 : index + 1]) == null ? void 0 : _a.focus();\n      }\n    }\n  }\n};\nconst TrapFocus = {\n  beforeMount(el) {\n    el[FOCUSABLE_CHILDREN] = obtainAllFocusableElements(el);\n    FOCUS_STACK.push(el);\n    if (FOCUS_STACK.length <= 1) {\n      document.addEventListener(\"keydown\", FOCUS_HANDLER);\n    }\n  },\n  updated(el) {\n    nextTick(() => {\n      el[FOCUSABLE_CHILDREN] = obtainAllFocusableElements(el);\n    });\n  },\n  unmounted() {\n    FOCUS_STACK.shift();\n    if (FOCUS_STACK.length === 0) {\n      document.removeEventListener(\"keydown\", FOCUS_HANDLER);\n    }\n  }\n};\nexport { FOCUSABLE_CHILDREN, TRAP_FOCUS_HANDLER, TrapFocus as default };", "map": {"version": 3, "names": ["FOCUSABLE_CHILDREN", "TRAP_FOCUS_HANDLER", "FOCUS_STACK", "FOCUS_HANDLER", "e", "_a", "length", "focusableElement", "code", "EVENT_CODE", "tab", "preventDefault", "document", "activeElement", "focus", "goingBackward", "shift<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON>", "target", "isLast", "process", "env", "NODE_ENV", "index", "indexOf", "TrapFocus", "beforeMount", "el", "obtainAllFocusableElements", "push", "addEventListener", "updated", "nextTick", "unmounted", "shift", "removeEventListener"], "sources": ["../../../../../packages/directives/trap-focus/index.ts"], "sourcesContent": ["import { nextTick } from 'vue'\nimport { obtainAllFocusableElements } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport type { ObjectDirective } from 'vue'\n\nexport const FOCUSABLE_CHILDREN = '_trap-focus-children'\nexport const TRAP_FOCUS_HANDLER = '_trap-focus-handler'\n\nexport interface TrapFocusElement extends HTMLElement {\n  [FOCUSABLE_CHILDREN]: HTMLElement[]\n  [TRAP_FOCUS_HANDLER]: (e: KeyboardEvent) => void\n}\n\nconst FOCUS_STACK: TrapFocusElement[] = []\n\nconst FOCUS_HANDLER = (e: KeyboardEvent) => {\n  // Getting the top layer.\n  if (FOCUS_STACK.length === 0) return\n  const focusableElement =\n    FOCUS_STACK[FOCUS_STACK.length - 1][FOCUSABLE_CHILDREN]\n  if (focusableElement.length > 0 && e.code === EVENT_CODE.tab) {\n    if (focusableElement.length === 1) {\n      e.preventDefault()\n      if (document.activeElement !== focusableElement[0]) {\n        focusableElement[0].focus()\n      }\n      return\n    }\n    const goingBackward = e.shiftKey\n    const isFirst = e.target === focusableElement[0]\n    const isLast = e.target === focusableElement[focusableElement.length - 1]\n    if (isFirst && goingBackward) {\n      e.preventDefault()\n      focusableElement[focusableElement.length - 1].focus()\n    }\n    if (isLast && !goingBackward) {\n      e.preventDefault()\n      focusableElement[0].focus()\n    }\n\n    // the is critical since jsdom did not implement user actions, you can only mock it\n    // DELETE ME: when testing env switches to puppeteer\n    if (process.env.NODE_ENV === 'test') {\n      const index = focusableElement.indexOf(e.target as HTMLElement)\n      if (index !== -1) {\n        focusableElement[goingBackward ? index - 1 : index + 1]?.focus()\n      }\n    }\n  }\n}\n\nconst TrapFocus: ObjectDirective = {\n  beforeMount(el: TrapFocusElement) {\n    el[FOCUSABLE_CHILDREN] = obtainAllFocusableElements(el)\n    FOCUS_STACK.push(el)\n    if (FOCUS_STACK.length <= 1) {\n      document.addEventListener('keydown', FOCUS_HANDLER)\n    }\n  },\n  updated(el: TrapFocusElement) {\n    nextTick(() => {\n      el[FOCUSABLE_CHILDREN] = obtainAllFocusableElements(el)\n    })\n  },\n  unmounted() {\n    FOCUS_STACK.shift()\n    if (FOCUS_STACK.length === 0) {\n      document.removeEventListener('keydown', FOCUS_HANDLER)\n    }\n  },\n}\n\nexport default TrapFocus\n"], "mappings": ";;;;AAGY,MAACA,kBAAkB,GAAG;AACtB,MAACC,kBAAkB,GAAG;AAClC,MAAMC,WAAW,GAAG,EAAE;AACtB,MAAMC,aAAa,GAAIC,CAAC,IAAK;EAC3B,IAAIC,EAAE;EACN,IAAIH,WAAW,CAACI,MAAM,KAAK,CAAC,EAC1B;EACF,MAAMC,gBAAgB,GAAGL,WAAW,CAACA,WAAW,CAACI,MAAM,GAAG,CAAC,CAAC,CAACN,kBAAkB,CAAC;EAChF,IAAIO,gBAAgB,CAACD,MAAM,GAAG,CAAC,IAAIF,CAAC,CAACI,IAAI,KAAKC,UAAU,CAACC,GAAG,EAAE;IAC5D,IAAIH,gBAAgB,CAACD,MAAM,KAAK,CAAC,EAAE;MACjCF,CAAC,CAACO,cAAc,EAAE;MAClB,IAAIC,QAAQ,CAACC,aAAa,KAAKN,gBAAgB,CAAC,CAAC,CAAC,EAAE;QAClDA,gBAAgB,CAAC,CAAC,CAAC,CAACO,KAAK,EAAE;MACnC;MACM;IACN;IACI,MAAMC,aAAa,GAAGX,CAAC,CAACY,QAAQ;IAChC,MAAMC,OAAO,GAAGb,CAAC,CAACc,MAAM,KAAKX,gBAAgB,CAAC,CAAC,CAAC;IAChD,MAAMY,MAAM,GAAGf,CAAC,CAACc,MAAM,KAAKX,gBAAgB,CAACA,gBAAgB,CAACD,MAAM,GAAG,CAAC,CAAC;IACzE,IAAIW,OAAO,IAAIF,aAAa,EAAE;MAC5BX,CAAC,CAACO,cAAc,EAAE;MAClBJ,gBAAgB,CAACA,gBAAgB,CAACD,MAAM,GAAG,CAAC,CAAC,CAACQ,KAAK,EAAE;IAC3D;IACI,IAAIK,MAAM,IAAI,CAACJ,aAAa,EAAE;MAC5BX,CAAC,CAACO,cAAc,EAAE;MAClBJ,gBAAgB,CAAC,CAAC,CAAC,CAACO,KAAK,EAAE;IACjC;IACI,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnC,MAAMC,KAAK,GAAGhB,gBAAgB,CAACiB,OAAO,CAACpB,CAAC,CAACc,MAAM,CAAC;MAChD,IAAIK,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,CAAClB,EAAE,GAAGE,gBAAgB,CAACQ,aAAa,GAAGQ,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGlB,EAAE,CAACS,KAAK,EAAE;MACpG;IACA;EACA;AACA,CAAC;AACI,MAACW,SAAS,GAAG;EAChBC,WAAWA,CAACC,EAAE,EAAE;IACdA,EAAE,CAAC3B,kBAAkB,CAAC,GAAG4B,0BAA0B,CAACD,EAAE,CAAC;IACvDzB,WAAW,CAAC2B,IAAI,CAACF,EAAE,CAAC;IACpB,IAAIzB,WAAW,CAACI,MAAM,IAAI,CAAC,EAAE;MAC3BM,QAAQ,CAACkB,gBAAgB,CAAC,SAAS,EAAE3B,aAAa,CAAC;IACzD;EACA,CAAG;EACD4B,OAAOA,CAACJ,EAAE,EAAE;IACVK,QAAQ,CAAC,MAAM;MACbL,EAAE,CAAC3B,kBAAkB,CAAC,GAAG4B,0BAA0B,CAACD,EAAE,CAAC;IAC7D,CAAK,CAAC;EACN,CAAG;EACDM,SAASA,CAAA,EAAG;IACV/B,WAAW,CAACgC,KAAK,EAAE;IACnB,IAAIhC,WAAW,CAACI,MAAM,KAAK,CAAC,EAAE;MAC5BM,QAAQ,CAACuB,mBAAmB,CAAC,SAAS,EAAEhC,aAAa,CAAC;IAC5D;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}