{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { ref, onMounted, watch, onBeforeUnmount, computed, unref, watchEffect } from 'vue';\nimport { offset, flip, shift, detectOverflow, arrow, computePosition, autoUpdate } from '@floating-ui/dom';\nimport { isArray, isString, isFunction } from '@vue/shared';\nimport { isClient } from '@vueuse/core';\nimport { keysOf } from '../../../utils/objects.mjs';\nconst useTarget = (target, open, gap, mergedMask, scrollIntoViewOptions) => {\n  const posInfo = ref(null);\n  const getTargetEl = () => {\n    let targetEl;\n    if (isString(target.value)) {\n      targetEl = document.querySelector(target.value);\n    } else if (isFunction(target.value)) {\n      targetEl = target.value();\n    } else {\n      targetEl = target.value;\n    }\n    return targetEl;\n  };\n  const updatePosInfo = () => {\n    const targetEl = getTargetEl();\n    if (!targetEl || !open.value) {\n      posInfo.value = null;\n      return;\n    }\n    if (!isInViewPort(targetEl)) {\n      targetEl.scrollIntoView(scrollIntoViewOptions.value);\n    }\n    const {\n      left,\n      top,\n      width,\n      height\n    } = targetEl.getBoundingClientRect();\n    posInfo.value = {\n      left,\n      top,\n      width,\n      height,\n      radius: 0\n    };\n  };\n  onMounted(() => {\n    watch([open, target], () => {\n      updatePosInfo();\n    }, {\n      immediate: true\n    });\n    window.addEventListener(\"resize\", updatePosInfo);\n  });\n  onBeforeUnmount(() => {\n    window.removeEventListener(\"resize\", updatePosInfo);\n  });\n  const getGapOffset = index => {\n    var _a;\n    return (_a = isArray(gap.value.offset) ? gap.value.offset[index] : gap.value.offset) != null ? _a : 6;\n  };\n  const mergedPosInfo = computed(() => {\n    var _a;\n    if (!posInfo.value) return posInfo.value;\n    const gapOffsetX = getGapOffset(0);\n    const gapOffsetY = getGapOffset(1);\n    const gapRadius = ((_a = gap.value) == null ? void 0 : _a.radius) || 2;\n    return {\n      left: posInfo.value.left - gapOffsetX,\n      top: posInfo.value.top - gapOffsetY,\n      width: posInfo.value.width + gapOffsetX * 2,\n      height: posInfo.value.height + gapOffsetY * 2,\n      radius: gapRadius\n    };\n  });\n  const triggerTarget = computed(() => {\n    const targetEl = getTargetEl();\n    if (!mergedMask.value || !targetEl || !window.DOMRect) {\n      return targetEl || void 0;\n    }\n    return {\n      getBoundingClientRect() {\n        var _a, _b, _c, _d;\n        return window.DOMRect.fromRect({\n          width: ((_a = mergedPosInfo.value) == null ? void 0 : _a.width) || 0,\n          height: ((_b = mergedPosInfo.value) == null ? void 0 : _b.height) || 0,\n          x: ((_c = mergedPosInfo.value) == null ? void 0 : _c.left) || 0,\n          y: ((_d = mergedPosInfo.value) == null ? void 0 : _d.top) || 0\n        });\n      }\n    };\n  });\n  return {\n    mergedPosInfo,\n    triggerTarget\n  };\n};\nconst tourKey = Symbol(\"ElTour\");\nfunction isInViewPort(element) {\n  const viewWidth = window.innerWidth || document.documentElement.clientWidth;\n  const viewHeight = window.innerHeight || document.documentElement.clientHeight;\n  const {\n    top,\n    right,\n    bottom,\n    left\n  } = element.getBoundingClientRect();\n  return top >= 0 && left >= 0 && right <= viewWidth && bottom <= viewHeight;\n}\nconst useFloating = (referenceRef, contentRef, arrowRef, placement, strategy, offset$1, zIndex, showArrow) => {\n  const x = ref();\n  const y = ref();\n  const middlewareData = ref({});\n  const states = {\n    x,\n    y,\n    placement,\n    strategy,\n    middlewareData\n  };\n  const middleware = computed(() => {\n    const _middleware = [offset(unref(offset$1)), flip(), shift(), overflowMiddleware()];\n    if (unref(showArrow) && unref(arrowRef)) {\n      _middleware.push(arrow({\n        element: unref(arrowRef)\n      }));\n    }\n    return _middleware;\n  });\n  const update = async () => {\n    if (!isClient) return;\n    const referenceEl = unref(referenceRef);\n    const contentEl = unref(contentRef);\n    if (!referenceEl || !contentEl) return;\n    const data = await computePosition(referenceEl, contentEl, {\n      placement: unref(placement),\n      strategy: unref(strategy),\n      middleware: unref(middleware)\n    });\n    keysOf(states).forEach(key => {\n      states[key].value = data[key];\n    });\n  };\n  const contentStyle = computed(() => {\n    if (!unref(referenceRef)) {\n      return {\n        position: \"fixed\",\n        top: \"50%\",\n        left: \"50%\",\n        transform: \"translate3d(-50%, -50%, 0)\",\n        maxWidth: \"100vw\",\n        zIndex: unref(zIndex)\n      };\n    }\n    const {\n      overflow\n    } = unref(middlewareData);\n    return {\n      position: unref(strategy),\n      zIndex: unref(zIndex),\n      top: unref(y) != null ? `${unref(y)}px` : \"\",\n      left: unref(x) != null ? `${unref(x)}px` : \"\",\n      maxWidth: (overflow == null ? void 0 : overflow.maxWidth) ? `${overflow == null ? void 0 : overflow.maxWidth}px` : \"\"\n    };\n  });\n  const arrowStyle = computed(() => {\n    if (!unref(showArrow)) return {};\n    const {\n      arrow: arrow2\n    } = unref(middlewareData);\n    return {\n      left: (arrow2 == null ? void 0 : arrow2.x) != null ? `${arrow2 == null ? void 0 : arrow2.x}px` : \"\",\n      top: (arrow2 == null ? void 0 : arrow2.y) != null ? `${arrow2 == null ? void 0 : arrow2.y}px` : \"\"\n    };\n  });\n  let cleanup;\n  onMounted(() => {\n    const referenceEl = unref(referenceRef);\n    const contentEl = unref(contentRef);\n    if (referenceEl && contentEl) {\n      cleanup = autoUpdate(referenceEl, contentEl, update);\n    }\n    watchEffect(() => {\n      update();\n    });\n  });\n  onBeforeUnmount(() => {\n    cleanup && cleanup();\n  });\n  return {\n    update,\n    contentStyle,\n    arrowStyle\n  };\n};\nconst overflowMiddleware = () => {\n  return {\n    name: \"overflow\",\n    async fn(state) {\n      const overflow = await detectOverflow(state);\n      let overWidth = 0;\n      if (overflow.left > 0) overWidth = overflow.left;\n      if (overflow.right > 0) overWidth = overflow.right;\n      const floatingWidth = state.rects.floating.width;\n      return {\n        data: {\n          maxWidth: floatingWidth - overWidth\n        }\n      };\n    }\n  };\n};\nexport { tourKey, useFloating, useTarget };", "map": {"version": 3, "names": ["useTarget", "target", "open", "gap", "mergedMask", "scrollIntoViewOptions", "posInfo", "ref", "getTargetEl", "targetEl", "isString", "value", "document", "querySelector", "isFunction", "updatePosInfo", "isInViewPort", "scrollIntoView", "left", "top", "width", "height", "getBoundingClientRect", "radius", "onMounted", "watch", "immediate", "window", "addEventListener", "onBeforeUnmount", "removeEventListener", "getGapOffset", "index", "_a", "isArray", "offset", "mergedPosInfo", "computed", "gapOffsetX", "gapOffsetY", "gapRadius", "triggerTarget", "DOMRect", "_b", "_c", "_d", "fromRect", "x", "y", "tourKey", "Symbol", "element", "viewWidth", "innerWidth", "documentElement", "clientWidth", "viewHeight", "innerHeight", "clientHeight", "right", "bottom", "useFloating", "referenceRef", "contentRef", "arrowRef", "placement", "strategy", "offset$1", "zIndex", "showArrow", "middlewareData", "states", "middleware", "_middleware", "unref", "flip", "shift", "overflowMiddleware", "push", "arrow", "update", "isClient", "referenceEl", "contentEl", "data", "computePosition", "keysOf", "for<PERSON>ach", "key", "contentStyle", "position", "transform", "max<PERSON><PERSON><PERSON>", "overflow", "arrowStyle", "arrow2", "cleanup", "autoUpdate", "watchEffect", "name", "fn", "state", "detectOverflow", "overWidth", "floatingWidth", "rects", "floating"], "sources": ["../../../../../../packages/components/tour/src/helper.ts"], "sourcesContent": ["import {\n  computed,\n  onBeforeUnmount,\n  onMounted,\n  ref,\n  unref,\n  watch,\n  watchEffect,\n} from 'vue'\nimport {\n  arrow,\n  autoUpdate,\n  computePosition,\n  detectOverflow,\n  flip,\n  offset as offsetMiddelware,\n  shift,\n} from '@floating-ui/dom'\nimport {\n  isArray,\n  isClient,\n  isFunction,\n  isString,\n  keysOf,\n} from '@element-plus/utils'\n\nimport type {\n  CSSProperties,\n  Component,\n  InjectionKey,\n  Ref,\n  SetupContext,\n} from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\nimport type { PosInfo, TourGap, TourMask } from './types'\nimport type {\n  ComputePositionReturn,\n  Middleware,\n  Placement,\n  Strategy,\n  VirtualElement,\n} from '@floating-ui/dom'\nimport type { TourStepProps } from './step'\n\nexport const useTarget = (\n  target: Ref<\n    string | HTMLElement | (() => HTMLElement | null) | null | undefined\n  >,\n  open: Ref<boolean>,\n  gap: Ref<TourGap>,\n  mergedMask: Ref<TourMask>,\n  scrollIntoViewOptions: Ref<boolean | ScrollIntoViewOptions>\n) => {\n  const posInfo: Ref<PosInfo | null> = ref(null)\n\n  const getTargetEl = () => {\n    let targetEl: HTMLElement | null | undefined\n    if (isString(target.value)) {\n      targetEl = document.querySelector<HTMLElement>(target.value)\n    } else if (isFunction(target.value)) {\n      targetEl = target.value()\n    } else {\n      targetEl = target.value\n    }\n    return targetEl\n  }\n\n  const updatePosInfo = () => {\n    const targetEl = getTargetEl()\n    if (!targetEl || !open.value) {\n      posInfo.value = null\n      return\n    }\n    if (!isInViewPort(targetEl)) {\n      targetEl.scrollIntoView(scrollIntoViewOptions.value)\n    }\n    const { left, top, width, height } = targetEl.getBoundingClientRect()\n    posInfo.value = {\n      left,\n      top,\n      width,\n      height,\n      radius: 0,\n    }\n  }\n\n  onMounted(() => {\n    watch(\n      [open, target],\n      () => {\n        updatePosInfo()\n      },\n      {\n        immediate: true,\n      }\n    )\n    window.addEventListener('resize', updatePosInfo)\n  })\n\n  onBeforeUnmount(() => {\n    window.removeEventListener('resize', updatePosInfo)\n  })\n\n  const getGapOffset = (index: number) =>\n    (isArray(gap.value.offset) ? gap.value.offset[index] : gap.value.offset) ??\n    6\n\n  const mergedPosInfo = computed(() => {\n    if (!posInfo.value) return posInfo.value\n\n    const gapOffsetX = getGapOffset(0)\n    const gapOffsetY = getGapOffset(1)\n    const gapRadius = gap.value?.radius || 2\n\n    return {\n      left: posInfo.value.left - gapOffsetX,\n      top: posInfo.value.top - gapOffsetY,\n      width: posInfo.value.width + gapOffsetX * 2,\n      height: posInfo.value.height + gapOffsetY * 2,\n      radius: gapRadius,\n    }\n  })\n\n  const triggerTarget = computed(() => {\n    const targetEl = getTargetEl()\n    if (!mergedMask.value || !targetEl || !window.DOMRect) {\n      return targetEl || undefined\n    }\n\n    return {\n      getBoundingClientRect() {\n        return window.DOMRect.fromRect({\n          width: mergedPosInfo.value?.width || 0,\n          height: mergedPosInfo.value?.height || 0,\n          x: mergedPosInfo.value?.left || 0,\n          y: mergedPosInfo.value?.top || 0,\n        })\n      },\n    }\n  })\n\n  return {\n    mergedPosInfo,\n    triggerTarget,\n  }\n}\n\nexport interface TourContext {\n  currentStep: Ref<TourStepProps | undefined>\n  current: Ref<number>\n  total: Ref<number>\n  showClose: Ref<boolean>\n  closeIcon: Ref<string | Component>\n  mergedType: Ref<'default' | 'primary' | undefined>\n  ns: UseNamespaceReturn\n  slots: SetupContext['slots']\n  updateModelValue(modelValue: boolean): void\n  onClose(): void\n  onFinish(): void\n  onChange(): void\n}\n\nexport const tourKey: InjectionKey<TourContext> = Symbol('ElTour')\n\nfunction isInViewPort(element: HTMLElement) {\n  const viewWidth = window.innerWidth || document.documentElement.clientWidth\n  const viewHeight = window.innerHeight || document.documentElement.clientHeight\n  const { top, right, bottom, left } = element.getBoundingClientRect()\n\n  return top >= 0 && left >= 0 && right <= viewWidth && bottom <= viewHeight\n}\n\nexport const useFloating = (\n  referenceRef: Ref<HTMLElement | VirtualElement | null>,\n  contentRef: Ref<HTMLElement | null>,\n  arrowRef: Ref<HTMLElement | null>,\n  placement: Ref<Placement | undefined>,\n  strategy: Ref<Strategy>,\n  offset: Ref<number>,\n  zIndex: Ref<number>,\n  showArrow: Ref<boolean>\n) => {\n  const x = ref<number>()\n  const y = ref<number>()\n  const middlewareData = ref<ComputePositionReturn['middlewareData']>({})\n\n  const states = {\n    x,\n    y,\n    placement,\n    strategy,\n    middlewareData,\n  } as const\n\n  const middleware = computed(() => {\n    const _middleware: Middleware[] = [\n      offsetMiddelware(unref(offset)),\n      flip(),\n      shift(),\n      overflowMiddleware(),\n    ]\n\n    if (unref(showArrow) && unref(arrowRef)) {\n      _middleware.push(\n        arrow({\n          element: unref(arrowRef)!,\n        })\n      )\n    }\n    return _middleware\n  })\n\n  const update = async () => {\n    if (!isClient) return\n\n    const referenceEl = unref(referenceRef)\n    const contentEl = unref(contentRef)\n    if (!referenceEl || !contentEl) return\n\n    const data = await computePosition(referenceEl, contentEl, {\n      placement: unref(placement),\n      strategy: unref(strategy),\n      middleware: unref(middleware),\n    })\n\n    keysOf(states).forEach((key) => {\n      states[key].value = data[key]\n    })\n  }\n\n  const contentStyle = computed<CSSProperties>(() => {\n    if (!unref(referenceRef)) {\n      return {\n        position: 'fixed',\n        top: '50%',\n        left: '50%',\n        transform: 'translate3d(-50%, -50%, 0)',\n        maxWidth: '100vw',\n        zIndex: unref(zIndex),\n      }\n    }\n\n    const { overflow } = unref(middlewareData)\n\n    return {\n      position: unref(strategy),\n      zIndex: unref(zIndex),\n      top: unref(y) != null ? `${unref(y)}px` : '',\n      left: unref(x) != null ? `${unref(x)}px` : '',\n      maxWidth: overflow?.maxWidth ? `${overflow?.maxWidth}px` : '',\n    }\n  })\n\n  const arrowStyle = computed<CSSProperties>(() => {\n    if (!unref(showArrow)) return {}\n\n    const { arrow } = unref(middlewareData)\n    return {\n      left: arrow?.x != null ? `${arrow?.x}px` : '',\n      top: arrow?.y != null ? `${arrow?.y}px` : '',\n    }\n  })\n\n  let cleanup: any\n  onMounted(() => {\n    const referenceEl = unref(referenceRef)\n    const contentEl = unref(contentRef)\n    if (referenceEl && contentEl) {\n      cleanup = autoUpdate(referenceEl, contentEl, update)\n    }\n\n    watchEffect(() => {\n      update()\n    })\n  })\n\n  onBeforeUnmount(() => {\n    cleanup && cleanup()\n  })\n\n  return {\n    update,\n    contentStyle,\n    arrowStyle,\n  }\n}\n\nconst overflowMiddleware = (): Middleware => {\n  return {\n    name: 'overflow',\n    async fn(state) {\n      const overflow = await detectOverflow(state)\n      let overWidth = 0\n      if (overflow.left > 0) overWidth = overflow.left\n      if (overflow.right > 0) overWidth = overflow.right\n      const floatingWidth = state.rects.floating.width\n      return {\n        data: {\n          maxWidth: floatingWidth - overWidth,\n        },\n      }\n    },\n  }\n}\n"], "mappings": ";;;;;;;;AAyBY,MAACA,SAAS,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAEC,qBAAqB,KAAK;EACjF,MAAMC,OAAO,GAAGC,GAAG,CAAC,IAAI,CAAC;EACzB,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIC,QAAQ;IACZ,IAAIC,QAAQ,CAACT,MAAM,CAACU,KAAK,CAAC,EAAE;MAC1BF,QAAQ,GAAGG,QAAQ,CAACC,aAAa,CAACZ,MAAM,CAACU,KAAK,CAAC;IACrD,CAAK,MAAM,IAAIG,UAAU,CAACb,MAAM,CAACU,KAAK,CAAC,EAAE;MACnCF,QAAQ,GAAGR,MAAM,CAACU,KAAK,EAAE;IAC/B,CAAK,MAAM;MACLF,QAAQ,GAAGR,MAAM,CAACU,KAAK;IAC7B;IACI,OAAOF,QAAQ;EACnB,CAAG;EACD,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMN,QAAQ,GAAGD,WAAW,EAAE;IAC9B,IAAI,CAACC,QAAQ,IAAI,CAACP,IAAI,CAACS,KAAK,EAAE;MAC5BL,OAAO,CAACK,KAAK,GAAG,IAAI;MACpB;IACN;IACI,IAAI,CAACK,YAAY,CAACP,QAAQ,CAAC,EAAE;MAC3BA,QAAQ,CAACQ,cAAc,CAACZ,qBAAqB,CAACM,KAAK,CAAC;IAC1D;IACI,MAAM;MAAEO,IAAI;MAAEC,GAAG;MAAEC,KAAK;MAAEC;IAAM,CAAE,GAAGZ,QAAQ,CAACa,qBAAqB,EAAE;IACrEhB,OAAO,CAACK,KAAK,GAAG;MACdO,IAAI;MACJC,GAAG;MACHC,KAAK;MACLC,MAAM;MACNE,MAAM,EAAE;IACd,CAAK;EACL,CAAG;EACDC,SAAS,CAAC,MAAM;IACdC,KAAK,CAAC,CAACvB,IAAI,EAAED,MAAM,CAAC,EAAE,MAAM;MAC1Bc,aAAa,EAAE;IACrB,CAAK,EAAE;MACDW,SAAS,EAAE;IACjB,CAAK,CAAC;IACFC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEb,aAAa,CAAC;EACpD,CAAG,CAAC;EACFc,eAAe,CAAC,MAAM;IACpBF,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEf,aAAa,CAAC;EACvD,CAAG,CAAC;EACF,MAAMgB,YAAY,GAAIC,KAAK,IAAK;IAC9B,IAAIC,EAAE;IACN,OAAO,CAACA,EAAE,GAAGC,OAAO,CAAC/B,GAAG,CAACQ,KAAK,CAACwB,MAAM,CAAC,GAAGhC,GAAG,CAACQ,KAAK,CAACwB,MAAM,CAACH,KAAK,CAAC,GAAG7B,GAAG,CAACQ,KAAK,CAACwB,MAAM,KAAK,IAAI,GAAGF,EAAE,GAAG,CAAC;EACzG,CAAG;EACD,MAAMG,aAAa,GAAGC,QAAQ,CAAC,MAAM;IACnC,IAAIJ,EAAE;IACN,IAAI,CAAC3B,OAAO,CAACK,KAAK,EAChB,OAAOL,OAAO,CAACK,KAAK;IACtB,MAAM2B,UAAU,GAAGP,YAAY,CAAC,CAAC,CAAC;IAClC,MAAMQ,UAAU,GAAGR,YAAY,CAAC,CAAC,CAAC;IAClC,MAAMS,SAAS,GAAG,CAAC,CAACP,EAAE,GAAG9B,GAAG,CAACQ,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsB,EAAE,CAACV,MAAM,KAAK,CAAC;IACtE,OAAO;MACLL,IAAI,EAAEZ,OAAO,CAACK,KAAK,CAACO,IAAI,GAAGoB,UAAU;MACrCnB,GAAG,EAAEb,OAAO,CAACK,KAAK,CAACQ,GAAG,GAAGoB,UAAU;MACnCnB,KAAK,EAAEd,OAAO,CAACK,KAAK,CAACS,KAAK,GAAGkB,UAAU,GAAG,CAAC;MAC3CjB,MAAM,EAAEf,OAAO,CAACK,KAAK,CAACU,MAAM,GAAGkB,UAAU,GAAG,CAAC;MAC7ChB,MAAM,EAAEiB;IACd,CAAK;EACL,CAAG,CAAC;EACF,MAAMC,aAAa,GAAGJ,QAAQ,CAAC,MAAM;IACnC,MAAM5B,QAAQ,GAAGD,WAAW,EAAE;IAC9B,IAAI,CAACJ,UAAU,CAACO,KAAK,IAAI,CAACF,QAAQ,IAAI,CAACkB,MAAM,CAACe,OAAO,EAAE;MACrD,OAAOjC,QAAQ,IAAI,KAAK,CAAC;IAC/B;IACI,OAAO;MACLa,qBAAqBA,CAAA,EAAG;QACtB,IAAIW,EAAE,EAAEU,EAAE,EAAEC,EAAE,EAAEC,EAAE;QAClB,OAAOlB,MAAM,CAACe,OAAO,CAACI,QAAQ,CAAC;UAC7B1B,KAAK,EAAE,CAAC,CAACa,EAAE,GAAGG,aAAa,CAACzB,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsB,EAAE,CAACb,KAAK,KAAK,CAAC;UACpEC,MAAM,EAAE,CAAC,CAACsB,EAAE,GAAGP,aAAa,CAACzB,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgC,EAAE,CAACtB,MAAM,KAAK,CAAC;UACtE0B,CAAC,EAAE,CAAC,CAACH,EAAE,GAAGR,aAAa,CAACzB,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiC,EAAE,CAAC1B,IAAI,KAAK,CAAC;UAC/D8B,CAAC,EAAE,CAAC,CAACH,EAAE,GAAGT,aAAa,CAACzB,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkC,EAAE,CAAC1B,GAAG,KAAK;QACvE,CAAS,CAAC;MACV;IACA,CAAK;EACL,CAAG,CAAC;EACF,OAAO;IACLiB,aAAa;IACbK;EACJ,CAAG;AACH;AACY,MAACQ,OAAO,GAAGC,MAAM,CAAC,QAAQ;AACtC,SAASlC,YAAYA,CAACmC,OAAO,EAAE;EAC7B,MAAMC,SAAS,GAAGzB,MAAM,CAAC0B,UAAU,IAAIzC,QAAQ,CAAC0C,eAAe,CAACC,WAAW;EAC3E,MAAMC,UAAU,GAAG7B,MAAM,CAAC8B,WAAW,IAAI7C,QAAQ,CAAC0C,eAAe,CAACI,YAAY;EAC9E,MAAM;IAAEvC,GAAG;IAAEwC,KAAK;IAAEC,MAAM;IAAE1C;EAAI,CAAE,GAAGiC,OAAO,CAAC7B,qBAAqB,EAAE;EACpE,OAAOH,GAAG,IAAI,CAAC,IAAID,IAAI,IAAI,CAAC,IAAIyC,KAAK,IAAIP,SAAS,IAAIQ,MAAM,IAAIJ,UAAU;AAC5E;AACY,MAACK,WAAW,GAAGA,CAACC,YAAY,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAM,EAAEC,MAAM,EAAEC,SAAS,KAAK;EACjH,MAAMtB,CAAC,GAAGxC,GAAG,EAAE;EACf,MAAMyC,CAAC,GAAGzC,GAAG,EAAE;EACf,MAAM+D,cAAc,GAAG/D,GAAG,CAAC,EAAE,CAAC;EAC9B,MAAMgE,MAAM,GAAG;IACbxB,CAAC;IACDC,CAAC;IACDiB,SAAS;IACTC,QAAQ;IACRI;EACJ,CAAG;EACD,MAAME,UAAU,GAAGnC,QAAQ,CAAC,MAAM;IAChC,MAAMoC,WAAW,GAAG,CAClBtC,MAAgB,CAACuC,KAAK,CAACP,QAAM,CAAC,CAAC,EAC/BQ,IAAI,EAAE,EACNC,KAAK,EAAE,EACPC,kBAAkB,EAAE,CACrB;IACD,IAAIH,KAAK,CAACL,SAAS,CAAC,IAAIK,KAAK,CAACV,QAAQ,CAAC,EAAE;MACvCS,WAAW,CAACK,IAAI,CAACC,KAAK,CAAC;QACrB5B,OAAO,EAAEuB,KAAK,CAACV,QAAQ;MAC/B,CAAO,CAAC,CAAC;IACT;IACI,OAAOS,WAAW;EACtB,CAAG,CAAC;EACF,MAAMO,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI,CAACC,QAAQ,EACX;IACF,MAAMC,WAAW,GAAGR,KAAK,CAACZ,YAAY,CAAC;IACvC,MAAMqB,SAAS,GAAGT,KAAK,CAACX,UAAU,CAAC;IACnC,IAAI,CAACmB,WAAW,IAAI,CAACC,SAAS,EAC5B;IACF,MAAMC,IAAI,GAAG,MAAMC,eAAe,CAACH,WAAW,EAAEC,SAAS,EAAE;MACzDlB,SAAS,EAAES,KAAK,CAACT,SAAS,CAAC;MAC3BC,QAAQ,EAAEQ,KAAK,CAACR,QAAQ,CAAC;MACzBM,UAAU,EAAEE,KAAK,CAACF,UAAU;IAClC,CAAK,CAAC;IACFc,MAAM,CAACf,MAAM,CAAC,CAACgB,OAAO,CAAEC,GAAG,IAAK;MAC9BjB,MAAM,CAACiB,GAAG,CAAC,CAAC7E,KAAK,GAAGyE,IAAI,CAACI,GAAG,CAAC;IACnC,CAAK,CAAC;EACN,CAAG;EACD,MAAMC,YAAY,GAAGpD,QAAQ,CAAC,MAAM;IAClC,IAAI,CAACqC,KAAK,CAACZ,YAAY,CAAC,EAAE;MACxB,OAAO;QACL4B,QAAQ,EAAE,OAAO;QACjBvE,GAAG,EAAE,KAAK;QACVD,IAAI,EAAE,KAAK;QACXyE,SAAS,EAAE,4BAA4B;QACvCC,QAAQ,EAAE,OAAO;QACjBxB,MAAM,EAAEM,KAAK,CAACN,MAAM;MAC5B,CAAO;IACP;IACI,MAAM;MAAEyB;IAAQ,CAAE,GAAGnB,KAAK,CAACJ,cAAc,CAAC;IAC1C,OAAO;MACLoB,QAAQ,EAAEhB,KAAK,CAACR,QAAQ,CAAC;MACzBE,MAAM,EAAEM,KAAK,CAACN,MAAM,CAAC;MACrBjD,GAAG,EAAEuD,KAAK,CAAC1B,CAAC,CAAC,IAAI,IAAI,GAAG,GAAG0B,KAAK,CAAC1B,CAAC,CAAC,IAAI,GAAG,EAAE;MAC5C9B,IAAI,EAAEwD,KAAK,CAAC3B,CAAC,CAAC,IAAI,IAAI,GAAG,GAAG2B,KAAK,CAAC3B,CAAC,CAAC,IAAI,GAAG,EAAE;MAC7C6C,QAAQ,EAAE,CAACC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACD,QAAQ,IAAI,GAAGC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACD,QAAQ,IAAI,GAAG;IACzH,CAAK;EACL,CAAG,CAAC;EACF,MAAME,UAAU,GAAGzD,QAAQ,CAAC,MAAM;IAChC,IAAI,CAACqC,KAAK,CAACL,SAAS,CAAC,EACnB,OAAO,EAAE;IACX,MAAM;MAAEU,KAAK,EAAEgB;IAAM,CAAE,GAAGrB,KAAK,CAACJ,cAAc,CAAC;IAC/C,OAAO;MACLpD,IAAI,EAAE,CAAC6E,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAChD,CAAC,KAAK,IAAI,GAAG,GAAGgD,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAChD,CAAC,IAAI,GAAG,EAAE;MACnG5B,GAAG,EAAE,CAAC4E,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC/C,CAAC,KAAK,IAAI,GAAG,GAAG+C,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC/C,CAAC,IAAI,GAAG;IACtG,CAAK;EACL,CAAG,CAAC;EACF,IAAIgD,OAAO;EACXxE,SAAS,CAAC,MAAM;IACd,MAAM0D,WAAW,GAAGR,KAAK,CAACZ,YAAY,CAAC;IACvC,MAAMqB,SAAS,GAAGT,KAAK,CAACX,UAAU,CAAC;IACnC,IAAImB,WAAW,IAAIC,SAAS,EAAE;MAC5Ba,OAAO,GAAGC,UAAU,CAACf,WAAW,EAAEC,SAAS,EAAEH,MAAM,CAAC;IAC1D;IACIkB,WAAW,CAAC,MAAM;MAChBlB,MAAM,EAAE;IACd,CAAK,CAAC;EACN,CAAG,CAAC;EACFnD,eAAe,CAAC,MAAM;IACpBmE,OAAO,IAAIA,OAAO,EAAE;EACxB,CAAG,CAAC;EACF,OAAO;IACLhB,MAAM;IACNS,YAAY;IACZK;EACJ,CAAG;AACH;AACA,MAAMjB,kBAAkB,GAAGA,CAAA,KAAM;EAC/B,OAAO;IACLsB,IAAI,EAAE,UAAU;IAChB,MAAMC,EAAEA,CAACC,KAAK,EAAE;MACd,MAAMR,QAAQ,GAAG,MAAMS,cAAc,CAACD,KAAK,CAAC;MAC5C,IAAIE,SAAS,GAAG,CAAC;MACjB,IAAIV,QAAQ,CAAC3E,IAAI,GAAG,CAAC,EACnBqF,SAAS,GAAGV,QAAQ,CAAC3E,IAAI;MAC3B,IAAI2E,QAAQ,CAAClC,KAAK,GAAG,CAAC,EACpB4C,SAAS,GAAGV,QAAQ,CAAClC,KAAK;MAC5B,MAAM6C,aAAa,GAAGH,KAAK,CAACI,KAAK,CAACC,QAAQ,CAACtF,KAAK;MAChD,OAAO;QACLgE,IAAI,EAAE;UACJQ,QAAQ,EAAEY,aAAa,GAAGD;QACpC;MACA,CAAO;IACP;EACA,CAAG;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}