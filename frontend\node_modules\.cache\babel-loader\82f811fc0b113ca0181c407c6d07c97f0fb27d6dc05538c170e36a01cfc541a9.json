{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { ref, computed, onMounted, reactive } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { ElMessage } from \"element-plus\";\nimport { Refresh } from \"@element-plus/icons-vue\";\nexport default {\n  name: \"CreditRecords\",\n  components: {\n    Refresh\n  },\n  setup() {\n    const store = useStore();\n    const loading = ref(true);\n    const filters = reactive({\n      type: \"\",\n      // positive, negative\n      date: \"\"\n    });\n    const sortBy = ref(\"created_at\");\n\n    // 类型选项\n    const typeOptions = [{\n      value: \"positive\",\n      label: \"加分记录\"\n    }, {\n      value: \"negative\",\n      label: \"扣分记录\"\n    }];\n\n    // 排序选项\n    const sortOptions = [{\n      value: \"created_at\",\n      label: \"按时间排序\"\n    }, {\n      value: \"delta\",\n      label: \"按分值排序\"\n    }, {\n      value: \"score_after\",\n      label: \"按最终分数排序\"\n    }];\n\n    // 获取信誉分记录\n    const getRecords = async () => {\n      try {\n        loading.value = true;\n        await store.dispatch(\"user/getCreditRecords\");\n      } catch (error) {\n        ElMessage.error(\"获取信誉分记录失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 刷新记录\n    const refreshRecords = () => {\n      getRecords();\n    };\n\n    // 过滤后的记录\n    const filteredRecords = computed(() => {\n      let result = store.getters[\"user/creditRecords\"];\n\n      // 类型过滤\n      if (filters.type) {\n        if (filters.type === \"positive\") {\n          result = result.filter(record => record.delta > 0);\n        } else if (filters.type === \"negative\") {\n          result = result.filter(record => record.delta < 0);\n        }\n      }\n\n      // 日期过滤\n      if (filters.date) {\n        const filterDate = new Date(filters.date);\n        filterDate.setHours(0, 0, 0, 0);\n        const nextDay = new Date(filterDate);\n        nextDay.setDate(nextDay.getDate() + 1);\n        result = result.filter(record => {\n          const createdAt = new Date(record.created_at);\n          return createdAt >= filterDate && createdAt < nextDay;\n        });\n      }\n\n      // 排序\n      result = [...result].sort((a, b) => {\n        if (sortBy.value === \"created_at\") {\n          return new Date(b.created_at) - new Date(a.created_at);\n        } else if (sortBy.value === \"delta\") {\n          return b.delta - a.delta;\n        } else if (sortBy.value === \"score_after\") {\n          return b.score_after - a.score_after;\n        }\n        return 0;\n      });\n      return result;\n    });\n\n    // 处理过滤变化\n    const handleFilterChange = () => {\n      // 过滤逻辑已在计算属性中实现\n    };\n\n    // 处理排序变化\n    const handleSortChange = () => {\n      // 排序逻辑已在计算属性中实现\n    };\n\n    // 获取记录类型\n    const getRecordType = record => {\n      return record.delta > 0 ? \"success\" : \"danger\";\n    };\n\n    // 获取记录颜色\n    const getRecordColor = record => {\n      return record.delta > 0 ? \"#67c23a\" : \"#f56c6c\";\n    };\n\n    // 获取相关实体文本\n    const getRelatedEntityText = entity => {\n      const entityMap = {\n        reservation: \"预约\",\n        check_in: \"签到\",\n        check_out: \"签退\",\n        violation: \"违规\"\n      };\n      return entityMap[entity] || entity;\n    };\n\n    // 获取操作员类型文本\n    const getOperatorTypeText = type => {\n      const typeMap = {\n        system: \"系统\",\n        admin: \"管理员\"\n      };\n      return typeMap[type] || type;\n    };\n\n    // 格式化日期\n    const formatDate = dateString => {\n      const date = new Date(dateString);\n      return date.toLocaleString(\"zh-CN\", {\n        year: \"numeric\",\n        month: \"2-digit\",\n        day: \"2-digit\",\n        hour: \"2-digit\",\n        minute: \"2-digit\",\n        second: \"2-digit\"\n      });\n    };\n    onMounted(() => {\n      getRecords();\n    });\n    return {\n      loading,\n      filters,\n      sortBy,\n      typeOptions,\n      sortOptions,\n      filteredRecords,\n      refreshRecords,\n      handleFilterChange,\n      handleSortChange,\n      getRecordType,\n      getRecordColor,\n      getRelatedEntityText,\n      getOperatorTypeText,\n      formatDate\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "reactive", "useStore", "ElMessage", "Refresh", "name", "components", "setup", "store", "loading", "filters", "type", "date", "sortBy", "typeOptions", "value", "label", "sortOptions", "getRecords", "dispatch", "error", "refreshRecords", "filteredRecords", "result", "getters", "filter", "record", "delta", "filterDate", "Date", "setHours", "nextDay", "setDate", "getDate", "createdAt", "created_at", "sort", "a", "b", "score_after", "handleFilterChange", "handleSortChange", "getRecordType", "getRecordColor", "getRelatedEntityText", "entity", "entityMap", "reservation", "check_in", "check_out", "violation", "getOperatorTypeText", "typeMap", "system", "admin", "formatDate", "dateString", "toLocaleString", "year", "month", "day", "hour", "minute", "second"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\CreditRecords.vue"], "sourcesContent": ["<template>\n  <div class=\"credit-records\">\n    <div class=\"page-header\">\n      <h2>信誉分记录</h2>\n      <div class=\"header-actions\">\n        <el-button type=\"primary\" @click=\"refreshRecords\">\n          <el-icon><Refresh /></el-icon>\n          刷新\n        </el-button>\n      </div>\n    </div>\n\n    <div class=\"filter-section\">\n      <el-card shadow=\"hover\" class=\"filter-card\">\n        <div class=\"filter-container\">\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">类型：</span>\n            <el-select\n              v-model=\"filters.type\"\n              placeholder=\"记录类型\"\n              clearable\n              @change=\"handleFilterChange\"\n            >\n              <el-option\n                v-for=\"option in typeOptions\"\n                :key=\"option.value\"\n                :label=\"option.label\"\n                :value=\"option.value\"\n              />\n            </el-select>\n          </div>\n\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">日期：</span>\n            <el-date-picker\n              v-model=\"filters.date\"\n              type=\"date\"\n              placeholder=\"选择日期\"\n              format=\"YYYY-MM-DD\"\n              value-format=\"YYYY-MM-DD\"\n              @change=\"handleFilterChange\"\n            />\n          </div>\n\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">排序：</span>\n            <el-select\n              v-model=\"sortBy\"\n              placeholder=\"排序方式\"\n              @change=\"handleSortChange\"\n            >\n              <el-option\n                v-for=\"option in sortOptions\"\n                :key=\"option.value\"\n                :label=\"option.label\"\n                :value=\"option.value\"\n              />\n            </el-select>\n          </div>\n        </div>\n      </el-card>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"3\" animated />\n      <el-skeleton :rows=\"3\" animated style=\"margin-top: 20px\" />\n    </div>\n\n    <div v-else-if=\"filteredRecords.length === 0\" class=\"empty-container\">\n      <el-empty description=\"没有找到符合条件的信誉分记录\" />\n    </div>\n\n    <div v-else class=\"records-list\">\n      <el-timeline>\n        <el-timeline-item\n          v-for=\"record in filteredRecords\"\n          :key=\"record.id\"\n          :type=\"getRecordType(record)\"\n          :color=\"getRecordColor(record)\"\n          :timestamp=\"formatDate(record.created_at)\"\n          placement=\"top\"\n        >\n          <el-card shadow=\"hover\" class=\"record-card\">\n            <div class=\"record-header\">\n              <div class=\"record-title\">\n                <span class=\"record-reason\">{{ record.reason }}</span>\n                <el-tag\n                  :type=\"record.delta > 0 ? 'success' : 'danger'\"\n                  effect=\"dark\"\n                  size=\"small\"\n                >\n                  {{ record.delta > 0 ? \"+\" : \"\" }}{{ record.delta }}分\n                </el-tag>\n              </div>\n              <div class=\"record-score\">信誉分: {{ record.score_after }}</div>\n            </div>\n\n            <div class=\"record-content\">\n              <p v-if=\"record.related_entity\" class=\"record-related\">\n                相关实体: {{ getRelatedEntityText(record.related_entity) }}\n                <span v-if=\"record.related_id\">\n                  (ID: {{ record.related_id }})\n                </span>\n              </p>\n              <p class=\"record-operator\">\n                操作类型: {{ getOperatorTypeText(record.operator_type) }}\n              </p>\n            </div>\n          </el-card>\n        </el-timeline-item>\n      </el-timeline>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted, reactive } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { ElMessage } from \"element-plus\";\nimport { Refresh } from \"@element-plus/icons-vue\";\n\nexport default {\n  name: \"CreditRecords\",\n  components: {\n    Refresh,\n  },\n  setup() {\n    const store = useStore();\n    const loading = ref(true);\n\n    const filters = reactive({\n      type: \"\", // positive, negative\n      date: \"\",\n    });\n\n    const sortBy = ref(\"created_at\");\n\n    // 类型选项\n    const typeOptions = [\n      { value: \"positive\", label: \"加分记录\" },\n      { value: \"negative\", label: \"扣分记录\" },\n    ];\n\n    // 排序选项\n    const sortOptions = [\n      { value: \"created_at\", label: \"按时间排序\" },\n      { value: \"delta\", label: \"按分值排序\" },\n      { value: \"score_after\", label: \"按最终分数排序\" },\n    ];\n\n    // 获取信誉分记录\n    const getRecords = async () => {\n      try {\n        loading.value = true;\n        await store.dispatch(\"user/getCreditRecords\");\n      } catch (error) {\n        ElMessage.error(\"获取信誉分记录失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 刷新记录\n    const refreshRecords = () => {\n      getRecords();\n    };\n\n    // 过滤后的记录\n    const filteredRecords = computed(() => {\n      let result = store.getters[\"user/creditRecords\"];\n\n      // 类型过滤\n      if (filters.type) {\n        if (filters.type === \"positive\") {\n          result = result.filter((record) => record.delta > 0);\n        } else if (filters.type === \"negative\") {\n          result = result.filter((record) => record.delta < 0);\n        }\n      }\n\n      // 日期过滤\n      if (filters.date) {\n        const filterDate = new Date(filters.date);\n        filterDate.setHours(0, 0, 0, 0);\n\n        const nextDay = new Date(filterDate);\n        nextDay.setDate(nextDay.getDate() + 1);\n\n        result = result.filter((record) => {\n          const createdAt = new Date(record.created_at);\n          return createdAt >= filterDate && createdAt < nextDay;\n        });\n      }\n\n      // 排序\n      result = [...result].sort((a, b) => {\n        if (sortBy.value === \"created_at\") {\n          return new Date(b.created_at) - new Date(a.created_at);\n        } else if (sortBy.value === \"delta\") {\n          return b.delta - a.delta;\n        } else if (sortBy.value === \"score_after\") {\n          return b.score_after - a.score_after;\n        }\n        return 0;\n      });\n\n      return result;\n    });\n\n    // 处理过滤变化\n    const handleFilterChange = () => {\n      // 过滤逻辑已在计算属性中实现\n    };\n\n    // 处理排序变化\n    const handleSortChange = () => {\n      // 排序逻辑已在计算属性中实现\n    };\n\n    // 获取记录类型\n    const getRecordType = (record) => {\n      return record.delta > 0 ? \"success\" : \"danger\";\n    };\n\n    // 获取记录颜色\n    const getRecordColor = (record) => {\n      return record.delta > 0 ? \"#67c23a\" : \"#f56c6c\";\n    };\n\n    // 获取相关实体文本\n    const getRelatedEntityText = (entity) => {\n      const entityMap = {\n        reservation: \"预约\",\n        check_in: \"签到\",\n        check_out: \"签退\",\n        violation: \"违规\",\n      };\n      return entityMap[entity] || entity;\n    };\n\n    // 获取操作员类型文本\n    const getOperatorTypeText = (type) => {\n      const typeMap = {\n        system: \"系统\",\n        admin: \"管理员\",\n      };\n      return typeMap[type] || type;\n    };\n\n    // 格式化日期\n    const formatDate = (dateString) => {\n      const date = new Date(dateString);\n      return date.toLocaleString(\"zh-CN\", {\n        year: \"numeric\",\n        month: \"2-digit\",\n        day: \"2-digit\",\n        hour: \"2-digit\",\n        minute: \"2-digit\",\n        second: \"2-digit\",\n      });\n    };\n\n    onMounted(() => {\n      getRecords();\n    });\n\n    return {\n      loading,\n      filters,\n      sortBy,\n      typeOptions,\n      sortOptions,\n      filteredRecords,\n      refreshRecords,\n      handleFilterChange,\n      handleSortChange,\n      getRecordType,\n      getRecordColor,\n      getRelatedEntityText,\n      getOperatorTypeText,\n      formatDate,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.credit-records {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  h2 {\n    margin: 0;\n  }\n}\n\n.filter-section {\n  margin-bottom: 20px;\n}\n\n.filter-container {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n\n.filter-item {\n  display: flex;\n  align-items: center;\n}\n\n.filter-label {\n  margin-right: 8px;\n  white-space: nowrap;\n}\n\n.loading-container {\n  padding: 20px;\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.empty-container {\n  padding: 40px 0;\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.records-list {\n  padding: 20px;\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.record-card {\n  margin-bottom: 10px;\n}\n\n.record-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.record-title {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.record-reason {\n  font-weight: bold;\n}\n\n.record-content {\n  color: #606266;\n  font-size: 14px;\n}\n\n.record-related,\n.record-operator {\n  margin: 5px 0;\n}\n</style>\n"], "mappings": ";;AAoHA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAO,QAAS,KAAK;AACxD,SAASC,QAAO,QAAS,MAAM;AAC/B,SAASC,SAAQ,QAAS,cAAc;AACxC,SAASC,OAAM,QAAS,yBAAyB;AAEjD,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIN,QAAQ,CAAC,CAAC;IACxB,MAAMO,OAAM,GAAIX,GAAG,CAAC,IAAI,CAAC;IAEzB,MAAMY,OAAM,GAAIT,QAAQ,CAAC;MACvBU,IAAI,EAAE,EAAE;MAAE;MACVC,IAAI,EAAE;IACR,CAAC,CAAC;IAEF,MAAMC,MAAK,GAAIf,GAAG,CAAC,YAAY,CAAC;;IAEhC;IACA,MAAMgB,WAAU,GAAI,CAClB;MAAEC,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAO,CAAC,EACpC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAO,CAAC,CACrC;;IAED;IACA,MAAMC,WAAU,GAAI,CAClB;MAAEF,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAQ,CAAC,EACvC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAC,EAClC;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAU,CAAC,CAC3C;;IAED;IACA,MAAME,UAAS,GAAI,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACFT,OAAO,CAACM,KAAI,GAAI,IAAI;QACpB,MAAMP,KAAK,CAACW,QAAQ,CAAC,uBAAuB,CAAC;MAC/C,EAAE,OAAOC,KAAK,EAAE;QACdjB,SAAS,CAACiB,KAAK,CAAC,WAAW,CAAC;MAC9B,UAAU;QACRX,OAAO,CAACM,KAAI,GAAI,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAMM,cAAa,GAAIA,CAAA,KAAM;MAC3BH,UAAU,CAAC,CAAC;IACd,CAAC;;IAED;IACA,MAAMI,eAAc,GAAIvB,QAAQ,CAAC,MAAM;MACrC,IAAIwB,MAAK,GAAIf,KAAK,CAACgB,OAAO,CAAC,oBAAoB,CAAC;;MAEhD;MACA,IAAId,OAAO,CAACC,IAAI,EAAE;QAChB,IAAID,OAAO,CAACC,IAAG,KAAM,UAAU,EAAE;UAC/BY,MAAK,GAAIA,MAAM,CAACE,MAAM,CAAEC,MAAM,IAAKA,MAAM,CAACC,KAAI,GAAI,CAAC,CAAC;QACtD,OAAO,IAAIjB,OAAO,CAACC,IAAG,KAAM,UAAU,EAAE;UACtCY,MAAK,GAAIA,MAAM,CAACE,MAAM,CAAEC,MAAM,IAAKA,MAAM,CAACC,KAAI,GAAI,CAAC,CAAC;QACtD;MACF;;MAEA;MACA,IAAIjB,OAAO,CAACE,IAAI,EAAE;QAChB,MAAMgB,UAAS,GAAI,IAAIC,IAAI,CAACnB,OAAO,CAACE,IAAI,CAAC;QACzCgB,UAAU,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAE/B,MAAMC,OAAM,GAAI,IAAIF,IAAI,CAACD,UAAU,CAAC;QACpCG,OAAO,CAACC,OAAO,CAACD,OAAO,CAACE,OAAO,CAAC,IAAI,CAAC,CAAC;QAEtCV,MAAK,GAAIA,MAAM,CAACE,MAAM,CAAEC,MAAM,IAAK;UACjC,MAAMQ,SAAQ,GAAI,IAAIL,IAAI,CAACH,MAAM,CAACS,UAAU,CAAC;UAC7C,OAAOD,SAAQ,IAAKN,UAAS,IAAKM,SAAQ,GAAIH,OAAO;QACvD,CAAC,CAAC;MACJ;;MAEA;MACAR,MAAK,GAAI,CAAC,GAAGA,MAAM,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAClC,IAAIzB,MAAM,CAACE,KAAI,KAAM,YAAY,EAAE;UACjC,OAAO,IAAIc,IAAI,CAACS,CAAC,CAACH,UAAU,IAAI,IAAIN,IAAI,CAACQ,CAAC,CAACF,UAAU,CAAC;QACxD,OAAO,IAAItB,MAAM,CAACE,KAAI,KAAM,OAAO,EAAE;UACnC,OAAOuB,CAAC,CAACX,KAAI,GAAIU,CAAC,CAACV,KAAK;QAC1B,OAAO,IAAId,MAAM,CAACE,KAAI,KAAM,aAAa,EAAE;UACzC,OAAOuB,CAAC,CAACC,WAAU,GAAIF,CAAC,CAACE,WAAW;QACtC;QACA,OAAO,CAAC;MACV,CAAC,CAAC;MAEF,OAAOhB,MAAM;IACf,CAAC,CAAC;;IAEF;IACA,MAAMiB,kBAAiB,GAAIA,CAAA,KAAM;MAC/B;IAAA,CACD;;IAED;IACA,MAAMC,gBAAe,GAAIA,CAAA,KAAM;MAC7B;IAAA,CACD;;IAED;IACA,MAAMC,aAAY,GAAKhB,MAAM,IAAK;MAChC,OAAOA,MAAM,CAACC,KAAI,GAAI,IAAI,SAAQ,GAAI,QAAQ;IAChD,CAAC;;IAED;IACA,MAAMgB,cAAa,GAAKjB,MAAM,IAAK;MACjC,OAAOA,MAAM,CAACC,KAAI,GAAI,IAAI,SAAQ,GAAI,SAAS;IACjD,CAAC;;IAED;IACA,MAAMiB,oBAAmB,GAAKC,MAAM,IAAK;MACvC,MAAMC,SAAQ,GAAI;QAChBC,WAAW,EAAE,IAAI;QACjBC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE;MACb,CAAC;MACD,OAAOJ,SAAS,CAACD,MAAM,KAAKA,MAAM;IACpC,CAAC;;IAED;IACA,MAAMM,mBAAkB,GAAKxC,IAAI,IAAK;MACpC,MAAMyC,OAAM,GAAI;QACdC,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE;MACT,CAAC;MACD,OAAOF,OAAO,CAACzC,IAAI,KAAKA,IAAI;IAC9B,CAAC;;IAED;IACA,MAAM4C,UAAS,GAAKC,UAAU,IAAK;MACjC,MAAM5C,IAAG,GAAI,IAAIiB,IAAI,CAAC2B,UAAU,CAAC;MACjC,OAAO5C,IAAI,CAAC6C,cAAc,CAAC,OAAO,EAAE;QAClCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,SAAS;QAChBC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IAED/D,SAAS,CAAC,MAAM;MACdkB,UAAU,CAAC,CAAC;IACd,CAAC,CAAC;IAEF,OAAO;MACLT,OAAO;MACPC,OAAO;MACPG,MAAM;MACNC,WAAW;MACXG,WAAW;MACXK,eAAe;MACfD,cAAc;MACdmB,kBAAkB;MAClBC,gBAAgB;MAChBC,aAAa;MACbC,cAAc;MACdC,oBAAoB;MACpBO,mBAAmB;MACnBI;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}