{"ast": null, "code": "import { defineComponent, ref, reactive, computed, watch, onMounted, onUpdated, openBlock, createElementBlock, normalizeClass, unref, withModifiers, withDirectives, withKeys, renderSlot, createVNode, withCtx, createBlock, createCommentVNode, createSlots } from 'vue';\nimport { isNil } from 'lodash-unified';\nimport { ElInput } from '../../input/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { ArrowDown, Minus, ArrowUp, Plus } from '@element-plus/icons-vue';\nimport { inputNumberProps, inputNumberEmits } from './input-number.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { vRepeatClick } from '../../../directives/repeat-click/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormItem } from '../../form/src/hooks/use-form-item.mjs';\nimport { isNumber, isUndefined } from '../../../utils/types.mjs';\nimport { debugWarn, throwError } from '../../../utils/error.mjs';\nimport { useFormSize, useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { isString } from '@vue/shared';\nimport { isFirefox } from '../../../utils/browser.mjs';\nconst __default__ = defineComponent({\n  name: \"ElInputNumber\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: inputNumberProps,\n  emits: inputNumberEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"input-number\");\n    const input = ref();\n    const data = reactive({\n      currentValue: props.modelValue,\n      userInput: null\n    });\n    const {\n      formItem\n    } = useFormItem();\n    const minDisabled = computed(() => isNumber(props.modelValue) && props.modelValue <= props.min);\n    const maxDisabled = computed(() => isNumber(props.modelValue) && props.modelValue >= props.max);\n    const numPrecision = computed(() => {\n      const stepPrecision = getPrecision(props.step);\n      if (!isUndefined(props.precision)) {\n        if (stepPrecision > props.precision) {\n          debugWarn(\"InputNumber\", \"precision should not be less than the decimal places of step\");\n        }\n        return props.precision;\n      } else {\n        return Math.max(getPrecision(props.modelValue), stepPrecision);\n      }\n    });\n    const controlsAtRight = computed(() => {\n      return props.controls && props.controlsPosition === \"right\";\n    });\n    const inputNumberSize = useFormSize();\n    const inputNumberDisabled = useFormDisabled();\n    const displayValue = computed(() => {\n      if (data.userInput !== null) {\n        return data.userInput;\n      }\n      let currentValue = data.currentValue;\n      if (isNil(currentValue)) return \"\";\n      if (isNumber(currentValue)) {\n        if (Number.isNaN(currentValue)) return \"\";\n        if (!isUndefined(props.precision)) {\n          currentValue = currentValue.toFixed(props.precision);\n        }\n      }\n      return currentValue;\n    });\n    const toPrecision = (num, pre) => {\n      if (isUndefined(pre)) pre = numPrecision.value;\n      if (pre === 0) return Math.round(num);\n      let snum = String(num);\n      const pointPos = snum.indexOf(\".\");\n      if (pointPos === -1) return num;\n      const nums = snum.replace(\".\", \"\").split(\"\");\n      const datum = nums[pointPos + pre];\n      if (!datum) return num;\n      const length = snum.length;\n      if (snum.charAt(length - 1) === \"5\") {\n        snum = `${snum.slice(0, Math.max(0, length - 1))}6`;\n      }\n      return Number.parseFloat(Number(snum).toFixed(pre));\n    };\n    const getPrecision = value => {\n      if (isNil(value)) return 0;\n      const valueString = value.toString();\n      const dotPosition = valueString.indexOf(\".\");\n      let precision = 0;\n      if (dotPosition !== -1) {\n        precision = valueString.length - dotPosition - 1;\n      }\n      return precision;\n    };\n    const ensurePrecision = (val, coefficient = 1) => {\n      if (!isNumber(val)) return data.currentValue;\n      return toPrecision(val + props.step * coefficient);\n    };\n    const increase = () => {\n      if (props.readonly || inputNumberDisabled.value || maxDisabled.value) return;\n      const value = Number(displayValue.value) || 0;\n      const newVal = ensurePrecision(value);\n      setCurrentValue(newVal);\n      emit(INPUT_EVENT, data.currentValue);\n      setCurrentValueToModelValue();\n    };\n    const decrease = () => {\n      if (props.readonly || inputNumberDisabled.value || minDisabled.value) return;\n      const value = Number(displayValue.value) || 0;\n      const newVal = ensurePrecision(value, -1);\n      setCurrentValue(newVal);\n      emit(INPUT_EVENT, data.currentValue);\n      setCurrentValueToModelValue();\n    };\n    const verifyValue = (value, update) => {\n      const {\n        max,\n        min,\n        step,\n        precision,\n        stepStrictly,\n        valueOnClear\n      } = props;\n      if (max < min) {\n        throwError(\"InputNumber\", \"min should not be greater than max.\");\n      }\n      let newVal = Number(value);\n      if (isNil(value) || Number.isNaN(newVal)) {\n        return null;\n      }\n      if (value === \"\") {\n        if (valueOnClear === null) {\n          return null;\n        }\n        newVal = isString(valueOnClear) ? {\n          min,\n          max\n        }[valueOnClear] : valueOnClear;\n      }\n      if (stepStrictly) {\n        newVal = toPrecision(Math.round(newVal / step) * step, precision);\n        if (newVal !== value) {\n          update && emit(UPDATE_MODEL_EVENT, newVal);\n        }\n      }\n      if (!isUndefined(precision)) {\n        newVal = toPrecision(newVal, precision);\n      }\n      if (newVal > max || newVal < min) {\n        newVal = newVal > max ? max : min;\n        update && emit(UPDATE_MODEL_EVENT, newVal);\n      }\n      return newVal;\n    };\n    const setCurrentValue = (value, emitChange = true) => {\n      var _a;\n      const oldVal = data.currentValue;\n      const newVal = verifyValue(value);\n      if (!emitChange) {\n        emit(UPDATE_MODEL_EVENT, newVal);\n        return;\n      }\n      if (oldVal === newVal && value) return;\n      data.userInput = null;\n      emit(UPDATE_MODEL_EVENT, newVal);\n      if (oldVal !== newVal) {\n        emit(CHANGE_EVENT, newVal, oldVal);\n      }\n      if (props.validateEvent) {\n        (_a = formItem == null ? void 0 : formItem.validate) == null ? void 0 : _a.call(formItem, \"change\").catch(err => debugWarn(err));\n      }\n      data.currentValue = newVal;\n    };\n    const handleInput = value => {\n      data.userInput = value;\n      const newVal = value === \"\" ? null : Number(value);\n      emit(INPUT_EVENT, newVal);\n      setCurrentValue(newVal, false);\n    };\n    const handleInputChange = value => {\n      const newVal = value !== \"\" ? Number(value) : \"\";\n      if (isNumber(newVal) && !Number.isNaN(newVal) || value === \"\") {\n        setCurrentValue(newVal);\n      }\n      setCurrentValueToModelValue();\n      data.userInput = null;\n    };\n    const focus = () => {\n      var _a, _b;\n      (_b = (_a = input.value) == null ? void 0 : _a.focus) == null ? void 0 : _b.call(_a);\n    };\n    const blur = () => {\n      var _a, _b;\n      (_b = (_a = input.value) == null ? void 0 : _a.blur) == null ? void 0 : _b.call(_a);\n    };\n    const handleFocus = event => {\n      emit(\"focus\", event);\n    };\n    const handleBlur = event => {\n      var _a, _b;\n      data.userInput = null;\n      if (isFirefox() && data.currentValue === null && ((_a = input.value) == null ? void 0 : _a.input)) {\n        input.value.input.value = \"\";\n      }\n      emit(\"blur\", event);\n      if (props.validateEvent) {\n        (_b = formItem == null ? void 0 : formItem.validate) == null ? void 0 : _b.call(formItem, \"blur\").catch(err => debugWarn(err));\n      }\n    };\n    const setCurrentValueToModelValue = () => {\n      if (data.currentValue !== props.modelValue) {\n        data.currentValue = props.modelValue;\n      }\n    };\n    const handleWheel = e => {\n      if (document.activeElement === e.target) e.preventDefault();\n    };\n    watch(() => props.modelValue, (value, oldValue) => {\n      const newValue = verifyValue(value, true);\n      if (data.userInput === null && newValue !== oldValue) {\n        data.currentValue = newValue;\n      }\n    }, {\n      immediate: true\n    });\n    onMounted(() => {\n      var _a;\n      const {\n        min,\n        max,\n        modelValue\n      } = props;\n      const innerInput = (_a = input.value) == null ? void 0 : _a.input;\n      innerInput.setAttribute(\"role\", \"spinbutton\");\n      if (Number.isFinite(max)) {\n        innerInput.setAttribute(\"aria-valuemax\", String(max));\n      } else {\n        innerInput.removeAttribute(\"aria-valuemax\");\n      }\n      if (Number.isFinite(min)) {\n        innerInput.setAttribute(\"aria-valuemin\", String(min));\n      } else {\n        innerInput.removeAttribute(\"aria-valuemin\");\n      }\n      innerInput.setAttribute(\"aria-valuenow\", data.currentValue || data.currentValue === 0 ? String(data.currentValue) : \"\");\n      innerInput.setAttribute(\"aria-disabled\", String(inputNumberDisabled.value));\n      if (!isNumber(modelValue) && modelValue != null) {\n        let val = Number(modelValue);\n        if (Number.isNaN(val)) {\n          val = null;\n        }\n        emit(UPDATE_MODEL_EVENT, val);\n      }\n      innerInput.addEventListener(\"wheel\", handleWheel, {\n        passive: false\n      });\n    });\n    onUpdated(() => {\n      var _a, _b;\n      const innerInput = (_a = input.value) == null ? void 0 : _a.input;\n      innerInput == null ? void 0 : innerInput.setAttribute(\"aria-valuenow\", `${(_b = data.currentValue) != null ? _b : \"\"}`);\n    });\n    expose({\n      focus,\n      blur\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(), unref(ns).m(unref(inputNumberSize)), unref(ns).is(\"disabled\", unref(inputNumberDisabled)), unref(ns).is(\"without-controls\", !_ctx.controls), unref(ns).is(\"controls-right\", unref(controlsAtRight))]),\n        onDragstart: withModifiers(() => {}, [\"prevent\"])\n      }, [_ctx.controls ? withDirectives((openBlock(), createElementBlock(\"span\", {\n        key: 0,\n        role: \"button\",\n        \"aria-label\": unref(t)(\"el.inputNumber.decrease\"),\n        class: normalizeClass([unref(ns).e(\"decrease\"), unref(ns).is(\"disabled\", unref(minDisabled))]),\n        onKeydown: withKeys(decrease, [\"enter\"])\n      }, [renderSlot(_ctx.$slots, \"decrease-icon\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [unref(controlsAtRight) ? (openBlock(), createBlock(unref(ArrowDown), {\n          key: 0\n        })) : (openBlock(), createBlock(unref(Minus), {\n          key: 1\n        }))]),\n        _: 1\n      })])], 42, [\"aria-label\", \"onKeydown\"])), [[unref(vRepeatClick), decrease]]) : createCommentVNode(\"v-if\", true), _ctx.controls ? withDirectives((openBlock(), createElementBlock(\"span\", {\n        key: 1,\n        role: \"button\",\n        \"aria-label\": unref(t)(\"el.inputNumber.increase\"),\n        class: normalizeClass([unref(ns).e(\"increase\"), unref(ns).is(\"disabled\", unref(maxDisabled))]),\n        onKeydown: withKeys(increase, [\"enter\"])\n      }, [renderSlot(_ctx.$slots, \"increase-icon\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [unref(controlsAtRight) ? (openBlock(), createBlock(unref(ArrowUp), {\n          key: 0\n        })) : (openBlock(), createBlock(unref(Plus), {\n          key: 1\n        }))]),\n        _: 1\n      })])], 42, [\"aria-label\", \"onKeydown\"])), [[unref(vRepeatClick), increase]]) : createCommentVNode(\"v-if\", true), createVNode(unref(ElInput), {\n        id: _ctx.id,\n        ref_key: \"input\",\n        ref: input,\n        type: \"number\",\n        step: _ctx.step,\n        \"model-value\": unref(displayValue),\n        placeholder: _ctx.placeholder,\n        readonly: _ctx.readonly,\n        disabled: unref(inputNumberDisabled),\n        size: unref(inputNumberSize),\n        max: _ctx.max,\n        min: _ctx.min,\n        name: _ctx.name,\n        \"aria-label\": _ctx.ariaLabel,\n        \"validate-event\": false,\n        onKeydown: [withKeys(withModifiers(increase, [\"prevent\"]), [\"up\"]), withKeys(withModifiers(decrease, [\"prevent\"]), [\"down\"])],\n        onBlur: handleBlur,\n        onFocus: handleFocus,\n        onInput: handleInput,\n        onChange: handleInputChange\n      }, createSlots({\n        _: 2\n      }, [_ctx.$slots.prefix ? {\n        name: \"prefix\",\n        fn: withCtx(() => [renderSlot(_ctx.$slots, \"prefix\")])\n      } : void 0, _ctx.$slots.suffix ? {\n        name: \"suffix\",\n        fn: withCtx(() => [renderSlot(_ctx.$slots, \"suffix\")])\n      } : void 0]), 1032, [\"id\", \"step\", \"model-value\", \"placeholder\", \"readonly\", \"disabled\", \"size\", \"max\", \"min\", \"name\", \"aria-label\", \"onKeydown\"])], 42, [\"onDragstart\"]);\n    };\n  }\n});\nvar InputNumber = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"input-number.vue\"]]);\nexport { InputNumber as default };", "map": {"version": 3, "names": ["name", "t", "useLocale", "ns", "useNamespace", "input", "ref", "data", "reactive", "currentValue", "props", "modelValue", "userInput", "formItem", "useFormItem", "minDisabled", "computed", "isNumber", "min", "maxDisabled", "max", "numPrecision", "stepPrecision", "getPrecision", "step", "isUndefined", "precision", "debugWarn", "Math", "controlsAtRight", "controls", "controlsPosition", "inputNumberSize", "useFormSize", "inputNumberDisabled", "useFormDisabled", "displayValue", "isNil", "Number", "isNaN", "toFixed", "toPrecision", "num", "pre", "value", "round", "snum", "String", "pointPos", "indexOf", "nums", "replace", "split", "datum", "length", "char<PERSON>t", "slice", "parseFloat", "valueString", "toString", "dotPosition", "ensurePrecision", "val", "coefficient", "increase", "readonly", "newVal", "setCurrentValue", "emit", "INPUT_EVENT", "setCurrentValueToModelValue", "decrease", "verifyValue", "update", "stepStrictly", "valueOnClear", "throwError", "isString", "UPDATE_MODEL_EVENT", "emitChange", "_a", "oldVal", "CHANGE_EVENT", "validateEvent", "validate", "call", "catch", "err", "handleInput", "handleInputChange", "focus", "_b", "blur", "handleFocus", "event", "handleBlur", "isFirefox", "handleWheel", "e", "document", "activeElement", "target", "preventDefault", "watch", "oldValue", "newValue", "immediate", "onMounted", "innerInput", "setAttribute", "isFinite", "removeAttribute", "addEventListener", "passive", "onUpdated", "expose"], "sources": ["../../../../../../packages/components/input-number/src/input-number.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ns.b(),\n      ns.m(inputNumberSize),\n      ns.is('disabled', inputNumberDisabled),\n      ns.is('without-controls', !controls),\n      ns.is('controls-right', controlsAtRight),\n    ]\"\n    @dragstart.prevent\n  >\n    <span\n      v-if=\"controls\"\n      v-repeat-click=\"decrease\"\n      role=\"button\"\n      :aria-label=\"t('el.inputNumber.decrease')\"\n      :class=\"[ns.e('decrease'), ns.is('disabled', minDisabled)]\"\n      @keydown.enter=\"decrease\"\n    >\n      <slot name=\"decrease-icon\">\n        <el-icon>\n          <arrow-down v-if=\"controlsAtRight\" />\n          <minus v-else />\n        </el-icon>\n      </slot>\n    </span>\n    <span\n      v-if=\"controls\"\n      v-repeat-click=\"increase\"\n      role=\"button\"\n      :aria-label=\"t('el.inputNumber.increase')\"\n      :class=\"[ns.e('increase'), ns.is('disabled', maxDisabled)]\"\n      @keydown.enter=\"increase\"\n    >\n      <slot name=\"increase-icon\">\n        <el-icon>\n          <arrow-up v-if=\"controlsAtRight\" />\n          <plus v-else />\n        </el-icon>\n      </slot>\n    </span>\n    <el-input\n      :id=\"id\"\n      ref=\"input\"\n      type=\"number\"\n      :step=\"step\"\n      :model-value=\"displayValue\"\n      :placeholder=\"placeholder\"\n      :readonly=\"readonly\"\n      :disabled=\"inputNumberDisabled\"\n      :size=\"inputNumberSize\"\n      :max=\"max\"\n      :min=\"min\"\n      :name=\"name\"\n      :aria-label=\"ariaLabel\"\n      :validate-event=\"false\"\n      @keydown.up.prevent=\"increase\"\n      @keydown.down.prevent=\"decrease\"\n      @blur=\"handleBlur\"\n      @focus=\"handleFocus\"\n      @input=\"handleInput\"\n      @change=\"handleInputChange\"\n    >\n      <template v-if=\"$slots.prefix\" #prefix>\n        <slot name=\"prefix\" />\n      </template>\n      <template v-if=\"$slots.suffix\" #suffix>\n        <slot name=\"suffix\" />\n      </template>\n    </el-input>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, onMounted, onUpdated, reactive, ref, watch } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { ElInput } from '@element-plus/components/input'\nimport { ElIcon } from '@element-plus/components/icon'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormSize,\n} from '@element-plus/components/form'\nimport { vRepeatClick } from '@element-plus/directives'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport {\n  debugWarn,\n  isFirefox,\n  isNumber,\n  isString,\n  isUndefined,\n  throwError,\n} from '@element-plus/utils'\nimport { ArrowDown, ArrowUp, Minus, Plus } from '@element-plus/icons-vue'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { inputNumberEmits, inputNumberProps } from './input-number'\n\nimport type { InputInstance } from '@element-plus/components/input'\n\ndefineOptions({\n  name: 'ElInputNumber',\n})\n\nconst props = defineProps(inputNumberProps)\nconst emit = defineEmits(inputNumberEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('input-number')\nconst input = ref<InputInstance>()\n\ninterface Data {\n  currentValue: number | null | undefined\n  userInput: null | number | string\n}\nconst data = reactive<Data>({\n  currentValue: props.modelValue,\n  userInput: null,\n})\n\nconst { formItem } = useFormItem()\n\nconst minDisabled = computed(\n  () => isNumber(props.modelValue) && props.modelValue <= props.min\n)\nconst maxDisabled = computed(\n  () => isNumber(props.modelValue) && props.modelValue >= props.max\n)\n\nconst numPrecision = computed(() => {\n  const stepPrecision = getPrecision(props.step)\n  if (!isUndefined(props.precision)) {\n    if (stepPrecision > props.precision) {\n      debugWarn(\n        'InputNumber',\n        'precision should not be less than the decimal places of step'\n      )\n    }\n    return props.precision\n  } else {\n    return Math.max(getPrecision(props.modelValue), stepPrecision)\n  }\n})\nconst controlsAtRight = computed(() => {\n  return props.controls && props.controlsPosition === 'right'\n})\n\nconst inputNumberSize = useFormSize()\nconst inputNumberDisabled = useFormDisabled()\n\nconst displayValue = computed(() => {\n  if (data.userInput !== null) {\n    return data.userInput\n  }\n  let currentValue: number | string | undefined | null = data.currentValue\n  if (isNil(currentValue)) return ''\n  if (isNumber(currentValue)) {\n    if (Number.isNaN(currentValue)) return ''\n    if (!isUndefined(props.precision)) {\n      currentValue = currentValue.toFixed(props.precision)\n    }\n  }\n  return currentValue\n})\nconst toPrecision = (num: number, pre?: number) => {\n  if (isUndefined(pre)) pre = numPrecision.value\n  if (pre === 0) return Math.round(num)\n  let snum = String(num)\n  const pointPos = snum.indexOf('.')\n  if (pointPos === -1) return num\n  const nums = snum.replace('.', '').split('')\n  const datum = nums[pointPos + pre]\n  if (!datum) return num\n  const length = snum.length\n  if (snum.charAt(length - 1) === '5') {\n    snum = `${snum.slice(0, Math.max(0, length - 1))}6`\n  }\n  return Number.parseFloat(Number(snum).toFixed(pre))\n}\nconst getPrecision = (value: number | null | undefined) => {\n  if (isNil(value)) return 0\n  const valueString = value.toString()\n  const dotPosition = valueString.indexOf('.')\n  let precision = 0\n  if (dotPosition !== -1) {\n    precision = valueString.length - dotPosition - 1\n  }\n  return precision\n}\nconst ensurePrecision = (val: number, coefficient: 1 | -1 = 1) => {\n  if (!isNumber(val)) return data.currentValue\n  // Solve the accuracy problem of JS decimal calculation by converting the value to integer.\n  return toPrecision(val + props.step * coefficient)\n}\nconst increase = () => {\n  if (props.readonly || inputNumberDisabled.value || maxDisabled.value) return\n  const value = Number(displayValue.value) || 0\n  const newVal = ensurePrecision(value)\n  setCurrentValue(newVal)\n  emit(INPUT_EVENT, data.currentValue)\n  setCurrentValueToModelValue()\n}\nconst decrease = () => {\n  if (props.readonly || inputNumberDisabled.value || minDisabled.value) return\n  const value = Number(displayValue.value) || 0\n  const newVal = ensurePrecision(value, -1)\n  setCurrentValue(newVal)\n  emit(INPUT_EVENT, data.currentValue)\n  setCurrentValueToModelValue()\n}\nconst verifyValue = (\n  value: number | string | null | undefined,\n  update?: boolean\n): number | null | undefined => {\n  const { max, min, step, precision, stepStrictly, valueOnClear } = props\n  if (max < min) {\n    throwError('InputNumber', 'min should not be greater than max.')\n  }\n  let newVal = Number(value)\n  if (isNil(value) || Number.isNaN(newVal)) {\n    return null\n  }\n  if (value === '') {\n    if (valueOnClear === null) {\n      return null\n    }\n    newVal = isString(valueOnClear) ? { min, max }[valueOnClear] : valueOnClear\n  }\n  if (stepStrictly) {\n    newVal = toPrecision(Math.round(newVal / step) * step, precision)\n    if (newVal !== value) {\n      update && emit(UPDATE_MODEL_EVENT, newVal)\n    }\n  }\n  if (!isUndefined(precision)) {\n    newVal = toPrecision(newVal, precision)\n  }\n  if (newVal > max || newVal < min) {\n    newVal = newVal > max ? max : min\n    update && emit(UPDATE_MODEL_EVENT, newVal)\n  }\n  return newVal\n}\nconst setCurrentValue = (\n  value: number | string | null | undefined,\n  emitChange = true\n) => {\n  const oldVal = data.currentValue\n  const newVal = verifyValue(value)\n  if (!emitChange) {\n    emit(UPDATE_MODEL_EVENT, newVal!)\n    return\n  }\n  if (oldVal === newVal && value) return\n  data.userInput = null\n  emit(UPDATE_MODEL_EVENT, newVal!)\n  if (oldVal !== newVal) {\n    emit(CHANGE_EVENT, newVal!, oldVal!)\n  }\n  if (props.validateEvent) {\n    formItem?.validate?.('change').catch((err) => debugWarn(err))\n  }\n  data.currentValue = newVal\n}\nconst handleInput = (value: string) => {\n  data.userInput = value\n  const newVal = value === '' ? null : Number(value)\n  emit(INPUT_EVENT, newVal)\n  setCurrentValue(newVal, false)\n}\nconst handleInputChange = (value: string) => {\n  const newVal = value !== '' ? Number(value) : ''\n  if ((isNumber(newVal) && !Number.isNaN(newVal)) || value === '') {\n    setCurrentValue(newVal)\n  }\n  setCurrentValueToModelValue()\n  data.userInput = null\n}\n\nconst focus = () => {\n  input.value?.focus?.()\n}\n\nconst blur = () => {\n  input.value?.blur?.()\n}\n\nconst handleFocus = (event: MouseEvent | FocusEvent) => {\n  emit('focus', event)\n}\n\nconst handleBlur = (event: MouseEvent | FocusEvent) => {\n  data.userInput = null\n  // This is a Firefox-specific problem. When non-numeric content is entered into a numeric input box,\n  // the content displayed on the page is not cleared after the value is cleared. #18533\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1398528\n  if (isFirefox() && data.currentValue === null && input.value?.input) {\n    input.value.input.value = ''\n  }\n  emit('blur', event)\n  if (props.validateEvent) {\n    formItem?.validate?.('blur').catch((err) => debugWarn(err))\n  }\n}\n\nconst setCurrentValueToModelValue = () => {\n  if (data.currentValue !== props.modelValue) {\n    data.currentValue = props.modelValue\n  }\n}\nconst handleWheel = (e: WheelEvent) => {\n  if (document.activeElement === e.target) e.preventDefault()\n}\n\nwatch(\n  () => props.modelValue,\n  (value, oldValue) => {\n    const newValue = verifyValue(value, true)\n    if (data.userInput === null && newValue !== oldValue) {\n      data.currentValue = newValue\n    }\n  },\n  { immediate: true }\n)\nonMounted(() => {\n  const { min, max, modelValue } = props\n  const innerInput = input.value?.input as HTMLInputElement\n  innerInput.setAttribute('role', 'spinbutton')\n  if (Number.isFinite(max)) {\n    innerInput.setAttribute('aria-valuemax', String(max))\n  } else {\n    innerInput.removeAttribute('aria-valuemax')\n  }\n  if (Number.isFinite(min)) {\n    innerInput.setAttribute('aria-valuemin', String(min))\n  } else {\n    innerInput.removeAttribute('aria-valuemin')\n  }\n  innerInput.setAttribute(\n    'aria-valuenow',\n    data.currentValue || data.currentValue === 0\n      ? String(data.currentValue)\n      : ''\n  )\n  innerInput.setAttribute('aria-disabled', String(inputNumberDisabled.value))\n  if (!isNumber(modelValue) && modelValue != null) {\n    let val: number | null = Number(modelValue)\n    if (Number.isNaN(val)) {\n      val = null\n    }\n    emit(UPDATE_MODEL_EVENT, val!)\n  }\n  innerInput.addEventListener('wheel', handleWheel, { passive: false })\n})\nonUpdated(() => {\n  const innerInput = input.value?.input\n  innerInput?.setAttribute('aria-valuenow', `${data.currentValue ?? ''}`)\n})\ndefineExpose({\n  /** @description get focus the input component */\n  focus,\n  /** @description remove focus the input component */\n  blur,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;mCAuGc;EACZA,IAAM;AACR;;;;;;;;;;IAKM;MAAEC;IAAE,IAAIC,SAAU;IAClB,MAAAC,EAAA,GAAKC,YAAA,CAAa,cAAc;IACtC,MAAMC,KAAA,GAAQC,GAAmB;IAMjC,MAAMC,IAAA,GAAOC,QAAe;MAC1BC,YAAA,EAAcC,KAAM,CAAAC,UAAA;MACpBC,SAAW;IAAA,CACZ;IAEK;MAAEC;IAAS,IAAIC,WAAY;IAEjC,MAAMC,WAAc,GAAAC,QAAA,OAAAC,QAAA,CAAAP,KAAA,CAAAC,UAAA,KAAAD,KAAA,CAAAC,UAAA,IAAAD,KAAA,CAAAQ,GAAA;IAAA,MAAAC,WACH,GAAAH,QAAA,OAAqBC,QAAA,CAAMP,KAAA,CAAAC,UAAoB,KAAAD,KAAA,CAAAC,UAAA,IAAAD,KAAA,CAAAU,GAAA;IAChE,MAAAC,YAAA,GAAAL,QAAA;MACA,MAAoBM,aAAA,GAAAC,YAAA,CAAAb,KAAA,CAAAc,IAAA;MAClB,KAAAC,WAAe,CAAAf,KAAA,CAAAgB,SAAqB;QACtC,IAAAJ,aAAA,GAAAZ,KAAA,CAAAgB,SAAA;UAEMC,SAAA,gBAA8B;QAClC;QACA,OAAKjB,KAAA,CAAAgB,SAAkB;MACrB,CAAI;QACF,OAAAE,IAAA,CAAAR,GAAA,CAAAG,YAAA,CAAAb,KAAA,CAAAC,UAAA,GAAAW,aAAA;MAAA;IACE,CACA;IACF,MAAAO,eAAA,GAAAb,QAAA;MACF,OAAAN,KAAA,CAAAoB,QAAA,IAAApB,KAAA,CAAAqB,gBAAA;IACA;IAAa,MACRC,eAAA,GAAAC,WAAA;IACL,MAAAC,mBAAgB,GAAAC,eAAmB;IACrC,MAAAC,YAAA,GAAApB,QAAA;MACD,IAAAT,IAAA,CAAAK,SAAA;QACK,OAAAL,IAAA,CAAAK,SAAA;MACJ;MACD,IAAAH,YAAA,GAAAF,IAAA,CAAAE,YAAA;MAED,IAAM4B,KAAA,CAAA5B,YAAkB,CAAY,EACpC;MAEM,IAAAQ,QAAA,CAAAR,YAAA,GAAwB;QACxB,IAAA6B,MAAA,CAAAC,KAAA,CAAA9B,YAAyB,GAC3B,OAAY;QACd,KAAAgB,WAAA,CAAAf,KAAA,CAAAgB,SAAA;UACIjB,YAAA,GAAmDA,YAAK,CAAA+B,OAAA,CAAA9B,KAAA,CAAAgB,SAAA;QAC5D;MACA;MACE,OAAWjB,YAAM;IACjB;IACiB,MAAAgC,WAAA,GAAAA,CAAAC,GAAA,EAAAC,GAAA,KAAa;MAC9B,IAAAlB,WAAA,CAAAkB,GAAA,GACFA,GAAA,GAAAtB,YAAA,CAAAuB,KAAA;MACO,IAAAD,GAAA,QACR,OAAAf,IAAA,CAAAiB,KAAA,CAAAH,GAAA;MACK,IAAAI,IAAA,GAAAC,MAAA,CAAcL,GAAC;MACnB,MAAgBM,QAAA,GAAAF,IAAM,CAAAG,OAAmB;MACzC,IAAID,QAAQ,KAAU,IAClB,OAAAN,GAAA;MACE,MAAAQ,IAAA,GAAAJ,IAAW,CAAKK,OAAA,MAAW,IAAAC,KAAA;MAC7B,MAAAC,KAAA,GAAAH,IAAA,CAAAF,QAAwB,GAAAL,GAAA;MAC5B,KAAAU,KAAA,EACM,OAAAX,GAAA;MACF,MAAAY,MAAe,GAAAR,IAAA,CAAAQ,MAAA;MACnB,IAAAR,IAAA,CAAAS,MAAe,CAAKD,MAAA;QACpBR,IAAS,MAAAA,IAAO,CAASU,KAAA,IAAC5B,IAAA,CAAAR,GAAW,IAAAkC,MAAA;MACnC;MACF,OAAAhB,MAAA,CAAAmB,UAAA,CAAAnB,MAAA,CAAAQ,IAAA,EAAAN,OAAA,CAAAG,GAAA;IACA;IACF,MAAApB,YAAA,GAAAqB,KAAA;MACM,IAAAP,KAAA,CAAAO,KAAA,GACA,QAAM;MACJ,MAAAc,WAAA,GAAcd,KAAA,CAAMe,QAAS;MAC7B,MAAAC,WAAA,GAAcF,WAAY,CAAAT,OAAA,CAAQ,GAAG;MAC3C,IAAIvB,SAAY;MAChB,IAAIkC,WAAA,KAAgB,CAAI;QACVlC,SAAA,GAAAgC,WAAA,CAAYJ,MAAA,GAASM,WAAc;MAAA;MAE1C,OAAAlC,SAAA;IAAA,CACT;IACA,MAAMmC,eAAkB,GAAAA,CAACC,GAAa,EAAAC,WAAA,GAAsB,CAAM;MAChE,IAAI,CAAC9C,QAAA,CAAS6C,GAAG,GAEjB,OAAmBvD,IAAA,CAAAE,YAAY;MACjC,OAAAgC,WAAA,CAAAqB,GAAA,GAAApD,KAAA,CAAAc,IAAA,GAAAuC,WAAA;IACA;IACE,MAAIC,QAAM,GAAAA,CAAA;MACV,IAAAtD,KAAc,CAAAuD,QAAA,IAAoB/B,mBAAU,CAAAU,KAAA,IAAAzB,WAAA,CAAAyB,KAAA,EACtC;MACN,MAAAA,KAAA,GAAAN,MAAsB,CAAAF,YAAA,CAAAQ,KAAA;MACjB,MAAAsB,MAAA,GAAAL,eAA8B,CAAAjB,KAAA;MACPuB,eAAA,CAAAD,MAAA;MAC9BE,IAAA,CAAAC,WAAA,EAAA9D,IAAA,CAAAE,YAAA;MACA6D,2BAAuB;IACrB;IACA,MAAAC,QAAc,GAAAA,CAAA,KAAO;MACf,IAAA7D,KAAA,CAAAuD,QAAyB,IAAA/B,mBAAS,CAAAU,KAAA,IAAA7B,WAAA,CAAA6B,KAAA,EACxC;MACK,MAAAA,KAAA,GAAAN,MAAA,CAAAF,YAA8B,CAAAQ,KAAA;MACP,MAAAsB,MAAA,GAAAL,eAAA,CAAAjB,KAAA;MAC9BuB,eAAA,CAAAD,MAAA;MACME,IAAA,CAAAC,WAAA,EAAc9D,IAClB,CAAAE,YAE8B;MAC9B6D,2BAAmC;IACnC;IACE,MAAAE,WAAA,GAAAA,CAAA5B,KAAA,EAAA6B,MAA+D;MACjE;QAAArD,GAAA;QAAAF,GAAA;QAAAM,IAAA;QAAAE,SAAA;QAAAgD,YAAA;QAAAC;MAAA,IAAAjE,KAAA;MACI,IAAAU,GAAA,GAAAF,GAAS;QACb0D,UAAe,cAAY,uCAAe;MACxC;MACF,IAAAV,MAAA,GAAA5B,MAAA,CAAAM,KAAA;MACA,IAAIP,KAAA,CAAAO,KAAc,KAAAN,MAAA,CAAAC,KAAA,CAAA2B,MAAA;QAChB;MACE;MACF,IAAAtB,KAAA;QACS,IAAA+B,YAAA;UACX;QACA;QACET,MAAA,GAASW,QAAA,CAAAF,YAAiB;UAAAzD,GAAA;UAAeE;QAAI,EAAAuD,YAAmB,IAAAA,YAAA;MAChE;MACY,IAAAD,YAAA;QACZR,MAAA,GAAAzB,WAAA,CAAAb,IAAA,CAAAiB,KAAA,CAAAqB,MAAA,GAAA1C,IAAA,IAAAA,IAAA,EAAAE,SAAA;QACF,IAAAwC,MAAA,KAAAtB,KAAA;UACI6B,MAAa,IAAAL,IAAA,CAAAU,kBAAY,EAAAZ,MAAA;QAC3B;MAAsC;MAEpC,KAAAzC,WAAgB,CAAAC,SAAA,GAAS;QAClBwC,MAAA,GAAAzB,WAAA,CAAAyB,MAAqB,EAAAxC,SAAA;MAC9B;MACF,IAAAwC,MAAA,GAAA9C,GAAA,IAAA8C,MAAA,GAAAhD,GAAA;QACOgD,MAAA,GAAAA,MAAA,GAAA9C,GAAA,GAAAA,GAAA,GAAAF,GAAA;QACTuD,MAAA,IAAAL,IAAA,CAAAU,kBAAA,EAAAZ,MAAA;MACA;MAIE,OAAAA,MAAA;IACA,CAAM;IACN,MAAIC,eAAa,GAAAA,CAAAvB,KAAA,EAAAmC,UAAA;MACf,IAAAC,EAAA;MACA,MAAAC,MAAA,GAAA1E,IAAA,CAAAE,YAAA;MACF,MAAAyD,MAAA,GAAAM,WAAA,CAAA5B,KAAA;MACI,KAAAmC,UAAW;QACfX,IAAiB,CAAAU,kBAAA,EAAAZ,MAAA;QACjB;MACA;MACO,IAAAe,MAAA,KAAAf,MAAc,IAAAtB,KAAgB,EACrC;MACArC,IAAI,CAAAK,SAAqB;MACbwD,IAAA,CAAAU,kBAAW,EAAAZ,MAAQ,CAAE;MACjC,IAAAe,MAAA,KAAAf,MAAA;QACAE,IAAoB,CAAAc,YAAA,EAAAhB,MAAA,EAAAe,MAAA;MAAA;MAEhB,IAAAvE,KAAA,CAAAyE,aAAiC;QACrC,CAAAH,EAAK,GAAYnE,QAAA,oBAAAA,QAAA,CAAAuE,QAAA,qBAAAJ,EAAA,CAAAK,IAAA,CAAAxE,QAAA,YAAAyE,KAAA,CAAAC,GAAA,IAAA5D,SAAA,CAAA4D,GAAA;MACjB;MACAhF,IAAA,CAAKE,YAAA,GAAmByD,MAAA;IACxB;IACF,MAAAsB,WAAA,GAAA5C,KAAA;MACMrC,IAAA,CAAAK,SAAA,GAAAgC,KAAA;MACJ,MAAMsB,MAAS,GAAAtB,KAAA,KAAU,EAAK,UAAON,MAAS,CAAAM,KAAA;MACzCwB,IAAA,CAAAC,WAAA,EAAAH,MAAoB,CAAC;MACxBC,eAAA,CAAAD,MAAsB;IAAA,CACxB;IAC4B,MAAAuB,iBAAA,GAAA7C,KAAA;MAC5B,MAAiBsB,MAAA,GAAAtB,KAAA,UAAAN,MAAA,CAAAM,KAAA;MACnB,IAAA3B,QAAA,CAAAiD,MAAA,MAAA5B,MAAA,CAAAC,KAAA,CAAA2B,MAAA,KAAAtB,KAAA;QAEAuB,eAAoB,CAAAD,MAAA;MAClB;MACFI,2BAAA;MAEA/D,IAAM,CAAAK,SAAa;IACjB;IACF,MAAA8E,KAAA,GAAAA,CAAA;MAEM,IAAAV,EAAA,EAAAW,EAAA;MACJ,CAAAA,EAAA,IAAAX,EAAA,GAAA3E,KAAmB,CAAAuC,KAAA,qBAAAoC,EAAA,CAAAU,KAAA,qBAAAC,EAAA,CAAAN,IAAA,CAAAL,EAAA;IAAA,CACrB;IAEM,MAAAY,IAAA,GAAAA,CAAA,KAAa;MACjB,IAAAZ,EAAiB,EAAAW,EAAA;MAIjB,CAAAA,EAAA,IAAAX,EAAA,GAAA3E,KAAmB,CAAAuC,KAAA,kBAAsB,GAAQoC,EAAA,CAAAY,IAAA,YAAoB,SAAAD,EAAA,CAAAN,IAAA,CAAAL,EAAA;IACnE,CAAM;IACR,MAAAa,WAAA,GAAAC,KAAA;MACA1B,IAAA,CAAK,SAAa0B,KAAA;IAClB;IACY,MAAAC,UAAA,GAAAD,KAAW;MACvB,IAAAd,EAAA,EAAAW,EAAA;MACFpF,IAAA,CAAAK,SAAA;MAEA,IAAMoF,SAAA,MAAAzF,IAAA,CAAAE,YAAoC,eAAAuE,EAAA,GAAA3E,KAAA,CAAAuC,KAAA,qBAAAoC,EAAA,CAAA3E,KAAA;QACpCA,KAAA,CAAAuC,KAAsB,CAAAvC,KAAA,CAAAuC,KAAA;MACxB;MACFwB,IAAA,SAAA0B,KAAA;MACF,IAAApF,KAAA,CAAAyE,aAAA;QACM,CAAAQ,EAAA,GAAA9E,QAAA,IAAiC,gBAAAA,QAAA,CAAAuE,QAAA,qBAAAO,EAAA,CAAAN,IAAA,CAAAxE,QAAA,UAAAyE,KAAA,CAAAC,GAAA,IAAA5D,SAAA,CAAA4D,GAAA;MACrC;IAA0D,CAC5D;IAEA,MAAAjB,2BAAA,GAAAA,CAAA;MACE,IAAA/D,IAAY,CAAAE,YAAA,KAAAC,KAAA,CAAAC,UAAA;QAAAJ,IAAA,CAAAE,YACS,GAAAC,KAAA,CAAAC,UAAA;MACnB;IACA;IACE,MAAAsF,WAAoB,GAAAC,CAAA;MACtB,IAAAC,QAAA,CAAAC,aAAA,KAAAF,CAAA,CAAAG,MAAA,EACFH,CAAA,CAAAI,cAAA;IAAA,CACA;IACFC,KAAA,OAAA7F,KAAA,CAAAC,UAAA,GAAAiC,KAAA,EAAA4D,QAAA;MACA,MAAAC,QAAgB,GAAAjC,WAAA,CAAA5B,KAAA;MACd,IAAArC,IAAQ,CAAAK,SAAU,aAAe6F,QAAA,KAAAD,QAAA;QAC3BjG,IAAA,CAAAE,YAAa,GAAAgG,QAAa;MAChC;IACA,CAAI;MAAAC,SAAgB;IAAA;IAClBC,SAAA,OAAwB;MAC1B,IAAO3B,EAAA;MACL;QAAA9D,GAAA;QAAWE,GAAA;QAAAT;MAAA,CAA+B,GAAAD,KAAA;MAC5C,MAAAkG,UAAA,IAAA5B,EAAA,GAAA3E,KAAA,CAAAuC,KAAA,qBAAAoC,EAAA,CAAA3E,KAAA;MACIuG,UAAA,CAAOC,YAAY,CAAG;MACxB,IAAAvE,MAAA,CAAAwE,QAAwB,CAAA1F,GAAA;QACnBwF,UAAA,CAAAC,YAAA,kBAAA9D,MAAA,CAAA3B,GAAA;MACL;QACFwF,UAAA,CAAAG,eAAA;MACA;MACE,IAAAzE,MAAA,CAAAwE,QAAA,CAAA5F,GAAA;QACA0F,UAAA,CAAAC,YAA0B,kBAAA9D,MACtB,CAAO7B,GAAA;MACP,CACN;QACA0F,UAAwB,CAAAG,eAAA,gBAAwB;MAChD;MACMH,UAAA,CAAAC,YAAsC,kBAAAtG,IAAA,CAAAE,YAAA,IAAAF,IAAA,CAAAE,YAAA,SAAAsC,MAAA,CAAAxC,IAAA,CAAAE,YAAA;MACtCmG,UAAA,CAAAC,YAAmB,kBAAA9D,MAAA,CAAAb,mBAAA,CAAAU,KAAA;MACf,KAAA3B,QAAA,CAAAN,UAAA,KAAAA,UAAA;QACR,IAAAmD,GAAA,GAAAxB,MAAA,CAAA3B,UAAA;QACA,IAAA2B,MAAA,CAAAC,KAAA,CAAAuB,GAAA;UACFA,GAAA;QACA;QACDM,IAAA,CAAAU,kBAAA,EAAAhB,GAAA;MACD;MACQ8C,UAAA,CAAAI,gBAA0B,UAAAf,WAAA;QAAAgB,OAAA;MAAA;IAChC;IACFC,SAAC;MACY,IAAAlC,EAAA,EAAAW,EAAA;MAAA,MAAAiB,UAAA,IAAA5B,EAAA,GAAA3E,KAAA,CAAAuC,KAAA,qBAAAoC,EAAA,CAAA3E,KAAA;MAEXuG,UAAA,oBAAAA,UAAA,CAAAC,YAAA,sBAAAlB,EAAA,GAAApF,IAAA,CAAAE,YAAA,YAAAkF,EAAA;IAAA;IAEAwB,MAAA;MACDzB,KAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}