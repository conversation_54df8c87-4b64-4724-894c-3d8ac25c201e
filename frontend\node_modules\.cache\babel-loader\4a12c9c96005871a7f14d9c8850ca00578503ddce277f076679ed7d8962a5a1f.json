{"ast": null, "code": "import { popperTriggerProps } from '../../popper/src/trigger.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nconst useTooltipTriggerProps = buildProps({\n  ...popperTriggerProps,\n  disabled: Boolean,\n  trigger: {\n    type: definePropType([String, Array]),\n    default: \"hover\"\n  },\n  triggerKeys: {\n    type: definePropType(Array),\n    default: () => [EVENT_CODE.enter, EVENT_CODE.numpadEnter, EVENT_CODE.space]\n  }\n});\nexport { useTooltipTriggerProps };", "map": {"version": 3, "names": ["useTooltipTriggerProps", "buildProps", "popperTriggerProps", "disabled", "Boolean", "trigger", "type", "definePropType", "String", "Array", "default", "triggerKeys", "EVENT_CODE", "enter", "numpadEnter", "space"], "sources": ["../../../../../../packages/components/tooltip/src/trigger.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { popperTriggerProps } from '@element-plus/components/popper'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport type { Arrayable } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\n\nexport type TooltipTriggerType = 'hover' | 'focus' | 'click' | 'contextmenu'\n\nexport const useTooltipTriggerProps = buildProps({\n  ...popperTriggerProps,\n  /**\n   * @description whether Toolt<PERSON> is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description How should the tooltip be triggered (to show)\n   */\n  trigger: {\n    type: definePropType<Arrayable<TooltipTriggerType>>([String, Array]),\n    default: 'hover',\n  },\n  /**\n   * @description When you click the mouse to focus on the trigger element, you can define a set of keyboard codes to control the display of tooltip through the keyboard\n   */\n  triggerKeys: {\n    type: definePropType<string[]>(Array),\n    default: () => [EVENT_CODE.enter, EVENT_CODE.numpadEnter, EVENT_CODE.space],\n  },\n} as const)\n\nexport type ElTooltipTriggerProps = ExtractPropTypes<\n  typeof useTooltipTriggerProps\n>\n"], "mappings": ";;;AAGY,MAACA,sBAAsB,GAAGC,UAAU,CAAC;EAC/C,GAAGC,kBAAkB;EACrBC,QAAQ,EAAEC,OAAO;EACjBC,OAAO,EAAE;IACPC,IAAI,EAAEC,cAAc,CAAC,CAACC,MAAM,EAAEC,KAAK,CAAC,CAAC;IACrCC,OAAO,EAAE;EACb,CAAG;EACDC,WAAW,EAAE;IACXL,IAAI,EAAEC,cAAc,CAACE,KAAK,CAAC;IAC3BC,OAAO,EAAEA,CAAA,KAAM,CAACE,UAAU,CAACC,KAAK,EAAED,UAAU,CAACE,WAAW,EAAEF,UAAU,CAACG,KAAK;EAC9E;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}