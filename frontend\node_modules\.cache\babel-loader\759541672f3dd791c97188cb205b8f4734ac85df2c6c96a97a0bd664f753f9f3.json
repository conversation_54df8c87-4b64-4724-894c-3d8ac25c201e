{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-content\"\n};\nconst _hoisted_3 = {\n  class: \"quick-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_statistic = _resolveComponent(\"el-statistic\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"welcome-card\"\n  }, {\n    header: _withCtx(() => _cache[3] || (_cache[3] = [_createElementVNode(\"div\", {\n      class: \"card-header\"\n    }, [_createElementVNode(\"h2\", null, \"欢迎使用图书馆自习室管理系统\")], -1 /* HOISTED */)])),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"当前可用座位\",\n          value: $setup.availableSeats\n        }, null, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"我的预约\",\n          value: $setup.myReservations\n        }, null, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"我的信誉分\",\n          value: $setup.creditScore\n        }, null, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createElementVNode(\"div\", _hoisted_3, [_cache[7] || (_cache[7] = _createElementVNode(\"h3\", null, \"快捷操作\", -1 /* HOISTED */)), _createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/seat/reservation'))\n        }, {\n          default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\" 预约座位 \")])),\n          _: 1 /* STABLE */,\n          __: [4]\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"info\",\n          onClick: _cache[1] || (_cache[1] = $event => _ctx.$router.push('/user/reservations'))\n        }, {\n          default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\" 查看我的预约 \")])),\n          _: 1 /* STABLE */,\n          __: [5]\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"success\",\n          onClick: _cache[2] || (_cache[2] = $event => _ctx.$router.push('/seat/map'))\n        }, {\n          default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\" 查看座位地图 \")])),\n          _: 1 /* STABLE */,\n          __: [6]\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])])]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_cache", "_createElementVNode", "default", "_hoisted_2", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_statistic", "title", "value", "$setup", "availableSeats", "_", "myReservations", "creditScore", "_hoisted_3", "_component_el_button", "type", "onClick", "$event", "_ctx", "$router", "push", "_createTextVNode", "__"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <el-card class=\"welcome-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h2>欢迎使用图书馆自习室管理系统</h2>\n        </div>\n      </template>\n      <div class=\"dashboard-content\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-statistic title=\"当前可用座位\" :value=\"availableSeats\" />\n          </el-col>\n          <el-col :span=\"8\">\n            <el-statistic title=\"我的预约\" :value=\"myReservations\" />\n          </el-col>\n          <el-col :span=\"8\">\n            <el-statistic title=\"我的信誉分\" :value=\"creditScore\" />\n          </el-col>\n        </el-row>\n\n        <div class=\"quick-actions\">\n          <h3>快捷操作</h3>\n          <el-row :gutter=\"20\">\n            <el-col :span=\"8\">\n              <el-button\n                type=\"primary\"\n                @click=\"$router.push('/seat/reservation')\"\n              >\n                预约座位\n              </el-button>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-button\n                type=\"info\"\n                @click=\"$router.push('/user/reservations')\"\n              >\n                查看我的预约\n              </el-button>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-button type=\"success\" @click=\"$router.push('/seat/map')\">\n                查看座位地图\n              </el-button>\n            </el-col>\n          </el-row>\n        </div>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted } from \"vue\";\n\nexport default {\n  name: \"Dashboard\",\n  setup() {\n    const availableSeats = ref(0);\n    const myReservations = ref(0);\n    const creditScore = ref(100);\n\n    onMounted(async () => {\n      try {\n        // 这里应该是从后端获取数据\n        // 暂时使用模拟数据\n        availableSeats.value = 120;\n        myReservations.value = 2;\n        creditScore.value = 95;\n      } catch (error) {\n        console.error(\"获取数据失败:\", error);\n      }\n    });\n\n    return {\n      availableSeats,\n      myReservations,\n      creditScore,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard-container {\n  padding: 20px;\n}\n\n.welcome-card {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.dashboard-content {\n  padding: 20px 0;\n}\n\n.quick-actions {\n  margin-top: 40px;\n\n  h3 {\n    margin-bottom: 20px;\n  }\n\n  .el-button {\n    width: 100%;\n  }\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAqB;;EAOvBA,KAAK,EAAC;AAAmB;;EAavBA,KAAK,EAAC;AAAe;;;;;;;uBApBhCC,mBAAA,CAgDM,OAhDNC,UAgDM,GA/CJC,YAAA,CA8CUC,kBAAA;IA9CDJ,KAAK,EAAC;EAAc;IAChBK,MAAM,EAAAC,QAAA,CACf,MAEMC,MAAA,QAAAA,MAAA,OAFNC,mBAAA,CAEM;MAFDR,KAAK,EAAC;IAAa,IACtBQ,mBAAA,CAAuB,YAAnB,gBAAc,E;IAL5BC,OAAA,EAAAH,QAAA,CAQM,MAuCM,CAvCNE,mBAAA,CAuCM,OAvCNE,UAuCM,GAtCJP,YAAA,CAUSQ,iBAAA;MAVAC,MAAM,EAAE;IAAE;MAT3BH,OAAA,EAAAH,QAAA,CAUU,MAES,CAFTH,YAAA,CAESU,iBAAA;QAFAC,IAAI,EAAE;MAAC;QAV1BL,OAAA,EAAAH,QAAA,CAWY,MAAuD,CAAvDH,YAAA,CAAuDY,uBAAA;UAAzCC,KAAK,EAAC,QAAQ;UAAEC,KAAK,EAAEC,MAAA,CAAAC;;QAXjDC,CAAA;UAaUjB,YAAA,CAESU,iBAAA;QAFAC,IAAI,EAAE;MAAC;QAb1BL,OAAA,EAAAH,QAAA,CAcY,MAAqD,CAArDH,YAAA,CAAqDY,uBAAA;UAAvCC,KAAK,EAAC,MAAM;UAAEC,KAAK,EAAEC,MAAA,CAAAG;;QAd/CD,CAAA;UAgBUjB,YAAA,CAESU,iBAAA;QAFAC,IAAI,EAAE;MAAC;QAhB1BL,OAAA,EAAAH,QAAA,CAiBY,MAAmD,CAAnDH,YAAA,CAAmDY,uBAAA;UAArCC,KAAK,EAAC,OAAO;UAAEC,KAAK,EAAEC,MAAA,CAAAI;;QAjBhDF,CAAA;;MAAAA,CAAA;QAqBQZ,mBAAA,CAyBM,OAzBNe,UAyBM,G,0BAxBJf,mBAAA,CAAa,YAAT,MAAI,sBACRL,YAAA,CAsBSQ,iBAAA;MAtBAC,MAAM,EAAE;IAAE;MAvB7BH,OAAA,EAAAH,QAAA,CAwBY,MAOS,CAPTH,YAAA,CAOSU,iBAAA;QAPAC,IAAI,EAAE;MAAC;QAxB5BL,OAAA,EAAAH,QAAA,CAyBc,MAKY,CALZH,YAAA,CAKYqB,oBAAA;UAJVC,IAAI,EAAC,SAAS;UACbC,OAAK,EAAAnB,MAAA,QAAAA,MAAA,MAAAoB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;UA3BpCrB,OAAA,EAAAH,QAAA,CA4Be,MAEDC,MAAA,QAAAA,MAAA,OA9BdwB,gBAAA,CA4Be,QAED,E;UA9BdX,CAAA;UAAAY,EAAA;;QAAAZ,CAAA;UAgCYjB,YAAA,CAOSU,iBAAA;QAPAC,IAAI,EAAE;MAAC;QAhC5BL,OAAA,EAAAH,QAAA,CAiCc,MAKY,CALZH,YAAA,CAKYqB,oBAAA;UAJVC,IAAI,EAAC,MAAM;UACVC,OAAK,EAAAnB,MAAA,QAAAA,MAAA,MAAAoB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;UAnCpCrB,OAAA,EAAAH,QAAA,CAoCe,MAEDC,MAAA,QAAAA,MAAA,OAtCdwB,gBAAA,CAoCe,UAED,E;UAtCdX,CAAA;UAAAY,EAAA;;QAAAZ,CAAA;UAwCYjB,YAAA,CAISU,iBAAA;QAJAC,IAAI,EAAE;MAAC;QAxC5BL,OAAA,EAAAH,QAAA,CAyCc,MAEY,CAFZH,YAAA,CAEYqB,oBAAA;UAFDC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAAnB,MAAA,QAAAA,MAAA,MAAAoB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;UAzC5DrB,OAAA,EAAAH,QAAA,CAyC2E,MAE7DC,MAAA,QAAAA,MAAA,OA3CdwB,gBAAA,CAyC2E,UAE7D,E;UA3CdX,CAAA;UAAAY,EAAA;;QAAAZ,CAAA;;MAAAA,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}