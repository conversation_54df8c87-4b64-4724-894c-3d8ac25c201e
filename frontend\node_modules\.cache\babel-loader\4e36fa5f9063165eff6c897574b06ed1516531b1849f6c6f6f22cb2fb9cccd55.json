{"ast": null, "code": "import TreeV2 from './src/tree.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElTreeV2 = withInstall(TreeV2);\nexport { ElTreeV2, ElTreeV2 as default };", "map": {"version": 3, "names": ["ElTreeV2", "withInstall", "TreeV2"], "sources": ["../../../../../packages/components/tree-v2/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport TreeV2 from './src/tree.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTreeV2: SFCWithInstall<typeof TreeV2> = withInstall(TreeV2)\nexport default ElTreeV2\n"], "mappings": ";;AAEY,MAACA,QAAQ,GAAGC,WAAW,CAACC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}