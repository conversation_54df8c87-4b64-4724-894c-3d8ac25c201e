{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { inject, computed, h } from 'vue';\nimport { merge } from 'lodash-unified';\nimport { getRowIdentity } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport useEvents from './events-helper.mjs';\nimport useStyles from './styles-helper.mjs';\nimport TdWrapper from './td-wrapper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isBoolean, isPropAbsent } from '../../../../utils/types.mjs';\nfunction useRender(props) {\n  const parent = inject(TABLE_INJECTION_KEY);\n  const ns = useNamespace(\"table\");\n  const {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger\n  } = useEvents(props);\n  const {\n    getRowStyle,\n    getRowClass,\n    getCellStyle,\n    getCellClass,\n    getSpan,\n    getColspanRealWidth\n  } = useStyles(props);\n  const firstDefaultColumnIndex = computed(() => {\n    return props.store.states.columns.value.findIndex(({\n      type\n    }) => type === \"default\");\n  });\n  const getKeyOfRow = (row, index) => {\n    const rowKey = parent.props.rowKey;\n    if (rowKey) {\n      return getRowIdentity(row, rowKey);\n    }\n    return index;\n  };\n  const rowRender = (row, $index, treeRowData, expanded = false) => {\n    const {\n      tooltipEffect,\n      tooltipOptions,\n      store\n    } = props;\n    const {\n      indent,\n      columns\n    } = store.states;\n    const rowClasses = getRowClass(row, $index);\n    let display = true;\n    if (treeRowData) {\n      rowClasses.push(ns.em(\"row\", `level-${treeRowData.level}`));\n      display = treeRowData.display;\n    }\n    const displayStyle = display ? null : {\n      display: \"none\"\n    };\n    return h(\"tr\", {\n      style: [displayStyle, getRowStyle(row, $index)],\n      class: rowClasses,\n      key: getKeyOfRow(row, $index),\n      onDblclick: $event => handleDoubleClick($event, row),\n      onClick: $event => handleClick($event, row),\n      onContextmenu: $event => handleContextMenu($event, row),\n      onMouseenter: () => handleMouseEnter($index),\n      onMouseleave: handleMouseLeave\n    }, columns.value.map((column, cellIndex) => {\n      const {\n        rowspan,\n        colspan\n      } = getSpan(row, column, $index, cellIndex);\n      if (!rowspan || !colspan) {\n        return null;\n      }\n      const columnData = Object.assign({}, column);\n      columnData.realWidth = getColspanRealWidth(columns.value, colspan, cellIndex);\n      const data = {\n        store: props.store,\n        _self: props.context || parent,\n        column: columnData,\n        row,\n        $index,\n        cellIndex,\n        expanded\n      };\n      if (cellIndex === firstDefaultColumnIndex.value && treeRowData) {\n        data.treeNode = {\n          indent: treeRowData.level * indent.value,\n          level: treeRowData.level\n        };\n        if (isBoolean(treeRowData.expanded)) {\n          data.treeNode.expanded = treeRowData.expanded;\n          if (\"loading\" in treeRowData) {\n            data.treeNode.loading = treeRowData.loading;\n          }\n          if (\"noLazyChildren\" in treeRowData) {\n            data.treeNode.noLazyChildren = treeRowData.noLazyChildren;\n          }\n        }\n      }\n      const baseKey = `${getKeyOfRow(row, $index)},${cellIndex}`;\n      const patchKey = columnData.columnKey || columnData.rawColumnKey || \"\";\n      const mergedTooltipOptions = column.showOverflowTooltip && merge({\n        effect: tooltipEffect\n      }, tooltipOptions, column.showOverflowTooltip);\n      return h(TdWrapper, {\n        style: getCellStyle($index, cellIndex, row, column),\n        class: getCellClass($index, cellIndex, row, column, colspan - 1),\n        key: `${patchKey}${baseKey}`,\n        rowspan,\n        colspan,\n        onMouseenter: $event => handleCellMouseEnter($event, row, mergedTooltipOptions),\n        onMouseleave: handleCellMouseLeave\n      }, {\n        default: () => cellChildren(cellIndex, column, data)\n      });\n    }));\n  };\n  const cellChildren = (cellIndex, column, data) => {\n    return column.renderCell(data);\n  };\n  const wrappedRowRender = (row, $index) => {\n    const store = props.store;\n    const {\n      isRowExpanded,\n      assertRowKey\n    } = store;\n    const {\n      treeData,\n      lazyTreeNodeMap,\n      childrenColumnName,\n      rowKey\n    } = store.states;\n    const columns = store.states.columns.value;\n    const hasExpandColumn = columns.some(({\n      type\n    }) => type === \"expand\");\n    if (hasExpandColumn) {\n      const expanded = isRowExpanded(row);\n      const tr = rowRender(row, $index, void 0, expanded);\n      const renderExpanded = parent.renderExpanded;\n      if (!renderExpanded) {\n        console.error(\"[Element Error]renderExpanded is required.\");\n        return tr;\n      }\n      const rows = [[tr]];\n      if (parent.props.preserveExpandedContent || expanded) {\n        rows[0].push(h(\"tr\", {\n          key: `expanded-row__${tr.key}`,\n          style: {\n            display: expanded ? \"\" : \"none\"\n          }\n        }, [h(\"td\", {\n          colspan: columns.length,\n          class: `${ns.e(\"cell\")} ${ns.e(\"expanded-cell\")}`\n        }, [renderExpanded({\n          row,\n          $index,\n          store,\n          expanded\n        })])]));\n      }\n      return rows;\n    } else if (Object.keys(treeData.value).length) {\n      assertRowKey();\n      const key = getRowIdentity(row, rowKey.value);\n      let cur = treeData.value[key];\n      let treeRowData = null;\n      if (cur) {\n        treeRowData = {\n          expanded: cur.expanded,\n          level: cur.level,\n          display: true\n        };\n        if (isBoolean(cur.lazy)) {\n          if (isBoolean(cur.loaded) && cur.loaded) {\n            treeRowData.noLazyChildren = !(cur.children && cur.children.length);\n          }\n          treeRowData.loading = cur.loading;\n        }\n      }\n      const tmp = [rowRender(row, $index, treeRowData)];\n      if (cur) {\n        let i = 0;\n        const traverse = (children, parent2) => {\n          if (!(children && children.length && parent2)) return;\n          children.forEach(node => {\n            const innerTreeRowData = {\n              display: parent2.display && parent2.expanded,\n              level: parent2.level + 1,\n              expanded: false,\n              noLazyChildren: false,\n              loading: false\n            };\n            const childKey = getRowIdentity(node, rowKey.value);\n            if (isPropAbsent(childKey)) {\n              throw new Error(\"For nested data item, row-key is required.\");\n            }\n            cur = {\n              ...treeData.value[childKey]\n            };\n            if (cur) {\n              innerTreeRowData.expanded = cur.expanded;\n              cur.level = cur.level || innerTreeRowData.level;\n              cur.display = !!(cur.expanded && innerTreeRowData.display);\n              if (isBoolean(cur.lazy)) {\n                if (isBoolean(cur.loaded) && cur.loaded) {\n                  innerTreeRowData.noLazyChildren = !(cur.children && cur.children.length);\n                }\n                innerTreeRowData.loading = cur.loading;\n              }\n            }\n            i++;\n            tmp.push(rowRender(node, $index + i, innerTreeRowData));\n            if (cur) {\n              const nodes2 = lazyTreeNodeMap.value[childKey] || node[childrenColumnName.value];\n              traverse(nodes2, cur);\n            }\n          });\n        };\n        cur.display = true;\n        const nodes = lazyTreeNodeMap.value[key] || row[childrenColumnName.value];\n        traverse(nodes, cur);\n      }\n      return tmp;\n    } else {\n      return rowRender(row, $index, void 0);\n    }\n  };\n  return {\n    wrappedRowRender,\n    tooltipContent,\n    tooltipTrigger\n  };\n}\nexport { useRender as default };", "map": {"version": 3, "names": ["useRender", "props", "parent", "inject", "TABLE_INJECTION_KEY", "ns", "useNamespace", "handleDoubleClick", "handleClick", "handleContextMenu", "handleMouseEnter", "handleMouseLeave", "handleCellMouseEnter", "handleCellMouseLeave", "tooltipContent", "tooltipTrigger", "useEvents", "getRowStyle", "getRowClass", "getCellStyle", "getCellClass", "getSpan", "getColspanRealWidth", "useStyles", "firstDefaultColumnIndex", "computed", "store", "states", "columns", "value", "findIndex", "type", "getKeyOfRow", "row", "index", "<PERSON><PERSON><PERSON>", "getRowIdentity", "rowRender", "$index", "treeRowData", "expanded", "tooltipEffect", "tooltipOptions", "indent", "rowClasses", "display", "push", "em", "level", "displayStyle", "h", "style", "class", "key", "onDblclick", "$event", "onClick", "onContextmenu", "onMouseenter", "onMouseleave", "map", "column", "cellIndex", "rowspan", "colspan", "columnData", "Object", "assign", "realWidth", "data", "_self", "context", "treeNode", "isBoolean", "loading", "noLazyChildren", "baseKey", "<PERSON><PERSON><PERSON>", "column<PERSON>ey", "rawColumnKey", "mergedTooltipOptions", "showOverflowTooltip", "merge", "effect", "TdWrapper", "default", "cellChildren", "renderCell", "wrappedRowRender", "isRowExpanded", "assertRowKey", "treeData", "lazyTreeNodeMap", "childrenColumnName", "hasExpandColumn", "some", "tr", "renderExpanded", "console", "error", "rows", "preserveExpandedContent", "length", "e", "keys", "cur", "lazy", "loaded", "children", "tmp", "i", "traverse", "parent2", "for<PERSON>ach", "node", "innerTreeRowData", "<PERSON><PERSON><PERSON>", "isPropAbsent", "Error", "nodes2", "nodes"], "sources": ["../../../../../../../packages/components/table/src/table-body/render-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, h, inject } from 'vue'\nimport { merge } from 'lodash-unified'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isBoolean, isPropAbsent } from '@element-plus/utils'\nimport { getRowIdentity } from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport useEvents from './events-helper'\nimport useStyles from './styles-helper'\nimport TdWrapper from './td-wrapper.vue'\nimport type { TableBodyProps } from './defaults'\nimport type { RenderRowData, TableProps, TreeNode } from '../table/defaults'\n\nfunction useRender<T>(props: Partial<TableBodyProps<T>>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const ns = useNamespace('table')\n  const {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger,\n  } = useEvents(props)\n  const {\n    getRowStyle,\n    getRowClass,\n    getCellStyle,\n    getCellClass,\n    getSpan,\n    getColspanRealWidth,\n  } = useStyles(props)\n  const firstDefaultColumnIndex = computed(() => {\n    return props.store.states.columns.value.findIndex(\n      ({ type }) => type === 'default'\n    )\n  })\n  const getKeyOfRow = (row: T, index: number) => {\n    const rowKey = (parent.props as Partial<TableProps<T>>).rowKey\n    if (rowKey) {\n      return getRowIdentity(row, rowKey)\n    }\n    return index\n  }\n  const rowRender = (\n    row: T,\n    $index: number,\n    treeRowData?: TreeNode,\n    expanded = false\n  ) => {\n    const { tooltipEffect, tooltipOptions, store } = props\n    const { indent, columns } = store.states\n    const rowClasses = getRowClass(row, $index)\n    let display = true\n    if (treeRowData) {\n      rowClasses.push(ns.em('row', `level-${treeRowData.level}`))\n      display = treeRowData.display\n    }\n    const displayStyle = display ? null : { display: 'none' }\n    return h(\n      'tr',\n      {\n        style: [displayStyle, getRowStyle(row, $index)],\n        class: rowClasses,\n        key: getKeyOfRow(row, $index),\n        onDblclick: ($event) => handleDoubleClick($event, row),\n        onClick: ($event) => handleClick($event, row),\n        onContextmenu: ($event) => handleContextMenu($event, row),\n        onMouseenter: () => handleMouseEnter($index),\n        onMouseleave: handleMouseLeave,\n      },\n      columns.value.map((column, cellIndex) => {\n        const { rowspan, colspan } = getSpan(row, column, $index, cellIndex)\n        if (!rowspan || !colspan) {\n          return null\n        }\n        const columnData = Object.assign({}, column)\n        columnData.realWidth = getColspanRealWidth(\n          columns.value,\n          colspan,\n          cellIndex\n        )\n        const data: RenderRowData<T> = {\n          store: props.store,\n          _self: props.context || parent,\n          column: columnData,\n          row,\n          $index,\n          cellIndex,\n          expanded,\n        }\n        if (cellIndex === firstDefaultColumnIndex.value && treeRowData) {\n          data.treeNode = {\n            indent: treeRowData.level * indent.value,\n            level: treeRowData.level,\n          }\n          if (isBoolean(treeRowData.expanded)) {\n            data.treeNode.expanded = treeRowData.expanded\n            // 表明是懒加载\n            if ('loading' in treeRowData) {\n              data.treeNode.loading = treeRowData.loading\n            }\n            if ('noLazyChildren' in treeRowData) {\n              data.treeNode.noLazyChildren = treeRowData.noLazyChildren\n            }\n          }\n        }\n        const baseKey = `${getKeyOfRow(row, $index)},${cellIndex}`\n        const patchKey = columnData.columnKey || columnData.rawColumnKey || ''\n        const mergedTooltipOptions =\n          column.showOverflowTooltip &&\n          merge(\n            {\n              effect: tooltipEffect,\n            },\n            tooltipOptions,\n            column.showOverflowTooltip\n          )\n        return h(\n          TdWrapper,\n          {\n            style: getCellStyle($index, cellIndex, row, column),\n            class: getCellClass($index, cellIndex, row, column, colspan - 1),\n            key: `${patchKey}${baseKey}`,\n            rowspan,\n            colspan,\n            onMouseenter: ($event) =>\n              handleCellMouseEnter($event, row, mergedTooltipOptions),\n            onMouseleave: handleCellMouseLeave,\n          },\n          {\n            default: () => cellChildren(cellIndex, column, data),\n          }\n        )\n      })\n    )\n  }\n  const cellChildren = (cellIndex, column, data) => {\n    return column.renderCell(data)\n  }\n\n  const wrappedRowRender = (row: T, $index: number) => {\n    const store = props.store\n    const { isRowExpanded, assertRowKey } = store\n    const { treeData, lazyTreeNodeMap, childrenColumnName, rowKey } =\n      store.states\n    const columns = store.states.columns.value\n    const hasExpandColumn = columns.some(({ type }) => type === 'expand')\n    if (hasExpandColumn) {\n      const expanded = isRowExpanded(row)\n      const tr = rowRender(row, $index, undefined, expanded)\n      const renderExpanded = parent.renderExpanded\n      if (!renderExpanded) {\n        console.error('[Element Error]renderExpanded is required.')\n        return tr\n      }\n\n      // 在没设置时候避免 h 执行\n      // 非保留模式且未展开时，直接返回\n      // 使用二维数组包装，避免修改 $index\n      const rows = [[tr]]\n\n      // 仅在需要时创建展开行（保留模式或展开状态）\n      if (parent.props.preserveExpandedContent || expanded) {\n        rows[0].push(\n          h(\n            'tr',\n            {\n              key: `expanded-row__${tr.key as string}`,\n              style: { display: expanded ? '' : 'none' },\n            },\n            [\n              h(\n                'td',\n                {\n                  colspan: columns.length,\n                  class: `${ns.e('cell')} ${ns.e('expanded-cell')}`,\n                },\n                [renderExpanded({ row, $index, store, expanded })]\n              ),\n            ]\n          )\n        )\n      }\n\n      return rows\n    } else if (Object.keys(treeData.value).length) {\n      assertRowKey()\n      // TreeTable 时，rowKey 必须由用户设定，不使用 getKeyOfRow 计算\n      // 在调用 rowRender 函数时，仍然会计算 rowKey，不太好的操作\n      const key = getRowIdentity(row, rowKey.value)\n      let cur = treeData.value[key]\n      let treeRowData = null\n      if (cur) {\n        treeRowData = {\n          expanded: cur.expanded,\n          level: cur.level,\n          display: true,\n        }\n        if (isBoolean(cur.lazy)) {\n          if (isBoolean(cur.loaded) && cur.loaded) {\n            treeRowData.noLazyChildren = !(cur.children && cur.children.length)\n          }\n          treeRowData.loading = cur.loading\n        }\n      }\n      const tmp = [rowRender(row, $index, treeRowData)]\n      // 渲染嵌套数据\n      if (cur) {\n        // currentRow 记录的是 index，所以还需主动增加 TreeTable 的 index\n        let i = 0\n        const traverse = (children, parent) => {\n          if (!(children && children.length && parent)) return\n          children.forEach((node) => {\n            // 父节点的 display 状态影响子节点的显示状态\n            const innerTreeRowData = {\n              display: parent.display && parent.expanded,\n              level: parent.level + 1,\n              expanded: false,\n              noLazyChildren: false,\n              loading: false,\n            }\n            const childKey = getRowIdentity(node, rowKey.value)\n            if (isPropAbsent(childKey)) {\n              throw new Error('For nested data item, row-key is required.')\n            }\n            cur = { ...treeData.value[childKey] }\n            // 对于当前节点，分成有无子节点两种情况。\n            // 如果包含子节点的，设置 expanded 属性。\n            // 对于它子节点的 display 属性由它本身的 expanded 与 display 共同决定。\n            if (cur) {\n              innerTreeRowData.expanded = cur.expanded\n              // 懒加载的某些节点，level 未知\n              cur.level = cur.level || innerTreeRowData.level\n              cur.display = !!(cur.expanded && innerTreeRowData.display)\n              if (isBoolean(cur.lazy)) {\n                if (isBoolean(cur.loaded) && cur.loaded) {\n                  innerTreeRowData.noLazyChildren = !(\n                    cur.children && cur.children.length\n                  )\n                }\n                innerTreeRowData.loading = cur.loading\n              }\n            }\n            i++\n            tmp.push(rowRender(node, $index + i, innerTreeRowData))\n            if (cur) {\n              const nodes =\n                lazyTreeNodeMap.value[childKey] ||\n                node[childrenColumnName.value]\n              traverse(nodes, cur)\n            }\n          })\n        }\n        // 对于 root 节点，display 一定为 true\n        cur.display = true\n        const nodes =\n          lazyTreeNodeMap.value[key] || row[childrenColumnName.value]\n        traverse(nodes, cur)\n      }\n      return tmp\n    } else {\n      return rowRender(row, $index, undefined)\n    }\n  }\n\n  return {\n    wrappedRowRender,\n    tooltipContent,\n    tooltipTrigger,\n  }\n}\n\nexport default useRender\n"], "mappings": ";;;;;;;;;;;;;;AASA,SAASA,SAASA,CAACC,KAAK,EAAE;EACxB,MAAMC,MAAM,GAAGC,MAAM,CAACC,mBAAmB,CAAC;EAC1C,MAAMC,EAAE,GAAGC,YAAY,CAAC,OAAO,CAAC;EAChC,MAAM;IACJC,iBAAiB;IACjBC,WAAW;IACXC,iBAAiB;IACjBC,gBAAgB;IAChBC,gBAAgB;IAChBC,oBAAoB;IACpBC,oBAAoB;IACpBC,cAAc;IACdC;EACJ,CAAG,GAAGC,SAAS,CAACf,KAAK,CAAC;EACpB,MAAM;IACJgB,WAAW;IACXC,WAAW;IACXC,YAAY;IACZC,YAAY;IACZC,OAAO;IACPC;EACJ,CAAG,GAAGC,SAAS,CAACtB,KAAK,CAAC;EACpB,MAAMuB,uBAAuB,GAAGC,QAAQ,CAAC,MAAM;IAC7C,OAAOxB,KAAK,CAACyB,KAAK,CAACC,MAAM,CAACC,OAAO,CAACC,KAAK,CAACC,SAAS,CAAC,CAAC;MAAEC;IAAI,CAAE,KAAKA,IAAI,KAAK,SAAS,CAAC;EACvF,CAAG,CAAC;EACF,MAAMC,WAAW,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IAClC,MAAMC,MAAM,GAAGjC,MAAM,CAACD,KAAK,CAACkC,MAAM;IAClC,IAAIA,MAAM,EAAE;MACV,OAAOC,cAAc,CAACH,GAAG,EAAEE,MAAM,CAAC;IACxC;IACI,OAAOD,KAAK;EAChB,CAAG;EACD,MAAMG,SAAS,GAAGA,CAACJ,GAAG,EAAEK,MAAM,EAAEC,WAAW,EAAEC,QAAQ,GAAG,KAAK,KAAK;IAChE,MAAM;MAAEC,aAAa;MAAEC,cAAc;MAAEhB;IAAK,CAAE,GAAGzB,KAAK;IACtD,MAAM;MAAE0C,MAAM;MAAEf;IAAO,CAAE,GAAGF,KAAK,CAACC,MAAM;IACxC,MAAMiB,UAAU,GAAG1B,WAAW,CAACe,GAAG,EAAEK,MAAM,CAAC;IAC3C,IAAIO,OAAO,GAAG,IAAI;IAClB,IAAIN,WAAW,EAAE;MACfK,UAAU,CAACE,IAAI,CAACzC,EAAE,CAAC0C,EAAE,CAAC,KAAK,EAAE,SAASR,WAAW,CAACS,KAAK,EAAE,CAAC,CAAC;MAC3DH,OAAO,GAAGN,WAAW,CAACM,OAAO;IACnC;IACI,MAAMI,YAAY,GAAGJ,OAAO,GAAG,IAAI,GAAG;MAAEA,OAAO,EAAE;IAAM,CAAE;IACzD,OAAOK,CAAC,CAAC,IAAI,EAAE;MACbC,KAAK,EAAE,CAACF,YAAY,EAAEhC,WAAW,CAACgB,GAAG,EAAEK,MAAM,CAAC,CAAC;MAC/Cc,KAAK,EAAER,UAAU;MACjBS,GAAG,EAAErB,WAAW,CAACC,GAAG,EAAEK,MAAM,CAAC;MAC7BgB,UAAU,EAAGC,MAAM,IAAKhD,iBAAiB,CAACgD,MAAM,EAAEtB,GAAG,CAAC;MACtDuB,OAAO,EAAGD,MAAM,IAAK/C,WAAW,CAAC+C,MAAM,EAAEtB,GAAG,CAAC;MAC7CwB,aAAa,EAAGF,MAAM,IAAK9C,iBAAiB,CAAC8C,MAAM,EAAEtB,GAAG,CAAC;MACzDyB,YAAY,EAAEA,CAAA,KAAMhD,gBAAgB,CAAC4B,MAAM,CAAC;MAC5CqB,YAAY,EAAEhD;IACpB,CAAK,EAAEiB,OAAO,CAACC,KAAK,CAAC+B,GAAG,CAAC,CAACC,MAAM,EAAEC,SAAS,KAAK;MAC1C,MAAM;QAAEC,OAAO;QAAEC;MAAO,CAAE,GAAG3C,OAAO,CAACY,GAAG,EAAE4B,MAAM,EAAEvB,MAAM,EAAEwB,SAAS,CAAC;MACpE,IAAI,CAACC,OAAO,IAAI,CAACC,OAAO,EAAE;QACxB,OAAO,IAAI;MACnB;MACM,MAAMC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEN,MAAM,CAAC;MAC5CI,UAAU,CAACG,SAAS,GAAG9C,mBAAmB,CAACM,OAAO,CAACC,KAAK,EAAEmC,OAAO,EAAEF,SAAS,CAAC;MAC7E,MAAMO,IAAI,GAAG;QACX3C,KAAK,EAAEzB,KAAK,CAACyB,KAAK;QAClB4C,KAAK,EAAErE,KAAK,CAACsE,OAAO,IAAIrE,MAAM;QAC9B2D,MAAM,EAAEI,UAAU;QAClBhC,GAAG;QACHK,MAAM;QACNwB,SAAS;QACTtB;MACR,CAAO;MACD,IAAIsB,SAAS,KAAKtC,uBAAuB,CAACK,KAAK,IAAIU,WAAW,EAAE;QAC9D8B,IAAI,CAACG,QAAQ,GAAG;UACd7B,MAAM,EAAEJ,WAAW,CAACS,KAAK,GAAGL,MAAM,CAACd,KAAK;UACxCmB,KAAK,EAAET,WAAW,CAACS;QAC7B,CAAS;QACD,IAAIyB,SAAS,CAAClC,WAAW,CAACC,QAAQ,CAAC,EAAE;UACnC6B,IAAI,CAACG,QAAQ,CAAChC,QAAQ,GAAGD,WAAW,CAACC,QAAQ;UAC7C,IAAI,SAAS,IAAID,WAAW,EAAE;YAC5B8B,IAAI,CAACG,QAAQ,CAACE,OAAO,GAAGnC,WAAW,CAACmC,OAAO;UACvD;UACU,IAAI,gBAAgB,IAAInC,WAAW,EAAE;YACnC8B,IAAI,CAACG,QAAQ,CAACG,cAAc,GAAGpC,WAAW,CAACoC,cAAc;UACrE;QACA;MACA;MACM,MAAMC,OAAO,GAAG,GAAG5C,WAAW,CAACC,GAAG,EAAEK,MAAM,CAAC,IAAIwB,SAAS,EAAE;MAC1D,MAAMe,QAAQ,GAAGZ,UAAU,CAACa,SAAS,IAAIb,UAAU,CAACc,YAAY,IAAI,EAAE;MACtE,MAAMC,oBAAoB,GAAGnB,MAAM,CAACoB,mBAAmB,IAAIC,KAAK,CAAC;QAC/DC,MAAM,EAAE1C;MAChB,CAAO,EAAEC,cAAc,EAAEmB,MAAM,CAACoB,mBAAmB,CAAC;MAC9C,OAAO/B,CAAC,CAACkC,SAAS,EAAE;QAClBjC,KAAK,EAAEhC,YAAY,CAACmB,MAAM,EAAEwB,SAAS,EAAE7B,GAAG,EAAE4B,MAAM,CAAC;QACnDT,KAAK,EAAEhC,YAAY,CAACkB,MAAM,EAAEwB,SAAS,EAAE7B,GAAG,EAAE4B,MAAM,EAAEG,OAAO,GAAG,CAAC,CAAC;QAChEX,GAAG,EAAE,GAAGwB,QAAQ,GAAGD,OAAO,EAAE;QAC5Bb,OAAO;QACPC,OAAO;QACPN,YAAY,EAAGH,MAAM,IAAK3C,oBAAoB,CAAC2C,MAAM,EAAEtB,GAAG,EAAE+C,oBAAoB,CAAC;QACjFrB,YAAY,EAAE9C;MACtB,CAAO,EAAE;QACDwE,OAAO,EAAEA,CAAA,KAAMC,YAAY,CAACxB,SAAS,EAAED,MAAM,EAAEQ,IAAI;MAC3D,CAAO,CAAC;IACR,CAAK,CAAC,CAAC;EACP,CAAG;EACD,MAAMiB,YAAY,GAAGA,CAACxB,SAAS,EAAED,MAAM,EAAEQ,IAAI,KAAK;IAChD,OAAOR,MAAM,CAAC0B,UAAU,CAAClB,IAAI,CAAC;EAClC,CAAG;EACD,MAAMmB,gBAAgB,GAAGA,CAACvD,GAAG,EAAEK,MAAM,KAAK;IACxC,MAAMZ,KAAK,GAAGzB,KAAK,CAACyB,KAAK;IACzB,MAAM;MAAE+D,aAAa;MAAEC;IAAY,CAAE,GAAGhE,KAAK;IAC7C,MAAM;MAAEiE,QAAQ;MAAEC,eAAe;MAAEC,kBAAkB;MAAE1D;IAAM,CAAE,GAAGT,KAAK,CAACC,MAAM;IAC9E,MAAMC,OAAO,GAAGF,KAAK,CAACC,MAAM,CAACC,OAAO,CAACC,KAAK;IAC1C,MAAMiE,eAAe,GAAGlE,OAAO,CAACmE,IAAI,CAAC,CAAC;MAAEhE;IAAI,CAAE,KAAKA,IAAI,KAAK,QAAQ,CAAC;IACrE,IAAI+D,eAAe,EAAE;MACnB,MAAMtD,QAAQ,GAAGiD,aAAa,CAACxD,GAAG,CAAC;MACnC,MAAM+D,EAAE,GAAG3D,SAAS,CAACJ,GAAG,EAAEK,MAAM,EAAE,KAAK,CAAC,EAAEE,QAAQ,CAAC;MACnD,MAAMyD,cAAc,GAAG/F,MAAM,CAAC+F,cAAc;MAC5C,IAAI,CAACA,cAAc,EAAE;QACnBC,OAAO,CAACC,KAAK,CAAC,4CAA4C,CAAC;QAC3D,OAAOH,EAAE;MACjB;MACM,MAAMI,IAAI,GAAG,CAAC,CAACJ,EAAE,CAAC,CAAC;MACnB,IAAI9F,MAAM,CAACD,KAAK,CAACoG,uBAAuB,IAAI7D,QAAQ,EAAE;QACpD4D,IAAI,CAAC,CAAC,CAAC,CAACtD,IAAI,CAACI,CAAC,CAAC,IAAI,EAAE;UACnBG,GAAG,EAAE,iBAAiB2C,EAAE,CAAC3C,GAAG,EAAE;UAC9BF,KAAK,EAAE;YAAEN,OAAO,EAAEL,QAAQ,GAAG,EAAE,GAAG;UAAM;QAClD,CAAS,EAAE,CACDU,CAAC,CAAC,IAAI,EAAE;UACNc,OAAO,EAAEpC,OAAO,CAAC0E,MAAM;UACvBlD,KAAK,EAAE,GAAG/C,EAAE,CAACkG,CAAC,CAAC,MAAM,CAAC,IAAIlG,EAAE,CAACkG,CAAC,CAAC,eAAe,CAAC;QAC3D,CAAW,EAAE,CAACN,cAAc,CAAC;UAAEhE,GAAG;UAAEK,MAAM;UAAEZ,KAAK;UAAEc;QAAQ,CAAE,CAAC,CAAC,CAAC,CACvD,CAAC,CAAC;MACX;MACM,OAAO4D,IAAI;IACjB,CAAK,MAAM,IAAIlC,MAAM,CAACsC,IAAI,CAACb,QAAQ,CAAC9D,KAAK,CAAC,CAACyE,MAAM,EAAE;MAC7CZ,YAAY,EAAE;MACd,MAAMrC,GAAG,GAAGjB,cAAc,CAACH,GAAG,EAAEE,MAAM,CAACN,KAAK,CAAC;MAC7C,IAAI4E,GAAG,GAAGd,QAAQ,CAAC9D,KAAK,CAACwB,GAAG,CAAC;MAC7B,IAAId,WAAW,GAAG,IAAI;MACtB,IAAIkE,GAAG,EAAE;QACPlE,WAAW,GAAG;UACZC,QAAQ,EAAEiE,GAAG,CAACjE,QAAQ;UACtBQ,KAAK,EAAEyD,GAAG,CAACzD,KAAK;UAChBH,OAAO,EAAE;QACnB,CAAS;QACD,IAAI4B,SAAS,CAACgC,GAAG,CAACC,IAAI,CAAC,EAAE;UACvB,IAAIjC,SAAS,CAACgC,GAAG,CAACE,MAAM,CAAC,IAAIF,GAAG,CAACE,MAAM,EAAE;YACvCpE,WAAW,CAACoC,cAAc,GAAG,EAAE8B,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACG,QAAQ,CAACN,MAAM,CAAC;UAC/E;UACU/D,WAAW,CAACmC,OAAO,GAAG+B,GAAG,CAAC/B,OAAO;QAC3C;MACA;MACM,MAAMmC,GAAG,GAAG,CAACxE,SAAS,CAACJ,GAAG,EAAEK,MAAM,EAAEC,WAAW,CAAC,CAAC;MACjD,IAAIkE,GAAG,EAAE;QACP,IAAIK,CAAC,GAAG,CAAC;QACT,MAAMC,QAAQ,GAAGA,CAACH,QAAQ,EAAEI,OAAO,KAAK;UACtC,IAAI,EAAEJ,QAAQ,IAAIA,QAAQ,CAACN,MAAM,IAAIU,OAAO,CAAC,EAC3C;UACFJ,QAAQ,CAACK,OAAO,CAAEC,IAAI,IAAK;YACzB,MAAMC,gBAAgB,GAAG;cACvBtE,OAAO,EAAEmE,OAAO,CAACnE,OAAO,IAAImE,OAAO,CAACxE,QAAQ;cAC5CQ,KAAK,EAAEgE,OAAO,CAAChE,KAAK,GAAG,CAAC;cACxBR,QAAQ,EAAE,KAAK;cACfmC,cAAc,EAAE,KAAK;cACrBD,OAAO,EAAE;YACvB,CAAa;YACD,MAAM0C,QAAQ,GAAGhF,cAAc,CAAC8E,IAAI,EAAE/E,MAAM,CAACN,KAAK,CAAC;YACnD,IAAIwF,YAAY,CAACD,QAAQ,CAAC,EAAE;cAC1B,MAAM,IAAIE,KAAK,CAAC,4CAA4C,CAAC;YAC3E;YACYb,GAAG,GAAG;cAAE,GAAGd,QAAQ,CAAC9D,KAAK,CAACuF,QAAQ;YAAC,CAAE;YACrC,IAAIX,GAAG,EAAE;cACPU,gBAAgB,CAAC3E,QAAQ,GAAGiE,GAAG,CAACjE,QAAQ;cACxCiE,GAAG,CAACzD,KAAK,GAAGyD,GAAG,CAACzD,KAAK,IAAImE,gBAAgB,CAACnE,KAAK;cAC/CyD,GAAG,CAAC5D,OAAO,GAAG,CAAC,EAAE4D,GAAG,CAACjE,QAAQ,IAAI2E,gBAAgB,CAACtE,OAAO,CAAC;cAC1D,IAAI4B,SAAS,CAACgC,GAAG,CAACC,IAAI,CAAC,EAAE;gBACvB,IAAIjC,SAAS,CAACgC,GAAG,CAACE,MAAM,CAAC,IAAIF,GAAG,CAACE,MAAM,EAAE;kBACvCQ,gBAAgB,CAACxC,cAAc,GAAG,EAAE8B,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACG,QAAQ,CAACN,MAAM,CAAC;gBAC1F;gBACgBa,gBAAgB,CAACzC,OAAO,GAAG+B,GAAG,CAAC/B,OAAO;cACtD;YACA;YACYoC,CAAC,EAAE;YACHD,GAAG,CAAC/D,IAAI,CAACT,SAAS,CAAC6E,IAAI,EAAE5E,MAAM,GAAGwE,CAAC,EAAEK,gBAAgB,CAAC,CAAC;YACvD,IAAIV,GAAG,EAAE;cACP,MAAMc,MAAM,GAAG3B,eAAe,CAAC/D,KAAK,CAACuF,QAAQ,CAAC,IAAIF,IAAI,CAACrB,kBAAkB,CAAChE,KAAK,CAAC;cAChFkF,QAAQ,CAACQ,MAAM,EAAEd,GAAG,CAAC;YACnC;UACA,CAAW,CAAC;QACZ,CAAS;QACDA,GAAG,CAAC5D,OAAO,GAAG,IAAI;QAClB,MAAM2E,KAAK,GAAG5B,eAAe,CAAC/D,KAAK,CAACwB,GAAG,CAAC,IAAIpB,GAAG,CAAC4D,kBAAkB,CAAChE,KAAK,CAAC;QACzEkF,QAAQ,CAACS,KAAK,EAAEf,GAAG,CAAC;MAC5B;MACM,OAAOI,GAAG;IAChB,CAAK,MAAM;MACL,OAAOxE,SAAS,CAACJ,GAAG,EAAEK,MAAM,EAAE,KAAK,CAAC,CAAC;IAC3C;EACA,CAAG;EACD,OAAO;IACLkD,gBAAgB;IAChB1E,cAAc;IACdC;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}