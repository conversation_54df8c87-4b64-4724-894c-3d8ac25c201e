{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"app-footer\"\n};\nconst _hoisted_2 = {\n  class: \"footer-content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"footer\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"p\", null, \" © \" + _toDisplayString($setup.currentYear) + \" 图书馆自习室管理系统 | 基于国密算法的安全座位管理平台 \", 1 /* TEXT */), _cache[0] || (_cache[0] = _createElementVNode(\"p\", null, [_createElementVNode(\"a\", {\n    href: \"/about\"\n  }, \"关于我们\"), _createTextVNode(\" | \"), _createElementVNode(\"a\", {\n    href: \"/privacy\"\n  }, \"隐私政策\"), _createTextVNode(\" | \"), _createElementVNode(\"a\", {\n    href: \"/terms\"\n  }, \"使用条款\"), _createTextVNode(\" | \"), _createElementVNode(\"a\", {\n    href: \"/contact\"\n  }, \"联系我们\")], -1 /* HOISTED */))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_toDisplayString", "$setup", "currentYear", "href", "_createTextVNode"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Footer.vue"], "sourcesContent": ["<template>\n  <footer class=\"app-footer\">\n    <div class=\"footer-content\">\n      <p>\n        &copy; {{ currentYear }} 图书馆自习室管理系统 |\n        基于国密算法的安全座位管理平台\n      </p>\n      <p>\n        <a href=\"/about\">关于我们</a> | <a href=\"/privacy\">隐私政策</a> |\n        <a href=\"/terms\">使用条款</a> |\n        <a href=\"/contact\">联系我们</a>\n      </p>\n    </div>\n  </footer>\n</template>\n\n<script>\nimport { computed } from \"vue\";\n\nexport default {\n  name: \"AppFooter\",\n  setup() {\n    const currentYear = computed(() => new Date().getFullYear());\n\n    return {\n      currentYear,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-footer {\n  background-color: #f5f7fa;\n  padding: 15px 0;\n  text-align: center;\n  font-size: 0.9rem;\n  color: #606266;\n  border-top: 1px solid #e6e6e6;\n}\n\n.footer-content {\n  max-width: 1200px;\n  margin: 0 auto;\n\n  p {\n    margin: 5px 0;\n  }\n\n  a {\n    color: #409eff;\n    text-decoration: none;\n\n    &:hover {\n      text-decoration: underline;\n    }\n  }\n}\n</style>\n"], "mappings": ";;EACUA,KAAK,EAAC;AAAY;;EACnBA,KAAK,EAAC;AAAgB;;uBAD7BC,mBAAA,CAYS,UAZTC,UAYS,GAXPC,mBAAA,CAUM,OAVNC,UAUM,GATJD,mBAAA,CAGI,WAHD,KACM,GAAAE,gBAAA,CAAGC,MAAA,CAAAC,WAAW,IAAG,gCAE1B,iB,0BACAJ,mBAAA,CAII,YAHFA,mBAAA,CAAyB;IAAtBK,IAAI,EAAC;EAAQ,GAAC,MAAI,GAR7BC,gBAAA,CAQiC,KAAG,GAAAN,mBAAA,CAA2B;IAAxBK,IAAI,EAAC;EAAU,GAAC,MAAI,GAR3DC,gBAAA,CAQ+D,KACvD,GAAAN,mBAAA,CAAyB;IAAtBK,IAAI,EAAC;EAAQ,GAAC,MAAI,GAT7BC,gBAAA,CASiC,KACzB,GAAAN,mBAAA,CAA2B;IAAxBK,IAAI,EAAC;EAAU,GAAC,MAAI,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}