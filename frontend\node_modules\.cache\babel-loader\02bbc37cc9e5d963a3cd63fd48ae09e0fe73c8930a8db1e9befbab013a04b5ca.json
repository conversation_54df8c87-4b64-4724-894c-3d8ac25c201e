{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"checkin-checkout-container\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"active-reservation\"\n};\nconst _hoisted_3 = {\n  class: \"reservation-info\"\n};\nconst _hoisted_4 = {\n  key: 0\n};\nconst _hoisted_5 = {\n  class: \"action-buttons\"\n};\nconst _hoisted_6 = {\n  class: \"no-reservation\"\n};\nconst _hoisted_7 = {\n  class: \"dialog-content\"\n};\nconst _hoisted_8 = {\n  class: \"seat-info\"\n};\nconst _hoisted_9 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_10 = {\n  class: \"dialog-content\"\n};\nconst _hoisted_11 = {\n  class: \"seat-info\"\n};\nconst _hoisted_12 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_13 = {\n  class: \"dialog-content\"\n};\nconst _hoisted_14 = {\n  class: \"seat-info\"\n};\nconst _hoisted_15 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_Location = _resolveComponent(\"Location\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_CircleCheck = _resolveComponent(\"CircleCheck\");\n  const _component_Close = _resolveComponent(\"Close\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_switch = _resolveComponent(\"el-switch\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"main-card\"\n  }, {\n    header: _withCtx(() => _cache[9] || (_cache[9] = [_createElementVNode(\"div\", {\n      class: \"card-header\"\n    }, [_createElementVNode(\"h2\", null, \"签到签退\")], -1 /* HOISTED */)])),\n    default: _withCtx(() => [$setup.activeReservation ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createVNode(_component_el_alert, {\n      title: `当前预约: ${$setup.activeReservation.seat.room.name} - ${$setup.activeReservation.seat.seat_number}`,\n      type: \"info\",\n      closable: false,\n      \"show-icon\": \"\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"p\", null, [_cache[10] || (_cache[10] = _createElementVNode(\"strong\", null, \"预约时间:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($setup.formatDateTime($setup.activeReservation.start_time)) + \" - \" + _toDisplayString($setup.formatDateTime($setup.activeReservation.end_time)), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[11] || (_cache[11] = _createElementVNode(\"strong\", null, \"状态:\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n        type: $setup.getStatusType($setup.activeReservation.status)\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText($setup.activeReservation.status)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"type\"])]), $setup.activeReservation.check_in_time ? (_openBlock(), _createElementBlock(\"p\", _hoisted_4, [_cache[12] || (_cache[12] = _createElementVNode(\"strong\", null, \"签到时间:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($setup.formatDateTime($setup.activeReservation.check_in_time)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"title\"]), _createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_5, [$setup.activeReservation.status === 'pending' ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 0,\n      type: \"primary\",\n      size: \"large\",\n      loading: $setup.checkingIn,\n      onClick: $setup.showCheckInDialog\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Location)]),\n        _: 1 /* STABLE */\n      }), _cache[13] || (_cache[13] = _createTextVNode(\" 签到 \"))]),\n      _: 1 /* STABLE */,\n      __: [13]\n    }, 8 /* PROPS */, [\"loading\", \"onClick\"])) : _createCommentVNode(\"v-if\", true), $setup.activeReservation.status === 'checked_in' ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 1,\n      type: \"success\",\n      size: \"large\",\n      loading: $setup.checkingOut,\n      onClick: $setup.showCheckOutDialog\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_CircleCheck)]),\n        _: 1 /* STABLE */\n      }), _cache[14] || (_cache[14] = _createTextVNode(\" 签退 \"))]),\n      _: 1 /* STABLE */,\n      __: [14]\n    }, 8 /* PROPS */, [\"loading\", \"onClick\"])) : _createCommentVNode(\"v-if\", true), $setup.activeReservation.status === 'pending' ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 2,\n      type: \"danger\",\n      size: \"large\",\n      loading: $setup.cancelling,\n      onClick: $setup.showCancelDialog\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Close)]),\n        _: 1 /* STABLE */\n      }), _cache[15] || (_cache[15] = _createTextVNode(\" 取消预约 \"))]),\n      _: 1 /* STABLE */,\n      __: [15]\n    }, 8 /* PROPS */, [\"loading\", \"onClick\"])) : _createCommentVNode(\"v-if\", true)])])) : (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" 无活跃预约 \"), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_empty, {\n      description: \"当前没有活跃的预约\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/seat/reservation'))\n      }, {\n        default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\" 去预约座位 \")])),\n        _: 1 /* STABLE */,\n        __: [16]\n      })]),\n      _: 1 /* STABLE */\n    })])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 签到对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.checkInDialogVisible,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.checkInDialogVisible = $event),\n    title: \"确认签到\",\n    width: \"400px\",\n    \"before-close\": $setup.handleDialogClose\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_9, [_createVNode(_component_el_button, {\n      onClick: _cache[2] || (_cache[2] = $event => $setup.checkInDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [21]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      loading: $setup.checkingIn,\n      onClick: $setup.handleCheckIn\n    }, {\n      default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\" 确认签到 \")])),\n      _: 1 /* STABLE */,\n      __: [22]\n    }, 8 /* PROPS */, [\"loading\", \"onClick\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_cache[20] || (_cache[20] = _createElementVNode(\"p\", null, \"确认签到到以下座位？\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"p\", null, [_cache[17] || (_cache[17] = _createElementVNode(\"strong\", null, \"自习室:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($setup.activeReservation?.seat.room.name), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[18] || (_cache[18] = _createElementVNode(\"strong\", null, \"座位:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($setup.activeReservation?.seat.seat_number), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[19] || (_cache[19] = _createElementVNode(\"strong\", null, \"预约时间:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($setup.formatDateTime($setup.activeReservation?.start_time)) + \" - \" + _toDisplayString($setup.formatDateTime($setup.activeReservation?.end_time)), 1 /* TEXT */)])]), _createCommentVNode(\" 签名选项 \"), $setup.userHasPublicKey ? (_openBlock(), _createBlock(_component_el_form, {\n      key: 0,\n      model: $setup.checkInForm,\n      \"label-position\": \"top\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"使用数字签名验证身份\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_switch, {\n          modelValue: $setup.checkInForm.useSignature,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.checkInForm.useSignature = $event),\n          \"active-text\": \"启用\",\n          \"inactive-text\": \"禁用\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])) : _createCommentVNode(\"v-if\", true)])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"before-close\"]), _createCommentVNode(\" 签退对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.checkOutDialogVisible,\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.checkOutDialogVisible = $event),\n    title: \"确认签退\",\n    width: \"400px\",\n    \"before-close\": $setup.handleDialogClose\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_12, [_createVNode(_component_el_button, {\n      onClick: _cache[5] || (_cache[5] = $event => $setup.checkOutDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [27]\n    }), _createVNode(_component_el_button, {\n      type: \"success\",\n      loading: $setup.checkingOut,\n      onClick: $setup.handleCheckOut\n    }, {\n      default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\" 确认签退 \")])),\n      _: 1 /* STABLE */,\n      __: [28]\n    }, 8 /* PROPS */, [\"loading\", \"onClick\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_10, [_cache[26] || (_cache[26] = _createElementVNode(\"p\", null, \"确认签退以下座位？\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"p\", null, [_cache[23] || (_cache[23] = _createElementVNode(\"strong\", null, \"自习室:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($setup.activeReservation?.seat.room.name), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[24] || (_cache[24] = _createElementVNode(\"strong\", null, \"座位:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($setup.activeReservation?.seat.seat_number), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[25] || (_cache[25] = _createElementVNode(\"strong\", null, \"签到时间:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($setup.formatDateTime($setup.activeReservation?.check_in_time)), 1 /* TEXT */)])]), _createCommentVNode(\" 签名选项 \"), $setup.userHasPublicKey ? (_openBlock(), _createBlock(_component_el_form, {\n      key: 0,\n      model: $setup.checkOutForm,\n      \"label-position\": \"top\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"使用数字签名验证身份\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_switch, {\n          modelValue: $setup.checkOutForm.useSignature,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.checkOutForm.useSignature = $event),\n          \"active-text\": \"启用\",\n          \"inactive-text\": \"禁用\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])) : _createCommentVNode(\"v-if\", true)])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"before-close\"]), _createCommentVNode(\" 取消预约对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.cancelDialogVisible,\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.cancelDialogVisible = $event),\n    title: \"取消预约\",\n    width: \"400px\",\n    \"before-close\": $setup.handleDialogClose\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_15, [_createVNode(_component_el_button, {\n      onClick: _cache[7] || (_cache[7] = $event => $setup.cancelDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[33] || (_cache[33] = [_createTextVNode(\"不取消\")])),\n      _: 1 /* STABLE */,\n      __: [33]\n    }), _createVNode(_component_el_button, {\n      type: \"danger\",\n      loading: $setup.cancelling,\n      onClick: $setup.handleCancel\n    }, {\n      default: _withCtx(() => _cache[34] || (_cache[34] = [_createTextVNode(\" 确认取消 \")])),\n      _: 1 /* STABLE */,\n      __: [34]\n    }, 8 /* PROPS */, [\"loading\", \"onClick\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_cache[32] || (_cache[32] = _createElementVNode(\"p\", null, \"确认取消以下预约？\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"p\", null, [_cache[29] || (_cache[29] = _createElementVNode(\"strong\", null, \"自习室:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($setup.activeReservation?.seat.room.name), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[30] || (_cache[30] = _createElementVNode(\"strong\", null, \"座位:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($setup.activeReservation?.seat.seat_number), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[31] || (_cache[31] = _createElementVNode(\"strong\", null, \"预约时间:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($setup.formatDateTime($setup.activeReservation?.start_time)) + \" - \" + _toDisplayString($setup.formatDateTime($setup.activeReservation?.end_time)), 1 /* TEXT */)])]), _createVNode(_component_el_alert, {\n      title: \"注意：取消预约可能会影响您的信誉分\",\n      type: \"warning\",\n      closable: false,\n      \"show-icon\": \"\"\n    })])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"before-close\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_cache", "_createElementVNode", "default", "$setup", "activeReservation", "_hoisted_2", "_component_el_alert", "title", "seat", "room", "name", "seat_number", "type", "closable", "_hoisted_3", "_createTextVNode", "_toDisplayString", "formatDateTime", "start_time", "end_time", "_component_el_tag", "getStatusType", "status", "getStatusText", "_", "check_in_time", "_hoisted_4", "_createCommentVNode", "_hoisted_5", "_createBlock", "_component_el_button", "size", "loading", "checkingIn", "onClick", "showCheckInDialog", "_component_el_icon", "_component_Location", "__", "checkingOut", "showCheckOutDialog", "_component_CircleCheck", "cancelling", "showCancelDialog", "_component_Close", "_Fragment", "_hoisted_6", "_component_el_empty", "description", "$event", "_ctx", "$router", "push", "_component_el_dialog", "modelValue", "checkInDialogVisible", "width", "handleDialogClose", "footer", "_hoisted_9", "handleCheckIn", "_hoisted_7", "_hoisted_8", "userHasPublicKey", "_component_el_form", "model", "checkInForm", "_component_el_form_item", "label", "_component_el_switch", "useSignature", "checkOutDialogVisible", "_hoisted_12", "handleCheckOut", "_hoisted_10", "_hoisted_11", "checkOutForm", "cancelDialogVisible", "_hoisted_15", "handleCancel", "_hoisted_13", "_hoisted_14"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\CheckInOut.vue"], "sourcesContent": ["<template>\n  <div class=\"checkin-checkout-container\">\n    <el-card class=\"main-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h2>签到签退</h2>\n        </div>\n      </template>\n\n      <!-- 当前活跃预约 -->\n      <div v-if=\"activeReservation\" class=\"active-reservation\">\n        <el-alert\n          :title=\"`当前预约: ${activeReservation.seat.room.name} - ${activeReservation.seat.seat_number}`\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon\n        >\n          <template #default>\n            <div class=\"reservation-info\">\n              <p>\n                <strong>预约时间:</strong>\n                {{ formatDateTime(activeReservation.start_time) }} -\n                {{ formatDateTime(activeReservation.end_time) }}\n              </p>\n              <p>\n                <strong>状态:</strong>\n                <el-tag :type=\"getStatusType(activeReservation.status)\">\n                  {{ getStatusText(activeReservation.status) }}\n                </el-tag>\n              </p>\n              <p v-if=\"activeReservation.check_in_time\">\n                <strong>签到时间:</strong>\n                {{ formatDateTime(activeReservation.check_in_time) }}\n              </p>\n            </div>\n          </template>\n        </el-alert>\n\n        <!-- 操作按钮 -->\n        <div class=\"action-buttons\">\n          <el-button\n            v-if=\"activeReservation.status === 'pending'\"\n            type=\"primary\"\n            size=\"large\"\n            :loading=\"checkingIn\"\n            @click=\"showCheckInDialog\"\n          >\n            <el-icon><Location /></el-icon>\n            签到\n          </el-button>\n\n          <el-button\n            v-if=\"activeReservation.status === 'checked_in'\"\n            type=\"success\"\n            size=\"large\"\n            :loading=\"checkingOut\"\n            @click=\"showCheckOutDialog\"\n          >\n            <el-icon><CircleCheck /></el-icon>\n            签退\n          </el-button>\n\n          <el-button\n            v-if=\"activeReservation.status === 'pending'\"\n            type=\"danger\"\n            size=\"large\"\n            :loading=\"cancelling\"\n            @click=\"showCancelDialog\"\n          >\n            <el-icon><Close /></el-icon>\n            取消预约\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 无活跃预约 -->\n      <div v-else class=\"no-reservation\">\n        <el-empty description=\"当前没有活跃的预约\">\n          <el-button type=\"primary\" @click=\"$router.push('/seat/reservation')\">\n            去预约座位\n          </el-button>\n        </el-empty>\n      </div>\n    </el-card>\n\n    <!-- 签到对话框 -->\n    <el-dialog\n      v-model=\"checkInDialogVisible\"\n      title=\"确认签到\"\n      width=\"400px\"\n      :before-close=\"handleDialogClose\"\n    >\n      <div class=\"dialog-content\">\n        <p>确认签到到以下座位？</p>\n        <div class=\"seat-info\">\n          <p>\n            <strong>自习室:</strong> {{ activeReservation?.seat.room.name }}\n          </p>\n          <p>\n            <strong>座位:</strong> {{ activeReservation?.seat.seat_number }}\n          </p>\n          <p>\n            <strong>预约时间:</strong>\n            {{ formatDateTime(activeReservation?.start_time) }} -\n            {{ formatDateTime(activeReservation?.end_time) }}\n          </p>\n        </div>\n\n        <!-- 签名选项 -->\n        <el-form\n          v-if=\"userHasPublicKey\"\n          :model=\"checkInForm\"\n          label-position=\"top\"\n        >\n          <el-form-item label=\"使用数字签名验证身份\">\n            <el-switch\n              v-model=\"checkInForm.useSignature\"\n              active-text=\"启用\"\n              inactive-text=\"禁用\"\n            />\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"checkInDialogVisible = false\">取消</el-button>\n          <el-button\n            type=\"primary\"\n            :loading=\"checkingIn\"\n            @click=\"handleCheckIn\"\n          >\n            确认签到\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 签退对话框 -->\n    <el-dialog\n      v-model=\"checkOutDialogVisible\"\n      title=\"确认签退\"\n      width=\"400px\"\n      :before-close=\"handleDialogClose\"\n    >\n      <div class=\"dialog-content\">\n        <p>确认签退以下座位？</p>\n        <div class=\"seat-info\">\n          <p>\n            <strong>自习室:</strong> {{ activeReservation?.seat.room.name }}\n          </p>\n          <p>\n            <strong>座位:</strong> {{ activeReservation?.seat.seat_number }}\n          </p>\n          <p>\n            <strong>签到时间:</strong>\n            {{ formatDateTime(activeReservation?.check_in_time) }}\n          </p>\n        </div>\n\n        <!-- 签名选项 -->\n        <el-form\n          v-if=\"userHasPublicKey\"\n          :model=\"checkOutForm\"\n          label-position=\"top\"\n        >\n          <el-form-item label=\"使用数字签名验证身份\">\n            <el-switch\n              v-model=\"checkOutForm.useSignature\"\n              active-text=\"启用\"\n              inactive-text=\"禁用\"\n            />\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"checkOutDialogVisible = false\">取消</el-button>\n          <el-button\n            type=\"success\"\n            :loading=\"checkingOut\"\n            @click=\"handleCheckOut\"\n          >\n            确认签退\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 取消预约对话框 -->\n    <el-dialog\n      v-model=\"cancelDialogVisible\"\n      title=\"取消预约\"\n      width=\"400px\"\n      :before-close=\"handleDialogClose\"\n    >\n      <div class=\"dialog-content\">\n        <p>确认取消以下预约？</p>\n        <div class=\"seat-info\">\n          <p>\n            <strong>自习室:</strong> {{ activeReservation?.seat.room.name }}\n          </p>\n          <p>\n            <strong>座位:</strong> {{ activeReservation?.seat.seat_number }}\n          </p>\n          <p>\n            <strong>预约时间:</strong>\n            {{ formatDateTime(activeReservation?.start_time) }} -\n            {{ formatDateTime(activeReservation?.end_time) }}\n          </p>\n        </div>\n        <el-alert\n          title=\"注意：取消预约可能会影响您的信誉分\"\n          type=\"warning\"\n          :closable=\"false\"\n          show-icon\n        />\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"cancelDialogVisible = false\">不取消</el-button>\n          <el-button type=\"danger\" :loading=\"cancelling\" @click=\"handleCancel\">\n            确认取消\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, computed } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { ElMessage } from \"element-plus\";\nimport { Location, CircleCheck, Close } from \"@element-plus/icons-vue\";\nimport { SM2Crypto } from \"@/utils/crypto\";\n\nexport default {\n  name: \"CheckInOut\",\n  components: {\n    Location,\n    CircleCheck,\n    Close,\n  },\n  setup() {\n    const store = useStore();\n\n    // 响应式数据\n    const activeReservation = ref(null);\n    const checkingIn = ref(false);\n    const checkingOut = ref(false);\n    const cancelling = ref(false);\n\n    // 对话框状态\n    const checkInDialogVisible = ref(false);\n    const checkOutDialogVisible = ref(false);\n    const cancelDialogVisible = ref(false);\n\n    // 表单数据\n    const checkInForm = reactive({\n      useSignature: false,\n    });\n\n    const checkOutForm = reactive({\n      useSignature: false,\n    });\n\n    // 计算属性\n    const userHasPublicKey = computed(() => {\n      return store.getters[\"user/userInfo\"]?.public_key;\n    });\n\n    // 方法\n    const loadActiveReservation = async () => {\n      try {\n        const response = await store.dispatch(\"seat/getActiveReservation\");\n        activeReservation.value = response;\n      } catch (error) {\n        if (error.response?.status !== 404) {\n          ElMessage.error(\"获取活跃预约失败\");\n        }\n      }\n    };\n\n    const showCheckInDialog = () => {\n      checkInDialogVisible.value = true;\n    };\n\n    const showCheckOutDialog = () => {\n      checkOutDialogVisible.value = true;\n    };\n\n    const showCancelDialog = () => {\n      cancelDialogVisible.value = true;\n    };\n\n    const handleCheckIn = async () => {\n      try {\n        checkingIn.value = true;\n\n        const requestData = {\n          reservation_code: activeReservation.value.reservation_code,\n        };\n\n        // 如果启用签名\n        if (checkInForm.useSignature && userHasPublicKey.value) {\n          const privateKey = localStorage.getItem(\"sm2_private_key\");\n          if (privateKey) {\n            const signature = SM2Crypto.sign(\n              privateKey,\n              activeReservation.value.reservation_code\n            );\n            requestData.signature = signature;\n          }\n        }\n\n        await store.dispatch(\"seat/checkIn\", requestData);\n\n        ElMessage.success(\"签到成功\");\n        checkInDialogVisible.value = false;\n        await loadActiveReservation();\n      } catch (error) {\n        ElMessage.error(error.message || \"签到失败\");\n      } finally {\n        checkingIn.value = false;\n      }\n    };\n\n    const handleCheckOut = async () => {\n      try {\n        checkingOut.value = true;\n\n        const requestData = {\n          reservation_id: activeReservation.value.id,\n        };\n\n        // 如果启用签名\n        if (checkOutForm.useSignature && userHasPublicKey.value) {\n          const privateKey = localStorage.getItem(\"sm2_private_key\");\n          if (privateKey) {\n            const signature = SM2Crypto.sign(\n              privateKey,\n              activeReservation.value.reservation_code\n            );\n            requestData.signature = signature;\n          }\n        }\n\n        await store.dispatch(\"seat/checkOut\", requestData);\n\n        ElMessage.success(\"签退成功\");\n        checkOutDialogVisible.value = false;\n        await loadActiveReservation();\n      } catch (error) {\n        ElMessage.error(error.message || \"签退失败\");\n      } finally {\n        checkingOut.value = false;\n      }\n    };\n\n    const handleCancel = async () => {\n      try {\n        cancelling.value = true;\n\n        await store.dispatch(\"seat/cancelReservation\", {\n          id: activeReservation.value.id,\n        });\n\n        ElMessage.success(\"预约已取消\");\n        cancelDialogVisible.value = false;\n        await loadActiveReservation();\n      } catch (error) {\n        ElMessage.error(error.message || \"取消预约失败\");\n      } finally {\n        cancelling.value = false;\n      }\n    };\n\n    const handleDialogClose = (done) => {\n      if (checkingIn.value || checkingOut.value || cancelling.value) {\n        ElMessage.warning(\"操作进行中，请稍候...\");\n        return;\n      }\n      done();\n    };\n\n    const getStatusType = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    const getStatusText = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知\";\n      }\n    };\n\n    const formatDateTime = (dateString) => {\n      if (!dateString) return \"\";\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(\n        2,\n        \"0\"\n      )}-${String(date.getDate()).padStart(2, \"0\")} ${String(\n        date.getHours()\n      ).padStart(2, \"0\")}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n    };\n\n    // 生命周期\n    onMounted(() => {\n      loadActiveReservation();\n    });\n\n    return {\n      activeReservation,\n      checkingIn,\n      checkingOut,\n      cancelling,\n      checkInDialogVisible,\n      checkOutDialogVisible,\n      cancelDialogVisible,\n      checkInForm,\n      checkOutForm,\n      userHasPublicKey,\n      showCheckInDialog,\n      showCheckOutDialog,\n      showCancelDialog,\n      handleCheckIn,\n      handleCheckOut,\n      handleCancel,\n      handleDialogClose,\n      getStatusType,\n      getStatusText,\n      formatDateTime,\n    };\n  },\n};\n</script>\n\n<style scoped>\n.checkin-checkout-container {\n  padding: 20px;\n}\n\n.main-card {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.card-header h2 {\n  margin: 0;\n  color: #303133;\n}\n\n.active-reservation {\n  margin-bottom: 20px;\n}\n\n.reservation-info {\n  margin-top: 10px;\n}\n\n.reservation-info p {\n  margin: 5px 0;\n  color: #606266;\n}\n\n.action-buttons {\n  margin-top: 20px;\n  text-align: center;\n}\n\n.action-buttons .el-button {\n  margin: 0 10px;\n  min-width: 120px;\n}\n\n.no-reservation {\n  text-align: center;\n  padding: 40px 0;\n}\n\n.dialog-content {\n  text-align: center;\n}\n\n.seat-info {\n  background-color: #f5f7fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin: 15px 0;\n  text-align: left;\n}\n\n.seat-info p {\n  margin: 5px 0;\n  color: #606266;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAA4B;;EADzCC,GAAA;EAUoCD,KAAK,EAAC;;;EAQzBA,KAAK,EAAC;AAAkB;;EAlBzCC,GAAA;AAAA;;EAuCaD,KAAK,EAAC;AAAgB;;EAqCjBA,KAAK,EAAC;AAAgB;;EAgB7BA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAW;;EA+BhBA,KAAK,EAAC;AAAe;;EAoBxBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAW;;EA8BhBA,KAAK,EAAC;AAAe;;EAoBxBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAW;;EAsBhBA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;uBA5NjCE,mBAAA,CAoOM,OApONC,UAoOM,GAnOJC,YAAA,CAiFUC,kBAAA;IAjFDL,KAAK,EAAC;EAAW;IACbM,MAAM,EAAAC,QAAA,CACf,MAEMC,MAAA,QAAAA,MAAA,OAFNC,mBAAA,CAEM;MAFDT,KAAK,EAAC;IAAa,IACtBS,mBAAA,CAAa,YAAT,MAAI,E;IALlBC,OAAA,EAAAH,QAAA,CAUM,MA+DM,CA/DKI,MAAA,CAAAC,iBAAiB,I,cAA5BV,mBAAA,CA+DM,OA/DNW,UA+DM,GA9DJT,YAAA,CAyBWU,mBAAA;MAxBRC,KAAK,WAAWJ,MAAA,CAAAC,iBAAiB,CAACI,IAAI,CAACC,IAAI,CAACC,IAAI,MAAMP,MAAA,CAAAC,iBAAiB,CAACI,IAAI,CAACG,WAAW;MACzFC,IAAI,EAAC,MAAM;MACVC,QAAQ,EAAE,KAAK;MAChB,WAAS,EAAT;;MAEWX,OAAO,EAAAH,QAAA,CAChB,MAgBM,CAhBNE,mBAAA,CAgBM,OAhBNa,UAgBM,GAfJb,mBAAA,CAII,Y,4BAHFA,mBAAA,CAAsB,gBAAd,OAAK,sBApB7Bc,gBAAA,CAoBsC,GACtB,GAAAC,gBAAA,CAAGb,MAAA,CAAAc,cAAc,CAACd,MAAA,CAAAC,iBAAiB,CAACc,UAAU,KAAI,KAClD,GAAAF,gBAAA,CAAGb,MAAA,CAAAc,cAAc,CAACd,MAAA,CAAAC,iBAAiB,CAACe,QAAQ,kB,GAE9ClB,mBAAA,CAKI,Y,4BAJFA,mBAAA,CAAoB,gBAAZ,KAAG,sBACXL,YAAA,CAESwB,iBAAA;QAFAR,IAAI,EAAET,MAAA,CAAAkB,aAAa,CAAClB,MAAA,CAAAC,iBAAiB,CAACkB,MAAM;;QA1BrEpB,OAAA,EAAAH,QAAA,CA2BkB,MAA6C,CA3B/DgB,gBAAA,CAAAC,gBAAA,CA2BqBb,MAAA,CAAAoB,aAAa,CAACpB,MAAA,CAAAC,iBAAiB,CAACkB,MAAM,kB;QA3B3DE,CAAA;qCA8BuBrB,MAAA,CAAAC,iBAAiB,CAACqB,aAAa,I,cAAxC/B,mBAAA,CAGI,KAjClBgC,UAAA,G,4BA+BgBzB,mBAAA,CAAsB,gBAAd,OAAK,sBA/B7Bc,gBAAA,CA+BsC,GACtB,GAAAC,gBAAA,CAAGb,MAAA,CAAAc,cAAc,CAACd,MAAA,CAAAC,iBAAiB,CAACqB,aAAa,kB,KAhCjEE,mBAAA,e;MAAAH,CAAA;kCAsCQG,mBAAA,UAAa,EACb1B,mBAAA,CAiCM,OAjCN2B,UAiCM,GA/BIzB,MAAA,CAAAC,iBAAiB,CAACkB,MAAM,kB,cADhCO,YAAA,CASYC,oBAAA;MAjDtBrC,GAAA;MA0CYmB,IAAI,EAAC,SAAS;MACdmB,IAAI,EAAC,OAAO;MACXC,OAAO,EAAE7B,MAAA,CAAA8B,UAAU;MACnBC,OAAK,EAAE/B,MAAA,CAAAgC;;MA7CpBjC,OAAA,EAAAH,QAAA,CA+CY,MAA+B,CAA/BH,YAAA,CAA+BwC,kBAAA;QA/C3ClC,OAAA,EAAAH,QAAA,CA+CqB,MAAY,CAAZH,YAAA,CAAYyC,mBAAA,E;QA/CjCb,CAAA;sCAAAT,gBAAA,CA+C2C,MAEjC,G;MAjDVS,CAAA;MAAAc,EAAA;iDAAAX,mBAAA,gBAoDkBxB,MAAA,CAAAC,iBAAiB,CAACkB,MAAM,qB,cADhCO,YAAA,CASYC,oBAAA;MA5DtBrC,GAAA;MAqDYmB,IAAI,EAAC,SAAS;MACdmB,IAAI,EAAC,OAAO;MACXC,OAAO,EAAE7B,MAAA,CAAAoC,WAAW;MACpBL,OAAK,EAAE/B,MAAA,CAAAqC;;MAxDpBtC,OAAA,EAAAH,QAAA,CA0DY,MAAkC,CAAlCH,YAAA,CAAkCwC,kBAAA;QA1D9ClC,OAAA,EAAAH,QAAA,CA0DqB,MAAe,CAAfH,YAAA,CAAe6C,sBAAA,E;QA1DpCjB,CAAA;sCAAAT,gBAAA,CA0D8C,MAEpC,G;MA5DVS,CAAA;MAAAc,EAAA;iDAAAX,mBAAA,gBA+DkBxB,MAAA,CAAAC,iBAAiB,CAACkB,MAAM,kB,cADhCO,YAAA,CASYC,oBAAA;MAvEtBrC,GAAA;MAgEYmB,IAAI,EAAC,QAAQ;MACbmB,IAAI,EAAC,OAAO;MACXC,OAAO,EAAE7B,MAAA,CAAAuC,UAAU;MACnBR,OAAK,EAAE/B,MAAA,CAAAwC;;MAnEpBzC,OAAA,EAAAH,QAAA,CAqEY,MAA4B,CAA5BH,YAAA,CAA4BwC,kBAAA;QArExClC,OAAA,EAAAH,QAAA,CAqEqB,MAAS,CAATH,YAAA,CAASgD,gBAAA,E;QArE9BpB,CAAA;sCAAAT,gBAAA,CAqEwC,QAE9B,G;MAvEVS,CAAA;MAAAc,EAAA;iDAAAX,mBAAA,e,sBA4EMjC,mBAAA,CAMMmD,SAAA;MAlFZpD,GAAA;IAAA,IA2EMkC,mBAAA,WAAc,EACd1B,mBAAA,CAMM,OANN6C,UAMM,GALJlD,YAAA,CAIWmD,mBAAA;MAJDC,WAAW,EAAC;IAAW;MA7EzC9C,OAAA,EAAAH,QAAA,CA8EU,MAEY,CAFZH,YAAA,CAEYkC,oBAAA;QAFDlB,IAAI,EAAC,SAAS;QAAEsB,OAAK,EAAAlC,MAAA,QAAAA,MAAA,MAAAiD,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;QA9ExDlD,OAAA,EAAAH,QAAA,CA8E+E,MAErEC,MAAA,SAAAA,MAAA,QAhFVe,gBAAA,CA8E+E,SAErE,E;QAhFVS,CAAA;QAAAc,EAAA;;MAAAd,CAAA;;IAAAA,CAAA;MAqFIG,mBAAA,WAAc,EACd/B,YAAA,CAkDYyD,oBAAA;IAxIhBC,UAAA,EAuFenD,MAAA,CAAAoD,oBAAoB;IAvFnC,uBAAAvD,MAAA,QAAAA,MAAA,MAAAiD,MAAA,IAuFe9C,MAAA,CAAAoD,oBAAoB,GAAAN,MAAA;IAC7B1C,KAAK,EAAC,MAAM;IACZiD,KAAK,EAAC,OAAO;IACZ,cAAY,EAAErD,MAAA,CAAAsD;;IAkCJC,MAAM,EAAA3D,QAAA,CACf,MASO,CATPE,mBAAA,CASO,QATP0D,UASO,GARL/D,YAAA,CAA+DkC,oBAAA;MAAnDI,OAAK,EAAAlC,MAAA,QAAAA,MAAA,MAAAiD,MAAA,IAAE9C,MAAA,CAAAoD,oBAAoB;;MA9HjDrD,OAAA,EAAAH,QAAA,CA8H2D,MAAEC,MAAA,SAAAA,MAAA,QA9H7De,gBAAA,CA8H2D,IAAE,E;MA9H7DS,CAAA;MAAAc,EAAA;QA+HU1C,YAAA,CAMYkC,oBAAA;MALVlB,IAAI,EAAC,SAAS;MACboB,OAAO,EAAE7B,MAAA,CAAA8B,UAAU;MACnBC,OAAK,EAAE/B,MAAA,CAAAyD;;MAlIpB1D,OAAA,EAAAH,QAAA,CAmIW,MAEDC,MAAA,SAAAA,MAAA,QArIVe,gBAAA,CAmIW,QAED,E;MArIVS,CAAA;MAAAc,EAAA;;IAAApC,OAAA,EAAAH,QAAA,CA4FM,MA8BM,CA9BNE,mBAAA,CA8BM,OA9BN4D,UA8BM,G,4BA7BJ5D,mBAAA,CAAiB,WAAd,YAAU,sBACbA,mBAAA,CAYM,OAZN6D,UAYM,GAXJ7D,mBAAA,CAEI,Y,4BADFA,mBAAA,CAAqB,gBAAb,MAAI,sBAhGxBc,gBAAA,CAgGiC,GAAC,GAAAC,gBAAA,CAAGb,MAAA,CAAAC,iBAAiB,EAAEI,IAAI,CAACC,IAAI,CAACC,IAAI,iB,GAE5DT,mBAAA,CAEI,Y,4BADFA,mBAAA,CAAoB,gBAAZ,KAAG,sBAnGvBc,gBAAA,CAmGgC,GAAC,GAAAC,gBAAA,CAAGb,MAAA,CAAAC,iBAAiB,EAAEI,IAAI,CAACG,WAAW,iB,GAE7DV,mBAAA,CAII,Y,4BAHFA,mBAAA,CAAsB,gBAAd,OAAK,sBAtGzBc,gBAAA,CAsGkC,GACtB,GAAAC,gBAAA,CAAGb,MAAA,CAAAc,cAAc,CAACd,MAAA,CAAAC,iBAAiB,EAAEc,UAAU,KAAI,KACnD,GAAAF,gBAAA,CAAGb,MAAA,CAAAc,cAAc,CAACd,MAAA,CAAAC,iBAAiB,EAAEe,QAAQ,kB,KAIjDQ,mBAAA,UAAa,EAELxB,MAAA,CAAA4D,gBAAgB,I,cADxBlC,YAAA,CAYUmC,kBAAA;MAzHlBvE,GAAA;MA+GWwE,KAAK,EAAE9D,MAAA,CAAA+D,WAAW;MACnB,gBAAc,EAAC;;MAhHzBhE,OAAA,EAAAH,QAAA,CAkHU,MAMe,CANfH,YAAA,CAMeuE,uBAAA;QANDC,KAAK,EAAC;MAAY;QAlH1ClE,OAAA,EAAAH,QAAA,CAmHY,MAIE,CAJFH,YAAA,CAIEyE,oBAAA;UAvHdf,UAAA,EAoHuBnD,MAAA,CAAA+D,WAAW,CAACI,YAAY;UApH/C,uBAAAtE,MAAA,QAAAA,MAAA,MAAAiD,MAAA,IAoHuB9C,MAAA,CAAA+D,WAAW,CAACI,YAAY,GAAArB,MAAA;UACjC,aAAW,EAAC,IAAI;UAChB,eAAa,EAAC;;QAtH5BzB,CAAA;;MAAAA,CAAA;oCAAAG,mBAAA,e;IAAAH,CAAA;qDA0IIG,mBAAA,WAAc,EACd/B,YAAA,CAiDYyD,oBAAA;IA5LhBC,UAAA,EA4IenD,MAAA,CAAAoE,qBAAqB;IA5IpC,uBAAAvE,MAAA,QAAAA,MAAA,MAAAiD,MAAA,IA4Ie9C,MAAA,CAAAoE,qBAAqB,GAAAtB,MAAA;IAC9B1C,KAAK,EAAC,MAAM;IACZiD,KAAK,EAAC,OAAO;IACZ,cAAY,EAAErD,MAAA,CAAAsD;;IAiCJC,MAAM,EAAA3D,QAAA,CACf,MASO,CATPE,mBAAA,CASO,QATPuE,WASO,GARL5E,YAAA,CAAgEkC,oBAAA;MAApDI,OAAK,EAAAlC,MAAA,QAAAA,MAAA,MAAAiD,MAAA,IAAE9C,MAAA,CAAAoE,qBAAqB;;MAlLlDrE,OAAA,EAAAH,QAAA,CAkL4D,MAAEC,MAAA,SAAAA,MAAA,QAlL9De,gBAAA,CAkL4D,IAAE,E;MAlL9DS,CAAA;MAAAc,EAAA;QAmLU1C,YAAA,CAMYkC,oBAAA;MALVlB,IAAI,EAAC,SAAS;MACboB,OAAO,EAAE7B,MAAA,CAAAoC,WAAW;MACpBL,OAAK,EAAE/B,MAAA,CAAAsE;;MAtLpBvE,OAAA,EAAAH,QAAA,CAuLW,MAEDC,MAAA,SAAAA,MAAA,QAzLVe,gBAAA,CAuLW,QAED,E;MAzLVS,CAAA;MAAAc,EAAA;;IAAApC,OAAA,EAAAH,QAAA,CAiJM,MA6BM,CA7BNE,mBAAA,CA6BM,OA7BNyE,WA6BM,G,4BA5BJzE,mBAAA,CAAgB,WAAb,WAAS,sBACZA,mBAAA,CAWM,OAXN0E,WAWM,GAVJ1E,mBAAA,CAEI,Y,4BADFA,mBAAA,CAAqB,gBAAb,MAAI,sBArJxBc,gBAAA,CAqJiC,GAAC,GAAAC,gBAAA,CAAGb,MAAA,CAAAC,iBAAiB,EAAEI,IAAI,CAACC,IAAI,CAACC,IAAI,iB,GAE5DT,mBAAA,CAEI,Y,4BADFA,mBAAA,CAAoB,gBAAZ,KAAG,sBAxJvBc,gBAAA,CAwJgC,GAAC,GAAAC,gBAAA,CAAGb,MAAA,CAAAC,iBAAiB,EAAEI,IAAI,CAACG,WAAW,iB,GAE7DV,mBAAA,CAGI,Y,4BAFFA,mBAAA,CAAsB,gBAAd,OAAK,sBA3JzBc,gBAAA,CA2JkC,GACtB,GAAAC,gBAAA,CAAGb,MAAA,CAAAc,cAAc,CAACd,MAAA,CAAAC,iBAAiB,EAAEqB,aAAa,kB,KAItDE,mBAAA,UAAa,EAELxB,MAAA,CAAA4D,gBAAgB,I,cADxBlC,YAAA,CAYUmC,kBAAA;MA7KlBvE,GAAA;MAmKWwE,KAAK,EAAE9D,MAAA,CAAAyE,YAAY;MACpB,gBAAc,EAAC;;MApKzB1E,OAAA,EAAAH,QAAA,CAsKU,MAMe,CANfH,YAAA,CAMeuE,uBAAA;QANDC,KAAK,EAAC;MAAY;QAtK1ClE,OAAA,EAAAH,QAAA,CAuKY,MAIE,CAJFH,YAAA,CAIEyE,oBAAA;UA3Kdf,UAAA,EAwKuBnD,MAAA,CAAAyE,YAAY,CAACN,YAAY;UAxKhD,uBAAAtE,MAAA,QAAAA,MAAA,MAAAiD,MAAA,IAwKuB9C,MAAA,CAAAyE,YAAY,CAACN,YAAY,GAAArB,MAAA;UAClC,aAAW,EAAC,IAAI;UAChB,eAAa,EAAC;;QA1K5BzB,CAAA;;MAAAA,CAAA;oCAAAG,mBAAA,e;IAAAH,CAAA;qDA8LIG,mBAAA,aAAgB,EAChB/B,YAAA,CAqCYyD,oBAAA;IApOhBC,UAAA,EAgMenD,MAAA,CAAA0E,mBAAmB;IAhMlC,uBAAA7E,MAAA,QAAAA,MAAA,MAAAiD,MAAA,IAgMe9C,MAAA,CAAA0E,mBAAmB,GAAA5B,MAAA;IAC5B1C,KAAK,EAAC,MAAM;IACZiD,KAAK,EAAC,OAAO;IACZ,cAAY,EAAErD,MAAA,CAAAsD;;IAyBJC,MAAM,EAAA3D,QAAA,CACf,MAKO,CALPE,mBAAA,CAKO,QALP6E,WAKO,GAJLlF,YAAA,CAA+DkC,oBAAA;MAAnDI,OAAK,EAAAlC,MAAA,QAAAA,MAAA,MAAAiD,MAAA,IAAE9C,MAAA,CAAA0E,mBAAmB;;MA9NhD3E,OAAA,EAAAH,QAAA,CA8N0D,MAAGC,MAAA,SAAAA,MAAA,QA9N7De,gBAAA,CA8N0D,KAAG,E;MA9N7DS,CAAA;MAAAc,EAAA;QA+NU1C,YAAA,CAEYkC,oBAAA;MAFDlB,IAAI,EAAC,QAAQ;MAAEoB,OAAO,EAAE7B,MAAA,CAAAuC,UAAU;MAAGR,OAAK,EAAE/B,MAAA,CAAA4E;;MA/NjE7E,OAAA,EAAAH,QAAA,CA+N+E,MAErEC,MAAA,SAAAA,MAAA,QAjOVe,gBAAA,CA+N+E,QAErE,E;MAjOVS,CAAA;MAAAc,EAAA;;IAAApC,OAAA,EAAAH,QAAA,CAqMM,MAqBM,CArBNE,mBAAA,CAqBM,OArBN+E,WAqBM,G,4BApBJ/E,mBAAA,CAAgB,WAAb,WAAS,sBACZA,mBAAA,CAYM,OAZNgF,WAYM,GAXJhF,mBAAA,CAEI,Y,4BADFA,mBAAA,CAAqB,gBAAb,MAAI,sBAzMxBc,gBAAA,CAyMiC,GAAC,GAAAC,gBAAA,CAAGb,MAAA,CAAAC,iBAAiB,EAAEI,IAAI,CAACC,IAAI,CAACC,IAAI,iB,GAE5DT,mBAAA,CAEI,Y,4BADFA,mBAAA,CAAoB,gBAAZ,KAAG,sBA5MvBc,gBAAA,CA4MgC,GAAC,GAAAC,gBAAA,CAAGb,MAAA,CAAAC,iBAAiB,EAAEI,IAAI,CAACG,WAAW,iB,GAE7DV,mBAAA,CAII,Y,4BAHFA,mBAAA,CAAsB,gBAAd,OAAK,sBA/MzBc,gBAAA,CA+MkC,GACtB,GAAAC,gBAAA,CAAGb,MAAA,CAAAc,cAAc,CAACd,MAAA,CAAAC,iBAAiB,EAAEc,UAAU,KAAI,KACnD,GAAAF,gBAAA,CAAGb,MAAA,CAAAc,cAAc,CAACd,MAAA,CAAAC,iBAAiB,EAAEe,QAAQ,kB,KAGjDvB,YAAA,CAKEU,mBAAA;MAJAC,KAAK,EAAC,mBAAmB;MACzBK,IAAI,EAAC,SAAS;MACbC,QAAQ,EAAE,KAAK;MAChB,WAAS,EAAT;;IAxNVW,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}