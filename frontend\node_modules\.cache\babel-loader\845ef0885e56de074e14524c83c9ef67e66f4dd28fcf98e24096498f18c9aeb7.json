{"ast": null, "code": "import { ref, computed, h } from \"vue\";\nimport { useRoute } from \"vue-router\";\nimport { Monitor, User, QuestionFilled } from \"@element-plus/icons-vue\";\n\n// 自定义座位图标组件 - 使用Vue 3的函数式组件\nconst SeatIcon = {\n  name: \"SeatIcon\",\n  setup() {\n    return () => h(\"svg\", {\n      viewBox: \"0 0 24 24\",\n      width: \"1em\",\n      height: \"1em\"\n    }, [h(\"path\", {\n      fill: \"currentColor\",\n      d: \"M4 18v-3h16v3h2v-6H2v6h2zm10-9h4V6h-4v3zm-6 0h4V6H8v3zM4 5v3h2V5h12v3h2V5c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2z\"\n    })]);\n  }\n};\nexport default {\n  name: \"AppSidebar\",\n  components: {\n    Monitor,\n    User,\n    QuestionFilled,\n    SeatIcon\n  },\n  setup() {\n    const route = useRoute();\n    const isCollapse = ref(false);\n    const activeMenu = computed(() => {\n      const {\n        path\n      } = route;\n      return path;\n    });\n    const toggleCollapse = () => {\n      isCollapse.value = !isCollapse.value;\n    };\n    return {\n      activeMenu,\n      isCollapse,\n      toggleCollapse\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "h", "useRoute", "Monitor", "User", "QuestionFilled", "SeatIcon", "name", "setup", "viewBox", "width", "height", "fill", "d", "components", "route", "isCollapse", "activeMenu", "path", "toggleCollapse", "value"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Sidebar.vue"], "sourcesContent": ["<template>\n  <div class=\"app-sidebar\">\n    <el-menu\n      :default-active=\"activeMenu\"\n      class=\"sidebar-menu\"\n      :router=\"true\"\n      :collapse=\"isCollapse\"\n    >\n      <el-menu-item index=\"/dashboard\">\n        <el-icon><Monitor /></el-icon>\n        <template #title>首页</template>\n      </el-menu-item>\n\n      <el-sub-menu index=\"/seat\">\n        <template #title>\n          <el-icon><SeatIcon /></el-icon>\n          <span>座位管理</span>\n        </template>\n        <el-menu-item index=\"/seat/rooms\">自习室列表</el-menu-item>\n        <el-menu-item index=\"/seat/map\">座位地图</el-menu-item>\n        <el-menu-item index=\"/seat/reservation\">预约座位</el-menu-item>\n      </el-sub-menu>\n\n      <el-sub-menu index=\"/user\">\n        <template #title>\n          <el-icon><User /></el-icon>\n          <span>个人中心</span>\n        </template>\n        <el-menu-item index=\"/user/profile\">个人信息</el-menu-item>\n        <el-menu-item index=\"/user/reservations\">我的预约</el-menu-item>\n        <el-menu-item index=\"/user/records\">操作记录</el-menu-item>\n        <el-menu-item index=\"/user/credit\">信誉分记录</el-menu-item>\n      </el-sub-menu>\n\n      <el-menu-item index=\"/help\">\n        <el-icon><QuestionFilled /></el-icon>\n        <template #title>帮助中心</template>\n      </el-menu-item>\n    </el-menu>\n\n    <div class=\"sidebar-footer\">\n      <el-button\n        type=\"text\"\n        :icon=\"isCollapse ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'\"\n        @click=\"toggleCollapse\"\n      >\n        {{ isCollapse ? \"展开\" : \"收起\" }}\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, h } from \"vue\";\nimport { useRoute } from \"vue-router\";\nimport { Monitor, User, QuestionFilled } from \"@element-plus/icons-vue\";\n\n// 自定义座位图标组件 - 使用Vue 3的函数式组件\nconst SeatIcon = {\n  name: \"SeatIcon\",\n  setup() {\n    return () =>\n      h(\n        \"svg\",\n        {\n          viewBox: \"0 0 24 24\",\n          width: \"1em\",\n          height: \"1em\",\n        },\n        [\n          h(\"path\", {\n            fill: \"currentColor\",\n            d: \"M4 18v-3h16v3h2v-6H2v6h2zm10-9h4V6h-4v3zm-6 0h4V6H8v3zM4 5v3h2V5h12v3h2V5c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2z\",\n          }),\n        ]\n      );\n  },\n};\n\nexport default {\n  name: \"AppSidebar\",\n  components: {\n    Monitor,\n    User,\n    QuestionFilled,\n    SeatIcon,\n  },\n  setup() {\n    const route = useRoute();\n    const isCollapse = ref(false);\n\n    const activeMenu = computed(() => {\n      const { path } = route;\n      return path;\n    });\n\n    const toggleCollapse = () => {\n      isCollapse.value = !isCollapse.value;\n    };\n\n    return {\n      activeMenu,\n      isCollapse,\n      toggleCollapse,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-sidebar {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  border-right: 1px solid #e6e6e6;\n  background-color: #fff;\n}\n\n.sidebar-menu {\n  flex: 1;\n  border-right: none;\n}\n\n.sidebar-footer {\n  padding: 10px;\n  border-top: 1px solid #e6e6e6;\n  text-align: center;\n}\n</style>\n"], "mappings": "AAqDA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,CAAA,QAAS,KAAK;AACtC,SAASC,QAAO,QAAS,YAAY;AACrC,SAASC,OAAO,EAAEC,IAAI,EAAEC,cAAa,QAAS,yBAAyB;;AAEvE;AACA,MAAMC,QAAO,GAAI;EACfC,IAAI,EAAE,UAAU;EAChBC,KAAKA,CAAA,EAAG;IACN,OAAO,MACLP,CAAC,CACC,KAAK,EACL;MACEQ,OAAO,EAAE,WAAW;MACpBC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE;IACV,CAAC,EACD,CACEV,CAAC,CAAC,MAAM,EAAE;MACRW,IAAI,EAAE,cAAc;MACpBC,CAAC,EAAE;IACL,CAAC,CAAC,CAEN,CAAC;EACL;AACF,CAAC;AAED,eAAe;EACbN,IAAI,EAAE,YAAY;EAClBO,UAAU,EAAE;IACVX,OAAO;IACPC,IAAI;IACJC,cAAc;IACdC;EACF,CAAC;EACDE,KAAKA,CAAA,EAAG;IACN,MAAMO,KAAI,GAAIb,QAAQ,CAAC,CAAC;IACxB,MAAMc,UAAS,GAAIjB,GAAG,CAAC,KAAK,CAAC;IAE7B,MAAMkB,UAAS,GAAIjB,QAAQ,CAAC,MAAM;MAChC,MAAM;QAAEkB;MAAK,IAAIH,KAAK;MACtB,OAAOG,IAAI;IACb,CAAC,CAAC;IAEF,MAAMC,cAAa,GAAIA,CAAA,KAAM;MAC3BH,UAAU,CAACI,KAAI,GAAI,CAACJ,UAAU,CAACI,KAAK;IACtC,CAAC;IAED,OAAO;MACLH,UAAU;MACVD,UAAU;MACVG;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}