{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, useSlots, computed, openBlock, createElementBlock, normalizeClass, unref, renderSlot } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElContainer\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    direction: {\n      type: String\n    }\n  },\n  setup(__props) {\n    const props = __props;\n    const slots = useSlots();\n    const ns = useNamespace(\"container\");\n    const isVertical = computed(() => {\n      if (props.direction === \"vertical\") {\n        return true;\n      } else if (props.direction === \"horizontal\") {\n        return false;\n      }\n      if (slots && slots.default) {\n        const vNodes = slots.default();\n        return vNodes.some(vNode => {\n          const tag = vNode.type.name;\n          return tag === \"ElHeader\" || tag === \"ElFooter\";\n        });\n      } else {\n        return false;\n      }\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"section\", {\n        class: normalizeClass([unref(ns).b(), unref(ns).is(\"vertical\", unref(isVertical))])\n      }, [renderSlot(_ctx.$slots, \"default\")], 2);\n    };\n  }\n});\nvar Container = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"container.vue\"]]);\nexport { Container as default };", "map": {"version": 3, "names": ["name", "props", "direction", "slots", "default", "vNodes", "some", "vNode", "tag", "type", "_ctx", "_cache", "openBlock", "createElementBlock", "class", "normalizeClass", "unref", "ns", "b", "is", "isVertical"], "sources": ["../../../../../../packages/components/container/src/container.vue"], "sourcesContent": ["<template>\n  <section :class=\"[ns.b(), ns.is('vertical', isVertical)]\">\n    <slot />\n  </section>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, useSlots } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { Component, VNode } from 'vue'\n\ndefineOptions({\n  name: 'ElContainer',\n})\nconst props = defineProps({\n  /**\n   * @description layout direction for child elements\n   */\n  direction: {\n    type: String,\n  },\n})\nconst slots = useSlots()\n\nconst ns = useNamespace('container')\n\nconst isVertical = computed(() => {\n  if (props.direction === 'vertical') {\n    return true\n  } else if (props.direction === 'horizontal') {\n    return false\n  }\n  if (slots && slots.default) {\n    const vNodes: VNode[] = slots.default()\n    return vNodes.some((vNode) => {\n      const tag = (vNode.type as Component).name\n      return tag === 'ElHeader' || tag === 'ElFooter'\n    })\n  } else {\n    return false\n  }\n})\n</script>\n"], "mappings": ";;;;;mCAYc;EACZA,IAAM;AACR;;;;;;;;;;;;;MASA,IAAMC,KAAA,CAAAC,SAAiB;QAEjB,OAAK;MAEX,CAAM,UAAAD,KAAA,CAAaC,SAAS,KAAM;QAC5B;MACF;MACF,IAAAC,KAAA,IAAiBA,KAAA,CAAAC,OAAA;QACR,MAAAC,MAAA,GAAAF,KAAA,CAAAC,OAAA;QACT,OAAAC,MAAA,CAAAC,IAAA,CAAAC,KAAA;UACI,MAAAC,GAAS,GAAAD,KAAe,CAAAE,IAAA,CAAAT,IAAA;UACpB,OAAAQ,GAAA,eAAgC,IAAAA,GAAA;QACtC,CAAO;MACL,CAAM;QACC;MAA8B;IACtC,EACH;IACS,QAAAE,IAAA,EAAAC,MAAA;MACT,OAAAC,SAAA,IAAAC,kBAAA;QACDC,KAAA,EAAAC,cAAA,EAAAC,KAAA,CAAAC,EAAA,EAAAC,CAAA,IAAAF,KAAA,CAAAC,EAAA,EAAAE,EAAA,aAAAH,KAAA,CAAAI,UAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}