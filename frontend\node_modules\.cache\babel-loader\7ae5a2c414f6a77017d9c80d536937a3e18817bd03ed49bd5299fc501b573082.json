{"ast": null, "code": "import arrayEach from './_arrayEach.js';\nimport baseAssignValue from './_baseAssignValue.js';\nimport bind from './bind.js';\nimport flatRest from './_flatRest.js';\nimport toKey from './_toKey.js';\n\n/**\n * Binds methods of an object to the object itself, overwriting the existing\n * method.\n *\n * **Note:** This method doesn't set the \"length\" property of bound functions.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {Object} object The object to bind and assign the bound methods to.\n * @param {...(string|string[])} methodNames The object method names to bind.\n * @returns {Object} Returns `object`.\n * @example\n *\n * var view = {\n *   'label': 'docs',\n *   'click': function() {\n *     console.log('clicked ' + this.label);\n *   }\n * };\n *\n * _.bindAll(view, ['click']);\n * jQuery(element).on('click', view.click);\n * // => Logs 'clicked docs' when clicked.\n */\nvar bindAll = flatRest(function (object, methodNames) {\n  arrayEach(methodNames, function (key) {\n    key = toKey(key);\n    baseAssignValue(object, key, bind(object[key], object));\n  });\n  return object;\n});\nexport default bindAll;", "map": {"version": 3, "names": ["arrayEach", "baseAssignValue", "bind", "flatRest", "to<PERSON><PERSON>", "bindAll", "object", "methodNames", "key"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/bindAll.js"], "sourcesContent": ["import arrayEach from './_arrayEach.js';\nimport baseAssignValue from './_baseAssignValue.js';\nimport bind from './bind.js';\nimport flatRest from './_flatRest.js';\nimport toKey from './_toKey.js';\n\n/**\n * Binds methods of an object to the object itself, overwriting the existing\n * method.\n *\n * **Note:** This method doesn't set the \"length\" property of bound functions.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {Object} object The object to bind and assign the bound methods to.\n * @param {...(string|string[])} methodNames The object method names to bind.\n * @returns {Object} Returns `object`.\n * @example\n *\n * var view = {\n *   'label': 'docs',\n *   'click': function() {\n *     console.log('clicked ' + this.label);\n *   }\n * };\n *\n * _.bindAll(view, ['click']);\n * jQuery(element).on('click', view.click);\n * // => Logs 'clicked docs' when clicked.\n */\nvar bindAll = flatRest(function(object, methodNames) {\n  arrayEach(methodNames, function(key) {\n    key = toKey(key);\n    baseAssignValue(object, key, bind(object[key], object));\n  });\n  return object;\n});\n\nexport default bindAll;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,KAAK,MAAM,aAAa;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAGF,QAAQ,CAAC,UAASG,MAAM,EAAEC,WAAW,EAAE;EACnDP,SAAS,CAACO,WAAW,EAAE,UAASC,GAAG,EAAE;IACnCA,GAAG,GAAGJ,KAAK,CAACI,GAAG,CAAC;IAChBP,eAAe,CAACK,MAAM,EAAEE,GAAG,EAAEN,IAAI,CAACI,MAAM,CAACE,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC;EACzD,CAAC,CAAC;EACF,OAAOA,MAAM;AACf,CAAC,CAAC;AAEF,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}