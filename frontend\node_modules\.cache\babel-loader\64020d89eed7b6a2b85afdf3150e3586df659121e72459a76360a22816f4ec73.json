{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst skeletonProps = buildProps({\n  animated: {\n    type: Boolean,\n    default: false\n  },\n  count: {\n    type: Number,\n    default: 1\n  },\n  rows: {\n    type: Number,\n    default: 3\n  },\n  loading: {\n    type: Boolean,\n    default: true\n  },\n  throttle: {\n    type: definePropType([Number, Object])\n  }\n});\nexport { skeletonProps };", "map": {"version": 3, "names": ["skeletonProps", "buildProps", "animated", "type", "Boolean", "default", "count", "Number", "rows", "loading", "throttle", "definePropType", "Object"], "sources": ["../../../../../../packages/components/skeleton/src/skeleton.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type Skeleton from './skeleton.vue'\nimport type { ExtractPropTypes } from 'vue'\nimport type { ThrottleType } from '@element-plus/hooks'\n\nexport const skeletonProps = buildProps({\n  /**\n   * @description whether showing the animation\n   */\n  animated: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description how many fake items to render to the DOM\n   */\n  count: {\n    type: Number,\n    default: 1,\n  },\n  /**\n   * @description whether showing the real DOM\n   */\n  rows: {\n    type: Number,\n    default: 3,\n  },\n  /**\n   * @description numbers of the row, only useful when no template slot were given\n   */\n  loading: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description rendering delay in milliseconds\n   */\n  throttle: {\n    type: definePropType<ThrottleType>([Number, Object]),\n  },\n} as const)\nexport type SkeletonProps = ExtractPropTypes<typeof skeletonProps>\n\nexport type SkeletonInstance = InstanceType<typeof Skeleton> & unknown\n"], "mappings": ";AACY,MAACA,aAAa,GAAGC,UAAU,CAAC;EACtCC,QAAQ,EAAE;IACRC,IAAI,EAAEC,OAAO;IACbC,OAAO,EAAE;EACb,CAAG;EACDC,KAAK,EAAE;IACLH,IAAI,EAAEI,MAAM;IACZF,OAAO,EAAE;EACb,CAAG;EACDG,IAAI,EAAE;IACJL,IAAI,EAAEI,MAAM;IACZF,OAAO,EAAE;EACb,CAAG;EACDI,OAAO,EAAE;IACPN,IAAI,EAAEC,OAAO;IACbC,OAAO,EAAE;EACb,CAAG;EACDK,QAAQ,EAAE;IACRP,IAAI,EAAEQ,cAAc,CAAC,CAACJ,MAAM,EAAEK,MAAM,CAAC;EACzC;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}