{"ast": null, "code": "import Descriptions from './src/description.mjs';\nimport DescriptionItem from './src/description-item.mjs';\nexport { descriptionItemProps } from './src/description-item.mjs';\nexport { descriptionProps } from './src/description2.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElDescriptions = withInstall(Descriptions, {\n  DescriptionsItem: DescriptionItem\n});\nconst ElDescriptionsItem = withNoopInstall(DescriptionItem);\nexport { ElDescriptions, ElDescriptionsItem, ElDescriptions as default };", "map": {"version": 3, "names": ["ElDescriptions", "withInstall", "Descriptions", "DescriptionsItem", "DescriptionItem", "ElDescriptionsItem", "withNoopInstall"], "sources": ["../../../../../packages/components/descriptions/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\n\nimport Descriptions from './src/description.vue'\nimport DescriptionsItem from './src/description-item'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElDescriptions: SFCWithInstall<typeof Descriptions> & {\n  DescriptionsItem: typeof DescriptionsItem\n} = withInstall(Descriptions, {\n  DescriptionsItem,\n})\n\nexport const ElDescriptionsItem: SFCWithInstall<typeof DescriptionsItem> =\n  withNoopInstall(DescriptionsItem)\n\nexport default ElDescriptions\n\nexport * from './src/description'\nexport * from './src/description-item'\n"], "mappings": ";;;;;AAGY,MAACA,cAAc,GAAGC,WAAW,CAACC,YAAY,EAAE;EACxDC,gBAAA,EAAEC;AACF,CAAC;AACW,MAACC,kBAAkB,GAAGC,eAAe,CAACF,eAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}