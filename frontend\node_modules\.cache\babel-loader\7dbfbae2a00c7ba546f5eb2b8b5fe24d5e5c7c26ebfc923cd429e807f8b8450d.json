{"ast": null, "code": "import { defineComponent, inject, computed, openBlock, createElementBlock, unref, normalizeClass, normalizeStyle, createElementVNode, renderSlot, toDisplayString, createVNode, withCtx, createBlock, resolveDynamicComponent, createCommentVNode } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { dialogInjectionKey } from './constants.mjs';\nimport { dialogContentProps, dialogContentEmits } from './dialog-content.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { FOCUS_TRAP_INJECTION_KEY } from '../../focus-trap/src/tokens.mjs';\nimport { useDraggable } from '../../../hooks/use-draggable/index.mjs';\nimport { CloseComponents } from '../../../utils/vue/icon.mjs';\nimport { composeRefs } from '../../../utils/vue/refs.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElDialogContent\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: dialogContentProps,\n  emits: dialogContentEmits,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const {\n      Close\n    } = CloseComponents;\n    const {\n      dialogRef,\n      headerRef,\n      bodyId,\n      ns,\n      style\n    } = inject(dialogInjectionKey);\n    const {\n      focusTrapRef\n    } = inject(FOCUS_TRAP_INJECTION_KEY);\n    const dialogKls = computed(() => [ns.b(), ns.is(\"fullscreen\", props.fullscreen), ns.is(\"draggable\", props.draggable), ns.is(\"align-center\", props.alignCenter), {\n      [ns.m(\"center\")]: props.center\n    }]);\n    const composedDialogRef = composeRefs(focusTrapRef, dialogRef);\n    const draggable = computed(() => props.draggable);\n    const overflow = computed(() => props.overflow);\n    const {\n      resetPosition,\n      updatePosition\n    } = useDraggable(dialogRef, headerRef, draggable, overflow);\n    expose({\n      resetPosition,\n      updatePosition\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref: unref(composedDialogRef),\n        class: normalizeClass(unref(dialogKls)),\n        style: normalizeStyle(unref(style)),\n        tabindex: \"-1\"\n      }, [createElementVNode(\"header\", {\n        ref_key: \"headerRef\",\n        ref: headerRef,\n        class: normalizeClass([unref(ns).e(\"header\"), _ctx.headerClass, {\n          \"show-close\": _ctx.showClose\n        }])\n      }, [renderSlot(_ctx.$slots, \"header\", {}, () => [createElementVNode(\"span\", {\n        role: \"heading\",\n        \"aria-level\": _ctx.ariaLevel,\n        class: normalizeClass(unref(ns).e(\"title\"))\n      }, toDisplayString(_ctx.title), 11, [\"aria-level\"])]), _ctx.showClose ? (openBlock(), createElementBlock(\"button\", {\n        key: 0,\n        \"aria-label\": unref(t)(\"el.dialog.close\"),\n        class: normalizeClass(unref(ns).e(\"headerbtn\")),\n        type: \"button\",\n        onClick: $event => _ctx.$emit(\"close\")\n      }, [createVNode(unref(ElIcon), {\n        class: normalizeClass(unref(ns).e(\"close\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.closeIcon || unref(Close))))]),\n        _: 1\n      }, 8, [\"class\"])], 10, [\"aria-label\", \"onClick\"])) : createCommentVNode(\"v-if\", true)], 2), createElementVNode(\"div\", {\n        id: unref(bodyId),\n        class: normalizeClass([unref(ns).e(\"body\"), _ctx.bodyClass])\n      }, [renderSlot(_ctx.$slots, \"default\")], 10, [\"id\"]), _ctx.$slots.footer ? (openBlock(), createElementBlock(\"footer\", {\n        key: 0,\n        class: normalizeClass([unref(ns).e(\"footer\"), _ctx.footerClass])\n      }, [renderSlot(_ctx.$slots, \"footer\")], 2)) : createCommentVNode(\"v-if\", true)], 6);\n    };\n  }\n});\nvar ElDialogContent = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"dialog-content.vue\"]]);\nexport { ElDialogContent as default };", "map": {"version": 3, "names": ["name", "t", "useLocale", "Close", "CloseComponents", "dialogRef", "headerRef", "bodyId", "ns", "style", "inject", "dialogInjectionKey", "focusTrapRef", "FOCUS_TRAP_INJECTION_KEY", "dialogKls", "computed", "b", "is", "props", "fullscreen", "draggable", "alignCenter", "m", "center", "composedDialogRef", "composeRefs", "overflow", "resetPosition", "updatePosition", "useDraggable", "expose", "_ctx", "_cache", "openBlock", "createElementBlock", "ref", "unref", "class", "normalizeClass", "normalizeStyle"], "sources": ["../../../../../../packages/components/dialog/src/dialog-content.vue"], "sourcesContent": ["<template>\n  <div :ref=\"composedDialogRef\" :class=\"dialogKls\" :style=\"style\" tabindex=\"-1\">\n    <header\n      ref=\"headerRef\"\n      :class=\"[ns.e('header'), headerClass, { 'show-close': showClose }]\"\n    >\n      <slot name=\"header\">\n        <span role=\"heading\" :aria-level=\"ariaLevel\" :class=\"ns.e('title')\">\n          {{ title }}\n        </span>\n      </slot>\n      <button\n        v-if=\"showClose\"\n        :aria-label=\"t('el.dialog.close')\"\n        :class=\"ns.e('headerbtn')\"\n        type=\"button\"\n        @click=\"$emit('close')\"\n      >\n        <el-icon :class=\"ns.e('close')\">\n          <component :is=\"closeIcon || Close\" />\n        </el-icon>\n      </button>\n    </header>\n    <div :id=\"bodyId\" :class=\"[ns.e('body'), bodyClass]\">\n      <slot />\n    </div>\n    <footer v-if=\"$slots.footer\" :class=\"[ns.e('footer'), footerClass]\">\n      <slot name=\"footer\" />\n    </footer>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { FOCUS_TRAP_INJECTION_KEY } from '@element-plus/components/focus-trap'\nimport { useDraggable, useLocale } from '@element-plus/hooks'\nimport { CloseComponents, composeRefs } from '@element-plus/utils'\nimport { dialogInjectionKey } from './constants'\nimport { dialogContentEmits, dialogContentProps } from './dialog-content'\n\nconst { t } = useLocale()\nconst { Close } = CloseComponents\n\ndefineOptions({ name: 'ElDialogContent' })\nconst props = defineProps(dialogContentProps)\ndefineEmits(dialogContentEmits)\n\nconst { dialogRef, headerRef, bodyId, ns, style } = inject(dialogInjectionKey)!\nconst { focusTrapRef } = inject(FOCUS_TRAP_INJECTION_KEY)!\n\nconst dialogKls = computed(() => [\n  ns.b(),\n  ns.is('fullscreen', props.fullscreen),\n  ns.is('draggable', props.draggable),\n  ns.is('align-center', props.alignCenter),\n  { [ns.m('center')]: props.center },\n])\n\nconst composedDialogRef = composeRefs(focusTrapRef, dialogRef)\n\nconst draggable = computed(() => props.draggable)\nconst overflow = computed(() => props.overflow)\nconst { resetPosition, updatePosition } = useDraggable(\n  dialogRef,\n  headerRef,\n  draggable,\n  overflow\n)\n\ndefineExpose({\n  resetPosition,\n  updatePosition,\n})\n</script>\n"], "mappings": ";;;;;;;;;;mCA4Cc;EAAEA,IAAM;AAAA,CAAkB;;;;;;;;;IAHlC;MAAEC;IAAE,IAAIC,SAAU;IAClB;MAAEC;IAAA,CAAU,GAAAC,eAAA;IAMZ;MAAEC,SAAA;MAAWC,SAAW;MAAAC,MAAA;MAAQC,EAAA;MAAIC;IAAM,IAAIC,MAAA,CAAOC,kBAAkB;IAC7E,MAAM;MAAEC;IAAA,CAAiB,GAAAF,MAAA,CAAOG,wBAAwB;IAElD,MAAAC,SAAA,GAAYC,QAAA,CAAS,MAAM,CAC/BP,EAAA,CAAGQ,CAAE,IACLR,EAAG,CAAAS,EAAA,CAAG,YAAc,EAAAC,KAAA,CAAMC,UAAU,GACpCX,EAAG,CAAAS,EAAA,CAAG,WAAa,EAAAC,KAAA,CAAME,SAAS,GAClCZ,EAAG,CAAAS,EAAA,CAAG,cAAgB,EAAAC,KAAA,CAAMG,WAAW,GACvC;MAAE,CAACb,EAAG,CAAAc,CAAA,CAAE,QAAQ,CAAC,GAAGJ,KAAA,CAAMK;IAAO,EAClC;IAEK,MAAAC,iBAAA,GAAoBC,WAAY,CAAAb,YAAA,EAAcP,SAAS;IAE7D,MAAMe,SAAY,GAAAL,QAAA,CAAS,MAAMG,KAAA,CAAME,SAAS;IAChD,MAAMM,QAAW,GAAAX,QAAA,CAAS,MAAMG,KAAA,CAAMQ,QAAQ;IACxC;MAAEC,aAAe;MAAAC;IAAA,CAAmB,GAAAC,YAAA,CAAAxB,SAAA,EAAAC,SAAA,EAAAc,SAAA,EAAAM,QAAA;IACxCI,MAAA;MACAH,aAAA;MACAC;IAAA,CACA;IACF,QAAAG,IAAA,EAAAC,MAAA;MAEa,OAAAC,SAAA,IAAAC,kBAAA;QACXC,GAAA,EAAAC,KAAA,CAAAZ,iBAAA;QACAa,KAAA,EAAAC,cAAA,CAAAF,KAAA,CAAAtB,SAAA;QACDL,KAAA,EAAA8B,cAAA,CAAAH,KAAA,CAAA3B,KAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}