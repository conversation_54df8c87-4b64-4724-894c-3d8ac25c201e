{"ast": null, "code": "import installer from './defaults.mjs';\nexport { default } from './defaults.mjs';\nexport { makeInstaller } from './make-installer.mjs';\nexport { default as dayjs } from 'dayjs';\nexport { affixEmits, affixProps } from './components/affix/src/affix.mjs';\nexport { ElAffix } from './components/affix/index.mjs';\nexport { alertEffects, alertEmits, alertProps } from './components/alert/src/alert.mjs';\nexport { ElAlert } from './components/alert/index.mjs';\nexport { autocompleteEmits, autocompleteProps } from './components/autocomplete/src/autocomplete.mjs';\nexport { ElAutocomplete } from './components/autocomplete/index.mjs';\nexport { avatarEmits, avatarProps } from './components/avatar/src/avatar.mjs';\nexport { ElAvatar } from './components/avatar/index.mjs';\nexport { backtopEmits, backtopProps } from './components/backtop/src/backtop.mjs';\nexport { ElBacktop } from './components/backtop/index.mjs';\nexport { badgeProps } from './components/badge/src/badge.mjs';\nexport { ElBadge } from './components/badge/index.mjs';\nexport { breadcrumbProps } from './components/breadcrumb/src/breadcrumb.mjs';\nexport { breadcrumbItemProps } from './components/breadcrumb/src/breadcrumb-item.mjs';\nexport { breadcrumbKey } from './components/breadcrumb/src/constants.mjs';\nexport { ElBreadcrumb, ElBreadcrumbItem } from './components/breadcrumb/index.mjs';\nexport { buttonEmits, buttonNativeTypes, buttonProps, buttonTypes } from './components/button/src/button.mjs';\nexport { buttonGroupContextKey } from './components/button/src/constants.mjs';\nexport { ElButton, ElButtonGroup } from './components/button/index.mjs';\nexport { calendarEmits, calendarProps } from './components/calendar/src/calendar.mjs';\nexport { ElCalendar } from './components/calendar/index.mjs';\nexport { cardProps } from './components/card/src/card.mjs';\nexport { ElCard } from './components/card/index.mjs';\nexport { carouselEmits, carouselProps } from './components/carousel/src/carousel.mjs';\nexport { carouselItemProps } from './components/carousel/src/carousel-item.mjs';\nexport { CAROUSEL_ITEM_NAME, carouselContextKey } from './components/carousel/src/constants.mjs';\nexport { ElCarousel, ElCarouselItem } from './components/carousel/index.mjs';\nexport { cascaderEmits, cascaderProps } from './components/cascader/src/cascader2.mjs';\nexport { ElCascader } from './components/cascader/index.mjs';\nexport { CASCADER_PANEL_INJECTION_KEY } from './components/cascader-panel/src/types.mjs';\nexport { CommonProps, DefaultProps, useCascaderConfig } from './components/cascader-panel/src/config.mjs';\nexport { ElCascaderPanel } from './components/cascader-panel/index.mjs';\nexport { checkTagEmits, checkTagProps } from './components/check-tag/src/check-tag.mjs';\nexport { ElCheckTag } from './components/check-tag/index.mjs';\nexport { checkboxGroupEmits, checkboxGroupProps } from './components/checkbox/src/checkbox-group.mjs';\nexport { checkboxEmits, checkboxProps } from './components/checkbox/src/checkbox.mjs';\nexport { checkboxGroupContextKey } from './components/checkbox/src/constants.mjs';\nexport { ElCheckbox, ElCheckboxButton, ElCheckboxGroup } from './components/checkbox/index.mjs';\nexport { colProps } from './components/col/src/col2.mjs';\nexport { ElCol } from './components/col/index.mjs';\nexport { collapseEmits, collapseProps, emitChangeFn } from './components/collapse/src/collapse.mjs';\nexport { collapseItemProps } from './components/collapse/src/collapse-item.mjs';\nexport { collapseContextKey } from './components/collapse/src/constants.mjs';\nexport { ElCollapse, ElCollapseItem } from './components/collapse/index.mjs';\nexport { ElCollapseTransition } from './components/collapse-transition/index.mjs';\nexport { colorPickerContextKey, colorPickerEmits, colorPickerProps } from './components/color-picker/src/color-picker.mjs';\nexport { ElColorPicker } from './components/color-picker/index.mjs';\nexport { messageConfig } from './components/config-provider/src/config-provider.mjs';\nexport { configProviderProps } from './components/config-provider/src/config-provider-props.mjs';\nexport { configProviderContextKey } from './components/config-provider/src/constants.mjs';\nexport { provideGlobalConfig, useGlobalComponentSettings, useGlobalConfig } from './components/config-provider/src/hooks/use-global-config.mjs';\nexport { ElConfigProvider } from './components/config-provider/index.mjs';\nexport { ElAside, ElContainer, ElFooter, ElHeader, ElMain } from './components/container/index.mjs';\nexport { countdownEmits, countdownProps } from './components/countdown/src/countdown.mjs';\nexport { ElCountdown } from './components/countdown/index.mjs';\nexport { ROOT_PICKER_INJECTION_KEY } from './components/date-picker/src/constants.mjs';\nexport { datePickerProps } from './components/date-picker/src/props/date-picker.mjs';\nexport { ElDatePicker } from './components/date-picker/index.mjs';\nexport { descriptionProps } from './components/descriptions/src/description2.mjs';\nexport { descriptionItemProps } from './components/descriptions/src/description-item.mjs';\nexport { ElDescriptions, ElDescriptionsItem } from './components/descriptions/index.mjs';\nexport { useDialog } from './components/dialog/src/use-dialog.mjs';\nexport { dialogEmits, dialogProps } from './components/dialog/src/dialog.mjs';\nexport { dialogInjectionKey } from './components/dialog/src/constants.mjs';\nexport { ElDialog } from './components/dialog/index.mjs';\nexport { dividerProps } from './components/divider/src/divider.mjs';\nexport { ElDivider } from './components/divider/index.mjs';\nexport { drawerEmits, drawerProps } from './components/drawer/src/drawer2.mjs';\nexport { ElDrawer } from './components/drawer/index.mjs';\nexport { DROPDOWN_COLLECTION_INJECTION_KEY, DROPDOWN_COLLECTION_ITEM_INJECTION_KEY, ElCollection, ElCollectionItem, FIRST_KEYS, FIRST_LAST_KEYS, LAST_KEYS, dropdownItemProps, dropdownMenuProps, dropdownProps } from './components/dropdown/src/dropdown.mjs';\nexport { DROPDOWN_INJECTION_KEY } from './components/dropdown/src/tokens.mjs';\nexport { ElDropdown, ElDropdownItem, ElDropdownMenu } from './components/dropdown/index.mjs';\nexport { emptyProps } from './components/empty/src/empty.mjs';\nexport { ElEmpty } from './components/empty/index.mjs';\nexport { formEmits, formMetaProps, formProps } from './components/form/src/form.mjs';\nexport { formItemProps, formItemValidateStates } from './components/form/src/form-item2.mjs';\nexport { formContextKey, formItemContextKey } from './components/form/src/constants.mjs';\nexport { useDisabled, useFormDisabled, useFormSize, useSize } from './components/form/src/hooks/use-form-common-props.mjs';\nexport { useFormItem, useFormItemInputId } from './components/form/src/hooks/use-form-item.mjs';\nexport { ElForm, ElFormItem } from './components/form/index.mjs';\nexport { iconProps } from './components/icon/src/icon2.mjs';\nexport { ElIcon } from './components/icon/index.mjs';\nexport { imageEmits, imageProps } from './components/image/src/image2.mjs';\nexport { ElImage } from './components/image/index.mjs';\nexport { imageViewerEmits, imageViewerProps } from './components/image-viewer/src/image-viewer2.mjs';\nexport { ElImageViewer } from './components/image-viewer/index.mjs';\nexport { inputEmits, inputProps } from './components/input/src/input.mjs';\nexport { ElInput } from './components/input/index.mjs';\nexport { inputNumberEmits, inputNumberProps } from './components/input-number/src/input-number.mjs';\nexport { ElInputNumber } from './components/input-number/index.mjs';\nexport { inputTagEmits, inputTagProps } from './components/input-tag/src/input-tag.mjs';\nexport { ElInputTag } from './components/input-tag/index.mjs';\nexport { linkEmits, linkProps } from './components/link/src/link.mjs';\nexport { ElLink } from './components/link/index.mjs';\nexport { menuEmits, menuProps } from './components/menu/src/menu.mjs';\nexport { menuItemEmits, menuItemProps } from './components/menu/src/menu-item2.mjs';\nexport { menuItemGroupProps } from './components/menu/src/menu-item-group.mjs';\nexport { subMenuProps } from './components/menu/src/sub-menu.mjs';\nexport { ElMenu, ElMenuItem, ElMenuItemGroup, ElSubMenu } from './components/menu/index.mjs';\nexport { overlayEmits, overlayProps } from './components/overlay/src/overlay.mjs';\nexport { ElOverlay } from './components/overlay/index.mjs';\nexport { pageHeaderEmits, pageHeaderProps } from './components/page-header/src/page-header.mjs';\nexport { ElPageHeader } from './components/page-header/index.mjs';\nexport { paginationEmits, paginationProps } from './components/pagination/src/pagination.mjs';\nexport { elPaginationKey } from './components/pagination/src/constants.mjs';\nexport { ElPagination } from './components/pagination/index.mjs';\nexport { popconfirmEmits, popconfirmProps } from './components/popconfirm/src/popconfirm.mjs';\nexport { ElPopconfirm } from './components/popconfirm/index.mjs';\nexport { Effect, popperProps, roleTypes, usePopperProps } from './components/popper/src/popper.mjs';\nexport { popperTriggerProps, usePopperTriggerProps } from './components/popper/src/trigger.mjs';\nexport { popperContentEmits, popperContentProps, popperCoreConfigProps, usePopperContentEmits, usePopperContentProps, usePopperCoreConfigProps } from './components/popper/src/content.mjs';\nexport { popperArrowProps, usePopperArrowProps } from './components/popper/src/arrow.mjs';\nexport { POPPER_CONTENT_INJECTION_KEY, POPPER_INJECTION_KEY } from './components/popper/src/constants.mjs';\nexport { default as ElPopperArrow } from './components/popper/src/arrow2.mjs';\nexport { default as ElPopperTrigger } from './components/popper/src/trigger2.mjs';\nexport { default as ElPopperContent } from './components/popper/src/content2.mjs';\nexport { ElPopper } from './components/popper/index.mjs';\nexport { progressProps } from './components/progress/src/progress.mjs';\nexport { ElProgress } from './components/progress/index.mjs';\nexport { radioEmits, radioProps, radioPropsBase } from './components/radio/src/radio.mjs';\nexport { radioGroupEmits, radioGroupProps } from './components/radio/src/radio-group.mjs';\nexport { radioButtonProps } from './components/radio/src/radio-button.mjs';\nexport { radioGroupKey } from './components/radio/src/constants.mjs';\nexport { ElRadio, ElRadioButton, ElRadioGroup } from './components/radio/index.mjs';\nexport { rateEmits, rateProps } from './components/rate/src/rate.mjs';\nexport { ElRate } from './components/rate/index.mjs';\nexport { IconComponentMap, IconMap, resultProps } from './components/result/src/result.mjs';\nexport { ElResult } from './components/result/index.mjs';\nexport { RowAlign, RowJustify, rowProps } from './components/row/src/row.mjs';\nexport { rowContextKey } from './components/row/src/constants.mjs';\nexport { ElRow } from './components/row/index.mjs';\nexport { BAR_MAP, GAP, renderThumbStyle } from './components/scrollbar/src/util.mjs';\nexport { scrollbarEmits, scrollbarProps } from './components/scrollbar/src/scrollbar.mjs';\nexport { thumbProps } from './components/scrollbar/src/thumb.mjs';\nexport { scrollbarContextKey } from './components/scrollbar/src/constants.mjs';\nexport { ElScrollbar } from './components/scrollbar/index.mjs';\nexport { selectGroupKey, selectKey } from './components/select/src/token.mjs';\nexport { SelectProps, selectEmits } from './components/select/src/select.mjs';\nexport { ElOption, ElOptionGroup, ElSelect } from './components/select/index.mjs';\nexport { selectV2InjectionKey } from './components/select-v2/src/token.mjs';\nexport { ElSelectV2 } from './components/select-v2/index.mjs';\nexport { skeletonProps } from './components/skeleton/src/skeleton.mjs';\nexport { skeletonItemProps } from './components/skeleton/src/skeleton-item.mjs';\nexport { ElSkeleton, ElSkeletonItem } from './components/skeleton/index.mjs';\nexport { sliderEmits, sliderProps } from './components/slider/src/slider.mjs';\nexport { sliderContextKey } from './components/slider/src/constants.mjs';\nexport { ElSlider } from './components/slider/index.mjs';\nexport { spaceProps } from './components/space/src/space.mjs';\nexport { spaceItemProps } from './components/space/src/item.mjs';\nexport { useSpace } from './components/space/src/use-space.mjs';\nexport { ElSpace } from './components/space/index.mjs';\nexport { statisticProps } from './components/statistic/src/statistic.mjs';\nexport { ElStatistic } from './components/statistic/index.mjs';\nexport { stepProps } from './components/steps/src/item.mjs';\nexport { stepsEmits, stepsProps } from './components/steps/src/steps.mjs';\nexport { ElStep, ElSteps } from './components/steps/index.mjs';\nexport { switchEmits, switchProps } from './components/switch/src/switch.mjs';\nexport { ElSwitch } from './components/switch/index.mjs';\nexport { ElTable, ElTableColumn } from './components/table/index.mjs';\nexport { Alignment as TableV2Alignment, FixedDir as TableV2FixedDir, SortOrder as TableV2SortOrder } from './components/table-v2/src/constants.mjs';\nexport { default as TableV2 } from './components/table-v2/src/table-v2.mjs';\nexport { placeholderSign as TableV2Placeholder } from './components/table-v2/src/private.mjs';\nexport { autoResizerProps } from './components/table-v2/src/auto-resizer.mjs';\nexport { tableV2Props } from './components/table-v2/src/table.mjs';\nexport { tableV2RowProps } from './components/table-v2/src/row.mjs';\nexport { ElAutoResizer, ElTableV2 } from './components/table-v2/index.mjs';\nexport { tabsEmits, tabsProps } from './components/tabs/src/tabs.mjs';\nexport { tabBarProps } from './components/tabs/src/tab-bar.mjs';\nexport { tabNavEmits, tabNavProps } from './components/tabs/src/tab-nav.mjs';\nexport { tabPaneProps } from './components/tabs/src/tab-pane.mjs';\nexport { tabsRootContextKey } from './components/tabs/src/constants.mjs';\nexport { ElTabPane, ElTabs } from './components/tabs/index.mjs';\nexport { tagEmits, tagProps } from './components/tag/src/tag.mjs';\nexport { ElTag } from './components/tag/index.mjs';\nexport { textProps } from './components/text/src/text.mjs';\nexport { ElText } from './components/text/index.mjs';\nexport { buildTimeList, dateEquals, dayOrDaysToDate, extractDateFormat, extractTimeFormat, formatter, makeList, parseDate, rangeArr, valueEquals } from './components/time-picker/src/utils.mjs';\nexport { DEFAULT_FORMATS_DATE, DEFAULT_FORMATS_DATEPICKER, DEFAULT_FORMATS_TIME, timeUnits } from './components/time-picker/src/constants.mjs';\nexport { timePickerDefaultProps, timePickerRangeTriggerProps, timePickerRngeTriggerProps } from './components/time-picker/src/common/props.mjs';\nexport { ElTimePicker } from './components/time-picker/index.mjs';\nexport { default as CommonPicker } from './components/time-picker/src/common/picker.mjs';\nexport { default as TimePickPanel } from './components/time-picker/src/time-picker-com/panel-time-pick.mjs';\nexport { timeSelectProps } from './components/time-select/src/time-select.mjs';\nexport { ElTimeSelect } from './components/time-select/index.mjs';\nexport { timelineItemProps } from './components/timeline/src/timeline-item.mjs';\nexport { ElTimeline, ElTimelineItem } from './components/timeline/index.mjs';\nexport { tooltipEmits, useTooltipModelToggle, useTooltipModelToggleEmits, useTooltipModelToggleProps, useTooltipProps } from './components/tooltip/src/tooltip.mjs';\nexport { useTooltipTriggerProps } from './components/tooltip/src/trigger.mjs';\nexport { useTooltipContentProps } from './components/tooltip/src/content.mjs';\nexport { TOOLTIP_INJECTION_KEY } from './components/tooltip/src/constants.mjs';\nexport { ElTooltip } from './components/tooltip/index.mjs';\nexport { LEFT_CHECK_CHANGE_EVENT, RIGHT_CHECK_CHANGE_EVENT, transferCheckedChangeFn, transferEmits, transferProps } from './components/transfer/src/transfer.mjs';\nexport { ElTransfer } from './components/transfer/index.mjs';\nexport { ElTree } from './components/tree/index.mjs';\nexport { ElTreeSelect } from './components/tree-select/index.mjs';\nexport { ElTreeV2 } from './components/tree-v2/index.mjs';\nexport { genFileId, uploadBaseProps, uploadListTypes, uploadProps } from './components/upload/src/upload2.mjs';\nexport { uploadContentProps } from './components/upload/src/upload-content2.mjs';\nexport { uploadListEmits, uploadListProps } from './components/upload/src/upload-list.mjs';\nexport { uploadDraggerEmits, uploadDraggerProps } from './components/upload/src/upload-dragger.mjs';\nexport { uploadContextKey } from './components/upload/src/constants.mjs';\nexport { ElUpload } from './components/upload/index.mjs';\nexport { default as FixedSizeList } from './components/virtual-list/src/components/fixed-size-list.mjs';\nexport { default as DynamicSizeList } from './components/virtual-list/src/components/dynamic-size-list.mjs';\nexport { default as FixedSizeGrid } from './components/virtual-list/src/components/fixed-size-grid.mjs';\nexport { default as DynamicSizeGrid } from './components/virtual-list/src/components/dynamic-size-grid.mjs';\nexport { virtualizedGridProps, virtualizedListProps, virtualizedProps, virtualizedScrollbarProps } from './components/virtual-list/src/props.mjs';\nexport { watermarkProps } from './components/watermark/src/watermark.mjs';\nexport { ElWatermark } from './components/watermark/index.mjs';\nexport { tourEmits, tourProps } from './components/tour/src/tour.mjs';\nexport { tourStepEmits, tourStepProps } from './components/tour/src/step2.mjs';\nexport { tourContentEmits, tourContentProps, tourPlacements, tourStrategies } from './components/tour/src/content2.mjs';\nexport { ElTour, ElTourStep } from './components/tour/index.mjs';\nexport { anchorEmits, anchorProps } from './components/anchor/src/anchor.mjs';\nexport { ElAnchor, ElAnchorLink } from './components/anchor/index.mjs';\nexport { defaultProps, segmentedEmits, segmentedProps } from './components/segmented/src/segmented.mjs';\nexport { ElSegmented } from './components/segmented/index.mjs';\nexport { mentionEmits, mentionProps } from './components/mention/src/mention.mjs';\nexport { ElMention } from './components/mention/index.mjs';\nexport { ElInfiniteScroll } from './components/infinite-scroll/index.mjs';\nexport { ElLoading } from './components/loading/index.mjs';\nexport { default as ElLoadingDirective, default as vLoading } from './components/loading/src/directive.mjs';\nexport { default as ElLoadingService } from './components/loading/src/service.mjs';\nexport { messageDefaults, messageEmits, messageProps, messageTypes } from './components/message/src/message.mjs';\nexport { ElMessage } from './components/message/index.mjs';\nexport { ElMessageBox } from './components/message-box/index.mjs';\nexport { notificationEmits, notificationProps, notificationTypes } from './components/notification/src/notification.mjs';\nexport { ElNotification } from './components/notification/index.mjs';\nexport { popoverEmits, popoverProps } from './components/popover/src/popover.mjs';\nexport { ElPopover, ElPopoverDirective } from './components/popover/index.mjs';\nexport { EVENT_CODE } from './constants/aria.mjs';\nexport { WEEK_DAYS, datePickTypes } from './constants/date.mjs';\nexport { CHANGE_EVENT, INPUT_EVENT, UPDATE_MODEL_EVENT } from './constants/event.mjs';\nexport { INSTALLED_KEY } from './constants/key.mjs';\nexport { componentSizeMap, componentSizes } from './constants/size.mjs';\nexport { default as ClickOutside } from './directives/click-outside/index.mjs';\nexport { vRepeatClick } from './directives/repeat-click/index.mjs';\nexport { default as TrapFocus } from './directives/trap-focus/index.mjs';\nexport { default as Mousewheel } from './directives/mousewheel/index.mjs';\nexport { useAttrs } from './hooks/use-attrs/index.mjs';\nexport { useCalcInputWidth } from './hooks/use-calc-input-width/index.mjs';\nexport { useDeprecated } from './hooks/use-deprecated/index.mjs';\nexport { useDraggable } from './hooks/use-draggable/index.mjs';\nexport { useFocus } from './hooks/use-focus/index.mjs';\nexport { buildLocaleContext, buildTranslator, localeContextKey, translate, useLocale } from './hooks/use-locale/index.mjs';\nexport { useLockscreen } from './hooks/use-lockscreen/index.mjs';\nexport { useModal } from './hooks/use-modal/index.mjs';\nexport { createModelToggleComposable, useModelToggle, useModelToggleEmits, useModelToggleProps } from './hooks/use-model-toggle/index.mjs';\nexport { usePreventGlobal } from './hooks/use-prevent-global/index.mjs';\nexport { useProp } from './hooks/use-prop/index.mjs';\nexport { usePopper } from './hooks/use-popper/index.mjs';\nexport { useSameTarget } from './hooks/use-same-target/index.mjs';\nexport { useTeleport } from './hooks/use-teleport/index.mjs';\nexport { useThrottleRender } from './hooks/use-throttle-render/index.mjs';\nexport { useTimeout } from './hooks/use-timeout/index.mjs';\nexport { useTransitionFallthrough, useTransitionFallthroughEmits } from './hooks/use-transition-fallthrough/index.mjs';\nexport { ID_INJECTION_KEY, useId, useIdInjection } from './hooks/use-id/index.mjs';\nexport { useEscapeKeydown } from './hooks/use-escape-keydown/index.mjs';\nexport { usePopperContainer, usePopperContainerId } from './hooks/use-popper-container/index.mjs';\nexport { useDelayedRender } from './hooks/use-intermediate-render/index.mjs';\nexport { useDelayedToggle, useDelayedToggleProps } from './hooks/use-delayed-toggle/index.mjs';\nexport { FORWARD_REF_INJECTION_KEY, useForwardRef, useForwardRefDirective } from './hooks/use-forward-ref/index.mjs';\nexport { defaultNamespace, namespaceContextKey, useGetDerivedNamespace, useNamespace } from './hooks/use-namespace/index.mjs';\nexport { ZINDEX_INJECTION_KEY, defaultInitialZIndex, useZIndex, zIndexContextKey } from './hooks/use-z-index/index.mjs';\nexport { arrowMiddleware, getPositionDataWithUnit, useFloating, useFloatingProps } from './hooks/use-floating/index.mjs';\nexport { useCursor } from './hooks/use-cursor/index.mjs';\nexport { useOrderedChildren } from './hooks/use-ordered-children/index.mjs';\nexport { SIZE_INJECTION_KEY, useGlobalSize, useSizeProp, useSizeProps } from './hooks/use-size/index.mjs';\nexport { useFocusController } from './hooks/use-focus-controller/index.mjs';\nexport { useComposition } from './hooks/use-composition/index.mjs';\nexport { DEFAULT_EMPTY_VALUES, DEFAULT_VALUE_ON_CLEAR, SCOPE, emptyValuesContextKey, useEmptyValues, useEmptyValuesProps } from './hooks/use-empty-values/index.mjs';\nexport { ariaProps, useAriaProps } from './hooks/use-aria/index.mjs';\nconst install = installer.install;\nconst version = installer.version;\nexport { install, version };", "map": {"version": 3, "names": ["install", "installer", "version"], "sources": ["../../../packages/element-plus/index.ts"], "sourcesContent": ["import installer from './defaults'\n\nexport * from '@element-plus/components'\nexport * from '@element-plus/constants'\nexport * from '@element-plus/directives'\nexport * from '@element-plus/hooks'\nexport * from './make-installer'\n\nexport const install = installer.install\nexport const version = installer.version\nexport default installer\n\nexport { default as dayjs } from 'dayjs'\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMY,MAACA,OAAO,GAAGC,SAAS,CAACD,OAAA;AACrB,MAACE,OAAO,GAAGD,SAAS,CAACC,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}