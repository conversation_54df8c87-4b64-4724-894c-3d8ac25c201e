{"ast": null, "code": "import CollapseTransition from './src/collapse-transition.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElCollapseTransition = withInstall(CollapseTransition);\nexport { ElCollapseTransition, ElCollapseTransition as default };", "map": {"version": 3, "names": ["ElCollapseTransition", "withInstall", "CollapseTransition"], "sources": ["../../../../../packages/components/collapse-transition/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport CollapseTransition from './src/collapse-transition.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCollapseTransition: SFCWithInstall<typeof CollapseTransition> =\n  withInstall(CollapseTransition)\n\nexport default ElCollapseTransition\n"], "mappings": ";;AAEY,MAACA,oBAAoB,GAAGC,WAAW,CAACC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}