{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { watch, nextTick, toRefs, computed } from 'vue';\nimport { pick } from 'lodash-unified';\nimport { ElSelect } from '../../select/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nconst useSelect = (props, {\n  attrs,\n  emit\n}, {\n  select,\n  tree,\n  key\n}) => {\n  const ns = useNamespace(\"tree-select\");\n  watch(() => props.data, () => {\n    if (props.filterable) {\n      nextTick(() => {\n        var _a, _b;\n        (_b = tree.value) == null ? void 0 : _b.filter((_a = select.value) == null ? void 0 : _a.states.inputValue);\n      });\n    }\n  }, {\n    flush: \"post\"\n  });\n  const result = {\n    ...pick(toRefs(props), Object.keys(ElSelect.props)),\n    ...attrs,\n    class: computed(() => attrs.class),\n    style: computed(() => attrs.style),\n    \"onUpdate:modelValue\": value => emit(UPDATE_MODEL_EVENT, value),\n    valueKey: key,\n    popperClass: computed(() => {\n      const classes = [ns.e(\"popper\")];\n      if (props.popperClass) classes.push(props.popperClass);\n      return classes.join(\" \");\n    }),\n    filterMethod: (keyword = \"\") => {\n      var _a;\n      if (props.filterMethod) {\n        props.filterMethod(keyword);\n      } else if (props.remoteMethod) {\n        props.remoteMethod(keyword);\n      } else {\n        (_a = tree.value) == null ? void 0 : _a.filter(keyword);\n      }\n    }\n  };\n  return result;\n};\nexport { useSelect };", "map": {"version": 3, "names": ["useSelect", "props", "attrs", "emit", "select", "tree", "key", "ns", "useNamespace", "watch", "data", "filterable", "nextTick", "_a", "_b", "value", "filter", "states", "inputValue", "flush", "result", "pick", "toRefs", "Object", "keys", "ElSelect", "class", "computed", "style", "UPDATE_MODEL_EVENT", "valueKey", "popperClass", "classes", "e", "push", "join", "filterMethod", "keyword", "remoteMethod"], "sources": ["../../../../../../packages/components/tree-select/src/select.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, nextTick, toRefs, watch } from 'vue'\nimport { pick } from 'lodash-unified'\nimport ElSelect from '@element-plus/components/select'\nimport { useNamespace } from '@element-plus/hooks'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport type { Ref } from 'vue'\nimport type ElTree from '@element-plus/components/tree'\n\nexport const useSelect = (\n  props,\n  { attrs, emit },\n  {\n    select,\n    tree,\n    key,\n  }: {\n    select: Ref<InstanceType<typeof ElSelect> | undefined>\n    tree: Ref<InstanceType<typeof ElTree> | undefined>\n    key: Ref<string>\n  }\n) => {\n  const ns = useNamespace('tree-select')\n\n  // update tree data when use filterMethod/remoteMethod\n  watch(\n    () => props.data,\n    () => {\n      if (props.filterable) {\n        nextTick(() => {\n          // let tree node expand only, same with tree filter\n          tree.value?.filter(select.value?.states.inputValue)\n        })\n      }\n    },\n    { flush: 'post' }\n  )\n\n  const result = {\n    ...pick(toRefs(props), Object.keys(ElSelect.props)),\n    ...attrs,\n    class: computed(() => attrs.class),\n    style: computed(() => attrs.style),\n    // attrs is not reactive, when v-model binding source changes,\n    // this listener is still old, see the bug(or test 'v-model source change'):\n    // https://github.com/element-plus/element-plus/issues/14204\n    'onUpdate:modelValue': (value) => emit(UPDATE_MODEL_EVENT, value),\n    valueKey: key,\n    popperClass: computed(() => {\n      const classes = [ns.e('popper')]\n      if (props.popperClass) classes.push(props.popperClass)\n      return classes.join(' ')\n    }),\n    filterMethod: (keyword = '') => {\n      if (props.filterMethod) {\n        props.filterMethod(keyword)\n      } else if (props.remoteMethod) {\n        props.remoteMethod(keyword)\n      } else {\n        // let tree node expand only, same with tree filter\n        tree.value?.filter(keyword)\n      }\n    },\n  }\n\n  return result\n}\n"], "mappings": ";;;;;;;;AAKY,MAACA,SAAS,GAAGA,CAACC,KAAK,EAAE;EAAEC,KAAK;EAAEC;AAAI,CAAE,EAAE;EAChDC,MAAM;EACNC,IAAI;EACJC;AACF,CAAC,KAAK;EACJ,MAAMC,EAAE,GAAGC,YAAY,CAAC,aAAa,CAAC;EACtCC,KAAK,CAAC,MAAMR,KAAK,CAACS,IAAI,EAAE,MAAM;IAC5B,IAAIT,KAAK,CAACU,UAAU,EAAE;MACpBC,QAAQ,CAAC,MAAM;QACb,IAAIC,EAAE,EAAEC,EAAE;QACV,CAACA,EAAE,GAAGT,IAAI,CAACU,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,MAAM,CAAC,CAACH,EAAE,GAAGT,MAAM,CAACW,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACI,MAAM,CAACC,UAAU,CAAC;MACnH,CAAO,CAAC;IACR;EACA,CAAG,EAAE;IAAEC,KAAK,EAAE;EAAM,CAAE,CAAC;EACrB,MAAMC,MAAM,GAAG;IACb,GAAGC,IAAI,CAACC,MAAM,CAACrB,KAAK,CAAC,EAAEsB,MAAM,CAACC,IAAI,CAACC,QAAQ,CAACxB,KAAK,CAAC,CAAC;IACnD,GAAGC,KAAK;IACRwB,KAAK,EAAEC,QAAQ,CAAC,MAAMzB,KAAK,CAACwB,KAAK,CAAC;IAClCE,KAAK,EAAED,QAAQ,CAAC,MAAMzB,KAAK,CAAC0B,KAAK,CAAC;IAClC,qBAAqB,EAAGb,KAAK,IAAKZ,IAAI,CAAC0B,kBAAkB,EAAEd,KAAK,CAAC;IACjEe,QAAQ,EAAExB,GAAG;IACbyB,WAAW,EAAEJ,QAAQ,CAAC,MAAM;MAC1B,MAAMK,OAAO,GAAG,CAACzB,EAAE,CAAC0B,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC,IAAIhC,KAAK,CAAC8B,WAAW,EACnBC,OAAO,CAACE,IAAI,CAACjC,KAAK,CAAC8B,WAAW,CAAC;MACjC,OAAOC,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC;IAC9B,CAAK,CAAC;IACFC,YAAY,EAAEA,CAACC,OAAO,GAAG,EAAE,KAAK;MAC9B,IAAIxB,EAAE;MACN,IAAIZ,KAAK,CAACmC,YAAY,EAAE;QACtBnC,KAAK,CAACmC,YAAY,CAACC,OAAO,CAAC;MACnC,CAAO,MAAM,IAAIpC,KAAK,CAACqC,YAAY,EAAE;QAC7BrC,KAAK,CAACqC,YAAY,CAACD,OAAO,CAAC;MACnC,CAAO,MAAM;QACL,CAACxB,EAAE,GAAGR,IAAI,CAACU,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,MAAM,CAACqB,OAAO,CAAC;MAC/D;IACA;EACA,CAAG;EACD,OAAOjB,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}