{"ast": null, "code": "const buttonGroupContextKey = Symbol(\"buttonGroupContextKey\");\nexport { buttonGroupContextKey };", "map": {"version": 3, "names": ["buttonGroupContextKey", "Symbol"], "sources": ["../../../../../../packages/components/button/src/constants.ts"], "sourcesContent": ["import type { InjectionKey } from 'vue'\n\nimport type { ButtonProps } from './button'\n\nexport interface ButtonGroupContext {\n  size?: ButtonProps['size']\n  type?: ButtonProps['type']\n}\n\nexport const buttonGroupContextKey: InjectionKey<ButtonGroupContext> = Symbol(\n  'buttonGroupContextKey'\n)\n"], "mappings": "AAAY,MAACA,qBAAqB,GAAGC,MAAM,CAAC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}