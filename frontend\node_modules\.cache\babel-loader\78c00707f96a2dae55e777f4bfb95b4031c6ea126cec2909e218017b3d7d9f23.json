{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst autoResizerProps = buildProps({\n  disableWidth: Boolean,\n  disableHeight: <PERSON>olean,\n  onResize: {\n    type: definePropType(Function)\n  }\n});\nexport { autoResizerProps };", "map": {"version": 3, "names": ["autoResizerProps", "buildProps", "disable<PERSON><PERSON><PERSON>", "Boolean", "disableHeight", "onResize", "type", "definePropType", "Function"], "sources": ["../../../../../../packages/components/table-v2/src/auto-resizer.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\n\ntype AutoResizeHandler = (event: { height: number; width: number }) => void\n\nexport const autoResizerProps = buildProps({\n  disableWidth: <PERSON>olean,\n  disableHeight: <PERSON>olean,\n  onResize: {\n    type: definePropType<AutoResizeHandler>(Function),\n  },\n} as const)\n\nexport type AutoResizerProps = ExtractPropTypes<typeof autoResizerProps>\n"], "mappings": ";AACY,MAACA,gBAAgB,GAAGC,UAAU,CAAC;EACzCC,YAAY,EAAEC,OAAO;EACrBC,aAAa,EAAED,OAAO;EACtBE,QAAQ,EAAE;IACRC,IAAI,EAAEC,cAAc,CAACC,QAAQ;EACjC;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}