{"ast": null, "code": "const FOCUS_AFTER_TRAPPED = \"focus-trap.focus-after-trapped\";\nconst FOCUS_AFTER_RELEASED = \"focus-trap.focus-after-released\";\nconst FOCUSOUT_PREVENTED = \"focus-trap.focusout-prevented\";\nconst FOCUS_AFTER_TRAPPED_OPTS = {\n  cancelable: true,\n  bubbles: false\n};\nconst FOCUSOUT_PREVENTED_OPTS = {\n  cancelable: true,\n  bubbles: false\n};\nconst ON_TRAP_FOCUS_EVT = \"focusAfterTrapped\";\nconst ON_RELEASE_FOCUS_EVT = \"focusAfterReleased\";\nconst FOCUS_TRAP_INJECTION_KEY = Symbol(\"elFocusTrap\");\nexport { FOCUSOUT_PREVENTED, FOCUSOUT_PREVENTED_OPTS, FOCUS_AFTER_RELEASED, FOCUS_AFTER_TRAPPED, FOCUS_AFTER_TRAPPED_OPTS, FOCUS_TRAP_INJECTION_KEY, ON_RELEASE_FOCUS_EVT, ON_TRAP_FOCUS_EVT };", "map": {"version": 3, "names": ["FOCUS_AFTER_TRAPPED", "FOCUS_AFTER_RELEASED", "FOCUSOUT_PREVENTED", "FOCUS_AFTER_TRAPPED_OPTS", "cancelable", "bubbles", "FOCUSOUT_PREVENTED_OPTS", "ON_TRAP_FOCUS_EVT", "ON_RELEASE_FOCUS_EVT", "FOCUS_TRAP_INJECTION_KEY", "Symbol"], "sources": ["../../../../../../packages/components/focus-trap/src/tokens.ts"], "sourcesContent": ["import type { InjectionKey, Ref } from 'vue'\n\nexport const FOCUS_AFTER_TRAPPED = 'focus-trap.focus-after-trapped'\nexport const FOCUS_AFTER_RELEASED = 'focus-trap.focus-after-released'\nexport const FOCUSOUT_PREVENTED = 'focus-trap.focusout-prevented'\nexport const FOCUS_AFTER_TRAPPED_OPTS: EventInit = {\n  cancelable: true,\n  bubbles: false,\n}\nexport const FOCUSOUT_PREVENTED_OPTS: EventInit = {\n  cancelable: true,\n  bubbles: false,\n}\n\nexport const ON_TRAP_FOCUS_EVT = 'focusAfterTrapped'\nexport const ON_RELEASE_FOCUS_EVT = 'focusAfterReleased'\n\nexport type FocusTrapInjectionContext = {\n  focusTrapRef: Ref<HTMLElement | undefined>\n  onKeydown: (e: KeyboardEvent) => void\n}\n\nexport const FOCUS_TRAP_INJECTION_KEY: InjectionKey<FocusTrapInjectionContext> =\n  Symbol('elFocusTrap')\n"], "mappings": "AAAY,MAACA,mBAAmB,GAAG;AACvB,MAACC,oBAAoB,GAAG;AACxB,MAACC,kBAAkB,GAAG;AACtB,MAACC,wBAAwB,GAAG;EACtCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE;AACX;AACY,MAACC,uBAAuB,GAAG;EACrCF,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE;AACX;AACY,MAACE,iBAAiB,GAAG;AACrB,MAACC,oBAAoB,GAAG;AACxB,MAACC,wBAAwB,GAAGC,MAAM,CAAC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}