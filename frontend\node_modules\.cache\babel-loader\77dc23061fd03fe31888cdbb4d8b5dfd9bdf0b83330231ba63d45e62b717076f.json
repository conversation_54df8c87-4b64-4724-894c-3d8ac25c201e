{"ast": null, "code": "import { getCurrentInstance, shallowRef, computed, ref, onMounted, watch } from 'vue';\nimport { draggable } from '../utils/draggable.mjs';\nimport { getClientXY } from '../../../../utils/dom/position.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { addUnit } from '../../../../utils/dom/style.mjs';\nconst useAlphaSlider = props => {\n  const instance = getCurrentInstance();\n  const {\n    t\n  } = useLocale();\n  const thumb = shallowRef();\n  const bar = shallowRef();\n  const alpha = computed(() => props.color.get(\"alpha\"));\n  const alphaLabel = computed(() => t(\"el.colorpicker.alphaLabel\"));\n  function handleClick(event) {\n    var _a;\n    const target = event.target;\n    if (target !== thumb.value) {\n      handleDrag(event);\n    }\n    (_a = thumb.value) == null ? void 0 : _a.focus();\n  }\n  function handleDrag(event) {\n    if (!bar.value || !thumb.value) return;\n    const el = instance.vnode.el;\n    const rect = el.getBoundingClientRect();\n    const {\n      clientX,\n      clientY\n    } = getClientXY(event);\n    if (!props.vertical) {\n      let left = clientX - rect.left;\n      left = Math.max(thumb.value.offsetWidth / 2, left);\n      left = Math.min(left, rect.width - thumb.value.offsetWidth / 2);\n      props.color.set(\"alpha\", Math.round((left - thumb.value.offsetWidth / 2) / (rect.width - thumb.value.offsetWidth) * 100));\n    } else {\n      let top = clientY - rect.top;\n      top = Math.max(thumb.value.offsetHeight / 2, top);\n      top = Math.min(top, rect.height - thumb.value.offsetHeight / 2);\n      props.color.set(\"alpha\", Math.round((top - thumb.value.offsetHeight / 2) / (rect.height - thumb.value.offsetHeight) * 100));\n    }\n  }\n  function handleKeydown(event) {\n    const {\n      code,\n      shiftKey\n    } = event;\n    const step = shiftKey ? 10 : 1;\n    switch (code) {\n      case EVENT_CODE.left:\n      case EVENT_CODE.down:\n        event.preventDefault();\n        event.stopPropagation();\n        incrementPosition(-step);\n        break;\n      case EVENT_CODE.right:\n      case EVENT_CODE.up:\n        event.preventDefault();\n        event.stopPropagation();\n        incrementPosition(step);\n        break;\n    }\n  }\n  function incrementPosition(step) {\n    let next = alpha.value + step;\n    next = next < 0 ? 0 : next > 100 ? 100 : next;\n    props.color.set(\"alpha\", next);\n  }\n  return {\n    thumb,\n    bar,\n    alpha,\n    alphaLabel,\n    handleDrag,\n    handleClick,\n    handleKeydown\n  };\n};\nconst useAlphaSliderDOM = (props, {\n  bar,\n  thumb,\n  handleDrag\n}) => {\n  const instance = getCurrentInstance();\n  const ns = useNamespace(\"color-alpha-slider\");\n  const thumbLeft = ref(0);\n  const thumbTop = ref(0);\n  const background = ref();\n  function getThumbLeft() {\n    if (!thumb.value) return 0;\n    if (props.vertical) return 0;\n    const el = instance.vnode.el;\n    const alpha = props.color.get(\"alpha\");\n    if (!el) return 0;\n    return Math.round(alpha * (el.offsetWidth - thumb.value.offsetWidth / 2) / 100);\n  }\n  function getThumbTop() {\n    if (!thumb.value) return 0;\n    const el = instance.vnode.el;\n    if (!props.vertical) return 0;\n    const alpha = props.color.get(\"alpha\");\n    if (!el) return 0;\n    return Math.round(alpha * (el.offsetHeight - thumb.value.offsetHeight / 2) / 100);\n  }\n  function getBackground() {\n    if (props.color && props.color.value) {\n      const {\n        r,\n        g,\n        b\n      } = props.color.toRgb();\n      return `linear-gradient(to right, rgba(${r}, ${g}, ${b}, 0) 0%, rgba(${r}, ${g}, ${b}, 1) 100%)`;\n    }\n    return \"\";\n  }\n  function update() {\n    thumbLeft.value = getThumbLeft();\n    thumbTop.value = getThumbTop();\n    background.value = getBackground();\n  }\n  onMounted(() => {\n    if (!bar.value || !thumb.value) return;\n    const dragConfig = {\n      drag: event => {\n        handleDrag(event);\n      },\n      end: event => {\n        handleDrag(event);\n      }\n    };\n    draggable(bar.value, dragConfig);\n    draggable(thumb.value, dragConfig);\n    update();\n  });\n  watch(() => props.color.get(\"alpha\"), () => update());\n  watch(() => props.color.value, () => update());\n  const rootKls = computed(() => [ns.b(), ns.is(\"vertical\", props.vertical)]);\n  const barKls = computed(() => ns.e(\"bar\"));\n  const thumbKls = computed(() => ns.e(\"thumb\"));\n  const barStyle = computed(() => ({\n    background: background.value\n  }));\n  const thumbStyle = computed(() => ({\n    left: addUnit(thumbLeft.value),\n    top: addUnit(thumbTop.value)\n  }));\n  return {\n    rootKls,\n    barKls,\n    barStyle,\n    thumbKls,\n    thumbStyle,\n    update\n  };\n};\nexport { useAlphaSlider, useAlphaSliderDOM };", "map": {"version": 3, "names": ["useAlphaSlider", "props", "instance", "getCurrentInstance", "t", "useLocale", "thumb", "shallowRef", "bar", "alpha", "computed", "color", "get", "alphaLabel", "handleClick", "event", "_a", "target", "value", "handleDrag", "focus", "el", "vnode", "rect", "getBoundingClientRect", "clientX", "clientY", "getClientXY", "vertical", "left", "Math", "max", "offsetWidth", "min", "width", "set", "round", "top", "offsetHeight", "height", "handleKeydown", "code", "shift<PERSON>ey", "step", "EVENT_CODE", "down", "preventDefault", "stopPropagation", "incrementPosition", "right", "up", "next", "useAlphaSliderDOM", "ns", "useNamespace", "thumbLeft", "ref", "thumbTop", "background", "getThumbLeft", "getThumbTop", "getBackground", "r", "g", "b", "toRgb", "update", "onMounted", "dragConfig", "drag", "end", "draggable", "watch", "rootKls", "is", "barKls", "e", "thumbKls", "barStyle", "thumbStyle", "addUnit"], "sources": ["../../../../../../../packages/components/color-picker/src/composables/use-alpha-slider.ts"], "sourcesContent": ["import {\n  computed,\n  getCurrentInstance,\n  onMounted,\n  ref,\n  shallowRef,\n  watch,\n} from 'vue'\nimport { addUnit, getClientXY } from '@element-plus/utils'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { draggable } from '../utils/draggable'\n\nimport type { AlphaSliderProps } from '../props/alpha-slider'\n\nexport const useAlphaSlider = (props: AlphaSliderProps) => {\n  const instance = getCurrentInstance()!\n  const { t } = useLocale()\n\n  const thumb = shallowRef<HTMLElement>()\n  const bar = shallowRef<HTMLElement>()\n\n  const alpha = computed(() => props.color.get('alpha'))\n  const alphaLabel = computed(() => t('el.colorpicker.alphaLabel'))\n\n  function handleClick(event: MouseEvent | TouchEvent) {\n    const target = event.target\n\n    if (target !== thumb.value) {\n      handleDrag(event)\n    }\n    thumb.value?.focus()\n  }\n\n  function handleDrag(event: MouseEvent | TouchEvent) {\n    if (!bar.value || !thumb.value) return\n\n    const el = instance.vnode.el as HTMLElement\n    const rect = el.getBoundingClientRect()\n    const { clientX, clientY } = getClientXY(event)\n\n    if (!props.vertical) {\n      let left = clientX - rect.left\n      left = Math.max(thumb.value.offsetWidth / 2, left)\n      left = Math.min(left, rect.width - thumb.value.offsetWidth / 2)\n\n      props.color.set(\n        'alpha',\n        Math.round(\n          ((left - thumb.value.offsetWidth / 2) /\n            (rect.width - thumb.value.offsetWidth)) *\n            100\n        )\n      )\n    } else {\n      let top = clientY - rect.top\n      top = Math.max(thumb.value.offsetHeight / 2, top)\n      top = Math.min(top, rect.height - thumb.value.offsetHeight / 2)\n\n      props.color.set(\n        'alpha',\n        Math.round(\n          ((top - thumb.value.offsetHeight / 2) /\n            (rect.height - thumb.value.offsetHeight)) *\n            100\n        )\n      )\n    }\n  }\n\n  function handleKeydown(event: KeyboardEvent) {\n    const { code, shiftKey } = event\n    const step = shiftKey ? 10 : 1\n\n    switch (code) {\n      case EVENT_CODE.left:\n      case EVENT_CODE.down:\n        event.preventDefault()\n        event.stopPropagation()\n        incrementPosition(-step)\n        break\n      case EVENT_CODE.right:\n      case EVENT_CODE.up:\n        event.preventDefault()\n        event.stopPropagation()\n        incrementPosition(step)\n        break\n    }\n  }\n\n  function incrementPosition(step: number) {\n    let next = alpha.value + step\n    next = next < 0 ? 0 : next > 100 ? 100 : next\n    props.color.set('alpha', next)\n  }\n\n  return {\n    thumb,\n    bar,\n    alpha,\n    alphaLabel,\n    handleDrag,\n    handleClick,\n    handleKeydown,\n  }\n}\n\nexport const useAlphaSliderDOM = (\n  props: AlphaSliderProps,\n  {\n    bar,\n    thumb,\n    handleDrag,\n  }: Pick<ReturnType<typeof useAlphaSlider>, 'bar' | 'thumb' | 'handleDrag'>\n) => {\n  const instance = getCurrentInstance()!\n\n  const ns = useNamespace('color-alpha-slider')\n  // refs\n\n  const thumbLeft = ref(0)\n  const thumbTop = ref(0)\n  const background = ref<string>()\n\n  function getThumbLeft() {\n    if (!thumb.value) return 0\n\n    if (props.vertical) return 0\n    const el = instance.vnode.el\n    const alpha = props.color.get('alpha')\n\n    if (!el) return 0\n    return Math.round(\n      (alpha * (el.offsetWidth - thumb.value.offsetWidth / 2)) / 100\n    )\n  }\n\n  function getThumbTop() {\n    if (!thumb.value) return 0\n\n    const el = instance.vnode.el\n    if (!props.vertical) return 0\n    const alpha = props.color.get('alpha')\n\n    if (!el) return 0\n    return Math.round(\n      (alpha * (el.offsetHeight - thumb.value.offsetHeight / 2)) / 100\n    )\n  }\n\n  function getBackground() {\n    if (props.color && props.color.value) {\n      const { r, g, b } = props.color.toRgb()\n      return `linear-gradient(to right, rgba(${r}, ${g}, ${b}, 0) 0%, rgba(${r}, ${g}, ${b}, 1) 100%)`\n    }\n    return ''\n  }\n\n  function update() {\n    thumbLeft.value = getThumbLeft()\n    thumbTop.value = getThumbTop()\n    background.value = getBackground()\n  }\n\n  onMounted(() => {\n    if (!bar.value || !thumb.value) return\n\n    const dragConfig = {\n      drag: (event: MouseEvent | TouchEvent) => {\n        handleDrag(event)\n      },\n      end: (event: MouseEvent | TouchEvent) => {\n        handleDrag(event)\n      },\n    }\n\n    draggable(bar.value, dragConfig)\n    draggable(thumb.value, dragConfig)\n    update()\n  })\n\n  watch(\n    () => props.color.get('alpha'),\n    () => update()\n  )\n  watch(\n    () => props.color.value,\n    () => update()\n  )\n\n  const rootKls = computed(() => [ns.b(), ns.is('vertical', props.vertical)])\n  const barKls = computed(() => ns.e('bar'))\n  const thumbKls = computed(() => ns.e('thumb'))\n  const barStyle = computed(() => ({ background: background.value }))\n  const thumbStyle = computed(() => ({\n    left: addUnit(thumbLeft.value),\n    top: addUnit(thumbTop.value),\n  }))\n\n  return { rootKls, barKls, barStyle, thumbKls, thumbStyle, update }\n}\n"], "mappings": ";;;;;;;AAYY,MAACA,cAAc,GAAIC,KAAK,IAAK;EACvC,MAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,MAAM;IAAEC;EAAC,CAAE,GAAGC,SAAS,EAAE;EACzB,MAAMC,KAAK,GAAGC,UAAU,EAAE;EAC1B,MAAMC,GAAG,GAAGD,UAAU,EAAE;EACxB,MAAME,KAAK,GAAGC,QAAQ,CAAC,MAAMT,KAAK,CAACU,KAAK,CAACC,GAAG,CAAC,OAAO,CAAC,CAAC;EACtD,MAAMC,UAAU,GAAGH,QAAQ,CAAC,MAAMN,CAAC,CAAC,2BAA2B,CAAC,CAAC;EACjE,SAASU,WAAWA,CAACC,KAAK,EAAE;IAC1B,IAAIC,EAAE;IACN,MAAMC,MAAM,GAAGF,KAAK,CAACE,MAAM;IAC3B,IAAIA,MAAM,KAAKX,KAAK,CAACY,KAAK,EAAE;MAC1BC,UAAU,CAACJ,KAAK,CAAC;IACvB;IACI,CAACC,EAAE,GAAGV,KAAK,CAACY,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACI,KAAK,EAAE;EACpD;EACE,SAASD,UAAUA,CAACJ,KAAK,EAAE;IACzB,IAAI,CAACP,GAAG,CAACU,KAAK,IAAI,CAACZ,KAAK,CAACY,KAAK,EAC5B;IACF,MAAMG,EAAE,GAAGnB,QAAQ,CAACoB,KAAK,CAACD,EAAE;IAC5B,MAAME,IAAI,GAAGF,EAAE,CAACG,qBAAqB,EAAE;IACvC,MAAM;MAAEC,OAAO;MAAEC;IAAO,CAAE,GAAGC,WAAW,CAACZ,KAAK,CAAC;IAC/C,IAAI,CAACd,KAAK,CAAC2B,QAAQ,EAAE;MACnB,IAAIC,IAAI,GAAGJ,OAAO,GAAGF,IAAI,CAACM,IAAI;MAC9BA,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACzB,KAAK,CAACY,KAAK,CAACc,WAAW,GAAG,CAAC,EAAEH,IAAI,CAAC;MAClDA,IAAI,GAAGC,IAAI,CAACG,GAAG,CAACJ,IAAI,EAAEN,IAAI,CAACW,KAAK,GAAG5B,KAAK,CAACY,KAAK,CAACc,WAAW,GAAG,CAAC,CAAC;MAC/D/B,KAAK,CAACU,KAAK,CAACwB,GAAG,CAAC,OAAO,EAAEL,IAAI,CAACM,KAAK,CAAC,CAACP,IAAI,GAAGvB,KAAK,CAACY,KAAK,CAACc,WAAW,GAAG,CAAC,KAAKT,IAAI,CAACW,KAAK,GAAG5B,KAAK,CAACY,KAAK,CAACc,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC;IAC/H,CAAK,MAAM;MACL,IAAIK,GAAG,GAAGX,OAAO,GAAGH,IAAI,CAACc,GAAG;MAC5BA,GAAG,GAAGP,IAAI,CAACC,GAAG,CAACzB,KAAK,CAACY,KAAK,CAACoB,YAAY,GAAG,CAAC,EAAED,GAAG,CAAC;MACjDA,GAAG,GAAGP,IAAI,CAACG,GAAG,CAACI,GAAG,EAAEd,IAAI,CAACgB,MAAM,GAAGjC,KAAK,CAACY,KAAK,CAACoB,YAAY,GAAG,CAAC,CAAC;MAC/DrC,KAAK,CAACU,KAAK,CAACwB,GAAG,CAAC,OAAO,EAAEL,IAAI,CAACM,KAAK,CAAC,CAACC,GAAG,GAAG/B,KAAK,CAACY,KAAK,CAACoB,YAAY,GAAG,CAAC,KAAKf,IAAI,CAACgB,MAAM,GAAGjC,KAAK,CAACY,KAAK,CAACoB,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC;IACjI;EACA;EACE,SAASE,aAAaA,CAACzB,KAAK,EAAE;IAC5B,MAAM;MAAE0B,IAAI;MAAEC;IAAQ,CAAE,GAAG3B,KAAK;IAChC,MAAM4B,IAAI,GAAGD,QAAQ,GAAG,EAAE,GAAG,CAAC;IAC9B,QAAQD,IAAI;MACV,KAAKG,UAAU,CAACf,IAAI;MACpB,KAAKe,UAAU,CAACC,IAAI;QAClB9B,KAAK,CAAC+B,cAAc,EAAE;QACtB/B,KAAK,CAACgC,eAAe,EAAE;QACvBC,iBAAiB,CAAC,CAACL,IAAI,CAAC;QACxB;MACF,KAAKC,UAAU,CAACK,KAAK;MACrB,KAAKL,UAAU,CAACM,EAAE;QAChBnC,KAAK,CAAC+B,cAAc,EAAE;QACtB/B,KAAK,CAACgC,eAAe,EAAE;QACvBC,iBAAiB,CAACL,IAAI,CAAC;QACvB;IACR;EACA;EACE,SAASK,iBAAiBA,CAACL,IAAI,EAAE;IAC/B,IAAIQ,IAAI,GAAG1C,KAAK,CAACS,KAAK,GAAGyB,IAAI;IAC7BQ,IAAI,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,GAAGA,IAAI,GAAG,GAAG,GAAG,GAAG,GAAGA,IAAI;IAC7ClD,KAAK,CAACU,KAAK,CAACwB,GAAG,CAAC,OAAO,EAAEgB,IAAI,CAAC;EAClC;EACE,OAAO;IACL7C,KAAK;IACLE,GAAG;IACHC,KAAK;IACLI,UAAU;IACVM,UAAU;IACVL,WAAW;IACX0B;EACJ,CAAG;AACH;AACY,MAACY,iBAAiB,GAAGA,CAACnD,KAAK,EAAE;EACvCO,GAAG;EACHF,KAAK;EACLa;AACF,CAAC,KAAK;EACJ,MAAMjB,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,MAAMkD,EAAE,GAAGC,YAAY,CAAC,oBAAoB,CAAC;EAC7C,MAAMC,SAAS,GAAGC,GAAG,CAAC,CAAC,CAAC;EACxB,MAAMC,QAAQ,GAAGD,GAAG,CAAC,CAAC,CAAC;EACvB,MAAME,UAAU,GAAGF,GAAG,EAAE;EACxB,SAASG,YAAYA,CAAA,EAAG;IACtB,IAAI,CAACrD,KAAK,CAACY,KAAK,EACd,OAAO,CAAC;IACV,IAAIjB,KAAK,CAAC2B,QAAQ,EAChB,OAAO,CAAC;IACV,MAAMP,EAAE,GAAGnB,QAAQ,CAACoB,KAAK,CAACD,EAAE;IAC5B,MAAMZ,KAAK,GAAGR,KAAK,CAACU,KAAK,CAACC,GAAG,CAAC,OAAO,CAAC;IACtC,IAAI,CAACS,EAAE,EACL,OAAO,CAAC;IACV,OAAOS,IAAI,CAACM,KAAK,CAAC3B,KAAK,IAAIY,EAAE,CAACW,WAAW,GAAG1B,KAAK,CAACY,KAAK,CAACc,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;EACnF;EACE,SAAS4B,WAAWA,CAAA,EAAG;IACrB,IAAI,CAACtD,KAAK,CAACY,KAAK,EACd,OAAO,CAAC;IACV,MAAMG,EAAE,GAAGnB,QAAQ,CAACoB,KAAK,CAACD,EAAE;IAC5B,IAAI,CAACpB,KAAK,CAAC2B,QAAQ,EACjB,OAAO,CAAC;IACV,MAAMnB,KAAK,GAAGR,KAAK,CAACU,KAAK,CAACC,GAAG,CAAC,OAAO,CAAC;IACtC,IAAI,CAACS,EAAE,EACL,OAAO,CAAC;IACV,OAAOS,IAAI,CAACM,KAAK,CAAC3B,KAAK,IAAIY,EAAE,CAACiB,YAAY,GAAGhC,KAAK,CAACY,KAAK,CAACoB,YAAY,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;EACrF;EACE,SAASuB,aAAaA,CAAA,EAAG;IACvB,IAAI5D,KAAK,CAACU,KAAK,IAAIV,KAAK,CAACU,KAAK,CAACO,KAAK,EAAE;MACpC,MAAM;QAAE4C,CAAC;QAAEC,CAAC;QAAEC;MAAC,CAAE,GAAG/D,KAAK,CAACU,KAAK,CAACsD,KAAK,EAAE;MACvC,OAAO,kCAAkCH,CAAC,KAAKC,CAAC,KAAKC,CAAC,iBAAiBF,CAAC,KAAKC,CAAC,KAAKC,CAAC,YAAY;IACtG;IACI,OAAO,EAAE;EACb;EACE,SAASE,MAAMA,CAAA,EAAG;IAChBX,SAAS,CAACrC,KAAK,GAAGyC,YAAY,EAAE;IAChCF,QAAQ,CAACvC,KAAK,GAAG0C,WAAW,EAAE;IAC9BF,UAAU,CAACxC,KAAK,GAAG2C,aAAa,EAAE;EACtC;EACEM,SAAS,CAAC,MAAM;IACd,IAAI,CAAC3D,GAAG,CAACU,KAAK,IAAI,CAACZ,KAAK,CAACY,KAAK,EAC5B;IACF,MAAMkD,UAAU,GAAG;MACjBC,IAAI,EAAGtD,KAAK,IAAK;QACfI,UAAU,CAACJ,KAAK,CAAC;MACzB,CAAO;MACDuD,GAAG,EAAGvD,KAAK,IAAK;QACdI,UAAU,CAACJ,KAAK,CAAC;MACzB;IACA,CAAK;IACDwD,SAAS,CAAC/D,GAAG,CAACU,KAAK,EAAEkD,UAAU,CAAC;IAChCG,SAAS,CAACjE,KAAK,CAACY,KAAK,EAAEkD,UAAU,CAAC;IAClCF,MAAM,EAAE;EACZ,CAAG,CAAC;EACFM,KAAK,CAAC,MAAMvE,KAAK,CAACU,KAAK,CAACC,GAAG,CAAC,OAAO,CAAC,EAAE,MAAMsD,MAAM,EAAE,CAAC;EACrDM,KAAK,CAAC,MAAMvE,KAAK,CAACU,KAAK,CAACO,KAAK,EAAE,MAAMgD,MAAM,EAAE,CAAC;EAC9C,MAAMO,OAAO,GAAG/D,QAAQ,CAAC,MAAM,CAAC2C,EAAE,CAACW,CAAC,EAAE,EAAEX,EAAE,CAACqB,EAAE,CAAC,UAAU,EAAEzE,KAAK,CAAC2B,QAAQ,CAAC,CAAC,CAAC;EAC3E,MAAM+C,MAAM,GAAGjE,QAAQ,CAAC,MAAM2C,EAAE,CAACuB,CAAC,CAAC,KAAK,CAAC,CAAC;EAC1C,MAAMC,QAAQ,GAAGnE,QAAQ,CAAC,MAAM2C,EAAE,CAACuB,CAAC,CAAC,OAAO,CAAC,CAAC;EAC9C,MAAME,QAAQ,GAAGpE,QAAQ,CAAC,OAAO;IAAEgD,UAAU,EAAEA,UAAU,CAACxC;EAAK,CAAE,CAAC,CAAC;EACnE,MAAM6D,UAAU,GAAGrE,QAAQ,CAAC,OAAO;IACjCmB,IAAI,EAAEmD,OAAO,CAACzB,SAAS,CAACrC,KAAK,CAAC;IAC9BmB,GAAG,EAAE2C,OAAO,CAACvB,QAAQ,CAACvC,KAAK;EAC/B,CAAG,CAAC,CAAC;EACH,OAAO;IAAEuD,OAAO;IAAEE,MAAM;IAAEG,QAAQ;IAAED,QAAQ;IAAEE,UAAU;IAAEb;EAAM,CAAE;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}