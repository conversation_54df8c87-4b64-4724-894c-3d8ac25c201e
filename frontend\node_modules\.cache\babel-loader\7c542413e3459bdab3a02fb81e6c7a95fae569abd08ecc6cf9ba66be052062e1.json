{"ast": null, "code": "import { castArray } from 'lodash-unified';\nimport { isFirefox } from '../../../utils/browser.mjs';\nconst filterOption = (pattern, option) => {\n  const lowerCase = pattern.toLowerCase();\n  const label = option.label || option.value;\n  return label.toLowerCase().includes(lowerCase);\n};\nconst getMentionCtx = (inputEl, prefix, split) => {\n  const {\n    selectionEnd\n  } = inputEl;\n  if (selectionEnd === null) return;\n  const inputValue = inputEl.value;\n  const prefixArray = castArray(prefix);\n  let splitIndex = -1;\n  let mentionCtx;\n  for (let i = selectionEnd - 1; i >= 0; --i) {\n    const char = inputValue[i];\n    if (char === split || char === \"\\n\" || char === \"\\r\") {\n      splitIndex = i;\n      continue;\n    }\n    if (prefixArray.includes(char)) {\n      const end = splitIndex === -1 ? selectionEnd : splitIndex;\n      const pattern = inputValue.slice(i + 1, end);\n      mentionCtx = {\n        pattern,\n        start: i + 1,\n        end,\n        prefix: char,\n        prefixIndex: i,\n        splitIndex,\n        selectionEnd\n      };\n      break;\n    }\n  }\n  return mentionCtx;\n};\nconst getCursorPosition = (element, options = {\n  debug: false,\n  useSelectionEnd: false\n}) => {\n  const selectionStart = element.selectionStart !== null ? element.selectionStart : 0;\n  const selectionEnd = element.selectionEnd !== null ? element.selectionEnd : 0;\n  const position = options.useSelectionEnd ? selectionEnd : selectionStart;\n  const properties = [\"direction\", \"boxSizing\", \"width\", \"height\", \"overflowX\", \"overflowY\", \"borderTopWidth\", \"borderRightWidth\", \"borderBottomWidth\", \"borderLeftWidth\", \"borderStyle\", \"paddingTop\", \"paddingRight\", \"paddingBottom\", \"paddingLeft\", \"fontStyle\", \"fontVariant\", \"fontWeight\", \"fontStretch\", \"fontSize\", \"fontSizeAdjust\", \"lineHeight\", \"fontFamily\", \"textAlign\", \"textTransform\", \"textIndent\", \"textDecoration\", \"letterSpacing\", \"wordSpacing\", \"tabSize\", \"MozTabSize\"];\n  if (options.debug) {\n    const el = document.querySelector(\"#input-textarea-caret-position-mirror-div\");\n    if (el == null ? void 0 : el.parentNode) el.parentNode.removeChild(el);\n  }\n  const div = document.createElement(\"div\");\n  div.id = \"input-textarea-caret-position-mirror-div\";\n  document.body.appendChild(div);\n  const style = div.style;\n  const computed = window.getComputedStyle(element);\n  const isInput = element.nodeName === \"INPUT\";\n  style.whiteSpace = isInput ? \"nowrap\" : \"pre-wrap\";\n  if (!isInput) style.wordWrap = \"break-word\";\n  style.position = \"absolute\";\n  if (!options.debug) style.visibility = \"hidden\";\n  properties.forEach(prop => {\n    if (isInput && prop === \"lineHeight\") {\n      if (computed.boxSizing === \"border-box\") {\n        const height = Number.parseInt(computed.height);\n        const outerHeight = Number.parseInt(computed.paddingTop) + Number.parseInt(computed.paddingBottom) + Number.parseInt(computed.borderTopWidth) + Number.parseInt(computed.borderBottomWidth);\n        const targetHeight = outerHeight + Number.parseInt(computed.lineHeight);\n        if (height > targetHeight) {\n          style.lineHeight = `${height - outerHeight}px`;\n        } else if (height === targetHeight) {\n          style.lineHeight = computed.lineHeight;\n        } else {\n          style.lineHeight = \"0\";\n        }\n      } else {\n        style.lineHeight = computed.height;\n      }\n    } else {\n      style[prop] = computed[prop];\n    }\n  });\n  if (isFirefox()) {\n    if (element.scrollHeight > Number.parseInt(computed.height)) {\n      style.overflowY = \"scroll\";\n    }\n  } else {\n    style.overflow = \"hidden\";\n  }\n  div.textContent = element.value.slice(0, Math.max(0, position));\n  if (isInput && div.textContent) {\n    div.textContent = div.textContent.replace(/\\s/g, \"\\xA0\");\n  }\n  const span = document.createElement(\"span\");\n  span.textContent = element.value.slice(Math.max(0, position)) || \".\";\n  span.style.position = \"relative\";\n  span.style.left = `${-element.scrollLeft}px`;\n  span.style.top = `${-element.scrollTop}px`;\n  div.appendChild(span);\n  const relativePosition = {\n    top: span.offsetTop + Number.parseInt(computed.borderTopWidth),\n    left: span.offsetLeft + Number.parseInt(computed.borderLeftWidth),\n    height: Number.parseInt(computed.fontSize) * 1.5\n  };\n  if (options.debug) {\n    span.style.backgroundColor = \"#aaa\";\n  } else {\n    document.body.removeChild(div);\n  }\n  if (relativePosition.left >= element.clientWidth) {\n    relativePosition.left = element.clientWidth;\n  }\n  return relativePosition;\n};\nexport { filterOption, getCursorPosition, getMentionCtx };", "map": {"version": 3, "names": ["filterOption", "pattern", "option", "lowerCase", "toLowerCase", "label", "value", "includes", "getMentionCtx", "inputEl", "prefix", "split", "selectionEnd", "inputValue", "prefixArray", "<PERSON><PERSON><PERSON><PERSON>", "splitIndex", "mentionCtx", "i", "char", "end", "slice", "start", "prefixIndex", "getCursorPosition", "element", "options", "debug", "useSelectionEnd", "selectionStart", "position", "properties", "el", "document", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "div", "createElement", "id", "body", "append<PERSON><PERSON><PERSON>", "style", "computed", "window", "getComputedStyle", "isInput", "nodeName", "whiteSpace", "wordWrap", "visibility", "for<PERSON>ach", "prop", "boxSizing", "height", "Number", "parseInt", "outerHeight", "paddingTop", "paddingBottom", "borderTopWidth", "borderBottomWidth", "targetHeight", "lineHeight", "isFirefox", "scrollHeight", "overflowY", "overflow", "textContent", "Math", "max", "replace", "span", "left", "scrollLeft", "top", "scrollTop", "relativePosition", "offsetTop", "offsetLeft", "borderLeftWidth", "fontSize", "backgroundColor", "clientWidth"], "sources": ["../../../../../../packages/components/mention/src/helper.ts"], "sourcesContent": ["import { ensureArray, isFirefox } from '@element-plus/utils'\n\nimport type { MentionCtx, MentionOption } from './types'\n\nexport const filterOption = (\n  pattern: string,\n  option: MentionOption\n): boolean => {\n  const lowerCase = pattern.toLowerCase()\n  const label = option.label || option.value\n  return label.toLowerCase().includes(lowerCase)\n}\n\nexport const getMentionCtx = (\n  inputEl: HTMLInputElement | HTMLTextAreaElement,\n  prefix: string | string[],\n  split: string\n) => {\n  const { selectionEnd } = inputEl\n  if (selectionEnd === null) return\n  const inputValue = inputEl.value\n  const prefixArray = ensureArray(prefix)\n  let splitIndex = -1\n  let mentionCtx: MentionCtx | undefined\n  for (let i = selectionEnd - 1; i >= 0; --i) {\n    const char = inputValue[i]\n    if (char === split || char === '\\n' || char === '\\r') {\n      splitIndex = i\n      continue\n    }\n    if (prefixArray.includes(char)) {\n      const end = splitIndex === -1 ? selectionEnd : splitIndex\n      const pattern = inputValue.slice(i + 1, end)\n      mentionCtx = {\n        pattern,\n        start: i + 1,\n        end,\n        prefix: char,\n        prefixIndex: i,\n        splitIndex,\n        selectionEnd,\n      }\n      break\n    }\n  }\n  return mentionCtx\n}\n\n/**\n * fork from textarea-caret-position\n * https://github.com/component/textarea-caret-position\n * The MIT License (MIT)\n * Copyright (c) 2015 <NAME_EMAIL>\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nexport const getCursorPosition = (\n  element: HTMLInputElement | HTMLTextAreaElement,\n  options = {\n    debug: false,\n    useSelectionEnd: false,\n  }\n) => {\n  const selectionStart =\n    element.selectionStart !== null ? element.selectionStart : 0\n  const selectionEnd = element.selectionEnd !== null ? element.selectionEnd : 0\n  const position = options.useSelectionEnd ? selectionEnd : selectionStart\n  // We'll copy the properties below into the mirror div.\n  // Note that some browsers, such as Firefox, do not concatenate properties\n  // into their shorthand (e.g. padding-top, padding-bottom etc. -> padding),\n  // so we have to list every single property explicitly.\n  const properties: string[] = [\n    'direction', // RTL support\n    'boxSizing',\n    'width', // on Chrome and IE, exclude the scrollbar, so the mirror div wraps exactly as the textarea does\n    'height',\n    'overflowX',\n    'overflowY', // copy the scrollbar for IE\n    'borderTopWidth',\n    'borderRightWidth',\n    'borderBottomWidth',\n    'borderLeftWidth',\n    'borderStyle',\n    'paddingTop',\n    'paddingRight',\n    'paddingBottom',\n    'paddingLeft',\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/font\n    'fontStyle',\n    'fontVariant',\n    'fontWeight',\n    'fontStretch',\n    'fontSize',\n    'fontSizeAdjust',\n    'lineHeight',\n    'fontFamily',\n    'textAlign',\n    'textTransform',\n    'textIndent',\n    'textDecoration', // might not make a difference, but better be safe\n    'letterSpacing',\n    'wordSpacing',\n    'tabSize',\n    'MozTabSize',\n  ]\n\n  if (options.debug) {\n    const el = document.querySelector(\n      '#input-textarea-caret-position-mirror-div'\n    )\n    if (el?.parentNode) el.parentNode.removeChild(el)\n  }\n\n  // The mirror div will replicate the textareas style\n  const div = document.createElement('div')\n  div.id = 'input-textarea-caret-position-mirror-div'\n  document.body.appendChild(div)\n\n  const style = div.style\n  const computed = window.getComputedStyle(element)\n\n  const isInput = element.nodeName === 'INPUT'\n\n  // Default textarea styles\n  style.whiteSpace = isInput ? 'nowrap' : 'pre-wrap'\n  if (!isInput) style.wordWrap = 'break-word' // only for textarea-s\n\n  // Position off-screen\n  style.position = 'absolute' // required to return coordinates properly\n  if (!options.debug) style.visibility = 'hidden' // not 'display: none' because we want rendering\n\n  // Transfer the element's properties to the div\n  properties.forEach((prop) => {\n    if (isInput && prop === 'lineHeight') {\n      // Special case for <input>s because text is rendered centered and line height may be != height\n      if (computed.boxSizing === 'border-box') {\n        const height = Number.parseInt(computed.height as string)\n        const outerHeight =\n          Number.parseInt(computed.paddingTop as string) +\n          Number.parseInt(computed.paddingBottom as string) +\n          Number.parseInt(computed.borderTopWidth as string) +\n          Number.parseInt(computed.borderBottomWidth as string)\n        const targetHeight =\n          outerHeight + Number.parseInt(computed.lineHeight as string)\n        if (height > targetHeight) {\n          style.lineHeight = `${height - outerHeight}px`\n        } else if (height === targetHeight) {\n          style.lineHeight = computed.lineHeight\n        } else {\n          style.lineHeight = '0'\n        }\n      } else {\n        style.lineHeight = computed.height\n      }\n    } else {\n      style[prop as any] = computed[prop as any]\n    }\n  })\n\n  if (isFirefox()) {\n    // Firefox lies about the overflow property for textareas: https://bugzilla.mozilla.org/show_bug.cgi?id=984275\n    if (element.scrollHeight > Number.parseInt(computed.height as string)) {\n      style.overflowY = 'scroll'\n    }\n  } else {\n    style.overflow = 'hidden' // for Chrome to not render a scrollbar; IE keeps overflowY = 'scroll'\n  }\n\n  div.textContent = element.value.slice(0, Math.max(0, position))\n  // The second special handling for input type=\"text\" vs textarea:\n  // spaces need to be replaced with non-breaking spaces - http://stackoverflow.com/a/13402035/1269037\n  if (isInput && div.textContent) {\n    div.textContent = div.textContent.replace(/\\s/g, '\\u00A0')\n  }\n\n  const span = document.createElement('span')\n  // Wrapping must be replicated *exactly*, including when a long word gets\n  // onto the next line, with whitespace at the end of the line before (#7).\n  // The  *only* reliable way to do that is to copy the *entire* rest of the\n  // textareas content into the <span> created at the caret position.\n  // For inputs, just '.' would be enough, but no need to bother.\n  span.textContent = element.value.slice(Math.max(0, position)) || '.' // || because a completely empty faux span doesn't render at all\n  span.style.position = 'relative'\n  span.style.left = `${-element.scrollLeft}px`\n  span.style.top = `${-element.scrollTop}px`\n  div.appendChild(span)\n\n  const relativePosition = {\n    top: span.offsetTop + Number.parseInt(computed.borderTopWidth as string),\n    left: span.offsetLeft + Number.parseInt(computed.borderLeftWidth as string),\n    // We don't use line-height since it may be too large for position. Eg. 34px\n    // for input\n    height: Number.parseInt(computed.fontSize as string) * 1.5,\n  }\n\n  if (options.debug) {\n    span.style.backgroundColor = '#aaa'\n  } else {\n    document.body.removeChild(div)\n  }\n\n  if (relativePosition.left >= element.clientWidth) {\n    relativePosition.left = element.clientWidth\n  }\n  return relativePosition\n}\n"], "mappings": ";;AACY,MAACA,YAAY,GAAGA,CAACC,OAAO,EAAEC,MAAM,KAAK;EAC/C,MAAMC,SAAS,GAAGF,OAAO,CAACG,WAAW,EAAE;EACvC,MAAMC,KAAK,GAAGH,MAAM,CAACG,KAAK,IAAIH,MAAM,CAACI,KAAK;EAC1C,OAAOD,KAAK,CAACD,WAAW,EAAE,CAACG,QAAQ,CAACJ,SAAS,CAAC;AAChD;AACY,MAACK,aAAa,GAAGA,CAACC,OAAO,EAAEC,MAAM,EAAEC,KAAK,KAAK;EACvD,MAAM;IAAEC;EAAY,CAAE,GAAGH,OAAO;EAChC,IAAIG,YAAY,KAAK,IAAI,EACvB;EACF,MAAMC,UAAU,GAAGJ,OAAO,CAACH,KAAK;EAChC,MAAMQ,WAAW,GAAGC,SAAW,CAACL,MAAM,CAAC;EACvC,IAAIM,UAAU,GAAG,CAAC,CAAC;EACnB,IAAIC,UAAU;EACd,KAAK,IAAIC,CAAC,GAAGN,YAAY,GAAG,CAAC,EAAEM,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;IAC1C,MAAMC,IAAI,GAAGN,UAAU,CAACK,CAAC,CAAC;IAC1B,IAAIC,IAAI,KAAKR,KAAK,IAAIQ,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,IAAI,EAAE;MACpDH,UAAU,GAAGE,CAAC;MACd;IACN;IACI,IAAIJ,WAAW,CAACP,QAAQ,CAACY,IAAI,CAAC,EAAE;MAC9B,MAAMC,GAAG,GAAGJ,UAAU,KAAK,CAAC,CAAC,GAAGJ,YAAY,GAAGI,UAAU;MACzD,MAAMf,OAAO,GAAGY,UAAU,CAACQ,KAAK,CAACH,CAAC,GAAG,CAAC,EAAEE,GAAG,CAAC;MAC5CH,UAAU,GAAG;QACXhB,OAAO;QACPqB,KAAK,EAAEJ,CAAC,GAAG,CAAC;QACZE,GAAG;QACHV,MAAM,EAAES,IAAI;QACZI,WAAW,EAAEL,CAAC;QACdF,UAAU;QACVJ;MACR,CAAO;MACD;IACN;EACA;EACE,OAAOK,UAAU;AACnB;AACY,MAACO,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,OAAO,GAAG;EACnDC,KAAK,EAAE,KAAK;EACZC,eAAe,EAAE;AACnB,CAAC,KAAK;EACJ,MAAMC,cAAc,GAAGJ,OAAO,CAACI,cAAc,KAAK,IAAI,GAAGJ,OAAO,CAACI,cAAc,GAAG,CAAC;EACnF,MAAMjB,YAAY,GAAGa,OAAO,CAACb,YAAY,KAAK,IAAI,GAAGa,OAAO,CAACb,YAAY,GAAG,CAAC;EAC7E,MAAMkB,QAAQ,GAAGJ,OAAO,CAACE,eAAe,GAAGhB,YAAY,GAAGiB,cAAc;EACxE,MAAME,UAAU,GAAG,CACjB,WAAW,EACX,WAAW,EACX,OAAO,EACP,QAAQ,EACR,WAAW,EACX,WAAW,EACX,gBAAgB,EAChB,kBAAkB,EAClB,mBAAmB,EACnB,iBAAiB,EACjB,aAAa,EACb,YAAY,EACZ,cAAc,EACd,eAAe,EACf,aAAa,EACb,WAAW,EACX,aAAa,EACb,YAAY,EACZ,aAAa,EACb,UAAU,EACV,gBAAgB,EAChB,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,eAAe,EACf,YAAY,EACZ,gBAAgB,EAChB,eAAe,EACf,aAAa,EACb,SAAS,EACT,YAAY,CACb;EACD,IAAIL,OAAO,CAACC,KAAK,EAAE;IACjB,MAAMK,EAAE,GAAGC,QAAQ,CAACC,aAAa,CAAC,2CAA2C,CAAC;IAC9E,IAAIF,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,UAAU,EACrCH,EAAE,CAACG,UAAU,CAACC,WAAW,CAACJ,EAAE,CAAC;EACnC;EACE,MAAMK,GAAG,GAAGJ,QAAQ,CAACK,aAAa,CAAC,KAAK,CAAC;EACzCD,GAAG,CAACE,EAAE,GAAG,0CAA0C;EACnDN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACJ,GAAG,CAAC;EAC9B,MAAMK,KAAK,GAAGL,GAAG,CAACK,KAAK;EACvB,MAAMC,QAAQ,GAAGC,MAAM,CAACC,gBAAgB,CAACpB,OAAO,CAAC;EACjD,MAAMqB,OAAO,GAAGrB,OAAO,CAACsB,QAAQ,KAAK,OAAO;EAC5CL,KAAK,CAACM,UAAU,GAAGF,OAAO,GAAG,QAAQ,GAAG,UAAU;EAClD,IAAI,CAACA,OAAO,EACVJ,KAAK,CAACO,QAAQ,GAAG,YAAY;EAC/BP,KAAK,CAACZ,QAAQ,GAAG,UAAU;EAC3B,IAAI,CAACJ,OAAO,CAACC,KAAK,EAChBe,KAAK,CAACQ,UAAU,GAAG,QAAQ;EAC7BnB,UAAU,CAACoB,OAAO,CAAEC,IAAI,IAAK;IAC3B,IAAIN,OAAO,IAAIM,IAAI,KAAK,YAAY,EAAE;MACpC,IAAIT,QAAQ,CAACU,SAAS,KAAK,YAAY,EAAE;QACvC,MAAMC,MAAM,GAAGC,MAAM,CAACC,QAAQ,CAACb,QAAQ,CAACW,MAAM,CAAC;QAC/C,MAAMG,WAAW,GAAGF,MAAM,CAACC,QAAQ,CAACb,QAAQ,CAACe,UAAU,CAAC,GAAGH,MAAM,CAACC,QAAQ,CAACb,QAAQ,CAACgB,aAAa,CAAC,GAAGJ,MAAM,CAACC,QAAQ,CAACb,QAAQ,CAACiB,cAAc,CAAC,GAAGL,MAAM,CAACC,QAAQ,CAACb,QAAQ,CAACkB,iBAAiB,CAAC;QAC3L,MAAMC,YAAY,GAAGL,WAAW,GAAGF,MAAM,CAACC,QAAQ,CAACb,QAAQ,CAACoB,UAAU,CAAC;QACvE,IAAIT,MAAM,GAAGQ,YAAY,EAAE;UACzBpB,KAAK,CAACqB,UAAU,GAAG,GAAGT,MAAM,GAAGG,WAAW,IAAI;QACxD,CAAS,MAAM,IAAIH,MAAM,KAAKQ,YAAY,EAAE;UAClCpB,KAAK,CAACqB,UAAU,GAAGpB,QAAQ,CAACoB,UAAU;QAChD,CAAS,MAAM;UACLrB,KAAK,CAACqB,UAAU,GAAG,GAAG;QAChC;MACA,CAAO,MAAM;QACLrB,KAAK,CAACqB,UAAU,GAAGpB,QAAQ,CAACW,MAAM;MAC1C;IACA,CAAK,MAAM;MACLZ,KAAK,CAACU,IAAI,CAAC,GAAGT,QAAQ,CAACS,IAAI,CAAC;IAClC;EACA,CAAG,CAAC;EACF,IAAIY,SAAS,EAAE,EAAE;IACf,IAAIvC,OAAO,CAACwC,YAAY,GAAGV,MAAM,CAACC,QAAQ,CAACb,QAAQ,CAACW,MAAM,CAAC,EAAE;MAC3DZ,KAAK,CAACwB,SAAS,GAAG,QAAQ;IAChC;EACA,CAAG,MAAM;IACLxB,KAAK,CAACyB,QAAQ,GAAG,QAAQ;EAC7B;EACE9B,GAAG,CAAC+B,WAAW,GAAG3C,OAAO,CAACnB,KAAK,CAACe,KAAK,CAAC,CAAC,EAAEgD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAExC,QAAQ,CAAC,CAAC;EAC/D,IAAIgB,OAAO,IAAIT,GAAG,CAAC+B,WAAW,EAAE;IAC9B/B,GAAG,CAAC+B,WAAW,GAAG/B,GAAG,CAAC+B,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;EAC5D;EACE,MAAMC,IAAI,GAAGvC,QAAQ,CAACK,aAAa,CAAC,MAAM,CAAC;EAC3CkC,IAAI,CAACJ,WAAW,GAAG3C,OAAO,CAACnB,KAAK,CAACe,KAAK,CAACgD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAExC,QAAQ,CAAC,CAAC,IAAI,GAAG;EACpE0C,IAAI,CAAC9B,KAAK,CAACZ,QAAQ,GAAG,UAAU;EAChC0C,IAAI,CAAC9B,KAAK,CAAC+B,IAAI,GAAG,GAAG,CAAChD,OAAO,CAACiD,UAAU,IAAI;EAC5CF,IAAI,CAAC9B,KAAK,CAACiC,GAAG,GAAG,GAAG,CAAClD,OAAO,CAACmD,SAAS,IAAI;EAC1CvC,GAAG,CAACI,WAAW,CAAC+B,IAAI,CAAC;EACrB,MAAMK,gBAAgB,GAAG;IACvBF,GAAG,EAAEH,IAAI,CAACM,SAAS,GAAGvB,MAAM,CAACC,QAAQ,CAACb,QAAQ,CAACiB,cAAc,CAAC;IAC9Da,IAAI,EAAED,IAAI,CAACO,UAAU,GAAGxB,MAAM,CAACC,QAAQ,CAACb,QAAQ,CAACqC,eAAe,CAAC;IACjE1B,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACb,QAAQ,CAACsC,QAAQ,CAAC,GAAG;EACjD,CAAG;EACD,IAAIvD,OAAO,CAACC,KAAK,EAAE;IACjB6C,IAAI,CAAC9B,KAAK,CAACwC,eAAe,GAAG,MAAM;EACvC,CAAG,MAAM;IACLjD,QAAQ,CAACO,IAAI,CAACJ,WAAW,CAACC,GAAG,CAAC;EAClC;EACE,IAAIwC,gBAAgB,CAACJ,IAAI,IAAIhD,OAAO,CAAC0D,WAAW,EAAE;IAChDN,gBAAgB,CAACJ,IAAI,GAAGhD,OAAO,CAAC0D,WAAW;EAC/C;EACE,OAAON,gBAAgB;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}