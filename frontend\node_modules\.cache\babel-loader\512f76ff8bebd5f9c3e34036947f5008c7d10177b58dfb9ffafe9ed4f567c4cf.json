{"ast": null, "code": "import { shallowRef, ref } from 'vue';\nimport { getStyle, setStyle } from '../../../../utils/dom/style.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nfunction useDragTag({\n  wrapperRef,\n  handleDragged,\n  afterDragged\n}) {\n  const ns = useNamespace(\"input-tag\");\n  const dropIndicatorRef = shallowRef();\n  const showDropIndicator = ref(false);\n  let draggingIndex;\n  let draggingTag;\n  let dropIndex;\n  let dropType;\n  function getTagClassName(index) {\n    return `.${ns.e(\"inner\")} .${ns.namespace.value}-tag:nth-child(${index + 1})`;\n  }\n  function handleDragStart(event, index) {\n    draggingIndex = index;\n    draggingTag = wrapperRef.value.querySelector(getTagClassName(index));\n    if (draggingTag) {\n      draggingTag.style.opacity = \"0.5\";\n    }\n    event.dataTransfer.effectAllowed = \"move\";\n  }\n  function handleDragOver(event, index) {\n    dropIndex = index;\n    event.preventDefault();\n    event.dataTransfer.dropEffect = \"move\";\n    if (isUndefined(draggingIndex) || draggingIndex === index) {\n      showDropIndicator.value = false;\n      return;\n    }\n    const dropPosition = wrapperRef.value.querySelector(getTagClassName(index)).getBoundingClientRect();\n    const dropPrev = !(draggingIndex + 1 === index);\n    const dropNext = !(draggingIndex - 1 === index);\n    const distance = event.clientX - dropPosition.left;\n    const prevPercent = dropPrev ? dropNext ? 0.5 : 1 : -1;\n    const nextPercent = dropNext ? dropPrev ? 0.5 : 0 : 1;\n    if (distance <= dropPosition.width * prevPercent) {\n      dropType = \"before\";\n    } else if (distance > dropPosition.width * nextPercent) {\n      dropType = \"after\";\n    } else {\n      dropType = void 0;\n    }\n    const innerEl = wrapperRef.value.querySelector(`.${ns.e(\"inner\")}`);\n    const innerPosition = innerEl.getBoundingClientRect();\n    const gap = Number.parseFloat(getStyle(innerEl, \"gap\")) / 2;\n    const indicatorTop = dropPosition.top - innerPosition.top;\n    let indicatorLeft = -9999;\n    if (dropType === \"before\") {\n      indicatorLeft = Math.max(dropPosition.left - innerPosition.left - gap, Math.floor(-gap / 2));\n    } else if (dropType === \"after\") {\n      const left = dropPosition.right - innerPosition.left;\n      indicatorLeft = left + (innerPosition.width === left ? Math.floor(gap / 2) : gap);\n    }\n    setStyle(dropIndicatorRef.value, {\n      top: `${indicatorTop}px`,\n      left: `${indicatorLeft}px`\n    });\n    showDropIndicator.value = !!dropType;\n  }\n  function handleDragEnd(event) {\n    event.preventDefault();\n    if (draggingTag) {\n      draggingTag.style.opacity = \"\";\n    }\n    if (dropType && !isUndefined(draggingIndex) && !isUndefined(dropIndex) && draggingIndex !== dropIndex) {\n      handleDragged(draggingIndex, dropIndex, dropType);\n    }\n    showDropIndicator.value = false;\n    draggingIndex = void 0;\n    draggingTag = null;\n    dropIndex = void 0;\n    dropType = void 0;\n    afterDragged == null ? void 0 : afterDragged();\n  }\n  return {\n    dropIndicatorRef,\n    showDropIndicator,\n    handleDragStart,\n    handleDragOver,\n    handleDragEnd\n  };\n}\nexport { useDragTag };", "map": {"version": 3, "names": ["useDragTag", "wrapperRef", "handleDragged", "afterDragged", "ns", "useNamespace", "dropIndicatorRef", "shallowRef", "showDropIndicator", "ref", "draggingIndex", "draggingTag", "dropIndex", "dropType", "getTagClassName", "index", "e", "namespace", "value", "handleDragStart", "event", "querySelector", "style", "opacity", "dataTransfer", "effectAllowed", "handleDragOver", "preventDefault", "dropEffect", "isUndefined", "dropPosition", "getBoundingClientRect", "dropPrev", "dropNext", "distance", "clientX", "left", "prevPercent", "nextPercent", "width", "innerEl", "innerPosition", "gap", "Number", "parseFloat", "getStyle", "indicatorTop", "top", "indicatorLeft", "Math", "max", "floor", "right", "setStyle", "handleDragEnd"], "sources": ["../../../../../../../packages/components/input-tag/src/composables/use-drag-tag.ts"], "sourcesContent": ["import { type ShallowRef, ref, shallowRef } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { getStyle, isUndefined, setStyle } from '@element-plus/utils'\n\ntype DropType = 'before' | 'after'\n\ninterface UseDragTagOptions {\n  wrapperRef: ShallowRef<HTMLElement | undefined>\n  handleDragged: (\n    draggingIndex: number,\n    dropIndex: number,\n    type: DropType\n  ) => void\n  afterDragged?: () => void\n}\n\nexport function useDragTag({\n  wrapperRef,\n  handleDragged,\n  afterDragged,\n}: UseDragTagOptions) {\n  const ns = useNamespace('input-tag')\n  const dropIndicatorRef = shallowRef<HTMLElement>()\n  const showDropIndicator = ref(false)\n\n  let draggingIndex: number | undefined\n  let draggingTag: HTMLElement | null\n  let dropIndex: number | undefined\n  let dropType: DropType | undefined\n\n  function getTagClassName(index: number) {\n    return `.${ns.e('inner')} .${ns.namespace.value}-tag:nth-child(${\n      index + 1\n    })`\n  }\n\n  function handleDragStart(event: DragEvent, index: number) {\n    draggingIndex = index\n    draggingTag = wrapperRef.value!.querySelector<HTMLElement>(\n      getTagClassName(index)\n    )\n\n    if (draggingTag) {\n      draggingTag.style.opacity = '0.5'\n    }\n    event.dataTransfer!.effectAllowed = 'move'\n  }\n\n  function handleDragOver(event: DragEvent, index: number) {\n    dropIndex = index\n    event.preventDefault()\n    event.dataTransfer!.dropEffect = 'move'\n\n    if (isUndefined(draggingIndex) || draggingIndex === index) {\n      showDropIndicator.value = false\n      return\n    }\n\n    const dropPosition = wrapperRef\n      .value!.querySelector<HTMLElement>(getTagClassName(index))!\n      .getBoundingClientRect()\n    const dropPrev = !(draggingIndex + 1 === index)\n    const dropNext = !(draggingIndex - 1 === index)\n    const distance = event.clientX - dropPosition.left\n    const prevPercent = dropPrev ? (dropNext ? 0.5 : 1) : -1\n    const nextPercent = dropNext ? (dropPrev ? 0.5 : 0) : 1\n\n    if (distance <= dropPosition.width * prevPercent) {\n      dropType = 'before'\n    } else if (distance > dropPosition.width * nextPercent) {\n      dropType = 'after'\n    } else {\n      dropType = undefined\n    }\n\n    const innerEl = wrapperRef.value!.querySelector<HTMLElement>(\n      `.${ns.e('inner')}`\n    )!\n    const innerPosition = innerEl.getBoundingClientRect()\n    const gap = Number.parseFloat(getStyle(innerEl, 'gap')) / 2\n\n    const indicatorTop = dropPosition.top - innerPosition.top\n    let indicatorLeft = -9999\n\n    if (dropType === 'before') {\n      indicatorLeft = Math.max(\n        dropPosition.left - innerPosition.left - gap,\n        Math.floor(-gap / 2)\n      )\n    } else if (dropType === 'after') {\n      const left = dropPosition.right - innerPosition.left\n      indicatorLeft =\n        left + (innerPosition.width === left ? Math.floor(gap / 2) : gap)\n    }\n\n    setStyle(dropIndicatorRef.value!, {\n      top: `${indicatorTop}px`,\n      left: `${indicatorLeft}px`,\n    })\n    showDropIndicator.value = !!dropType\n  }\n\n  function handleDragEnd(event: DragEvent) {\n    event.preventDefault()\n\n    if (draggingTag) {\n      draggingTag.style.opacity = ''\n    }\n\n    if (\n      dropType &&\n      !isUndefined(draggingIndex) &&\n      !isUndefined(dropIndex) &&\n      draggingIndex !== dropIndex\n    ) {\n      handleDragged(draggingIndex, dropIndex, dropType)\n    }\n\n    showDropIndicator.value = false\n    draggingIndex = undefined\n    draggingTag = null\n    dropIndex = undefined\n    dropType = undefined\n    afterDragged?.()\n  }\n\n  return {\n    dropIndicatorRef,\n    showDropIndicator,\n    handleDragStart,\n    handleDragOver,\n    handleDragEnd,\n  }\n}\n"], "mappings": ";;;;AAGO,SAASA,UAAUA,CAAC;EACzBC,UAAU;EACVC,aAAa;EACbC;AACF,CAAC,EAAE;EACD,MAAMC,EAAE,GAAGC,YAAY,CAAC,WAAW,CAAC;EACpC,MAAMC,gBAAgB,GAAGC,UAAU,EAAE;EACrC,MAAMC,iBAAiB,GAAGC,GAAG,CAAC,KAAK,CAAC;EACpC,IAAIC,aAAa;EACjB,IAAIC,WAAW;EACf,IAAIC,SAAS;EACb,IAAIC,QAAQ;EACZ,SAASC,eAAeA,CAACC,KAAK,EAAE;IAC9B,OAAO,IAAIX,EAAE,CAACY,CAAC,CAAC,OAAO,CAAC,KAAKZ,EAAE,CAACa,SAAS,CAACC,KAAK,kBAAkBH,KAAK,GAAG,CAAC,GAAG;EACjF;EACE,SAASI,eAAeA,CAACC,KAAK,EAAEL,KAAK,EAAE;IACrCL,aAAa,GAAGK,KAAK;IACrBJ,WAAW,GAAGV,UAAU,CAACiB,KAAK,CAACG,aAAa,CAACP,eAAe,CAACC,KAAK,CAAC,CAAC;IACpE,IAAIJ,WAAW,EAAE;MACfA,WAAW,CAACW,KAAK,CAACC,OAAO,GAAG,KAAK;IACvC;IACIH,KAAK,CAACI,YAAY,CAACC,aAAa,GAAG,MAAM;EAC7C;EACE,SAASC,cAAcA,CAACN,KAAK,EAAEL,KAAK,EAAE;IACpCH,SAAS,GAAGG,KAAK;IACjBK,KAAK,CAACO,cAAc,EAAE;IACtBP,KAAK,CAACI,YAAY,CAACI,UAAU,GAAG,MAAM;IACtC,IAAIC,WAAW,CAACnB,aAAa,CAAC,IAAIA,aAAa,KAAKK,KAAK,EAAE;MACzDP,iBAAiB,CAACU,KAAK,GAAG,KAAK;MAC/B;IACN;IACI,MAAMY,YAAY,GAAG7B,UAAU,CAACiB,KAAK,CAACG,aAAa,CAACP,eAAe,CAACC,KAAK,CAAC,CAAC,CAACgB,qBAAqB,EAAE;IACnG,MAAMC,QAAQ,GAAG,EAAEtB,aAAa,GAAG,CAAC,KAAKK,KAAK,CAAC;IAC/C,MAAMkB,QAAQ,GAAG,EAAEvB,aAAa,GAAG,CAAC,KAAKK,KAAK,CAAC;IAC/C,MAAMmB,QAAQ,GAAGd,KAAK,CAACe,OAAO,GAAGL,YAAY,CAACM,IAAI;IAClD,MAAMC,WAAW,GAAGL,QAAQ,GAAGC,QAAQ,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACtD,MAAMK,WAAW,GAAGL,QAAQ,GAAGD,QAAQ,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;IACrD,IAAIE,QAAQ,IAAIJ,YAAY,CAACS,KAAK,GAAGF,WAAW,EAAE;MAChDxB,QAAQ,GAAG,QAAQ;IACzB,CAAK,MAAM,IAAIqB,QAAQ,GAAGJ,YAAY,CAACS,KAAK,GAAGD,WAAW,EAAE;MACtDzB,QAAQ,GAAG,OAAO;IACxB,CAAK,MAAM;MACLA,QAAQ,GAAG,KAAK,CAAC;IACvB;IACI,MAAM2B,OAAO,GAAGvC,UAAU,CAACiB,KAAK,CAACG,aAAa,CAAC,IAAIjB,EAAE,CAACY,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;IACnE,MAAMyB,aAAa,GAAGD,OAAO,CAACT,qBAAqB,EAAE;IACrD,MAAMW,GAAG,GAAGC,MAAM,CAACC,UAAU,CAACC,QAAQ,CAACL,OAAO,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC;IAC3D,MAAMM,YAAY,GAAGhB,YAAY,CAACiB,GAAG,GAAGN,aAAa,CAACM,GAAG;IACzD,IAAIC,aAAa,GAAG,CAAC,IAAI;IACzB,IAAInC,QAAQ,KAAK,QAAQ,EAAE;MACzBmC,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACpB,YAAY,CAACM,IAAI,GAAGK,aAAa,CAACL,IAAI,GAAGM,GAAG,EAAEO,IAAI,CAACE,KAAK,CAAC,CAACT,GAAG,GAAG,CAAC,CAAC,CAAC;IAClG,CAAK,MAAM,IAAI7B,QAAQ,KAAK,OAAO,EAAE;MAC/B,MAAMuB,IAAI,GAAGN,YAAY,CAACsB,KAAK,GAAGX,aAAa,CAACL,IAAI;MACpDY,aAAa,GAAGZ,IAAI,IAAIK,aAAa,CAACF,KAAK,KAAKH,IAAI,GAAGa,IAAI,CAACE,KAAK,CAACT,GAAG,GAAG,CAAC,CAAC,GAAGA,GAAG,CAAC;IACvF;IACIW,QAAQ,CAAC/C,gBAAgB,CAACY,KAAK,EAAE;MAC/B6B,GAAG,EAAE,GAAGD,YAAY,IAAI;MACxBV,IAAI,EAAE,GAAGY,aAAa;IAC5B,CAAK,CAAC;IACFxC,iBAAiB,CAACU,KAAK,GAAG,CAAC,CAACL,QAAQ;EACxC;EACE,SAASyC,aAAaA,CAAClC,KAAK,EAAE;IAC5BA,KAAK,CAACO,cAAc,EAAE;IACtB,IAAIhB,WAAW,EAAE;MACfA,WAAW,CAACW,KAAK,CAACC,OAAO,GAAG,EAAE;IACpC;IACI,IAAIV,QAAQ,IAAI,CAACgB,WAAW,CAACnB,aAAa,CAAC,IAAI,CAACmB,WAAW,CAACjB,SAAS,CAAC,IAAIF,aAAa,KAAKE,SAAS,EAAE;MACrGV,aAAa,CAACQ,aAAa,EAAEE,SAAS,EAAEC,QAAQ,CAAC;IACvD;IACIL,iBAAiB,CAACU,KAAK,GAAG,KAAK;IAC/BR,aAAa,GAAG,KAAK,CAAC;IACtBC,WAAW,GAAG,IAAI;IAClBC,SAAS,GAAG,KAAK,CAAC;IAClBC,QAAQ,GAAG,KAAK,CAAC;IACjBV,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,EAAE;EAClD;EACE,OAAO;IACLG,gBAAgB;IAChBE,iBAAiB;IACjBW,eAAe;IACfO,cAAc;IACd4B;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}