{"ast": null, "code": "import { dialogProps, dialogEmits } from '../../dialog/src/dialog.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst drawerProps = buildProps({\n  ...dialogProps,\n  direction: {\n    type: String,\n    default: \"rtl\",\n    values: [\"ltr\", \"rtl\", \"ttb\", \"btt\"]\n  },\n  size: {\n    type: [String, Number],\n    default: \"30%\"\n  },\n  withHeader: {\n    type: Boolean,\n    default: true\n  },\n  modalFade: {\n    type: Boolean,\n    default: true\n  },\n  headerAriaLevel: {\n    type: String,\n    default: \"2\"\n  }\n});\nconst drawerEmits = dialogEmits;\nexport { drawerEmits, drawerProps };", "map": {"version": 3, "names": ["drawerProps", "buildProps", "dialogProps", "direction", "type", "String", "default", "values", "size", "Number", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "modalFade", "headerAriaLevel", "drawerEmits", "dialogEmits"], "sources": ["../../../../../../packages/components/drawer/src/drawer.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { dialogEmits, dialogProps } from '@element-plus/components/dialog'\nimport type { ExtractPropTypes } from 'vue'\n\nexport const drawerProps = buildProps({\n  ...dialogProps,\n  direction: {\n    type: String,\n    default: 'rtl',\n    values: ['ltr', 'rtl', 'ttb', 'btt'],\n  },\n  size: {\n    type: [String, Number],\n    default: '30%',\n  },\n  withHeader: {\n    type: Boolean,\n    default: true,\n  },\n  modalFade: {\n    type: Boolean,\n    default: true,\n  },\n  headerAriaLevel: {\n    type: String,\n    default: '2',\n  },\n} as const)\n\nexport type DrawerProps = ExtractPropTypes<typeof drawerProps>\n\nexport const drawerEmits = dialogEmits\n"], "mappings": ";;AAEY,MAACA,WAAW,GAAGC,UAAU,CAAC;EACpC,GAAGC,WAAW;EACdC,SAAS,EAAE;IACTC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;EACvC,CAAG;EACDC,IAAI,EAAE;IACJJ,IAAI,EAAE,CAACC,MAAM,EAAEI,MAAM,CAAC;IACtBH,OAAO,EAAE;EACb,CAAG;EACDI,UAAU,EAAE;IACVN,IAAI,EAAEO,OAAO;IACbL,OAAO,EAAE;EACb,CAAG;EACDM,SAAS,EAAE;IACTR,IAAI,EAAEO,OAAO;IACbL,OAAO,EAAE;EACb,CAAG;EACDO,eAAe,EAAE;IACfT,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb;AACA,CAAC;AACW,MAACQ,WAAW,GAAGC,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}