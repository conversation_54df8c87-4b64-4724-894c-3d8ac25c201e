{"ast": null, "code": "import baseDifference from './_baseDifference.js';\nimport baseFlatten from './_baseFlatten.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.difference` except that it accepts `iteratee` which\n * is invoked for each element of `array` and `values` to generate the criterion\n * by which they're compared. The order and references of result values are\n * determined by the first array. The iteratee is invoked with one argument:\n * (value).\n *\n * **Note:** Unlike `_.pullAllBy`, this method returns a new array.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {...Array} [values] The values to exclude.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n * @example\n *\n * _.differenceBy([2.1, 1.2], [2.3, 3.4], Math.floor);\n * // => [1.2]\n *\n * // The `_.property` iteratee shorthand.\n * _.differenceBy([{ 'x': 2 }, { 'x': 1 }], [{ 'x': 1 }], 'x');\n * // => [{ 'x': 2 }]\n */\nvar differenceBy = baseRest(function (array, values) {\n  var iteratee = last(values);\n  if (isArrayLikeObject(iteratee)) {\n    iteratee = undefined;\n  }\n  return isArrayLikeObject(array) ? baseDifference(array, baseFlatten(values, 1, isArrayLikeObject, true), baseIteratee(iteratee, 2)) : [];\n});\nexport default differenceBy;", "map": {"version": 3, "names": ["baseDifference", "baseFlatten", "baseIteratee", "baseRest", "isArrayLikeObject", "last", "differenceBy", "array", "values", "iteratee", "undefined"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/differenceBy.js"], "sourcesContent": ["import baseDifference from './_baseDifference.js';\nimport baseFlatten from './_baseFlatten.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.difference` except that it accepts `iteratee` which\n * is invoked for each element of `array` and `values` to generate the criterion\n * by which they're compared. The order and references of result values are\n * determined by the first array. The iteratee is invoked with one argument:\n * (value).\n *\n * **Note:** Unlike `_.pullAllBy`, this method returns a new array.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {...Array} [values] The values to exclude.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n * @example\n *\n * _.differenceBy([2.1, 1.2], [2.3, 3.4], Math.floor);\n * // => [1.2]\n *\n * // The `_.property` iteratee shorthand.\n * _.differenceBy([{ 'x': 2 }, { 'x': 1 }], [{ 'x': 1 }], 'x');\n * // => [{ 'x': 2 }]\n */\nvar differenceBy = baseRest(function(array, values) {\n  var iteratee = last(values);\n  if (isArrayLikeObject(iteratee)) {\n    iteratee = undefined;\n  }\n  return isArrayLikeObject(array)\n    ? baseDifference(array, baseFlatten(values, 1, isArrayLikeObject, true), baseIteratee(iteratee, 2))\n    : [];\n});\n\nexport default differenceBy;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;AACjD,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAGH,QAAQ,CAAC,UAASI,KAAK,EAAEC,MAAM,EAAE;EAClD,IAAIC,QAAQ,GAAGJ,IAAI,CAACG,MAAM,CAAC;EAC3B,IAAIJ,iBAAiB,CAACK,QAAQ,CAAC,EAAE;IAC/BA,QAAQ,GAAGC,SAAS;EACtB;EACA,OAAON,iBAAiB,CAACG,KAAK,CAAC,GAC3BP,cAAc,CAACO,KAAK,EAAEN,WAAW,CAACO,MAAM,EAAE,CAAC,EAAEJ,iBAAiB,EAAE,IAAI,CAAC,EAAEF,YAAY,CAACO,QAAQ,EAAE,CAAC,CAAC,CAAC,GACjG,EAAE;AACR,CAAC,CAAC;AAEF,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}