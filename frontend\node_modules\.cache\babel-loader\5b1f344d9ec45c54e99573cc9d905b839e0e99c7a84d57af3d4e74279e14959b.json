{"ast": null, "code": "const parseTime = time => {\n  const values = (time || \"\").split(\":\");\n  if (values.length >= 2) {\n    let hours = Number.parseInt(values[0], 10);\n    const minutes = Number.parseInt(values[1], 10);\n    const timeUpper = time.toUpperCase();\n    if (timeUpper.includes(\"AM\") && hours === 12) {\n      hours = 0;\n    } else if (timeUpper.includes(\"PM\") && hours !== 12) {\n      hours += 12;\n    }\n    return {\n      hours,\n      minutes\n    };\n  }\n  return null;\n};\nconst compareTime = (time1, time2) => {\n  const value1 = parseTime(time1);\n  if (!value1) return -1;\n  const value2 = parseTime(time2);\n  if (!value2) return -1;\n  const minutes1 = value1.minutes + value1.hours * 60;\n  const minutes2 = value2.minutes + value2.hours * 60;\n  if (minutes1 === minutes2) {\n    return 0;\n  }\n  return minutes1 > minutes2 ? 1 : -1;\n};\nconst padTime = time => {\n  return `${time}`.padStart(2, \"0\");\n};\nconst formatTime = time => {\n  return `${padTime(time.hours)}:${padTime(time.minutes)}`;\n};\nconst nextTime = (time, step) => {\n  const timeValue = parseTime(time);\n  if (!timeValue) return \"\";\n  const stepValue = parseTime(step);\n  if (!stepValue) return \"\";\n  const next = {\n    hours: timeValue.hours,\n    minutes: timeValue.minutes\n  };\n  next.minutes += stepValue.minutes;\n  next.hours += stepValue.hours;\n  next.hours += Math.floor(next.minutes / 60);\n  next.minutes = next.minutes % 60;\n  return formatTime(next);\n};\nexport { compareTime, formatTime, nextTime, padTime, parseTime };", "map": {"version": 3, "names": ["parseTime", "time", "values", "split", "length", "hours", "Number", "parseInt", "minutes", "timeUpper", "toUpperCase", "includes", "compareTime", "time1", "time2", "value1", "value2", "minutes1", "minutes2", "padTime", "padStart", "formatTime", "nextTime", "step", "timeValue", "<PERSON><PERSON><PERSON><PERSON>", "next", "Math", "floor"], "sources": ["../../../../../../packages/components/time-select/src/utils.ts"], "sourcesContent": ["interface Time {\n  hours: number\n  minutes: number\n}\n\nexport const parseTime = (time: string): null | Time => {\n  const values = (time || '').split(':')\n  if (values.length >= 2) {\n    let hours = Number.parseInt(values[0], 10)\n    const minutes = Number.parseInt(values[1], 10)\n    const timeUpper = time.toUpperCase()\n    if (timeUpper.includes('AM') && hours === 12) {\n      hours = 0\n    } else if (timeUpper.includes('PM') && hours !== 12) {\n      hours += 12\n    }\n    return {\n      hours,\n      minutes,\n    }\n  }\n\n  return null\n}\n\nexport const compareTime = (time1: string, time2: string): number => {\n  const value1 = parseTime(time1)\n  if (!value1) return -1\n  const value2 = parseTime(time2)\n  if (!value2) return -1\n  const minutes1 = value1.minutes + value1.hours * 60\n  const minutes2 = value2.minutes + value2.hours * 60\n  if (minutes1 === minutes2) {\n    return 0\n  }\n  return minutes1 > minutes2 ? 1 : -1\n}\n\nexport const padTime = (time: number | string) => {\n  return `${time}`.padStart(2, '0')\n}\nexport const formatTime = (time: Time): string => {\n  return `${padTime(time.hours)}:${padTime(time.minutes)}`\n}\n\nexport const nextTime = (time: string, step: string): string => {\n  const timeValue = parseTime(time)\n  if (!timeValue) return ''\n\n  const stepValue = parseTime(step)\n  if (!stepValue) return ''\n\n  const next = {\n    hours: timeValue.hours,\n    minutes: timeValue.minutes,\n  }\n  next.minutes += stepValue.minutes\n  next.hours += stepValue.hours\n  next.hours += Math.floor(next.minutes / 60)\n  next.minutes = next.minutes % 60\n  return formatTime(next)\n}\n"], "mappings": "AAAY,MAACA,SAAS,GAAIC,IAAI,IAAK;EACjC,MAAMC,MAAM,GAAG,CAACD,IAAI,IAAI,EAAE,EAAEE,KAAK,CAAC,GAAG,CAAC;EACtC,IAAID,MAAM,CAACE,MAAM,IAAI,CAAC,EAAE;IACtB,IAAIC,KAAK,GAAGC,MAAM,CAACC,QAAQ,CAACL,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1C,MAAMM,OAAO,GAAGF,MAAM,CAACC,QAAQ,CAACL,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC9C,MAAMO,SAAS,GAAGR,IAAI,CAACS,WAAW,EAAE;IACpC,IAAID,SAAS,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIN,KAAK,KAAK,EAAE,EAAE;MAC5CA,KAAK,GAAG,CAAC;IACf,CAAK,MAAM,IAAII,SAAS,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIN,KAAK,KAAK,EAAE,EAAE;MACnDA,KAAK,IAAI,EAAE;IACjB;IACI,OAAO;MACLA,KAAK;MACLG;IACN,CAAK;EACL;EACE,OAAO,IAAI;AACb;AACY,MAACI,WAAW,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EAC3C,MAAMC,MAAM,GAAGf,SAAS,CAACa,KAAK,CAAC;EAC/B,IAAI,CAACE,MAAM,EACT,OAAO,CAAC,CAAC;EACX,MAAMC,MAAM,GAAGhB,SAAS,CAACc,KAAK,CAAC;EAC/B,IAAI,CAACE,MAAM,EACT,OAAO,CAAC,CAAC;EACX,MAAMC,QAAQ,GAAGF,MAAM,CAACP,OAAO,GAAGO,MAAM,CAACV,KAAK,GAAG,EAAE;EACnD,MAAMa,QAAQ,GAAGF,MAAM,CAACR,OAAO,GAAGQ,MAAM,CAACX,KAAK,GAAG,EAAE;EACnD,IAAIY,QAAQ,KAAKC,QAAQ,EAAE;IACzB,OAAO,CAAC;EACZ;EACE,OAAOD,QAAQ,GAAGC,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;AACrC;AACY,MAACC,OAAO,GAAIlB,IAAI,IAAK;EAC/B,OAAO,GAAGA,IAAI,EAAE,CAACmB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;AACnC;AACY,MAACC,UAAU,GAAIpB,IAAI,IAAK;EAClC,OAAO,GAAGkB,OAAO,CAAClB,IAAI,CAACI,KAAK,CAAC,IAAIc,OAAO,CAAClB,IAAI,CAACO,OAAO,CAAC,EAAE;AAC1D;AACY,MAACc,QAAQ,GAAGA,CAACrB,IAAI,EAAEsB,IAAI,KAAK;EACtC,MAAMC,SAAS,GAAGxB,SAAS,CAACC,IAAI,CAAC;EACjC,IAAI,CAACuB,SAAS,EACZ,OAAO,EAAE;EACX,MAAMC,SAAS,GAAGzB,SAAS,CAACuB,IAAI,CAAC;EACjC,IAAI,CAACE,SAAS,EACZ,OAAO,EAAE;EACX,MAAMC,IAAI,GAAG;IACXrB,KAAK,EAAEmB,SAAS,CAACnB,KAAK;IACtBG,OAAO,EAAEgB,SAAS,CAAChB;EACvB,CAAG;EACDkB,IAAI,CAAClB,OAAO,IAAIiB,SAAS,CAACjB,OAAO;EACjCkB,IAAI,CAACrB,KAAK,IAAIoB,SAAS,CAACpB,KAAK;EAC7BqB,IAAI,CAACrB,KAAK,IAAIsB,IAAI,CAACC,KAAK,CAACF,IAAI,CAAClB,OAAO,GAAG,EAAE,CAAC;EAC3CkB,IAAI,CAAClB,OAAO,GAAGkB,IAAI,CAAClB,OAAO,GAAG,EAAE;EAChC,OAAOa,UAAU,CAACK,IAAI,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}