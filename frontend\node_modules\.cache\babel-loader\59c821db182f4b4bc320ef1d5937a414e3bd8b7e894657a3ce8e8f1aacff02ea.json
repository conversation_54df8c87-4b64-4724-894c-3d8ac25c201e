{"ast": null, "code": "import { Clock, CircleClose } from '@element-plus/icons-vue';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useEmptyValuesProps } from '../../../hooks/use-empty-values/index.mjs';\nconst timeSelectProps = buildProps({\n  format: {\n    type: String,\n    default: \"HH:mm\"\n  },\n  modelValue: String,\n  disabled: Boolean,\n  editable: {\n    type: Boolean,\n    default: true\n  },\n  effect: {\n    type: definePropType(String),\n    default: \"light\"\n  },\n  clearable: {\n    type: Boolean,\n    default: true\n  },\n  size: useSizeProp,\n  placeholder: String,\n  start: {\n    type: String,\n    default: \"09:00\"\n  },\n  end: {\n    type: String,\n    default: \"18:00\"\n  },\n  step: {\n    type: String,\n    default: \"00:30\"\n  },\n  minTime: String,\n  maxTime: String,\n  includeEndTime: {\n    type: Boolean,\n    default: false\n  },\n  name: String,\n  prefixIcon: {\n    type: definePropType([String, Object]),\n    default: () => Clock\n  },\n  clearIcon: {\n    type: definePropType([String, Object]),\n    default: () => CircleClose\n  },\n  ...useEmptyValuesProps\n});\nexport { timeSelectProps };", "map": {"version": 3, "names": ["timeSelectProps", "buildProps", "format", "type", "String", "default", "modelValue", "disabled", "Boolean", "editable", "effect", "definePropType", "clearable", "size", "useSizeProp", "placeholder", "start", "end", "step", "minTime", "maxTime", "includeEndTime", "name", "prefixIcon", "Object", "Clock", "clearIcon", "CircleClose", "useEmptyValuesProps"], "sources": ["../../../../../../packages/components/time-select/src/time-select.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { CircleClose, Clock } from '@element-plus/icons-vue'\nimport { useEmptyValuesProps, useSizeProp } from '@element-plus/hooks'\nimport type { PopperEffect } from '@element-plus/components/popper'\nimport type TimeSelect from './time-select.vue'\nimport type { Component, ExtractPropTypes } from 'vue'\n\nexport const timeSelectProps = buildProps({\n  /**\n   * @description set format of time\n   */\n  format: {\n    type: String,\n    default: 'HH:mm',\n  },\n  /**\n   * @description binding value\n   */\n  modelValue: String,\n  /**\n   * @description whether TimeSelect is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description whether the input is editable\n   */\n  editable: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description Tooltip theme, built-in theme: `dark` / `light`\n   */\n  effect: {\n    type: definePropType<PopperEffect>(String),\n    default: 'light',\n  },\n  /**\n   * @description whether to show clear button\n   */\n  clearable: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description size of Input\n   */\n  size: useSizeProp,\n  /**\n   * @description placeholder in non-range mode\n   */\n  placeholder: String,\n  /**\n   * @description start time\n   */\n  start: {\n    type: String,\n    default: '09:00',\n  },\n  /**\n   * @description end time\n   */\n  end: {\n    type: String,\n    default: '18:00',\n  },\n  /**\n   * @description time step\n   */\n  step: {\n    type: String,\n    default: '00:30',\n  },\n  /**\n   * @description minimum time, any time before this time will be disabled\n   */\n  minTime: String,\n  /**\n   * @description maximum time, any time after this time will be disabled\n   */\n  maxTime: String,\n  /**\n   * @description whether `end` is included in options\n   */\n  includeEndTime: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description same as `name` in native input\n   */\n  name: String,\n  /**\n   * @description custom prefix icon component\n   */\n  prefixIcon: {\n    type: definePropType<string | Component>([String, Object]),\n    default: () => Clock,\n  },\n  /**\n   * @description custom clear icon component\n   */\n  clearIcon: {\n    type: definePropType<string | Component>([String, Object]),\n    default: () => CircleClose,\n  },\n  ...useEmptyValuesProps,\n} as const)\n\nexport type TimeSelectProps = ExtractPropTypes<typeof timeSelectProps>\n\nexport type TimeSelectInstance = InstanceType<typeof TimeSelect> & unknown\n"], "mappings": ";;;;AAGY,MAACA,eAAe,GAAGC,UAAU,CAAC;EACxCC,MAAM,EAAE;IACNC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,UAAU,EAAEF,MAAM;EAClBG,QAAQ,EAAEC,OAAO;EACjBC,QAAQ,EAAE;IACRN,IAAI,EAAEK,OAAO;IACbH,OAAO,EAAE;EACb,CAAG;EACDK,MAAM,EAAE;IACNP,IAAI,EAAEQ,cAAc,CAACP,MAAM,CAAC;IAC5BC,OAAO,EAAE;EACb,CAAG;EACDO,SAAS,EAAE;IACTT,IAAI,EAAEK,OAAO;IACbH,OAAO,EAAE;EACb,CAAG;EACDQ,IAAI,EAAEC,WAAW;EACjBC,WAAW,EAAEX,MAAM;EACnBY,KAAK,EAAE;IACLb,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDY,GAAG,EAAE;IACHd,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDa,IAAI,EAAE;IACJf,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDc,OAAO,EAAEf,MAAM;EACfgB,OAAO,EAAEhB,MAAM;EACfiB,cAAc,EAAE;IACdlB,IAAI,EAAEK,OAAO;IACbH,OAAO,EAAE;EACb,CAAG;EACDiB,IAAI,EAAElB,MAAM;EACZmB,UAAU,EAAE;IACVpB,IAAI,EAAEQ,cAAc,CAAC,CAACP,MAAM,EAAEoB,MAAM,CAAC,CAAC;IACtCnB,OAAO,EAAEA,CAAA,KAAMoB;EACnB,CAAG;EACDC,SAAS,EAAE;IACTvB,IAAI,EAAEQ,cAAc,CAAC,CAACP,MAAM,EAAEoB,MAAM,CAAC,CAAC;IACtCnB,OAAO,EAAEA,CAAA,KAAMsB;EACnB,CAAG;EACD,GAAGC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}