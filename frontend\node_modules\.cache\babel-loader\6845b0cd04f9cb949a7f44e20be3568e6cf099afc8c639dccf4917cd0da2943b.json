{"ast": null, "code": "import { defineComponent, inject, ref, computed, onBeforeUnmount, toRef, openBlock, createBlock, Transition, unref, withCtx, withDirectives, createElementVNode, normalizeClass, withModifiers, normalizeStyle, vShow } from 'vue';\nimport { useEventListener, isClient } from '@vueuse/core';\nimport { scrollbarContextKey } from './constants.mjs';\nimport { BAR_MAP, renderThumbStyle } from './util.mjs';\nimport { thumbProps } from './thumb.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nconst COMPONENT_NAME = \"Thumb\";\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"thumb\",\n  props: thumbProps,\n  setup(__props) {\n    const props = __props;\n    const scrollbar = inject(scrollbarContextKey);\n    const ns = useNamespace(\"scrollbar\");\n    if (!scrollbar) throwError(COMPONENT_NAME, \"can not inject scrollbar context\");\n    const instance = ref();\n    const thumb = ref();\n    const thumbState = ref({});\n    const visible = ref(false);\n    let cursorDown = false;\n    let cursorLeave = false;\n    let originalOnSelectStart = isClient ? document.onselectstart : null;\n    const bar = computed(() => BAR_MAP[props.vertical ? \"vertical\" : \"horizontal\"]);\n    const thumbStyle = computed(() => renderThumbStyle({\n      size: props.size,\n      move: props.move,\n      bar: bar.value\n    }));\n    const offsetRatio = computed(() => instance.value[bar.value.offset] ** 2 / scrollbar.wrapElement[bar.value.scrollSize] / props.ratio / thumb.value[bar.value.offset]);\n    const clickThumbHandler = e => {\n      var _a;\n      e.stopPropagation();\n      if (e.ctrlKey || [1, 2].includes(e.button)) return;\n      (_a = window.getSelection()) == null ? void 0 : _a.removeAllRanges();\n      startDrag(e);\n      const el = e.currentTarget;\n      if (!el) return;\n      thumbState.value[bar.value.axis] = el[bar.value.offset] - (e[bar.value.client] - el.getBoundingClientRect()[bar.value.direction]);\n    };\n    const clickTrackHandler = e => {\n      if (!thumb.value || !instance.value || !scrollbar.wrapElement) return;\n      const offset = Math.abs(e.target.getBoundingClientRect()[bar.value.direction] - e[bar.value.client]);\n      const thumbHalf = thumb.value[bar.value.offset] / 2;\n      const thumbPositionPercentage = (offset - thumbHalf) * 100 * offsetRatio.value / instance.value[bar.value.offset];\n      scrollbar.wrapElement[bar.value.scroll] = thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize] / 100;\n    };\n    const startDrag = e => {\n      e.stopImmediatePropagation();\n      cursorDown = true;\n      document.addEventListener(\"mousemove\", mouseMoveDocumentHandler);\n      document.addEventListener(\"mouseup\", mouseUpDocumentHandler);\n      originalOnSelectStart = document.onselectstart;\n      document.onselectstart = () => false;\n    };\n    const mouseMoveDocumentHandler = e => {\n      if (!instance.value || !thumb.value) return;\n      if (cursorDown === false) return;\n      const prevPage = thumbState.value[bar.value.axis];\n      if (!prevPage) return;\n      const offset = (instance.value.getBoundingClientRect()[bar.value.direction] - e[bar.value.client]) * -1;\n      const thumbClickPosition = thumb.value[bar.value.offset] - prevPage;\n      const thumbPositionPercentage = (offset - thumbClickPosition) * 100 * offsetRatio.value / instance.value[bar.value.offset];\n      scrollbar.wrapElement[bar.value.scroll] = thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize] / 100;\n    };\n    const mouseUpDocumentHandler = () => {\n      cursorDown = false;\n      thumbState.value[bar.value.axis] = 0;\n      document.removeEventListener(\"mousemove\", mouseMoveDocumentHandler);\n      document.removeEventListener(\"mouseup\", mouseUpDocumentHandler);\n      restoreOnselectstart();\n      if (cursorLeave) visible.value = false;\n    };\n    const mouseMoveScrollbarHandler = () => {\n      cursorLeave = false;\n      visible.value = !!props.size;\n    };\n    const mouseLeaveScrollbarHandler = () => {\n      cursorLeave = true;\n      visible.value = cursorDown;\n    };\n    onBeforeUnmount(() => {\n      restoreOnselectstart();\n      document.removeEventListener(\"mouseup\", mouseUpDocumentHandler);\n    });\n    const restoreOnselectstart = () => {\n      if (document.onselectstart !== originalOnSelectStart) document.onselectstart = originalOnSelectStart;\n    };\n    useEventListener(toRef(scrollbar, \"scrollbarElement\"), \"mousemove\", mouseMoveScrollbarHandler);\n    useEventListener(toRef(scrollbar, \"scrollbarElement\"), \"mouseleave\", mouseLeaveScrollbarHandler);\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(Transition, {\n        name: unref(ns).b(\"fade\"),\n        persisted: \"\"\n      }, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"div\", {\n          ref_key: \"instance\",\n          ref: instance,\n          class: normalizeClass([unref(ns).e(\"bar\"), unref(ns).is(unref(bar).key)]),\n          onMousedown: clickTrackHandler,\n          onClick: withModifiers(() => {}, [\"stop\"])\n        }, [createElementVNode(\"div\", {\n          ref_key: \"thumb\",\n          ref: thumb,\n          class: normalizeClass(unref(ns).e(\"thumb\")),\n          style: normalizeStyle(unref(thumbStyle)),\n          onMousedown: clickThumbHandler\n        }, null, 38)], 42, [\"onClick\"]), [[vShow, _ctx.always || visible.value]])]),\n        _: 1\n      }, 8, [\"name\"]);\n    };\n  }\n});\nvar Thumb = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"thumb.vue\"]]);\nexport { Thumb as default };", "map": {"version": 3, "names": ["scrollbar", "inject", "scrollbarContextKey", "ns", "useNamespace", "throwError", "COMPONENT_NAME", "instance", "ref", "thumb", "thumbState", "visible", "cursorDown", "cursorLeave", "originalOnSelectStart", "isClient", "document", "onselectstart", "bar", "computed", "BAR_MAP", "props", "vertical", "thumbStyle", "renderThumbStyle", "size", "move", "value", "offsetRatio", "offset", "wrapElement", "scrollSize", "ratio", "clickThumbHandler", "e", "_a", "stopPropagation", "ctrl<PERSON>ey", "includes", "button", "window", "getSelection", "removeAllRanges", "startDrag", "el", "currentTarget", "axis", "client", "getBoundingClientRect", "direction", "clickTrackHandler", "Math", "abs", "target", "<PERSON><PERSON><PERSON>f", "thumbPositionPercentage", "scroll", "stopImmediatePropagation", "addEventListener", "mouseMoveDocumentHandler", "mouseUpDocumentHandler", "prevPage", "thumbClickPosition", "removeEventListener", "restoreOnselectstart", "mouseMoveScrollbarHandler", "mouseLeaveScrollbarHandler", "onBeforeUnmount", "useEventListener", "toRef", "_ctx", "_cache", "openBlock", "createBlock", "Transition", "name", "unref", "b", "persisted", "default", "withCtx", "withDirectives", "createElementVNode", "ref_key", "class", "normalizeClass", "is", "key"], "sources": ["../../../../../../packages/components/scrollbar/src/thumb.vue"], "sourcesContent": ["<template>\n  <transition :name=\"ns.b('fade')\">\n    <div\n      v-show=\"always || visible\"\n      ref=\"instance\"\n      :class=\"[ns.e('bar'), ns.is(bar.key)]\"\n      @mousedown=\"clickTrackHandler\"\n      @click.stop\n    >\n      <div\n        ref=\"thumb\"\n        :class=\"ns.e('thumb')\"\n        :style=\"thumbStyle\"\n        @mousedown=\"clickThumbHandler\"\n      />\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, onBeforeUnmount, ref, toRef } from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { isClient, throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { scrollbarContextKey } from './constants'\nimport { BAR_MAP, renderThumbStyle } from './util'\nimport { thumbProps } from './thumb'\n\nconst COMPONENT_NAME = 'Thumb'\nconst props = defineProps(thumbProps)\n\nconst scrollbar = inject(scrollbarContextKey)\nconst ns = useNamespace('scrollbar')\n\nif (!scrollbar) throwError(COMPONENT_NAME, 'can not inject scrollbar context')\n\nconst instance = ref<HTMLDivElement>()\nconst thumb = ref<HTMLDivElement>()\n\nconst thumbState = ref<Partial<Record<'X' | 'Y', number>>>({})\nconst visible = ref(false)\n\nlet cursorDown = false\nlet cursorLeave = false\nlet originalOnSelectStart:\n  | ((this: GlobalEventHandlers, ev: Event) => any)\n  | null = isClient ? document.onselectstart : null\n\nconst bar = computed(() => BAR_MAP[props.vertical ? 'vertical' : 'horizontal'])\n\nconst thumbStyle = computed(() =>\n  renderThumbStyle({\n    size: props.size,\n    move: props.move,\n    bar: bar.value,\n  })\n)\n\nconst offsetRatio = computed(\n  () =>\n    // offsetRatioX = original width of thumb / current width of thumb / ratioX\n    // offsetRatioY = original height of thumb / current height of thumb / ratioY\n    // instance height = wrap height - GAP\n    instance.value![bar.value.offset] ** 2 /\n    scrollbar.wrapElement![bar.value.scrollSize] /\n    props.ratio /\n    thumb.value![bar.value.offset]\n)\n\nconst clickThumbHandler = (e: MouseEvent) => {\n  // prevent click event of middle and right button\n  e.stopPropagation()\n  if (e.ctrlKey || [1, 2].includes(e.button)) return\n\n  window.getSelection()?.removeAllRanges()\n  startDrag(e)\n\n  const el = e.currentTarget as HTMLDivElement\n  if (!el) return\n  thumbState.value[bar.value.axis] =\n    el[bar.value.offset] -\n    (e[bar.value.client] - el.getBoundingClientRect()[bar.value.direction])\n}\n\nconst clickTrackHandler = (e: MouseEvent) => {\n  if (!thumb.value || !instance.value || !scrollbar.wrapElement) return\n\n  const offset = Math.abs(\n    (e.target as HTMLElement).getBoundingClientRect()[bar.value.direction] -\n      e[bar.value.client]\n  )\n  const thumbHalf = thumb.value[bar.value.offset] / 2\n  const thumbPositionPercentage =\n    ((offset - thumbHalf) * 100 * offsetRatio.value) /\n    instance.value[bar.value.offset]\n\n  scrollbar.wrapElement[bar.value.scroll] =\n    (thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize]) /\n    100\n}\n\nconst startDrag = (e: MouseEvent) => {\n  e.stopImmediatePropagation()\n  cursorDown = true\n  document.addEventListener('mousemove', mouseMoveDocumentHandler)\n  document.addEventListener('mouseup', mouseUpDocumentHandler)\n  originalOnSelectStart = document.onselectstart\n  document.onselectstart = () => false\n}\n\nconst mouseMoveDocumentHandler = (e: MouseEvent) => {\n  if (!instance.value || !thumb.value) return\n  if (cursorDown === false) return\n\n  const prevPage = thumbState.value[bar.value.axis]\n  if (!prevPage) return\n\n  const offset =\n    (instance.value.getBoundingClientRect()[bar.value.direction] -\n      e[bar.value.client]) *\n    -1\n  const thumbClickPosition = thumb.value[bar.value.offset] - prevPage\n  const thumbPositionPercentage =\n    ((offset - thumbClickPosition) * 100 * offsetRatio.value) /\n    instance.value[bar.value.offset]\n  scrollbar.wrapElement[bar.value.scroll] =\n    (thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize]) /\n    100\n}\n\nconst mouseUpDocumentHandler = () => {\n  cursorDown = false\n  thumbState.value[bar.value.axis] = 0\n  document.removeEventListener('mousemove', mouseMoveDocumentHandler)\n  document.removeEventListener('mouseup', mouseUpDocumentHandler)\n  restoreOnselectstart()\n  if (cursorLeave) visible.value = false\n}\n\nconst mouseMoveScrollbarHandler = () => {\n  cursorLeave = false\n  visible.value = !!props.size\n}\n\nconst mouseLeaveScrollbarHandler = () => {\n  cursorLeave = true\n  visible.value = cursorDown\n}\n\nonBeforeUnmount(() => {\n  restoreOnselectstart()\n  document.removeEventListener('mouseup', mouseUpDocumentHandler)\n})\n\nconst restoreOnselectstart = () => {\n  if (document.onselectstart !== originalOnSelectStart)\n    document.onselectstart = originalOnSelectStart\n}\n\nuseEventListener(\n  toRef(scrollbar, 'scrollbarElement'),\n  'mousemove',\n  mouseMoveScrollbarHandler\n)\nuseEventListener(\n  toRef(scrollbar, 'scrollbarElement'),\n  'mouseleave',\n  mouseLeaveScrollbarHandler\n)\n</script>\n"], "mappings": ";;;;;;;;;;;;;;IA+BM,MAAAA,SAAA,GAAYC,MAAA,CAAOC,mBAAmB;IACtC,MAAAC,EAAA,GAAKC,YAAA,CAAa,WAAW;IAEnC,IAAI,CAACJ,SAAA,EAELK,UAAA,CAAAC,cAAqC;IACrC,MAAMC,QAAQ,GAAoBC,GAAA;IAE5B,MAAAC,KAAA,GAAAD,GAAA,EAAa;IACb,MAAAE,UAAU,GAAAF,GAAS;IAEzB,MAAiBG,OAAA,GAAAH,GAAA;IACjB,IAAII,UAAc;IACd,IAAAC,WAAA;IAIE,IAAAC,qBAAqB,GAAAC,QAAA,GAAAC,QAAyB,CAAAC,aAAA;IAEpD,MAAMC,GAAa,GAAAC,QAAA,OAAAC,OAAA,CAAAC,KAAA,CAAAC,QAAA;IAAA,MAAAC,UACA,GAAAJ,QAAA,OAAAK,gBAAA;MAAAC,IAAA,EAAAJ,KACH,CAAAI,IAAA;MAAAC,IAAA,EAAAL,KACA,CAAAK,IAAA;MAAAR,GAAA,EAAAA,GACH,CAAAS;IAAA,EACX,CAAC;IACH,MAAAC,WAAA,GAAAT,QAAA,OAAAZ,QAAA,CAAAoB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAE,MAAA,SAAA7B,SAAA,CAAA8B,WAAA,CAAAZ,GAAA,CAAAS,KAAA,CAAAI,UAAA,IAAAV,KAAA,CAAAW,KAAA,GAAAvB,KAAA,CAAAkB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAE,MAAA;IAEA,MAAMI,iBAAc,GAAAC,CAAA;MAClB,IAAAC,EAAA;MAAAD,CAAA,CAAAE,eAAA;MAAA,IAAAF,CAAA,CAAAG,OAAA,WAAAC,QAAA,CAAAJ,CAAA,CAAAK,MAAA;MAAA,CAAAJ,EAAA,GAAAK,MAIkB,CAAAC,YAAA,EAAU,KAAM,YACtB,IAAAN,EAAA,CAAAO,eAAA,EAAiB;MAEEC,SAAA,CAAAT,CAAA;MACjC,MAAAU,EAAA,GAAAV,CAAA,CAAAW,aAAA;MAEM,KAAAD,EAAA,EAEF;MACElC,UAAA,CAAAiB,KAAA,CAAaT,GAAC,CAAGS,KAAG,CAAAmB,IAAA,IAAWF,EAAA,CAAA1B,GAAM,CAAGS,KAAA,CAAAE,MAAA,KAAAK,CAAA,CAAAhB,GAAA,CAAAS,KAAA,CAAAoB,MAAA,IAAAH,EAAA,CAAAI,qBAAA,GAAA9B,GAAA,CAAAS,KAAA,CAAAsB,SAAA;IAE5C,CAAO;IACP,MAAAC,iBAAW,GAAAhB,CAAA;MAEX,KAAAzB,KAAA,CAAWkB,KAAE,KAAApB,QAAA,CAAAoB,KAAA,KAAA3B,SAAA,CAAA8B,WAAA,EACb;MACW,MAAAD,MAAA,GAAAsB,IAAA,CAAAC,GAAgB,CAAAlB,CAAA,CAAAmB,MAAA,CAAAL,qBACN,GAAA9B,GAAA,CAChBS,KAAI,CAAAsB,SAAA,CAAY,GAAIf,CAAA,CAAAhB,GAAA,CAAAS,KAAA,CAAAoB,MAAA;MAC3B,MAAAO,SAAA,GAAA7C,KAAA,CAAAkB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAE,MAAA;MAEM,MAAA0B,uBAAuC,IAAA1B,MAAA,GAAAyB,SAAA,UAAA1B,WAAA,CAAAD,KAAA,GAAApB,QAAA,CAAAoB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAE,MAAA;MACvC7B,SAAA,CAAA8B,WAAiB,CAAAZ,GAAA,CAAAS,KAAkB,CAAA6B,MAAA,IAAAD,uBAAwB,GAAAvD,SAAA,CAAA8B,WAAA,CAAAZ,GAAA,CAAAS,KAAA,CAAAI,UAAA;IAE/D;IACG,MAAEY,SAAuB,GAAAT,CAAA;MAE5BA,CAAA,CAAAuB,wBAAA;MACA7C,UAAA;MACMI,QAAA,CAAA0C,gBAAA,YACO,EAAAC,wBAAmB;MAGtB3C,QAAA,CAAA0C,gBAAgB,UAAY,EAAAE,sBACT;MAE/B9C,qBAAA,GAAAE,QAAA,CAAAC,aAAA;MAEMD,QAAA,CAAAC,aAA+B;IACnC;IACa,MAAA0C,wBAAA,GAAAzB,CAAA;MACJ,KAAA3B,QAAA,CAAAoB,KAAA,KAAAlB,KAAA,CAAAkB,KAAA,EACA;MACT,IAAAf,UAAA,UAAwB,EACxB;MACF,MAAAiD,QAAA,GAAAnD,UAAA,CAAAiB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAmB,IAAA;MAEM,KAAAe,QAAA,EACJ;MACA,MAAAhC,MAAA,IAAAtB,QAA0B,CAAAoB,KAAA,CAAAqB,qBAAA,GAAA9B,GAAA,CAAAS,KAAA,CAAAsB,SAAA,IAAAf,CAAA,CAAAhB,GAAA,CAAAS,KAAA,CAAAoB,MAAA;MAE1B,MAAMe,kBAAW,GAAArD,KAAiB,CAAAkB,KAAI,CAAAT,GAAA,CAAAS,KAAU,CAAAE,MAAA,IAAAgC,QAAA;MAChD,MAAeN,uBAAA,IAAA1B,MAAA,GAAAiC,kBAAA,UAAAlC,WAAA,CAAAD,KAAA,GAAApB,QAAA,CAAAoB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAE,MAAA;MAEf7B,SACG,CAAA8B,WAAA,CAAAZ,GAAS,CAAMS,KAAA,CAAA6B,MAAA,IAAAD,uBAAkC,GAAAvD,SAChD,CAAA8B,WAAM,CAAMZ,GAAA,CAAAS,KACd,CAAAI,UAAA;IACF;IACM,MAAA6B,sBAAA,GAAAA,CAAA,KACO;MAEHhD,UAAA;MAGZF,UAAA,CAAAiB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAmB,IAAA;MAEA9B,QAAA,CAAA+C,mBAAA,CAA+B,WAAM,EAAAJ,wBAAA;MACtB3C,QAAA,CAAA+C,mBAAA,YAAAH,sBAAA;MACbI,oBAAiB,EAAU;MAClB,IAAAnD,WAAA,EACAF,OAAA,CAAAgB,KAAA;IACT,CAAqB;IACjB,MAAAsC,yBAA6B,GAAAA,CAAA;MACnCpD,WAAA;MAEAF,OAAA,CAAAgB,KAAA,KAAAN,KAAA,CAAAI,IAAA;IACE,CAAc;IACN,MAAAyC,0BAAgB,GAAAA,CAAA;MAC1BrD,WAAA;MAEAF,OAAA,CAAAgB,KAAA,GAAAf,UAAA;IACE,CAAc;IACduD,eAAgB;MAClBH,oBAAA;MAEAhD,QAAA,CAAA+C,mBAAsB,YAAAH,sBAAA;IACpB,CAAqB;IACZ,MAAAI,oBAAA,GAAAA,CAAA;MACV,IAAAhD,QAAA,CAAAC,aAAA,KAAAH,qBAAA,EAEDE,QAAA,CAAAC,aAAA,GAA6BH,qBAAM;IACjC;IACEsD,gBAAyB,CAAAC,KAAA,CAAArE,SAAA,oCAAAiE,yBAAA;IAC7BG,gBAAA,CAAAC,KAAA,CAAArE,SAAA,qCAAAkE,0BAAA;IAEA,QAAAI,IAAA,EAAAC,MAAA;MACE,OAAAC,SAAiB,EAAkB,EAAAC,WAAA,CAAAC,UAAA;QACnCC,IAAA,EAAAC,KAAA,CAAAzE,EAAA,EAAA0E,CAAA;QACAC,SAAA;MAAA,CACF;QACAC,OAAA,EAAAC,OAAA,QACEC,cAAmC,CAAAC,kBAAA;UACnCC,OAAA;UACA3E,GAAA,EAAAD,QAAA;UACF6E,KAAA,EAAAC,cAAA,EAAAT,KAAA,CAAAzE,EAAA,EAAA+B,CAAA,SAAA0C,KAAA,CAAAzE,EAAA,EAAAmF,EAAA,CAAAV,KAAA,CAAA1D,GAAA,EAAAqE,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}