{"ast": null, "code": "import { ref, onMounted } from 'vue';\nimport axios from 'axios';\nexport default {\n  name: 'OperationRecords',\n  setup() {\n    const records = ref([]);\n    const loading = ref(true);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    const total = ref(0);\n\n    // 模拟数据\n    const mockData = [{\n      id: 1,\n      operation_type: '预约座位',\n      operation_time: '2023-10-15 09:30:45',\n      operation_content: '预约了A区101号座位',\n      status: '成功'\n    }, {\n      id: 2,\n      operation_type: '取消预约',\n      operation_time: '2023-10-16 14:20:30',\n      operation_content: '取消了A区105号座位的预约',\n      status: '成功'\n    }, {\n      id: 3,\n      operation_type: '签到',\n      operation_time: '2023-10-17 08:05:12',\n      operation_content: '在B区203号座位签到',\n      status: '成功'\n    }, {\n      id: 4,\n      operation_type: '签退',\n      operation_time: '2023-10-17 12:30:00',\n      operation_content: '从B区203号座位签退',\n      status: '成功'\n    }, {\n      id: 5,\n      operation_type: '预约座位',\n      operation_time: '2023-10-18 16:45:22',\n      operation_content: '预约了C区301号座位',\n      status: '失败'\n    }];\n    const fetchRecords = () => {\n      loading.value = true;\n      // 模拟API请求\n      setTimeout(() => {\n        records.value = mockData;\n        total.value = mockData.length;\n        loading.value = false;\n      }, 500);\n    };\n    const handleSizeChange = val => {\n      pageSize.value = val;\n      fetchRecords();\n    };\n    const handleCurrentChange = val => {\n      currentPage.value = val;\n      fetchRecords();\n    };\n    onMounted(() => {\n      fetchRecords();\n    });\n    return {\n      records,\n      loading,\n      currentPage,\n      pageSize,\n      total,\n      handleSizeChange,\n      handleCurrentChange\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "axios", "name", "setup", "records", "loading", "currentPage", "pageSize", "total", "mockData", "id", "operation_type", "operation_time", "operation_content", "status", "fetchRecords", "value", "setTimeout", "length", "handleSizeChange", "val", "handleCurrentChange"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\OperationRecords.vue"], "sourcesContent": ["<template>\n  <div class=\"operation-records-container\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h2>操作记录</h2>\n        </div>\n      </template>\n      \n      <el-table\n        :data=\"records\"\n        style=\"width: 100%\"\n        v-loading=\"loading\"\n      >\n        <el-table-column\n          prop=\"id\"\n          label=\"ID\"\n          width=\"80\"\n        />\n        <el-table-column\n          prop=\"operation_type\"\n          label=\"操作类型\"\n          width=\"120\"\n        />\n        <el-table-column\n          prop=\"operation_time\"\n          label=\"操作时间\"\n          width=\"180\"\n        />\n        <el-table-column\n          prop=\"operation_content\"\n          label=\"操作内容\"\n        />\n        <el-table-column\n          prop=\"status\"\n          label=\"状态\"\n          width=\"100\"\n        >\n          <template #default=\"scope\">\n            <el-tag\n              :type=\"scope.row.status === '成功' ? 'success' : 'danger'\"\n            >\n              {{ scope.row.status }}\n            </el-tag>\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <div class=\"pagination-container\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n        />\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted } from 'vue';\nimport axios from 'axios';\n\nexport default {\n  name: 'OperationRecords',\n  setup() {\n    const records = ref([]);\n    const loading = ref(true);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    const total = ref(0);\n\n    // 模拟数据\n    const mockData = [\n      {\n        id: 1,\n        operation_type: '预约座位',\n        operation_time: '2023-10-15 09:30:45',\n        operation_content: '预约了A区101号座位',\n        status: '成功'\n      },\n      {\n        id: 2,\n        operation_type: '取消预约',\n        operation_time: '2023-10-16 14:20:30',\n        operation_content: '取消了A区105号座位的预约',\n        status: '成功'\n      },\n      {\n        id: 3,\n        operation_type: '签到',\n        operation_time: '2023-10-17 08:05:12',\n        operation_content: '在B区203号座位签到',\n        status: '成功'\n      },\n      {\n        id: 4,\n        operation_type: '签退',\n        operation_time: '2023-10-17 12:30:00',\n        operation_content: '从B区203号座位签退',\n        status: '成功'\n      },\n      {\n        id: 5,\n        operation_type: '预约座位',\n        operation_time: '2023-10-18 16:45:22',\n        operation_content: '预约了C区301号座位',\n        status: '失败'\n      }\n    ];\n\n    const fetchRecords = () => {\n      loading.value = true;\n      // 模拟API请求\n      setTimeout(() => {\n        records.value = mockData;\n        total.value = mockData.length;\n        loading.value = false;\n      }, 500);\n    };\n\n    const handleSizeChange = (val) => {\n      pageSize.value = val;\n      fetchRecords();\n    };\n\n    const handleCurrentChange = (val) => {\n      currentPage.value = val;\n      fetchRecords();\n    };\n\n    onMounted(() => {\n      fetchRecords();\n    });\n\n    return {\n      records,\n      loading,\n      currentPage,\n      pageSize,\n      total,\n      handleSizeChange,\n      handleCurrentChange\n    };\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.operation-records-container {\n  padding: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n}\n</style>\n"], "mappings": "AAgEA,SAASA,GAAG,EAAEC,SAAQ,QAAS,KAAK;AACpC,OAAOC,KAAI,MAAO,OAAO;AAEzB,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,KAAKA,CAAA,EAAG;IACN,MAAMC,OAAM,GAAIL,GAAG,CAAC,EAAE,CAAC;IACvB,MAAMM,OAAM,GAAIN,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMO,WAAU,GAAIP,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMQ,QAAO,GAAIR,GAAG,CAAC,EAAE,CAAC;IACxB,MAAMS,KAAI,GAAIT,GAAG,CAAC,CAAC,CAAC;;IAEpB;IACA,MAAMU,QAAO,GAAI,CACf;MACEC,EAAE,EAAE,CAAC;MACLC,cAAc,EAAE,MAAM;MACtBC,cAAc,EAAE,qBAAqB;MACrCC,iBAAiB,EAAE,aAAa;MAChCC,MAAM,EAAE;IACV,CAAC,EACD;MACEJ,EAAE,EAAE,CAAC;MACLC,cAAc,EAAE,MAAM;MACtBC,cAAc,EAAE,qBAAqB;MACrCC,iBAAiB,EAAE,gBAAgB;MACnCC,MAAM,EAAE;IACV,CAAC,EACD;MACEJ,EAAE,EAAE,CAAC;MACLC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,qBAAqB;MACrCC,iBAAiB,EAAE,aAAa;MAChCC,MAAM,EAAE;IACV,CAAC,EACD;MACEJ,EAAE,EAAE,CAAC;MACLC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,qBAAqB;MACrCC,iBAAiB,EAAE,aAAa;MAChCC,MAAM,EAAE;IACV,CAAC,EACD;MACEJ,EAAE,EAAE,CAAC;MACLC,cAAc,EAAE,MAAM;MACtBC,cAAc,EAAE,qBAAqB;MACrCC,iBAAiB,EAAE,aAAa;MAChCC,MAAM,EAAE;IACV,EACD;IAED,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzBV,OAAO,CAACW,KAAI,GAAI,IAAI;MACpB;MACAC,UAAU,CAAC,MAAM;QACfb,OAAO,CAACY,KAAI,GAAIP,QAAQ;QACxBD,KAAK,CAACQ,KAAI,GAAIP,QAAQ,CAACS,MAAM;QAC7Bb,OAAO,CAACW,KAAI,GAAI,KAAK;MACvB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;IAED,MAAMG,gBAAe,GAAKC,GAAG,IAAK;MAChCb,QAAQ,CAACS,KAAI,GAAII,GAAG;MACpBL,YAAY,CAAC,CAAC;IAChB,CAAC;IAED,MAAMM,mBAAkB,GAAKD,GAAG,IAAK;MACnCd,WAAW,CAACU,KAAI,GAAII,GAAG;MACvBL,YAAY,CAAC,CAAC;IAChB,CAAC;IAEDf,SAAS,CAAC,MAAM;MACde,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC;IAEF,OAAO;MACLX,OAAO;MACPC,OAAO;MACPC,WAAW;MACXC,QAAQ;MACRC,KAAK;MACLW,gBAAgB;MAChBE;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}