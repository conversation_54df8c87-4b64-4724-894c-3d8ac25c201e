#!/usr/bin/env python
"""
调试Django设置
"""
import os
import sys
import django
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.conf import settings

print("=== Django设置调试 ===")
print(f"BASE_DIR: {settings.BASE_DIR}")
print(f"BASE_DIR type: {type(settings.BASE_DIR)}")
print(f"SM2_PUBLIC_KEY_PATH: {settings.SM2_PUBLIC_KEY_PATH}")
print(f"SM2_PRIVATE_KEY_PATH: {settings.SM2_PRIVATE_KEY_PATH}")
print(f"SM2_PUBLIC_KEY_PATH type: {type(settings.SM2_PUBLIC_KEY_PATH)}")
print(f"SM2_PRIVATE_KEY_PATH type: {type(settings.SM2_PRIVATE_KEY_PATH)}")

# 检查路径是否存在
print(f"公钥路径存在: {os.path.exists(settings.SM2_PUBLIC_KEY_PATH)}")
print(f"私钥路径存在: {os.path.exists(settings.SM2_PRIVATE_KEY_PATH)}")

# 检查目录
keys_dir = os.path.dirname(settings.SM2_PUBLIC_KEY_PATH)
print(f"密钥目录: {keys_dir}")
print(f"密钥目录存在: {os.path.exists(keys_dir)}")

# 测试SM2Crypto导入
try:
    from utils.crypto import SM2Crypto
    print("SM2Crypto导入成功")
    
    # 尝试生成密钥对
    print("尝试生成密钥对...")
    key_pair = SM2Crypto.generate_key_pair()
    print("密钥对生成成功!")
    print(f"私钥长度: {len(key_pair['private_key'])}")
    print(f"公钥长度: {len(key_pair['public_key'])}")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
