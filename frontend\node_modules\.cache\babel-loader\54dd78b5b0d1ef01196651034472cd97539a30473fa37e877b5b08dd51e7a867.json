{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, computed, unref, reactive, toRefs, getCurrentInstance, onBeforeUnmount, nextTick, withDirectives, openBlock, createElementBlock, normalizeClass, withModifiers, renderSlot, createElementVNode, toDisplayString, vShow } from 'vue';\nimport { useOption } from './useOption.mjs';\nimport { COMPONENT_NAME, optionProps } from './option.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nconst _sfc_main = defineComponent({\n  name: COMPONENT_NAME,\n  componentName: COMPONENT_NAME,\n  props: optionProps,\n  setup(props) {\n    const ns = useNamespace(\"select\");\n    const id = useId();\n    const containerKls = computed(() => [ns.be(\"dropdown\", \"item\"), ns.is(\"disabled\", unref(isDisabled)), ns.is(\"selected\", unref(itemSelected)), ns.is(\"hovering\", unref(hover))]);\n    const states = reactive({\n      index: -1,\n      groupDisabled: false,\n      visible: true,\n      hover: false\n    });\n    const {\n      currentLabel,\n      itemSelected,\n      isDisabled,\n      select,\n      hoverItem,\n      updateOption\n    } = useOption(props, states);\n    const {\n      visible,\n      hover\n    } = toRefs(states);\n    const vm = getCurrentInstance().proxy;\n    select.onOptionCreate(vm);\n    onBeforeUnmount(() => {\n      const key = vm.value;\n      const {\n        selected: selectedOptions\n      } = select.states;\n      const doesSelected = selectedOptions.some(item => {\n        return item.value === vm.value;\n      });\n      nextTick(() => {\n        if (select.states.cachedOptions.get(key) === vm && !doesSelected) {\n          select.states.cachedOptions.delete(key);\n        }\n      });\n      select.onOptionDestroy(key, vm);\n    });\n    function selectOptionClick() {\n      if (!isDisabled.value) {\n        select.handleOptionSelect(vm);\n      }\n    }\n    return {\n      ns,\n      id,\n      containerKls,\n      currentLabel,\n      itemSelected,\n      isDisabled,\n      select,\n      visible,\n      hover,\n      states,\n      hoverItem,\n      updateOption,\n      selectOptionClick\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache) {\n  return withDirectives((openBlock(), createElementBlock(\"li\", {\n    id: _ctx.id,\n    class: normalizeClass(_ctx.containerKls),\n    role: \"option\",\n    \"aria-disabled\": _ctx.isDisabled || void 0,\n    \"aria-selected\": _ctx.itemSelected,\n    onMousemove: _ctx.hoverItem,\n    onClick: withModifiers(_ctx.selectOptionClick, [\"stop\"])\n  }, [renderSlot(_ctx.$slots, \"default\", {}, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.currentLabel), 1)])], 42, [\"id\", \"aria-disabled\", \"aria-selected\", \"onMousemove\", \"onClick\"])), [[vShow, _ctx.visible]]);\n}\nvar Option = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"option.vue\"]]);\nexport { Option as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "name", "COMPONENT_NAME", "componentName", "props", "optionProps", "setup", "ns", "useNamespace", "id", "useId", "containerKls", "computed", "be", "is", "unref", "isDisabled", "itemSelected", "hover", "states", "reactive", "index", "groupDisabled", "visible", "current<PERSON><PERSON><PERSON>", "select", "hoverItem", "updateOption", "useOption", "toRefs", "vm", "getCurrentInstance", "proxy", "onOptionCreate", "onBeforeUnmount", "key", "value", "selected", "selectedOptions", "doesSelected", "some", "item", "nextTick", "cachedOptions", "get", "delete", "onOptionDestroy", "selectOptionClick", "handleOptionSelect", "createElementBlock", "_ctx", "class", "normalizeClass", "role", "onMousemove", "onClick", "withModifiers", "renderSlot", "$slots", "createElementVNode", "toDisplayString", "vShow", "Option", "_export_sfc", "_sfc_render"], "sources": ["../../../../../../packages/components/select/src/option.vue"], "sourcesContent": ["<template>\n  <li\n    v-show=\"visible\"\n    :id=\"id\"\n    :class=\"containerKls\"\n    role=\"option\"\n    :aria-disabled=\"isDisabled || undefined\"\n    :aria-selected=\"itemSelected\"\n    @mousemove=\"hoverItem\"\n    @click.stop=\"selectOptionClick\"\n  >\n    <slot>\n      <span>{{ currentLabel }}</span>\n    </slot>\n  </li>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  nextTick,\n  onBeforeUnmount,\n  reactive,\n  toRefs,\n  unref,\n} from 'vue'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport { useOption } from './useOption'\nimport { COMPONENT_NAME, optionProps } from './option'\n\nimport type { OptionExposed, OptionInternalInstance, OptionStates } from './type'\n\nexport default defineComponent({\n  name: COMPONENT_NAME,\n  componentName: COMPONENT_NAME,\n\n  props: optionProps,\n\n  setup(props) {\n    const ns = useNamespace('select')\n    const id = useId()\n\n    const containerKls = computed(() => [\n      ns.be('dropdown', 'item'),\n      ns.is('disabled', unref(isDisabled)),\n      ns.is('selected', unref(itemSelected)),\n      ns.is('hovering', unref(hover)),\n    ])\n\n    const states = reactive<OptionStates>({\n      index: -1,\n      groupDisabled: false,\n      visible: true,\n      hover: false,\n    })\n\n    const {\n      currentLabel,\n      itemSelected,\n      isDisabled,\n      select,\n      hoverItem,\n      updateOption,\n    } = useOption(props, states)\n\n    const { visible, hover } = toRefs(states)\n\n    const vm = (getCurrentInstance()! as OptionInternalInstance).proxy\n\n    select.onOptionCreate(vm)\n\n    onBeforeUnmount(() => {\n      const key = vm.value\n      const { selected: selectedOptions } = select.states\n      const doesSelected = selectedOptions.some((item) => {\n        return item.value === vm.value\n      })\n      // if option is not selected, remove it from cache\n      nextTick(() => {\n        if (select.states.cachedOptions.get(key) === vm && !doesSelected) {\n          select.states.cachedOptions.delete(key)\n        }\n      })\n      select.onOptionDestroy(key, vm)\n    })\n\n    function selectOptionClick() {\n      if (!isDisabled.value) {\n        select.handleOptionSelect(vm)\n      }\n    }\n\n    return {\n      ns,\n      id,\n      containerKls,\n      currentLabel,\n      itemSelected,\n      isDisabled,\n      select,\n      visible,\n      hover,\n      states,\n\n      hoverItem,\n      updateOption,\n      selectOptionClick,\n    } satisfies OptionExposed\n  },\n})\n</script>\n"], "mappings": ";;;;;;;;AAkCA,MAAKA,SAAA,GAAaC,eAAa;EAC7BC,IAAM,EAAAC,cAAA;EACNC,aAAe,EAAAD,cAAA;EAEfE,KAAO,EAAAC,WAAA;EAEPC,MAAMF,KAAO;IACL,MAAAG,EAAA,GAAKC,YAAA,CAAa,QAAQ;IAChC,MAAMC,EAAA,GAAKC,KAAM;IAEX,MAAAC,YAAA,GAAeC,QAAA,CAAS,MAAM,CAClCL,EAAA,CAAGM,EAAG,aAAY,MAAM,GACxBN,EAAG,CAAAO,EAAA,CAAG,UAAY,EAAAC,KAAA,CAAMC,UAAU,CAAC,GACnCT,EAAG,CAAAO,EAAA,CAAG,UAAY,EAAAC,KAAA,CAAME,YAAY,CAAC,GACrCV,EAAG,CAAAO,EAAA,CAAG,UAAY,EAAAC,KAAA,CAAMG,KAAK,CAAC,EAC/B;IAED,MAAMC,MAAA,GAASC,QAAuB;MACpCC,KAAO;MACPC,aAAe;MACfC,OAAS;MACTL,KAAO;IAAA,CACR;IAEK;MACJM,YAAA;MACAP,YAAA;MACAD,UAAA;MACAS,MAAA;MACAC,SAAA;MACAC;IAAA,CACF,GAAIC,SAAU,CAAAxB,KAAA,EAAOe,MAAM;IAE3B,MAAM;MAAEI,OAAA;MAASL;IAAM,IAAIW,MAAA,CAAOV,MAAM;IAElC,MAAAW,EAAA,GAAMC,kBAAA,EAAiD,CAAAC,KAAA;IAE7DP,MAAA,CAAOQ,cAAA,CAAeH,EAAE;IAExBI,eAAA,CAAgB,MAAM;MACpB,MAAMC,GAAA,GAAML,EAAG,CAAAM,KAAA;MACf,MAAM;QAAEC,QAAA,EAAUC;MAAgB,IAAIb,MAAO,CAAAN,MAAA;MAC7C,MAAMoB,YAAe,GAAAD,eAAA,CAAgBE,IAAK,CAACC,IAAS;QAC3C,OAAAA,IAAA,CAAKL,KAAA,KAAUN,EAAG,CAAAM,KAAA;MAAA,CAC1B;MAEDM,QAAA,CAAS,MAAM;QACT,IAAAjB,MAAA,CAAON,MAAA,CAAOwB,aAAc,CAAAC,GAAA,CAAIT,GAAG,CAAM,KAAAL,EAAA,IAAM,CAACS,YAAc;UACzDd,MAAA,CAAAN,MAAA,CAAOwB,aAAc,CAAAE,MAAA,CAAOV,GAAG;QAAA;MACxC,CACD;MACMV,MAAA,CAAAqB,eAAA,CAAgBX,GAAA,EAAKL,EAAE;IAAA,CAC/B;IAED,SAASiB,iBAAoBA,CAAA;MACvB,KAAC/B,UAAA,CAAWoB,KAAO;QACrBX,MAAA,CAAOuB,kBAAA,CAAmBlB,EAAE;MAAA;IAC9B;IAGK;MACLvB,EAAA;MACAE,EAAA;MACAE,YAAA;MACAa,YAAA;MACAP,YAAA;MACAD,UAAA;MACAS,MAAA;MACAF,OAAA;MACAL,KAAA;MACAC,MAAA;MAEAO,SAAA;MACAC,YAAA;MACAoB;IAAA,CACF;EAAA;AAEJ,CAAC;;sCA9GCE,kBAaK;IAXFxC,EAAI,EAAAyC,IAAA,CAAAzC,EAAA;IACJ0C,KAAA,EAAKC,cAAA,CAAEF,IAAY,CAAAvC,YAAA;IACpB0C,IAAK;IACJ,iBAAeH,IAAc,CAAAlC,UAAA;IAC7B,eAAe,EAAAkC,IAAA,CAAAjC,YAAA;IACfqC,WAAW,EAAAJ,IAAA,CAAAxB,SAAA;IACX6B,OAAA,EAAKC,aAAA,CAAON,IAAiB,CAAAH,iBAAA;EAAA,IAE9BU,UAAA,CAEOP,IAAA,CAAAQ,MAAA,iBAFP,MAEO,CADLC,kBAAA,eAAAC,eAAA,CAAAV,IAAA,CAAA1B,YAAA,MAA+B,gFAAAqC,KAAA,EAAAX,IAAA,CAAA3B,OAAtB,CAAY;AAAA;AAAA,IAAAuC,MAAA,kBAAAC,WAAA,CAAAhE,SAAA,cAAAiE,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}