{"ast": null, "code": "import { defineComponent, ref, computed, watch, onMounted, onBeforeUnmount, provide, renderSlot, unref } from 'vue';\nimport { useTimeoutFn } from '@vueuse/core';\nimport { TOOLTIP_V2_OPEN, tooltipV2RootKey } from './constants.mjs';\nimport { tooltipV2RootProps } from './root2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { isPropAbsent, isNumber } from '../../../utils/types.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTooltipV2Root\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tooltipV2RootProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const _open = ref(props.defaultOpen);\n    const triggerRef = ref(null);\n    const open = computed({\n      get: () => isPropAbsent(props.open) ? _open.value : props.open,\n      set: open2 => {\n        var _a;\n        _open.value = open2;\n        (_a = props[\"onUpdate:open\"]) == null ? void 0 : _a.call(props, open2);\n      }\n    });\n    const isOpenDelayed = computed(() => isNumber(props.delayDuration) && props.delayDuration > 0);\n    const {\n      start: onDelayedOpen,\n      stop: clearTimer\n    } = useTimeoutFn(() => {\n      open.value = true;\n    }, computed(() => props.delayDuration), {\n      immediate: false\n    });\n    const ns = useNamespace(\"tooltip-v2\");\n    const contentId = useId();\n    const onNormalOpen = () => {\n      clearTimer();\n      open.value = true;\n    };\n    const onDelayOpen = () => {\n      unref(isOpenDelayed) ? onDelayedOpen() : onNormalOpen();\n    };\n    const onOpen = onNormalOpen;\n    const onClose = () => {\n      clearTimer();\n      open.value = false;\n    };\n    const onChange = open2 => {\n      var _a;\n      if (open2) {\n        document.dispatchEvent(new CustomEvent(TOOLTIP_V2_OPEN));\n        onOpen();\n      }\n      (_a = props.onOpenChange) == null ? void 0 : _a.call(props, open2);\n    };\n    watch(open, onChange);\n    onMounted(() => {\n      document.addEventListener(TOOLTIP_V2_OPEN, onClose);\n    });\n    onBeforeUnmount(() => {\n      clearTimer();\n      document.removeEventListener(TOOLTIP_V2_OPEN, onClose);\n    });\n    provide(tooltipV2RootKey, {\n      contentId,\n      triggerRef,\n      ns,\n      onClose,\n      onDelayOpen,\n      onOpen\n    });\n    expose({\n      onOpen,\n      onClose\n    });\n    return (_ctx, _cache) => {\n      return renderSlot(_ctx.$slots, \"default\", {\n        open: unref(open)\n      });\n    };\n  }\n});\nvar TooltipV2Root = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"root.vue\"]]);\nexport { TooltipV2Root as default };", "map": {"version": 3, "names": ["name", "_open", "ref", "props", "defaultOpen", "triggerRef", "open", "computed", "get", "isPropAbsent", "value", "set", "open2", "_a", "call", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isNumber", "delayDuration", "start", "onDelayedOpen", "stop", "clearTimer", "useTimeoutFn", "immediate", "ns", "useNamespace", "contentId", "useId", "onNormalOpen", "onDelayOpen", "unref", "onOpen", "onClose", "onChange", "document", "dispatchEvent", "CustomEvent", "TOOLTIP_V2_OPEN", "onOpenChange", "watch", "onMounted", "addEventListener", "onBeforeUnmount", "removeEventListener", "provide", "tooltipV2RootKey", "expose", "_ctx", "_cache", "renderSlot", "$slots", "TooltipV2Root", "_export_sfc", "_sfc_main"], "sources": ["../../../../../../packages/components/tooltip-v2/src/root.vue"], "sourcesContent": ["<template>\n  <slot :open=\"open\" />\n</template>\n\n<script setup lang=\"ts\">\nimport {\n  computed,\n  onBeforeUnmount,\n  onMounted,\n  provide,\n  ref,\n  unref,\n  watch,\n} from 'vue'\nimport { useTimeoutFn } from '@vueuse/core'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport { isNumber, isPropAbsent } from '@element-plus/utils'\nimport { TOOLTIP_V2_OPEN, tooltipV2RootKey } from './constants'\nimport { tooltipV2RootProps } from './root'\n\ndefineOptions({\n  name: 'ElTooltipV2Root',\n})\n\nconst props = defineProps(tooltipV2RootProps)\n\n/**\n * internal open state, when no model value was provided, use this as indicator instead\n */\nconst _open = ref(props.defaultOpen)\nconst triggerRef = ref<HTMLElement | null>(null)\n\nconst open = computed<boolean>({\n  get: () => (isPropAbsent(props.open) ? _open.value : props.open),\n  set: (open) => {\n    _open.value = open\n    props['onUpdate:open']?.(open)\n  },\n})\n\nconst isOpenDelayed = computed(\n  () => isNumber(props.delayDuration) && props.delayDuration > 0\n)\n\nconst { start: onDelayedOpen, stop: clearTimer } = useTimeoutFn(\n  () => {\n    open.value = true\n  },\n  computed(() => props.delayDuration),\n  {\n    immediate: false,\n  }\n)\n\nconst ns = useNamespace('tooltip-v2')\n\nconst contentId = useId()\n\nconst onNormalOpen = () => {\n  clearTimer()\n  open.value = true\n}\n\nconst onDelayOpen = () => {\n  unref(isOpenDelayed) ? onDelayedOpen() : onNormalOpen()\n}\n\nconst onOpen = onNormalOpen\n\nconst onClose = () => {\n  clearTimer()\n  open.value = false\n}\n\nconst onChange = (open: boolean) => {\n  if (open) {\n    document.dispatchEvent(new CustomEvent(TOOLTIP_V2_OPEN))\n    onOpen()\n  }\n\n  props.onOpenChange?.(open)\n}\n\nwatch(open, onChange)\n\nonMounted(() => {\n  // Keeps only 1 tooltip open at a time\n  document.addEventListener(TOOLTIP_V2_OPEN, onClose)\n})\n\nonBeforeUnmount(() => {\n  clearTimer()\n  document.removeEventListener(TOOLTIP_V2_OPEN, onClose)\n})\n\nprovide(tooltipV2RootKey, {\n  contentId,\n  triggerRef,\n  ns,\n\n  onClose,\n  onDelayOpen,\n  onOpen,\n})\n\ndefineExpose({\n  /**\n   * @description open tooltip programmatically\n   */\n  onOpen,\n\n  /**\n   * @description close tooltip programmatically\n   */\n  onClose,\n})\n</script>\n"], "mappings": ";;;;;;;;mCAoBc;EACZA,IAAM;AACR;;;;;;;;IAOM,MAAAC,KAAA,GAAQC,GAAI,CAAAC,KAAA,CAAMC,WAAW;IAC7B,MAAAC,UAAA,GAAaH,GAAA,CAAwB,IAAI;IAE/C,MAAMI,IAAA,GAAOC,QAAkB;MAC7BC,GAAA,EAAKA,CAAA,KAAOC,YAAa,CAAAN,KAAA,CAAMG,IAAI,CAAI,GAAAL,KAAA,CAAMS,KAAA,GAAQP,KAAM,CAAAG,IAAA;MAC3DK,GAAA,EAAMC,KAAS;QACb,IAAAC,EAAM;QACAZ,KAAA,CAAAS,KAAA,GAAAE,KAAA;QACR,CAAAC,EAAA,GAAAV,KAAA,sCAAAU,EAAA,CAAAC,IAAA,CAAAX,KAAA,EAAAS,KAAA;MAAA;IAGF;IAAsB,MAAAG,aACL,GAAAR,QAAmB,OAAAS,QAAA,CAAAb,KAA2B,CAAAc,aAAA,KAAAd,KAAA,CAAAc,aAAA;IAC/D;MAAAC,KAAA,EAAAC,aAAA;MAAAC,IAAA,EAAAC;IAAA,IAAAC,YAAA;MAEAhB,IAAM,CAAEI,KAAA,GAAO,IAAe;IAAqB,GAC3CH,QAAA,OAAAJ,KAAA,CAAAc,aAAA;MACJM,SAAa;IAAA,CACf;IACA,MAAAC,EAAA,GAASC,YAAY,CAAa;IAClC,MAAAC,SAAA,GAAAC,KAAA;IAAA,MACaC,YAAA,GAAAA,CAAA;MACbP,UAAA;MACFf,IAAA,CAAAI,KAAA;IAEA,CAAM;IAEN,MAAMmB,WAAA,GAAkBA,CAAA;MAExBC,KAAA,CAAAf,aAAqB,CAAM,GAAAI,aAAA,KAAAS,YAAA;IACzB,CAAW;IACX,MAAAG,MAAa,GAAAH,YAAA;IACf,MAAAI,OAAA,GAAAA,CAAA;MAEAX,UAAA;MACEf,IAAA,CAAAI,KAAmB;IAAmC,CACxD;IAEA,MAAMuB,QAAS,GAAArB,KAAA;MAEf,IAAMC,EAAA;MACO,IAAAD,KAAA;QACXsB,QAAa,CAAAC,aAAA,KAAAC,WAAA,CAAAC,eAAA;QACfN,MAAA;MAEA;MACE,CAAAlB,EAAA,GAAUV,KAAA,CAAAmC,YAAA,qBAAAzB,EAAA,CAAAC,IAAA,CAAAX,KAAA,EAAAS,KAAA;IACR;IACO2B,KAAA,CAAAjC,IAAA,EAAA2B,QAAA;IACTO,SAAA;MAEAN,QAAA,CAAAO,gBAAyB,CAAAJ,eAAA,EAAAL,OAAA;IAAA,CAC3B;IAEAU,eAAoB;MAEpBrB,UAAgB;MAELa,QAAA,CAAAS,mBAAA,CAAAN,eAAyC,EAAAL,OAAA;IAAA,CACnD;IAEDY,OAAA,CAAAC,gBAAsB;MACTnB,SAAA;MACFrB,UAAA;MACVmB,EAAA;MAEDQ,OAA0B;MACxBH,WAAA;MACAE;IAAA,CACA;IAEAe,MAAA;MACAf,MAAA;MACAC;IAAA,CACD;IAEY,QAAAe,IAAA,EAAAC,MAAA;MAAA,OAAAC,UAAA,CAAAF,IAAA,CAAAG,MAAA;QAAA5C,IAAA,EAAAwB,KAAA,CAAAxB,IAAA;MAAA;IAAA;EAAA;AAAA,CAIX;AAAA,IAAA6C,aAAA,kBAAAC,WAAA,CAAAC,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}