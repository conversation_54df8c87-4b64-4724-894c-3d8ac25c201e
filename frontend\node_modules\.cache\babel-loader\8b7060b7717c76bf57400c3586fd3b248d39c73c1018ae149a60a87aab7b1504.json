{"ast": null, "code": "import { timePanelSharedProps } from './shared.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst panelTimePickerProps = buildProps({\n  ...timePanelSharedProps,\n  datetimeRole: String,\n  parsedValue: {\n    type: definePropType(Object)\n  }\n});\nexport { panelTimePickerProps };", "map": {"version": 3, "names": ["panelTimePickerProps", "buildProps", "timePanelSharedProps", "datetimeRole", "String", "parsedValue", "type", "definePropType", "Object"], "sources": ["../../../../../../../packages/components/time-picker/src/props/panel-time-picker.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { timePanelSharedProps } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const panelTimePickerProps = buildProps({\n  ...timePanelSharedProps,\n  datetimeRole: String,\n  parsedValue: {\n    type: definePropType<Dayjs>(Object),\n  },\n} as const)\n\nexport type PanelTimePickerProps = ExtractPropTypes<typeof panelTimePickerProps>\n"], "mappings": ";;AAEY,MAACA,oBAAoB,GAAGC,UAAU,CAAC;EAC7C,GAAGC,oBAAoB;EACvBC,YAAY,EAAEC,MAAM;EACpBC,WAAW,EAAE;IACXC,IAAI,EAAEC,cAAc,CAACC,MAAM;EAC/B;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}