{"ast": null, "code": "import message from './src/method.mjs';\nexport { messageDefaults, messageEmits, messageProps, messageTypes } from './src/message.mjs';\nimport { withInstallFunction } from '../../utils/vue/install.mjs';\nconst ElMessage = withInstallFunction(message, \"$message\");\nexport { ElMessage, ElMessage as default };", "map": {"version": 3, "names": ["ElMessage", "withInstallFunction", "message"], "sources": ["../../../../../packages/components/message/index.ts"], "sourcesContent": ["import { withInstallFunction } from '@element-plus/utils'\n\nimport Message from './src/method'\n\nexport const ElMessage = withInstallFunction(Message, '$message')\nexport default ElMessage\n\nexport * from './src/message'\n"], "mappings": ";;;AAEY,MAACA,SAAS,GAAGC,mBAAmB,CAACC,OAAO,EAAE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}