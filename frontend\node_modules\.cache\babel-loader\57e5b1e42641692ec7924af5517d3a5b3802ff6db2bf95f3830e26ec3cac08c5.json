{"ast": null, "code": "export default {\n  name: \"NotFoundView\"\n};", "map": {"version": 3, "names": ["name"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\NotFound.vue"], "sourcesContent": ["<template>\n  <div class=\"not-found-container\">\n    <el-result icon=\"error\" title=\"404\" sub-title=\"抱歉，您访问的页面不存在\">\n      <template #extra>\n        <el-button type=\"primary\" @click=\"$router.push('/')\"\n          >返回首页</el-button\n        >\n      </template>\n    </el-result>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"NotFoundView\",\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.not-found-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  padding: 40px;\n}\n</style>\n"], "mappings": "AAaA,eAAe;EACbA,IAAI,EAAE;AACR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}