{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/**\n * 模拟数据\n * 用于前端开发阶段，提供静态数据以便展示界面效果\n */\n\n// 用户信息\nexport const userInfo = {\n  id: 1,\n  studentIdHash: \"202400001\",\n  email: \"<EMAIL>\",\n  phone: \"13800138000\",\n  creditScore: 95,\n  publicKey: \"04d2e670a482970951033c564a5c4c153f5a4c8c7f86ad24ba8b0c7f9c716b3e8b3e6c4e88c1fa0eb7d9b1fb9c4e8c1fa0eb7d9b1fb9c4e8c1fa0eb7d9b1fb9c4e8c1\",\n  privateKey: \"c4e8c1fa0eb7d9b1fb9c4e8c1fa0eb7d9b1fb9c4e8c1fa0eb7d9b1fb9c4e8c1\",\n  lastLogin: \"2023-05-15T08:30:00Z\",\n  createdAt: \"2023-01-01T00:00:00Z\"\n};\n\n// 自习室列表\nexport const rooms = [{\n  id: 1,\n  name: \"1楼东区自习室\",\n  location: \"图书馆1楼东区\",\n  floor: 1,\n  capacity: 36,\n  availableSeats: 25,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆1楼东区自习室，环境安静，靠近入口\"\n}, {\n  id: 2,\n  name: \"1楼西区自习室\",\n  location: \"图书馆1楼西区\",\n  floor: 1,\n  capacity: 36,\n  availableSeats: 18,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆1楼西区自习室，靠近图书借阅区\"\n}, {\n  id: 3,\n  name: \"2楼东区自习室\",\n  location: \"图书馆2楼东区\",\n  floor: 2,\n  capacity: 36,\n  availableSeats: 30,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆2楼东区自习室，环境安静，靠近电梯\"\n}, {\n  id: 4,\n  name: \"2楼西区自习室\",\n  location: \"图书馆2楼西区\",\n  floor: 2,\n  capacity: 36,\n  availableSeats: 22,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆2楼西区自习室，靠近期刊阅览区\"\n}, {\n  id: 5,\n  name: \"3楼东区自习室\",\n  location: \"图书馆3楼东区\",\n  floor: 3,\n  capacity: 36,\n  availableSeats: 15,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆3楼东区自习室，环境安静，靠近电梯\"\n}, {\n  id: 6,\n  name: \"3楼西区自习室\",\n  location: \"图书馆3楼西区\",\n  floor: 3,\n  capacity: 36,\n  availableSeats: 10,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆3楼西区自习室，靠近研讨室\"\n}, {\n  id: 7,\n  name: \"4楼东区自习室\",\n  location: \"图书馆4楼东区\",\n  floor: 4,\n  capacity: 36,\n  availableSeats: 28,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆4楼东区自习室，环境安静，靠近电梯\"\n}, {\n  id: 8,\n  name: \"4楼西区自习室\",\n  location: \"图书馆4楼西区\",\n  floor: 4,\n  capacity: 36,\n  availableSeats: 20,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆4楼西区自习室，靠近多媒体区\"\n}];\n\n// 生成座位数据\nexport const generateSeats = roomId => {\n  const seats = [];\n  for (let row = 1; row <= 6; row++) {\n    for (let col = 1; col <= 6; col++) {\n      // 靠边的座位：第1行、第6行、第1列、第6列\n      const isEdgeSeat = row === 1 || row === 6 || col === 1 || col === 6;\n\n      // 每隔一位有电源插座\n      const isPowerOutlet = isEdgeSeat && (row + col) % 2 === 0;\n\n      // 靠窗的座位：第1行和第6行\n      const isWindowSeat = row === 1 || row === 6;\n\n      // 随机生成座位状态\n      const statusOptions = [\"available\", \"occupied\", \"disabled\"];\n      const randomIndex = Math.floor(Math.random() * 10);\n      let status;\n      if (randomIndex < 7) {\n        status = \"available\"; // 70%概率可用\n      } else if (randomIndex < 9) {\n        status = \"occupied\"; // 20%概率已占用\n      } else {\n        status = \"disabled\"; // 10%概率禁用\n      }\n      seats.push({\n        id: roomId * 100 + row * 10 + col,\n        roomId: roomId,\n        seatNumber: `${row}-${col}`,\n        row: row,\n        column: col,\n        status: status,\n        isPowerOutlet: isPowerOutlet,\n        isWindowSeat: isWindowSeat\n      });\n    }\n  }\n  return seats;\n};\n\n// 我的预约\nexport const myReservations = [{\n  id: 1001,\n  seatId: 105,\n  roomId: 1,\n  roomName: \"1楼东区自习室\",\n  seatNumber: \"1-5\",\n  startTime: \"2023-05-15T09:00:00Z\",\n  endTime: \"2023-05-15T12:00:00Z\",\n  status: \"completed\",\n  checkInTime: \"2023-05-15T09:05:00Z\",\n  checkOutTime: \"2023-05-15T11:55:00Z\",\n  createdAt: \"2023-05-14T15:30:00Z\"\n}, {\n  id: 1002,\n  seatId: 304,\n  roomId: 3,\n  roomName: \"2楼东区自习室\",\n  seatNumber: \"3-4\",\n  startTime: \"2023-05-16T14:00:00Z\",\n  endTime: \"2023-05-16T18:00:00Z\",\n  status: \"pending\",\n  checkInTime: null,\n  checkOutTime: null,\n  createdAt: \"2023-05-15T10:15:00Z\"\n}, {\n  id: 1003,\n  seatId: 206,\n  roomId: 2,\n  roomName: \"1楼西区自习室\",\n  seatNumber: \"2-6\",\n  startTime: \"2023-05-17T09:00:00Z\",\n  endTime: \"2023-05-17T12:00:00Z\",\n  status: \"cancelled\",\n  checkInTime: null,\n  checkOutTime: null,\n  createdAt: \"2023-05-15T16:45:00Z\"\n}];\n\n// 操作记录\nexport const operationRecords = [{\n  id: 1,\n  action: \"reservation_create\",\n  targetType: \"seat\",\n  targetId: 105,\n  description: \"预约了1楼东区自习室的1-5座位\",\n  status: \"success\",\n  ipAddress: \"*************\",\n  createdAt: \"2023-05-14T15:30:00Z\"\n}, {\n  id: 2,\n  action: \"check_in\",\n  targetType: \"reservation\",\n  targetId: 1001,\n  description: \"签到了1楼东区自习室的1-5座位\",\n  status: \"success\",\n  ipAddress: \"*************\",\n  createdAt: \"2023-05-15T09:05:00Z\"\n}, {\n  id: 3,\n  action: \"check_out\",\n  targetType: \"reservation\",\n  targetId: 1001,\n  description: \"签退了1楼东区自习室的1-5座位\",\n  status: \"success\",\n  ipAddress: \"*************\",\n  createdAt: \"2023-05-15T11:55:00Z\"\n}, {\n  id: 4,\n  action: \"reservation_create\",\n  targetType: \"seat\",\n  targetId: 304,\n  description: \"预约了2楼东区自习室的3-4座位\",\n  status: \"success\",\n  ipAddress: \"*************\",\n  createdAt: \"2023-05-15T10:15:00Z\"\n}, {\n  id: 5,\n  action: \"reservation_create\",\n  targetType: \"seat\",\n  targetId: 206,\n  description: \"预约了1楼西区自习室的2-6座位\",\n  status: \"success\",\n  ipAddress: \"*************\",\n  createdAt: \"2023-05-15T16:45:00Z\"\n}, {\n  id: 6,\n  action: \"reservation_cancel\",\n  targetType: \"reservation\",\n  targetId: 1003,\n  description: \"取消了1楼西区自习室的2-6座位预约\",\n  status: \"success\",\n  ipAddress: \"*************\",\n  createdAt: \"2023-05-15T17:30:00Z\"\n}];\n\n// 信誉分记录\nexport const creditRecords = [{\n  id: 1,\n  score: -5,\n  reason: \"预约未签到\",\n  description: \"预约座位后未按时签到\",\n  createdAt: \"2023-04-10T10:00:00Z\"\n}, {\n  id: 2,\n  score: 5,\n  reason: \"按时签退\",\n  description: \"使用座位后按时签退\",\n  createdAt: \"2023-04-15T12:00:00Z\"\n}, {\n  id: 3,\n  score: -10,\n  reason: \"违规占座\",\n  description: \"长时间离开座位未签退\",\n  createdAt: \"2023-04-20T15:30:00Z\"\n}, {\n  id: 4,\n  score: 5,\n  reason: \"按时签退\",\n  description: \"使用座位后按时签退\",\n  createdAt: \"2023-05-05T11:45:00Z\"\n}];\n\n// 时间段\nexport const timeSlots = [{\n  startTime: \"08:00:00\",\n  endTime: \"10:00:00\",\n  isAvailable: true\n}, {\n  startTime: \"10:00:00\",\n  endTime: \"12:00:00\",\n  isAvailable: true\n}, {\n  startTime: \"12:00:00\",\n  endTime: \"14:00:00\",\n  isAvailable: false\n}, {\n  startTime: \"14:00:00\",\n  endTime: \"16:00:00\",\n  isAvailable: true\n}, {\n  startTime: \"16:00:00\",\n  endTime: \"18:00:00\",\n  isAvailable: true\n}, {\n  startTime: \"18:00:00\",\n  endTime: \"20:00:00\",\n  isAvailable: false\n}, {\n  startTime: \"20:00:00\",\n  endTime: \"22:00:00\",\n  isAvailable: true\n}];", "map": {"version": 3, "names": ["userInfo", "id", "studentIdHash", "email", "phone", "creditScore", "public<PERSON>ey", "privateKey", "lastLogin", "createdAt", "rooms", "name", "location", "floor", "capacity", "availableSeats", "openTime", "closeTime", "status", "description", "generateSeats", "roomId", "seats", "row", "col", "isEdgeSeat", "isPowerOutlet", "isWindowSeat", "statusOptions", "randomIndex", "Math", "random", "push", "seatNumber", "column", "myReservations", "seatId", "roomName", "startTime", "endTime", "checkInTime", "checkOutTime", "operationRecords", "action", "targetType", "targetId", "ip<PERSON><PERSON><PERSON>", "creditRecords", "score", "reason", "timeSlots", "isAvailable"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/src/mock/data.js"], "sourcesContent": ["/**\n * 模拟数据\n * 用于前端开发阶段，提供静态数据以便展示界面效果\n */\n\n// 用户信息\nexport const userInfo = {\n  id: 1,\n  studentIdHash: \"202400001\",\n  email: \"<EMAIL>\",\n  phone: \"13800138000\",\n  creditScore: 95,\n  publicKey:\n    \"04d2e670a482970951033c564a5c4c153f5a4c8c7f86ad24ba8b0c7f9c716b3e8b3e6c4e88c1fa0eb7d9b1fb9c4e8c1fa0eb7d9b1fb9c4e8c1fa0eb7d9b1fb9c4e8c1\",\n  privateKey: \"c4e8c1fa0eb7d9b1fb9c4e8c1fa0eb7d9b1fb9c4e8c1fa0eb7d9b1fb9c4e8c1\",\n  lastLogin: \"2023-05-15T08:30:00Z\",\n  createdAt: \"2023-01-01T00:00:00Z\",\n};\n\n// 自习室列表\nexport const rooms = [\n  {\n    id: 1,\n    name: \"1楼东区自习室\",\n    location: \"图书馆1楼东区\",\n    floor: 1,\n    capacity: 36,\n    availableSeats: 25,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆1楼东区自习室，环境安静，靠近入口\",\n  },\n  {\n    id: 2,\n    name: \"1楼西区自习室\",\n    location: \"图书馆1楼西区\",\n    floor: 1,\n    capacity: 36,\n    availableSeats: 18,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆1楼西区自习室，靠近图书借阅区\",\n  },\n  {\n    id: 3,\n    name: \"2楼东区自习室\",\n    location: \"图书馆2楼东区\",\n    floor: 2,\n    capacity: 36,\n    availableSeats: 30,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆2楼东区自习室，环境安静，靠近电梯\",\n  },\n  {\n    id: 4,\n    name: \"2楼西区自习室\",\n    location: \"图书馆2楼西区\",\n    floor: 2,\n    capacity: 36,\n    availableSeats: 22,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆2楼西区自习室，靠近期刊阅览区\",\n  },\n  {\n    id: 5,\n    name: \"3楼东区自习室\",\n    location: \"图书馆3楼东区\",\n    floor: 3,\n    capacity: 36,\n    availableSeats: 15,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆3楼东区自习室，环境安静，靠近电梯\",\n  },\n  {\n    id: 6,\n    name: \"3楼西区自习室\",\n    location: \"图书馆3楼西区\",\n    floor: 3,\n    capacity: 36,\n    availableSeats: 10,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆3楼西区自习室，靠近研讨室\",\n  },\n  {\n    id: 7,\n    name: \"4楼东区自习室\",\n    location: \"图书馆4楼东区\",\n    floor: 4,\n    capacity: 36,\n    availableSeats: 28,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆4楼东区自习室，环境安静，靠近电梯\",\n  },\n  {\n    id: 8,\n    name: \"4楼西区自习室\",\n    location: \"图书馆4楼西区\",\n    floor: 4,\n    capacity: 36,\n    availableSeats: 20,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆4楼西区自习室，靠近多媒体区\",\n  },\n];\n\n// 生成座位数据\nexport const generateSeats = (roomId) => {\n  const seats = [];\n  for (let row = 1; row <= 6; row++) {\n    for (let col = 1; col <= 6; col++) {\n      // 靠边的座位：第1行、第6行、第1列、第6列\n      const isEdgeSeat = row === 1 || row === 6 || col === 1 || col === 6;\n\n      // 每隔一位有电源插座\n      const isPowerOutlet = isEdgeSeat && (row + col) % 2 === 0;\n\n      // 靠窗的座位：第1行和第6行\n      const isWindowSeat = row === 1 || row === 6;\n\n      // 随机生成座位状态\n      const statusOptions = [\"available\", \"occupied\", \"disabled\"];\n      const randomIndex = Math.floor(Math.random() * 10);\n      let status;\n      if (randomIndex < 7) {\n        status = \"available\"; // 70%概率可用\n      } else if (randomIndex < 9) {\n        status = \"occupied\"; // 20%概率已占用\n      } else {\n        status = \"disabled\"; // 10%概率禁用\n      }\n\n      seats.push({\n        id: roomId * 100 + row * 10 + col,\n        roomId: roomId,\n        seatNumber: `${row}-${col}`,\n        row: row,\n        column: col,\n        status: status,\n        isPowerOutlet: isPowerOutlet,\n        isWindowSeat: isWindowSeat,\n      });\n    }\n  }\n  return seats;\n};\n\n// 我的预约\nexport const myReservations = [\n  {\n    id: 1001,\n    seatId: 105,\n    roomId: 1,\n    roomName: \"1楼东区自习室\",\n    seatNumber: \"1-5\",\n    startTime: \"2023-05-15T09:00:00Z\",\n    endTime: \"2023-05-15T12:00:00Z\",\n    status: \"completed\",\n    checkInTime: \"2023-05-15T09:05:00Z\",\n    checkOutTime: \"2023-05-15T11:55:00Z\",\n    createdAt: \"2023-05-14T15:30:00Z\",\n  },\n  {\n    id: 1002,\n    seatId: 304,\n    roomId: 3,\n    roomName: \"2楼东区自习室\",\n    seatNumber: \"3-4\",\n    startTime: \"2023-05-16T14:00:00Z\",\n    endTime: \"2023-05-16T18:00:00Z\",\n    status: \"pending\",\n    checkInTime: null,\n    checkOutTime: null,\n    createdAt: \"2023-05-15T10:15:00Z\",\n  },\n  {\n    id: 1003,\n    seatId: 206,\n    roomId: 2,\n    roomName: \"1楼西区自习室\",\n    seatNumber: \"2-6\",\n    startTime: \"2023-05-17T09:00:00Z\",\n    endTime: \"2023-05-17T12:00:00Z\",\n    status: \"cancelled\",\n    checkInTime: null,\n    checkOutTime: null,\n    createdAt: \"2023-05-15T16:45:00Z\",\n  },\n];\n\n// 操作记录\nexport const operationRecords = [\n  {\n    id: 1,\n    action: \"reservation_create\",\n    targetType: \"seat\",\n    targetId: 105,\n    description: \"预约了1楼东区自习室的1-5座位\",\n    status: \"success\",\n    ipAddress: \"*************\",\n    createdAt: \"2023-05-14T15:30:00Z\",\n  },\n  {\n    id: 2,\n    action: \"check_in\",\n    targetType: \"reservation\",\n    targetId: 1001,\n    description: \"签到了1楼东区自习室的1-5座位\",\n    status: \"success\",\n    ipAddress: \"*************\",\n    createdAt: \"2023-05-15T09:05:00Z\",\n  },\n  {\n    id: 3,\n    action: \"check_out\",\n    targetType: \"reservation\",\n    targetId: 1001,\n    description: \"签退了1楼东区自习室的1-5座位\",\n    status: \"success\",\n    ipAddress: \"*************\",\n    createdAt: \"2023-05-15T11:55:00Z\",\n  },\n  {\n    id: 4,\n    action: \"reservation_create\",\n    targetType: \"seat\",\n    targetId: 304,\n    description: \"预约了2楼东区自习室的3-4座位\",\n    status: \"success\",\n    ipAddress: \"*************\",\n    createdAt: \"2023-05-15T10:15:00Z\",\n  },\n  {\n    id: 5,\n    action: \"reservation_create\",\n    targetType: \"seat\",\n    targetId: 206,\n    description: \"预约了1楼西区自习室的2-6座位\",\n    status: \"success\",\n    ipAddress: \"*************\",\n    createdAt: \"2023-05-15T16:45:00Z\",\n  },\n  {\n    id: 6,\n    action: \"reservation_cancel\",\n    targetType: \"reservation\",\n    targetId: 1003,\n    description: \"取消了1楼西区自习室的2-6座位预约\",\n    status: \"success\",\n    ipAddress: \"*************\",\n    createdAt: \"2023-05-15T17:30:00Z\",\n  },\n];\n\n// 信誉分记录\nexport const creditRecords = [\n  {\n    id: 1,\n    score: -5,\n    reason: \"预约未签到\",\n    description: \"预约座位后未按时签到\",\n    createdAt: \"2023-04-10T10:00:00Z\",\n  },\n  {\n    id: 2,\n    score: 5,\n    reason: \"按时签退\",\n    description: \"使用座位后按时签退\",\n    createdAt: \"2023-04-15T12:00:00Z\",\n  },\n  {\n    id: 3,\n    score: -10,\n    reason: \"违规占座\",\n    description: \"长时间离开座位未签退\",\n    createdAt: \"2023-04-20T15:30:00Z\",\n  },\n  {\n    id: 4,\n    score: 5,\n    reason: \"按时签退\",\n    description: \"使用座位后按时签退\",\n    createdAt: \"2023-05-05T11:45:00Z\",\n  },\n];\n\n// 时间段\nexport const timeSlots = [\n  { startTime: \"08:00:00\", endTime: \"10:00:00\", isAvailable: true },\n  { startTime: \"10:00:00\", endTime: \"12:00:00\", isAvailable: true },\n  { startTime: \"12:00:00\", endTime: \"14:00:00\", isAvailable: false },\n  { startTime: \"14:00:00\", endTime: \"16:00:00\", isAvailable: true },\n  { startTime: \"16:00:00\", endTime: \"18:00:00\", isAvailable: true },\n  { startTime: \"18:00:00\", endTime: \"20:00:00\", isAvailable: false },\n  { startTime: \"20:00:00\", endTime: \"22:00:00\", isAvailable: true },\n];\n"], "mappings": ";AAAA;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,QAAQ,GAAG;EACtBC,EAAE,EAAE,CAAC;EACLC,aAAa,EAAE,WAAW;EAC1BC,KAAK,EAAE,qBAAqB;EAC5BC,KAAK,EAAE,aAAa;EACpBC,WAAW,EAAE,EAAE;EACfC,SAAS,EACP,uIAAuI;EACzIC,UAAU,EAAE,iEAAiE;EAC7EC,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE;AACb,CAAC;;AAED;AACA,OAAO,MAAMC,KAAK,GAAG,CACnB;EACET,EAAE,EAAE,CAAC;EACLU,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACElB,EAAE,EAAE,CAAC;EACLU,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACElB,EAAE,EAAE,CAAC;EACLU,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACElB,EAAE,EAAE,CAAC;EACLU,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACElB,EAAE,EAAE,CAAC;EACLU,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACElB,EAAE,EAAE,CAAC;EACLU,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACElB,EAAE,EAAE,CAAC;EACLU,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACElB,EAAE,EAAE,CAAC;EACLU,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,CACF;;AAED;AACA,OAAO,MAAMC,aAAa,GAAIC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAG,EAAE;EAChB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;IACjC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;MACjC;MACA,MAAMC,UAAU,GAAGF,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC,IAAIC,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC;;MAEnE;MACA,MAAME,aAAa,GAAGD,UAAU,IAAI,CAACF,GAAG,GAAGC,GAAG,IAAI,CAAC,KAAK,CAAC;;MAEzD;MACA,MAAMG,YAAY,GAAGJ,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC;;MAE3C;MACA,MAAMK,aAAa,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC;MAC3D,MAAMC,WAAW,GAAGC,IAAI,CAACjB,KAAK,CAACiB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;MAClD,IAAIb,MAAM;MACV,IAAIW,WAAW,GAAG,CAAC,EAAE;QACnBX,MAAM,GAAG,WAAW,CAAC,CAAC;MACxB,CAAC,MAAM,IAAIW,WAAW,GAAG,CAAC,EAAE;QAC1BX,MAAM,GAAG,UAAU,CAAC,CAAC;MACvB,CAAC,MAAM;QACLA,MAAM,GAAG,UAAU,CAAC,CAAC;MACvB;MAEAI,KAAK,CAACU,IAAI,CAAC;QACT/B,EAAE,EAAEoB,MAAM,GAAG,GAAG,GAAGE,GAAG,GAAG,EAAE,GAAGC,GAAG;QACjCH,MAAM,EAAEA,MAAM;QACdY,UAAU,EAAE,GAAGV,GAAG,IAAIC,GAAG,EAAE;QAC3BD,GAAG,EAAEA,GAAG;QACRW,MAAM,EAAEV,GAAG;QACXN,MAAM,EAAEA,MAAM;QACdQ,aAAa,EAAEA,aAAa;QAC5BC,YAAY,EAAEA;MAChB,CAAC,CAAC;IACJ;EACF;EACA,OAAOL,KAAK;AACd,CAAC;;AAED;AACA,OAAO,MAAMa,cAAc,GAAG,CAC5B;EACElC,EAAE,EAAE,IAAI;EACRmC,MAAM,EAAE,GAAG;EACXf,MAAM,EAAE,CAAC;EACTgB,QAAQ,EAAE,SAAS;EACnBJ,UAAU,EAAE,KAAK;EACjBK,SAAS,EAAE,sBAAsB;EACjCC,OAAO,EAAE,sBAAsB;EAC/BrB,MAAM,EAAE,WAAW;EACnBsB,WAAW,EAAE,sBAAsB;EACnCC,YAAY,EAAE,sBAAsB;EACpChC,SAAS,EAAE;AACb,CAAC,EACD;EACER,EAAE,EAAE,IAAI;EACRmC,MAAM,EAAE,GAAG;EACXf,MAAM,EAAE,CAAC;EACTgB,QAAQ,EAAE,SAAS;EACnBJ,UAAU,EAAE,KAAK;EACjBK,SAAS,EAAE,sBAAsB;EACjCC,OAAO,EAAE,sBAAsB;EAC/BrB,MAAM,EAAE,SAAS;EACjBsB,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBhC,SAAS,EAAE;AACb,CAAC,EACD;EACER,EAAE,EAAE,IAAI;EACRmC,MAAM,EAAE,GAAG;EACXf,MAAM,EAAE,CAAC;EACTgB,QAAQ,EAAE,SAAS;EACnBJ,UAAU,EAAE,KAAK;EACjBK,SAAS,EAAE,sBAAsB;EACjCC,OAAO,EAAE,sBAAsB;EAC/BrB,MAAM,EAAE,WAAW;EACnBsB,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBhC,SAAS,EAAE;AACb,CAAC,CACF;;AAED;AACA,OAAO,MAAMiC,gBAAgB,GAAG,CAC9B;EACEzC,EAAE,EAAE,CAAC;EACL0C,MAAM,EAAE,oBAAoB;EAC5BC,UAAU,EAAE,MAAM;EAClBC,QAAQ,EAAE,GAAG;EACb1B,WAAW,EAAE,kBAAkB;EAC/BD,MAAM,EAAE,SAAS;EACjB4B,SAAS,EAAE,eAAe;EAC1BrC,SAAS,EAAE;AACb,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACL0C,MAAM,EAAE,UAAU;EAClBC,UAAU,EAAE,aAAa;EACzBC,QAAQ,EAAE,IAAI;EACd1B,WAAW,EAAE,kBAAkB;EAC/BD,MAAM,EAAE,SAAS;EACjB4B,SAAS,EAAE,eAAe;EAC1BrC,SAAS,EAAE;AACb,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACL0C,MAAM,EAAE,WAAW;EACnBC,UAAU,EAAE,aAAa;EACzBC,QAAQ,EAAE,IAAI;EACd1B,WAAW,EAAE,kBAAkB;EAC/BD,MAAM,EAAE,SAAS;EACjB4B,SAAS,EAAE,eAAe;EAC1BrC,SAAS,EAAE;AACb,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACL0C,MAAM,EAAE,oBAAoB;EAC5BC,UAAU,EAAE,MAAM;EAClBC,QAAQ,EAAE,GAAG;EACb1B,WAAW,EAAE,kBAAkB;EAC/BD,MAAM,EAAE,SAAS;EACjB4B,SAAS,EAAE,eAAe;EAC1BrC,SAAS,EAAE;AACb,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACL0C,MAAM,EAAE,oBAAoB;EAC5BC,UAAU,EAAE,MAAM;EAClBC,QAAQ,EAAE,GAAG;EACb1B,WAAW,EAAE,kBAAkB;EAC/BD,MAAM,EAAE,SAAS;EACjB4B,SAAS,EAAE,eAAe;EAC1BrC,SAAS,EAAE;AACb,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACL0C,MAAM,EAAE,oBAAoB;EAC5BC,UAAU,EAAE,aAAa;EACzBC,QAAQ,EAAE,IAAI;EACd1B,WAAW,EAAE,oBAAoB;EACjCD,MAAM,EAAE,SAAS;EACjB4B,SAAS,EAAE,eAAe;EAC1BrC,SAAS,EAAE;AACb,CAAC,CACF;;AAED;AACA,OAAO,MAAMsC,aAAa,GAAG,CAC3B;EACE9C,EAAE,EAAE,CAAC;EACL+C,KAAK,EAAE,CAAC,CAAC;EACTC,MAAM,EAAE,OAAO;EACf9B,WAAW,EAAE,YAAY;EACzBV,SAAS,EAAE;AACb,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACL+C,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,MAAM;EACd9B,WAAW,EAAE,WAAW;EACxBV,SAAS,EAAE;AACb,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACL+C,KAAK,EAAE,CAAC,EAAE;EACVC,MAAM,EAAE,MAAM;EACd9B,WAAW,EAAE,YAAY;EACzBV,SAAS,EAAE;AACb,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACL+C,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,MAAM;EACd9B,WAAW,EAAE,WAAW;EACxBV,SAAS,EAAE;AACb,CAAC,CACF;;AAED;AACA,OAAO,MAAMyC,SAAS,GAAG,CACvB;EAAEZ,SAAS,EAAE,UAAU;EAAEC,OAAO,EAAE,UAAU;EAAEY,WAAW,EAAE;AAAK,CAAC,EACjE;EAAEb,SAAS,EAAE,UAAU;EAAEC,OAAO,EAAE,UAAU;EAAEY,WAAW,EAAE;AAAK,CAAC,EACjE;EAAEb,SAAS,EAAE,UAAU;EAAEC,OAAO,EAAE,UAAU;EAAEY,WAAW,EAAE;AAAM,CAAC,EAClE;EAAEb,SAAS,EAAE,UAAU;EAAEC,OAAO,EAAE,UAAU;EAAEY,WAAW,EAAE;AAAK,CAAC,EACjE;EAAEb,SAAS,EAAE,UAAU;EAAEC,OAAO,EAAE,UAAU;EAAEY,WAAW,EAAE;AAAK,CAAC,EACjE;EAAEb,SAAS,EAAE,UAAU;EAAEC,OAAO,EAAE,UAAU;EAAEY,WAAW,EAAE;AAAM,CAAC,EAClE;EAAEb,SAAS,EAAE,UAAU;EAAEC,OAAO,EAAE,UAAU;EAAEY,WAAW,EAAE;AAAK,CAAC,CAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}