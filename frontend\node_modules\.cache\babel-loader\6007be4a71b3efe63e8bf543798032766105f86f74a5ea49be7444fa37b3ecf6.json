{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"app-sidebar\"\n};\nconst _hoisted_2 = {\n  class: \"sidebar-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Monitor = _resolveComponent(\"Monitor\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_menu_item = _resolveComponent(\"el-menu-item\");\n  const _component_SeatIcon = _resolveComponent(\"SeatIcon\");\n  const _component_el_sub_menu = _resolveComponent(\"el-sub-menu\");\n  const _component_User = _resolveComponent(\"User\");\n  const _component_QuestionFilled = _resolveComponent(\"QuestionFilled\");\n  const _component_el_menu = _resolveComponent(\"el-menu\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_menu, {\n    \"default-active\": $setup.activeMenu,\n    class: \"sidebar-menu\",\n    router: true,\n    collapse: $setup.isCollapse\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n      index: \"/dashboard\"\n    }, {\n      title: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"首页\")])),\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Monitor)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_sub_menu, {\n      index: \"/seat\"\n    }, {\n      title: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_SeatIcon)]),\n        _: 1 /* STABLE */\n      }), _cache[1] || (_cache[1] = _createElementVNode(\"span\", null, \"座位管理\", -1 /* HOISTED */))]),\n      default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n        index: \"/seat/rooms\"\n      }, {\n        default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"自习室列表\")])),\n        _: 1 /* STABLE */,\n        __: [2]\n      }), _createVNode(_component_el_menu_item, {\n        index: \"/seat/map\"\n      }, {\n        default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"座位地图\")])),\n        _: 1 /* STABLE */,\n        __: [3]\n      }), _createVNode(_component_el_menu_item, {\n        index: \"/seat/reservation\"\n      }, {\n        default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"预约座位\")])),\n        _: 1 /* STABLE */,\n        __: [4]\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_sub_menu, {\n      index: \"/user\"\n    }, {\n      title: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_User)]),\n        _: 1 /* STABLE */\n      }), _cache[5] || (_cache[5] = _createElementVNode(\"span\", null, \"个人中心\", -1 /* HOISTED */))]),\n      default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n        index: \"/user/profile\"\n      }, {\n        default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"个人信息\")])),\n        _: 1 /* STABLE */,\n        __: [6]\n      }), _createVNode(_component_el_menu_item, {\n        index: \"/user/reservations\"\n      }, {\n        default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\"我的预约\")])),\n        _: 1 /* STABLE */,\n        __: [7]\n      }), _createVNode(_component_el_menu_item, {\n        index: \"/user/records\"\n      }, {\n        default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\"操作记录\")])),\n        _: 1 /* STABLE */,\n        __: [8]\n      }), _createVNode(_component_el_menu_item, {\n        index: \"/user/credit\"\n      }, {\n        default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"信誉分记录\")])),\n        _: 1 /* STABLE */,\n        __: [9]\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_menu_item, {\n      index: \"/help\"\n    }, {\n      title: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"帮助中心\")])),\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_QuestionFilled)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"default-active\", \"collapse\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n    type: \"text\",\n    icon: $setup.isCollapse ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left',\n    onClick: $setup.toggleCollapse\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.isCollapse ? \"展开\" : \"收起\"), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"icon\", \"onClick\"])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_menu", "$setup", "activeMenu", "router", "collapse", "isCollapse", "default", "_withCtx", "_component_el_menu_item", "index", "title", "_cache", "_createTextVNode", "_component_el_icon", "_component_Monitor", "_", "_component_el_sub_menu", "_component_SeatIcon", "_createElementVNode", "__", "_component_User", "_component_QuestionFilled", "_hoisted_2", "_component_el_button", "type", "icon", "onClick", "toggleCollapse", "_toDisplayString"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Sidebar.vue"], "sourcesContent": ["<template>\n  <div class=\"app-sidebar\">\n    <el-menu\n      :default-active=\"activeMenu\"\n      class=\"sidebar-menu\"\n      :router=\"true\"\n      :collapse=\"isCollapse\"\n    >\n      <el-menu-item index=\"/dashboard\">\n        <el-icon><Monitor /></el-icon>\n        <template #title>首页</template>\n      </el-menu-item>\n\n      <el-sub-menu index=\"/seat\">\n        <template #title>\n          <el-icon><SeatIcon /></el-icon>\n          <span>座位管理</span>\n        </template>\n        <el-menu-item index=\"/seat/rooms\">自习室列表</el-menu-item>\n        <el-menu-item index=\"/seat/map\">座位地图</el-menu-item>\n        <el-menu-item index=\"/seat/reservation\">预约座位</el-menu-item>\n      </el-sub-menu>\n\n      <el-sub-menu index=\"/user\">\n        <template #title>\n          <el-icon><User /></el-icon>\n          <span>个人中心</span>\n        </template>\n        <el-menu-item index=\"/user/profile\">个人信息</el-menu-item>\n        <el-menu-item index=\"/user/reservations\">我的预约</el-menu-item>\n        <el-menu-item index=\"/user/records\">操作记录</el-menu-item>\n        <el-menu-item index=\"/user/credit\">信誉分记录</el-menu-item>\n      </el-sub-menu>\n\n      <el-menu-item index=\"/help\">\n        <el-icon><QuestionFilled /></el-icon>\n        <template #title>帮助中心</template>\n      </el-menu-item>\n    </el-menu>\n\n    <div class=\"sidebar-footer\">\n      <el-button\n        type=\"text\"\n        :icon=\"isCollapse ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'\"\n        @click=\"toggleCollapse\"\n      >\n        {{ isCollapse ? \"展开\" : \"收起\" }}\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, h } from \"vue\";\nimport { useRoute } from \"vue-router\";\nimport { Monitor, User, QuestionFilled } from \"@element-plus/icons-vue\";\n\n// 自定义座位图标组件 - 使用Vue 3的函数式组件\nconst SeatIcon = {\n  name: \"SeatIcon\",\n  setup() {\n    return () =>\n      h(\n        \"svg\",\n        {\n          viewBox: \"0 0 24 24\",\n          width: \"1em\",\n          height: \"1em\",\n        },\n        [\n          h(\"path\", {\n            fill: \"currentColor\",\n            d: \"M4 18v-3h16v3h2v-6H2v6h2zm10-9h4V6h-4v3zm-6 0h4V6H8v3zM4 5v3h2V5h12v3h2V5c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2z\",\n          }),\n        ]\n      );\n  },\n};\n\nexport default {\n  name: \"AppSidebar\",\n  components: {\n    Monitor,\n    User,\n    QuestionFilled,\n    SeatIcon,\n  },\n  setup() {\n    const route = useRoute();\n    const isCollapse = ref(false);\n\n    const activeMenu = computed(() => {\n      const { path } = route;\n      return path;\n    });\n\n    const toggleCollapse = () => {\n      isCollapse.value = !isCollapse.value;\n    };\n\n    return {\n      activeMenu,\n      isCollapse,\n      toggleCollapse,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-sidebar {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  border-right: 1px solid #e6e6e6;\n  background-color: #fff;\n}\n\n.sidebar-menu {\n  flex: 1;\n  border-right: none;\n}\n\n.sidebar-footer {\n  padding: 10px;\n  border-top: 1px solid #e6e6e6;\n  text-align: center;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAa;;EAuCjBA,KAAK,EAAC;AAAgB;;;;;;;;;;;uBAvC7BC,mBAAA,CAgDM,OAhDNC,UAgDM,GA/CJC,YAAA,CAoCUC,kBAAA;IAnCP,gBAAc,EAAEC,MAAA,CAAAC,UAAU;IAC3BN,KAAK,EAAC,cAAc;IACnBO,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAEH,MAAA,CAAAI;;IANjBC,OAAA,EAAAC,QAAA,CAQM,MAGe,CAHfR,YAAA,CAGeS,uBAAA;MAHDC,KAAK,EAAC;IAAY;MAEnBC,KAAK,EAAAH,QAAA,CAAC,MAAEI,MAAA,QAAAA,MAAA,OAV3BC,gBAAA,CAUyB,IAAE,E;MAV3BN,OAAA,EAAAC,QAAA,CASQ,MAA8B,CAA9BR,YAAA,CAA8Bc,kBAAA;QATtCP,OAAA,EAAAC,QAAA,CASiB,MAAW,CAAXR,YAAA,CAAWe,kBAAA,E;QAT5BC,CAAA;;MAAAA,CAAA;QAaMhB,YAAA,CAQciB,sBAAA;MARDP,KAAK,EAAC;IAAO;MACbC,KAAK,EAAAH,QAAA,CACd,MAA+B,CAA/BR,YAAA,CAA+Bc,kBAAA;QAfzCP,OAAA,EAAAC,QAAA,CAemB,MAAY,CAAZR,YAAA,CAAYkB,mBAAA,E;QAf/BF,CAAA;oCAgBUG,mBAAA,CAAiB,cAAX,MAAI,qB;MAhBpBZ,OAAA,EAAAC,QAAA,CAkBQ,MAAsD,CAAtDR,YAAA,CAAsDS,uBAAA;QAAxCC,KAAK,EAAC;MAAa;QAlBzCH,OAAA,EAAAC,QAAA,CAkB0C,MAAKI,MAAA,QAAAA,MAAA,OAlB/CC,gBAAA,CAkB0C,OAAK,E;QAlB/CG,CAAA;QAAAI,EAAA;UAmBQpB,YAAA,CAAmDS,uBAAA;QAArCC,KAAK,EAAC;MAAW;QAnBvCH,OAAA,EAAAC,QAAA,CAmBwC,MAAII,MAAA,QAAAA,MAAA,OAnB5CC,gBAAA,CAmBwC,MAAI,E;QAnB5CG,CAAA;QAAAI,EAAA;UAoBQpB,YAAA,CAA2DS,uBAAA;QAA7CC,KAAK,EAAC;MAAmB;QApB/CH,OAAA,EAAAC,QAAA,CAoBgD,MAAII,MAAA,QAAAA,MAAA,OApBpDC,gBAAA,CAoBgD,MAAI,E;QApBpDG,CAAA;QAAAI,EAAA;;MAAAJ,CAAA;QAuBMhB,YAAA,CASciB,sBAAA;MATDP,KAAK,EAAC;IAAO;MACbC,KAAK,EAAAH,QAAA,CACd,MAA2B,CAA3BR,YAAA,CAA2Bc,kBAAA;QAzBrCP,OAAA,EAAAC,QAAA,CAyBmB,MAAQ,CAARR,YAAA,CAAQqB,eAAA,E;QAzB3BL,CAAA;oCA0BUG,mBAAA,CAAiB,cAAX,MAAI,qB;MA1BpBZ,OAAA,EAAAC,QAAA,CA4BQ,MAAuD,CAAvDR,YAAA,CAAuDS,uBAAA;QAAzCC,KAAK,EAAC;MAAe;QA5B3CH,OAAA,EAAAC,QAAA,CA4B4C,MAAII,MAAA,QAAAA,MAAA,OA5BhDC,gBAAA,CA4B4C,MAAI,E;QA5BhDG,CAAA;QAAAI,EAAA;UA6BQpB,YAAA,CAA4DS,uBAAA;QAA9CC,KAAK,EAAC;MAAoB;QA7BhDH,OAAA,EAAAC,QAAA,CA6BiD,MAAII,MAAA,QAAAA,MAAA,OA7BrDC,gBAAA,CA6BiD,MAAI,E;QA7BrDG,CAAA;QAAAI,EAAA;UA8BQpB,YAAA,CAAuDS,uBAAA;QAAzCC,KAAK,EAAC;MAAe;QA9B3CH,OAAA,EAAAC,QAAA,CA8B4C,MAAII,MAAA,QAAAA,MAAA,OA9BhDC,gBAAA,CA8B4C,MAAI,E;QA9BhDG,CAAA;QAAAI,EAAA;UA+BQpB,YAAA,CAAuDS,uBAAA;QAAzCC,KAAK,EAAC;MAAc;QA/B1CH,OAAA,EAAAC,QAAA,CA+B2C,MAAKI,MAAA,QAAAA,MAAA,OA/BhDC,gBAAA,CA+B2C,OAAK,E;QA/BhDG,CAAA;QAAAI,EAAA;;MAAAJ,CAAA;QAkCMhB,YAAA,CAGeS,uBAAA;MAHDC,KAAK,EAAC;IAAO;MAEdC,KAAK,EAAAH,QAAA,CAAC,MAAII,MAAA,SAAAA,MAAA,QApC7BC,gBAAA,CAoCyB,MAAI,E;MApC7BN,OAAA,EAAAC,QAAA,CAmCQ,MAAqC,CAArCR,YAAA,CAAqCc,kBAAA;QAnC7CP,OAAA,EAAAC,QAAA,CAmCiB,MAAkB,CAAlBR,YAAA,CAAkBsB,yBAAA,E;QAnCnCN,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;qDAwCIG,mBAAA,CAQM,OARNI,UAQM,GAPJvB,YAAA,CAMYwB,oBAAA;IALVC,IAAI,EAAC,MAAM;IACVC,IAAI,EAAExB,MAAA,CAAAI,UAAU;IAChBqB,OAAK,EAAEzB,MAAA,CAAA0B;;IA5ChBrB,OAAA,EAAAC,QAAA,CA8CQ,MAA8B,CA9CtCK,gBAAA,CAAAgB,gBAAA,CA8CW3B,MAAA,CAAAI,UAAU,+B;IA9CrBU,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}