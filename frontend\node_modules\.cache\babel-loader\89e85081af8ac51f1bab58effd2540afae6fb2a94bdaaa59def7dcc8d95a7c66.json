{"ast": null, "code": "import { defineComponent, useSlots, computed, ref, provide, openBlock, createBlock, unref, withCtx, createVNode, Transition, withDirectives, createElementVNode, normalizeClass, normalizeStyle, mergeProps, createSlots, renderSlot, createCommentVNode, vShow } from 'vue';\nimport { ElOverlay } from '../../overlay/index.mjs';\nimport ElFocusTrap from '../../focus-trap/src/focus-trap.mjs';\nimport { ElTeleport } from '../../teleport/index.mjs';\nimport ElDialogContent from './dialog-content2.mjs';\nimport { dialogInjectionKey } from './constants.mjs';\nimport { dialogProps, dialogEmits } from './dialog.mjs';\nimport { useDialog } from './use-dialog.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useDeprecated } from '../../../hooks/use-deprecated/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useSameTarget } from '../../../hooks/use-same-target/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElDialog\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: dialogProps,\n  emits: dialogEmits,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const slots = useSlots();\n    useDeprecated({\n      scope: \"el-dialog\",\n      from: \"the title slot\",\n      replacement: \"the header slot\",\n      version: \"3.0.0\",\n      ref: \"https://element-plus.org/en-US/component/dialog.html#slots\"\n    }, computed(() => !!slots.title));\n    const ns = useNamespace(\"dialog\");\n    const dialogRef = ref();\n    const headerRef = ref();\n    const dialogContentRef = ref();\n    const {\n      visible,\n      titleId,\n      bodyId,\n      style,\n      overlayDialogStyle,\n      rendered,\n      zIndex,\n      afterEnter,\n      afterLeave,\n      beforeLeave,\n      handleClose,\n      onModalClick,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      onCloseRequested,\n      onFocusoutPrevented\n    } = useDialog(props, dialogRef);\n    provide(dialogInjectionKey, {\n      dialogRef,\n      headerRef,\n      bodyId,\n      ns,\n      rendered,\n      style\n    });\n    const overlayEvent = useSameTarget(onModalClick);\n    const draggable = computed(() => props.draggable && !props.fullscreen);\n    const resetPosition = () => {\n      var _a;\n      (_a = dialogContentRef.value) == null ? void 0 : _a.resetPosition();\n    };\n    expose({\n      visible,\n      dialogContentRef,\n      resetPosition,\n      handleClose\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTeleport), {\n        to: _ctx.appendTo,\n        disabled: _ctx.appendTo !== \"body\" ? false : !_ctx.appendToBody\n      }, {\n        default: withCtx(() => [createVNode(Transition, {\n          name: \"dialog-fade\",\n          onAfterEnter: unref(afterEnter),\n          onAfterLeave: unref(afterLeave),\n          onBeforeLeave: unref(beforeLeave),\n          persisted: \"\"\n        }, {\n          default: withCtx(() => [withDirectives(createVNode(unref(ElOverlay), {\n            \"custom-mask-event\": \"\",\n            mask: _ctx.modal,\n            \"overlay-class\": _ctx.modalClass,\n            \"z-index\": unref(zIndex)\n          }, {\n            default: withCtx(() => [createElementVNode(\"div\", {\n              role: \"dialog\",\n              \"aria-modal\": \"true\",\n              \"aria-label\": _ctx.title || void 0,\n              \"aria-labelledby\": !_ctx.title ? unref(titleId) : void 0,\n              \"aria-describedby\": unref(bodyId),\n              class: normalizeClass(`${unref(ns).namespace.value}-overlay-dialog`),\n              style: normalizeStyle(unref(overlayDialogStyle)),\n              onClick: unref(overlayEvent).onClick,\n              onMousedown: unref(overlayEvent).onMousedown,\n              onMouseup: unref(overlayEvent).onMouseup\n            }, [createVNode(unref(ElFocusTrap), {\n              loop: \"\",\n              trapped: unref(visible),\n              \"focus-start-el\": \"container\",\n              onFocusAfterTrapped: unref(onOpenAutoFocus),\n              onFocusAfterReleased: unref(onCloseAutoFocus),\n              onFocusoutPrevented: unref(onFocusoutPrevented),\n              onReleaseRequested: unref(onCloseRequested)\n            }, {\n              default: withCtx(() => [unref(rendered) ? (openBlock(), createBlock(ElDialogContent, mergeProps({\n                key: 0,\n                ref_key: \"dialogContentRef\",\n                ref: dialogContentRef\n              }, _ctx.$attrs, {\n                center: _ctx.center,\n                \"align-center\": _ctx.alignCenter,\n                \"close-icon\": _ctx.closeIcon,\n                draggable: unref(draggable),\n                overflow: _ctx.overflow,\n                fullscreen: _ctx.fullscreen,\n                \"header-class\": _ctx.headerClass,\n                \"body-class\": _ctx.bodyClass,\n                \"footer-class\": _ctx.footerClass,\n                \"show-close\": _ctx.showClose,\n                title: _ctx.title,\n                \"aria-level\": _ctx.headerAriaLevel,\n                onClose: unref(handleClose)\n              }), createSlots({\n                header: withCtx(() => [!_ctx.$slots.title ? renderSlot(_ctx.$slots, \"header\", {\n                  key: 0,\n                  close: unref(handleClose),\n                  titleId: unref(titleId),\n                  titleClass: unref(ns).e(\"title\")\n                }) : renderSlot(_ctx.$slots, \"title\", {\n                  key: 1\n                })]),\n                default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n                _: 2\n              }, [_ctx.$slots.footer ? {\n                name: \"footer\",\n                fn: withCtx(() => [renderSlot(_ctx.$slots, \"footer\")])\n              } : void 0]), 1040, [\"center\", \"align-center\", \"close-icon\", \"draggable\", \"overflow\", \"fullscreen\", \"header-class\", \"body-class\", \"footer-class\", \"show-close\", \"title\", \"aria-level\", \"onClose\"])) : createCommentVNode(\"v-if\", true)]),\n              _: 3\n            }, 8, [\"trapped\", \"onFocusAfterTrapped\", \"onFocusAfterReleased\", \"onFocusoutPrevented\", \"onReleaseRequested\"])], 46, [\"aria-label\", \"aria-labelledby\", \"aria-describedby\", \"onClick\", \"onMousedown\", \"onMouseup\"])]),\n            _: 3\n          }, 8, [\"mask\", \"overlay-class\", \"z-index\"]), [[vShow, unref(visible)]])]),\n          _: 3\n        }, 8, [\"onAfterEnter\", \"onAfterLeave\", \"onBeforeLeave\"])]),\n        _: 3\n      }, 8, [\"to\", \"disabled\"]);\n    };\n  }\n});\nvar Dialog = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"dialog.vue\"]]);\nexport { Dialog as default };", "map": {"version": 3, "names": ["name", "inheritAttrs", "slots", "useSlots", "useDeprecated", "scope", "from", "replacement", "version", "ref", "computed", "title", "ns", "useNamespace", "dialogRef", "headerRef", "dialogContentRef", "visible", "titleId", "bodyId", "style", "overlayDialogStyle", "rendered", "zIndex", "afterEnter", "afterLeave", "beforeLeave", "handleClose", "onModalClick", "onOpenAutoFocus", "onCloseAutoFocus", "onCloseRequested", "onFocusoutPrevented", "useDialog", "props", "provide", "dialogInjectionKey", "overlayEvent", "useSameTarget", "draggable", "fullscreen", "resetPosition", "_a", "value", "expose", "_ctx", "_cache", "openBlock", "createBlock", "unref", "ElTeleport", "to", "appendTo"], "sources": ["../../../../../../packages/components/dialog/src/dialog.vue"], "sourcesContent": ["<template>\n  <el-teleport\n    :to=\"appendTo\"\n    :disabled=\"appendTo !== 'body' ? false : !appendToBody\"\n  >\n    <transition\n      name=\"dialog-fade\"\n      @after-enter=\"afterEnter\"\n      @after-leave=\"afterLeave\"\n      @before-leave=\"beforeLeave\"\n    >\n      <el-overlay\n        v-show=\"visible\"\n        custom-mask-event\n        :mask=\"modal\"\n        :overlay-class=\"modalClass\"\n        :z-index=\"zIndex\"\n      >\n        <div\n          role=\"dialog\"\n          aria-modal=\"true\"\n          :aria-label=\"title || undefined\"\n          :aria-labelledby=\"!title ? titleId : undefined\"\n          :aria-describedby=\"bodyId\"\n          :class=\"`${ns.namespace.value}-overlay-dialog`\"\n          :style=\"overlayDialogStyle\"\n          @click=\"overlayEvent.onClick\"\n          @mousedown=\"overlayEvent.onMousedown\"\n          @mouseup=\"overlayEvent.onMouseup\"\n        >\n          <el-focus-trap\n            loop\n            :trapped=\"visible\"\n            focus-start-el=\"container\"\n            @focus-after-trapped=\"onOpenAutoFocus\"\n            @focus-after-released=\"onCloseAutoFocus\"\n            @focusout-prevented=\"onFocusoutPrevented\"\n            @release-requested=\"onCloseRequested\"\n          >\n            <el-dialog-content\n              v-if=\"rendered\"\n              ref=\"dialogContentRef\"\n              v-bind=\"$attrs\"\n              :center=\"center\"\n              :align-center=\"alignCenter\"\n              :close-icon=\"closeIcon\"\n              :draggable=\"draggable\"\n              :overflow=\"overflow\"\n              :fullscreen=\"fullscreen\"\n              :header-class=\"headerClass\"\n              :body-class=\"bodyClass\"\n              :footer-class=\"footerClass\"\n              :show-close=\"showClose\"\n              :title=\"title\"\n              :aria-level=\"headerAriaLevel\"\n              @close=\"handleClose\"\n            >\n              <template #header>\n                <slot\n                  v-if=\"!$slots.title\"\n                  name=\"header\"\n                  :close=\"handleClose\"\n                  :title-id=\"titleId\"\n                  :title-class=\"ns.e('title')\"\n                />\n                <slot v-else name=\"title\" />\n              </template>\n              <slot />\n              <template v-if=\"$slots.footer\" #footer>\n                <slot name=\"footer\" />\n              </template>\n            </el-dialog-content>\n          </el-focus-trap>\n        </div>\n      </el-overlay>\n    </transition>\n  </el-teleport>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, provide, ref, useSlots } from 'vue'\nimport { ElOverlay } from '@element-plus/components/overlay'\nimport { useDeprecated, useNamespace, useSameTarget } from '@element-plus/hooks'\nimport ElFocusTrap from '@element-plus/components/focus-trap'\nimport ElTeleport from '@element-plus/components/teleport'\nimport ElDialogContent from './dialog-content.vue'\nimport { dialogInjectionKey } from './constants'\nimport { dialogEmits, dialogProps } from './dialog'\nimport { useDialog } from './use-dialog'\n\ndefineOptions({\n  name: 'ElDialog',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(dialogProps)\ndefineEmits(dialogEmits)\nconst slots = useSlots()\n\nuseDeprecated(\n  {\n    scope: 'el-dialog',\n    from: 'the title slot',\n    replacement: 'the header slot',\n    version: '3.0.0',\n    ref: 'https://element-plus.org/en-US/component/dialog.html#slots',\n  },\n  computed(() => !!slots.title)\n)\n\nconst ns = useNamespace('dialog')\nconst dialogRef = ref<HTMLElement>()\nconst headerRef = ref<HTMLElement>()\nconst dialogContentRef = ref()\n\nconst {\n  visible,\n  titleId,\n  bodyId,\n  style,\n  overlayDialogStyle,\n  rendered,\n  zIndex,\n  afterEnter,\n  afterLeave,\n  beforeLeave,\n  handleClose,\n  onModalClick,\n  onOpenAutoFocus,\n  onCloseAutoFocus,\n  onCloseRequested,\n  onFocusoutPrevented,\n} = useDialog(props, dialogRef)\n\nprovide(dialogInjectionKey, {\n  dialogRef,\n  headerRef,\n  bodyId,\n  ns,\n  rendered,\n  style,\n})\n\nconst overlayEvent = useSameTarget(onModalClick)\n\nconst draggable = computed(() => props.draggable && !props.fullscreen)\n\nconst resetPosition = () => {\n  dialogContentRef.value?.resetPosition()\n}\n\ndefineExpose({\n  /** @description whether the dialog is visible */\n  visible,\n  dialogContentRef,\n  resetPosition,\n  handleClose,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;mCA0Fc;EACZA,IAAM;EACNC,YAAc;AAChB;;;;;;;;;IAIA,MAAMC,KAAA,GAAQC,QAAS;IAEvBC,aAAA;MACEC,KAAA;MAAAC,IACS;MAAAC,WACD;MAAAC,OACO;MAAAC,GACJ;IAAA,GAAAC,QACJ,SAAAR,KAAA,CAAAS,KAAA;IACP,MAAAC,EAAA,GAAAC,YAAA;IAAA,MACSC,SAAA,GAAOL,GAAA;IAClB,MAAAM,SAAA,GAAAN,GAAA;IAEM,MAAAO,gBAAA,GAA0BP,GAAA;IAChC,MAAM;MACNQ,OAAA;MACAC,OAAA;MAEMC,MAAA;MACJC,KAAA;MACAC,kBAAA;MACAC,QAAA;MACAC,MAAA;MACAC,UAAA;MACAC,UAAA;MACAC,WAAA;MACAC,WAAA;MACAC,YAAA;MACAC,eAAA;MACAC,gBAAA;MACAC,gBAAA;MACAC;IAAA,CACA,GAAAC,SAAA,CAAAC,KAAA,EAAApB,SAAA;IACAqB,OAAA,CAAAC,kBAAA;MACAtB,SAAA;MACFC,SAAc;MAEdI,MAAQ;MACNP,EAAA;MACAU,QAAA;MACAF;IAAA,CACA;IACA,MAAAiB,YAAA,GAAAC,aAAA,CAAAV,YAAA;IACA,MAAAW,SAAA,GAAA7B,QAAA,OAAAwB,KAAA,CAAAK,SAAA,KAAAL,KAAA,CAAAM,UAAA;IACF,MAACC,aAAA,GAAAA,CAAA;MAEK,IAAAC,EAAA;MAEN,CAAAA,EAAA,GAAA1B,gBAA2B,CAAA2B,KAAA,SAAY,GAAa,SAAAD,EAAC,CAAAD,aAAgB;IAErE;IACEG,MAAA;MACF3B,OAAA;MAEaD,gBAAA;MAAAyB,aAAA;MAEXd;IAAA,CACA;IACA,QAAAkB,IAAA,EAAAC,MAAA;MACA,OAAAC,SAAA,IAAAC,WAAA,CAAAC,KAAA,CAAAC,UAAA;QACDC,EAAA,EAAAN,IAAA,CAAAO,QAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}