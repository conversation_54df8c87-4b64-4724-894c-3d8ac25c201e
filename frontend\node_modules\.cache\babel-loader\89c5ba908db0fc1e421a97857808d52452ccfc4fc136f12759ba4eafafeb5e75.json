{"ast": null, "code": "import { tooltipV2Sides } from './common.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst tooltipV2ArrowProps = buildProps({\n  width: {\n    type: Number,\n    default: 10\n  },\n  height: {\n    type: Number,\n    default: 10\n  },\n  style: {\n    type: definePropType(Object),\n    default: null\n  }\n});\nconst tooltipV2ArrowSpecialProps = buildProps({\n  side: {\n    type: definePropType(String),\n    values: tooltipV2Sides,\n    required: true\n  }\n});\nexport { tooltipV2ArrowProps, tooltipV2ArrowSpecialProps };", "map": {"version": 3, "names": ["tooltipV2ArrowProps", "buildProps", "width", "type", "Number", "default", "height", "style", "definePropType", "Object", "tooltipV2ArrowSpecialProps", "side", "String", "values", "tooltipV2Sides", "required"], "sources": ["../../../../../../packages/components/tooltip-v2/src/arrow.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { tooltipV2Sides } from './common'\n\nimport type { CSSProperties, ExtractPropTypes } from 'vue'\nimport type { TooltipV2Sides } from './common'\n\nexport const tooltipV2ArrowProps = buildProps({\n  width: {\n    type: Number,\n    default: 10,\n  },\n  height: {\n    type: Number,\n    default: 10,\n  },\n  style: {\n    type: definePropType<CSSProperties | null>(Object),\n    default: null,\n  },\n} as const)\n\nexport const tooltipV2ArrowSpecialProps = buildProps({\n  side: {\n    type: definePropType<TooltipV2Sides>(String),\n    values: tooltipV2Sides,\n    required: true,\n  },\n} as const)\n\nexport type TooltipV2ArrowProps = ExtractPropTypes<typeof tooltipV2ArrowProps>\n"], "mappings": ";;AAEY,MAACA,mBAAmB,GAAGC,UAAU,CAAC;EAC5CC,KAAK,EAAE;IACLC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,MAAM,EAAE;IACNH,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDE,KAAK,EAAE;IACLJ,IAAI,EAAEK,cAAc,CAACC,MAAM,CAAC;IAC5BJ,OAAO,EAAE;EACb;AACA,CAAC;AACW,MAACK,0BAA0B,GAAGT,UAAU,CAAC;EACnDU,IAAI,EAAE;IACJR,IAAI,EAAEK,cAAc,CAACI,MAAM,CAAC;IAC5BC,MAAM,EAAEC,cAAc;IACtBC,QAAQ,EAAE;EACd;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}