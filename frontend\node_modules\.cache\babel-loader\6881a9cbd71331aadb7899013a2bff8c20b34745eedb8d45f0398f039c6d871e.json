{"ast": null, "code": "import createGrid from '../builders/build-grid.mjs';\nimport { DEFAULT_DYNAMIC_LIST_ITEM_SIZE, AUTO_ALIGNMENT, CENTERED_ALIGNMENT, END_ALIGNMENT, START_ALIGNMENT, SMART_ALIGNMENT } from '../defaults.mjs';\nimport { isFunction } from '@vue/shared';\nimport { throwError } from '../../../../utils/error.mjs';\nimport { isNumber, isUndefined } from '../../../../utils/types.mjs';\nconst {\n  max,\n  min,\n  floor\n} = Math;\nconst SCOPE = \"ElDynamicSizeGrid\";\nconst ACCESS_SIZER_KEY_MAP = {\n  column: \"columnWidth\",\n  row: \"rowHeight\"\n};\nconst ACCESS_LAST_VISITED_KEY_MAP = {\n  column: \"lastVisitedColumnIndex\",\n  row: \"lastVisitedRowIndex\"\n};\nconst getItemFromCache = (props, index, gridCache, type) => {\n  const [cachedItems, sizer, lastVisited] = [gridCache[type], props[ACCESS_SIZER_KEY_MAP[type]], gridCache[ACCESS_LAST_VISITED_KEY_MAP[type]]];\n  if (index > lastVisited) {\n    let offset = 0;\n    if (lastVisited >= 0) {\n      const item = cachedItems[lastVisited];\n      offset = item.offset + item.size;\n    }\n    for (let i = lastVisited + 1; i <= index; i++) {\n      const size = sizer(i);\n      cachedItems[i] = {\n        offset,\n        size\n      };\n      offset += size;\n    }\n    gridCache[ACCESS_LAST_VISITED_KEY_MAP[type]] = index;\n  }\n  return cachedItems[index];\n};\nconst bs = (props, gridCache, low, high, offset, type) => {\n  while (low <= high) {\n    const mid = low + floor((high - low) / 2);\n    const currentOffset = getItemFromCache(props, mid, gridCache, type).offset;\n    if (currentOffset === offset) {\n      return mid;\n    } else if (currentOffset < offset) {\n      low = mid + 1;\n    } else {\n      high = mid - 1;\n    }\n  }\n  return max(0, low - 1);\n};\nconst es = (props, gridCache, idx, offset, type) => {\n  const total = type === \"column\" ? props.totalColumn : props.totalRow;\n  let exponent = 1;\n  while (idx < total && getItemFromCache(props, idx, gridCache, type).offset < offset) {\n    idx += exponent;\n    exponent *= 2;\n  }\n  return bs(props, gridCache, floor(idx / 2), min(idx, total - 1), offset, type);\n};\nconst findItem = (props, gridCache, offset, type) => {\n  const [cache, lastVisitedIndex] = [gridCache[type], gridCache[ACCESS_LAST_VISITED_KEY_MAP[type]]];\n  const lastVisitedItemOffset = lastVisitedIndex > 0 ? cache[lastVisitedIndex].offset : 0;\n  if (lastVisitedItemOffset >= offset) {\n    return bs(props, gridCache, 0, lastVisitedIndex, offset, type);\n  }\n  return es(props, gridCache, max(0, lastVisitedIndex), offset, type);\n};\nconst getEstimatedTotalHeight = ({\n  totalRow\n}, {\n  estimatedRowHeight,\n  lastVisitedRowIndex,\n  row\n}) => {\n  let sizeOfVisitedRows = 0;\n  if (lastVisitedRowIndex >= totalRow) {\n    lastVisitedRowIndex = totalRow - 1;\n  }\n  if (lastVisitedRowIndex >= 0) {\n    const item = row[lastVisitedRowIndex];\n    sizeOfVisitedRows = item.offset + item.size;\n  }\n  const unvisitedItems = totalRow - lastVisitedRowIndex - 1;\n  const sizeOfUnvisitedItems = unvisitedItems * estimatedRowHeight;\n  return sizeOfVisitedRows + sizeOfUnvisitedItems;\n};\nconst getEstimatedTotalWidth = ({\n  totalColumn\n}, {\n  column,\n  estimatedColumnWidth,\n  lastVisitedColumnIndex\n}) => {\n  let sizeOfVisitedColumns = 0;\n  if (lastVisitedColumnIndex > totalColumn) {\n    lastVisitedColumnIndex = totalColumn - 1;\n  }\n  if (lastVisitedColumnIndex >= 0) {\n    const item = column[lastVisitedColumnIndex];\n    sizeOfVisitedColumns = item.offset + item.size;\n  }\n  const unvisitedItems = totalColumn - lastVisitedColumnIndex - 1;\n  const sizeOfUnvisitedItems = unvisitedItems * estimatedColumnWidth;\n  return sizeOfVisitedColumns + sizeOfUnvisitedItems;\n};\nconst ACCESS_ESTIMATED_SIZE_KEY_MAP = {\n  column: getEstimatedTotalWidth,\n  row: getEstimatedTotalHeight\n};\nconst getOffset = (props, index, alignment, scrollOffset, cache, type, scrollBarWidth) => {\n  const [size, estimatedSizeAssociates] = [type === \"row\" ? props.height : props.width, ACCESS_ESTIMATED_SIZE_KEY_MAP[type]];\n  const item = getItemFromCache(props, index, cache, type);\n  const estimatedSize = estimatedSizeAssociates(props, cache);\n  const maxOffset = max(0, min(estimatedSize - size, item.offset));\n  const minOffset = max(0, item.offset - size + scrollBarWidth + item.size);\n  if (alignment === SMART_ALIGNMENT) {\n    if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n      alignment = AUTO_ALIGNMENT;\n    } else {\n      alignment = CENTERED_ALIGNMENT;\n    }\n  }\n  switch (alignment) {\n    case START_ALIGNMENT:\n      {\n        return maxOffset;\n      }\n    case END_ALIGNMENT:\n      {\n        return minOffset;\n      }\n    case CENTERED_ALIGNMENT:\n      {\n        return Math.round(minOffset + (maxOffset - minOffset) / 2);\n      }\n    case AUTO_ALIGNMENT:\n    default:\n      {\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (minOffset > maxOffset) {\n          return minOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n      }\n  }\n};\nconst DynamicSizeGrid = createGrid({\n  name: \"ElDynamicSizeGrid\",\n  getColumnPosition: (props, idx, cache) => {\n    const item = getItemFromCache(props, idx, cache, \"column\");\n    return [item.size, item.offset];\n  },\n  getRowPosition: (props, idx, cache) => {\n    const item = getItemFromCache(props, idx, cache, \"row\");\n    return [item.size, item.offset];\n  },\n  getColumnOffset: (props, columnIndex, alignment, scrollLeft, cache, scrollBarWidth) => getOffset(props, columnIndex, alignment, scrollLeft, cache, \"column\", scrollBarWidth),\n  getRowOffset: (props, rowIndex, alignment, scrollTop, cache, scrollBarWidth) => getOffset(props, rowIndex, alignment, scrollTop, cache, \"row\", scrollBarWidth),\n  getColumnStartIndexForOffset: (props, scrollLeft, cache) => findItem(props, cache, scrollLeft, \"column\"),\n  getColumnStopIndexForStartIndex: (props, startIndex, scrollLeft, cache) => {\n    const item = getItemFromCache(props, startIndex, cache, \"column\");\n    const maxOffset = scrollLeft + props.width;\n    let offset = item.offset + item.size;\n    let stopIndex = startIndex;\n    while (stopIndex < props.totalColumn - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemFromCache(props, startIndex, cache, \"column\").size;\n    }\n    return stopIndex;\n  },\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n  getRowStartIndexForOffset: (props, scrollTop, cache) => findItem(props, cache, scrollTop, \"row\"),\n  getRowStopIndexForStartIndex: (props, startIndex, scrollTop, cache) => {\n    const {\n      totalRow,\n      height\n    } = props;\n    const item = getItemFromCache(props, startIndex, cache, \"row\");\n    const maxOffset = scrollTop + height;\n    let offset = item.size + item.offset;\n    let stopIndex = startIndex;\n    while (stopIndex < totalRow - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemFromCache(props, stopIndex, cache, \"row\").size;\n    }\n    return stopIndex;\n  },\n  injectToInstance: (instance, cache) => {\n    const resetAfter = ({\n      columnIndex,\n      rowIndex\n    }, forceUpdate) => {\n      var _a, _b;\n      forceUpdate = isUndefined(forceUpdate) ? true : forceUpdate;\n      if (isNumber(columnIndex)) {\n        cache.value.lastVisitedColumnIndex = Math.min(cache.value.lastVisitedColumnIndex, columnIndex - 1);\n      }\n      if (isNumber(rowIndex)) {\n        cache.value.lastVisitedRowIndex = Math.min(cache.value.lastVisitedRowIndex, rowIndex - 1);\n      }\n      (_a = instance.exposed) == null ? void 0 : _a.getItemStyleCache.value(-1, null, null);\n      if (forceUpdate) (_b = instance.proxy) == null ? void 0 : _b.$forceUpdate();\n    };\n    const resetAfterColumnIndex = (columnIndex, forceUpdate) => {\n      resetAfter({\n        columnIndex\n      }, forceUpdate);\n    };\n    const resetAfterRowIndex = (rowIndex, forceUpdate) => {\n      resetAfter({\n        rowIndex\n      }, forceUpdate);\n    };\n    Object.assign(instance.proxy, {\n      resetAfterColumnIndex,\n      resetAfterRowIndex,\n      resetAfter\n    });\n  },\n  initCache: ({\n    estimatedColumnWidth = DEFAULT_DYNAMIC_LIST_ITEM_SIZE,\n    estimatedRowHeight = DEFAULT_DYNAMIC_LIST_ITEM_SIZE\n  }) => {\n    const cache = {\n      column: {},\n      estimatedColumnWidth,\n      estimatedRowHeight,\n      lastVisitedColumnIndex: -1,\n      lastVisitedRowIndex: -1,\n      row: {}\n    };\n    return cache;\n  },\n  clearCache: false,\n  validateProps: ({\n    columnWidth,\n    rowHeight\n  }) => {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!isFunction(columnWidth)) {\n        throwError(SCOPE, `\n          \"columnWidth\" must be passed as function,\n            instead ${typeof columnWidth} was given.\n        `);\n      }\n      if (!isFunction(rowHeight)) {\n        throwError(SCOPE, `\n          \"rowHeight\" must be passed as function,\n            instead ${typeof rowHeight} was given.\n        `);\n      }\n    }\n  }\n});\nexport { DynamicSizeGrid as default };", "map": {"version": 3, "names": ["max", "min", "floor", "Math", "SCOPE", "ACCESS_SIZER_KEY_MAP", "column", "row", "ACCESS_LAST_VISITED_KEY_MAP", "getItemFromCache", "props", "index", "gridCache", "type", "cachedItems", "sizer", "lastVisited", "offset", "item", "size", "i", "bs", "low", "high", "mid", "currentOffset", "es", "idx", "total", "totalColumn", "totalRow", "exponent", "findItem", "cache", "lastVisitedIndex", "lastVisitedItemOffset", "getEstimatedTotalHeight", "estimatedRowHeight", "lastVisitedRowIndex", "sizeOfVisitedRows", "unvisitedItems", "sizeOfUnvisitedItems", "getEstimatedTotalWidth", "estimatedColumnWidth", "lastVisitedColumnIndex", "sizeOfVisitedColumns", "ACCESS_ESTIMATED_SIZE_KEY_MAP", "getOffset", "alignment", "scrollOffset", "scrollBarWidth", "estimatedSizeAssociates", "height", "width", "estimatedSize", "maxOffset", "minOffset", "SMART_ALIGNMENT", "AUTO_ALIGNMENT", "CENTERED_ALIGNMENT", "START_ALIGNMENT", "END_ALIGNMENT", "round", "DynamicSizeGrid", "createGrid", "name", "getColumnPosition", "getRowPosition", "getColumnOffset", "columnIndex", "scrollLeft", "getRowOffset", "rowIndex", "scrollTop", "getColumnStartIndexForOffset", "getColumnStopIndexForStartIndex", "startIndex", "stopIndex", "getRowStartIndexForOffset", "getRowStopIndexForStartIndex", "injectToInstance", "instance", "resetAfter", "forceUpdate", "_a", "_b", "isUndefined", "isNumber", "value", "exposed", "getItemStyleCache", "proxy", "$forceUpdate", "resetAfterColumnIndex", "resetAfterRowIndex", "Object", "assign", "initCache", "DEFAULT_DYNAMIC_LIST_ITEM_SIZE", "clearCache", "validateProps", "columnWidth", "rowHeight", "process", "env", "NODE_ENV", "isFunction", "throwError"], "sources": ["../../../../../../../packages/components/virtual-list/src/components/dynamic-size-grid.ts"], "sourcesContent": ["import {\n  isFunction,\n  isNumber,\n  isUndefined,\n  throwError,\n} from '@element-plus/utils'\nimport createGrid from '../builders/build-grid'\n\nimport {\n  AUTO_ALIGNMENT,\n  CENTERED_ALIGNMENT,\n  DEFAULT_DYNAMIC_LIST_ITEM_SIZE,\n  END_ALIGNMENT,\n  SMART_ALIGNMENT,\n  START_ALIGNMENT,\n} from '../defaults'\nimport type { GridInstance } from '../builders/build-grid'\nimport type { VirtualizedGridProps } from '../props'\n\nimport type { Alignment, GridCache, ItemSize } from '../types'\n\nconst { max, min, floor } = Math\nconst SCOPE = 'ElDynamicSizeGrid'\n\ntype Props = VirtualizedGridProps\ntype CacheItemType = 'column' | 'row'\ntype Indices = {\n  columnIndex?: number\n  rowIndex?: number\n}\n\n// generates props access key via type\nconst ACCESS_SIZER_KEY_MAP = {\n  column: 'columnWidth',\n  row: 'rowHeight',\n} as const\n\n// generates cache access key via type\nconst ACCESS_LAST_VISITED_KEY_MAP = {\n  column: 'lastVisitedColumnIndex',\n  row: 'lastVisitedRowIndex',\n} as const\n\nconst getItemFromCache = (\n  props: Props,\n  index: number,\n  gridCache: GridCache,\n  type: CacheItemType\n) => {\n  const [cachedItems, sizer, lastVisited] = [\n    gridCache[type],\n    props[ACCESS_SIZER_KEY_MAP[type]] as ItemSize,\n    gridCache[ACCESS_LAST_VISITED_KEY_MAP[type]],\n  ]\n\n  if (index > lastVisited) {\n    let offset = 0\n    if (lastVisited >= 0) {\n      const item = cachedItems[lastVisited]\n      offset = item.offset + item.size\n    }\n\n    for (let i = lastVisited + 1; i <= index; i++) {\n      const size = sizer(i)\n\n      cachedItems[i] = {\n        offset,\n        size,\n      }\n\n      offset += size\n    }\n\n    gridCache[ACCESS_LAST_VISITED_KEY_MAP[type]] = index\n  }\n\n  return cachedItems[index]\n}\n\nconst bs = (\n  props: Props,\n  gridCache: GridCache,\n  low: number,\n  high: number,\n  offset: number,\n  type: CacheItemType\n) => {\n  while (low <= high) {\n    const mid = low + floor((high - low) / 2)\n    const currentOffset = getItemFromCache(props, mid, gridCache, type).offset\n\n    if (currentOffset === offset) {\n      return mid\n    } else if (currentOffset < offset) {\n      low = mid + 1\n    } else {\n      high = mid - 1\n    }\n  }\n\n  return max(0, low - 1)\n}\n\nconst es = (\n  props: Props,\n  gridCache: GridCache,\n  idx: number,\n  offset: number,\n  type: CacheItemType\n) => {\n  const total = type === 'column' ? props.totalColumn : props.totalRow\n  let exponent = 1\n\n  while (\n    idx < total &&\n    getItemFromCache(props, idx, gridCache, type).offset < offset\n  ) {\n    idx += exponent\n    exponent *= 2\n  }\n\n  return bs(props, gridCache, floor(idx / 2), min(idx, total - 1), offset, type)\n}\n\nconst findItem = (\n  props: Props,\n  gridCache: GridCache,\n  offset: number,\n  type: CacheItemType\n) => {\n  const [cache, lastVisitedIndex] = [\n    gridCache[type],\n    gridCache[ACCESS_LAST_VISITED_KEY_MAP[type]],\n  ]\n\n  const lastVisitedItemOffset =\n    lastVisitedIndex > 0 ? cache[lastVisitedIndex].offset : 0\n\n  if (lastVisitedItemOffset >= offset) {\n    return bs(props, gridCache, 0, lastVisitedIndex, offset, type)\n  }\n\n  return es(props, gridCache, max(0, lastVisitedIndex), offset, type)\n}\n\nconst getEstimatedTotalHeight = (\n  { totalRow }: Props,\n  { estimatedRowHeight, lastVisitedRowIndex, row }: GridCache\n) => {\n  let sizeOfVisitedRows = 0\n\n  if (lastVisitedRowIndex >= totalRow) {\n    lastVisitedRowIndex = totalRow - 1\n  }\n\n  if (lastVisitedRowIndex >= 0) {\n    const item = row[lastVisitedRowIndex]\n    sizeOfVisitedRows = item.offset + item.size\n  }\n\n  const unvisitedItems = totalRow - lastVisitedRowIndex - 1\n  const sizeOfUnvisitedItems = unvisitedItems * estimatedRowHeight\n\n  return sizeOfVisitedRows + sizeOfUnvisitedItems\n}\nconst getEstimatedTotalWidth = (\n  { totalColumn }: Props,\n  { column, estimatedColumnWidth, lastVisitedColumnIndex }: GridCache\n) => {\n  let sizeOfVisitedColumns = 0\n\n  if (lastVisitedColumnIndex > totalColumn) {\n    lastVisitedColumnIndex = totalColumn - 1\n  }\n\n  if (lastVisitedColumnIndex >= 0) {\n    const item = column[lastVisitedColumnIndex]\n    sizeOfVisitedColumns = item.offset + item.size\n  }\n\n  const unvisitedItems = totalColumn - lastVisitedColumnIndex - 1\n  const sizeOfUnvisitedItems = unvisitedItems * estimatedColumnWidth\n\n  return sizeOfVisitedColumns + sizeOfUnvisitedItems\n}\n\nconst ACCESS_ESTIMATED_SIZE_KEY_MAP = {\n  column: getEstimatedTotalWidth,\n  row: getEstimatedTotalHeight,\n}\n\nconst getOffset = (\n  props: Props,\n  index: number,\n  alignment: Alignment,\n  scrollOffset: number,\n  cache: GridCache,\n  type: CacheItemType,\n  scrollBarWidth: number\n) => {\n  const [size, estimatedSizeAssociates] = [\n    type === 'row' ? props.height : props.width,\n    ACCESS_ESTIMATED_SIZE_KEY_MAP[type],\n  ] as [number, (props: Props, cache: GridCache) => number]\n  const item = getItemFromCache(props, index, cache, type)\n\n  const estimatedSize = estimatedSizeAssociates(props, cache)\n\n  const maxOffset = max(0, min(estimatedSize - size, item.offset))\n  const minOffset = max(0, item.offset - size + scrollBarWidth + item.size)\n\n  if (alignment === SMART_ALIGNMENT) {\n    if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n      alignment = AUTO_ALIGNMENT\n    } else {\n      alignment = CENTERED_ALIGNMENT\n    }\n  }\n\n  switch (alignment) {\n    case START_ALIGNMENT: {\n      return maxOffset\n    }\n    case END_ALIGNMENT: {\n      return minOffset\n    }\n    case CENTERED_ALIGNMENT: {\n      return Math.round(minOffset + (maxOffset - minOffset) / 2)\n    }\n    case AUTO_ALIGNMENT:\n    default: {\n      if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n        return scrollOffset\n      } else if (minOffset > maxOffset) {\n        return minOffset\n      } else if (scrollOffset < minOffset) {\n        return minOffset\n      } else {\n        return maxOffset\n      }\n    }\n  }\n}\n\nconst DynamicSizeGrid = createGrid({\n  name: 'ElDynamicSizeGrid',\n  getColumnPosition: (props, idx, cache) => {\n    const item = getItemFromCache(props, idx, cache, 'column')\n    return [item.size, item.offset]\n  },\n\n  getRowPosition: (props, idx, cache) => {\n    const item = getItemFromCache(props, idx, cache, 'row')\n    return [item.size, item.offset]\n  },\n\n  getColumnOffset: (\n    props,\n    columnIndex,\n    alignment,\n    scrollLeft,\n    cache,\n    scrollBarWidth\n  ) =>\n    getOffset(\n      props,\n      columnIndex,\n      alignment,\n      scrollLeft,\n      cache,\n      'column',\n      scrollBarWidth\n    ),\n\n  getRowOffset: (\n    props,\n    rowIndex,\n    alignment,\n    scrollTop,\n    cache,\n    scrollBarWidth: number\n  ) =>\n    getOffset(\n      props,\n      rowIndex,\n      alignment,\n      scrollTop,\n      cache,\n      'row',\n      scrollBarWidth\n    ),\n\n  getColumnStartIndexForOffset: (props, scrollLeft, cache) =>\n    findItem(props, cache, scrollLeft, 'column'),\n\n  getColumnStopIndexForStartIndex: (props, startIndex, scrollLeft, cache) => {\n    const item = getItemFromCache(props, startIndex, cache, 'column')\n\n    const maxOffset = scrollLeft + (props.width as number)\n\n    let offset = item.offset + item.size\n    let stopIndex = startIndex\n    while (stopIndex < props.totalColumn - 1 && offset < maxOffset) {\n      stopIndex++\n      offset += getItemFromCache(props, startIndex, cache, 'column').size\n    }\n    return stopIndex\n  },\n\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n\n  getRowStartIndexForOffset: (props, scrollTop, cache) =>\n    findItem(props, cache, scrollTop, 'row'),\n\n  getRowStopIndexForStartIndex: (props, startIndex, scrollTop, cache) => {\n    const { totalRow, height } = props\n    const item = getItemFromCache(props, startIndex, cache, 'row')\n    const maxOffset = scrollTop + (height as number)\n\n    let offset = item.size + item.offset\n    let stopIndex = startIndex\n\n    while (stopIndex < totalRow - 1 && offset < maxOffset) {\n      stopIndex++\n      offset += getItemFromCache(props, stopIndex, cache, 'row').size\n    }\n\n    return stopIndex\n  },\n  injectToInstance: (instance, cache) => {\n    const resetAfter = (\n      { columnIndex, rowIndex }: Indices,\n      forceUpdate?: boolean\n    ) => {\n      forceUpdate = isUndefined(forceUpdate) ? true : forceUpdate\n\n      if (isNumber(columnIndex)) {\n        cache.value.lastVisitedColumnIndex = Math.min(\n          cache.value.lastVisitedColumnIndex,\n          columnIndex - 1\n        )\n      }\n\n      if (isNumber(rowIndex)) {\n        cache.value.lastVisitedRowIndex = Math.min(\n          cache.value.lastVisitedRowIndex,\n          rowIndex - 1\n        )\n      }\n\n      instance.exposed?.getItemStyleCache.value(-1, null, null)\n\n      if (forceUpdate) instance.proxy?.$forceUpdate()\n    }\n\n    const resetAfterColumnIndex = (\n      columnIndex: number,\n      forceUpdate: boolean\n    ) => {\n      resetAfter(\n        {\n          columnIndex,\n        },\n        forceUpdate\n      )\n    }\n\n    const resetAfterRowIndex = (rowIndex: number, forceUpdate: boolean) => {\n      resetAfter(\n        {\n          rowIndex,\n        },\n        forceUpdate\n      )\n    }\n\n    Object.assign(instance.proxy!, {\n      resetAfterColumnIndex,\n      resetAfterRowIndex,\n      resetAfter,\n    })\n  },\n  initCache: ({\n    estimatedColumnWidth = DEFAULT_DYNAMIC_LIST_ITEM_SIZE,\n    estimatedRowHeight = DEFAULT_DYNAMIC_LIST_ITEM_SIZE,\n  }) => {\n    const cache = {\n      column: {},\n      estimatedColumnWidth,\n      estimatedRowHeight,\n      lastVisitedColumnIndex: -1,\n      lastVisitedRowIndex: -1,\n      row: {},\n    } as GridCache\n\n    // TODO: expose methods.\n    return cache\n  },\n\n  clearCache: false,\n\n  validateProps: ({ columnWidth, rowHeight }) => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!isFunction(columnWidth)) {\n        throwError(\n          SCOPE,\n          `\n          \"columnWidth\" must be passed as function,\n            instead ${typeof columnWidth} was given.\n        `\n        )\n      }\n\n      if (!isFunction(rowHeight)) {\n        throwError(\n          SCOPE,\n          `\n          \"rowHeight\" must be passed as function,\n            instead ${typeof rowHeight} was given.\n        `\n        )\n      }\n    }\n  },\n})\n\nexport default DynamicSizeGrid\n\nexport type ResetAfterIndex = (idx: number, forceUpdate: boolean) => void\nexport type ResetAfterIndices = (indices: Indices, forceUpdate: boolean) => void\n\nexport type DynamicSizeGridInstance = GridInstance & {\n  resetAfterColumnIndex: ResetAfterIndex\n  resetAfterRowIndex: ResetAfterIndex\n  resetAfter: ResetAfterIndices\n}\n"], "mappings": ";;;;;AAeA,MAAM;EAAEA,GAAG;EAAEC,GAAG;EAAEC;AAAK,CAAE,GAAGC,IAAI;AAChC,MAAMC,KAAK,GAAG,mBAAmB;AACjC,MAAMC,oBAAoB,GAAG;EAC3BC,MAAM,EAAE,aAAa;EACrBC,GAAG,EAAE;AACP,CAAC;AACD,MAAMC,2BAA2B,GAAG;EAClCF,MAAM,EAAE,wBAAwB;EAChCC,GAAG,EAAE;AACP,CAAC;AACD,MAAME,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,IAAI,KAAK;EAC1D,MAAM,CAACC,WAAW,EAAEC,KAAK,EAAEC,WAAW,CAAC,GAAG,CACxCJ,SAAS,CAACC,IAAI,CAAC,EACfH,KAAK,CAACL,oBAAoB,CAACQ,IAAI,CAAC,CAAC,EACjCD,SAAS,CAACJ,2BAA2B,CAACK,IAAI,CAAC,CAAC,CAC7C;EACD,IAAIF,KAAK,GAAGK,WAAW,EAAE;IACvB,IAAIC,MAAM,GAAG,CAAC;IACd,IAAID,WAAW,IAAI,CAAC,EAAE;MACpB,MAAME,IAAI,GAAGJ,WAAW,CAACE,WAAW,CAAC;MACrCC,MAAM,GAAGC,IAAI,CAACD,MAAM,GAAGC,IAAI,CAACC,IAAI;IACtC;IACI,KAAK,IAAIC,CAAC,GAAGJ,WAAW,GAAG,CAAC,EAAEI,CAAC,IAAIT,KAAK,EAAES,CAAC,EAAE,EAAE;MAC7C,MAAMD,IAAI,GAAGJ,KAAK,CAACK,CAAC,CAAC;MACrBN,WAAW,CAACM,CAAC,CAAC,GAAG;QACfH,MAAM;QACNE;MACR,CAAO;MACDF,MAAM,IAAIE,IAAI;IACpB;IACIP,SAAS,CAACJ,2BAA2B,CAACK,IAAI,CAAC,CAAC,GAAGF,KAAK;EACxD;EACE,OAAOG,WAAW,CAACH,KAAK,CAAC;AAC3B,CAAC;AACD,MAAMU,EAAE,GAAGA,CAACX,KAAK,EAAEE,SAAS,EAAEU,GAAG,EAAEC,IAAI,EAAEN,MAAM,EAAEJ,IAAI,KAAK;EACxD,OAAOS,GAAG,IAAIC,IAAI,EAAE;IAClB,MAAMC,GAAG,GAAGF,GAAG,GAAGpB,KAAK,CAAC,CAACqB,IAAI,GAAGD,GAAG,IAAI,CAAC,CAAC;IACzC,MAAMG,aAAa,GAAGhB,gBAAgB,CAACC,KAAK,EAAEc,GAAG,EAAEZ,SAAS,EAAEC,IAAI,CAAC,CAACI,MAAM;IAC1E,IAAIQ,aAAa,KAAKR,MAAM,EAAE;MAC5B,OAAOO,GAAG;IAChB,CAAK,MAAM,IAAIC,aAAa,GAAGR,MAAM,EAAE;MACjCK,GAAG,GAAGE,GAAG,GAAG,CAAC;IACnB,CAAK,MAAM;MACLD,IAAI,GAAGC,GAAG,GAAG,CAAC;IACpB;EACA;EACE,OAAOxB,GAAG,CAAC,CAAC,EAAEsB,GAAG,GAAG,CAAC,CAAC;AACxB,CAAC;AACD,MAAMI,EAAE,GAAGA,CAAChB,KAAK,EAAEE,SAAS,EAAEe,GAAG,EAAEV,MAAM,EAAEJ,IAAI,KAAK;EAClD,MAAMe,KAAK,GAAGf,IAAI,KAAK,QAAQ,GAAGH,KAAK,CAACmB,WAAW,GAAGnB,KAAK,CAACoB,QAAQ;EACpE,IAAIC,QAAQ,GAAG,CAAC;EAChB,OAAOJ,GAAG,GAAGC,KAAK,IAAInB,gBAAgB,CAACC,KAAK,EAAEiB,GAAG,EAAEf,SAAS,EAAEC,IAAI,CAAC,CAACI,MAAM,GAAGA,MAAM,EAAE;IACnFU,GAAG,IAAII,QAAQ;IACfA,QAAQ,IAAI,CAAC;EACjB;EACE,OAAOV,EAAE,CAACX,KAAK,EAAEE,SAAS,EAAEV,KAAK,CAACyB,GAAG,GAAG,CAAC,CAAC,EAAE1B,GAAG,CAAC0B,GAAG,EAAEC,KAAK,GAAG,CAAC,CAAC,EAAEX,MAAM,EAAEJ,IAAI,CAAC;AAChF,CAAC;AACD,MAAMmB,QAAQ,GAAGA,CAACtB,KAAK,EAAEE,SAAS,EAAEK,MAAM,EAAEJ,IAAI,KAAK;EACnD,MAAM,CAACoB,KAAK,EAAEC,gBAAgB,CAAC,GAAG,CAChCtB,SAAS,CAACC,IAAI,CAAC,EACfD,SAAS,CAACJ,2BAA2B,CAACK,IAAI,CAAC,CAAC,CAC7C;EACD,MAAMsB,qBAAqB,GAAGD,gBAAgB,GAAG,CAAC,GAAGD,KAAK,CAACC,gBAAgB,CAAC,CAACjB,MAAM,GAAG,CAAC;EACvF,IAAIkB,qBAAqB,IAAIlB,MAAM,EAAE;IACnC,OAAOI,EAAE,CAACX,KAAK,EAAEE,SAAS,EAAE,CAAC,EAAEsB,gBAAgB,EAAEjB,MAAM,EAAEJ,IAAI,CAAC;EAClE;EACE,OAAOa,EAAE,CAAChB,KAAK,EAAEE,SAAS,EAAEZ,GAAG,CAAC,CAAC,EAAEkC,gBAAgB,CAAC,EAAEjB,MAAM,EAAEJ,IAAI,CAAC;AACrE,CAAC;AACD,MAAMuB,uBAAuB,GAAGA,CAAC;EAAEN;AAAQ,CAAE,EAAE;EAAEO,kBAAkB;EAAEC,mBAAmB;EAAE/B;AAAG,CAAE,KAAK;EAClG,IAAIgC,iBAAiB,GAAG,CAAC;EACzB,IAAID,mBAAmB,IAAIR,QAAQ,EAAE;IACnCQ,mBAAmB,GAAGR,QAAQ,GAAG,CAAC;EACtC;EACE,IAAIQ,mBAAmB,IAAI,CAAC,EAAE;IAC5B,MAAMpB,IAAI,GAAGX,GAAG,CAAC+B,mBAAmB,CAAC;IACrCC,iBAAiB,GAAGrB,IAAI,CAACD,MAAM,GAAGC,IAAI,CAACC,IAAI;EAC/C;EACE,MAAMqB,cAAc,GAAGV,QAAQ,GAAGQ,mBAAmB,GAAG,CAAC;EACzD,MAAMG,oBAAoB,GAAGD,cAAc,GAAGH,kBAAkB;EAChE,OAAOE,iBAAiB,GAAGE,oBAAoB;AACjD,CAAC;AACD,MAAMC,sBAAsB,GAAGA,CAAC;EAAEb;AAAW,CAAE,EAAE;EAAEvB,MAAM;EAAEqC,oBAAoB;EAAEC;AAAsB,CAAE,KAAK;EAC5G,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAID,sBAAsB,GAAGf,WAAW,EAAE;IACxCe,sBAAsB,GAAGf,WAAW,GAAG,CAAC;EAC5C;EACE,IAAIe,sBAAsB,IAAI,CAAC,EAAE;IAC/B,MAAM1B,IAAI,GAAGZ,MAAM,CAACsC,sBAAsB,CAAC;IAC3CC,oBAAoB,GAAG3B,IAAI,CAACD,MAAM,GAAGC,IAAI,CAACC,IAAI;EAClD;EACE,MAAMqB,cAAc,GAAGX,WAAW,GAAGe,sBAAsB,GAAG,CAAC;EAC/D,MAAMH,oBAAoB,GAAGD,cAAc,GAAGG,oBAAoB;EAClE,OAAOE,oBAAoB,GAAGJ,oBAAoB;AACpD,CAAC;AACD,MAAMK,6BAA6B,GAAG;EACpCxC,MAAM,EAAEoC,sBAAsB;EAC9BnC,GAAG,EAAE6B;AACP,CAAC;AACD,MAAMW,SAAS,GAAGA,CAACrC,KAAK,EAAEC,KAAK,EAAEqC,SAAS,EAAEC,YAAY,EAAEhB,KAAK,EAAEpB,IAAI,EAAEqC,cAAc,KAAK;EACxF,MAAM,CAAC/B,IAAI,EAAEgC,uBAAuB,CAAC,GAAG,CACtCtC,IAAI,KAAK,KAAK,GAAGH,KAAK,CAAC0C,MAAM,GAAG1C,KAAK,CAAC2C,KAAK,EAC3CP,6BAA6B,CAACjC,IAAI,CAAC,CACpC;EACD,MAAMK,IAAI,GAAGT,gBAAgB,CAACC,KAAK,EAAEC,KAAK,EAAEsB,KAAK,EAAEpB,IAAI,CAAC;EACxD,MAAMyC,aAAa,GAAGH,uBAAuB,CAACzC,KAAK,EAAEuB,KAAK,CAAC;EAC3D,MAAMsB,SAAS,GAAGvD,GAAG,CAAC,CAAC,EAAEC,GAAG,CAACqD,aAAa,GAAGnC,IAAI,EAAED,IAAI,CAACD,MAAM,CAAC,CAAC;EAChE,MAAMuC,SAAS,GAAGxD,GAAG,CAAC,CAAC,EAAEkB,IAAI,CAACD,MAAM,GAAGE,IAAI,GAAG+B,cAAc,GAAGhC,IAAI,CAACC,IAAI,CAAC;EACzE,IAAI6B,SAAS,KAAKS,eAAe,EAAE;IACjC,IAAIR,YAAY,IAAIO,SAAS,GAAGrC,IAAI,IAAI8B,YAAY,IAAIM,SAAS,GAAGpC,IAAI,EAAE;MACxE6B,SAAS,GAAGU,cAAc;IAChC,CAAK,MAAM;MACLV,SAAS,GAAGW,kBAAkB;IACpC;EACA;EACE,QAAQX,SAAS;IACf,KAAKY,eAAe;MAAE;QACpB,OAAOL,SAAS;MACtB;IACI,KAAKM,aAAa;MAAE;QAClB,OAAOL,SAAS;MACtB;IACI,KAAKG,kBAAkB;MAAE;QACvB,OAAOxD,IAAI,CAAC2D,KAAK,CAACN,SAAS,GAAG,CAACD,SAAS,GAAGC,SAAS,IAAI,CAAC,CAAC;MAChE;IACI,KAAKE,cAAc;IACnB;MAAS;QACP,IAAIT,YAAY,IAAIO,SAAS,IAAIP,YAAY,IAAIM,SAAS,EAAE;UAC1D,OAAON,YAAY;QAC3B,CAAO,MAAM,IAAIO,SAAS,GAAGD,SAAS,EAAE;UAChC,OAAOC,SAAS;QACxB,CAAO,MAAM,IAAIP,YAAY,GAAGO,SAAS,EAAE;UACnC,OAAOA,SAAS;QACxB,CAAO,MAAM;UACL,OAAOD,SAAS;QACxB;MACA;EACA;AACA,CAAC;AACI,MAACQ,eAAe,GAAGC,UAAU,CAAC;EACjCC,IAAI,EAAE,mBAAmB;EACzBC,iBAAiB,EAAEA,CAACxD,KAAK,EAAEiB,GAAG,EAAEM,KAAK,KAAK;IACxC,MAAMf,IAAI,GAAGT,gBAAgB,CAACC,KAAK,EAAEiB,GAAG,EAAEM,KAAK,EAAE,QAAQ,CAAC;IAC1D,OAAO,CAACf,IAAI,CAACC,IAAI,EAAED,IAAI,CAACD,MAAM,CAAC;EACnC,CAAG;EACDkD,cAAc,EAAEA,CAACzD,KAAK,EAAEiB,GAAG,EAAEM,KAAK,KAAK;IACrC,MAAMf,IAAI,GAAGT,gBAAgB,CAACC,KAAK,EAAEiB,GAAG,EAAEM,KAAK,EAAE,KAAK,CAAC;IACvD,OAAO,CAACf,IAAI,CAACC,IAAI,EAAED,IAAI,CAACD,MAAM,CAAC;EACnC,CAAG;EACDmD,eAAe,EAAEA,CAAC1D,KAAK,EAAE2D,WAAW,EAAErB,SAAS,EAAEsB,UAAU,EAAErC,KAAK,EAAEiB,cAAc,KAAKH,SAAS,CAACrC,KAAK,EAAE2D,WAAW,EAAErB,SAAS,EAAEsB,UAAU,EAAErC,KAAK,EAAE,QAAQ,EAAEiB,cAAc,CAAC;EAC5KqB,YAAY,EAAEA,CAAC7D,KAAK,EAAE8D,QAAQ,EAAExB,SAAS,EAAEyB,SAAS,EAAExC,KAAK,EAAEiB,cAAc,KAAKH,SAAS,CAACrC,KAAK,EAAE8D,QAAQ,EAAExB,SAAS,EAAEyB,SAAS,EAAExC,KAAK,EAAE,KAAK,EAAEiB,cAAc,CAAC;EAC9JwB,4BAA4B,EAAEA,CAAChE,KAAK,EAAE4D,UAAU,EAAErC,KAAK,KAAKD,QAAQ,CAACtB,KAAK,EAAEuB,KAAK,EAAEqC,UAAU,EAAE,QAAQ,CAAC;EACxGK,+BAA+B,EAAEA,CAACjE,KAAK,EAAEkE,UAAU,EAAEN,UAAU,EAAErC,KAAK,KAAK;IACzE,MAAMf,IAAI,GAAGT,gBAAgB,CAACC,KAAK,EAAEkE,UAAU,EAAE3C,KAAK,EAAE,QAAQ,CAAC;IACjE,MAAMsB,SAAS,GAAGe,UAAU,GAAG5D,KAAK,CAAC2C,KAAK;IAC1C,IAAIpC,MAAM,GAAGC,IAAI,CAACD,MAAM,GAAGC,IAAI,CAACC,IAAI;IACpC,IAAI0D,SAAS,GAAGD,UAAU;IAC1B,OAAOC,SAAS,GAAGnE,KAAK,CAACmB,WAAW,GAAG,CAAC,IAAIZ,MAAM,GAAGsC,SAAS,EAAE;MAC9DsB,SAAS,EAAE;MACX5D,MAAM,IAAIR,gBAAgB,CAACC,KAAK,EAAEkE,UAAU,EAAE3C,KAAK,EAAE,QAAQ,CAAC,CAACd,IAAI;IACzE;IACI,OAAO0D,SAAS;EACpB,CAAG;EACDzC,uBAAuB;EACvBM,sBAAsB;EACtBoC,yBAAyB,EAAEA,CAACpE,KAAK,EAAE+D,SAAS,EAAExC,KAAK,KAAKD,QAAQ,CAACtB,KAAK,EAAEuB,KAAK,EAAEwC,SAAS,EAAE,KAAK,CAAC;EAChGM,4BAA4B,EAAEA,CAACrE,KAAK,EAAEkE,UAAU,EAAEH,SAAS,EAAExC,KAAK,KAAK;IACrE,MAAM;MAAEH,QAAQ;MAAEsB;IAAM,CAAE,GAAG1C,KAAK;IAClC,MAAMQ,IAAI,GAAGT,gBAAgB,CAACC,KAAK,EAAEkE,UAAU,EAAE3C,KAAK,EAAE,KAAK,CAAC;IAC9D,MAAMsB,SAAS,GAAGkB,SAAS,GAAGrB,MAAM;IACpC,IAAInC,MAAM,GAAGC,IAAI,CAACC,IAAI,GAAGD,IAAI,CAACD,MAAM;IACpC,IAAI4D,SAAS,GAAGD,UAAU;IAC1B,OAAOC,SAAS,GAAG/C,QAAQ,GAAG,CAAC,IAAIb,MAAM,GAAGsC,SAAS,EAAE;MACrDsB,SAAS,EAAE;MACX5D,MAAM,IAAIR,gBAAgB,CAACC,KAAK,EAAEmE,SAAS,EAAE5C,KAAK,EAAE,KAAK,CAAC,CAACd,IAAI;IACrE;IACI,OAAO0D,SAAS;EACpB,CAAG;EACDG,gBAAgB,EAAEA,CAACC,QAAQ,EAAEhD,KAAK,KAAK;IACrC,MAAMiD,UAAU,GAAGA,CAAC;MAAEb,WAAW;MAAEG;IAAQ,CAAE,EAAEW,WAAW,KAAK;MAC7D,IAAIC,EAAE,EAAEC,EAAE;MACVF,WAAW,GAAGG,WAAW,CAACH,WAAW,CAAC,GAAG,IAAI,GAAGA,WAAW;MAC3D,IAAII,QAAQ,CAAClB,WAAW,CAAC,EAAE;QACzBpC,KAAK,CAACuD,KAAK,CAAC5C,sBAAsB,GAAGzC,IAAI,CAACF,GAAG,CAACgC,KAAK,CAACuD,KAAK,CAAC5C,sBAAsB,EAAEyB,WAAW,GAAG,CAAC,CAAC;MAC1G;MACM,IAAIkB,QAAQ,CAACf,QAAQ,CAAC,EAAE;QACtBvC,KAAK,CAACuD,KAAK,CAAClD,mBAAmB,GAAGnC,IAAI,CAACF,GAAG,CAACgC,KAAK,CAACuD,KAAK,CAAClD,mBAAmB,EAAEkC,QAAQ,GAAG,CAAC,CAAC;MACjG;MACM,CAACY,EAAE,GAAGH,QAAQ,CAACQ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGL,EAAE,CAACM,iBAAiB,CAACF,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;MACrF,IAAIL,WAAW,EACb,CAACE,EAAE,GAAGJ,QAAQ,CAACU,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGN,EAAE,CAACO,YAAY,EAAE;IAClE,CAAK;IACD,MAAMC,qBAAqB,GAAGA,CAACxB,WAAW,EAAEc,WAAW,KAAK;MAC1DD,UAAU,CAAC;QACTb;MACR,CAAO,EAAEc,WAAW,CAAC;IACrB,CAAK;IACD,MAAMW,kBAAkB,GAAGA,CAACtB,QAAQ,EAAEW,WAAW,KAAK;MACpDD,UAAU,CAAC;QACTV;MACR,CAAO,EAAEW,WAAW,CAAC;IACrB,CAAK;IACDY,MAAM,CAACC,MAAM,CAACf,QAAQ,CAACU,KAAK,EAAE;MAC5BE,qBAAqB;MACrBC,kBAAkB;MAClBZ;IACN,CAAK,CAAC;EACN,CAAG;EACDe,SAAS,EAAEA,CAAC;IACVtD,oBAAoB,GAAGuD,8BAA8B;IACrD7D,kBAAkB,GAAG6D;EACzB,CAAG,KAAK;IACJ,MAAMjE,KAAK,GAAG;MACZ3B,MAAM,EAAE,EAAE;MACVqC,oBAAoB;MACpBN,kBAAkB;MAClBO,sBAAsB,EAAE,CAAC,CAAC;MAC1BN,mBAAmB,EAAE,CAAC,CAAC;MACvB/B,GAAG,EAAE;IACX,CAAK;IACD,OAAO0B,KAAK;EAChB,CAAG;EACDkE,UAAU,EAAE,KAAK;EACjBC,aAAa,EAAEA,CAAC;IAAEC,WAAW;IAAEC;EAAS,CAAE,KAAK;IAC7C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACC,UAAU,CAACL,WAAW,CAAC,EAAE;QAC5BM,UAAU,CAACvG,KAAK,EAAE;AAC1B;AACA,sBAAsB,OAAOiG,WAAW;AACxC,SAAS,CAAC;MACV;MACM,IAAI,CAACK,UAAU,CAACJ,SAAS,CAAC,EAAE;QAC1BK,UAAU,CAACvG,KAAK,EAAE;AAC1B;AACA,sBAAsB,OAAOkG,SAAS;AACtC,SAAS,CAAC;MACV;IACA;EACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}