{"ast": null, "code": "import { defineComponent, useAttrs as useAttrs$1, ref, computed, onBeforeUnmount, onMounted, openBlock, createBlock, unref, withCtx, createElementVNode, normalizeClass, normalizeStyle, createVNode, createElementBlock, renderSlot, Fragment, renderList, createTextVNode, toDisplayString, mergeProps, withKey<PERSON>, withModifiers, createSlots } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { onClickOutside } from '@vueuse/core';\nimport { Loading } from '@element-plus/icons-vue';\nimport { ElInput } from '../../input/index.mjs';\nimport { ElScrollbar } from '../../scrollbar/index.mjs';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { autocompleteProps, autocompleteEmits } from './autocomplete.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useAttrs } from '../../../hooks/use-attrs/index.mjs';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { isArray } from '@vue/shared';\nimport { INPUT_EVENT, UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nconst COMPONENT_NAME = \"ElAutocomplete\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME,\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: autocompleteProps,\n  emits: autocompleteEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const attrs = useAttrs();\n    const rawAttrs = useAttrs$1();\n    const disabled = useFormDisabled();\n    const ns = useNamespace(\"autocomplete\");\n    const inputRef = ref();\n    const regionRef = ref();\n    const popperRef = ref();\n    const listboxRef = ref();\n    let readonly = false;\n    let ignoreFocusEvent = false;\n    const suggestions = ref([]);\n    const highlightedIndex = ref(-1);\n    const dropdownWidth = ref(\"\");\n    const activated = ref(false);\n    const suggestionDisabled = ref(false);\n    const loading = ref(false);\n    const listboxId = useId();\n    const styles = computed(() => rawAttrs.style);\n    const suggestionVisible = computed(() => {\n      const isValidData = suggestions.value.length > 0;\n      return (isValidData || loading.value) && activated.value;\n    });\n    const suggestionLoading = computed(() => !props.hideLoading && loading.value);\n    const refInput = computed(() => {\n      if (inputRef.value) {\n        return Array.from(inputRef.value.$el.querySelectorAll(\"input\"));\n      }\n      return [];\n    });\n    const onSuggestionShow = () => {\n      if (suggestionVisible.value) {\n        dropdownWidth.value = `${inputRef.value.$el.offsetWidth}px`;\n      }\n    };\n    const onHide = () => {\n      highlightedIndex.value = -1;\n    };\n    const getData = async queryString => {\n      if (suggestionDisabled.value) return;\n      const cb = suggestionList => {\n        loading.value = false;\n        if (suggestionDisabled.value) return;\n        if (isArray(suggestionList)) {\n          suggestions.value = suggestionList;\n          highlightedIndex.value = props.highlightFirstItem ? 0 : -1;\n        } else {\n          throwError(COMPONENT_NAME, \"autocomplete suggestions must be an array\");\n        }\n      };\n      loading.value = true;\n      if (isArray(props.fetchSuggestions)) {\n        cb(props.fetchSuggestions);\n      } else {\n        const result = await props.fetchSuggestions(queryString, cb);\n        if (isArray(result)) cb(result);\n      }\n    };\n    const debouncedGetData = debounce(getData, props.debounce);\n    const handleInput = value => {\n      const valuePresented = !!value;\n      emit(INPUT_EVENT, value);\n      emit(UPDATE_MODEL_EVENT, value);\n      suggestionDisabled.value = false;\n      activated.value || (activated.value = valuePresented);\n      if (!props.triggerOnFocus && !value) {\n        suggestionDisabled.value = true;\n        suggestions.value = [];\n        return;\n      }\n      debouncedGetData(value);\n    };\n    const handleMouseDown = event => {\n      var _a;\n      if (disabled.value) return;\n      if (((_a = event.target) == null ? void 0 : _a.tagName) !== \"INPUT\" || refInput.value.includes(document.activeElement)) {\n        activated.value = true;\n      }\n    };\n    const handleChange = value => {\n      emit(CHANGE_EVENT, value);\n    };\n    const handleFocus = evt => {\n      var _a;\n      if (!ignoreFocusEvent) {\n        activated.value = true;\n        emit(\"focus\", evt);\n        const queryString = (_a = props.modelValue) != null ? _a : \"\";\n        if (props.triggerOnFocus && !readonly) {\n          debouncedGetData(String(queryString));\n        }\n      } else {\n        ignoreFocusEvent = false;\n      }\n    };\n    const handleBlur = evt => {\n      setTimeout(() => {\n        var _a;\n        if ((_a = popperRef.value) == null ? void 0 : _a.isFocusInsideContent()) {\n          ignoreFocusEvent = true;\n          return;\n        }\n        activated.value && close();\n        emit(\"blur\", evt);\n      });\n    };\n    const handleClear = () => {\n      activated.value = false;\n      emit(UPDATE_MODEL_EVENT, \"\");\n      emit(\"clear\");\n    };\n    const handleKeyEnter = async () => {\n      if (suggestionVisible.value && highlightedIndex.value >= 0 && highlightedIndex.value < suggestions.value.length) {\n        handleSelect(suggestions.value[highlightedIndex.value]);\n      } else if (props.selectWhenUnmatched) {\n        emit(\"select\", {\n          value: props.modelValue\n        });\n        suggestions.value = [];\n        highlightedIndex.value = -1;\n      }\n    };\n    const handleKeyEscape = evt => {\n      if (suggestionVisible.value) {\n        evt.preventDefault();\n        evt.stopPropagation();\n        close();\n      }\n    };\n    const close = () => {\n      activated.value = false;\n    };\n    const focus = () => {\n      var _a;\n      (_a = inputRef.value) == null ? void 0 : _a.focus();\n    };\n    const blur = () => {\n      var _a;\n      (_a = inputRef.value) == null ? void 0 : _a.blur();\n    };\n    const handleSelect = async item => {\n      emit(INPUT_EVENT, item[props.valueKey]);\n      emit(UPDATE_MODEL_EVENT, item[props.valueKey]);\n      emit(\"select\", item);\n      suggestions.value = [];\n      highlightedIndex.value = -1;\n    };\n    const highlight = index => {\n      if (!suggestionVisible.value || loading.value) return;\n      if (index < 0) {\n        highlightedIndex.value = -1;\n        return;\n      }\n      if (index >= suggestions.value.length) {\n        index = suggestions.value.length - 1;\n      }\n      const suggestion = regionRef.value.querySelector(`.${ns.be(\"suggestion\", \"wrap\")}`);\n      const suggestionList = suggestion.querySelectorAll(`.${ns.be(\"suggestion\", \"list\")} li`);\n      const highlightItem = suggestionList[index];\n      const scrollTop = suggestion.scrollTop;\n      const {\n        offsetTop,\n        scrollHeight\n      } = highlightItem;\n      if (offsetTop + scrollHeight > scrollTop + suggestion.clientHeight) {\n        suggestion.scrollTop += scrollHeight;\n      }\n      if (offsetTop < scrollTop) {\n        suggestion.scrollTop -= scrollHeight;\n      }\n      highlightedIndex.value = index;\n      inputRef.value.ref.setAttribute(\"aria-activedescendant\", `${listboxId.value}-item-${highlightedIndex.value}`);\n    };\n    const stopHandle = onClickOutside(listboxRef, () => {\n      var _a;\n      if ((_a = popperRef.value) == null ? void 0 : _a.isFocusInsideContent()) return;\n      suggestionVisible.value && close();\n    });\n    onBeforeUnmount(() => {\n      stopHandle == null ? void 0 : stopHandle();\n    });\n    onMounted(() => {\n      inputRef.value.ref.setAttribute(\"role\", \"textbox\");\n      inputRef.value.ref.setAttribute(\"aria-autocomplete\", \"list\");\n      inputRef.value.ref.setAttribute(\"aria-controls\", \"id\");\n      inputRef.value.ref.setAttribute(\"aria-activedescendant\", `${listboxId.value}-item-${highlightedIndex.value}`);\n      readonly = inputRef.value.ref.hasAttribute(\"readonly\");\n    });\n    expose({\n      highlightedIndex,\n      activated,\n      loading,\n      inputRef,\n      popperRef,\n      suggestions,\n      handleSelect,\n      handleKeyEnter,\n      focus,\n      blur,\n      close,\n      highlight,\n      getData\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTooltip), {\n        ref_key: \"popperRef\",\n        ref: popperRef,\n        visible: unref(suggestionVisible),\n        placement: _ctx.placement,\n        \"fallback-placements\": [\"bottom-start\", \"top-start\"],\n        \"popper-class\": [unref(ns).e(\"popper\"), _ctx.popperClass],\n        teleported: _ctx.teleported,\n        \"append-to\": _ctx.appendTo,\n        \"gpu-acceleration\": false,\n        pure: \"\",\n        \"manual-mode\": \"\",\n        effect: \"light\",\n        trigger: \"click\",\n        transition: `${unref(ns).namespace.value}-zoom-in-top`,\n        persistent: \"\",\n        role: \"listbox\",\n        onBeforeShow: onSuggestionShow,\n        onHide\n      }, {\n        content: withCtx(() => [createElementVNode(\"div\", {\n          ref_key: \"regionRef\",\n          ref: regionRef,\n          class: normalizeClass([unref(ns).b(\"suggestion\"), unref(ns).is(\"loading\", unref(suggestionLoading))]),\n          style: normalizeStyle({\n            [_ctx.fitInputWidth ? \"width\" : \"minWidth\"]: dropdownWidth.value,\n            outline: \"none\"\n          }),\n          role: \"region\"\n        }, [createVNode(unref(ElScrollbar), {\n          id: unref(listboxId),\n          tag: \"ul\",\n          \"wrap-class\": unref(ns).be(\"suggestion\", \"wrap\"),\n          \"view-class\": unref(ns).be(\"suggestion\", \"list\"),\n          role: \"listbox\"\n        }, {\n          default: withCtx(() => [unref(suggestionLoading) ? (openBlock(), createElementBlock(\"li\", {\n            key: 0\n          }, [renderSlot(_ctx.$slots, \"loading\", {}, () => [createVNode(unref(ElIcon), {\n            class: normalizeClass(unref(ns).is(\"loading\"))\n          }, {\n            default: withCtx(() => [createVNode(unref(Loading))]),\n            _: 1\n          }, 8, [\"class\"])])])) : (openBlock(true), createElementBlock(Fragment, {\n            key: 1\n          }, renderList(suggestions.value, (item, index) => {\n            return openBlock(), createElementBlock(\"li\", {\n              id: `${unref(listboxId)}-item-${index}`,\n              key: index,\n              class: normalizeClass({\n                highlighted: highlightedIndex.value === index\n              }),\n              role: \"option\",\n              \"aria-selected\": highlightedIndex.value === index,\n              onClick: $event => handleSelect(item)\n            }, [renderSlot(_ctx.$slots, \"default\", {\n              item\n            }, () => [createTextVNode(toDisplayString(item[_ctx.valueKey]), 1)])], 10, [\"id\", \"aria-selected\", \"onClick\"]);\n          }), 128))]),\n          _: 3\n        }, 8, [\"id\", \"wrap-class\", \"view-class\"])], 6)]),\n        default: withCtx(() => [createElementVNode(\"div\", {\n          ref_key: \"listboxRef\",\n          ref: listboxRef,\n          class: normalizeClass([unref(ns).b(), _ctx.$attrs.class]),\n          style: normalizeStyle(unref(styles)),\n          role: \"combobox\",\n          \"aria-haspopup\": \"listbox\",\n          \"aria-expanded\": unref(suggestionVisible),\n          \"aria-owns\": unref(listboxId)\n        }, [createVNode(unref(ElInput), mergeProps({\n          ref_key: \"inputRef\",\n          ref: inputRef\n        }, unref(attrs), {\n          clearable: _ctx.clearable,\n          disabled: unref(disabled),\n          name: _ctx.name,\n          \"model-value\": _ctx.modelValue,\n          \"aria-label\": _ctx.ariaLabel,\n          onInput: handleInput,\n          onChange: handleChange,\n          onFocus: handleFocus,\n          onBlur: handleBlur,\n          onClear: handleClear,\n          onKeydown: [withKeys(withModifiers($event => highlight(highlightedIndex.value - 1), [\"prevent\"]), [\"up\"]), withKeys(withModifiers($event => highlight(highlightedIndex.value + 1), [\"prevent\"]), [\"down\"]), withKeys(handleKeyEnter, [\"enter\"]), withKeys(close, [\"tab\"]), withKeys(handleKeyEscape, [\"esc\"])],\n          onMousedown: handleMouseDown\n        }), createSlots({\n          _: 2\n        }, [_ctx.$slots.prepend ? {\n          name: \"prepend\",\n          fn: withCtx(() => [renderSlot(_ctx.$slots, \"prepend\")])\n        } : void 0, _ctx.$slots.append ? {\n          name: \"append\",\n          fn: withCtx(() => [renderSlot(_ctx.$slots, \"append\")])\n        } : void 0, _ctx.$slots.prefix ? {\n          name: \"prefix\",\n          fn: withCtx(() => [renderSlot(_ctx.$slots, \"prefix\")])\n        } : void 0, _ctx.$slots.suffix ? {\n          name: \"suffix\",\n          fn: withCtx(() => [renderSlot(_ctx.$slots, \"suffix\")])\n        } : void 0]), 1040, [\"clearable\", \"disabled\", \"name\", \"model-value\", \"aria-label\", \"onKeydown\"])], 14, [\"aria-expanded\", \"aria-owns\"])]),\n        _: 3\n      }, 8, [\"visible\", \"placement\", \"popper-class\", \"teleported\", \"append-to\", \"transition\"]);\n    };\n  }\n});\nvar Autocomplete = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"autocomplete.vue\"]]);\nexport { Autocomplete as default };", "map": {"version": 3, "names": ["name", "COMPONENT_NAME", "inheritAttrs", "attrs", "useAttrs", "rawAttrs", "useAttrs$1", "disabled", "useFormDisabled", "ns", "useNamespace", "inputRef", "ref", "regionRef", "popperRef", "listboxRef", "readonly", "ignoreFocusEvent", "suggestions", "highlightedIndex", "dropdownWidth", "activated", "suggestionDisabled", "loading", "listboxId", "useId", "styles", "computed", "style", "suggestionVisible", "isValidData", "value", "length", "suggestionLoading", "props", "hideLoading", "refInput", "Array", "from", "$el", "querySelectorAll", "onSuggestionShow", "offsetWidth", "onHide", "getData", "queryString", "cb", "suggestionList", "isArray", "highlightFirstItem", "throwError", "fetchSuggestions", "result", "debouncedGetData", "debounce", "handleInput", "valuePresented", "emit", "INPUT_EVENT", "UPDATE_MODEL_EVENT", "triggerOnFocus", "handleMouseDown", "event", "_a", "target", "tagName", "includes", "document", "activeElement", "handleChange", "CHANGE_EVENT", "handleFocus", "evt", "modelValue", "String", "handleBlur", "setTimeout", "isFocusInsideContent", "close", "handleClear", "handleKeyEnter", "handleSelect", "selectWhenUnmatched", "handleKeyEscape", "preventDefault", "stopPropagation", "focus", "blur", "item", "valueKey", "highlight", "index", "suggestion", "querySelector", "be", "highlightItem", "scrollTop", "offsetTop", "scrollHeight", "clientHeight", "setAttribute", "stopHandle", "onClickOutside", "onBeforeUnmount", "onMounted", "hasAttribute", "expose", "_ctx", "_cache", "openBlock", "createBlock", "unref", "ElTooltip", "ref_key", "visible", "placement", "e", "popperClass", "teleported", "appendTo", "pure"], "sources": ["../../../../../../packages/components/autocomplete/src/autocomplete.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"popperRef\"\n    :visible=\"suggestionVisible\"\n    :placement=\"placement\"\n    :fallback-placements=\"['bottom-start', 'top-start']\"\n    :popper-class=\"[ns.e('popper'), popperClass]\"\n    :teleported=\"teleported\"\n    :append-to=\"appendTo\"\n    :gpu-acceleration=\"false\"\n    pure\n    manual-mode\n    effect=\"light\"\n    trigger=\"click\"\n    :transition=\"`${ns.namespace.value}-zoom-in-top`\"\n    persistent\n    role=\"listbox\"\n    @before-show=\"onSuggestionShow\"\n    @hide=\"onHide\"\n  >\n    <div\n      ref=\"listboxRef\"\n      :class=\"[ns.b(), $attrs.class]\"\n      :style=\"styles\"\n      role=\"combobox\"\n      aria-haspopup=\"listbox\"\n      :aria-expanded=\"suggestionVisible\"\n      :aria-owns=\"listboxId\"\n    >\n      <el-input\n        ref=\"inputRef\"\n        v-bind=\"attrs\"\n        :clearable=\"clearable\"\n        :disabled=\"disabled\"\n        :name=\"name\"\n        :model-value=\"modelValue\"\n        :aria-label=\"ariaLabel\"\n        @input=\"handleInput\"\n        @change=\"handleChange\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n        @clear=\"handleClear\"\n        @keydown.up.prevent=\"highlight(highlightedIndex - 1)\"\n        @keydown.down.prevent=\"highlight(highlightedIndex + 1)\"\n        @keydown.enter=\"handleKeyEnter\"\n        @keydown.tab=\"close\"\n        @keydown.esc=\"handleKeyEscape\"\n        @mousedown=\"handleMouseDown\"\n      >\n        <template v-if=\"$slots.prepend\" #prepend>\n          <slot name=\"prepend\" />\n        </template>\n        <template v-if=\"$slots.append\" #append>\n          <slot name=\"append\" />\n        </template>\n        <template v-if=\"$slots.prefix\" #prefix>\n          <slot name=\"prefix\" />\n        </template>\n        <template v-if=\"$slots.suffix\" #suffix>\n          <slot name=\"suffix\" />\n        </template>\n      </el-input>\n    </div>\n    <template #content>\n      <div\n        ref=\"regionRef\"\n        :class=\"[ns.b('suggestion'), ns.is('loading', suggestionLoading)]\"\n        :style=\"{\n          [fitInputWidth ? 'width' : 'minWidth']: dropdownWidth,\n          outline: 'none',\n        }\"\n        role=\"region\"\n      >\n        <el-scrollbar\n          :id=\"listboxId\"\n          tag=\"ul\"\n          :wrap-class=\"ns.be('suggestion', 'wrap')\"\n          :view-class=\"ns.be('suggestion', 'list')\"\n          role=\"listbox\"\n        >\n          <li v-if=\"suggestionLoading\">\n            <slot name=\"loading\">\n              <el-icon :class=\"ns.is('loading')\">\n                <Loading />\n              </el-icon>\n            </slot>\n          </li>\n          <template v-else>\n            <li\n              v-for=\"(item, index) in suggestions\"\n              :id=\"`${listboxId}-item-${index}`\"\n              :key=\"index\"\n              :class=\"{ highlighted: highlightedIndex === index }\"\n              role=\"option\"\n              :aria-selected=\"highlightedIndex === index\"\n              @click=\"handleSelect(item)\"\n            >\n              <slot :item=\"item\">{{ item[valueKey] }}</slot>\n            </li>\n          </template>\n        </el-scrollbar>\n      </div>\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  onBeforeUnmount,\n  onMounted,\n  ref,\n  useAttrs as useRawAttrs,\n} from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { onClickOutside } from '@vueuse/core'\nimport { Loading } from '@element-plus/icons-vue'\nimport { useAttrs, useId, useNamespace } from '@element-plus/hooks'\nimport { isArray, throwError } from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport ElInput from '@element-plus/components/input'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElIcon from '@element-plus/components/icon'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { autocompleteEmits, autocompleteProps } from './autocomplete'\nimport type { AutocompleteData } from './autocomplete'\n\nimport type { StyleValue } from 'vue'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\nimport type { InputInstance } from '@element-plus/components/input'\n\nconst COMPONENT_NAME = 'ElAutocomplete'\ndefineOptions({\n  name: COMPONENT_NAME,\n  inheritAttrs: false,\n})\n\nconst props = defineProps(autocompleteProps)\nconst emit = defineEmits(autocompleteEmits)\n\nconst attrs = useAttrs()\nconst rawAttrs = useRawAttrs()\nconst disabled = useFormDisabled()\nconst ns = useNamespace('autocomplete')\n\nconst inputRef = ref<InputInstance>()\nconst regionRef = ref<HTMLElement>()\nconst popperRef = ref<TooltipInstance>()\nconst listboxRef = ref<HTMLElement>()\n\nlet readonly = false\nlet ignoreFocusEvent = false\nconst suggestions = ref<AutocompleteData>([])\nconst highlightedIndex = ref(-1)\nconst dropdownWidth = ref('')\nconst activated = ref(false)\nconst suggestionDisabled = ref(false)\nconst loading = ref(false)\n\nconst listboxId = useId()\nconst styles = computed(() => rawAttrs.style as StyleValue)\n\nconst suggestionVisible = computed(() => {\n  const isValidData = suggestions.value.length > 0\n  return (isValidData || loading.value) && activated.value\n})\n\nconst suggestionLoading = computed(() => !props.hideLoading && loading.value)\n\nconst refInput = computed<HTMLInputElement[]>(() => {\n  if (inputRef.value) {\n    return Array.from<HTMLInputElement>(\n      inputRef.value.$el.querySelectorAll('input')\n    )\n  }\n  return []\n})\n\nconst onSuggestionShow = () => {\n  if (suggestionVisible.value) {\n    dropdownWidth.value = `${inputRef.value!.$el.offsetWidth}px`\n  }\n}\n\nconst onHide = () => {\n  highlightedIndex.value = -1\n}\n\nconst getData = async (queryString: string) => {\n  if (suggestionDisabled.value) return\n\n  const cb = (suggestionList: AutocompleteData) => {\n    loading.value = false\n    if (suggestionDisabled.value) return\n\n    if (isArray(suggestionList)) {\n      suggestions.value = suggestionList\n      highlightedIndex.value = props.highlightFirstItem ? 0 : -1\n    } else {\n      throwError(COMPONENT_NAME, 'autocomplete suggestions must be an array')\n    }\n  }\n\n  loading.value = true\n  if (isArray(props.fetchSuggestions)) {\n    cb(props.fetchSuggestions)\n  } else {\n    const result = await props.fetchSuggestions(queryString, cb)\n    if (isArray(result)) cb(result)\n  }\n}\nconst debouncedGetData = debounce(getData, props.debounce)\n\nconst handleInput = (value: string) => {\n  const valuePresented = !!value\n\n  emit(INPUT_EVENT, value)\n  emit(UPDATE_MODEL_EVENT, value)\n\n  suggestionDisabled.value = false\n  activated.value ||= valuePresented\n\n  if (!props.triggerOnFocus && !value) {\n    suggestionDisabled.value = true\n    suggestions.value = []\n    return\n  }\n\n  debouncedGetData(value)\n}\n\nconst handleMouseDown = (event: MouseEvent) => {\n  if (disabled.value) return\n  if (\n    (event.target as HTMLElement)?.tagName !== 'INPUT' ||\n    refInput.value.includes(document.activeElement as HTMLInputElement)\n  ) {\n    activated.value = true\n  }\n}\n\nconst handleChange = (value: string) => {\n  emit(CHANGE_EVENT, value)\n}\n\nconst handleFocus = (evt: FocusEvent) => {\n  if (!ignoreFocusEvent) {\n    activated.value = true\n    emit('focus', evt)\n    const queryString = props.modelValue ?? ''\n    if (props.triggerOnFocus && !readonly) {\n      debouncedGetData(String(queryString))\n    }\n  } else {\n    ignoreFocusEvent = false\n  }\n}\n\nconst handleBlur = (evt: FocusEvent) => {\n  setTimeout(() => {\n    // validate current focus event is inside el-tooltip-content\n    // if so, ignore the blur event and the next focus event\n    if (popperRef.value?.isFocusInsideContent()) {\n      ignoreFocusEvent = true\n      return\n    }\n    activated.value && close()\n    emit('blur', evt)\n  })\n}\n\nconst handleClear = () => {\n  activated.value = false\n  emit(UPDATE_MODEL_EVENT, '')\n  emit('clear')\n}\n\nconst handleKeyEnter = async () => {\n  if (\n    suggestionVisible.value &&\n    highlightedIndex.value >= 0 &&\n    highlightedIndex.value < suggestions.value.length\n  ) {\n    handleSelect(suggestions.value[highlightedIndex.value])\n  } else if (props.selectWhenUnmatched) {\n    emit('select', { value: props.modelValue })\n    suggestions.value = []\n    highlightedIndex.value = -1\n  }\n}\n\nconst handleKeyEscape = (evt: Event) => {\n  if (suggestionVisible.value) {\n    evt.preventDefault()\n    evt.stopPropagation()\n    close()\n  }\n}\n\nconst close = () => {\n  activated.value = false\n}\n\nconst focus = () => {\n  inputRef.value?.focus()\n}\n\nconst blur = () => {\n  inputRef.value?.blur()\n}\n\nconst handleSelect = async (item: any) => {\n  emit(INPUT_EVENT, item[props.valueKey])\n  emit(UPDATE_MODEL_EVENT, item[props.valueKey])\n  emit('select', item)\n  suggestions.value = []\n  highlightedIndex.value = -1\n}\n\nconst highlight = (index: number) => {\n  if (!suggestionVisible.value || loading.value) return\n\n  if (index < 0) {\n    highlightedIndex.value = -1\n    return\n  }\n\n  if (index >= suggestions.value.length) {\n    index = suggestions.value.length - 1\n  }\n  const suggestion = regionRef.value!.querySelector(\n    `.${ns.be('suggestion', 'wrap')}`\n  )!\n  const suggestionList = suggestion.querySelectorAll<HTMLElement>(\n    `.${ns.be('suggestion', 'list')} li`\n  )!\n  const highlightItem = suggestionList[index]\n  const scrollTop = suggestion.scrollTop\n  const { offsetTop, scrollHeight } = highlightItem\n\n  if (offsetTop + scrollHeight > scrollTop + suggestion.clientHeight) {\n    suggestion.scrollTop += scrollHeight\n  }\n  if (offsetTop < scrollTop) {\n    suggestion.scrollTop -= scrollHeight\n  }\n  highlightedIndex.value = index\n  // TODO: use Volar generate dts to fix it.\n  ;(inputRef.value as any).ref!.setAttribute(\n    'aria-activedescendant',\n    `${listboxId.value}-item-${highlightedIndex.value}`\n  )\n}\n\nconst stopHandle = onClickOutside(listboxRef, () => {\n  // Prevent closing if focus is inside popper content\n  if (popperRef.value?.isFocusInsideContent()) return\n  suggestionVisible.value && close()\n})\n\nonBeforeUnmount(() => {\n  stopHandle?.()\n})\n\nonMounted(() => {\n  // TODO: use Volar generate dts to fix it.\n  ;(inputRef.value as any).ref!.setAttribute('role', 'textbox')\n  ;(inputRef.value as any).ref!.setAttribute('aria-autocomplete', 'list')\n  ;(inputRef.value as any).ref!.setAttribute('aria-controls', 'id')\n  ;(inputRef.value as any).ref!.setAttribute(\n    'aria-activedescendant',\n    `${listboxId.value}-item-${highlightedIndex.value}`\n  )\n  // get readonly attr\n  readonly = (inputRef.value as any).ref!.hasAttribute('readonly')\n})\n\ndefineExpose({\n  /** @description the index of the currently highlighted item */\n  highlightedIndex,\n  /** @description autocomplete whether activated */\n  activated,\n  /** @description remote search loading status */\n  loading,\n  /** @description el-input component instance */\n  inputRef,\n  /** @description el-tooltip component instance */\n  popperRef,\n  /** @description fetch suggestions result */\n  suggestions,\n  /** @description triggers when a suggestion is clicked */\n  handleSelect,\n  /** @description handle keyboard enter event */\n  handleKeyEnter,\n  /** @description focus the input element */\n  focus,\n  /** @description blur the input element */\n  blur,\n  /** @description close suggestion */\n  close,\n  /** @description highlight an item in a suggestion */\n  highlight,\n  /** @description loading suggestion list */\n  getData,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;mCAyIc;EACZA,IAAM,EAAAC,cAAA;EACNC,YAAc;AAChB;;;;;;;;;;IAKA,MAAMC,KAAA,GAAQC,QAAS;IACvB,MAAMC,QAAA,GAAWC,UAAY;IAC7B,MAAMC,QAAA,GAAWC,eAAgB;IAC3B,MAAAC,EAAA,GAAKC,YAAA,CAAa,cAAc;IAEtC,MAAMC,QAAA,GAAWC,GAAmB;IACpC,MAAMC,SAAA,GAAYD,GAAiB;IACnC,MAAME,SAAA,GAAYF,GAAqB;IACvC,MAAMG,UAAA,GAAaH,GAAiB;IAEpC,IAAII,QAAW;IACf,IAAIC,gBAAmB;IACjB,MAAAC,WAAA,GAAcN,GAAsB,GAAE;IACtC,MAAAO,gBAAA,GAAmBP,GAAA,CAAI,CAAE;IACzB,MAAAQ,aAAA,GAAgBR,GAAA,CAAI,EAAE;IACtB,MAAAS,SAAA,GAAYT,GAAA,CAAI,KAAK;IACrB,MAAAU,kBAAA,GAAqBV,GAAA,CAAI,KAAK;IAC9B,MAAAW,OAAA,GAAUX,GAAA,CAAI,KAAK;IAEzB,MAAMY,SAAA,GAAYC,KAAM;IACxB,MAAMC,MAAS,GAAAC,QAAA,CAAS,MAAMtB,QAAA,CAASuB,KAAmB;IAEpD,MAAAC,iBAAA,GAAoBF,QAAA,CAAS,MAAM;MACjC,MAAAG,WAAA,GAAcZ,WAAY,CAAAa,KAAA,CAAMC,MAAS;MACvC,QAAAF,WAAA,IAAeP,OAAQ,CAAAQ,KAAA,KAAUV,SAAU,CAAAU,KAAA;IAAA,CACpD;IAED,MAAME,iBAAA,GAAoBN,QAAS,OAAM,CAACO,KAAM,CAAAC,WAAA,IAAeZ,OAAA,CAAQQ,KAAK;IAEtE,MAAAK,QAAA,GAAWT,QAAA,CAA6B,MAAM;MAClD,IAAIhB,QAAA,CAASoB,KAAO;QAClB,OAAOM,KAAM,CAAAC,IAAA,CAAA3B,QAAA,CAAAoB,KAAA,CAAAQ,GAAA,CAAAC,gBAAA;MAAA;MAEb;IAAA,CACF;IACA,MAAAC,gBAAQ,GAAAA,CAAA;MACT,IAAAZ,iBAAA,CAAAE,KAAA;QAEDX,aAAA,CAAAW,KAAA,GAA+B,GAAApB,QAAA,CAAAoB,KAAA,CAAAQ,GAAA,CAAAG,WAAA;MAC7B;IACE;IACF,MAAAC,MAAA,GAAAA,CAAA;MACFxB,gBAAA,CAAAY,KAAA;IAEA;IACE,MAAAa,OAAA,SAAyBC,WAAA;MAC3B,IAAAvB,kBAAA,CAAAS,KAAA,EAEM;MACJ,MAAAe,EAAA,GAAAC,cAA8B;QAExBxB,OAAA,CAAAQ,KAA2C;QAC/C,IAAAT,kBAAgB,CAAAS,KAAA,EAChB;QAEI,IAAAiB,OAAA,CAAQD,cAAc,CAAG;UAC3B7B,WAAA,CAAYa,KAAQ,GAAAgB,cAAA;UACH5B,gBAAA,CAAAY,KAAA,GAAQG,KAAM,CAAAe,kBAAA,GAAqB,CAAI;QAAA,CACnD;UACLC,UAAA,CAAWjD,cAAA,EAAgB,2CAA2C;QAAA;MACxE,CACF;MAEAsB,OAAA,CAAQQ,KAAQ;MACZ,IAAAiB,OAAA,CAAQd,KAAM,CAAAiB,gBAAgB,CAAG;QACnCL,EAAA,CAAGZ,KAAA,CAAMiB,gBAAgB;MAAA,CACpB;QACL,MAAMC,MAAS,SAAMlB,KAAM,CAAAiB,gBAAA,CAAiBN,WAAA,EAAaC,EAAE;QAC3D,IAAIE,OAAQ,CAAAI,MAAM,CAAG,EACvBN,EAAA,CAAAM,MAAA;MAAA;IAEF;IAEM,MAAAC,gBAAiC,GAAAC,QAAA,CAAAV,OAAA,EAAAV,KAAA,CAAAoB,QAAA;IAC/B,MAAAC,WAAA,GAAAxB,KAAkB,IAAC;MAEzB,MAAAyB,cAAuB,KAAAzB,KAAA;MACvB0B,IAAA,CAAKC,WAAA,EAAA3B,KAAA;MAEL0B,IAAA,CAAAE,kBAA2B,EAAA5B,KAAA;MAC3BT,kBAAoB,CAAAS,KAAA;MAEpBV,SAAK,CAAAU,KAAwB,KAAAV,SAAA,CAACU,KAAO,GAAAyB,cAAA;MACnC,KAAAtB,KAAA,CAAA0B,cAA2B,KAAA7B,KAAA;QAC3BT,kBAAA,CAAAS,KAAqB;QACrBb,WAAA,CAAAa,KAAA;QACF;MAEA;MACFsB,gBAAA,CAAAtB,KAAA;IAEA,CAAM;IACJ,MAAI8B,eAAgB,GAAAC,KAAA;MAEjB,IAAAC,EAAA;MAGD,IAAAxD,QAAU,CAAQwB,KAAA,EACpB;MACF,MAAAgC,EAAA,GAAAD,KAAA,CAAAE,MAAA,qBAAAD,EAAA,CAAAE,OAAA,iBAAA7B,QAAA,CAAAL,KAAA,CAAAmC,QAAA,CAAAC,QAAA,CAAAC,aAAA;QAEM/C,SAAA,CAAAU,KAAA,GAAgB,IAAkB;MACtC;IAAwB,CAC1B;IAEM,MAAAsC,YAAA,GAAmCtC,KAAA;MACvC0B,IAAI,CAACa,YAAkB,EAAAvC,KAAA;IACrB;IACA,MAAAwC,WAAA,GAAiBC,GAAA;MACX,IAAAT,EAAA;MACF,KAAA9C,gBAAwB;QACTI,SAAA,CAAAU,KAAA;QACnB0B,IAAA,UAAAe,GAAA;QACK,MAAA3B,WAAA,IAAAkB,EAAA,GAAA7B,KAAA,CAAAuC,UAAA,YAAAV,EAAA;QACc,IAAA7B,KAAA,CAAA0B,cAAA,KAAA5C,QAAA;UACrBqC,gBAAA,CAAAqB,MAAA,CAAA7B,WAAA;QAAA;MAGF,CAAM;QACJ5B,gBAAiB;MAGf;IACE,CAAmB;IACnB,MAAA0D,UAAA,GAAAH,GAAA;MACFI,UAAA;QACA,IAAAb,EAAA;QACA,KAAKA,EAAA,GAAAjD,SAAW,CAAAiB,KAAA,qBAAAgC,EAAA,CAAAc,oBAAA;UACjB5D,gBAAA;UACH;QAEA;QACEI,SAAkB,CAAAU,KAAA,IAAA+C,KAAA;QAClBrB,IAAA,SAAAe,GAAA;MACA;IAAY,CACd;IAEA,MAAMO,WAAA,GAAAA,CAAA,KAA6B;MAE/B1D,SAAA,CAAAU,KAAA;MAIA0B,IAAA,CAAAE,kBAAyB;MAC3BF,IAAA;IACE;IACA,MAAAuB,cAAA,SAAAA,CAAA,KAAqB;MACrB,IAAAnD,iBAAyB,CAAAE,KAAA,IAAAZ,gBAAA,CAAAY,KAAA,SAAAZ,gBAAA,CAAAY,KAAA,GAAAb,WAAA,CAAAa,KAAA,CAAAC,MAAA;QAC3BiD,YAAA,CAAA/D,WAAA,CAAAa,KAAA,CAAAZ,gBAAA,CAAAY,KAAA;MAAA,CACF,UAAAG,KAAA,CAAAgD,mBAAA;QAEMzB,IAAA;UAAA1B,KAAkC,EAAAG,KAAA,CAAAuC;QAAA;QACtCvD,WAAA,CAAAa,KAAA,GAAsB,EAAO;QAC3BZ,gBAAmB,CAAAY,KAAA;MACnB;IACA,CAAM;IACR,MAAAoD,eAAA,GAAAX,GAAA;MACF,IAAA3C,iBAAA,CAAAE,KAAA;QAEAyC,GAAA,CAAAY,cAAoB;QAClBZ,GAAA,CAAAa,eAAkB;QACpBP,KAAA;MAEA;IACE;IACF,MAAAA,KAAA,GAAAA,CAAA;MAEAzD,SAAA,CAAAU,KAAmB;IACjB;IACF,MAAAuD,KAAA,GAAAA,CAAA;MAEM,IAAAvB,EAAA;MACJ,CAAAA,EAAA,GAAkBpD,QAAA,CAAAoB,KAAA,KAAW,YAAQ,CAAC,GAAAgC,EAAA,CAAAuB,KAAA;IACtC;IACA,MAAAC,IAAA,GAAAA,CAAA,KAAmB;MACnB,IAAAxB,EAAA;MACA,CAAAA,EAAA,GAAApD,QAAA,CAAAoB,KAAyB,qBAAAgC,EAAA,CAAAwB,IAAA;IAAA,CAC3B;IAEM,MAAAN,YAAY,GAAmB,MAAAO,IAAA;MACnC/B,IAAI,CAACC,WAAA,EAAA8B,IAAA,CAAkBtD,KAAS,CAAAuD,QAAA;MAEhChC,IAAI,CAAAE,kBAAW,EAAA6B,IAAA,CAAAtD,KAAA,CAAAuD,QAAA;MACbhC,IAAA,WAAA+B,IAAiB,CAAQ;MACzBtE,WAAA,CAAAa,KAAA;MACFZ,gBAAA,CAAAY,KAAA;IAEA,CAAI;IACM,MAAA2D,SAAA,GAAAC,KAAY;MACtB,KAAA9D,iBAAA,CAAAE,KAAA,IAAAR,OAAA,CAAAQ,KAAA,EACM;MAA8B,IAC9B4D,KAAG,GAAG;QACZxE,gBAAA,CAAAY,KAAA;QACA;MAAkC;MAElC,IAAA4D,KAAA,IAAAzE,WAAA,CAAAa,KAAA,CAAAC,MAAA;QACM2D,KAAA,GAAAzE,WAAA,CAAgBa,KAAA,CAAAC,MAAA,GAAe,CAAK;MAC1C;MACM,MAAA4D,UAAa,GAAA/E,SAAA,CAAAkB,KAAiB,CAAA8D,aAAA,KAAApF,EAAA,CAAAqF,EAAA;MAEpC,MAAgB/C,cAAA,GAAA6C,UAA2B,CAAApD,gBAAA,KAAyB/B,EAAA,CAAAqF,EAAA;MAClE,MAAAC,aAAwB,GAAAhD,cAAA,CAAA4C,KAAA;MAC1B,MAAAK,SAAA,GAAAJ,UAAA,CAAAI,SAAA;MACA;QAAAC,SAA2B;QAAAC;MAAA,IAAAH,aAAA;MACzB,IAAAE,SAAW,GAAaC,YAAA,GAAAF,SAAA,GAAAJ,UAAA,CAAAO,YAAA;QAC1BP,UAAA,CAAAI,SAAA,IAAAE,YAAA;MACA;MAEE,IAAAD,SAAA,GAA4BD,SAAA;QAC5BJ,UAAA,CAAAI,SAAA,IAAAE,YAAA;MAAA;MAEF/E,gBAAA,CAAAY,KAAA,GAAA4D,KAAA;MACFhF,QAAA,CAAAoB,KAAA,CAAAnB,GAAA,CAAAwF,YAAA,6BAAA5E,SAAA,CAAAO,KAAA,SAAAZ,gBAAA,CAAAY,KAAA;IAEA,CAAM;IAEA,MAAAsE,UAAU,GAAOC,cAAA,CAAAvF,UAAqB,EAAG;MAC7C,IAAAgD,EAAA;MACD,KAAAA,EAAA,GAAAjD,SAAA,CAAAiB,KAAA,qBAAAgC,EAAA,CAAAc,oBAAA,IAED;MACehD,iBAAA,CAAAE,KAAA,IAAA+C,KAAA;IAAA,CACd;IAEDyB,eAAgB;MAEdF,UAAA,oBAAAA,UAAA;IAAC,EAAC;IACDG,SAAU;MAET7F,QAAA,CAASoB,KAAA,CAAcnB,GAAK,CAAAwF,YAAA;MAC5BzF,QAAA,CAAAoB,KAAA,CAAAnB,GAAA,CAAAwF,YAAA;MAAAzF,QACG,CAAAoB,KAAA,CAAUnB,GAAK,CAAAwF,YAAA,gBAA+B;MACnDzF,QAAA,CAAAoB,KAAA,CAAAnB,GAAA,CAAAwF,YAAA,6BAAA5E,SAAA,CAAAO,KAAA,SAAAZ,gBAAA,CAAAY,KAAA;MAEAf,QAAA,GAAYL,QAAS,CAAAoB,KAAA,CAAcnB,GAAK,CAAA6F,YAAA,CAAa,UAAU;IAAA,CAChE;IAEYC,MAAA;MAAAvF,gBAAA;MAEXE,SAAA;MAAAE,OAAA;MAEAZ,QAAA;MAAAG,SAAA;MAEAI,WAAA;MAAA+D,YAAA;MAEAD,cAAA;MAAAM,KAAA;MAEAC,IAAA;MAAAT,KAAA;MAEAY,SAAA;MAAA9C;IAAA,CAEA;IAAA,QAAA+D,IAAA,EAAAC,MAAA;MAEA,OAAAC,SAAA,IAAAC,WAAA,CAAAC,KAAA,CAAAC,SAAA;QAAAC,OAAA;QAEArG,GAAA,EAAAE,SAAA;QAAAoG,OAAA,EAAAH,KAAA,CAAAlF,iBAAA;QAEAsF,SAAA,EAAAR,IAAA,CAAAQ,SAAA;QAAA;QAEA,iBAAAJ,KAAA,CAAAtG,EAAA,EAAA2G,CAAA,YAAAT,IAAA,CAAAU,WAAA;QAAAC,UAAA,EAAAX,IAAA,CAAAW,UAAA;QAEA,aAAAX,IAAA,CAAAY,QAAA;QAAA;QAEAC,IAAA;QACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}