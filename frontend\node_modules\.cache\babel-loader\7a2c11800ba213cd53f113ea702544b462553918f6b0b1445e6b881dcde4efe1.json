{"ast": null, "code": "import arrayPush from './_arrayPush.js';\nimport baseFlatten from './_baseFlatten.js';\nimport copyArray from './_copyArray.js';\nimport isArray from './isArray.js';\n\n/**\n * Creates a new array concatenating `array` with any additional arrays\n * and/or values.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to concatenate.\n * @param {...*} [values] The values to concatenate.\n * @returns {Array} Returns the new concatenated array.\n * @example\n *\n * var array = [1];\n * var other = _.concat(array, 2, [3], [[4]]);\n *\n * console.log(other);\n * // => [1, 2, 3, [4]]\n *\n * console.log(array);\n * // => [1]\n */\nfunction concat() {\n  var length = arguments.length;\n  if (!length) {\n    return [];\n  }\n  var args = Array(length - 1),\n    array = arguments[0],\n    index = length;\n  while (index--) {\n    args[index - 1] = arguments[index];\n  }\n  return arrayPush(isArray(array) ? copyArray(array) : [array], baseFlatten(args, 1));\n}\nexport default concat;", "map": {"version": 3, "names": ["arrayPush", "baseFlatten", "copyArray", "isArray", "concat", "length", "arguments", "args", "Array", "array", "index"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/concat.js"], "sourcesContent": ["import arrayPush from './_arrayPush.js';\nimport baseFlatten from './_baseFlatten.js';\nimport copyArray from './_copyArray.js';\nimport isArray from './isArray.js';\n\n/**\n * Creates a new array concatenating `array` with any additional arrays\n * and/or values.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to concatenate.\n * @param {...*} [values] The values to concatenate.\n * @returns {Array} Returns the new concatenated array.\n * @example\n *\n * var array = [1];\n * var other = _.concat(array, 2, [3], [[4]]);\n *\n * console.log(other);\n * // => [1, 2, 3, [4]]\n *\n * console.log(array);\n * // => [1]\n */\nfunction concat() {\n  var length = arguments.length;\n  if (!length) {\n    return [];\n  }\n  var args = Array(length - 1),\n      array = arguments[0],\n      index = length;\n\n  while (index--) {\n    args[index - 1] = arguments[index];\n  }\n  return arrayPush(isArray(array) ? copyArray(array) : [array], baseFlatten(args, 1));\n}\n\nexport default concat;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAAA,EAAG;EAChB,IAAIC,MAAM,GAAGC,SAAS,CAACD,MAAM;EAC7B,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,EAAE;EACX;EACA,IAAIE,IAAI,GAAGC,KAAK,CAACH,MAAM,GAAG,CAAC,CAAC;IACxBI,KAAK,GAAGH,SAAS,CAAC,CAAC,CAAC;IACpBI,KAAK,GAAGL,MAAM;EAElB,OAAOK,KAAK,EAAE,EAAE;IACdH,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,KAAK,CAAC;EACpC;EACA,OAAOV,SAAS,CAACG,OAAO,CAACM,KAAK,CAAC,GAAGP,SAAS,CAACO,KAAK,CAAC,GAAG,CAACA,KAAK,CAAC,EAAER,WAAW,CAACM,IAAI,EAAE,CAAC,CAAC,CAAC;AACrF;AAEA,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}