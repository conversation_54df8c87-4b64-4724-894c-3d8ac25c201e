{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { computed } from 'vue';\nimport { useCheckboxDisabled } from './use-checkbox-disabled.mjs';\nimport { useCheckboxEvent } from './use-checkbox-event.mjs';\nimport { useCheckboxModel } from './use-checkbox-model.mjs';\nimport { useCheckboxStatus } from './use-checkbox-status.mjs';\nimport { useFormItem, useFormItemInputId } from '../../../form/src/hooks/use-form-item.mjs';\nimport { useDeprecated } from '../../../../hooks/use-deprecated/index.mjs';\nimport { isPropAbsent } from '../../../../utils/types.mjs';\nimport { isArray } from '@vue/shared';\nconst useCheckbox = (props, slots) => {\n  const {\n    formItem: elFormItem\n  } = useFormItem();\n  const {\n    model,\n    isGroup,\n    isLimitExceeded\n  } = useCheckboxModel(props);\n  const {\n    isFocused,\n    isChecked,\n    checkboxButtonSize,\n    checkboxSize,\n    hasOwnLabel,\n    actualValue\n  } = useCheckboxStatus(props, slots, {\n    model\n  });\n  const {\n    isDisabled\n  } = useCheckboxDisabled({\n    model,\n    isChecked\n  });\n  const {\n    inputId,\n    isLabeledByFormItem\n  } = useFormItemInputId(props, {\n    formItemContext: elFormItem,\n    disableIdGeneration: hasOwnLabel,\n    disableIdManagement: isGroup\n  });\n  const {\n    handleChange,\n    onClickRoot\n  } = useCheckboxEvent(props, {\n    model,\n    isLimitExceeded,\n    hasOwnLabel,\n    isDisabled,\n    isLabeledByFormItem\n  });\n  const setStoreValue = () => {\n    function addToStore() {\n      var _a, _b;\n      if (isArray(model.value) && !model.value.includes(actualValue.value)) {\n        model.value.push(actualValue.value);\n      } else {\n        model.value = (_b = (_a = props.trueValue) != null ? _a : props.trueLabel) != null ? _b : true;\n      }\n    }\n    props.checked && addToStore();\n  };\n  setStoreValue();\n  useDeprecated({\n    from: \"label act as value\",\n    replacement: \"value\",\n    version: \"3.0.0\",\n    scope: \"el-checkbox\",\n    ref: \"https://element-plus.org/en-US/component/checkbox.html\"\n  }, computed(() => isGroup.value && isPropAbsent(props.value)));\n  useDeprecated({\n    from: \"true-label\",\n    replacement: \"true-value\",\n    version: \"3.0.0\",\n    scope: \"el-checkbox\",\n    ref: \"https://element-plus.org/en-US/component/checkbox.html\"\n  }, computed(() => !!props.trueLabel));\n  useDeprecated({\n    from: \"false-label\",\n    replacement: \"false-value\",\n    version: \"3.0.0\",\n    scope: \"el-checkbox\",\n    ref: \"https://element-plus.org/en-US/component/checkbox.html\"\n  }, computed(() => !!props.falseLabel));\n  return {\n    inputId,\n    isLabeledByFormItem,\n    isChecked,\n    isDisabled,\n    isFocused,\n    checkboxButtonSize,\n    checkboxSize,\n    hasOwnLabel,\n    model,\n    actualValue,\n    handleChange,\n    onClickRoot\n  };\n};\nexport { useCheckbox };", "map": {"version": 3, "names": ["useCheckbox", "props", "slots", "formItem", "elFormItem", "useFormItem", "model", "isGroup", "isLimitExceeded", "useCheckboxModel", "isFocused", "isChecked", "checkboxButtonSize", "checkboxSize", "hasOwnLabel", "actualValue", "useCheckboxStatus", "isDisabled", "useCheckboxDisabled", "inputId", "isLabeledByFormItem", "useFormItemInputId", "formItemContext", "disableIdGeneration", "disableIdManagement", "handleChange", "onClickRoot", "useCheckboxEvent", "setStoreValue", "addToStore", "_a", "_b", "isArray", "value", "includes", "push", "trueValue", "<PERSON><PERSON><PERSON><PERSON>", "checked", "useDeprecated", "from", "replacement", "version", "scope", "ref", "computed", "isPropAbsent", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { useFormItem, useFormItemInputId } from '@element-plus/components/form'\nimport { isArray, isPropAbsent } from '@element-plus/utils'\nimport { useDeprecated } from '@element-plus/hooks'\nimport { useCheckboxDisabled } from './use-checkbox-disabled'\nimport { useCheckboxEvent } from './use-checkbox-event'\nimport { useCheckboxModel } from './use-checkbox-model'\nimport { useCheckboxStatus } from './use-checkbox-status'\nimport type { ComponentInternalInstance } from 'vue'\n\nimport type { CheckboxProps } from '../checkbox'\n\nexport const useCheckbox = (\n  props: CheckboxProps,\n  slots: ComponentInternalInstance['slots']\n) => {\n  const { formItem: elFormItem } = useFormItem()\n  const { model, isGroup, isLimitExceeded } = useCheckboxModel(props)\n  const {\n    isFocused,\n    isChecked,\n    checkboxButtonSize,\n    checkboxSize,\n    hasOwnLabel,\n    actualValue,\n  } = useCheckboxStatus(props, slots, { model })\n  const { isDisabled } = useCheckboxDisabled({ model, isChecked })\n  const { inputId, isLabeledByFormItem } = useFormItemInputId(props, {\n    formItemContext: elFormItem,\n    disableIdGeneration: hasOwnLabel,\n    disableIdManagement: isGroup,\n  })\n  const { handleChange, onClickRoot } = useCheckboxEvent(props, {\n    model,\n    isLimitExceeded,\n    hasOwnLabel,\n    isDisabled,\n    isLabeledByFormItem,\n  })\n\n  const setStoreValue = () => {\n    function addToStore() {\n      if (isArray(model.value) && !model.value.includes(actualValue.value)) {\n        model.value.push(actualValue.value)\n      } else {\n        model.value = props.trueValue ?? props.trueLabel ?? true\n      }\n    }\n    props.checked && addToStore()\n  }\n\n  setStoreValue()\n\n  useDeprecated(\n    {\n      from: 'label act as value',\n      replacement: 'value',\n      version: '3.0.0',\n      scope: 'el-checkbox',\n      ref: 'https://element-plus.org/en-US/component/checkbox.html',\n    },\n    computed(() => isGroup.value && isPropAbsent(props.value))\n  )\n\n  useDeprecated(\n    {\n      from: 'true-label',\n      replacement: 'true-value',\n      version: '3.0.0',\n      scope: 'el-checkbox',\n      ref: 'https://element-plus.org/en-US/component/checkbox.html',\n    },\n    computed(() => !!props.trueLabel)\n  )\n\n  useDeprecated(\n    {\n      from: 'false-label',\n      replacement: 'false-value',\n      version: '3.0.0',\n      scope: 'el-checkbox',\n      ref: 'https://element-plus.org/en-US/component/checkbox.html',\n    },\n    computed(() => !!props.falseLabel)\n  )\n\n  return {\n    inputId,\n    isLabeledByFormItem,\n    isChecked,\n    isDisabled,\n    isFocused,\n    checkboxButtonSize,\n    checkboxSize,\n    hasOwnLabel,\n    model,\n    actualValue,\n    handleChange,\n    onClickRoot,\n  }\n}\n"], "mappings": ";;;;;;;;;;AAQY,MAACA,WAAW,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EAC3C,MAAM;IAAEC,QAAQ,EAAEC;EAAU,CAAE,GAAGC,WAAW,EAAE;EAC9C,MAAM;IAAEC,KAAK;IAAEC,OAAO;IAAEC;EAAe,CAAE,GAAGC,gBAAgB,CAACR,KAAK,CAAC;EACnE,MAAM;IACJS,SAAS;IACTC,SAAS;IACTC,kBAAkB;IAClBC,YAAY;IACZC,WAAW;IACXC;EACJ,CAAG,GAAGC,iBAAiB,CAACf,KAAK,EAAEC,KAAK,EAAE;IAAEI;EAAK,CAAE,CAAC;EAC9C,MAAM;IAAEW;EAAU,CAAE,GAAGC,mBAAmB,CAAC;IAAEZ,KAAK;IAAEK;EAAS,CAAE,CAAC;EAChE,MAAM;IAAEQ,OAAO;IAAEC;EAAmB,CAAE,GAAGC,kBAAkB,CAACpB,KAAK,EAAE;IACjEqB,eAAe,EAAElB,UAAU;IAC3BmB,mBAAmB,EAAET,WAAW;IAChCU,mBAAmB,EAAEjB;EACzB,CAAG,CAAC;EACF,MAAM;IAAEkB,YAAY;IAAEC;EAAW,CAAE,GAAGC,gBAAgB,CAAC1B,KAAK,EAAE;IAC5DK,KAAK;IACLE,eAAe;IACfM,WAAW;IACXG,UAAU;IACVG;EACJ,CAAG,CAAC;EACF,MAAMQ,aAAa,GAAGA,CAAA,KAAM;IAC1B,SAASC,UAAUA,CAAA,EAAG;MACpB,IAAIC,EAAE,EAAEC,EAAE;MACV,IAAIC,OAAO,CAAC1B,KAAK,CAAC2B,KAAK,CAAC,IAAI,CAAC3B,KAAK,CAAC2B,KAAK,CAACC,QAAQ,CAACnB,WAAW,CAACkB,KAAK,CAAC,EAAE;QACpE3B,KAAK,CAAC2B,KAAK,CAACE,IAAI,CAACpB,WAAW,CAACkB,KAAK,CAAC;MAC3C,CAAO,MAAM;QACL3B,KAAK,CAAC2B,KAAK,GAAG,CAACF,EAAE,GAAG,CAACD,EAAE,GAAG7B,KAAK,CAACmC,SAAS,KAAK,IAAI,GAAGN,EAAE,GAAG7B,KAAK,CAACoC,SAAS,KAAK,IAAI,GAAGN,EAAE,GAAG,IAAI;MACtG;IACA;IACI9B,KAAK,CAACqC,OAAO,IAAIT,UAAU,EAAE;EACjC,CAAG;EACDD,aAAa,EAAE;EACfW,aAAa,CAAC;IACZC,IAAI,EAAE,oBAAoB;IAC1BC,WAAW,EAAE,OAAO;IACpBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE,aAAa;IACpBC,GAAG,EAAE;EACT,CAAG,EAAEC,QAAQ,CAAC,MAAMtC,OAAO,CAAC0B,KAAK,IAAIa,YAAY,CAAC7C,KAAK,CAACgC,KAAK,CAAC,CAAC,CAAC;EAC9DM,aAAa,CAAC;IACZC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,YAAY;IACzBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE,aAAa;IACpBC,GAAG,EAAE;EACT,CAAG,EAAEC,QAAQ,CAAC,MAAM,CAAC,CAAC5C,KAAK,CAACoC,SAAS,CAAC,CAAC;EACrCE,aAAa,CAAC;IACZC,IAAI,EAAE,aAAa;IACnBC,WAAW,EAAE,aAAa;IAC1BC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE,aAAa;IACpBC,GAAG,EAAE;EACT,CAAG,EAAEC,QAAQ,CAAC,MAAM,CAAC,CAAC5C,KAAK,CAAC8C,UAAU,CAAC,CAAC;EACtC,OAAO;IACL5B,OAAO;IACPC,mBAAmB;IACnBT,SAAS;IACTM,UAAU;IACVP,SAAS;IACTE,kBAAkB;IAClBC,YAAY;IACZC,WAAW;IACXR,KAAK;IACLS,WAAW;IACXU,YAAY;IACZC;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}