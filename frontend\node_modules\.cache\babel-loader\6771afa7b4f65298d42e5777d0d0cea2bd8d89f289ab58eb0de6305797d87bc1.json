{"ast": null, "code": "/** Used to match template delimiters. */\nvar reEscape = /<%-([\\s\\S]+?)%>/g;\nexport default reEscape;", "map": {"version": 3, "names": ["reEscape"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/_reEscape.js"], "sourcesContent": ["/** Used to match template delimiters. */\nvar reEscape = /<%-([\\s\\S]+?)%>/g;\n\nexport default reEscape;\n"], "mappings": "AAAA;AACA,IAAIA,QAAQ,GAAG,kBAAkB;AAEjC,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}