{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { nextTick } from 'vue';\nimport Node from './node.mjs';\nimport { getNodeKey } from './util.mjs';\nimport { hasOwn, isObject } from '@vue/shared';\nimport { isPropAbsent } from '../../../../utils/types.mjs';\nclass TreeStore {\n  constructor(options) {\n    this.currentNode = null;\n    this.currentNodeKey = null;\n    for (const option in options) {\n      if (hasOwn(options, option)) {\n        this[option] = options[option];\n      }\n    }\n    this.nodesMap = {};\n  }\n  initialize() {\n    this.root = new Node({\n      data: this.data,\n      store: this\n    });\n    this.root.initialize();\n    if (this.lazy && this.load) {\n      const loadFn = this.load;\n      loadFn(this.root, data => {\n        this.root.doCreateChildren(data);\n        this._initDefaultCheckedNodes();\n      });\n    } else {\n      this._initDefaultCheckedNodes();\n    }\n  }\n  filter(value) {\n    const filterNodeMethod = this.filterNodeMethod;\n    const lazy = this.lazy;\n    const traverse = async function (node) {\n      const childNodes = node.root ? node.root.childNodes : node.childNodes;\n      for (const [index, child] of childNodes.entries()) {\n        child.visible = filterNodeMethod.call(child, value, child.data, child);\n        if (index % 80 === 0 && index > 0) {\n          await nextTick();\n        }\n        await traverse(child);\n      }\n      if (!node.visible && childNodes.length) {\n        let allHidden = true;\n        allHidden = !childNodes.some(child => child.visible);\n        if (node.root) {\n          node.root.visible = allHidden === false;\n        } else {\n          node.visible = allHidden === false;\n        }\n      }\n      if (!value) return;\n      if (node.visible && !node.isLeaf) {\n        if (!lazy || node.loaded) {\n          node.expand();\n        }\n      }\n    };\n    traverse(this);\n  }\n  setData(newVal) {\n    const instanceChanged = newVal !== this.root.data;\n    if (instanceChanged) {\n      this.nodesMap = {};\n      this.root.setData(newVal);\n      this._initDefaultCheckedNodes();\n      this.setCurrentNodeKey(this.currentNodeKey);\n    } else {\n      this.root.updateChildren();\n    }\n  }\n  getNode(data) {\n    if (data instanceof Node) return data;\n    const key = isObject(data) ? getNodeKey(this.key, data) : data;\n    return this.nodesMap[key] || null;\n  }\n  insertBefore(data, refData) {\n    const refNode = this.getNode(refData);\n    refNode.parent.insertBefore({\n      data\n    }, refNode);\n  }\n  insertAfter(data, refData) {\n    const refNode = this.getNode(refData);\n    refNode.parent.insertAfter({\n      data\n    }, refNode);\n  }\n  remove(data) {\n    const node = this.getNode(data);\n    if (node && node.parent) {\n      if (node === this.currentNode) {\n        this.currentNode = null;\n      }\n      node.parent.removeChild(node);\n    }\n  }\n  append(data, parentData) {\n    const parentNode = !isPropAbsent(parentData) ? this.getNode(parentData) : this.root;\n    if (parentNode) {\n      parentNode.insertChild({\n        data\n      });\n    }\n  }\n  _initDefaultCheckedNodes() {\n    const defaultCheckedKeys = this.defaultCheckedKeys || [];\n    const nodesMap = this.nodesMap;\n    defaultCheckedKeys.forEach(checkedKey => {\n      const node = nodesMap[checkedKey];\n      if (node) {\n        node.setChecked(true, !this.checkStrictly);\n      }\n    });\n  }\n  _initDefaultCheckedNode(node) {\n    const defaultCheckedKeys = this.defaultCheckedKeys || [];\n    if (defaultCheckedKeys.includes(node.key)) {\n      node.setChecked(true, !this.checkStrictly);\n    }\n  }\n  setDefaultCheckedKey(newVal) {\n    if (newVal !== this.defaultCheckedKeys) {\n      this.defaultCheckedKeys = newVal;\n      this._initDefaultCheckedNodes();\n    }\n  }\n  registerNode(node) {\n    const key = this.key;\n    if (!node || !node.data) return;\n    if (!key) {\n      this.nodesMap[node.id] = node;\n    } else {\n      const nodeKey = node.key;\n      if (nodeKey !== void 0) this.nodesMap[node.key] = node;\n    }\n  }\n  deregisterNode(node) {\n    const key = this.key;\n    if (!key || !node || !node.data) return;\n    node.childNodes.forEach(child => {\n      this.deregisterNode(child);\n    });\n    delete this.nodesMap[node.key];\n  }\n  getCheckedNodes(leafOnly = false, includeHalfChecked = false) {\n    const checkedNodes = [];\n    const traverse = function (node) {\n      const childNodes = node.root ? node.root.childNodes : node.childNodes;\n      childNodes.forEach(child => {\n        if ((child.checked || includeHalfChecked && child.indeterminate) && (!leafOnly || leafOnly && child.isLeaf)) {\n          checkedNodes.push(child.data);\n        }\n        traverse(child);\n      });\n    };\n    traverse(this);\n    return checkedNodes;\n  }\n  getCheckedKeys(leafOnly = false) {\n    return this.getCheckedNodes(leafOnly).map(data => (data || {})[this.key]);\n  }\n  getHalfCheckedNodes() {\n    const nodes = [];\n    const traverse = function (node) {\n      const childNodes = node.root ? node.root.childNodes : node.childNodes;\n      childNodes.forEach(child => {\n        if (child.indeterminate) {\n          nodes.push(child.data);\n        }\n        traverse(child);\n      });\n    };\n    traverse(this);\n    return nodes;\n  }\n  getHalfCheckedKeys() {\n    return this.getHalfCheckedNodes().map(data => (data || {})[this.key]);\n  }\n  _getAllNodes() {\n    const allNodes = [];\n    const nodesMap = this.nodesMap;\n    for (const nodeKey in nodesMap) {\n      if (hasOwn(nodesMap, nodeKey)) {\n        allNodes.push(nodesMap[nodeKey]);\n      }\n    }\n    return allNodes;\n  }\n  updateChildren(key, data) {\n    const node = this.nodesMap[key];\n    if (!node) return;\n    const childNodes = node.childNodes;\n    for (let i = childNodes.length - 1; i >= 0; i--) {\n      const child = childNodes[i];\n      this.remove(child.data);\n    }\n    for (let i = 0, j = data.length; i < j; i++) {\n      const child = data[i];\n      this.append(child, node.data);\n    }\n  }\n  _setCheckedKeys(key, leafOnly = false, checkedKeys) {\n    const allNodes = this._getAllNodes().sort((a, b) => a.level - b.level);\n    const cache = /* @__PURE__ */Object.create(null);\n    const keys = Object.keys(checkedKeys);\n    allNodes.forEach(node => node.setChecked(false, false));\n    const cacheCheckedChild = node => {\n      node.childNodes.forEach(child => {\n        var _a;\n        cache[child.data[key]] = true;\n        if ((_a = child.childNodes) == null ? void 0 : _a.length) {\n          cacheCheckedChild(child);\n        }\n      });\n    };\n    for (let i = 0, j = allNodes.length; i < j; i++) {\n      const node = allNodes[i];\n      const nodeKey = node.data[key].toString();\n      const checked = keys.includes(nodeKey);\n      if (!checked) {\n        if (node.checked && !cache[nodeKey]) {\n          node.setChecked(false, false);\n        }\n        continue;\n      }\n      if (node.childNodes.length) {\n        cacheCheckedChild(node);\n      }\n      if (node.isLeaf || this.checkStrictly) {\n        node.setChecked(true, false);\n        continue;\n      }\n      node.setChecked(true, true);\n      if (leafOnly) {\n        node.setChecked(false, false);\n        const traverse = function (node2) {\n          const childNodes = node2.childNodes;\n          childNodes.forEach(child => {\n            if (!child.isLeaf) {\n              child.setChecked(false, false);\n            }\n            traverse(child);\n          });\n        };\n        traverse(node);\n      }\n    }\n  }\n  setCheckedNodes(array, leafOnly = false) {\n    const key = this.key;\n    const checkedKeys = {};\n    array.forEach(item => {\n      checkedKeys[(item || {})[key]] = true;\n    });\n    this._setCheckedKeys(key, leafOnly, checkedKeys);\n  }\n  setCheckedKeys(keys, leafOnly = false) {\n    this.defaultCheckedKeys = keys;\n    const key = this.key;\n    const checkedKeys = {};\n    keys.forEach(key2 => {\n      checkedKeys[key2] = true;\n    });\n    this._setCheckedKeys(key, leafOnly, checkedKeys);\n  }\n  setDefaultExpandedKeys(keys) {\n    keys = keys || [];\n    this.defaultExpandedKeys = keys;\n    keys.forEach(key => {\n      const node = this.getNode(key);\n      if (node) node.expand(null, this.autoExpandParent);\n    });\n  }\n  setChecked(data, checked, deep) {\n    const node = this.getNode(data);\n    if (node) {\n      node.setChecked(!!checked, deep);\n    }\n  }\n  getCurrentNode() {\n    return this.currentNode;\n  }\n  setCurrentNode(currentNode) {\n    const prevCurrentNode = this.currentNode;\n    if (prevCurrentNode) {\n      prevCurrentNode.isCurrent = false;\n    }\n    this.currentNode = currentNode;\n    this.currentNode.isCurrent = true;\n  }\n  setUserCurrentNode(node, shouldAutoExpandParent = true) {\n    const key = node[this.key];\n    const currNode = this.nodesMap[key];\n    this.setCurrentNode(currNode);\n    if (shouldAutoExpandParent && this.currentNode.level > 1) {\n      this.currentNode.parent.expand(null, true);\n    }\n  }\n  setCurrentNodeKey(key, shouldAutoExpandParent = true) {\n    this.currentNodeKey = key;\n    if (isPropAbsent(key)) {\n      this.currentNode && (this.currentNode.isCurrent = false);\n      this.currentNode = null;\n      return;\n    }\n    const node = this.getNode(key);\n    if (node) {\n      this.setCurrentNode(node);\n      if (shouldAutoExpandParent && this.currentNode.level > 1) {\n        this.currentNode.parent.expand(null, true);\n      }\n    }\n  }\n}\nexport { TreeStore as default };", "map": {"version": 3, "names": ["TreeStore", "constructor", "options", "currentNode", "currentNodeKey", "option", "hasOwn", "nodesMap", "initialize", "root", "Node", "data", "store", "lazy", "load", "loadFn", "doCreate<PERSON><PERSON><PERSON>n", "_initDefaultCheckedNodes", "filter", "value", "filterNodeMethod", "traverse", "node", "childNodes", "index", "child", "entries", "visible", "call", "nextTick", "length", "allHidden", "some", "<PERSON><PERSON><PERSON><PERSON>", "loaded", "expand", "setData", "newVal", "instanceChanged", "setCurrentNodeKey", "update<PERSON><PERSON><PERSON>n", "getNode", "key", "isObject", "getNodeKey", "insertBefore", "refData", "refNode", "parent", "insertAfter", "remove", "<PERSON><PERSON><PERSON><PERSON>", "append", "parentData", "parentNode", "isPropAbsent", "<PERSON><PERSON><PERSON><PERSON>", "defaultCheckedKeys", "for<PERSON>ach", "<PERSON><PERSON><PERSON>", "setChecked", "checkStrictly", "_initDefaultCheckedNode", "includes", "setDefaultCheckedKey", "registerNode", "id", "nodeKey", "deregisterNode", "getCheckedNodes", "leafOnly", "includeHalfChecked", "checkedNodes", "checked", "indeterminate", "push", "getChe<PERSON><PERSON>eys", "map", "getHalfCheckedNodes", "nodes", "getHalfCheckedKeys", "_getAllNodes", "allNodes", "i", "j", "_setChe<PERSON><PERSON><PERSON>s", "checked<PERSON>eys", "sort", "a", "b", "level", "cache", "Object", "create", "keys", "cacheCheckedChild", "_a", "toString", "node2", "setCheckedNodes", "array", "item", "set<PERSON><PERSON><PERSON><PERSON>eys", "key2", "setDefaultExpandedKeys", "defaultExpandedKeys", "autoExpandParent", "deep", "getCurrentNode", "setCurrentNode", "prevCurrentNode", "isCurrent", "setUserCurrentNode", "shouldAutoExpandParent", "currNode"], "sources": ["../../../../../../../packages/components/tree/src/model/tree-store.ts"], "sourcesContent": ["// @ts-nocheck\nimport { nextTick } from 'vue'\nimport { hasOwn, isObject, isPropAbsent } from '@element-plus/utils'\nimport Node from './node'\nimport { getNodeKey } from './util'\n\nimport type {\n  FilterNodeMethodFunction,\n  FilterValue,\n  LoadFunction,\n  TreeData,\n  TreeKey,\n  TreeNodeData,\n  TreeOptionProps,\n  TreeStoreNodesMap,\n  TreeStoreOptions,\n} from '../tree.type'\n\nexport default class TreeStore {\n  currentNode: Node\n  currentNodeKey: TreeKey\n  nodesMap: TreeStoreNodesMap\n  root: Node\n  data: TreeData\n  lazy: boolean\n  load: LoadFunction\n  filterNodeMethod: FilterNodeMethodFunction\n  key: TreeKey\n  defaultCheckedKeys: TreeKey[]\n  checkStrictly: boolean\n  defaultExpandedKeys: TreeKey[]\n  autoExpandParent: boolean\n  defaultExpandAll: boolean\n  checkDescendants: boolean\n  props: TreeOptionProps\n\n  constructor(options: TreeStoreOptions) {\n    this.currentNode = null\n    this.currentNodeKey = null\n\n    for (const option in options) {\n      if (hasOwn(options, option)) {\n        this[option] = options[option]\n      }\n    }\n\n    this.nodesMap = {}\n  }\n\n  initialize() {\n    this.root = new Node({\n      data: this.data,\n      store: this,\n    })\n    this.root.initialize()\n\n    if (this.lazy && this.load) {\n      const loadFn = this.load\n      loadFn(this.root, (data) => {\n        this.root.doCreateChildren(data)\n        this._initDefaultCheckedNodes()\n      })\n    } else {\n      this._initDefaultCheckedNodes()\n    }\n  }\n\n  filter(value: FilterValue): void {\n    const filterNodeMethod = this.filterNodeMethod\n    const lazy = this.lazy\n    const traverse = async function (node: TreeStore | Node) {\n      const childNodes = (node as TreeStore).root\n        ? (node as TreeStore).root.childNodes\n        : (node as Node).childNodes\n\n      for (const [index, child] of childNodes.entries()) {\n        child.visible = filterNodeMethod.call(child, value, child.data, child)\n\n        if (index % 80 === 0 && index > 0) {\n          await nextTick()\n        }\n        await traverse(child)\n      }\n\n      if (!(node as Node).visible && childNodes.length) {\n        let allHidden = true\n        allHidden = !childNodes.some((child) => child.visible)\n\n        if ((node as TreeStore).root) {\n          ;(node as TreeStore).root.visible = allHidden === false\n        } else {\n          ;(node as Node).visible = allHidden === false\n        }\n      }\n      if (!value) return\n\n      if ((node as Node).visible && !(node as Node).isLeaf) {\n        if (!lazy || node.loaded) {\n          ;(node as Node).expand()\n        }\n      }\n    }\n\n    traverse(this)\n  }\n\n  setData(newVal: TreeData): void {\n    const instanceChanged = newVal !== this.root.data\n    if (instanceChanged) {\n      this.nodesMap = {}\n      this.root.setData(newVal)\n      this._initDefaultCheckedNodes()\n      this.setCurrentNodeKey(this.currentNodeKey)\n    } else {\n      this.root.updateChildren()\n    }\n  }\n\n  getNode(data: TreeKey | TreeNodeData | Node): Node {\n    if (data instanceof Node) return data\n    const key = isObject(data) ? getNodeKey(this.key, data) : data\n    return this.nodesMap[key] || null\n  }\n\n  insertBefore(\n    data: TreeNodeData,\n    refData: TreeKey | TreeNodeData | Node\n  ): void {\n    const refNode = this.getNode(refData)\n    refNode.parent.insertBefore({ data }, refNode)\n  }\n\n  insertAfter(\n    data: TreeNodeData,\n    refData: TreeKey | TreeNodeData | Node\n  ): void {\n    const refNode = this.getNode(refData)\n    refNode.parent.insertAfter({ data }, refNode)\n  }\n\n  remove(data: TreeNodeData | Node): void {\n    const node = this.getNode(data)\n\n    if (node && node.parent) {\n      if (node === this.currentNode) {\n        this.currentNode = null\n      }\n      node.parent.removeChild(node)\n    }\n  }\n\n  append(data: TreeNodeData, parentData: TreeNodeData | TreeKey | Node): void {\n    const parentNode = !isPropAbsent(parentData)\n      ? this.getNode(parentData)\n      : this.root\n\n    if (parentNode) {\n      parentNode.insertChild({ data })\n    }\n  }\n\n  _initDefaultCheckedNodes(): void {\n    const defaultCheckedKeys = this.defaultCheckedKeys || []\n    const nodesMap = this.nodesMap\n\n    defaultCheckedKeys.forEach((checkedKey) => {\n      const node = nodesMap[checkedKey]\n\n      if (node) {\n        node.setChecked(true, !this.checkStrictly)\n      }\n    })\n  }\n\n  _initDefaultCheckedNode(node: Node): void {\n    const defaultCheckedKeys = this.defaultCheckedKeys || []\n\n    if (defaultCheckedKeys.includes(node.key)) {\n      node.setChecked(true, !this.checkStrictly)\n    }\n  }\n\n  setDefaultCheckedKey(newVal: TreeKey[]): void {\n    if (newVal !== this.defaultCheckedKeys) {\n      this.defaultCheckedKeys = newVal\n      this._initDefaultCheckedNodes()\n    }\n  }\n\n  registerNode(node: Node): void {\n    const key = this.key\n    if (!node || !node.data) return\n\n    if (!key) {\n      this.nodesMap[node.id] = node\n    } else {\n      const nodeKey = node.key\n      if (nodeKey !== undefined) this.nodesMap[node.key] = node\n    }\n  }\n\n  deregisterNode(node: Node): void {\n    const key = this.key\n    if (!key || !node || !node.data) return\n\n    node.childNodes.forEach((child) => {\n      this.deregisterNode(child)\n    })\n\n    delete this.nodesMap[node.key]\n  }\n\n  getCheckedNodes(\n    leafOnly = false,\n    includeHalfChecked = false\n  ): TreeNodeData[] {\n    const checkedNodes: TreeNodeData[] = []\n    const traverse = function (node: TreeStore | Node) {\n      const childNodes = (node as TreeStore).root\n        ? (node as TreeStore).root.childNodes\n        : (node as Node).childNodes\n\n      childNodes.forEach((child) => {\n        if (\n          (child.checked || (includeHalfChecked && child.indeterminate)) &&\n          (!leafOnly || (leafOnly && child.isLeaf))\n        ) {\n          checkedNodes.push(child.data)\n        }\n\n        traverse(child)\n      })\n    }\n\n    traverse(this)\n\n    return checkedNodes\n  }\n\n  getCheckedKeys(leafOnly = false): TreeKey[] {\n    return this.getCheckedNodes(leafOnly).map((data) => (data || {})[this.key])\n  }\n\n  getHalfCheckedNodes(): TreeNodeData[] {\n    const nodes: TreeNodeData[] = []\n    const traverse = function (node: TreeStore | Node) {\n      const childNodes = (node as TreeStore).root\n        ? (node as TreeStore).root.childNodes\n        : (node as Node).childNodes\n\n      childNodes.forEach((child) => {\n        if (child.indeterminate) {\n          nodes.push(child.data)\n        }\n\n        traverse(child)\n      })\n    }\n\n    traverse(this)\n\n    return nodes\n  }\n\n  getHalfCheckedKeys(): TreeKey[] {\n    return this.getHalfCheckedNodes().map((data) => (data || {})[this.key])\n  }\n\n  _getAllNodes(): Node[] {\n    const allNodes: Node[] = []\n    const nodesMap = this.nodesMap\n    for (const nodeKey in nodesMap) {\n      if (hasOwn(nodesMap, nodeKey)) {\n        allNodes.push(nodesMap[nodeKey])\n      }\n    }\n\n    return allNodes\n  }\n\n  updateChildren(key: TreeKey, data: TreeData): void {\n    const node = this.nodesMap[key]\n    if (!node) return\n    const childNodes = node.childNodes\n    for (let i = childNodes.length - 1; i >= 0; i--) {\n      const child = childNodes[i]\n      this.remove(child.data)\n    }\n    for (let i = 0, j = data.length; i < j; i++) {\n      const child = data[i]\n      this.append(child, node.data)\n    }\n  }\n\n  _setCheckedKeys(\n    key: TreeKey,\n    leafOnly = false,\n    checkedKeys: { [key: string]: boolean }\n  ): void {\n    const allNodes = this._getAllNodes().sort((a, b) => a.level - b.level)\n    const cache = Object.create(null)\n    const keys = Object.keys(checkedKeys)\n    allNodes.forEach((node) => node.setChecked(false, false))\n    const cacheCheckedChild = (node) => {\n      node.childNodes.forEach((child) => {\n        cache[child.data[key]] = true\n        if (child.childNodes?.length) {\n          cacheCheckedChild(child)\n        }\n      })\n    }\n    for (let i = 0, j = allNodes.length; i < j; i++) {\n      const node = allNodes[i]\n      const nodeKey = node.data[key].toString()\n      const checked = keys.includes(nodeKey)\n      if (!checked) {\n        if (node.checked && !cache[nodeKey]) {\n          node.setChecked(false, false)\n        }\n        continue\n      }\n\n      if (node.childNodes.length) {\n        cacheCheckedChild(node)\n      }\n\n      if (node.isLeaf || this.checkStrictly) {\n        node.setChecked(true, false)\n        continue\n      }\n      node.setChecked(true, true)\n\n      if (leafOnly) {\n        node.setChecked(false, false)\n        const traverse = function (node: Node): void {\n          const childNodes = node.childNodes\n          childNodes.forEach((child) => {\n            if (!child.isLeaf) {\n              child.setChecked(false, false)\n            }\n            traverse(child)\n          })\n        }\n        traverse(node)\n      }\n    }\n  }\n\n  setCheckedNodes(array: Node[], leafOnly = false): void {\n    const key = this.key\n    const checkedKeys = {}\n    array.forEach((item) => {\n      checkedKeys[(item || {})[key]] = true\n    })\n\n    this._setCheckedKeys(key, leafOnly, checkedKeys)\n  }\n\n  setCheckedKeys(keys: TreeKey[], leafOnly = false): void {\n    this.defaultCheckedKeys = keys\n    const key = this.key\n    const checkedKeys = {}\n    keys.forEach((key) => {\n      checkedKeys[key] = true\n    })\n\n    this._setCheckedKeys(key, leafOnly, checkedKeys)\n  }\n\n  setDefaultExpandedKeys(keys: TreeKey[]) {\n    keys = keys || []\n    this.defaultExpandedKeys = keys\n    keys.forEach((key) => {\n      const node = this.getNode(key)\n      if (node) node.expand(null, this.autoExpandParent)\n    })\n  }\n\n  setChecked(\n    data: TreeKey | TreeNodeData,\n    checked: boolean,\n    deep: boolean\n  ): void {\n    const node = this.getNode(data)\n\n    if (node) {\n      node.setChecked(!!checked, deep)\n    }\n  }\n\n  getCurrentNode(): Node {\n    return this.currentNode\n  }\n\n  setCurrentNode(currentNode: Node): void {\n    const prevCurrentNode = this.currentNode\n    if (prevCurrentNode) {\n      prevCurrentNode.isCurrent = false\n    }\n    this.currentNode = currentNode\n    this.currentNode.isCurrent = true\n  }\n\n  setUserCurrentNode(node: Node, shouldAutoExpandParent = true): void {\n    const key = node[this.key]\n    const currNode = this.nodesMap[key]\n    this.setCurrentNode(currNode)\n    if (shouldAutoExpandParent && this.currentNode.level > 1) {\n      this.currentNode.parent.expand(null, true)\n    }\n  }\n\n  setCurrentNodeKey(key?: TreeKey, shouldAutoExpandParent = true): void {\n    this.currentNodeKey = key\n    if (isPropAbsent(key)) {\n      this.currentNode && (this.currentNode.isCurrent = false)\n      this.currentNode = null\n      return\n    }\n    const node = this.getNode(key)\n    if (node) {\n      this.setCurrentNode(node)\n      if (shouldAutoExpandParent && this.currentNode.level > 1) {\n        this.currentNode.parent.expand(null, true)\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;AAIe,MAAMA,SAAS,CAAC;EAC7BC,WAAWA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,KAAK,MAAMC,MAAM,IAAIH,OAAO,EAAE;MAC5B,IAAII,MAAM,CAACJ,OAAO,EAAEG,MAAM,CAAC,EAAE;QAC3B,IAAI,CAACA,MAAM,CAAC,GAAGH,OAAO,CAACG,MAAM,CAAC;MACtC;IACA;IACI,IAAI,CAACE,QAAQ,GAAG,EAAE;EACtB;EACEC,UAAUA,CAAA,EAAG;IACX,IAAI,CAACC,IAAI,GAAG,IAAIC,IAAI,CAAC;MACnBC,IAAI,EAAE,IAAI,CAACA,IAAI;MACfC,KAAK,EAAE;IACb,CAAK,CAAC;IACF,IAAI,CAACH,IAAI,CAACD,UAAU,EAAE;IACtB,IAAI,IAAI,CAACK,IAAI,IAAI,IAAI,CAACC,IAAI,EAAE;MAC1B,MAAMC,MAAM,GAAG,IAAI,CAACD,IAAI;MACxBC,MAAM,CAAC,IAAI,CAACN,IAAI,EAAGE,IAAI,IAAK;QAC1B,IAAI,CAACF,IAAI,CAACO,gBAAgB,CAACL,IAAI,CAAC;QAChC,IAAI,CAACM,wBAAwB,EAAE;MACvC,CAAO,CAAC;IACR,CAAK,MAAM;MACL,IAAI,CAACA,wBAAwB,EAAE;IACrC;EACA;EACEC,MAAMA,CAACC,KAAK,EAAE;IACZ,MAAMC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,MAAMP,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMQ,QAAQ,GAAG,eAAAA,CAAeC,IAAI,EAAE;MACpC,MAAMC,UAAU,GAAGD,IAAI,CAACb,IAAI,GAAGa,IAAI,CAACb,IAAI,CAACc,UAAU,GAAGD,IAAI,CAACC,UAAU;MACrE,KAAK,MAAM,CAACC,KAAK,EAAEC,KAAK,CAAC,IAAIF,UAAU,CAACG,OAAO,EAAE,EAAE;QACjDD,KAAK,CAACE,OAAO,GAAGP,gBAAgB,CAACQ,IAAI,CAACH,KAAK,EAAEN,KAAK,EAAEM,KAAK,CAACd,IAAI,EAAEc,KAAK,CAAC;QACtE,IAAID,KAAK,GAAG,EAAE,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;UACjC,MAAMK,QAAQ,EAAE;QAC1B;QACQ,MAAMR,QAAQ,CAACI,KAAK,CAAC;MAC7B;MACM,IAAI,CAACH,IAAI,CAACK,OAAO,IAAIJ,UAAU,CAACO,MAAM,EAAE;QACtC,IAAIC,SAAS,GAAG,IAAI;QACpBA,SAAS,GAAG,CAACR,UAAU,CAACS,IAAI,CAAEP,KAAK,IAAKA,KAAK,CAACE,OAAO,CAAC;QACtD,IAAIL,IAAI,CAACb,IAAI,EAAE;UAEba,IAAI,CAACb,IAAI,CAACkB,OAAO,GAAGI,SAAS,KAAK,KAAK;QACjD,CAAS,MAAM;UAELT,IAAI,CAACK,OAAO,GAAGI,SAAS,KAAK,KAAK;QAC5C;MACA;MACM,IAAI,CAACZ,KAAK,EACR;MACF,IAAIG,IAAI,CAACK,OAAO,IAAI,CAACL,IAAI,CAACW,MAAM,EAAE;QAChC,IAAI,CAACpB,IAAI,IAAIS,IAAI,CAACY,MAAM,EAAE;UAExBZ,IAAI,CAACa,MAAM,EAAE;QACvB;MACA;IACA,CAAK;IACDd,QAAQ,CAAC,IAAI,CAAC;EAClB;EACEe,OAAOA,CAACC,MAAM,EAAE;IACd,MAAMC,eAAe,GAAGD,MAAM,KAAK,IAAI,CAAC5B,IAAI,CAACE,IAAI;IACjD,IAAI2B,eAAe,EAAE;MACnB,IAAI,CAAC/B,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACE,IAAI,CAAC2B,OAAO,CAACC,MAAM,CAAC;MACzB,IAAI,CAACpB,wBAAwB,EAAE;MAC/B,IAAI,CAACsB,iBAAiB,CAAC,IAAI,CAACnC,cAAc,CAAC;IACjD,CAAK,MAAM;MACL,IAAI,CAACK,IAAI,CAAC+B,cAAc,EAAE;IAChC;EACA;EACEC,OAAOA,CAAC9B,IAAI,EAAE;IACZ,IAAIA,IAAI,YAAYD,IAAI,EACtB,OAAOC,IAAI;IACb,MAAM+B,GAAG,GAAGC,QAAQ,CAAChC,IAAI,CAAC,GAAGiC,UAAU,CAAC,IAAI,CAACF,GAAG,EAAE/B,IAAI,CAAC,GAAGA,IAAI;IAC9D,OAAO,IAAI,CAACJ,QAAQ,CAACmC,GAAG,CAAC,IAAI,IAAI;EACrC;EACEG,YAAYA,CAAClC,IAAI,EAAEmC,OAAO,EAAE;IAC1B,MAAMC,OAAO,GAAG,IAAI,CAACN,OAAO,CAACK,OAAO,CAAC;IACrCC,OAAO,CAACC,MAAM,CAACH,YAAY,CAAC;MAAElC;IAAI,CAAE,EAAEoC,OAAO,CAAC;EAClD;EACEE,WAAWA,CAACtC,IAAI,EAAEmC,OAAO,EAAE;IACzB,MAAMC,OAAO,GAAG,IAAI,CAACN,OAAO,CAACK,OAAO,CAAC;IACrCC,OAAO,CAACC,MAAM,CAACC,WAAW,CAAC;MAAEtC;IAAI,CAAE,EAAEoC,OAAO,CAAC;EACjD;EACEG,MAAMA,CAACvC,IAAI,EAAE;IACX,MAAMW,IAAI,GAAG,IAAI,CAACmB,OAAO,CAAC9B,IAAI,CAAC;IAC/B,IAAIW,IAAI,IAAIA,IAAI,CAAC0B,MAAM,EAAE;MACvB,IAAI1B,IAAI,KAAK,IAAI,CAACnB,WAAW,EAAE;QAC7B,IAAI,CAACA,WAAW,GAAG,IAAI;MAC/B;MACMmB,IAAI,CAAC0B,MAAM,CAACG,WAAW,CAAC7B,IAAI,CAAC;IACnC;EACA;EACE8B,MAAMA,CAACzC,IAAI,EAAE0C,UAAU,EAAE;IACvB,MAAMC,UAAU,GAAG,CAACC,YAAY,CAACF,UAAU,CAAC,GAAG,IAAI,CAACZ,OAAO,CAACY,UAAU,CAAC,GAAG,IAAI,CAAC5C,IAAI;IACnF,IAAI6C,UAAU,EAAE;MACdA,UAAU,CAACE,WAAW,CAAC;QAAE7C;MAAI,CAAE,CAAC;IACtC;EACA;EACEM,wBAAwBA,CAAA,EAAG;IACzB,MAAMwC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,IAAI,EAAE;IACxD,MAAMlD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9BkD,kBAAkB,CAACC,OAAO,CAAEC,UAAU,IAAK;MACzC,MAAMrC,IAAI,GAAGf,QAAQ,CAACoD,UAAU,CAAC;MACjC,IAAIrC,IAAI,EAAE;QACRA,IAAI,CAACsC,UAAU,CAAC,IAAI,EAAE,CAAC,IAAI,CAACC,aAAa,CAAC;MAClD;IACA,CAAK,CAAC;EACN;EACEC,uBAAuBA,CAACxC,IAAI,EAAE;IAC5B,MAAMmC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,IAAI,EAAE;IACxD,IAAIA,kBAAkB,CAACM,QAAQ,CAACzC,IAAI,CAACoB,GAAG,CAAC,EAAE;MACzCpB,IAAI,CAACsC,UAAU,CAAC,IAAI,EAAE,CAAC,IAAI,CAACC,aAAa,CAAC;IAChD;EACA;EACEG,oBAAoBA,CAAC3B,MAAM,EAAE;IAC3B,IAAIA,MAAM,KAAK,IAAI,CAACoB,kBAAkB,EAAE;MACtC,IAAI,CAACA,kBAAkB,GAAGpB,MAAM;MAChC,IAAI,CAACpB,wBAAwB,EAAE;IACrC;EACA;EACEgD,YAAYA,CAAC3C,IAAI,EAAE;IACjB,MAAMoB,GAAG,GAAG,IAAI,CAACA,GAAG;IACpB,IAAI,CAACpB,IAAI,IAAI,CAACA,IAAI,CAACX,IAAI,EACrB;IACF,IAAI,CAAC+B,GAAG,EAAE;MACR,IAAI,CAACnC,QAAQ,CAACe,IAAI,CAAC4C,EAAE,CAAC,GAAG5C,IAAI;IACnC,CAAK,MAAM;MACL,MAAM6C,OAAO,GAAG7C,IAAI,CAACoB,GAAG;MACxB,IAAIyB,OAAO,KAAK,KAAK,CAAC,EACpB,IAAI,CAAC5D,QAAQ,CAACe,IAAI,CAACoB,GAAG,CAAC,GAAGpB,IAAI;IACtC;EACA;EACE8C,cAAcA,CAAC9C,IAAI,EAAE;IACnB,MAAMoB,GAAG,GAAG,IAAI,CAACA,GAAG;IACpB,IAAI,CAACA,GAAG,IAAI,CAACpB,IAAI,IAAI,CAACA,IAAI,CAACX,IAAI,EAC7B;IACFW,IAAI,CAACC,UAAU,CAACmC,OAAO,CAAEjC,KAAK,IAAK;MACjC,IAAI,CAAC2C,cAAc,CAAC3C,KAAK,CAAC;IAChC,CAAK,CAAC;IACF,OAAO,IAAI,CAAClB,QAAQ,CAACe,IAAI,CAACoB,GAAG,CAAC;EAClC;EACE2B,eAAeA,CAACC,QAAQ,GAAG,KAAK,EAAEC,kBAAkB,GAAG,KAAK,EAAE;IAC5D,MAAMC,YAAY,GAAG,EAAE;IACvB,MAAMnD,QAAQ,GAAG,SAAAA,CAASC,IAAI,EAAE;MAC9B,MAAMC,UAAU,GAAGD,IAAI,CAACb,IAAI,GAAGa,IAAI,CAACb,IAAI,CAACc,UAAU,GAAGD,IAAI,CAACC,UAAU;MACrEA,UAAU,CAACmC,OAAO,CAAEjC,KAAK,IAAK;QAC5B,IAAI,CAACA,KAAK,CAACgD,OAAO,IAAIF,kBAAkB,IAAI9C,KAAK,CAACiD,aAAa,MAAM,CAACJ,QAAQ,IAAIA,QAAQ,IAAI7C,KAAK,CAACQ,MAAM,CAAC,EAAE;UAC3GuC,YAAY,CAACG,IAAI,CAAClD,KAAK,CAACd,IAAI,CAAC;QACvC;QACQU,QAAQ,CAACI,KAAK,CAAC;MACvB,CAAO,CAAC;IACR,CAAK;IACDJ,QAAQ,CAAC,IAAI,CAAC;IACd,OAAOmD,YAAY;EACvB;EACEI,cAAcA,CAACN,QAAQ,GAAG,KAAK,EAAE;IAC/B,OAAO,IAAI,CAACD,eAAe,CAACC,QAAQ,CAAC,CAACO,GAAG,CAAElE,IAAI,IAAK,CAACA,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC+B,GAAG,CAAC,CAAC;EAC/E;EACEoC,mBAAmBA,CAAA,EAAG;IACpB,MAAMC,KAAK,GAAG,EAAE;IAChB,MAAM1D,QAAQ,GAAG,SAAAA,CAASC,IAAI,EAAE;MAC9B,MAAMC,UAAU,GAAGD,IAAI,CAACb,IAAI,GAAGa,IAAI,CAACb,IAAI,CAACc,UAAU,GAAGD,IAAI,CAACC,UAAU;MACrEA,UAAU,CAACmC,OAAO,CAAEjC,KAAK,IAAK;QAC5B,IAAIA,KAAK,CAACiD,aAAa,EAAE;UACvBK,KAAK,CAACJ,IAAI,CAAClD,KAAK,CAACd,IAAI,CAAC;QAChC;QACQU,QAAQ,CAACI,KAAK,CAAC;MACvB,CAAO,CAAC;IACR,CAAK;IACDJ,QAAQ,CAAC,IAAI,CAAC;IACd,OAAO0D,KAAK;EAChB;EACEC,kBAAkBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACF,mBAAmB,EAAE,CAACD,GAAG,CAAElE,IAAI,IAAK,CAACA,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC+B,GAAG,CAAC,CAAC;EAC3E;EACEuC,YAAYA,CAAA,EAAG;IACb,MAAMC,QAAQ,GAAG,EAAE;IACnB,MAAM3E,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,KAAK,MAAM4D,OAAO,IAAI5D,QAAQ,EAAE;MAC9B,IAAID,MAAM,CAACC,QAAQ,EAAE4D,OAAO,CAAC,EAAE;QAC7Be,QAAQ,CAACP,IAAI,CAACpE,QAAQ,CAAC4D,OAAO,CAAC,CAAC;MACxC;IACA;IACI,OAAOe,QAAQ;EACnB;EACE1C,cAAcA,CAACE,GAAG,EAAE/B,IAAI,EAAE;IACxB,MAAMW,IAAI,GAAG,IAAI,CAACf,QAAQ,CAACmC,GAAG,CAAC;IAC/B,IAAI,CAACpB,IAAI,EACP;IACF,MAAMC,UAAU,GAAGD,IAAI,CAACC,UAAU;IAClC,KAAK,IAAI4D,CAAC,GAAG5D,UAAU,CAACO,MAAM,GAAG,CAAC,EAAEqD,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC/C,MAAM1D,KAAK,GAAGF,UAAU,CAAC4D,CAAC,CAAC;MAC3B,IAAI,CAACjC,MAAM,CAACzB,KAAK,CAACd,IAAI,CAAC;IAC7B;IACI,KAAK,IAAIwE,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGzE,IAAI,CAACmB,MAAM,EAAEqD,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC3C,MAAM1D,KAAK,GAAGd,IAAI,CAACwE,CAAC,CAAC;MACrB,IAAI,CAAC/B,MAAM,CAAC3B,KAAK,EAAEH,IAAI,CAACX,IAAI,CAAC;IACnC;EACA;EACE0E,eAAeA,CAAC3C,GAAG,EAAE4B,QAAQ,GAAG,KAAK,EAAEgB,WAAW,EAAE;IAClD,MAAMJ,QAAQ,GAAG,IAAI,CAACD,YAAY,EAAE,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,KAAK,GAAGD,CAAC,CAACC,KAAK,CAAC;IACtE,MAAMC,KAAK,kBAAmBC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACjD,MAAMC,IAAI,GAAGF,MAAM,CAACE,IAAI,CAACR,WAAW,CAAC;IACrCJ,QAAQ,CAACxB,OAAO,CAAEpC,IAAI,IAAKA,IAAI,CAACsC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACzD,MAAMmC,iBAAiB,GAAIzE,IAAI,IAAK;MAClCA,IAAI,CAACC,UAAU,CAACmC,OAAO,CAAEjC,KAAK,IAAK;QACjC,IAAIuE,EAAE;QACNL,KAAK,CAAClE,KAAK,CAACd,IAAI,CAAC+B,GAAG,CAAC,CAAC,GAAG,IAAI;QAC7B,IAAI,CAACsD,EAAE,GAAGvE,KAAK,CAACF,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyE,EAAE,CAAClE,MAAM,EAAE;UACxDiE,iBAAiB,CAACtE,KAAK,CAAC;QAClC;MACA,CAAO,CAAC;IACR,CAAK;IACD,KAAK,IAAI0D,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGF,QAAQ,CAACpD,MAAM,EAAEqD,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC/C,MAAM7D,IAAI,GAAG4D,QAAQ,CAACC,CAAC,CAAC;MACxB,MAAMhB,OAAO,GAAG7C,IAAI,CAACX,IAAI,CAAC+B,GAAG,CAAC,CAACuD,QAAQ,EAAE;MACzC,MAAMxB,OAAO,GAAGqB,IAAI,CAAC/B,QAAQ,CAACI,OAAO,CAAC;MACtC,IAAI,CAACM,OAAO,EAAE;QACZ,IAAInD,IAAI,CAACmD,OAAO,IAAI,CAACkB,KAAK,CAACxB,OAAO,CAAC,EAAE;UACnC7C,IAAI,CAACsC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC;QACvC;QACQ;MACR;MACM,IAAItC,IAAI,CAACC,UAAU,CAACO,MAAM,EAAE;QAC1BiE,iBAAiB,CAACzE,IAAI,CAAC;MAC/B;MACM,IAAIA,IAAI,CAACW,MAAM,IAAI,IAAI,CAAC4B,aAAa,EAAE;QACrCvC,IAAI,CAACsC,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC;QAC5B;MACR;MACMtC,IAAI,CAACsC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC;MAC3B,IAAIU,QAAQ,EAAE;QACZhD,IAAI,CAACsC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC;QAC7B,MAAMvC,QAAQ,GAAG,SAAAA,CAAS6E,KAAK,EAAE;UAC/B,MAAM3E,UAAU,GAAG2E,KAAK,CAAC3E,UAAU;UACnCA,UAAU,CAACmC,OAAO,CAAEjC,KAAK,IAAK;YAC5B,IAAI,CAACA,KAAK,CAACQ,MAAM,EAAE;cACjBR,KAAK,CAACmC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC;YAC5C;YACYvC,QAAQ,CAACI,KAAK,CAAC;UAC3B,CAAW,CAAC;QACZ,CAAS;QACDJ,QAAQ,CAACC,IAAI,CAAC;MACtB;IACA;EACA;EACE6E,eAAeA,CAACC,KAAK,EAAE9B,QAAQ,GAAG,KAAK,EAAE;IACvC,MAAM5B,GAAG,GAAG,IAAI,CAACA,GAAG;IACpB,MAAM4C,WAAW,GAAG,EAAE;IACtBc,KAAK,CAAC1C,OAAO,CAAE2C,IAAI,IAAK;MACtBf,WAAW,CAAC,CAACe,IAAI,IAAI,EAAE,EAAE3D,GAAG,CAAC,CAAC,GAAG,IAAI;IAC3C,CAAK,CAAC;IACF,IAAI,CAAC2C,eAAe,CAAC3C,GAAG,EAAE4B,QAAQ,EAAEgB,WAAW,CAAC;EACpD;EACEgB,cAAcA,CAACR,IAAI,EAAExB,QAAQ,GAAG,KAAK,EAAE;IACrC,IAAI,CAACb,kBAAkB,GAAGqC,IAAI;IAC9B,MAAMpD,GAAG,GAAG,IAAI,CAACA,GAAG;IACpB,MAAM4C,WAAW,GAAG,EAAE;IACtBQ,IAAI,CAACpC,OAAO,CAAE6C,IAAI,IAAK;MACrBjB,WAAW,CAACiB,IAAI,CAAC,GAAG,IAAI;IAC9B,CAAK,CAAC;IACF,IAAI,CAAClB,eAAe,CAAC3C,GAAG,EAAE4B,QAAQ,EAAEgB,WAAW,CAAC;EACpD;EACEkB,sBAAsBA,CAACV,IAAI,EAAE;IAC3BA,IAAI,GAAGA,IAAI,IAAI,EAAE;IACjB,IAAI,CAACW,mBAAmB,GAAGX,IAAI;IAC/BA,IAAI,CAACpC,OAAO,CAAEhB,GAAG,IAAK;MACpB,MAAMpB,IAAI,GAAG,IAAI,CAACmB,OAAO,CAACC,GAAG,CAAC;MAC9B,IAAIpB,IAAI,EACNA,IAAI,CAACa,MAAM,CAAC,IAAI,EAAE,IAAI,CAACuE,gBAAgB,CAAC;IAChD,CAAK,CAAC;EACN;EACE9C,UAAUA,CAACjD,IAAI,EAAE8D,OAAO,EAAEkC,IAAI,EAAE;IAC9B,MAAMrF,IAAI,GAAG,IAAI,CAACmB,OAAO,CAAC9B,IAAI,CAAC;IAC/B,IAAIW,IAAI,EAAE;MACRA,IAAI,CAACsC,UAAU,CAAC,CAAC,CAACa,OAAO,EAAEkC,IAAI,CAAC;IACtC;EACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAACzG,WAAW;EAC3B;EACE0G,cAAcA,CAAC1G,WAAW,EAAE;IAC1B,MAAM2G,eAAe,GAAG,IAAI,CAAC3G,WAAW;IACxC,IAAI2G,eAAe,EAAE;MACnBA,eAAe,CAACC,SAAS,GAAG,KAAK;IACvC;IACI,IAAI,CAAC5G,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACA,WAAW,CAAC4G,SAAS,GAAG,IAAI;EACrC;EACEC,kBAAkBA,CAAC1F,IAAI,EAAE2F,sBAAsB,GAAG,IAAI,EAAE;IACtD,MAAMvE,GAAG,GAAGpB,IAAI,CAAC,IAAI,CAACoB,GAAG,CAAC;IAC1B,MAAMwE,QAAQ,GAAG,IAAI,CAAC3G,QAAQ,CAACmC,GAAG,CAAC;IACnC,IAAI,CAACmE,cAAc,CAACK,QAAQ,CAAC;IAC7B,IAAID,sBAAsB,IAAI,IAAI,CAAC9G,WAAW,CAACuF,KAAK,GAAG,CAAC,EAAE;MACxD,IAAI,CAACvF,WAAW,CAAC6C,MAAM,CAACb,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;IAChD;EACA;EACEI,iBAAiBA,CAACG,GAAG,EAAEuE,sBAAsB,GAAG,IAAI,EAAE;IACpD,IAAI,CAAC7G,cAAc,GAAGsC,GAAG;IACzB,IAAIa,YAAY,CAACb,GAAG,CAAC,EAAE;MACrB,IAAI,CAACvC,WAAW,KAAK,IAAI,CAACA,WAAW,CAAC4G,SAAS,GAAG,KAAK,CAAC;MACxD,IAAI,CAAC5G,WAAW,GAAG,IAAI;MACvB;IACN;IACI,MAAMmB,IAAI,GAAG,IAAI,CAACmB,OAAO,CAACC,GAAG,CAAC;IAC9B,IAAIpB,IAAI,EAAE;MACR,IAAI,CAACuF,cAAc,CAACvF,IAAI,CAAC;MACzB,IAAI2F,sBAAsB,IAAI,IAAI,CAAC9G,WAAW,CAACuF,KAAK,GAAG,CAAC,EAAE;QACxD,IAAI,CAACvF,WAAW,CAAC6C,MAAM,CAACb,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;MAClD;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}