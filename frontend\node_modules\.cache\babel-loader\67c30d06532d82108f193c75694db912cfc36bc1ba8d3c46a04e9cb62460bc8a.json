{"ast": null, "code": "import { defineComponent, openBlock, createBlock, Transition, mergeProps, unref, toHandlers, withCtx, renderSlot } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElCollapseTransition\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  setup(__props) {\n    const ns = useNamespace(\"collapse-transition\");\n    const reset = el => {\n      el.style.maxHeight = \"\";\n      el.style.overflow = el.dataset.oldOverflow;\n      el.style.paddingTop = el.dataset.oldPaddingTop;\n      el.style.paddingBottom = el.dataset.oldPaddingBottom;\n    };\n    const on = {\n      beforeEnter(el) {\n        if (!el.dataset) el.dataset = {};\n        el.dataset.oldPaddingTop = el.style.paddingTop;\n        el.dataset.oldPaddingBottom = el.style.paddingBottom;\n        if (el.style.height) el.dataset.elExistsHeight = el.style.height;\n        el.style.maxHeight = 0;\n        el.style.paddingTop = 0;\n        el.style.paddingBottom = 0;\n      },\n      enter(el) {\n        requestAnimationFrame(() => {\n          el.dataset.oldOverflow = el.style.overflow;\n          if (el.dataset.elExistsHeight) {\n            el.style.maxHeight = el.dataset.elExistsHeight;\n          } else if (el.scrollHeight !== 0) {\n            el.style.maxHeight = `${el.scrollHeight}px`;\n          } else {\n            el.style.maxHeight = 0;\n          }\n          el.style.paddingTop = el.dataset.oldPaddingTop;\n          el.style.paddingBottom = el.dataset.oldPaddingBottom;\n          el.style.overflow = \"hidden\";\n        });\n      },\n      afterEnter(el) {\n        el.style.maxHeight = \"\";\n        el.style.overflow = el.dataset.oldOverflow;\n      },\n      enterCancelled(el) {\n        reset(el);\n      },\n      beforeLeave(el) {\n        if (!el.dataset) el.dataset = {};\n        el.dataset.oldPaddingTop = el.style.paddingTop;\n        el.dataset.oldPaddingBottom = el.style.paddingBottom;\n        el.dataset.oldOverflow = el.style.overflow;\n        el.style.maxHeight = `${el.scrollHeight}px`;\n        el.style.overflow = \"hidden\";\n      },\n      leave(el) {\n        if (el.scrollHeight !== 0) {\n          el.style.maxHeight = 0;\n          el.style.paddingTop = 0;\n          el.style.paddingBottom = 0;\n        }\n      },\n      afterLeave(el) {\n        reset(el);\n      },\n      leaveCancelled(el) {\n        reset(el);\n      }\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(Transition, mergeProps({\n        name: unref(ns).b()\n      }, toHandlers(on)), {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 16, [\"name\"]);\n    };\n  }\n});\nvar CollapseTransition = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"collapse-transition.vue\"]]);\nexport { CollapseTransition as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace", "reset", "el", "style", "maxHeight", "overflow", "dataset", "oldOverflow", "paddingTop", "oldPaddingTop", "paddingBottom", "oldPaddingBottom", "on", "beforeEnter", "height", "elExistsHeight", "enter", "requestAnimationFrame", "scrollHeight", "afterEnter", "enterCancelled", "beforeLeave", "leave", "afterLeave", "leaveCancelled"], "sources": ["../../../../../../packages/components/collapse-transition/src/collapse-transition.vue"], "sourcesContent": ["<template>\n  <transition :name=\"ns.b()\" v-on=\"on\">\n    <slot />\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useNamespace } from '@element-plus/hooks'\nimport type { RendererElement } from 'vue'\n\ndefineOptions({\n  name: 'ElCollapseTransition',\n})\n\nconst ns = useNamespace('collapse-transition')\n\nconst reset = (el: RendererElement) => {\n  el.style.maxHeight = ''\n  el.style.overflow = el.dataset.oldOverflow\n  el.style.paddingTop = el.dataset.oldPaddingTop\n  el.style.paddingBottom = el.dataset.oldPaddingBottom\n}\n\nconst on = {\n  beforeEnter(el: RendererElement) {\n    if (!el.dataset) el.dataset = {}\n\n    el.dataset.oldPaddingTop = el.style.paddingTop\n    el.dataset.oldPaddingBottom = el.style.paddingBottom\n    if (el.style.height) el.dataset.elExistsHeight = el.style.height\n\n    el.style.maxHeight = 0\n    el.style.paddingTop = 0\n    el.style.paddingBottom = 0\n  },\n\n  enter(el: RendererElement) {\n    requestAnimationFrame(() => {\n      el.dataset.oldOverflow = el.style.overflow\n      if (el.dataset.elExistsHeight) {\n        el.style.maxHeight = el.dataset.elExistsHeight\n      } else if (el.scrollHeight !== 0) {\n        el.style.maxHeight = `${el.scrollHeight}px`\n      } else {\n        el.style.maxHeight = 0\n      }\n\n      el.style.paddingTop = el.dataset.oldPaddingTop\n      el.style.paddingBottom = el.dataset.oldPaddingBottom\n      el.style.overflow = 'hidden'\n    })\n  },\n\n  afterEnter(el: RendererElement) {\n    el.style.maxHeight = ''\n    el.style.overflow = el.dataset.oldOverflow\n  },\n\n  enterCancelled(el: RendererElement) {\n    reset(el)\n  },\n\n  beforeLeave(el: RendererElement) {\n    if (!el.dataset) el.dataset = {}\n    el.dataset.oldPaddingTop = el.style.paddingTop\n    el.dataset.oldPaddingBottom = el.style.paddingBottom\n    el.dataset.oldOverflow = el.style.overflow\n\n    el.style.maxHeight = `${el.scrollHeight}px`\n    el.style.overflow = 'hidden'\n  },\n\n  leave(el: RendererElement) {\n    if (el.scrollHeight !== 0) {\n      el.style.maxHeight = 0\n      el.style.paddingTop = 0\n      el.style.paddingBottom = 0\n    }\n  },\n\n  afterLeave(el: RendererElement) {\n    reset(el)\n  },\n\n  leaveCancelled(el: RendererElement) {\n    reset(el)\n  },\n}\n</script>\n"], "mappings": ";;;mCAUc;EACZA,IAAM;AACR;;;;IAEM,MAAAC,EAAA,GAAKC,YAAA,CAAa,qBAAqB;IAEvC,MAAAC,KAAA,GAASC,EAAwB;MACrCA,EAAA,CAAGC,KAAA,CAAMC,SAAY;MAClBF,EAAA,CAAAC,KAAA,CAAME,QAAW,GAAAH,EAAA,CAAGI,OAAQ,CAAAC,WAAA;MAC5BL,EAAA,CAAAC,KAAA,CAAMK,UAAa,GAAAN,EAAA,CAAGI,OAAQ,CAAAG,aAAA;MAC9BP,EAAA,CAAAC,KAAA,CAAMO,aAAgB,GAAAR,EAAA,CAAGI,OAAQ,CAAAK,gBAAA;IAAA,CACtC;IAEA,MAAMC,EAAK;MACTC,YAAYX,EAAqB;QAC/B,IAAI,CAACA,EAAA,CAAGI,OAAS,EAEdJ,EAAA,CAAAI,OAAwB;QACxBJ,EAAA,CAAAI,OAAA,CAAQG,aAAmB,GAAAP,EAAA,CAAAC,KAAS,CAAAK,UAAA;QACvCN,EAAA,CAAAI,OAAa,CAAAK,gBAAmB,GAAAT,EAAA,CAAAC,KAAA,CAAAO,aAAoB;QAEpD,IAAAR,EAAA,CAAAC,KAAqB,CAAAW,MAAA,EACrBZ,EAAA,CAAAI,OAAsB,CAAAS,cAAA,GAAAb,EAAA,CAAAC,KAAA,CAAAW,MAAA;QACtBZ,EAAA,CAAGC,KAAA,CAAMC,SAAgB;QAC3BF,EAAA,CAAAC,KAAA,CAAAK,UAAA;QAAAN,EAAA,CAAAC,KAE2B,CAAAO,aAAA;MACzB;MACKM,MAAAd,EAAA;QACCe,qBAA2B;UAC1Bf,EAAA,CAAAI,OAAA,CAAMC,WAAY,GAAAL,EAAW,CAAAC,KAAA,CAAAE,QAAA;UAClC,IAAAH,EAAA,CAAAI,OAAc,CAAAS,cAAA,EAAiB;YAC7Bb,EAAA,CAAGC,KAAM,CAAAC,SAAA,GAAYF,EAAG,CAAAI,OAAe,CAAAS,cAAA;UAAA,CAClC,UAAAb,EAAA,CAAAgB,YAAA;YACLhB,EAAA,CAAGC,KAAA,CAAMC,SAAY,MAAAF,EAAA,CAAAgB,YAAA;UAAA,CACvB;YAEGhB,EAAA,CAAAC,KAAmB,CAAAC,SAAA;UACtB;UACAF,EAAA,CAAGC,KAAA,CAAMK,UAAW,GAAAN,EAAA,CAAAI,OAAA,CAAAG,aAAA;UACrBP,EAAA,CAAAC,KAAA,CAAAO,aAAA,GAAAR,EAAA,CAAAI,OAAA,CAAAK,gBAAA;UACHT,EAAA,CAAAC,KAAA,CAAAE,QAAA;QAAA;MAGE;MACGc,WAAMjB,EAAW;QACtBA,EAAA,CAAAC,KAAA,CAAAC,SAAA;QAAAF,EAAA,CAAAC,KAAA,CAAAE,QAEoC,GAAAH,EAAA,CAAAI,OAAA,CAAAC,WAAA;MAClC;MACFa,eAAAlB,EAAA;QAAAD,KAAA,CAAAC,EAAA;MAGE;MACGmB,YAAAnB,EAAwB;QACxB,KAAAA,EAAA,CAAAI,OAA2B,EAC3BJ,EAAA,CAAAI,OAAsB;QAEzBJ,EAAA,CAAGI,OAAM,CAAAG,aAAe,GAAGP,EAAY,CAAAC,KAAA,CAAAK,UAAA;QACvCN,EAAA,CAAGI,OAAiB,CAAAK,gBAAA,GAAAT,EAAA,CAAAC,KAAA,CAAAO,aAAA;QACtBR,EAAA,CAAAI,OAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,KAAA,CAAAE,QAAA;QAAAH,EAAA,CAAAC,KAE2B,CAAAC,SAAA,MAAAF,EAAA,CAAAgB,YAAA;QACrBhB,EAAA,CAAAC,KAAA,CAAAE,QAAA,WAAuB;MACzB;MACAiB,MAAApB,EAAA;QACA,IAAAA,EAAA,CAAAgB,YAAyB;UAC3BhB,EAAA,CAAAC,KAAA,CAAAC,SAAA;UACFF,EAAA,CAAAC,KAAA,CAAAK,UAAA;UAAAN,EAAA,CAAAC,KAEgC,CAAAO,aAAA;QAC9B;MAAQ,CACV;MAEAa,WAAArB,EAAA,EAAe;QACbD,KAAA,CAAMC,EAAE;MAAA,CACV;MACFsB,eAAAtB,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}