{"ast": null, "code": "import { LTR, RTL, HORIZONTAL, FORWARD, BACKWARD, RTL_OFFSET_POS_DESC, RTL_OFFSET_NAG, RTL_OFFSET_POS_ASC } from './defaults.mjs';\nconst getScrollDir = (prev, cur) => prev < cur ? FORWARD : BACKWARD;\nconst isHorizontal = dir => dir === LTR || dir === RTL || dir === HORIZONTAL;\nconst isRTL = dir => dir === RTL;\nlet cachedRTLResult = null;\nfunction getRTLOffsetType(recalculate = false) {\n  if (cachedRTLResult === null || recalculate) {\n    const outerDiv = document.createElement(\"div\");\n    const outerStyle = outerDiv.style;\n    outerStyle.width = \"50px\";\n    outerStyle.height = \"50px\";\n    outerStyle.overflow = \"scroll\";\n    outerStyle.direction = \"rtl\";\n    const innerDiv = document.createElement(\"div\");\n    const innerStyle = innerDiv.style;\n    innerStyle.width = \"100px\";\n    innerStyle.height = \"100px\";\n    outerDiv.appendChild(innerDiv);\n    document.body.appendChild(outerDiv);\n    if (outerDiv.scrollLeft > 0) {\n      cachedRTLResult = RTL_OFFSET_POS_DESC;\n    } else {\n      outerDiv.scrollLeft = 1;\n      if (outerDiv.scrollLeft === 0) {\n        cachedRTLResult = RTL_OFFSET_NAG;\n      } else {\n        cachedRTLResult = RTL_OFFSET_POS_ASC;\n      }\n    }\n    document.body.removeChild(outerDiv);\n    return cachedRTLResult;\n  }\n  return cachedRTLResult;\n}\nfunction renderThumbStyle({\n  move,\n  size,\n  bar\n}, layout) {\n  const style = {};\n  const translate = `translate${bar.axis}(${move}px)`;\n  style[bar.size] = size;\n  style.transform = translate;\n  if (layout === \"horizontal\") {\n    style.height = \"100%\";\n  } else {\n    style.width = \"100%\";\n  }\n  return style;\n}\nexport { getRTLOffsetType, getScrollDir, isHorizontal, isRTL, renderThumbStyle };", "map": {"version": 3, "names": ["getScrollDir", "prev", "cur", "FORWARD", "BACKWARD", "isHorizontal", "dir", "LTR", "RTL", "HORIZONTAL", "isRTL", "cachedRTLResult", "getRTLOffsetType", "recalculate", "outerDiv", "document", "createElement", "outerStyle", "style", "width", "height", "overflow", "direction", "innerDiv", "innerStyle", "append<PERSON><PERSON><PERSON>", "body", "scrollLeft", "RTL_OFFSET_POS_DESC", "RTL_OFFSET_NAG", "RTL_OFFSET_POS_ASC", "<PERSON><PERSON><PERSON><PERSON>", "renderThumbStyle", "move", "size", "bar", "layout", "translate", "axis", "transform"], "sources": ["../../../../../../packages/components/virtual-list/src/utils.ts"], "sourcesContent": ["import {\n  BACKWARD,\n  FORWARD,\n  HORIZONTAL,\n  LTR,\n  RTL,\n  RTL_OFFSET_NAG,\n  RTL_OFFSET_POS_ASC,\n  RTL_OFFSET_POS_DESC,\n} from './defaults'\n\nimport type { CSSProperties } from 'vue'\nimport type { Direction, RTLOffsetType } from './types'\n\nexport const getScrollDir = (prev: number, cur: number) =>\n  prev < cur ? FORWARD : BACKWARD\n\nexport const isHorizontal = (dir: string) =>\n  dir === LTR || dir === RTL || dir === HORIZONTAL\n\nexport const isRTL = (dir: Direction) => dir === RTL\n\nlet cachedRTLResult: RTLOffsetType | null = null\n\nexport function getRTLOffsetType(recalculate = false): RTLOffsetType {\n  if (cachedRTLResult === null || recalculate) {\n    const outerDiv = document.createElement('div')\n    const outerStyle = outerDiv.style\n    outerStyle.width = '50px'\n    outerStyle.height = '50px'\n    outerStyle.overflow = 'scroll'\n    outerStyle.direction = 'rtl'\n\n    const innerDiv = document.createElement('div')\n    const innerStyle = innerDiv.style\n    innerStyle.width = '100px'\n    innerStyle.height = '100px'\n\n    outerDiv.appendChild(innerDiv)\n\n    document.body.appendChild(outerDiv)\n\n    if (outerDiv.scrollLeft > 0) {\n      cachedRTLResult = RTL_OFFSET_POS_DESC\n    } else {\n      outerDiv.scrollLeft = 1\n      if (outerDiv.scrollLeft === 0) {\n        cachedRTLResult = RTL_OFFSET_NAG\n      } else {\n        cachedRTLResult = RTL_OFFSET_POS_ASC\n      }\n    }\n\n    document.body.removeChild(outerDiv)\n\n    return cachedRTLResult\n  }\n\n  return cachedRTLResult\n}\n\ntype RenderThumbStyleParams = {\n  bar: {\n    size: 'height' | 'width'\n    axis: 'X' | 'Y'\n  }\n  size: string\n  move: number\n}\n\nexport function renderThumbStyle(\n  { move, size, bar }: RenderThumbStyleParams,\n  layout: string\n) {\n  const style: CSSProperties = {}\n  const translate = `translate${bar.axis}(${move}px)`\n\n  style[bar.size] = size\n  style.transform = translate\n\n  if (layout === 'horizontal') {\n    style.height = '100%'\n  } else {\n    style.width = '100%'\n  }\n\n  return style\n}\n"], "mappings": ";AAUY,MAACA,YAAY,GAAGA,CAACC,IAAI,EAAEC,GAAG,KAAKD,IAAI,GAAGC,GAAG,GAAGC,OAAO,GAAGC,QAAA;AACtD,MAACC,YAAY,GAAIC,GAAG,IAAKA,GAAG,KAAKC,GAAG,IAAID,GAAG,KAAKE,GAAG,IAAIF,GAAG,KAAKG,UAAA;AAC/D,MAACC,KAAK,GAAIJ,GAAG,IAAKA,GAAG,KAAKE,GAAA;AACtC,IAAIG,eAAe,GAAG,IAAI;AACnB,SAASC,gBAAgBA,CAACC,WAAW,GAAG,KAAK,EAAE;EACpD,IAAIF,eAAe,KAAK,IAAI,IAAIE,WAAW,EAAE;IAC3C,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9C,MAAMC,UAAU,GAAGH,QAAQ,CAACI,KAAK;IACjCD,UAAU,CAACE,KAAK,GAAG,MAAM;IACzBF,UAAU,CAACG,MAAM,GAAG,MAAM;IAC1BH,UAAU,CAACI,QAAQ,GAAG,QAAQ;IAC9BJ,UAAU,CAACK,SAAS,GAAG,KAAK;IAC5B,MAAMC,QAAQ,GAAGR,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9C,MAAMQ,UAAU,GAAGD,QAAQ,CAACL,KAAK;IACjCM,UAAU,CAACL,KAAK,GAAG,OAAO;IAC1BK,UAAU,CAACJ,MAAM,GAAG,OAAO;IAC3BN,QAAQ,CAACW,WAAW,CAACF,QAAQ,CAAC;IAC9BR,QAAQ,CAACW,IAAI,CAACD,WAAW,CAACX,QAAQ,CAAC;IACnC,IAAIA,QAAQ,CAACa,UAAU,GAAG,CAAC,EAAE;MAC3BhB,eAAe,GAAGiB,mBAAmB;IAC3C,CAAK,MAAM;MACLd,QAAQ,CAACa,UAAU,GAAG,CAAC;MACvB,IAAIb,QAAQ,CAACa,UAAU,KAAK,CAAC,EAAE;QAC7BhB,eAAe,GAAGkB,cAAc;MACxC,CAAO,MAAM;QACLlB,eAAe,GAAGmB,kBAAkB;MAC5C;IACA;IACIf,QAAQ,CAACW,IAAI,CAACK,WAAW,CAACjB,QAAQ,CAAC;IACnC,OAAOH,eAAe;EAC1B;EACE,OAAOA,eAAe;AACxB;AACO,SAASqB,gBAAgBA,CAAC;EAAEC,IAAI;EAAEC,IAAI;EAAEC;AAAG,CAAE,EAAEC,MAAM,EAAE;EAC5D,MAAMlB,KAAK,GAAG,EAAE;EAChB,MAAMmB,SAAS,GAAG,YAAYF,GAAG,CAACG,IAAI,IAAIL,IAAI,KAAK;EACnDf,KAAK,CAACiB,GAAG,CAACD,IAAI,CAAC,GAAGA,IAAI;EACtBhB,KAAK,CAACqB,SAAS,GAAGF,SAAS;EAC3B,IAAID,MAAM,KAAK,YAAY,EAAE;IAC3BlB,KAAK,CAACE,MAAM,GAAG,MAAM;EACzB,CAAG,MAAM;IACLF,KAAK,CAACC,KAAK,GAAG,MAAM;EACxB;EACE,OAAOD,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}