{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst thumbProps = buildProps({\n  vertical: Boolean,\n  size: String,\n  move: Number,\n  ratio: {\n    type: Number,\n    required: true\n  },\n  always: Boolean\n});\nexport { thumbProps };", "map": {"version": 3, "names": ["thumbProps", "buildProps", "vertical", "Boolean", "size", "String", "move", "Number", "ratio", "type", "required", "always"], "sources": ["../../../../../../packages/components/scrollbar/src/thumb.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Thumb from './thumb.vue'\n\nexport const thumbProps = buildProps({\n  vertical: Boolean,\n  size: String,\n  move: Number,\n  ratio: {\n    type: Number,\n    required: true,\n  },\n  always: Boolean,\n} as const)\nexport type ThumbProps = ExtractPropTypes<typeof thumbProps>\n\nexport type ThumbInstance = InstanceType<typeof Thumb> & unknown\n"], "mappings": ";AACY,MAACA,UAAU,GAAGC,UAAU,CAAC;EACnCC,QAAQ,EAAEC,OAAO;EACjBC,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAEC,MAAM;EACZC,KAAK,EAAE;IACLC,IAAI,EAAEF,MAAM;IACZG,QAAQ,EAAE;EACd,CAAG;EACDC,MAAM,EAAER;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}