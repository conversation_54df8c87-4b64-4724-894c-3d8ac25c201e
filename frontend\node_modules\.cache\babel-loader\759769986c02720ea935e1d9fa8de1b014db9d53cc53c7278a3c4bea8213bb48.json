{"ast": null, "code": "import { pick } from 'lodash-unified';\nimport { buildProps } from '../../utils/vue/props/runtime.mjs';\nconst ariaProps = buildProps({\n  ariaLabel: String,\n  ariaOrientation: {\n    type: String,\n    values: [\"horizontal\", \"vertical\", \"undefined\"]\n  },\n  ariaControls: String\n});\nconst useAriaProps = arias => {\n  return pick(ariaProps, arias);\n};\nexport { ariaProps, useAriaProps };", "map": {"version": 3, "names": ["ariaProps", "buildProps", "aria<PERSON><PERSON><PERSON>", "String", "ariaOrientation", "type", "values", "ariaControls", "useAriaProps", "arias", "pick"], "sources": ["../../../../../packages/hooks/use-aria/index.ts"], "sourcesContent": ["import { pick } from 'lodash-unified'\nimport { buildProps } from '@element-plus/utils'\n\nexport const ariaProps = buildProps({\n  /**\n   * @description native `aria-label` attribute\n   */\n  ariaLabel: String,\n  /**\n   * @description native `aria-orientation` attribute\n   */\n  ariaOrientation: {\n    type: String,\n    values: ['horizontal', 'vertical', 'undefined'],\n  },\n  /**\n   * @description native `aria-controls` attribute\n   */\n  ariaControls: String,\n})\n\nexport const useAriaProps = <T extends keyof typeof ariaProps>(\n  arias: Array<T>\n) => {\n  return pick<typeof ariaProps, T>(ariaProps, arias)\n}\n"], "mappings": ";;AAEY,MAACA,SAAS,GAAGC,UAAU,CAAC;EAClCC,SAAS,EAAEC,MAAM;EACjBC,eAAe,EAAE;IACfC,IAAI,EAAEF,MAAM;IACZG,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,WAAW;EAClD,CAAG;EACDC,YAAY,EAAEJ;AAChB,CAAC;AACW,MAACK,YAAY,GAAIC,KAAK,IAAK;EACrC,OAAOC,IAAI,CAACV,SAAS,EAAES,KAAK,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}