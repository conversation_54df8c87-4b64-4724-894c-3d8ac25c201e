{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { getCurrentInstance, shallowRef, ref, computed, unref } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { FixedDir } from '../constants.mjs';\nimport { isNumber } from '../../../../utils/types.mjs';\nconst useRow = (props, {\n  mainTableRef,\n  leftTableRef,\n  rightTableRef,\n  tableInstance,\n  ns,\n  isScrolling\n}) => {\n  const vm = getCurrentInstance();\n  const {\n    emit\n  } = vm;\n  const isResetting = shallowRef(false);\n  const expandedRowKeys = ref(props.defaultExpandedRowKeys || []);\n  const lastRenderedRowIndex = ref(-1);\n  const resetIndex = shallowRef(null);\n  const rowHeights = ref({});\n  const pendingRowHeights = ref({});\n  const leftTableHeights = shallowRef({});\n  const mainTableHeights = shallowRef({});\n  const rightTableHeights = shallowRef({});\n  const isDynamic = computed(() => isNumber(props.estimatedRowHeight));\n  function onRowsRendered(params) {\n    var _a;\n    (_a = props.onRowsRendered) == null ? void 0 : _a.call(props, params);\n    if (params.rowCacheEnd > unref(lastRenderedRowIndex)) {\n      lastRenderedRowIndex.value = params.rowCacheEnd;\n    }\n  }\n  function onRowHovered({\n    hovered,\n    rowKey\n  }) {\n    if (isScrolling.value) {\n      return;\n    }\n    const tableRoot = tableInstance.vnode.el;\n    const rows = tableRoot.querySelectorAll(`[rowkey=\"${String(rowKey)}\"]`);\n    rows.forEach(row => {\n      if (hovered) {\n        row.classList.add(ns.is(\"hovered\"));\n      } else {\n        row.classList.remove(ns.is(\"hovered\"));\n      }\n    });\n  }\n  function onRowExpanded({\n    expanded,\n    rowData,\n    rowIndex,\n    rowKey\n  }) {\n    var _a, _b;\n    const _expandedRowKeys = [...unref(expandedRowKeys)];\n    const currentKeyIndex = _expandedRowKeys.indexOf(rowKey);\n    if (expanded) {\n      if (currentKeyIndex === -1) _expandedRowKeys.push(rowKey);\n    } else {\n      if (currentKeyIndex > -1) _expandedRowKeys.splice(currentKeyIndex, 1);\n    }\n    expandedRowKeys.value = _expandedRowKeys;\n    emit(\"update:expandedRowKeys\", _expandedRowKeys);\n    (_a = props.onRowExpand) == null ? void 0 : _a.call(props, {\n      expanded,\n      rowData,\n      rowIndex,\n      rowKey\n    });\n    (_b = props.onExpandedRowsChange) == null ? void 0 : _b.call(props, _expandedRowKeys);\n  }\n  const flushingRowHeights = debounce(() => {\n    var _a, _b, _c, _d;\n    isResetting.value = true;\n    rowHeights.value = {\n      ...unref(rowHeights),\n      ...unref(pendingRowHeights)\n    };\n    resetAfterIndex(unref(resetIndex), false);\n    pendingRowHeights.value = {};\n    resetIndex.value = null;\n    (_a = mainTableRef.value) == null ? void 0 : _a.forceUpdate();\n    (_b = leftTableRef.value) == null ? void 0 : _b.forceUpdate();\n    (_c = rightTableRef.value) == null ? void 0 : _c.forceUpdate();\n    (_d = vm.proxy) == null ? void 0 : _d.$forceUpdate();\n    isResetting.value = false;\n  }, 0);\n  function resetAfterIndex(index, forceUpdate = false) {\n    if (!unref(isDynamic)) return;\n    [mainTableRef, leftTableRef, rightTableRef].forEach(tableRef => {\n      const table = unref(tableRef);\n      if (table) table.resetAfterRowIndex(index, forceUpdate);\n    });\n  }\n  function resetHeights(rowKey, height, rowIdx) {\n    const resetIdx = unref(resetIndex);\n    if (resetIdx === null) {\n      resetIndex.value = rowIdx;\n    } else {\n      if (resetIdx > rowIdx) {\n        resetIndex.value = rowIdx;\n      }\n    }\n    pendingRowHeights.value[rowKey] = height;\n  }\n  function onRowHeightChange({\n    rowKey,\n    height,\n    rowIndex\n  }, fixedDir) {\n    if (!fixedDir) {\n      mainTableHeights.value[rowKey] = height;\n    } else {\n      if (fixedDir === FixedDir.RIGHT) {\n        rightTableHeights.value[rowKey] = height;\n      } else {\n        leftTableHeights.value[rowKey] = height;\n      }\n    }\n    const maximumHeight = Math.max(...[leftTableHeights, rightTableHeights, mainTableHeights].map(records => records.value[rowKey] || 0));\n    if (unref(rowHeights)[rowKey] !== maximumHeight) {\n      resetHeights(rowKey, maximumHeight, rowIndex);\n      flushingRowHeights();\n    }\n  }\n  return {\n    expandedRowKeys,\n    lastRenderedRowIndex,\n    isDynamic,\n    isResetting,\n    rowHeights,\n    resetAfterIndex,\n    onRowExpanded,\n    onRowHovered,\n    onRowsRendered,\n    onRowHeightChange\n  };\n};\nexport { useRow };", "map": {"version": 3, "names": ["useRow", "props", "mainTableRef", "leftTableRef", "rightTableRef", "tableInstance", "ns", "isScrolling", "vm", "getCurrentInstance", "emit", "isResetting", "shallowRef", "expandedRowKeys", "ref", "defaultExpandedRowKeys", "lastRenderedRowIndex", "resetIndex", "rowHeights", "pendingRowHeights", "leftTableHeights", "mainTableHeights", "rightTableHeights", "isDynamic", "computed", "isNumber", "estimatedRowHeight", "onRowsRendered", "params", "_a", "call", "rowCacheEnd", "unref", "value", "onRowHovered", "hovered", "<PERSON><PERSON><PERSON>", "tableRoot", "vnode", "el", "rows", "querySelectorAll", "String", "for<PERSON>ach", "row", "classList", "add", "is", "remove", "onRowExpanded", "expanded", "rowData", "rowIndex", "_b", "_expandedRowKeys", "currentKeyIndex", "indexOf", "push", "splice", "onRowExpand", "onExpandedRowsChange", "flushingRowHeights", "debounce", "_c", "_d", "resetAfterIndex", "forceUpdate", "proxy", "$forceUpdate", "index", "tableRef", "table", "resetAfterRowIndex", "resetHeights", "height", "rowIdx", "resetIdx", "onRowHeightChange", "fixedDir", "FixedDir", "RIGHT", "maximumHeight", "Math", "max", "map", "records"], "sources": ["../../../../../../../packages/components/table-v2/src/composables/use-row.ts"], "sourcesContent": ["import { computed, getCurrentInstance, ref, shallowRef, unref } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { isNumber } from '@element-plus/utils'\nimport { FixedDir } from '../constants'\n\nimport type { ComponentInternalInstance, Ref, ShallowRef } from 'vue'\nimport type { TableV2Props } from '../table'\nimport type {\n  RowExpandParams,\n  RowHeightChangedParams,\n  RowHoverParams,\n} from '../row'\nimport type { FixedDirection, KeyType } from '../types'\nimport type { onRowRenderedParams } from '../grid'\nimport type { TableGridInstance } from '../table-grid'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\n\ntype Heights = Record<KeyType, number>\ntype GridInstanceRef = Ref<TableGridInstance | undefined>\n\ntype UseRowProps = {\n  mainTableRef: GridInstanceRef\n  leftTableRef: GridInstanceRef\n  rightTableRef: GridInstanceRef\n  tableInstance: ComponentInternalInstance\n  ns: UseNamespaceReturn\n  isScrolling: ShallowRef<boolean>\n}\n\nexport const useRow = (\n  props: TableV2Props,\n  {\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n    tableInstance,\n    ns,\n    isScrolling,\n  }: UseRowProps\n) => {\n  const vm = getCurrentInstance()!\n  const { emit } = vm\n  const isResetting = shallowRef(false)\n  const expandedRowKeys = ref<KeyType[]>(props.defaultExpandedRowKeys || [])\n  const lastRenderedRowIndex = ref(-1)\n  const resetIndex = shallowRef<number | null>(null)\n  const rowHeights = ref<Heights>({})\n  const pendingRowHeights = ref<Heights>({})\n  const leftTableHeights = shallowRef<Heights>({})\n  const mainTableHeights = shallowRef<Heights>({})\n  const rightTableHeights = shallowRef<Heights>({})\n  const isDynamic = computed(() => isNumber(props.estimatedRowHeight))\n\n  function onRowsRendered(params: onRowRenderedParams) {\n    props.onRowsRendered?.(params)\n\n    if (params.rowCacheEnd > unref(lastRenderedRowIndex)) {\n      lastRenderedRowIndex.value = params.rowCacheEnd\n    }\n  }\n\n  function onRowHovered({ hovered, rowKey }: RowHoverParams) {\n    if (isScrolling.value) {\n      return\n    }\n    const tableRoot = tableInstance!.vnode.el as HTMLElement\n    const rows = tableRoot.querySelectorAll(`[rowkey=\"${String(rowKey)}\"]`)\n    rows.forEach((row) => {\n      if (hovered) {\n        row.classList.add(ns.is('hovered'))\n      } else {\n        row.classList.remove(ns.is('hovered'))\n      }\n    })\n  }\n\n  function onRowExpanded({\n    expanded,\n    rowData,\n    rowIndex,\n    rowKey,\n  }: RowExpandParams) {\n    const _expandedRowKeys = [...unref(expandedRowKeys)]\n    const currentKeyIndex = _expandedRowKeys.indexOf(rowKey)\n    if (expanded) {\n      if (currentKeyIndex === -1) _expandedRowKeys.push(rowKey)\n    } else {\n      if (currentKeyIndex > -1) _expandedRowKeys.splice(currentKeyIndex, 1)\n    }\n    expandedRowKeys.value = _expandedRowKeys\n\n    emit('update:expandedRowKeys', _expandedRowKeys)\n    props.onRowExpand?.({\n      expanded,\n      rowData,\n      rowIndex,\n      rowKey,\n    })\n    // If this is not controlled, then use this to notify changes\n    props.onExpandedRowsChange?.(_expandedRowKeys)\n  }\n\n  const flushingRowHeights = debounce(() => {\n    isResetting.value = true\n    rowHeights.value = { ...unref(rowHeights), ...unref(pendingRowHeights) }\n    resetAfterIndex(unref(resetIndex)!, false)\n    pendingRowHeights.value = {}\n    // force update\n    resetIndex.value = null\n    mainTableRef.value?.forceUpdate()\n    leftTableRef.value?.forceUpdate()\n    rightTableRef.value?.forceUpdate()\n    vm.proxy?.$forceUpdate()\n    isResetting.value = false\n  }, 0)\n\n  function resetAfterIndex(index: number, forceUpdate = false) {\n    if (!unref(isDynamic)) return\n    ;[mainTableRef, leftTableRef, rightTableRef].forEach((tableRef) => {\n      const table = unref(tableRef)\n      if (table) table.resetAfterRowIndex(index, forceUpdate)\n    })\n  }\n\n  function resetHeights(rowKey: KeyType, height: number, rowIdx: number) {\n    const resetIdx = unref(resetIndex)\n    if (resetIdx === null) {\n      resetIndex.value = rowIdx\n    } else {\n      if (resetIdx > rowIdx) {\n        resetIndex.value = rowIdx\n      }\n    }\n\n    pendingRowHeights.value[rowKey] = height\n  }\n\n  function onRowHeightChange(\n    { rowKey, height, rowIndex }: RowHeightChangedParams,\n    fixedDir: FixedDirection\n  ) {\n    if (!fixedDir) {\n      mainTableHeights.value[rowKey] = height\n    } else {\n      if (fixedDir === FixedDir.RIGHT) {\n        rightTableHeights.value[rowKey] = height\n      } else {\n        leftTableHeights.value[rowKey] = height\n      }\n    }\n\n    const maximumHeight = Math.max(\n      ...[leftTableHeights, rightTableHeights, mainTableHeights].map(\n        (records) => records.value[rowKey] || 0\n      )\n    )\n\n    if (unref(rowHeights)[rowKey] !== maximumHeight) {\n      resetHeights(rowKey, maximumHeight, rowIndex)\n      flushingRowHeights()\n    }\n  }\n\n  return {\n    expandedRowKeys,\n    lastRenderedRowIndex,\n    isDynamic,\n    isResetting,\n    rowHeights,\n\n    resetAfterIndex,\n    onRowExpanded,\n    onRowHovered,\n    onRowsRendered,\n    onRowHeightChange,\n  }\n}\n\nexport type UseRowReturn = ReturnType<typeof useRow>\n"], "mappings": ";;;;;;;AAIY,MAACA,MAAM,GAAGA,CAACC,KAAK,EAAE;EAC5BC,YAAY;EACZC,YAAY;EACZC,aAAa;EACbC,aAAa;EACbC,EAAE;EACFC;AACF,CAAC,KAAK;EACJ,MAAMC,EAAE,GAAGC,kBAAkB,EAAE;EAC/B,MAAM;IAAEC;EAAI,CAAE,GAAGF,EAAE;EACnB,MAAMG,WAAW,GAAGC,UAAU,CAAC,KAAK,CAAC;EACrC,MAAMC,eAAe,GAAGC,GAAG,CAACb,KAAK,CAACc,sBAAsB,IAAI,EAAE,CAAC;EAC/D,MAAMC,oBAAoB,GAAGF,GAAG,CAAC,CAAC,CAAC,CAAC;EACpC,MAAMG,UAAU,GAAGL,UAAU,CAAC,IAAI,CAAC;EACnC,MAAMM,UAAU,GAAGJ,GAAG,CAAC,EAAE,CAAC;EAC1B,MAAMK,iBAAiB,GAAGL,GAAG,CAAC,EAAE,CAAC;EACjC,MAAMM,gBAAgB,GAAGR,UAAU,CAAC,EAAE,CAAC;EACvC,MAAMS,gBAAgB,GAAGT,UAAU,CAAC,EAAE,CAAC;EACvC,MAAMU,iBAAiB,GAAGV,UAAU,CAAC,EAAE,CAAC;EACxC,MAAMW,SAAS,GAAGC,QAAQ,CAAC,MAAMC,QAAQ,CAACxB,KAAK,CAACyB,kBAAkB,CAAC,CAAC;EACpE,SAASC,cAAcA,CAACC,MAAM,EAAE;IAC9B,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG5B,KAAK,CAAC0B,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,EAAE,CAACC,IAAI,CAAC7B,KAAK,EAAE2B,MAAM,CAAC;IACrE,IAAIA,MAAM,CAACG,WAAW,GAAGC,KAAK,CAAChB,oBAAoB,CAAC,EAAE;MACpDA,oBAAoB,CAACiB,KAAK,GAAGL,MAAM,CAACG,WAAW;IACrD;EACA;EACE,SAASG,YAAYA,CAAC;IAAEC,OAAO;IAAEC;EAAM,CAAE,EAAE;IACzC,IAAI7B,WAAW,CAAC0B,KAAK,EAAE;MACrB;IACN;IACI,MAAMI,SAAS,GAAGhC,aAAa,CAACiC,KAAK,CAACC,EAAE;IACxC,MAAMC,IAAI,GAAGH,SAAS,CAACI,gBAAgB,CAAC,YAAYC,MAAM,CAACN,MAAM,CAAC,IAAI,CAAC;IACvEI,IAAI,CAACG,OAAO,CAAEC,GAAG,IAAK;MACpB,IAAIT,OAAO,EAAE;QACXS,GAAG,CAACC,SAAS,CAACC,GAAG,CAACxC,EAAE,CAACyC,EAAE,CAAC,SAAS,CAAC,CAAC;MAC3C,CAAO,MAAM;QACLH,GAAG,CAACC,SAAS,CAACG,MAAM,CAAC1C,EAAE,CAACyC,EAAE,CAAC,SAAS,CAAC,CAAC;MAC9C;IACA,CAAK,CAAC;EACN;EACE,SAASE,aAAaA,CAAC;IACrBC,QAAQ;IACRC,OAAO;IACPC,QAAQ;IACRhB;EACJ,CAAG,EAAE;IACD,IAAIP,EAAE,EAAEwB,EAAE;IACV,MAAMC,gBAAgB,GAAG,CAAC,GAAGtB,KAAK,CAACnB,eAAe,CAAC,CAAC;IACpD,MAAM0C,eAAe,GAAGD,gBAAgB,CAACE,OAAO,CAACpB,MAAM,CAAC;IACxD,IAAIc,QAAQ,EAAE;MACZ,IAAIK,eAAe,KAAK,CAAC,CAAC,EACxBD,gBAAgB,CAACG,IAAI,CAACrB,MAAM,CAAC;IACrC,CAAK,MAAM;MACL,IAAImB,eAAe,GAAG,CAAC,CAAC,EACtBD,gBAAgB,CAACI,MAAM,CAACH,eAAe,EAAE,CAAC,CAAC;IACnD;IACI1C,eAAe,CAACoB,KAAK,GAAGqB,gBAAgB;IACxC5C,IAAI,CAAC,wBAAwB,EAAE4C,gBAAgB,CAAC;IAChD,CAACzB,EAAE,GAAG5B,KAAK,CAAC0D,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG9B,EAAE,CAACC,IAAI,CAAC7B,KAAK,EAAE;MACzDiD,QAAQ;MACRC,OAAO;MACPC,QAAQ;MACRhB;IACN,CAAK,CAAC;IACF,CAACiB,EAAE,GAAGpD,KAAK,CAAC2D,oBAAoB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACvB,IAAI,CAAC7B,KAAK,EAAEqD,gBAAgB,CAAC;EACzF;EACE,MAAMO,kBAAkB,GAAGC,QAAQ,CAAC,MAAM;IACxC,IAAIjC,EAAE,EAAEwB,EAAE,EAAEU,EAAE,EAAEC,EAAE;IAClBrD,WAAW,CAACsB,KAAK,GAAG,IAAI;IACxBf,UAAU,CAACe,KAAK,GAAG;MAAE,GAAGD,KAAK,CAACd,UAAU,CAAC;MAAE,GAAGc,KAAK,CAACb,iBAAiB;IAAC,CAAE;IACxE8C,eAAe,CAACjC,KAAK,CAACf,UAAU,CAAC,EAAE,KAAK,CAAC;IACzCE,iBAAiB,CAACc,KAAK,GAAG,EAAE;IAC5BhB,UAAU,CAACgB,KAAK,GAAG,IAAI;IACvB,CAACJ,EAAE,GAAG3B,YAAY,CAAC+B,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,EAAE,CAACqC,WAAW,EAAE;IAC7D,CAACb,EAAE,GAAGlD,YAAY,CAAC8B,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoB,EAAE,CAACa,WAAW,EAAE;IAC7D,CAACH,EAAE,GAAG3D,aAAa,CAAC6B,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8B,EAAE,CAACG,WAAW,EAAE;IAC9D,CAACF,EAAE,GAAGxD,EAAE,CAAC2D,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,EAAE,CAACI,YAAY,EAAE;IACpDzD,WAAW,CAACsB,KAAK,GAAG,KAAK;EAC7B,CAAG,EAAE,CAAC,CAAC;EACL,SAASgC,eAAeA,CAACI,KAAK,EAAEH,WAAW,GAAG,KAAK,EAAE;IACnD,IAAI,CAAClC,KAAK,CAACT,SAAS,CAAC,EACnB;IACF,CAACrB,YAAY,EAAEC,YAAY,EAAEC,aAAa,CAAC,CAACuC,OAAO,CAAE2B,QAAQ,IAAK;MAChE,MAAMC,KAAK,GAAGvC,KAAK,CAACsC,QAAQ,CAAC;MAC7B,IAAIC,KAAK,EACPA,KAAK,CAACC,kBAAkB,CAACH,KAAK,EAAEH,WAAW,CAAC;IACpD,CAAK,CAAC;EACN;EACE,SAASO,YAAYA,CAACrC,MAAM,EAAEsC,MAAM,EAAEC,MAAM,EAAE;IAC5C,MAAMC,QAAQ,GAAG5C,KAAK,CAACf,UAAU,CAAC;IAClC,IAAI2D,QAAQ,KAAK,IAAI,EAAE;MACrB3D,UAAU,CAACgB,KAAK,GAAG0C,MAAM;IAC/B,CAAK,MAAM;MACL,IAAIC,QAAQ,GAAGD,MAAM,EAAE;QACrB1D,UAAU,CAACgB,KAAK,GAAG0C,MAAM;MACjC;IACA;IACIxD,iBAAiB,CAACc,KAAK,CAACG,MAAM,CAAC,GAAGsC,MAAM;EAC5C;EACE,SAASG,iBAAiBA,CAAC;IAAEzC,MAAM;IAAEsC,MAAM;IAAEtB;EAAQ,CAAE,EAAE0B,QAAQ,EAAE;IACjE,IAAI,CAACA,QAAQ,EAAE;MACbzD,gBAAgB,CAACY,KAAK,CAACG,MAAM,CAAC,GAAGsC,MAAM;IAC7C,CAAK,MAAM;MACL,IAAII,QAAQ,KAAKC,QAAQ,CAACC,KAAK,EAAE;QAC/B1D,iBAAiB,CAACW,KAAK,CAACG,MAAM,CAAC,GAAGsC,MAAM;MAChD,CAAO,MAAM;QACLtD,gBAAgB,CAACa,KAAK,CAACG,MAAM,CAAC,GAAGsC,MAAM;MAC/C;IACA;IACI,MAAMO,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,CAAC/D,gBAAgB,EAAEE,iBAAiB,EAAED,gBAAgB,CAAC,CAAC+D,GAAG,CAAEC,OAAO,IAAKA,OAAO,CAACpD,KAAK,CAACG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACvI,IAAIJ,KAAK,CAACd,UAAU,CAAC,CAACkB,MAAM,CAAC,KAAK6C,aAAa,EAAE;MAC/CR,YAAY,CAACrC,MAAM,EAAE6C,aAAa,EAAE7B,QAAQ,CAAC;MAC7CS,kBAAkB,EAAE;IAC1B;EACA;EACE,OAAO;IACLhD,eAAe;IACfG,oBAAoB;IACpBO,SAAS;IACTZ,WAAW;IACXO,UAAU;IACV+C,eAAe;IACfhB,aAAa;IACbf,YAAY;IACZP,cAAc;IACdkD;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}