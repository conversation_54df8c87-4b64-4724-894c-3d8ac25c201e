{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, with<PERSON><PERSON><PERSON>, withModifiers, renderSlot, createTextVNode, toDisplayString, createVNode, withCtx, createBlock, resolveDynamicComponent, withDirectives, vShow } from 'vue';\nimport { ElCollapseTransition } from '../../collapse-transition/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { collapseItemProps } from './collapse-item.mjs';\nimport { useCollapseItem, useCollapseItemDOM } from './use-collapse-item.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElCollapseItem\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: collapseItemProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const {\n      focusing,\n      id,\n      isActive,\n      handleFocus,\n      handleHeaderClick,\n      handleEnterClick\n    } = useCollapseItem(props);\n    const {\n      arrowKls,\n      headKls,\n      rootKls,\n      itemTitleKls,\n      itemWrapperKls,\n      itemContentKls,\n      scopedContentId,\n      scopedHeadId\n    } = useCollapseItemDOM(props, {\n      focusing,\n      isActive,\n      id\n    });\n    expose({\n      isActive\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(rootKls))\n      }, [createElementVNode(\"button\", {\n        id: unref(scopedHeadId),\n        class: normalizeClass(unref(headKls)),\n        \"aria-expanded\": unref(isActive),\n        \"aria-controls\": unref(scopedContentId),\n        \"aria-describedby\": unref(scopedContentId),\n        tabindex: _ctx.disabled ? -1 : 0,\n        type: \"button\",\n        onClick: unref(handleHeaderClick),\n        onKeydown: withKeys(withModifiers(unref(handleEnterClick), [\"stop\", \"prevent\"]), [\"space\", \"enter\"]),\n        onFocus: unref(handleFocus),\n        onBlur: $event => focusing.value = false\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass(unref(itemTitleKls))\n      }, [renderSlot(_ctx.$slots, \"title\", {\n        isActive: unref(isActive)\n      }, () => [createTextVNode(toDisplayString(_ctx.title), 1)])], 2), renderSlot(_ctx.$slots, \"icon\", {\n        isActive: unref(isActive)\n      }, () => [createVNode(unref(ElIcon), {\n        class: normalizeClass(unref(arrowKls))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))]),\n        _: 1\n      }, 8, [\"class\"])])], 42, [\"id\", \"aria-expanded\", \"aria-controls\", \"aria-describedby\", \"tabindex\", \"onClick\", \"onKeydown\", \"onFocus\", \"onBlur\"]), createVNode(unref(ElCollapseTransition), null, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"div\", {\n          id: unref(scopedContentId),\n          role: \"region\",\n          class: normalizeClass(unref(itemWrapperKls)),\n          \"aria-hidden\": !unref(isActive),\n          \"aria-labelledby\": unref(scopedHeadId)\n        }, [createElementVNode(\"div\", {\n          class: normalizeClass(unref(itemContentKls))\n        }, [renderSlot(_ctx.$slots, \"default\")], 2)], 10, [\"id\", \"aria-hidden\", \"aria-labelledby\"]), [[vShow, unref(isActive)]])]),\n        _: 3\n      })], 2);\n    };\n  }\n});\nvar CollapseItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"collapse-item.vue\"]]);\nexport { CollapseItem as default };", "map": {"version": 3, "names": ["name", "focusing", "id", "isActive", "handleFocus", "handleHeaderClick", "handleEnterClick", "useCollapseItem", "props", "arrowKls", "headKls", "rootKls", "itemTitleKls", "itemWrapperKls", "itemContentKls", "scopedContentId", "scopedHeadId", "useCollapseItemDOM", "expose", "_ctx", "_cache"], "sources": ["../../../../../../packages/components/collapse/src/collapse-item.vue"], "sourcesContent": ["<template>\n  <div :class=\"rootKls\">\n    <button\n      :id=\"scopedHeadId\"\n      :class=\"headKls\"\n      :aria-expanded=\"isActive\"\n      :aria-controls=\"scopedContentId\"\n      :aria-describedby=\"scopedContentId\"\n      :tabindex=\"disabled ? -1 : 0\"\n      type=\"button\"\n      @click=\"handleHeaderClick\"\n      @keydown.space.enter.stop.prevent=\"handleEnterClick\"\n      @focus=\"handleFocus\"\n      @blur=\"focusing = false\"\n    >\n      <span :class=\"itemTitleKls\">\n        <slot name=\"title\" :is-active=\"isActive\">{{ title }}</slot>\n      </span>\n      <slot name=\"icon\" :is-active=\"isActive\">\n        <el-icon :class=\"arrowKls\">\n          <component :is=\"icon\" />\n        </el-icon>\n      </slot>\n    </button>\n\n    <el-collapse-transition>\n      <div\n        v-show=\"isActive\"\n        :id=\"scopedContentId\"\n        role=\"region\"\n        :class=\"itemWrapperKls\"\n        :aria-hidden=\"!isActive\"\n        :aria-labelledby=\"scopedHeadId\"\n      >\n        <div :class=\"itemContentKls\">\n          <slot />\n        </div>\n      </div>\n    </el-collapse-transition>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport ElCollapseTransition from '@element-plus/components/collapse-transition'\nimport ElIcon from '@element-plus/components/icon'\nimport { collapseItemProps } from './collapse-item'\nimport { useCollapseItem, useCollapseItemDOM } from './use-collapse-item'\n\ndefineOptions({\n  name: 'ElCollapseItem',\n})\n\nconst props = defineProps(collapseItemProps)\nconst {\n  focusing,\n  id,\n  isActive,\n  handleFocus,\n  handleHeaderClick,\n  handleEnterClick,\n} = useCollapseItem(props)\n\nconst {\n  arrowKls,\n  headKls,\n  rootKls,\n  itemTitleKls,\n  itemWrapperKls,\n  itemContentKls,\n  scopedContentId,\n  scopedHeadId,\n} = useCollapseItemDOM(props, { focusing, isActive, id })\n\ndefineExpose({\n  /** @description current collapse-item whether active */\n  isActive,\n})\n</script>\n"], "mappings": ";;;;;;mCAgDc;EACZA,IAAM;AACR;;;;;;;;IAGM;MACJC,QAAA;MACAC,EAAA;MACAC,QAAA;MACAC,WAAA;MACAC,iBAAA;MACAC;IAAA,CACF,GAAIC,eAAA,CAAgBC,KAAK;IAEnB;MACJC,QAAA;MACAC,OAAA;MACAC,OAAA;MACAC,YAAA;MACAC,cAAA;MACAC,cAAA;MACAC,eAAA;MACAC;IAAA,IACEC,kBAAmB,CAAAT,KAAA,EAAO;MAAEP,QAAU;MAAAE,QAAA;MAAUD;IAAA,CAAI;IAE3CgB,MAAA;MAAAf;IAAA,CAEX;IACF,OAAC,CAAAgB,IAAA,EAAAC,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}