{"ast": null, "code": "import Anchor from './src/anchor2.mjs';\nimport AnchorLink from './src/anchor-link2.mjs';\nexport { anchorEmits, anchorProps } from './src/anchor.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElAnchor = withInstall(Anchor, {\n  AnchorLink\n});\nconst ElAnchorLink = withNoopInstall(AnchorLink);\nexport { ElAnchor, ElAnchorLink, ElAnchor as default };", "map": {"version": 3, "names": ["ElAnchor", "withInstall", "<PERSON><PERSON>", "AnchorLink", "ElAnchorLink", "withNoopInstall"], "sources": ["../../../../../packages/components/anchor/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON>oopInstall } from '@element-plus/utils'\nimport Anchor from './src/anchor.vue'\nimport AnchorLink from './src/anchor-link.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElAnchor: SFCWithInstall<typeof Anchor> & {\n  AnchorLink: typeof AnchorLink\n} = withInstall(Anchor, {\n  AnchorLink,\n})\nexport const ElAnchorLink: SFCWithInstall<typeof AnchorLink> =\n  withNoopInstall(AnchorLink)\nexport default ElAnchor\n\nexport * from './src/anchor'\n"], "mappings": ";;;;AAGY,MAACA,QAAQ,GAAGC,WAAW,CAACC,MAAM,EAAE;EAC1CC;AACF,CAAC;AACW,MAACC,YAAY,GAAGC,eAAe,CAACF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}