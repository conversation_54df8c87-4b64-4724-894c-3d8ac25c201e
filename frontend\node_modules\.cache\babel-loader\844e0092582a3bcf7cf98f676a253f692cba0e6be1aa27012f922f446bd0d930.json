{"ast": null, "code": "const carouselContextKey = Symbol(\"carouselContextKey\");\nconst CAROUSEL_ITEM_NAME = \"ElCarouselItem\";\nexport { CAROUSEL_ITEM_NAME, carouselContextKey };", "map": {"version": 3, "names": ["carouselContextKey", "Symbol", "CAROUSEL_ITEM_NAME"], "sources": ["../../../../../../packages/components/carousel/src/constants.ts"], "sourcesContent": ["import type { InjectionKey, Ref } from 'vue'\n\nimport type { CarouselItemProps } from './carousel-item'\n\nexport type CarouselItemStates = {\n  hover: boolean\n  translate: number\n  scale: number\n  active: boolean\n  ready: boolean\n  inStage: boolean\n  animating: boolean\n}\n\nexport type CarouselItemContext = {\n  props: CarouselItemProps\n  states: CarouselItemStates\n  uid: number\n  translateItem: (index: number, activeIndex: number, oldIndex?: number) => void\n}\n\nexport type CarouselContext = {\n  root: Ref<HTMLElement | undefined>\n  items: Ref<CarouselItemContext[]>\n  isCardType: Ref<boolean>\n  isVertical: Ref<boolean>\n  loop: boolean\n  cardScale: number\n  addItem: (item: CarouselItemContext) => void\n  removeItem: (uid: number) => void\n  setActiveItem: (index: number) => void\n  setContainerHeight: (height: number) => void\n}\n\nexport const carouselContextKey: InjectionKey<CarouselContext> =\n  Symbol('carouselContextKey')\n\nexport const CAROUSEL_ITEM_NAME = 'ElCarouselItem'\n"], "mappings": "AAAY,MAACA,kBAAkB,GAAGC,MAAM,CAAC,oBAAoB;AACjD,MAACC,kBAAkB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}