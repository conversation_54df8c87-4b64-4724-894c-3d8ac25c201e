{"ast": null, "code": "import { defineComponent, inject, ref, getCurrentInstance, provide, watch, nextTick, resolveComponent, withDirectives, openBlock, createElementBlock, normalizeClass, withModifiers, createElementVNode, normalizeStyle, createBlock, withCtx, resolveDynamicComponent, createCommentVNode, createVNode, Fragment, renderList, vShow } from 'vue';\nimport { ElCollapseTransition } from '../../collapse-transition/index.mjs';\nimport { ElCheckbox } from '../../checkbox/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { Loading, CaretRight } from '@element-plus/icons-vue';\nimport NodeContent from './tree-node-content.mjs';\nimport { getNodeKey, handleCurrentChange } from './model/util.mjs';\nimport { useNodeExpandEventBroadcast } from './model/useNodeExpandEventBroadcast.mjs';\nimport { dragEventsKey } from './model/useDragNode.mjs';\nimport Node from './model/node.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { isFunction, isString } from '@vue/shared';\nconst _sfc_main = defineComponent({\n  name: \"ElTreeNode\",\n  components: {\n    ElCollapseTransition,\n    ElCheckbox,\n    NodeContent,\n    ElIcon,\n    Loading\n  },\n  props: {\n    node: {\n      type: Node,\n      default: () => ({})\n    },\n    props: {\n      type: Object,\n      default: () => ({})\n    },\n    accordion: Boolean,\n    renderContent: Function,\n    renderAfterExpand: Boolean,\n    showCheckbox: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: [\"node-expand\"],\n  setup(props, ctx) {\n    const ns = useNamespace(\"tree\");\n    const {\n      broadcastExpanded\n    } = useNodeExpandEventBroadcast(props);\n    const tree = inject(\"RootTree\");\n    const expanded = ref(false);\n    const childNodeRendered = ref(false);\n    const oldChecked = ref();\n    const oldIndeterminate = ref();\n    const node$ = ref();\n    const dragEvents = inject(dragEventsKey);\n    const instance = getCurrentInstance();\n    provide(\"NodeInstance\", instance);\n    if (!tree) {\n      debugWarn(\"Tree\", \"Can not find node's tree.\");\n    }\n    if (props.node.expanded) {\n      expanded.value = true;\n      childNodeRendered.value = true;\n    }\n    const childrenKey = tree.props.props[\"children\"] || \"children\";\n    watch(() => {\n      var _a;\n      const children = (_a = props.node.data) == null ? void 0 : _a[childrenKey];\n      return children && [...children];\n    }, () => {\n      props.node.updateChildren();\n    });\n    watch(() => props.node.indeterminate, val => {\n      handleSelectChange(props.node.checked, val);\n    });\n    watch(() => props.node.checked, val => {\n      handleSelectChange(val, props.node.indeterminate);\n    });\n    watch(() => props.node.childNodes.length, () => props.node.reInitChecked());\n    watch(() => props.node.expanded, val => {\n      nextTick(() => expanded.value = val);\n      if (val) {\n        childNodeRendered.value = true;\n      }\n    });\n    const getNodeKey$1 = node => {\n      return getNodeKey(tree.props.nodeKey, node.data);\n    };\n    const getNodeClass = node => {\n      const nodeClassFunc = props.props.class;\n      if (!nodeClassFunc) {\n        return {};\n      }\n      let className;\n      if (isFunction(nodeClassFunc)) {\n        const {\n          data\n        } = node;\n        className = nodeClassFunc(data, node);\n      } else {\n        className = nodeClassFunc;\n      }\n      if (isString(className)) {\n        return {\n          [className]: true\n        };\n      } else {\n        return className;\n      }\n    };\n    const handleSelectChange = (checked, indeterminate) => {\n      if (oldChecked.value !== checked || oldIndeterminate.value !== indeterminate) {\n        tree.ctx.emit(\"check-change\", props.node.data, checked, indeterminate);\n      }\n      oldChecked.value = checked;\n      oldIndeterminate.value = indeterminate;\n    };\n    const handleClick = e => {\n      handleCurrentChange(tree.store, tree.ctx.emit, () => {\n        var _a;\n        const nodeKeyProp = (_a = tree == null ? void 0 : tree.props) == null ? void 0 : _a.nodeKey;\n        if (nodeKeyProp) {\n          const curNodeKey = getNodeKey$1(props.node);\n          tree.store.value.setCurrentNodeKey(curNodeKey);\n        } else {\n          tree.store.value.setCurrentNode(props.node);\n        }\n      });\n      tree.currentNode.value = props.node;\n      if (tree.props.expandOnClickNode) {\n        handleExpandIconClick();\n      }\n      if ((tree.props.checkOnClickNode || props.node.isLeaf && tree.props.checkOnClickLeaf && props.showCheckbox) && !props.node.disabled) {\n        handleCheckChange(!props.node.checked);\n      }\n      tree.ctx.emit(\"node-click\", props.node.data, props.node, instance, e);\n    };\n    const handleContextMenu = event => {\n      var _a;\n      if ((_a = tree.instance.vnode.props) == null ? void 0 : _a[\"onNodeContextmenu\"]) {\n        event.stopPropagation();\n        event.preventDefault();\n      }\n      tree.ctx.emit(\"node-contextmenu\", event, props.node.data, props.node, instance);\n    };\n    const handleExpandIconClick = () => {\n      if (props.node.isLeaf) return;\n      if (expanded.value) {\n        tree.ctx.emit(\"node-collapse\", props.node.data, props.node, instance);\n        props.node.collapse();\n      } else {\n        props.node.expand(() => {\n          ctx.emit(\"node-expand\", props.node.data, props.node, instance);\n        });\n      }\n    };\n    const handleCheckChange = value => {\n      props.node.setChecked(value, !(tree == null ? void 0 : tree.props.checkStrictly));\n      nextTick(() => {\n        const store = tree.store.value;\n        tree.ctx.emit(\"check\", props.node.data, {\n          checkedNodes: store.getCheckedNodes(),\n          checkedKeys: store.getCheckedKeys(),\n          halfCheckedNodes: store.getHalfCheckedNodes(),\n          halfCheckedKeys: store.getHalfCheckedKeys()\n        });\n      });\n    };\n    const handleChildNodeExpand = (nodeData, node, instance2) => {\n      broadcastExpanded(node);\n      tree.ctx.emit(\"node-expand\", nodeData, node, instance2);\n    };\n    const handleDragStart = event => {\n      if (!tree.props.draggable) return;\n      dragEvents.treeNodeDragStart({\n        event,\n        treeNode: props\n      });\n    };\n    const handleDragOver = event => {\n      event.preventDefault();\n      if (!tree.props.draggable) return;\n      dragEvents.treeNodeDragOver({\n        event,\n        treeNode: {\n          $el: node$.value,\n          node: props.node\n        }\n      });\n    };\n    const handleDrop = event => {\n      event.preventDefault();\n    };\n    const handleDragEnd = event => {\n      if (!tree.props.draggable) return;\n      dragEvents.treeNodeDragEnd(event);\n    };\n    return {\n      ns,\n      node$,\n      tree,\n      expanded,\n      childNodeRendered,\n      oldChecked,\n      oldIndeterminate,\n      getNodeKey: getNodeKey$1,\n      getNodeClass,\n      handleSelectChange,\n      handleClick,\n      handleContextMenu,\n      handleExpandIconClick,\n      handleCheckChange,\n      handleChildNodeExpand,\n      handleDragStart,\n      handleDragOver,\n      handleDrop,\n      handleDragEnd,\n      CaretRight\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = resolveComponent(\"el-icon\");\n  const _component_el_checkbox = resolveComponent(\"el-checkbox\");\n  const _component_loading = resolveComponent(\"loading\");\n  const _component_node_content = resolveComponent(\"node-content\");\n  const _component_el_tree_node = resolveComponent(\"el-tree-node\");\n  const _component_el_collapse_transition = resolveComponent(\"el-collapse-transition\");\n  return withDirectives((openBlock(), createElementBlock(\"div\", {\n    ref: \"node$\",\n    class: normalizeClass([_ctx.ns.b(\"node\"), _ctx.ns.is(\"expanded\", _ctx.expanded), _ctx.ns.is(\"current\", _ctx.node.isCurrent), _ctx.ns.is(\"hidden\", !_ctx.node.visible), _ctx.ns.is(\"focusable\", !_ctx.node.disabled), _ctx.ns.is(\"checked\", !_ctx.node.disabled && _ctx.node.checked), _ctx.getNodeClass(_ctx.node)]),\n    role: \"treeitem\",\n    tabindex: \"-1\",\n    \"aria-expanded\": _ctx.expanded,\n    \"aria-disabled\": _ctx.node.disabled,\n    \"aria-checked\": _ctx.node.checked,\n    draggable: _ctx.tree.props.draggable,\n    \"data-key\": _ctx.getNodeKey(_ctx.node),\n    onClick: withModifiers(_ctx.handleClick, [\"stop\"]),\n    onContextmenu: _ctx.handleContextMenu,\n    onDragstart: withModifiers(_ctx.handleDragStart, [\"stop\"]),\n    onDragover: withModifiers(_ctx.handleDragOver, [\"stop\"]),\n    onDragend: withModifiers(_ctx.handleDragEnd, [\"stop\"]),\n    onDrop: withModifiers(_ctx.handleDrop, [\"stop\"])\n  }, [createElementVNode(\"div\", {\n    class: normalizeClass(_ctx.ns.be(\"node\", \"content\")),\n    style: normalizeStyle({\n      paddingLeft: (_ctx.node.level - 1) * _ctx.tree.props.indent + \"px\"\n    })\n  }, [_ctx.tree.props.icon || _ctx.CaretRight ? (openBlock(), createBlock(_component_el_icon, {\n    key: 0,\n    class: normalizeClass([_ctx.ns.be(\"node\", \"expand-icon\"), _ctx.ns.is(\"leaf\", _ctx.node.isLeaf), {\n      expanded: !_ctx.node.isLeaf && _ctx.expanded\n    }]),\n    onClick: withModifiers(_ctx.handleExpandIconClick, [\"stop\"])\n  }, {\n    default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.tree.props.icon || _ctx.CaretRight)))]),\n    _: 1\n  }, 8, [\"class\", \"onClick\"])) : createCommentVNode(\"v-if\", true), _ctx.showCheckbox ? (openBlock(), createBlock(_component_el_checkbox, {\n    key: 1,\n    \"model-value\": _ctx.node.checked,\n    indeterminate: _ctx.node.indeterminate,\n    disabled: !!_ctx.node.disabled,\n    onClick: withModifiers(() => {}, [\"stop\"]),\n    onChange: _ctx.handleCheckChange\n  }, null, 8, [\"model-value\", \"indeterminate\", \"disabled\", \"onClick\", \"onChange\"])) : createCommentVNode(\"v-if\", true), _ctx.node.loading ? (openBlock(), createBlock(_component_el_icon, {\n    key: 2,\n    class: normalizeClass([_ctx.ns.be(\"node\", \"loading-icon\"), _ctx.ns.is(\"loading\")])\n  }, {\n    default: withCtx(() => [createVNode(_component_loading)]),\n    _: 1\n  }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true), createVNode(_component_node_content, {\n    node: _ctx.node,\n    \"render-content\": _ctx.renderContent\n  }, null, 8, [\"node\", \"render-content\"])], 6), createVNode(_component_el_collapse_transition, null, {\n    default: withCtx(() => [!_ctx.renderAfterExpand || _ctx.childNodeRendered ? withDirectives((openBlock(), createElementBlock(\"div\", {\n      key: 0,\n      class: normalizeClass(_ctx.ns.be(\"node\", \"children\")),\n      role: \"group\",\n      \"aria-expanded\": _ctx.expanded,\n      onClick: withModifiers(() => {}, [\"stop\"])\n    }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.node.childNodes, child => {\n      return openBlock(), createBlock(_component_el_tree_node, {\n        key: _ctx.getNodeKey(child),\n        \"render-content\": _ctx.renderContent,\n        \"render-after-expand\": _ctx.renderAfterExpand,\n        \"show-checkbox\": _ctx.showCheckbox,\n        node: child,\n        accordion: _ctx.accordion,\n        props: _ctx.props,\n        onNodeExpand: _ctx.handleChildNodeExpand\n      }, null, 8, [\"render-content\", \"render-after-expand\", \"show-checkbox\", \"node\", \"accordion\", \"props\", \"onNodeExpand\"]);\n    }), 128))], 10, [\"aria-expanded\", \"onClick\"])), [[vShow, _ctx.expanded]]) : createCommentVNode(\"v-if\", true)]),\n    _: 1\n  })], 42, [\"aria-expanded\", \"aria-disabled\", \"aria-checked\", \"draggable\", \"data-key\", \"onClick\", \"onContextmenu\", \"onDragstart\", \"onDragover\", \"onDragend\", \"onDrop\"])), [[vShow, _ctx.node.visible]]);\n}\nvar ElTreeNode = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"tree-node.vue\"]]);\nexport { ElTreeNode as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "name", "components", "ElCollapseTransition", "ElCheckbox", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElIcon", "Loading", "props", "node", "type", "Node", "default", "Object", "accordion", "Boolean", "renderContent", "Function", "renderAfterExpand", "showCheckbox", "emits", "setup", "ctx", "ns", "useNamespace", "broadcastExpanded", "useNodeExpandEventBroadcast", "tree", "inject", "expanded", "ref", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldChecked", "oldIndeterminate", "node$", "dragEvents", "dragEventsKey", "instance", "getCurrentInstance", "provide", "debugWarn", "value", "<PERSON><PERSON><PERSON>", "watch", "_a", "children", "data", "update<PERSON><PERSON><PERSON>n", "indeterminate", "val", "handleSelectChange", "checked", "childNodes", "length", "reInitChecked", "nextTick", "getNodeKey$1", "getNodeKey", "nodeKey", "getNodeClass", "nodeClassFunc", "class", "className", "isFunction", "isString", "emit", "handleClick", "e", "handleCurrentChange", "store", "nodeKeyProp", "cur<PERSON><PERSON><PERSON><PERSON>", "setCurrentNodeKey", "setCurrentNode", "currentNode", "expandOnClickNode", "handleExpandIconClick", "checkOnClickNode", "<PERSON><PERSON><PERSON><PERSON>", "checkOnClickLeaf", "disabled", "handleCheckChange", "handleContextMenu", "event", "vnode", "stopPropagation", "preventDefault", "collapse", "expand", "setChecked", "checkStrictly", "checkedNodes", "getCheckedNodes", "checked<PERSON>eys", "getChe<PERSON><PERSON>eys", "halfCheckedNodes", "getHalfCheckedNodes", "halfC<PERSON>cked<PERSON>eys", "getHalfCheckedKeys", "handleChildNodeExpand", "nodeData", "instance2", "handleDragStart", "draggable", "treeNodeDragStart", "treeNode", "handleDragOver", "treeNodeDragOver", "$el", "handleDrop", "handleDragEnd", "treeNodeDragEnd", "CaretRight", "_sfc_render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_el_icon", "resolveComponent", "_component_el_checkbox", "_component_loading", "_component_node_content", "_component_el_tree_node", "_component_el_collapse_transition", "withDirectives", "openBlock", "createElementBlock", "normalizeClass", "b", "is", "isCurrent", "onClick", "withModifiers", "onContextmenu", "onDragstart", "onDragover", "onDragend", "onDrop", "createElementVNode", "be", "style", "normalizeStyle", "paddingLeft", "level", "indent", "icon", "createBlock", "key", "withCtx", "resolveDynamicComponent", "_", "createCommentVNode", "onChange", "createVNode", "role", "Fragment", "renderList", "child", "vShow", "visible", "ElTreeNode", "_export_sfc"], "sources": ["../../../../../../packages/components/tree/src/tree-node.vue"], "sourcesContent": ["<template>\n  <div\n    v-show=\"node.visible\"\n    ref=\"node$\"\n    :class=\"[\n      ns.b('node'),\n      ns.is('expanded', expanded),\n      ns.is('current', node.isCurrent),\n      ns.is('hidden', !node.visible),\n      ns.is('focusable', !node.disabled),\n      ns.is('checked', !node.disabled && node.checked),\n      getNodeClass(node),\n    ]\"\n    role=\"treeitem\"\n    tabindex=\"-1\"\n    :aria-expanded=\"expanded\"\n    :aria-disabled=\"node.disabled\"\n    :aria-checked=\"node.checked\"\n    :draggable=\"tree.props.draggable\"\n    :data-key=\"getNodeKey(node)\"\n    @click.stop=\"handleClick\"\n    @contextmenu=\"handleContextMenu\"\n    @dragstart.stop=\"handleDragStart\"\n    @dragover.stop=\"handleDragOver\"\n    @dragend.stop=\"handleDragEnd\"\n    @drop.stop=\"handleDrop\"\n  >\n    <div\n      :class=\"ns.be('node', 'content')\"\n      :style=\"{ paddingLeft: (node.level - 1) * tree.props.indent + 'px' }\"\n    >\n      <el-icon\n        v-if=\"tree.props.icon || CaretRight\"\n        :class=\"[\n          ns.be('node', 'expand-icon'),\n          ns.is('leaf', node.isLeaf),\n          {\n            expanded: !node.isLeaf && expanded,\n          },\n        ]\"\n        @click.stop=\"handleExpandIconClick\"\n      >\n        <component :is=\"tree.props.icon || CaretRight\" />\n      </el-icon>\n      <el-checkbox\n        v-if=\"showCheckbox\"\n        :model-value=\"node.checked\"\n        :indeterminate=\"node.indeterminate\"\n        :disabled=\"!!node.disabled\"\n        @click.stop\n        @change=\"handleCheckChange\"\n      />\n      <el-icon\n        v-if=\"node.loading\"\n        :class=\"[ns.be('node', 'loading-icon'), ns.is('loading')]\"\n      >\n        <loading />\n      </el-icon>\n      <node-content :node=\"node\" :render-content=\"renderContent\" />\n    </div>\n    <el-collapse-transition>\n      <div\n        v-if=\"!renderAfterExpand || childNodeRendered\"\n        v-show=\"expanded\"\n        :class=\"ns.be('node', 'children')\"\n        role=\"group\"\n        :aria-expanded=\"expanded\"\n        @click.stop\n      >\n        <el-tree-node\n          v-for=\"child in node.childNodes\"\n          :key=\"getNodeKey(child)\"\n          :render-content=\"renderContent\"\n          :render-after-expand=\"renderAfterExpand\"\n          :show-checkbox=\"showCheckbox\"\n          :node=\"child\"\n          :accordion=\"accordion\"\n          :props=\"props\"\n          @node-expand=\"handleChildNodeExpand\"\n        />\n      </div>\n    </el-collapse-transition>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport {\n  defineComponent,\n  getCurrentInstance,\n  inject,\n  nextTick,\n  provide,\n  ref,\n  watch,\n} from 'vue'\nimport { debugWarn, isFunction, isString } from '@element-plus/utils'\nimport ElCollapseTransition from '@element-plus/components/collapse-transition'\nimport ElCheckbox from '@element-plus/components/checkbox'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { CaretRight, Loading } from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport NodeContent from './tree-node-content.vue'\nimport { getNodeKey as getNodeKeyUtil, handleCurrentChange } from './model/util'\nimport { useNodeExpandEventBroadcast } from './model/useNodeExpandEventBroadcast'\nimport { dragEventsKey } from './model/useDragNode'\nimport Node from './model/node'\n\nimport type { ComponentInternalInstance, PropType } from 'vue'\nimport type { RootTreeType, TreeNodeData, TreeOptionProps } from './tree.type'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\n\nexport default defineComponent({\n  name: 'ElTreeNode',\n  components: {\n    ElCollapseTransition,\n    ElCheckbox,\n    NodeContent,\n    ElIcon,\n    Loading,\n  },\n  props: {\n    node: {\n      type: Node,\n      default: () => ({}),\n    },\n    props: {\n      type: Object as PropType<TreeOptionProps>,\n      default: () => ({}),\n    },\n    accordion: Boolean,\n    renderContent: Function,\n    renderAfterExpand: Boolean,\n    showCheckbox: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  emits: ['node-expand'],\n  setup(props, ctx) {\n    const ns = useNamespace('tree')\n    const { broadcastExpanded } = useNodeExpandEventBroadcast(props)\n    const tree = inject<RootTreeType>('RootTree')!\n    const expanded = ref(false)\n    const childNodeRendered = ref(false)\n    const oldChecked = ref<boolean>()\n    const oldIndeterminate = ref<boolean>()\n    const node$ = ref<HTMLElement>()\n    const dragEvents = inject(dragEventsKey)!\n    const instance = getCurrentInstance()\n\n    provide('NodeInstance', instance)\n    if (!tree) {\n      debugWarn('Tree', \"Can not find node's tree.\")\n    }\n\n    if (props.node.expanded) {\n      expanded.value = true\n      childNodeRendered.value = true\n    }\n\n    const childrenKey = tree.props.props['children'] || 'children'\n    watch(\n      () => {\n        const children = props.node.data?.[childrenKey]\n        return children && [...children]\n      },\n      () => {\n        props.node.updateChildren()\n      }\n    )\n\n    watch(\n      () => props.node.indeterminate,\n      (val) => {\n        handleSelectChange(props.node.checked, val)\n      }\n    )\n\n    watch(\n      () => props.node.checked,\n      (val) => {\n        handleSelectChange(val, props.node.indeterminate)\n      }\n    )\n\n    watch(\n      () => props.node.childNodes.length,\n      () => props.node.reInitChecked()\n    )\n\n    watch(\n      () => props.node.expanded,\n      (val) => {\n        nextTick(() => (expanded.value = val))\n        if (val) {\n          childNodeRendered.value = true\n        }\n      }\n    )\n\n    const getNodeKey = (node: Node): any => {\n      return getNodeKeyUtil(tree.props.nodeKey, node.data)\n    }\n\n    const getNodeClass = (node: Node) => {\n      const nodeClassFunc = props.props.class\n      if (!nodeClassFunc) {\n        return {}\n      }\n      let className\n      if (isFunction(nodeClassFunc)) {\n        const { data } = node\n        className = nodeClassFunc(data, node)\n      } else {\n        className = nodeClassFunc\n      }\n\n      if (isString(className)) {\n        return { [className]: true }\n      } else {\n        return className\n      }\n    }\n\n    const handleSelectChange = (checked: boolean, indeterminate: boolean) => {\n      if (\n        oldChecked.value !== checked ||\n        oldIndeterminate.value !== indeterminate\n      ) {\n        tree.ctx.emit('check-change', props.node.data, checked, indeterminate)\n      }\n      oldChecked.value = checked\n      oldIndeterminate.value = indeterminate\n    }\n\n    const handleClick = (e: MouseEvent) => {\n      handleCurrentChange(tree.store, tree.ctx.emit, () => {\n        const nodeKeyProp = tree?.props?.nodeKey\n        if (nodeKeyProp) {\n          const curNodeKey = getNodeKey(props.node)\n          tree.store.value.setCurrentNodeKey(curNodeKey)\n        } else {\n          tree.store.value.setCurrentNode(props.node)\n        }\n      })\n      tree.currentNode.value = props.node\n\n      if (tree.props.expandOnClickNode) {\n        handleExpandIconClick()\n      }\n\n      if (\n        (tree.props.checkOnClickNode ||\n          (props.node.isLeaf &&\n            tree.props.checkOnClickLeaf &&\n            props.showCheckbox)) &&\n        !props.node.disabled\n      ) {\n        handleCheckChange(!props.node.checked)\n      }\n      tree.ctx.emit('node-click', props.node.data, props.node, instance, e)\n    }\n\n    const handleContextMenu = (event: Event) => {\n      if (tree.instance.vnode.props?.['onNodeContextmenu']) {\n        event.stopPropagation()\n        event.preventDefault()\n      }\n      tree.ctx.emit(\n        'node-contextmenu',\n        event,\n        props.node.data,\n        props.node,\n        instance\n      )\n    }\n\n    const handleExpandIconClick = () => {\n      if (props.node.isLeaf) return\n      if (expanded.value) {\n        tree.ctx.emit('node-collapse', props.node.data, props.node, instance)\n        props.node.collapse()\n      } else {\n        props.node.expand(() => {\n          ctx.emit('node-expand', props.node.data, props.node, instance)\n        })\n      }\n    }\n\n    const handleCheckChange = (value: CheckboxValueType) => {\n      props.node.setChecked(value as boolean, !tree?.props.checkStrictly)\n      nextTick(() => {\n        const store = tree.store.value\n        tree.ctx.emit('check', props.node.data, {\n          checkedNodes: store.getCheckedNodes(),\n          checkedKeys: store.getCheckedKeys(),\n          halfCheckedNodes: store.getHalfCheckedNodes(),\n          halfCheckedKeys: store.getHalfCheckedKeys(),\n        })\n      })\n    }\n\n    const handleChildNodeExpand = (\n      nodeData: TreeNodeData,\n      node: Node,\n      instance: ComponentInternalInstance\n    ) => {\n      broadcastExpanded(node)\n      tree.ctx.emit('node-expand', nodeData, node, instance)\n    }\n\n    const handleDragStart = (event: DragEvent) => {\n      if (!tree.props.draggable) return\n      dragEvents.treeNodeDragStart({ event, treeNode: props })\n    }\n\n    const handleDragOver = (event: DragEvent) => {\n      event.preventDefault()\n      if (!tree.props.draggable) return\n      dragEvents.treeNodeDragOver({\n        event,\n        treeNode: { $el: node$.value, node: props.node },\n      })\n    }\n\n    const handleDrop = (event: DragEvent) => {\n      event.preventDefault()\n    }\n\n    const handleDragEnd = (event: DragEvent) => {\n      if (!tree.props.draggable) return\n      dragEvents.treeNodeDragEnd(event)\n    }\n\n    return {\n      ns,\n      node$,\n      tree,\n      expanded,\n      childNodeRendered,\n      oldChecked,\n      oldIndeterminate,\n      getNodeKey,\n      getNodeClass,\n      handleSelectChange,\n      handleClick,\n      handleContextMenu,\n      handleExpandIconClick,\n      handleCheckChange,\n      handleChildNodeExpand,\n      handleDragStart,\n      handleDragOver,\n      handleDrop,\n      handleDragEnd,\n      CaretRight,\n    }\n  },\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;AA+GA,MAAKA,SAAA,GAAaC,eAAa;EAC7BC,IAAM;EACNC,UAAY;IACVC,oBAAA;IACAC,UAAA;IACAC,WAAA;IACAC,MAAA;IACAC;EAAA,CACF;EACAC,KAAO;IACLC,IAAM;MACJC,IAAM,EAAAC,IAAA;MACNC,OAAA,EAASA,CAAA,MAAO,EAAC;IAAA,CACnB;IACAJ,KAAO;MACLE,IAAM,EAAAG,MAAA;MACND,OAAA,EAASA,CAAA,MAAO,EAAC;IAAA,CACnB;IACAE,SAAW,EAAAC,OAAA;IACXC,aAAe,EAAAC,QAAA;IACfC,iBAAmB,EAAAH,OAAA;IACnBI,YAAc;MACZT,IAAM,EAAAK,OAAA;MACNH,OAAS;IAAA;EACX,CACF;EACAQ,KAAA,EAAO,CAAC,aAAa;EACrBC,MAAMb,KAAA,EAAOc,GAAK;IACV,MAAAC,EAAA,GAAKC,YAAA,CAAa,MAAM;IAC9B,MAAM;MAAEC;IAAA,CAAsB,GAAAC,2BAAA,CAA4BlB,KAAK;IACzD,MAAAmB,IAAA,GAAOC,MAAA,CAAqB,UAAU;IACtC,MAAAC,QAAA,GAAWC,GAAA,CAAI,KAAK;IACpB,MAAAC,iBAAA,GAAoBD,GAAA,CAAI,KAAK;IACnC,MAAME,UAAA,GAAaF,GAAa;IAChC,MAAMG,gBAAA,GAAmBH,GAAa;IACtC,MAAMI,KAAA,GAAQJ,GAAiB;IACzB,MAAAK,UAAA,GAAaP,MAAA,CAAOQ,aAAa;IACvC,MAAMC,QAAA,GAAWC,kBAAmB;IAEpCC,OAAA,CAAQ,gBAAgBF,QAAQ;IAChC,IAAI,CAACV,IAAM;MACTa,SAAA,CAAU,QAAQ,2BAA2B;IAAA;IAG3C,IAAAhC,KAAA,CAAMC,IAAA,CAAKoB,QAAU;MACvBA,QAAA,CAASY,KAAQ;MACjBV,iBAAA,CAAkBU,KAAQ;IAAA;IAG5B,MAAMC,WAAc,GAAAf,IAAA,CAAKnB,KAAM,CAAAA,KAAA,CAAM,UAAU,CAAK;IACpDmC,KAAA;MACE,IAAMC,EAAA;MACJ,MAAAC,QAAiB,IAAAD,EAAA,GAAApC,KAAW,CAAAC,IAAA,CAAAqC,IAAkB,qBAAAF,EAAA,CAAAF,WAAA;MACvC,OAAAG,QAAA,QAAaA,QAAW;IAAA,CACjC;MACArC,KAAM,CAAAC,IAAA,CAAAsC,cAAA;IACJ;IACFJ,KAAA,OAAAnC,KAAA,CAAAC,IAAA,CAAAuC,aAAA,EAAAC,GAAA;MACFC,kBAAA,CAAA1C,KAAA,CAAAC,IAAA,CAAA0C,OAAA,EAAAF,GAAA;IAEA;IACEN,KAAA,OAAAnC,KAAiB,CAAAC,IAAA,CAAA0C,OAAA,EAAAF,GAAA;MACjBC,kBAAS,CAAAD,GAAA,EAAAzC,KAAA,CAAAC,IAAA,CAAAuC,aAAA;IACP,CAAmB;IACrBL,KAAA,OAAAnC,KAAA,CAAAC,IAAA,CAAA2C,UAAA,CAAAC,MAAA,QAAA7C,KAAA,CAAAC,IAAA,CAAA6C,aAAA;IACFX,KAAA,OAAAnC,KAAA,CAAAC,IAAA,CAAAoB,QAAA,EAAAoB,GAAA;MAEAM,QAAA,OAAA1B,QAAA,CAAAY,KAAA,GAAAQ,GAAA;MACE,IAAAA,GAAA;QACSlB,iBAAA,CAAAU,KAAA;MACP;IAAgD,CAClD;IACF,MAAAe,YAAA,GAAA/C,IAAA;MAEA,OAAAgD,UAAA,CAAA9B,IAAA,CAAAnB,KAAA,CAAAkD,OAAA,EAAAjD,IAAA,CAAAqC,IAAA;IAAA,CACE;IACA,MAAAa,YAAY,GAAmBlD,IAAA;MACjC,MAAAmD,aAAA,GAAApD,KAAA,CAAAA,KAAA,CAAAqD,KAAA;MAEA,KAAAD,aAAA;QACE;MAAiB;MAEN,IAAAE,SAAA;MACT,IAAAC,UAAS,CAAAH,aAAA;QACP;UAAAd;QAAA,IAAArC,IAA0B;QAC5BqD,SAAA,GAAAF,aAAA,CAAAd,IAAA,EAAArC,IAAA;MAAA,CACF;QACFqD,SAAA,GAAAF,aAAA;MAEA;MACE,IAAAI,QAAsB,CAAAF,SAAA;QACxB;UAAA,CAAAA,SAAA;QAAA;MAEA,CAAM;QACE,OAAAA,SAAA;MACN;IACE;IACF,MAAAZ,kBAAA,GAAAA,CAAAC,OAAA,EAAAH,aAAA;MACI,IAAAhB,UAAA,CAAAS,KAAA,KAAAU,OAAA,IAAAlB,gBAAA,CAAAQ,KAAA,KAAAO,aAAA;QACArB,IAAA,CAAAL,GAAA,CAAA2C,IAAW,eAAgB,EAAAzD,KAAA,CAAAC,IAAA,CAAAqC,IAAA,EAAAK,OAAA,EAAAH,aAAA;MAC7B;MACYhB,UAAA,CAAAS,KAAA,GAAAU,OAAA;MACdlB,gBAAO,CAAAQ,KAAA,GAAAO,aAAA;IACL,CAAY;IACd,MAAAkB,WAAA,GAAAC,CAAA;MAEIC,mBAAA,CAAAzC,IAAqB,CAAA0C,KAAA,EAAA1C,IAAA,CAAAL,GAAA,CAAA2C,IAAA;QACvB,IAAArB,EAAA;QACK,MAAA0B,WAAA,IAAA1B,EAAA,GAAAjB,IAAA,oBAAAA,IAAA,CAAAnB,KAAA,qBAAAoC,EAAA,CAAAc,OAAA;QACE,IAAAY,WAAA;UACT,MAAAC,UAAA,GAAAf,YAAA,CAAAhD,KAAA,CAAAC,IAAA;UACFkB,IAAA,CAAA0C,KAAA,CAAA5B,KAAA,CAAA+B,iBAAA,CAAAD,UAAA;QAEA,CAAM;UAEF5C,IAAW,CAAA0C,KAAA,CAAA5B,KAAA,CAAAgC,cACX,CAAAjE,KAAA,CAAAC,IAAA;QAEA;MAAqE,CACvE;MACAkB,IAAA,CAAA+C,WAAmB,CAAAjC,KAAA,GAAAjC,KAAA,CAAAC,IAAA;MACnB,IAAAkB,IAAA,CAAAnB,KAAA,CAAAmE,iBAAyB;QAC3BC,qBAAA;MAEA;MACE,KAAAjD,IAAA,CAAAnB,KAAA,CAAAqE,gBAAgC,IAAKrE,KAAI,CAAAC,IAAA,CAAMqE,MAAM,IAAAnD,IAAA,CAAAnB,KAAA,CAAAuE,gBAAA,IAAAvE,KAAA,CAAAW,YAAA,MAAAX,KAAA,CAAAC,IAAA,CAAAuE,QAAA;QAC7CC,iBAAA,EAAAzE,KAAA,CAAAC,IAA2B,CAAA0C,OAAA;MACjC;MACQxB,IAAA,CAAAL,GAAA,CAAA2C,IAAA,aAAwB,EAAAzD,KAAA,CAAAC,IAAA,CAAAqC,IAAU,EAAAtC,KAAA,CAAAC,IAAA,EAAA4B,QAAA,EAAA8B,CAAA;IACxC,CAAK;IAAwC,MACxCe,iBAAA,GAAAC,KAAA;MACL,IAAAvC,EAAA;MACF,KAAAA,EAAA,GAAAjB,IAAA,CAAAU,QAAA,CAAA+C,KAAA,CAAA5E,KAAA,qBAAAoC,EAAA;QACDuC,KAAA,CAAAE,eAAA;QACIF,KAAA,CAAAG,cAAA;MAEL;MACwB3D,IAAA,CAAAL,GAAA,CAAA2C,IAAA,qBAAAkB,KAAA,EAAA3E,KAAA,CAAAC,IAAA,CAAAqC,IAAA,EAAAtC,KAAA,CAAAC,IAAA,EAAA4B,QAAA;IAAA,CACxB;IAEA,MAAAuC,qBAEK,GAAAA,CAAA;MAKe,IAAApE,KAAA,CAAAC,IAAA,CAAAqE,MAAC,EACrB;MACK,IAAAjD,QAAA,CAAAY,KAAuB;QAC9Bd,IAAA,CAAAL,GAAA,CAAA2C,IAAA,kBAAAzD,KAAA,CAAAC,IAAA,CAAAqC,IAAA,EAAAtC,KAAA,CAAAC,IAAA,EAAA4B,QAAA;QAEM7B,KAAA,CAAAC,IAAA,CAAA8E,QAAA;MACJ,OAAS;QACP/E,KAAA,CAAMC,IAAgB,CAAA+E,MAAA;UACtBlE,GAAA,CAAM2C,IAAe,gBAAAzD,KAAA,CAAAC,IAAA,CAAAqC,IAAA,EAAAtC,KAAA,CAAAC,IAAA,EAAA4B,QAAA;QAAA,CACvB;MACA;IAAS,CACP;IACA,MAAA4C,iBAAA,GAAAxC,KAAA;MAAAjC,KAAA,CAAAC,IACW,CAAAgF,UAAA,CAAAhD,KAAA,IAAAd,IAAA,oBAAAA,IAAA,CAAAnB,KAAA,CAAAkF,aAAA;MAAAnC,QACL;QACN,MAAAc,KAAA,GAAA1C,IAAA,CAAA0C,KAAA,CAAA5B,KAAA;QACFd,IAAA,CAAAL,GAAA,CAAA2C,IAAA,UAAAzD,KAAA,CAAAC,IAAA,CAAAqC,IAAA;UACF6C,YAAA,EAAAtB,KAAA,CAAAuB,eAAA;UAEMC,WAAA,EAAAxB,KAAA,CAAAyB,cAA8B;UAC9BC,gBAAmB,EAAA1B,KAAA,CAAA2B,mBAAA;UACnBC,eAAgB,EAAA5B,KAAA,CAAA6B,kBAAA;QAClB,CAAK;MACL;IAAoB;IAEd,MAAAC,qBAAkB,GAAAA,CAAAC,QAAA,EAAA3F,IAAA,EAAA4F,SAAA;MACtB5E,iBAAwB,CAAAhB,IAAA;MAAqCkB,IAC9D,CAAAL,GAAA,CAAA2C,IAAA,gBAAAmC,QAAA,EAAA3F,IAAA,EAAA4F,SAAA;IAAA,CACH;IACF,MAAAC,eAAA,GAAAnB,KAAA;MAEM,KAAAxD,IAAA,CAAAnB,KAAA,CAAA+F,SAAqB,EACzB;MACApE,UAAe,CAAAqE,iBAAA;QAAArB,KAAA;QAAAsB,QAAA,EAAAjG;MAAA;IACb,CAAM;IACN,MAAAkG,cAAuB,GAAAvB,KAAA;MACrBA,KAAA,CAAAG,cAAA;MACA,KAAA3D,IAAA,CAAAnB,KAAA,CAAA+F,SAAkC,EAClC;MACApE,UAAA,CAAAwE,gBAAuB,CAAmB;QAC5CxB,KAAC;QACFsB,QAAA;UAAAG,GAAA,EAAA1E,KAAA,CAAAO,KAAA;UAAAhC,IAAA,EAAAD,KAAA,CAAAC;QAAA;MAAA,CACH;IAEA;IAKE,MAAAoG,UAAA,GAAkB1B,KAAI;MACtBA,KAAK,CAAIG,cAAoB;IAAwB,CACvD;IAEM,MAAAwB,aAAA,GAAA3B,KAAwC;MACxC,KAACxD,IAAK,CAAAnB,KAAA,CAAM+F,SAAW,EAC3B;MACFpE,UAAA,CAAA4E,eAAA,CAAA5B,KAAA;IAEA,CAAM;IACJ;MACI5D,EAAA;MACJW,KAAA;MACEP,IAAA;MAAAE,QAAA;MAEFE,iBAAC;MACHC,UAAA;MAEMC,gBAAA;MACJwB,UAAA,EAAAD,YAAqB;MACvBG,YAAA;MAEMT,kBAAA;MACAgB,WAAM;MACVgB,iBAAA;MACFN,qBAAA;MAEOK,iBAAA;MACLkB,qBAAA;MACAG,eAAA;MACAI,cAAA;MACAG,UAAA;MACAC,aAAA;MACAE;IAAA,CACA;EAAA;AACA,CACA;AACA,SACAC,YAAAC,IAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,QAAA;EACA,MAAAC,kBAAA,GAAAC,gBAAA;EACA,MAAAC,sBAAA,GAAAD,gBAAA;EACA,MAAAE,kBAAA,GAAAF,gBAAA;EACA,MAAAG,uBAAA,GAAAH,gBAAA;EACA,MAAAI,uBAAA,GAAAJ,gBAAA;EACA,MAAAK,iCAAA,GAAAL,gBAAA;EACA,OAAAM,cAAA,EAAAC,SAAA,IAAAC,kBAAA;IACAnG,GAAA;IACA+B,KAAA,EAAAqE,cAAA,EACFhB,IAAA,CAAA3F,EAAA,CAAA4G,CAAA,UACFjB,IAAA,CAAA3F,EAAA,CAAA6G,EAAA,aAAAlB,IAAA,CAAArF,QAAA,GACDqF,IAAA,CAAA3F,EAAA,CAAA6G,EAAA,YAAAlB,IAAA,CAAAzG,IAAA,CAAA4H,SAAA,G;;;;;IAlWG,cAAI,EAAAnB,IAAA,CAAAzG,IAAA,CAAA0C,OAAA;IACHoD,SAAK,EAAAW,IAAA,CAAAvF,IAAA,CAAAnB,KAAA,CAAA+F,SAAA;IAAA,UAAa,EAACW,IAAA,CAAAzD,UAAA,CAAAyD,IAAA,CAAAzG,IAAA;IAAgB6H,OAAA,EAAAC,aAAK,CAAArB,IAAqB,CAAAhD,WAAA;IAAAsE,aAAc,EAAYtB,IAAA,CAAAhC,iBAAc;IAAAuD,WAAY,EAAEF,aAAY,CAAArB,IAAA,CAAAZ,eAAY;IAAAoC,UAAY,EAAAH,aAAiB,CAAArB,IAAA,CAAAR,cAAa;IAAAiC,SAAA,EAAcJ,aAAa,CAAKrB,IAAA,CAAAJ,aAAA,GAAY;IAAY8B,MAAA,EAAAL,aAA0B,CAAArB,IAAA,CAAAL,UAAA;EAAA,IASxQgC,kBAAK;IACIhF,KAAA,EAAAqE,cAAA,CAAAhB,IAAA,CAAA3F,EAAA,CAAAuH,EAAA;IACOC,KAAA,EAAAC,cAAA;MAAAC,WAAA,GAAA/B,IAAA,CAAAzG,IAAA,CAAAyI,KAAA,QAAAhC,IAAA,CAAAvF,IAAA,CAAAnB,KAAA,CAAA2I,MAAA;IAAA;EAAA,IACKjC,IAAA,CAAAvF,IAAA,CAAAnB,KACD,CAAA4I,IAAA,IAAAlC,IAAA,CAAAF,UAAA,IAAAgB,SAAA,IAAAqB,WAAA,CAAA7B,kBAAA;IACnB8B,GAAA;IACAzF,KAAA,EAAAqE,cAAA,EACAhB,IAAA,CAAA3F,EAAA,CAAAuH,EAAA,SAAuB,gBACV5B,IAAA,CAAA3F,EAAA,CAAA6G,EAAA,SAAAlB,IAAA,CAAAzG,IAAA,CAAAqE,MAAA,GACb;MACAjD,QAAA,GAAAqF,IAAA,CAAAzG,IAA6B,CAAAqE,MAAA,IAAAoC,IAAA,CAAArF;IAAA,CAC7B,CACA;IAAqByG,OAAA,EAAAC,aAAA,CAAArB,IAAA,CAAAtC,qBAAA;EAEtB;IAgCMhE,OAAA,EAAA2I,OAAA,SAAAvB,SAAA,IAAAqB,WAAA,CAAAG,uBAAA,CAAAtC,IAAA,CAAAvF,IAAA,CAAAnB,KAAA,CAAA4I,IAAA,IAAAlC,IAAA,CAAAF,UAAA,IA/BH;IACAyC,CAAA;EAA0D,+BAAAC,kBAAA,gB;IAGnDJ,GAAK;IAWH,eAAApC,IAAA,CAAAzG,IAAA,CAAA0C,OAAA;iBAVF,EAAA+D,IAAA,CAAAzG,IAAA,CAAAuC,aAAA;IAAAgC,QAAA,IAAiBkC,IAAE,CAAAzG,IAAA,CAAAuE,QAAA;IAAAsD,OAAsC,EAAAC,aAAW,QAAW;IAAsCoB,QAAA,EAAAzC,IAAA,CAAAjC;EAAe,oFAAAyE,kBAAA,gB;IAOzIJ,GAAA;IAAiCzF,KAAA,EAAAqE,cAAA,EAAAhB,IAAA,CAAA3F,EAAA,CAAAuH,EAAA,0BAAA5B,IAAA,CAAA3F,EAAA,CAAA6G,EAAA;;IAEexH,OAAA,EAAA2I,OAAA,QAAJK,WAAA,CAAAjC,kBAAA,E;;wDAGvCiC,WAAA,CAAAhC,uBAAA,EAMN;IAAAnH,IAAA,EAAAyG,IAAA,CAAAzG,IAAA;IALC,kBAAkByG,IAAA,CAAAlG;EAAA,mBACH,EAAK,mBACpB,MAAiB4I,WAAA,CAAA9B,iCAClB;IAAWlH,OAAA,EAAA2I,OAAA,SAAArC,IACF,CAAAhG,iBAAA,IAAAgG,IAAA,CAAAnF,iBAAA,GAAAgG,cAAA,EAAAC,SAAA,IAAAC,kBAAA;MAAAqB,GAAA;MAGHzF,KAAA,EAAAqE,cAAA,CAAAhB,IAAA,CAAA3F,EAAA,CAAAuH,EADR,CAKU;MAAAe,IAAA;MAHP,eAAQ,EAAA3C,IAAA,CAAArF,QAAA;MAAoCyG,OAAA,EAAAC,aAAA,Q;IAElC,KAAAP,SAAA,QAAAC,kBAAA,CAAA6B,QAAA,QAAAC,UAAA,CAAA7C,IAAA,CAAAzG,IAAA,CAAA2C,UAAA,EAAA4G,KAAA;;;;QAEgD,uBAAA9C,IAAA,CAAAhG,iBAAA;QAAxC,iBAAAgG,IAAA,CAAA/F,YAAA;QAAuBV,IAAA,EAAAuJ,KAAA;QAAAlJ,SAAA,EAAAoG,IAAA,CAAApG,SAAA;;;;cAuBrB,wCAAAmJ,KAAA,EAAA/C,IAAA,CApBvBrF,QAmBM,EAlBG,IAAA6H,kBAAA,eAkBH;;EAhBS,GACR,OACJ,eAAe,iJAAAO,KAAA,EAAA/C,IAAA,CAAAzG,IAChB,CAAAyJ,OAAA,EAAW;AAAA;AAEX,IAAAC,UAAA,GAUE,eAAAC,WAAA,CAAArK,SAAA,cAAAkH,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}