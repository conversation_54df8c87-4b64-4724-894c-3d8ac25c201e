{"ast": null, "code": "import TableV2 from './src/table-v2.mjs';\nexport { default as TableV2 } from './src/table-v2.mjs';\nimport AutoResizer from './src/components/auto-resizer.mjs';\nexport { Alignment as TableV2Alignment, FixedDir as TableV2FixedDir, SortOrder as TableV2SortOrder } from './src/constants.mjs';\nexport { autoResizerProps } from './src/auto-resizer.mjs';\nexport { placeholderSign as TableV2Placeholder } from './src/private.mjs';\nexport { tableV2Props } from './src/table.mjs';\nexport { tableV2RowProps } from './src/row.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElTableV2 = withInstall(TableV2);\nconst ElAutoResizer = withInstall(AutoResizer);\nexport { ElAutoResizer, ElTableV2 };", "map": {"version": 3, "names": ["ElTableV2", "withInstall", "TableV2", "ElAutoResizer", "AutoResizer"], "sources": ["../../../../../packages/components/table-v2/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport TableV2 from './src/table-v2'\nimport AutoResizer from './src/components/auto-resizer'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport {\n  Alignment as TableV2Alignment,\n  FixedDir as TableV2FixedDir,\n  SortOrder as TableV2SortOrder,\n} from './src/constants'\nexport { default as TableV2 } from './src/table-v2'\nexport * from './src/auto-resizer'\nexport { placeholderSign as TableV2Placeholder } from './src/private'\n\nexport const ElTableV2: SFCWithInstall<typeof TableV2> = withInstall(TableV2)\nexport const ElAutoResizer: SFCWithInstall<typeof AutoResizer> =\n  withInstall(AutoResizer)\n\nexport type {\n  Column,\n  Columns,\n  SortBy,\n  SortState,\n  TableV2CustomizedHeaderSlotParam,\n} from './src/types'\nexport type { TableV2Instance } from './src/table-v2'\nexport * from './src/table'\nexport * from './src/row'\n\nexport type { HeaderCellSlotProps } from './src/renderers/header-cell'\n"], "mappings": ";;;;;;;;;AAWY,MAACA,SAAS,GAAGC,WAAW,CAACC,OAAO;AAChC,MAACC,aAAa,GAAGF,WAAW,CAACG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}