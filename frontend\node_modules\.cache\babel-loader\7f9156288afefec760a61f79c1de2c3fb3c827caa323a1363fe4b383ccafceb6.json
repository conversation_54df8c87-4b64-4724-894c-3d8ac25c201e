{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst badgeProps = buildProps({\n  value: {\n    type: [String, Number],\n    default: \"\"\n  },\n  max: {\n    type: Number,\n    default: 99\n  },\n  isDot: Boolean,\n  hidden: Boolean,\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"warning\", \"info\", \"danger\"],\n    default: \"danger\"\n  },\n  showZero: {\n    type: Boolean,\n    default: true\n  },\n  color: String,\n  badgeStyle: {\n    type: definePropType([String, Object, Array])\n  },\n  offset: {\n    type: definePropType(Array),\n    default: [0, 0]\n  },\n  badgeClass: {\n    type: String\n  }\n});\nexport { badgeProps };", "map": {"version": 3, "names": ["badgeProps", "buildProps", "value", "type", "String", "Number", "default", "max", "isDot", "Boolean", "hidden", "values", "showZero", "color", "badgeStyle", "definePropType", "Object", "Array", "offset", "badgeClass"], "sources": ["../../../../../../packages/components/badge/src/badge.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes, StyleValue } from 'vue'\n\nexport const badgeProps = buildProps({\n  /**\n   * @description display value.\n   */\n  value: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description maximum value, shows `{max}+` when exceeded. Only works if value is a number.\n   */\n  max: {\n    type: Number,\n    default: 99,\n  },\n  /**\n   * @description if a little dot is displayed.\n   */\n  isDot: Boolean,\n  /**\n   * @description hidden badge.\n   */\n  hidden: Boolean,\n  /**\n   * @description badge type.\n   */\n  type: {\n    type: String,\n    values: ['primary', 'success', 'warning', 'info', 'danger'],\n    default: 'danger',\n  },\n  /**\n   * @description whether to show badge when value is zero.\n   */\n  showZero: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description customize dot background color\n   */\n  color: String,\n  /**\n   * @description CSS style of badge\n   */\n  badgeStyle: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n  },\n  /**\n   * @description set offset of the badge\n   */\n  offset: {\n    type: definePropType<[number, number]>(Array),\n    default: [0, 0],\n  },\n  /**\n   * @description custom class name of badge\n   */\n  badgeClass: {\n    type: String,\n  },\n} as const)\nexport type BadgeProps = ExtractPropTypes<typeof badgeProps>\n"], "mappings": ";AACY,MAACA,UAAU,GAAGC,UAAU,CAAC;EACnCC,KAAK,EAAE;IACLC,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;IACtBC,OAAO,EAAE;EACb,CAAG;EACDC,GAAG,EAAE;IACHJ,IAAI,EAAEE,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDE,KAAK,EAAEC,OAAO;EACdC,MAAM,EAAED,OAAO;EACfN,IAAI,EAAE;IACJA,IAAI,EAAEC,MAAM;IACZO,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC;IAC3DL,OAAO,EAAE;EACb,CAAG;EACDM,QAAQ,EAAE;IACRT,IAAI,EAAEM,OAAO;IACbH,OAAO,EAAE;EACb,CAAG;EACDO,KAAK,EAAET,MAAM;EACbU,UAAU,EAAE;IACVX,IAAI,EAAEY,cAAc,CAAC,CAACX,MAAM,EAAEY,MAAM,EAAEC,KAAK,CAAC;EAChD,CAAG;EACDC,MAAM,EAAE;IACNf,IAAI,EAAEY,cAAc,CAACE,KAAK,CAAC;IAC3BX,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC;EAClB,CAAG;EACDa,UAAU,EAAE;IACVhB,IAAI,EAAEC;EACV;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}