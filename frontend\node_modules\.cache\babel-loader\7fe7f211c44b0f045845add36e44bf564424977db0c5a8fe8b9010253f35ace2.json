{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { inject, computed } from 'vue';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nconst getAllColumns = columns => {\n  const result = [];\n  columns.forEach(column => {\n    if (column.children) {\n      result.push(column);\n      result.push.apply(result, getAllColumns(column.children));\n    } else {\n      result.push(column);\n    }\n  });\n  return result;\n};\nconst convertToRows = originColumns => {\n  let maxLevel = 1;\n  const traverse = (column, parent) => {\n    if (parent) {\n      column.level = parent.level + 1;\n      if (maxLevel < column.level) {\n        maxLevel = column.level;\n      }\n    }\n    if (column.children) {\n      let colSpan = 0;\n      column.children.forEach(subColumn => {\n        traverse(subColumn, column);\n        colSpan += subColumn.colSpan;\n      });\n      column.colSpan = colSpan;\n    } else {\n      column.colSpan = 1;\n    }\n  };\n  originColumns.forEach(column => {\n    column.level = 1;\n    traverse(column, void 0);\n  });\n  const rows = [];\n  for (let i = 0; i < maxLevel; i++) {\n    rows.push([]);\n  }\n  const allColumns = getAllColumns(originColumns);\n  allColumns.forEach(column => {\n    if (!column.children) {\n      column.rowSpan = maxLevel - column.level + 1;\n    } else {\n      column.rowSpan = 1;\n      column.children.forEach(col => col.isSubColumn = true);\n    }\n    rows[column.level - 1].push(column);\n  });\n  return rows;\n};\nfunction useUtils(props) {\n  const parent = inject(TABLE_INJECTION_KEY);\n  const columnRows = computed(() => {\n    return convertToRows(props.store.states.originColumns.value);\n  });\n  const isGroup = computed(() => {\n    const result = columnRows.value.length > 1;\n    if (result && parent) {\n      parent.state.isGroup.value = true;\n    }\n    return result;\n  });\n  const toggleAllSelection = event => {\n    event.stopPropagation();\n    parent == null ? void 0 : parent.store.commit(\"toggleAllSelection\");\n  };\n  return {\n    isGroup,\n    toggleAllSelection,\n    columnRows\n  };\n}\nexport { convertToRows, useUtils as default };", "map": {"version": 3, "names": ["getAllColumns", "columns", "result", "for<PERSON>ach", "column", "children", "push", "apply", "convertToRows", "originColumns", "maxLevel", "traverse", "parent", "level", "colSpan", "subColumn", "rows", "i", "allColumns", "rowSpan", "col", "isSubColumn", "useUtils", "props", "inject", "TABLE_INJECTION_KEY", "columnRows", "computed", "store", "states", "value", "isGroup", "length", "state", "toggleAllSelection", "event", "stopPropagation", "commit"], "sources": ["../../../../../../../packages/components/table/src/table-header/utils-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, inject } from 'vue'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableHeaderProps } from '.'\n\nconst getAllColumns = <T>(\n  columns: TableColumnCtx<T>[]\n): TableColumnCtx<T>[] => {\n  const result: TableColumnCtx<T>[] = []\n  columns.forEach((column) => {\n    if (column.children) {\n      result.push(column)\n      // eslint-disable-next-line prefer-spread\n      result.push.apply(result, getAllColumns(column.children))\n    } else {\n      result.push(column)\n    }\n  })\n  return result\n}\n\nexport const convertToRows = <T>(\n  originColumns: TableColumnCtx<T>[]\n): TableColumnCtx<T>[] => {\n  let maxLevel = 1\n  const traverse = (column: TableColumnCtx<T>, parent: TableColumnCtx<T>) => {\n    if (parent) {\n      column.level = parent.level + 1\n      if (maxLevel < column.level) {\n        maxLevel = column.level\n      }\n    }\n    if (column.children) {\n      let colSpan = 0\n      column.children.forEach((subColumn) => {\n        traverse(subColumn, column)\n        colSpan += subColumn.colSpan\n      })\n      column.colSpan = colSpan\n    } else {\n      column.colSpan = 1\n    }\n  }\n\n  originColumns.forEach((column) => {\n    column.level = 1\n    traverse(column, undefined)\n  })\n\n  const rows = []\n  for (let i = 0; i < maxLevel; i++) {\n    rows.push([])\n  }\n\n  const allColumns: TableColumnCtx<T>[] = getAllColumns(originColumns)\n\n  allColumns.forEach((column) => {\n    if (!column.children) {\n      column.rowSpan = maxLevel - column.level + 1\n    } else {\n      column.rowSpan = 1\n      column.children.forEach((col) => (col.isSubColumn = true))\n    }\n    rows[column.level - 1].push(column)\n  })\n\n  return rows\n}\n\nfunction useUtils<T>(props: TableHeaderProps<T>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const columnRows = computed(() => {\n    return convertToRows(props.store.states.originColumns.value)\n  })\n  const isGroup = computed(() => {\n    const result = columnRows.value.length > 1\n    if (result && parent) {\n      parent.state.isGroup.value = true\n    }\n    return result\n  })\n  const toggleAllSelection = (event: Event) => {\n    event.stopPropagation()\n    parent?.store.commit('toggleAllSelection')\n  }\n  return {\n    isGroup,\n    toggleAllSelection,\n    columnRows,\n  }\n}\n\nexport default useUtils\n"], "mappings": ";;;;;AAEA,MAAMA,aAAa,GAAIC,OAAO,IAAK;EACjC,MAAMC,MAAM,GAAG,EAAE;EACjBD,OAAO,CAACE,OAAO,CAAEC,MAAM,IAAK;IAC1B,IAAIA,MAAM,CAACC,QAAQ,EAAE;MACnBH,MAAM,CAACI,IAAI,CAACF,MAAM,CAAC;MACnBF,MAAM,CAACI,IAAI,CAACC,KAAK,CAACL,MAAM,EAAEF,aAAa,CAACI,MAAM,CAACC,QAAQ,CAAC,CAAC;IAC/D,CAAK,MAAM;MACLH,MAAM,CAACI,IAAI,CAACF,MAAM,CAAC;IACzB;EACA,CAAG,CAAC;EACF,OAAOF,MAAM;AACf,CAAC;AACW,MAACM,aAAa,GAAIC,aAAa,IAAK;EAC9C,IAAIC,QAAQ,GAAG,CAAC;EAChB,MAAMC,QAAQ,GAAGA,CAACP,MAAM,EAAEQ,MAAM,KAAK;IACnC,IAAIA,MAAM,EAAE;MACVR,MAAM,CAACS,KAAK,GAAGD,MAAM,CAACC,KAAK,GAAG,CAAC;MAC/B,IAAIH,QAAQ,GAAGN,MAAM,CAACS,KAAK,EAAE;QAC3BH,QAAQ,GAAGN,MAAM,CAACS,KAAK;MAC/B;IACA;IACI,IAAIT,MAAM,CAACC,QAAQ,EAAE;MACnB,IAAIS,OAAO,GAAG,CAAC;MACfV,MAAM,CAACC,QAAQ,CAACF,OAAO,CAAEY,SAAS,IAAK;QACrCJ,QAAQ,CAACI,SAAS,EAAEX,MAAM,CAAC;QAC3BU,OAAO,IAAIC,SAAS,CAACD,OAAO;MACpC,CAAO,CAAC;MACFV,MAAM,CAACU,OAAO,GAAGA,OAAO;IAC9B,CAAK,MAAM;MACLV,MAAM,CAACU,OAAO,GAAG,CAAC;IACxB;EACA,CAAG;EACDL,aAAa,CAACN,OAAO,CAAEC,MAAM,IAAK;IAChCA,MAAM,CAACS,KAAK,GAAG,CAAC;IAChBF,QAAQ,CAACP,MAAM,EAAE,KAAK,CAAC,CAAC;EAC5B,CAAG,CAAC;EACF,MAAMY,IAAI,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,QAAQ,EAAEO,CAAC,EAAE,EAAE;IACjCD,IAAI,CAACV,IAAI,CAAC,EAAE,CAAC;EACjB;EACE,MAAMY,UAAU,GAAGlB,aAAa,CAACS,aAAa,CAAC;EAC/CS,UAAU,CAACf,OAAO,CAAEC,MAAM,IAAK;IAC7B,IAAI,CAACA,MAAM,CAACC,QAAQ,EAAE;MACpBD,MAAM,CAACe,OAAO,GAAGT,QAAQ,GAAGN,MAAM,CAACS,KAAK,GAAG,CAAC;IAClD,CAAK,MAAM;MACLT,MAAM,CAACe,OAAO,GAAG,CAAC;MAClBf,MAAM,CAACC,QAAQ,CAACF,OAAO,CAAEiB,GAAG,IAAKA,GAAG,CAACC,WAAW,GAAG,IAAI,CAAC;IAC9D;IACIL,IAAI,CAACZ,MAAM,CAACS,KAAK,GAAG,CAAC,CAAC,CAACP,IAAI,CAACF,MAAM,CAAC;EACvC,CAAG,CAAC;EACF,OAAOY,IAAI;AACb;AACA,SAASM,QAAQA,CAACC,KAAK,EAAE;EACvB,MAAMX,MAAM,GAAGY,MAAM,CAACC,mBAAmB,CAAC;EAC1C,MAAMC,UAAU,GAAGC,QAAQ,CAAC,MAAM;IAChC,OAAOnB,aAAa,CAACe,KAAK,CAACK,KAAK,CAACC,MAAM,CAACpB,aAAa,CAACqB,KAAK,CAAC;EAChE,CAAG,CAAC;EACF,MAAMC,OAAO,GAAGJ,QAAQ,CAAC,MAAM;IAC7B,MAAMzB,MAAM,GAAGwB,UAAU,CAACI,KAAK,CAACE,MAAM,GAAG,CAAC;IAC1C,IAAI9B,MAAM,IAAIU,MAAM,EAAE;MACpBA,MAAM,CAACqB,KAAK,CAACF,OAAO,CAACD,KAAK,GAAG,IAAI;IACvC;IACI,OAAO5B,MAAM;EACjB,CAAG,CAAC;EACF,MAAMgC,kBAAkB,GAAIC,KAAK,IAAK;IACpCA,KAAK,CAACC,eAAe,EAAE;IACvBxB,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACgB,KAAK,CAACS,MAAM,CAAC,oBAAoB,CAAC;EACvE,CAAG;EACD,OAAO;IACLN,OAAO;IACPG,kBAAkB;IAClBR;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}