{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, renderSlot } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElFooter\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    height: {\n      type: String,\n      default: null\n    }\n  },\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"footer\");\n    const style = computed(() => props.height ? ns.cssVarBlock({\n      height: props.height\n    }) : {});\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"footer\", {\n        class: normalizeClass(unref(ns).b()),\n        style: normalizeStyle(unref(style))\n      }, [renderSlot(_ctx.$slots, \"default\")], 6);\n    };\n  }\n});\nvar Footer = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"footer.vue\"]]);\nexport { Footer as default };", "map": {"version": 3, "names": ["name", "openBlock", "createElementBlock", "class", "normalizeClass", "unref", "ns", "b", "style", "normalizeStyle"], "sources": ["../../../../../../packages/components/container/src/footer.vue"], "sourcesContent": ["<template>\n  <footer :class=\"ns.b()\" :style=\"style\">\n    <slot />\n  </footer>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: '<PERSON>Footer',\n})\nconst props = defineProps({\n  /**\n   * @description height of the footer\n   */\n  height: {\n    type: String,\n    default: null,\n  },\n})\n\nconst ns = useNamespace('footer')\n\nconst style = computed(\n  () =>\n    (props.height\n      ? ns.cssVarBlock({ height: props.height })\n      : {}) as CSSProperties\n)\n</script>\n"], "mappings": ";;;mCAYc;EACZA,IAAM;AACR;;;;;;;;;;;;;;;;MAWM,OAAAC,SAAA,IAAAC,kBAA0B;QAEhCC,KAAc,EAAAC,cAAA,CAAAC,KAAA,CAAAC,EAAA,EAAAC,CAAA;QACZC,KACS,EAAAC,cACA,CAAAJ,KAAA,CAAAG,KAAc;MAChB,CACT,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}