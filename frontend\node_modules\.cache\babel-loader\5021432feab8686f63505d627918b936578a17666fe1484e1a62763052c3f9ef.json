{"ast": null, "code": "import { isRef, ref } from 'vue';\nimport Loading from './service.mjs';\nimport { isObject, hyphenate, isString } from '@vue/shared';\nconst INSTANCE_KEY = Symbol(\"ElLoading\");\nconst createInstance = (el, binding) => {\n  var _a, _b, _c, _d;\n  const vm = binding.instance;\n  const getBindingProp = key => isObject(binding.value) ? binding.value[key] : void 0;\n  const resolveExpression = key => {\n    const data = isString(key) && (vm == null ? void 0 : vm[key]) || key;\n    if (data) return ref(data);else return data;\n  };\n  const getProp = name => resolveExpression(getBindingProp(name) || el.getAttribute(`element-loading-${hyphenate(name)}`));\n  const fullscreen = (_a = getBindingProp(\"fullscreen\")) != null ? _a : binding.modifiers.fullscreen;\n  const options = {\n    text: getProp(\"text\"),\n    svg: getProp(\"svg\"),\n    svgViewBox: getProp(\"svgViewBox\"),\n    spinner: getProp(\"spinner\"),\n    background: getProp(\"background\"),\n    customClass: getProp(\"customClass\"),\n    fullscreen,\n    target: (_b = getBindingProp(\"target\")) != null ? _b : fullscreen ? void 0 : el,\n    body: (_c = getBindingProp(\"body\")) != null ? _c : binding.modifiers.body,\n    lock: (_d = getBindingProp(\"lock\")) != null ? _d : binding.modifiers.lock\n  };\n  const instance = Loading(options);\n  instance._context = vLoading._context;\n  el[INSTANCE_KEY] = {\n    options,\n    instance\n  };\n};\nconst updateOptions = (newOptions, originalOptions) => {\n  for (const key of Object.keys(originalOptions)) {\n    if (isRef(originalOptions[key])) originalOptions[key].value = newOptions[key];\n  }\n};\nconst vLoading = {\n  mounted(el, binding) {\n    if (binding.value) {\n      createInstance(el, binding);\n    }\n  },\n  updated(el, binding) {\n    const instance = el[INSTANCE_KEY];\n    if (binding.oldValue !== binding.value) {\n      if (binding.value && !binding.oldValue) {\n        createInstance(el, binding);\n      } else if (binding.value && binding.oldValue) {\n        if (isObject(binding.value)) updateOptions(binding.value, instance.options);\n      } else {\n        instance == null ? void 0 : instance.instance.close();\n      }\n    }\n  },\n  unmounted(el) {\n    var _a;\n    (_a = el[INSTANCE_KEY]) == null ? void 0 : _a.instance.close();\n    el[INSTANCE_KEY] = null;\n  }\n};\nvLoading._context = null;\nexport { vLoading as default };", "map": {"version": 3, "names": ["INSTANCE_KEY", "Symbol", "createInstance", "el", "binding", "_a", "_b", "_c", "_d", "vm", "instance", "getBindingProp", "key", "isObject", "value", "resolveExpression", "data", "isString", "ref", "getProp", "name", "getAttribute", "hyphenate", "fullscreen", "modifiers", "options", "text", "svg", "svgViewBox", "spinner", "background", "customClass", "target", "body", "lock", "Loading", "_context", "vLoading", "updateOptions", "newOptions", "originalOptions", "Object", "keys", "isRef", "mounted", "updated", "oldValue", "close", "unmounted"], "sources": ["../../../../../../packages/components/loading/src/directive.ts"], "sourcesContent": ["// @ts-nocheck\nimport { isRef, ref } from 'vue'\nimport { hyphenate, isObject, isString } from '@element-plus/utils'\nimport Loading from './service'\nimport type { Directive, DirectiveBinding, UnwrapRef } from 'vue'\nimport type { LoadingOptions } from './types'\nimport type { LoadingInstance } from './loading'\n\nconst INSTANCE_KEY = Symbol('ElLoading')\n\nexport type LoadingBinding = boolean | UnwrapRef<LoadingOptions>\nexport interface ElementLoading extends HTMLElement {\n  [INSTANCE_KEY]?: {\n    instance: LoadingInstance\n    options: LoadingOptions\n  }\n}\n\nconst createInstance = (\n  el: ElementLoading,\n  binding: DirectiveBinding<LoadingBinding>\n) => {\n  const vm = binding.instance\n\n  const getBindingProp = <K extends keyof LoadingOptions>(\n    key: K\n  ): LoadingOptions[K] =>\n    isObject(binding.value) ? binding.value[key] : undefined\n\n  const resolveExpression = (key: any) => {\n    const data = (isString(key) && vm?.[key]) || key\n    if (data) return ref(data)\n    else return data\n  }\n\n  const getProp = <K extends keyof LoadingOptions>(name: K) =>\n    resolveExpression(\n      getBindingProp(name) ||\n        el.getAttribute(`element-loading-${hyphenate(name)}`)\n    )\n\n  const fullscreen =\n    getBindingProp('fullscreen') ?? binding.modifiers.fullscreen\n\n  const options: LoadingOptions = {\n    text: getProp('text'),\n    svg: getProp('svg'),\n    svgViewBox: getProp('svgViewBox'),\n    spinner: getProp('spinner'),\n    background: getProp('background'),\n    customClass: getProp('customClass'),\n    fullscreen,\n    target: getBindingProp('target') ?? (fullscreen ? undefined : el),\n    body: getBindingProp('body') ?? binding.modifiers.body,\n    lock: getBindingProp('lock') ?? binding.modifiers.lock,\n  }\n  const instance = Loading(options)\n  instance._context = vLoading._context\n  el[INSTANCE_KEY] = {\n    options,\n    instance,\n  }\n}\n\nconst updateOptions = (\n  newOptions: UnwrapRef<LoadingOptions>,\n  originalOptions: LoadingOptions\n) => {\n  for (const key of Object.keys(originalOptions)) {\n    if (isRef(originalOptions[key]))\n      originalOptions[key].value = newOptions[key]\n  }\n}\n\nconst vLoading: Directive<ElementLoading, LoadingBinding> = {\n  mounted(el, binding) {\n    if (binding.value) {\n      createInstance(el, binding)\n    }\n  },\n  updated(el, binding) {\n    const instance = el[INSTANCE_KEY]\n    if (binding.oldValue !== binding.value) {\n      if (binding.value && !binding.oldValue) {\n        createInstance(el, binding)\n      } else if (binding.value && binding.oldValue) {\n        if (isObject(binding.value))\n          updateOptions(binding.value, instance!.options)\n      } else {\n        instance?.instance.close()\n      }\n    }\n  },\n  unmounted(el) {\n    el[INSTANCE_KEY]?.instance.close()\n    el[INSTANCE_KEY] = null\n  },\n}\n\nvLoading._context = null\nexport default vLoading\n"], "mappings": ";;;AAGA,MAAMA,YAAY,GAAGC,MAAM,CAAC,WAAW,CAAC;AACxC,MAAMC,cAAc,GAAGA,CAACC,EAAE,EAAEC,OAAO,KAAK;EACtC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,MAAMC,EAAE,GAAGL,OAAO,CAACM,QAAQ;EAC3B,MAAMC,cAAc,GAAIC,GAAG,IAAKC,QAAQ,CAACT,OAAO,CAACU,KAAK,CAAC,GAAGV,OAAO,CAACU,KAAK,CAACF,GAAG,CAAC,GAAG,KAAK,CAAC;EACrF,MAAMG,iBAAiB,GAAIH,GAAG,IAAK;IACjC,MAAMI,IAAI,GAAGC,QAAQ,CAACL,GAAG,CAAC,KAAKH,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,GAAG,CAAC,CAAC,IAAIA,GAAG;IACpE,IAAII,IAAI,EACN,OAAOE,GAAG,CAACF,IAAI,CAAC,CAAC,KAEjB,OAAOA,IAAI;EACjB,CAAG;EACD,MAAMG,OAAO,GAAIC,IAAI,IAAKL,iBAAiB,CAACJ,cAAc,CAACS,IAAI,CAAC,IAAIjB,EAAE,CAACkB,YAAY,CAAC,mBAAmBC,SAAS,CAACF,IAAI,CAAC,EAAE,CAAC,CAAC;EAC1H,MAAMG,UAAU,GAAG,CAAClB,EAAE,GAAGM,cAAc,CAAC,YAAY,CAAC,KAAK,IAAI,GAAGN,EAAE,GAAGD,OAAO,CAACoB,SAAS,CAACD,UAAU;EAClG,MAAME,OAAO,GAAG;IACdC,IAAI,EAAEP,OAAO,CAAC,MAAM,CAAC;IACrBQ,GAAG,EAAER,OAAO,CAAC,KAAK,CAAC;IACnBS,UAAU,EAAET,OAAO,CAAC,YAAY,CAAC;IACjCU,OAAO,EAAEV,OAAO,CAAC,SAAS,CAAC;IAC3BW,UAAU,EAAEX,OAAO,CAAC,YAAY,CAAC;IACjCY,WAAW,EAAEZ,OAAO,CAAC,aAAa,CAAC;IACnCI,UAAU;IACVS,MAAM,EAAE,CAAC1B,EAAE,GAAGK,cAAc,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAGL,EAAE,GAAGiB,UAAU,GAAG,KAAK,CAAC,GAAGpB,EAAE;IAC/E8B,IAAI,EAAE,CAAC1B,EAAE,GAAGI,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAGJ,EAAE,GAAGH,OAAO,CAACoB,SAAS,CAACS,IAAI;IACzEC,IAAI,EAAE,CAAC1B,EAAE,GAAGG,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAGH,EAAE,GAAGJ,OAAO,CAACoB,SAAS,CAACU;EACzE,CAAG;EACD,MAAMxB,QAAQ,GAAGyB,OAAO,CAACV,OAAO,CAAC;EACjCf,QAAQ,CAAC0B,QAAQ,GAAGC,QAAQ,CAACD,QAAQ;EACrCjC,EAAE,CAACH,YAAY,CAAC,GAAG;IACjByB,OAAO;IACPf;EACJ,CAAG;AACH,CAAC;AACD,MAAM4B,aAAa,GAAGA,CAACC,UAAU,EAAEC,eAAe,KAAK;EACrD,KAAK,MAAM5B,GAAG,IAAI6B,MAAM,CAACC,IAAI,CAACF,eAAe,CAAC,EAAE;IAC9C,IAAIG,KAAK,CAACH,eAAe,CAAC5B,GAAG,CAAC,CAAC,EAC7B4B,eAAe,CAAC5B,GAAG,CAAC,CAACE,KAAK,GAAGyB,UAAU,CAAC3B,GAAG,CAAC;EAClD;AACA,CAAC;AACI,MAACyB,QAAQ,GAAG;EACfO,OAAOA,CAACzC,EAAE,EAAEC,OAAO,EAAE;IACnB,IAAIA,OAAO,CAACU,KAAK,EAAE;MACjBZ,cAAc,CAACC,EAAE,EAAEC,OAAO,CAAC;IACjC;EACA,CAAG;EACDyC,OAAOA,CAAC1C,EAAE,EAAEC,OAAO,EAAE;IACnB,MAAMM,QAAQ,GAAGP,EAAE,CAACH,YAAY,CAAC;IACjC,IAAII,OAAO,CAAC0C,QAAQ,KAAK1C,OAAO,CAACU,KAAK,EAAE;MACtC,IAAIV,OAAO,CAACU,KAAK,IAAI,CAACV,OAAO,CAAC0C,QAAQ,EAAE;QACtC5C,cAAc,CAACC,EAAE,EAAEC,OAAO,CAAC;MACnC,CAAO,MAAM,IAAIA,OAAO,CAACU,KAAK,IAAIV,OAAO,CAAC0C,QAAQ,EAAE;QAC5C,IAAIjC,QAAQ,CAACT,OAAO,CAACU,KAAK,CAAC,EACzBwB,aAAa,CAAClC,OAAO,CAACU,KAAK,EAAEJ,QAAQ,CAACe,OAAO,CAAC;MACxD,CAAO,MAAM;QACLf,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACA,QAAQ,CAACqC,KAAK,EAAE;MAC7D;IACA;EACA,CAAG;EACDC,SAASA,CAAC7C,EAAE,EAAE;IACZ,IAAIE,EAAE;IACN,CAACA,EAAE,GAAGF,EAAE,CAACH,YAAY,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGK,EAAE,CAACK,QAAQ,CAACqC,KAAK,EAAE;IAC9D5C,EAAE,CAACH,YAAY,CAAC,GAAG,IAAI;EAC3B;AACA;AACAqC,QAAQ,CAACD,QAAQ,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}