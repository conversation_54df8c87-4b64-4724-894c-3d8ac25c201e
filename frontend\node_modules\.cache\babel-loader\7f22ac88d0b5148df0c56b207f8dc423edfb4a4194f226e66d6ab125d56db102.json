{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nconst tabBarProps = buildProps({\n  tabs: {\n    type: definePropType(Array),\n    default: () => mutable([])\n  }\n});\nexport { tabBarProps };", "map": {"version": 3, "names": ["tabBarProps", "buildProps", "tabs", "type", "definePropType", "Array", "default", "mutable"], "sources": ["../../../../../../packages/components/tabs/src/tab-bar.ts"], "sourcesContent": ["import { buildProps, definePropType, mutable } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type { TabsPaneContext } from './constants'\nimport type TabBar from './tab-bar.vue'\n\nexport const tabBarProps = buildProps({\n  tabs: {\n    type: definePropType<TabsPaneContext[]>(Array),\n    default: () => mutable([] as const),\n  },\n} as const)\n\nexport type TabBarProps = ExtractPropTypes<typeof tabBarProps>\nexport type TabBarInstance = InstanceType<typeof TabBar> & unknown\n"], "mappings": ";;AACY,MAACA,WAAW,GAAGC,UAAU,CAAC;EACpCC,IAAI,EAAE;IACJC,IAAI,EAAEC,cAAc,CAACC,KAAK,CAAC;IAC3BC,OAAO,EAAEA,CAAA,KAAMC,OAAO,CAAC,EAAE;EAC7B;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}