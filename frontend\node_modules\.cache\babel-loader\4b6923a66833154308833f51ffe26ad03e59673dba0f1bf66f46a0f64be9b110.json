{"ast": null, "code": "import { isClient } from '@vueuse/core';\nimport { isString } from '@vue/shared';\nconst getElement = target => {\n  if (!isClient || target === \"\") return null;\n  if (isString(target)) {\n    try {\n      return document.querySelector(target);\n    } catch (e) {\n      return null;\n    }\n  }\n  return target;\n};\nexport { getElement };", "map": {"version": 3, "names": ["getElement", "target", "isClient", "isString", "document", "querySelector", "e"], "sources": ["../../../../../packages/utils/dom/element.ts"], "sourcesContent": ["import { isString } from '../types'\nimport { isClient } from '../browser'\n\ntype GetElement = <T extends string | HTMLElement | Window | null | undefined>(\n  target: T\n) => T extends string ? HTMLElement | null : T\n\nexport const getElement = ((\n  target: string | HTMLElement | Window | null | undefined\n) => {\n  if (!isClient || target === '') return null\n  if (isString(target)) {\n    try {\n      return document.querySelector<HTMLElement>(target)\n    } catch {\n      return null\n    }\n  }\n  return target\n}) as GetElement\n"], "mappings": ";;AAEY,MAACA,UAAU,GAAIC,MAAM,IAAK;EACpC,IAAI,CAACC,QAAQ,IAAID,MAAM,KAAK,EAAE,EAC5B,OAAO,IAAI;EACb,IAAIE,QAAQ,CAACF,MAAM,CAAC,EAAE;IACpB,IAAI;MACF,OAAOG,QAAQ,CAACC,aAAa,CAACJ,MAAM,CAAC;IAC3C,CAAK,CAAC,OAAOK,CAAC,EAAE;MACV,OAAO,IAAI;IACjB;EACA;EACE,OAAOL,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}