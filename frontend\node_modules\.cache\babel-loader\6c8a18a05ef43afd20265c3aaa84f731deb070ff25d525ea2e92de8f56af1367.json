{"ast": null, "code": "import { computed } from 'vue';\nimport { get } from 'lodash-unified';\nconst defaultProps = {\n  label: \"label\",\n  value: \"value\",\n  disabled: \"disabled\",\n  options: \"options\"\n};\nfunction useProps(props) {\n  const aliasProps = computed(() => ({\n    ...defaultProps,\n    ...props.props\n  }));\n  const getLabel = option => get(option, aliasProps.value.label);\n  const getValue = option => get(option, aliasProps.value.value);\n  const getDisabled = option => get(option, aliasProps.value.disabled);\n  const getOptions = option => get(option, aliasProps.value.options);\n  return {\n    aliasProps,\n    getLabel,\n    getValue,\n    getDisabled,\n    getOptions\n  };\n}\nexport { defaultProps, useProps };", "map": {"version": 3, "names": ["defaultProps", "label", "value", "disabled", "options", "useProps", "props", "aliasProps", "computed", "get<PERSON><PERSON><PERSON>", "option", "get", "getValue", "getDisabled", "getOptions"], "sources": ["../../../../../../packages/components/select-v2/src/useProps.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { get } from 'lodash-unified'\n\nimport type { ISelectV2Props } from './token'\nimport type { Option } from './select.types'\n\nexport interface Props {\n  label?: string\n  value?: string\n  disabled?: string\n  options?: string\n}\n\nexport const defaultProps: Required<Props> = {\n  label: 'label',\n  value: 'value',\n  disabled: 'disabled',\n  options: 'options',\n}\n\nexport function useProps(props: Pick<ISelectV2Props, 'props'>) {\n  const aliasProps = computed(() => ({ ...defaultProps, ...props.props }))\n\n  const getLabel = (option: Option) => get(option, aliasProps.value.label)\n  const getValue = (option: Option) => get(option, aliasProps.value.value)\n  const getDisabled = (option: Option) => get(option, aliasProps.value.disabled)\n  const getOptions = (option: Option) => get(option, aliasProps.value.options)\n\n  return {\n    aliasProps,\n    getLabel,\n    getValue,\n    getDisabled,\n    getOptions,\n  }\n}\n"], "mappings": ";;AAEY,MAACA,YAAY,GAAG;EAC1BC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE;AACX;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,MAAMC,UAAU,GAAGC,QAAQ,CAAC,OAAO;IAAE,GAAGR,YAAY;IAAE,GAAGM,KAAK,CAACA;EAAK,CAAE,CAAC,CAAC;EACxE,MAAMG,QAAQ,GAAIC,MAAM,IAAKC,GAAG,CAACD,MAAM,EAAEH,UAAU,CAACL,KAAK,CAACD,KAAK,CAAC;EAChE,MAAMW,QAAQ,GAAIF,MAAM,IAAKC,GAAG,CAACD,MAAM,EAAEH,UAAU,CAACL,KAAK,CAACA,KAAK,CAAC;EAChE,MAAMW,WAAW,GAAIH,MAAM,IAAKC,GAAG,CAACD,MAAM,EAAEH,UAAU,CAACL,KAAK,CAACC,QAAQ,CAAC;EACtE,MAAMW,UAAU,GAAIJ,MAAM,IAAKC,GAAG,CAACD,MAAM,EAAEH,UAAU,CAACL,KAAK,CAACE,OAAO,CAAC;EACpE,OAAO;IACLG,UAAU;IACVE,QAAQ;IACRG,QAAQ;IACRC,WAAW;IACXC;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}