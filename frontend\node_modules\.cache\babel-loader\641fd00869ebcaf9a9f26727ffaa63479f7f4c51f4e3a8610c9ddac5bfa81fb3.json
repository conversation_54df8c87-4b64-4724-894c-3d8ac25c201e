{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isNil } from 'lodash-unified';\nimport { throwError } from '../../../utils/error.mjs';\nimport { isArray } from '@vue/shared';\nconst SCOPE = \"ElUpload\";\nclass UploadAjaxError extends Error {\n  constructor(message, status, method, url) {\n    super(message);\n    this.name = \"UploadAjaxError\";\n    this.status = status;\n    this.method = method;\n    this.url = url;\n  }\n}\nfunction getError(action, option, xhr) {\n  let msg;\n  if (xhr.response) {\n    msg = `${xhr.response.error || xhr.response}`;\n  } else if (xhr.responseText) {\n    msg = `${xhr.responseText}`;\n  } else {\n    msg = `fail to ${option.method} ${action} ${xhr.status}`;\n  }\n  return new UploadAjaxError(msg, xhr.status, option.method, action);\n}\nfunction getBody(xhr) {\n  const text = xhr.responseText || xhr.response;\n  if (!text) {\n    return text;\n  }\n  try {\n    return JSON.parse(text);\n  } catch (e) {\n    return text;\n  }\n}\nconst ajaxUpload = option => {\n  if (typeof XMLHttpRequest === \"undefined\") throwError(SCOPE, \"XMLHttpRequest is undefined\");\n  const xhr = new XMLHttpRequest();\n  const action = option.action;\n  if (xhr.upload) {\n    xhr.upload.addEventListener(\"progress\", evt => {\n      const progressEvt = evt;\n      progressEvt.percent = evt.total > 0 ? evt.loaded / evt.total * 100 : 0;\n      option.onProgress(progressEvt);\n    });\n  }\n  const formData = new FormData();\n  if (option.data) {\n    for (const [key, value] of Object.entries(option.data)) {\n      if (isArray(value) && value.length) formData.append(key, ...value);else formData.append(key, value);\n    }\n  }\n  formData.append(option.filename, option.file, option.file.name);\n  xhr.addEventListener(\"error\", () => {\n    option.onError(getError(action, option, xhr));\n  });\n  xhr.addEventListener(\"load\", () => {\n    if (xhr.status < 200 || xhr.status >= 300) {\n      return option.onError(getError(action, option, xhr));\n    }\n    option.onSuccess(getBody(xhr));\n  });\n  xhr.open(option.method, action, true);\n  if (option.withCredentials && \"withCredentials\" in xhr) {\n    xhr.withCredentials = true;\n  }\n  const headers = option.headers || {};\n  if (headers instanceof Headers) {\n    headers.forEach((value, key) => xhr.setRequestHeader(key, value));\n  } else {\n    for (const [key, value] of Object.entries(headers)) {\n      if (isNil(value)) continue;\n      xhr.setRequestHeader(key, String(value));\n    }\n  }\n  xhr.send(formData);\n  return xhr;\n};\nexport { UploadAjaxError, ajaxUpload };", "map": {"version": 3, "names": ["SCOPE", "UploadAjaxError", "Error", "constructor", "message", "status", "method", "url", "name", "getError", "action", "option", "xhr", "msg", "response", "error", "responseText", "getBody", "text", "JSON", "parse", "e", "ajaxUpload", "XMLHttpRequest", "throwError", "upload", "addEventListener", "evt", "progressEvt", "percent", "total", "loaded", "onProgress", "formData", "FormData", "data", "key", "value", "Object", "entries", "isArray", "length", "append", "filename", "file", "onError", "onSuccess", "open", "withCredentials", "headers", "Headers", "for<PERSON>ach", "setRequestHeader", "isNil", "String", "send"], "sources": ["../../../../../../packages/components/upload/src/ajax.ts"], "sourcesContent": ["import { isNil } from 'lodash-unified'\nimport { isArray, throwError } from '@element-plus/utils'\nimport type {\n  UploadProgressEvent,\n  UploadRequestHandler,\n  UploadRequestOptions,\n} from './upload'\n\nconst SCOPE = 'ElUpload'\n\nexport class UploadAjaxError extends Error {\n  name = 'UploadAjaxError'\n  status: number\n  method: string\n  url: string\n\n  constructor(message: string, status: number, method: string, url: string) {\n    super(message)\n    this.status = status\n    this.method = method\n    this.url = url\n  }\n}\n\nfunction getError(\n  action: string,\n  option: UploadRequestOptions,\n  xhr: XMLHttpRequest\n) {\n  let msg: string\n  if (xhr.response) {\n    msg = `${xhr.response.error || xhr.response}`\n  } else if (xhr.responseText) {\n    msg = `${xhr.responseText}`\n  } else {\n    msg = `fail to ${option.method} ${action} ${xhr.status}`\n  }\n\n  return new UploadAjaxError(msg, xhr.status, option.method, action)\n}\n\nfunction getBody(xhr: XMLHttpRequest): XMLHttpRequestResponseType {\n  const text = xhr.responseText || xhr.response\n  if (!text) {\n    return text\n  }\n\n  try {\n    return JSON.parse(text)\n  } catch {\n    return text\n  }\n}\n\nexport const ajaxUpload: UploadRequestHandler = (option) => {\n  if (typeof XMLHttpRequest === 'undefined')\n    throwError(SCOPE, 'XMLHttpRequest is undefined')\n\n  const xhr = new XMLHttpRequest()\n  const action = option.action\n\n  if (xhr.upload) {\n    xhr.upload.addEventListener('progress', (evt) => {\n      const progressEvt = evt as UploadProgressEvent\n      progressEvt.percent = evt.total > 0 ? (evt.loaded / evt.total) * 100 : 0\n      option.onProgress(progressEvt)\n    })\n  }\n\n  const formData = new FormData()\n  if (option.data) {\n    for (const [key, value] of Object.entries(option.data)) {\n      if (isArray(value) && value.length) formData.append(key, ...value)\n      else formData.append(key, value)\n    }\n  }\n  formData.append(option.filename, option.file, option.file.name)\n\n  xhr.addEventListener('error', () => {\n    option.onError(getError(action, option, xhr))\n  })\n\n  xhr.addEventListener('load', () => {\n    if (xhr.status < 200 || xhr.status >= 300) {\n      return option.onError(getError(action, option, xhr))\n    }\n    option.onSuccess(getBody(xhr))\n  })\n\n  xhr.open(option.method, action, true)\n\n  if (option.withCredentials && 'withCredentials' in xhr) {\n    xhr.withCredentials = true\n  }\n\n  const headers = option.headers || {}\n  if (headers instanceof Headers) {\n    headers.forEach((value, key) => xhr.setRequestHeader(key, value))\n  } else {\n    for (const [key, value] of Object.entries(headers)) {\n      if (isNil(value)) continue\n      xhr.setRequestHeader(key, String(value))\n    }\n  }\n\n  xhr.send(formData)\n  return xhr\n}\n"], "mappings": ";;;;;AAEA,MAAMA,KAAK,GAAG,UAAU;AACjB,MAAMC,eAAe,SAASC,KAAK,CAAC;EACzCC,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,GAAG,EAAE;IACxC,KAAK,CAACH,OAAO,CAAC;IACd,IAAI,CAACI,IAAI,GAAG,iBAAiB;IAC7B,IAAI,CAACH,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;AACA;AACA,SAASE,QAAQA,CAACC,MAAM,EAAEC,MAAM,EAAEC,GAAG,EAAE;EACrC,IAAIC,GAAG;EACP,IAAID,GAAG,CAACE,QAAQ,EAAE;IAChBD,GAAG,GAAG,GAAGD,GAAG,CAACE,QAAQ,CAACC,KAAK,IAAIH,GAAG,CAACE,QAAQ,EAAE;EACjD,CAAG,MAAM,IAAIF,GAAG,CAACI,YAAY,EAAE;IAC3BH,GAAG,GAAG,GAAGD,GAAG,CAACI,YAAY,EAAE;EAC/B,CAAG,MAAM;IACLH,GAAG,GAAG,WAAWF,MAAM,CAACL,MAAM,IAAII,MAAM,IAAIE,GAAG,CAACP,MAAM,EAAE;EAC5D;EACE,OAAO,IAAIJ,eAAe,CAACY,GAAG,EAAED,GAAG,CAACP,MAAM,EAAEM,MAAM,CAACL,MAAM,EAAEI,MAAM,CAAC;AACpE;AACA,SAASO,OAAOA,CAACL,GAAG,EAAE;EACpB,MAAMM,IAAI,GAAGN,GAAG,CAACI,YAAY,IAAIJ,GAAG,CAACE,QAAQ;EAC7C,IAAI,CAACI,IAAI,EAAE;IACT,OAAOA,IAAI;EACf;EACE,IAAI;IACF,OAAOC,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC;EAC3B,CAAG,CAAC,OAAOG,CAAC,EAAE;IACV,OAAOH,IAAI;EACf;AACA;AACY,MAACI,UAAU,GAAIX,MAAM,IAAK;EACpC,IAAI,OAAOY,cAAc,KAAK,WAAW,EACvCC,UAAU,CAACxB,KAAK,EAAE,6BAA6B,CAAC;EAClD,MAAMY,GAAG,GAAG,IAAIW,cAAc,EAAE;EAChC,MAAMb,MAAM,GAAGC,MAAM,CAACD,MAAM;EAC5B,IAAIE,GAAG,CAACa,MAAM,EAAE;IACdb,GAAG,CAACa,MAAM,CAACC,gBAAgB,CAAC,UAAU,EAAGC,GAAG,IAAK;MAC/C,MAAMC,WAAW,GAAGD,GAAG;MACvBC,WAAW,CAACC,OAAO,GAAGF,GAAG,CAACG,KAAK,GAAG,CAAC,GAAGH,GAAG,CAACI,MAAM,GAAGJ,GAAG,CAACG,KAAK,GAAG,GAAG,GAAG,CAAC;MACtEnB,MAAM,CAACqB,UAAU,CAACJ,WAAW,CAAC;IACpC,CAAK,CAAC;EACN;EACE,MAAMK,QAAQ,GAAG,IAAIC,QAAQ,EAAE;EAC/B,IAAIvB,MAAM,CAACwB,IAAI,EAAE;IACf,KAAK,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC5B,MAAM,CAACwB,IAAI,CAAC,EAAE;MACtD,IAAIK,OAAO,CAACH,KAAK,CAAC,IAAIA,KAAK,CAACI,MAAM,EAChCR,QAAQ,CAACS,MAAM,CAACN,GAAG,EAAE,GAAGC,KAAK,CAAC,CAAC,KAE/BJ,QAAQ,CAACS,MAAM,CAACN,GAAG,EAAEC,KAAK,CAAC;IACnC;EACA;EACEJ,QAAQ,CAACS,MAAM,CAAC/B,MAAM,CAACgC,QAAQ,EAAEhC,MAAM,CAACiC,IAAI,EAAEjC,MAAM,CAACiC,IAAI,CAACpC,IAAI,CAAC;EAC/DI,GAAG,CAACc,gBAAgB,CAAC,OAAO,EAAE,MAAM;IAClCf,MAAM,CAACkC,OAAO,CAACpC,QAAQ,CAACC,MAAM,EAAEC,MAAM,EAAEC,GAAG,CAAC,CAAC;EACjD,CAAG,CAAC;EACFA,GAAG,CAACc,gBAAgB,CAAC,MAAM,EAAE,MAAM;IACjC,IAAId,GAAG,CAACP,MAAM,GAAG,GAAG,IAAIO,GAAG,CAACP,MAAM,IAAI,GAAG,EAAE;MACzC,OAAOM,MAAM,CAACkC,OAAO,CAACpC,QAAQ,CAACC,MAAM,EAAEC,MAAM,EAAEC,GAAG,CAAC,CAAC;IAC1D;IACID,MAAM,CAACmC,SAAS,CAAC7B,OAAO,CAACL,GAAG,CAAC,CAAC;EAClC,CAAG,CAAC;EACFA,GAAG,CAACmC,IAAI,CAACpC,MAAM,CAACL,MAAM,EAAEI,MAAM,EAAE,IAAI,CAAC;EACrC,IAAIC,MAAM,CAACqC,eAAe,IAAI,iBAAiB,IAAIpC,GAAG,EAAE;IACtDA,GAAG,CAACoC,eAAe,GAAG,IAAI;EAC9B;EACE,MAAMC,OAAO,GAAGtC,MAAM,CAACsC,OAAO,IAAI,EAAE;EACpC,IAAIA,OAAO,YAAYC,OAAO,EAAE;IAC9BD,OAAO,CAACE,OAAO,CAAC,CAACd,KAAK,EAAED,GAAG,KAAKxB,GAAG,CAACwC,gBAAgB,CAAChB,GAAG,EAAEC,KAAK,CAAC,CAAC;EACrE,CAAG,MAAM;IACL,KAAK,MAAM,CAACD,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACU,OAAO,CAAC,EAAE;MAClD,IAAII,KAAK,CAAChB,KAAK,CAAC,EACd;MACFzB,GAAG,CAACwC,gBAAgB,CAAChB,GAAG,EAAEkB,MAAM,CAACjB,KAAK,CAAC,CAAC;IAC9C;EACA;EACEzB,GAAG,CAAC2C,IAAI,CAACtB,QAAQ,CAAC;EAClB,OAAOrB,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}