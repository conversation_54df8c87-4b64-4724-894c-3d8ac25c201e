{"ast": null, "code": "import { rowKey, columns, dataType, fixedDataType, expandKeys, classType, requiredNumber } from './common.mjs';\nimport { tableV2RowProps } from './row.mjs';\nimport { tableV2HeaderProps } from './header.mjs';\nimport { tableV2GridProps } from './grid.mjs';\nimport { virtualizedGridProps, virtualizedScrollbarProps } from '../../virtual-list/src/props.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst tableV2Props = buildProps({\n  cache: tableV2GridProps.cache,\n  estimatedRowHeight: tableV2RowProps.estimatedRowHeight,\n  rowKey,\n  headerClass: {\n    type: definePropType([String, Function])\n  },\n  headerProps: {\n    type: definePropType([Object, Function])\n  },\n  headerCellProps: {\n    type: definePropType([Object, Function])\n  },\n  headerHeight: tableV2HeaderProps.headerHeight,\n  footerHeight: {\n    type: Number,\n    default: 0\n  },\n  rowClass: {\n    type: definePropType([String, Function])\n  },\n  rowProps: {\n    type: definePropType([Object, Function])\n  },\n  rowHeight: {\n    type: Number,\n    default: 50\n  },\n  cellProps: {\n    type: definePropType([Object, Function])\n  },\n  columns,\n  data: dataType,\n  dataGetter: {\n    type: definePropType(Function)\n  },\n  fixedData: fixedDataType,\n  expandColumnKey: tableV2RowProps.expandColumnKey,\n  expandedRowKeys: expandKeys,\n  defaultExpandedRowKeys: expandKeys,\n  class: classType,\n  fixed: Boolean,\n  style: {\n    type: definePropType(Object)\n  },\n  width: requiredNumber,\n  height: requiredNumber,\n  maxHeight: Number,\n  useIsScrolling: Boolean,\n  indentSize: {\n    type: Number,\n    default: 12\n  },\n  iconSize: {\n    type: Number,\n    default: 12\n  },\n  hScrollbarSize: virtualizedGridProps.hScrollbarSize,\n  vScrollbarSize: virtualizedGridProps.vScrollbarSize,\n  scrollbarAlwaysOn: virtualizedScrollbarProps.alwaysOn,\n  sortBy: {\n    type: definePropType(Object),\n    default: () => ({})\n  },\n  sortState: {\n    type: definePropType(Object),\n    default: void 0\n  },\n  onColumnSort: {\n    type: definePropType(Function)\n  },\n  onExpandedRowsChange: {\n    type: definePropType(Function)\n  },\n  onEndReached: {\n    type: definePropType(Function)\n  },\n  onRowExpand: tableV2RowProps.onRowExpand,\n  onScroll: tableV2GridProps.onScroll,\n  onRowsRendered: tableV2GridProps.onRowsRendered,\n  rowEventHandlers: tableV2RowProps.rowEventHandlers\n});\nexport { tableV2Props };", "map": {"version": 3, "names": ["tableV2Props", "buildProps", "cache", "tableV2GridProps", "estimatedRowHeight", "tableV2RowProps", "<PERSON><PERSON><PERSON>", "headerClass", "type", "definePropType", "String", "Function", "headerProps", "Object", "headerCellProps", "headerHeight", "tableV2HeaderProps", "footerHeight", "Number", "default", "rowClass", "rowProps", "rowHeight", "cellProps", "columns", "data", "dataType", "dataGetter", "fixedData", "fixedDataType", "expandColumnKey", "expandedRowKeys", "expandKeys", "defaultExpandedRowKeys", "class", "classType", "fixed", "Boolean", "style", "width", "requiredNumber", "height", "maxHeight", "useIsScrolling", "indentSize", "iconSize", "hScrollbarSize", "virtualizedGridProps", "vScrollbarSize", "scrollbarAlwaysOn", "virtualizedScrollbarProps", "alwaysOn", "sortBy", "sortState", "onColumnSort", "onExpandedRowsChange", "onEndReached", "onRowExpand", "onScroll", "onRowsRendered", "rowEventHandlers"], "sources": ["../../../../../../packages/components/table-v2/src/table.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport {\n  virtualizedGridProps,\n  virtualizedScrollbarProps,\n} from '@element-plus/components/virtual-list'\nimport {\n  classType,\n  columns,\n  dataType,\n  expandKeys,\n  fixedDataType,\n  requiredNumber,\n  rowKey,\n} from './common'\nimport { tableV2RowProps } from './row'\nimport { tableV2HeaderProps } from './header'\nimport { tableV2GridProps } from './grid'\n\nimport type { CSSProperties, ExtractPropTypes } from 'vue'\nimport type { SortOrder } from './constants'\nimport type {\n  Column,\n  ColumnCommonParams,\n  DataGetter,\n  KeyType,\n  RowCommonParams,\n  SortBy,\n  SortState,\n} from './types'\n\n/**\n * Param types\n */\nexport type ColumnSortParams<T> = {\n  column: Column<T>\n  key: KeyType\n  order: SortOrder\n}\n\n/**\n * Renderer/Getter types\n */\n\nexport type ExtraCellPropGetter<T> = (\n  params: ColumnCommonParams<T> &\n    RowCommonParams & { cellData: T; rowData: any }\n) => any\n\nexport type ExtractHeaderPropGetter<T> = (params: {\n  columns: Column<T>[]\n  headerIndex: number\n}) => any\n\nexport type ExtractHeaderCellPropGetter<T> = (\n  params: ColumnCommonParams<T> & { headerIndex: number }\n) => any\n\nexport type ExtractRowPropGetter<T> = (\n  params: { columns: Column<T>[] } & RowCommonParams\n) => any\n\nexport type HeaderClassNameGetter<T> = (params: {\n  columns: Column<T>[]\n  headerIndex: number\n}) => string\n\nexport type RowClassNameGetter<T> = (\n  params: { columns: Column<T>[] } & RowCommonParams\n) => string\n\n/**\n * Handler types\n */\nexport type ColumnSortHandler<T> = (params: ColumnSortParams<T>) => void\nexport type ColumnResizeHandler<T> = (column: Column<T>, width: number) => void\nexport type ExpandedRowsChangeHandler = (expandedRowKeys: KeyType[]) => void\n\nexport const tableV2Props = buildProps({\n  cache: tableV2GridProps.cache,\n  estimatedRowHeight: tableV2RowProps.estimatedRowHeight,\n  rowKey,\n  // Header attributes\n  headerClass: {\n    type: definePropType<string | HeaderClassNameGetter<any>>([\n      String,\n      Function,\n    ]),\n  },\n  headerProps: {\n    type: definePropType<any | ExtractHeaderPropGetter<any>>([\n      Object,\n      Function,\n    ]),\n  },\n  headerCellProps: {\n    type: definePropType<any | ExtractHeaderCellPropGetter<any>>([\n      Object,\n      Function,\n    ]),\n  },\n  headerHeight: tableV2HeaderProps.headerHeight,\n  /**\n   * Footer attributes\n   */\n  footerHeight: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * Row attributes\n   */\n  rowClass: {\n    type: definePropType<string | RowClassNameGetter<any>>([String, Function]),\n  },\n  rowProps: {\n    type: definePropType<ExtractRowPropGetter<any> | any>([Object, Function]),\n  },\n  rowHeight: {\n    type: Number,\n    default: 50,\n  },\n\n  /**\n   * Cell attributes\n   */\n  cellProps: {\n    type: definePropType<Record<string, any> | ExtraCellPropGetter<any>>([\n      Object,\n      Function,\n    ]),\n  },\n  /**\n   * Data models\n   */\n  columns,\n  data: dataType,\n  dataGetter: {\n    type: definePropType<DataGetter<any>>(Function),\n  },\n  fixedData: fixedDataType,\n  /**\n   * Expanded keys\n   */\n  expandColumnKey: tableV2RowProps.expandColumnKey,\n  expandedRowKeys: expandKeys,\n  defaultExpandedRowKeys: expandKeys,\n\n  /**\n   * Attributes\n   */\n  class: classType,\n  // disabled: Boolean,\n  fixed: Boolean,\n  style: {\n    type: definePropType<CSSProperties>(Object),\n  },\n  width: requiredNumber,\n  height: requiredNumber,\n  maxHeight: Number,\n  useIsScrolling: Boolean,\n  indentSize: {\n    type: Number,\n    default: 12,\n  },\n  iconSize: {\n    type: Number,\n    default: 12,\n  },\n  hScrollbarSize: virtualizedGridProps.hScrollbarSize,\n  vScrollbarSize: virtualizedGridProps.vScrollbarSize,\n  scrollbarAlwaysOn: virtualizedScrollbarProps.alwaysOn,\n\n  /**\n   * Sorting\n   */\n  sortBy: {\n    type: definePropType<SortBy>(Object),\n    default: () => ({} as { key: KeyType; order: SortOrder }),\n  },\n  sortState: {\n    type: definePropType<SortState>(Object),\n    default: undefined,\n  },\n\n  /**\n   * Handlers\n   */\n  onColumnSort: {\n    type: definePropType<ColumnSortHandler<any>>(Function),\n  },\n  onExpandedRowsChange: {\n    type: definePropType<ExpandedRowsChangeHandler>(Function),\n  },\n  onEndReached: {\n    type: definePropType<(remainDistance: number) => void>(Function),\n  },\n  onRowExpand: tableV2RowProps.onRowExpand,\n  onScroll: tableV2GridProps.onScroll,\n  onRowsRendered: tableV2GridProps.onRowsRendered,\n  rowEventHandlers: tableV2RowProps.rowEventHandlers,\n} as const)\n\nexport type TableV2Props = ExtractPropTypes<typeof tableV2Props>\n"], "mappings": ";;;;;;AAiBY,MAACA,YAAY,GAAGC,UAAU,CAAC;EACrCC,KAAK,EAAEC,gBAAgB,CAACD,KAAK;EAC7BE,kBAAkB,EAAEC,eAAe,CAACD,kBAAkB;EACtDE,MAAM;EACNC,WAAW,EAAE;IACXC,IAAI,EAAEC,cAAc,CAAC,CACnBC,MAAM,EACNC,QAAQ,CACT;EACL,CAAG;EACDC,WAAW,EAAE;IACXJ,IAAI,EAAEC,cAAc,CAAC,CACnBI,MAAM,EACNF,QAAQ,CACT;EACL,CAAG;EACDG,eAAe,EAAE;IACfN,IAAI,EAAEC,cAAc,CAAC,CACnBI,MAAM,EACNF,QAAQ,CACT;EACL,CAAG;EACDI,YAAY,EAAEC,kBAAkB,CAACD,YAAY;EAC7CE,YAAY,EAAE;IACZT,IAAI,EAAEU,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,QAAQ,EAAE;IACRZ,IAAI,EAAEC,cAAc,CAAC,CAACC,MAAM,EAAEC,QAAQ,CAAC;EAC3C,CAAG;EACDU,QAAQ,EAAE;IACRb,IAAI,EAAEC,cAAc,CAAC,CAACI,MAAM,EAAEF,QAAQ,CAAC;EAC3C,CAAG;EACDW,SAAS,EAAE;IACTd,IAAI,EAAEU,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDI,SAAS,EAAE;IACTf,IAAI,EAAEC,cAAc,CAAC,CACnBI,MAAM,EACNF,QAAQ,CACT;EACL,CAAG;EACDa,OAAO;EACPC,IAAI,EAAEC,QAAQ;EACdC,UAAU,EAAE;IACVnB,IAAI,EAAEC,cAAc,CAACE,QAAQ;EACjC,CAAG;EACDiB,SAAS,EAAEC,aAAa;EACxBC,eAAe,EAAEzB,eAAe,CAACyB,eAAe;EAChDC,eAAe,EAAEC,UAAU;EAC3BC,sBAAsB,EAAED,UAAU;EAClCE,KAAK,EAAEC,SAAS;EAChBC,KAAK,EAAEC,OAAO;EACdC,KAAK,EAAE;IACL9B,IAAI,EAAEC,cAAc,CAACI,MAAM;EAC/B,CAAG;EACD0B,KAAK,EAAEC,cAAc;EACrBC,MAAM,EAAED,cAAc;EACtBE,SAAS,EAAExB,MAAM;EACjByB,cAAc,EAAEN,OAAO;EACvBO,UAAU,EAAE;IACVpC,IAAI,EAAEU,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACD0B,QAAQ,EAAE;IACRrC,IAAI,EAAEU,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACD2B,cAAc,EAAEC,oBAAoB,CAACD,cAAc;EACnDE,cAAc,EAAED,oBAAoB,CAACC,cAAc;EACnDC,iBAAiB,EAAEC,yBAAyB,CAACC,QAAQ;EACrDC,MAAM,EAAE;IACN5C,IAAI,EAAEC,cAAc,CAACI,MAAM,CAAC;IAC5BM,OAAO,EAAEA,CAAA,MAAO,EAAE;EACtB,CAAG;EACDkC,SAAS,EAAE;IACT7C,IAAI,EAAEC,cAAc,CAACI,MAAM,CAAC;IAC5BM,OAAO,EAAE,KAAK;EAClB,CAAG;EACDmC,YAAY,EAAE;IACZ9C,IAAI,EAAEC,cAAc,CAACE,QAAQ;EACjC,CAAG;EACD4C,oBAAoB,EAAE;IACpB/C,IAAI,EAAEC,cAAc,CAACE,QAAQ;EACjC,CAAG;EACD6C,YAAY,EAAE;IACZhD,IAAI,EAAEC,cAAc,CAACE,QAAQ;EACjC,CAAG;EACD8C,WAAW,EAAEpD,eAAe,CAACoD,WAAW;EACxCC,QAAQ,EAAEvD,gBAAgB,CAACuD,QAAQ;EACnCC,cAAc,EAAExD,gBAAgB,CAACwD,cAAc;EAC/CC,gBAAgB,EAAEvD,eAAe,CAACuD;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}