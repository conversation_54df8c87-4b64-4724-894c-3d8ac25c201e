{"ast": null, "code": "import { createVNode, isVNode } from 'vue';\nimport { tryCall } from '../utils.mjs';\nimport HeaderRow from '../components/header-row.mjs';\nfunction _isSlot(s) {\n  return typeof s === \"function\" || Object.prototype.toString.call(s) === \"[object Object]\" && !isVNode(s);\n}\nconst HeaderRenderer = ({\n  columns,\n  columnsStyles,\n  headerIndex,\n  style,\n  headerClass,\n  headerProps,\n  ns\n}, {\n  slots\n}) => {\n  const param = {\n    columns,\n    headerIndex\n  };\n  const kls = [ns.e(\"header-row\"), tryCall(headerClass, param, \"\"), {\n    [ns.is(\"customized\")]: Boolean(slots.header)\n  }];\n  const extraProps = {\n    ...tryCall(headerProps, param),\n    columnsStyles,\n    class: kls,\n    columns,\n    headerIndex,\n    style\n  };\n  return createVNode(HeaderRow, extraProps, _isSlot(slots) ? slots : {\n    default: () => [slots]\n  });\n};\nvar Header = HeaderRenderer;\nexport { Header as default };", "map": {"version": 3, "names": ["columnsStyles", "headerIndex", "style", "headerClass", "headerProps", "ns", "slots", "param", "columns", "kls", "e", "tryCall", "is", "Boolean", "header", "extraProps", "class", "createVNode", "HeaderRow", "_isSlot", "default", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../../../../../packages/components/table-v2/src/renderers/header.tsx"], "sourcesContent": ["import { HeaderRow } from '../components'\nimport { tryCall } from '../utils'\n\nimport type { FunctionalComponent } from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\nimport type { TableV2HeaderRendererParams } from '../components'\nimport type { TableV2Props } from '../table'\n\ntype HeaderRendererProps = TableV2HeaderRendererParams &\n  Pick<TableV2Props, 'headerClass' | 'headerProps'> & {\n    ns: UseNamespaceReturn\n  }\n\nconst HeaderRenderer: FunctionalComponent<HeaderRendererProps> = (\n  {\n    columns,\n    columnsStyles,\n    headerIndex,\n    style,\n    // derived from root\n    headerClass,\n    headerProps,\n\n    ns,\n  },\n  { slots }\n) => {\n  const param = { columns, headerIndex }\n\n  const kls = [\n    ns.e('header-row'),\n    tryCall(headerClass, param, ''),\n    {\n      // [ns.is('resizing')]: <PERSON>ole<PERSON>(resizingKey),\n      [ns.is('customized')]: <PERSON><PERSON>an(slots.header),\n    },\n  ]\n\n  const extraProps = {\n    ...tryCall(headerProps, param),\n    columnsStyles,\n    class: kls,\n    columns,\n    headerIndex,\n    style,\n  }\n\n  return <HeaderRow {...extraProps}>{slots}</HeaderRow>\n}\n\nexport default HeaderRenderer\n"], "mappings": ";;;;;;;;EAaMA,aAAA;EAEFC,WADF;EAEEC,KAFF;EAGEC,WAHF;EAIEC,WAJF;EAKEC;GALF;EAOEC;AAEA;EAEF,MAAAC,KAAA;IAAEC,OAAA;IACCP;EACH;QAAcQ,GAAA,IAAAJ,EAAA,CAAAK,CAAA,gBAAAC,OAAA,CAAAR,WAAA,EAAAI,KAAA;IAAW,CAAAF,EAAA,CAAAO,EAAA,iBAAAC,OAAA,CAAAP,KAAA,CAAAQ,MAAA;GAAzB;EAEA,MAAMC,UACD,GAAH;IAGE,GAAAJ,OAAA,CAAAP,WAAA,EAAAG,KAAA;IACAP,aAAO;IALXgB,KAAA,EAAAP,GAAA;IASMD,OAAA;IAEJP,WAFiB;IAGjBC;;SAHiBe,WAAA,CAAAC,SAAA,EAAAH,UAAA,EAAAI,OAAA,CAAAb,KAAA,IAAAA,KAAA;IAMjBc,OAAA,EAAAA,CAAA,MAAAd,KAAA;GANF;AASA;AAAA,IAAAe,MAAA,GAAAC,cAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}