{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { createRouter, createWebHashHistory } from \"vue-router\";\nimport Layout from \"@/components/layout/Layout.vue\";\n\n// 路由配置\nconst routes = [{\n  path: \"/\",\n  component: Layout,\n  redirect: \"/dashboard\",\n  children: [{\n    path: \"dashboard\",\n    name: \"Dashboard\",\n    component: () => import(\"@/views/Dashboard.vue\"),\n    meta: {\n      title: \"首页\",\n      requiresAuth: true\n    }\n  }]\n}, {\n  path: \"/login\",\n  name: \"Login\",\n  component: () => import(\"@/views/auth/Login.vue\"),\n  meta: {\n    title: \"登录\"\n  }\n}, {\n  path: \"/register\",\n  name: \"Register\",\n  component: () => import(\"@/views/auth/Register.vue\"),\n  meta: {\n    title: \"注册\"\n  }\n}, {\n  path: \"/user\",\n  component: Layout,\n  redirect: \"/user/profile\",\n  meta: {\n    requiresAuth: true\n  },\n  children: [{\n    path: \"profile\",\n    name: \"UserProfile\",\n    component: () => import(\"@/views/user/UserProfile.vue\"),\n    meta: {\n      title: \"个人信息\",\n      requiresAuth: true\n    }\n  }, {\n    path: \"reservations\",\n    name: \"MyReservations\",\n    component: () => import(\"@/views/user/MyReservations.vue\"),\n    meta: {\n      title: \"我的预约\",\n      requiresAuth: true\n    }\n  }, {\n    path: \"records\",\n    name: \"OperationRecords\",\n    component: () => import(\"@/views/user/OperationRecords.vue\"),\n    meta: {\n      title: \"操作记录\",\n      requiresAuth: true\n    }\n  }, {\n    path: \"credit\",\n    name: \"CreditRecords\",\n    component: () => import(\"@/views/user/CreditRecords.vue\"),\n    meta: {\n      title: \"信誉分记录\",\n      requiresAuth: true\n    }\n  }]\n}, {\n  path: \"/seat\",\n  component: Layout,\n  redirect: \"/seat/rooms\",\n  meta: {\n    requiresAuth: true\n  },\n  children: [{\n    path: \"rooms\",\n    name: \"RoomList\",\n    component: () => import(\"@/views/seat/RoomList.vue\"),\n    meta: {\n      title: \"自习室列表\",\n      requiresAuth: true\n    }\n  }, {\n    path: \"map\",\n    name: \"SeatMap\",\n    component: () => import(\"@/views/seat/SeatMap.vue\"),\n    meta: {\n      title: \"座位地图\",\n      requiresAuth: true\n    }\n  }, {\n    path: \"reservation\",\n    name: \"SeatReservation\",\n    component: () => import(\"@/views/seat/SeatReservation.vue\"),\n    meta: {\n      title: \"座位预约\",\n      requiresAuth: true\n    }\n  }, {\n    path: \"reservation/:id\",\n    name: \"ReservationDetail\",\n    component: () => import(\"@/views/seat/ReservationDetail.vue\"),\n    meta: {\n      title: \"预约详情\",\n      requiresAuth: true\n    }\n  }]\n}, {\n  path: \"/help\",\n  component: Layout,\n  children: [{\n    path: \"\",\n    name: \"Help\",\n    component: () => import(\"@/views/Help.vue\"),\n    meta: {\n      title: \"帮助中心\"\n    }\n  }]\n}, {\n  path: \"/:pathMatch(.*)*\",\n  name: \"NotFound\",\n  component: () => import(\"@/views/NotFound.vue\"),\n  meta: {\n    title: \"页面不存在\"\n  }\n}];\nconst router = createRouter({\n  history: createWebHashHistory(),\n  routes\n});\n\n// 全局前置守卫\nrouter.beforeEach((to, from, next) => {\n  // 设置页面标题\n  document.title = to.meta.title ? `${to.meta.title} - 基于国密算法的图书馆自习室座位管理系统` : \"基于国密算法的图书馆自习室座位管理系统\";\n\n  // 检查是否需要登录\n  if (to.matched.some(record => record.meta.requiresAuth)) {\n    // 检查用户是否已登录\n    const isLoggedIn = localStorage.getItem(\"token\") !== null;\n    if (!isLoggedIn) {\n      // 未登录，重定向到登录页\n      next({\n        path: \"/login\",\n        query: {\n          redirect: to.fullPath\n        }\n      });\n    } else {\n      // 已登录，允许访问\n      next();\n    }\n  } else {\n    // 不需要登录，允许访问\n    next();\n  }\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHashHistory", "Layout", "routes", "path", "component", "redirect", "children", "name", "meta", "title", "requiresAuth", "router", "history", "beforeEach", "to", "from", "next", "document", "matched", "some", "record", "isLoggedIn", "localStorage", "getItem", "query", "fullPath"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHashHistory } from \"vue-router\";\nimport Layout from \"@/components/layout/Layout.vue\";\n\n// 路由配置\nconst routes = [\n  {\n    path: \"/\",\n    component: Layout,\n    redirect: \"/dashboard\",\n    children: [\n      {\n        path: \"dashboard\",\n        name: \"Dashboard\",\n        component: () => import(\"@/views/Dashboard.vue\"),\n        meta: { title: \"首页\", requiresAuth: true },\n      },\n    ],\n  },\n  {\n    path: \"/login\",\n    name: \"<PERSON><PERSON>\",\n    component: () => import(\"@/views/auth/Login.vue\"),\n    meta: { title: \"登录\" },\n  },\n  {\n    path: \"/register\",\n    name: \"Register\",\n    component: () => import(\"@/views/auth/Register.vue\"),\n    meta: { title: \"注册\" },\n  },\n  {\n    path: \"/user\",\n    component: Layout,\n    redirect: \"/user/profile\",\n    meta: { requiresAuth: true },\n    children: [\n      {\n        path: \"profile\",\n        name: \"UserProfile\",\n        component: () => import(\"@/views/user/UserProfile.vue\"),\n        meta: { title: \"个人信息\", requiresAuth: true },\n      },\n      {\n        path: \"reservations\",\n        name: \"MyReservations\",\n        component: () => import(\"@/views/user/MyReservations.vue\"),\n        meta: { title: \"我的预约\", requiresAuth: true },\n      },\n      {\n        path: \"records\",\n        name: \"OperationRecords\",\n        component: () => import(\"@/views/user/OperationRecords.vue\"),\n        meta: { title: \"操作记录\", requiresAuth: true },\n      },\n      {\n        path: \"credit\",\n        name: \"CreditRecords\",\n        component: () => import(\"@/views/user/CreditRecords.vue\"),\n        meta: { title: \"信誉分记录\", requiresAuth: true },\n      },\n    ],\n  },\n  {\n    path: \"/seat\",\n    component: Layout,\n    redirect: \"/seat/rooms\",\n    meta: { requiresAuth: true },\n    children: [\n      {\n        path: \"rooms\",\n        name: \"RoomList\",\n        component: () => import(\"@/views/seat/RoomList.vue\"),\n        meta: { title: \"自习室列表\", requiresAuth: true },\n      },\n      {\n        path: \"map\",\n        name: \"SeatMap\",\n        component: () => import(\"@/views/seat/SeatMap.vue\"),\n        meta: { title: \"座位地图\", requiresAuth: true },\n      },\n      {\n        path: \"reservation\",\n        name: \"SeatReservation\",\n        component: () => import(\"@/views/seat/SeatReservation.vue\"),\n        meta: { title: \"座位预约\", requiresAuth: true },\n      },\n      {\n        path: \"reservation/:id\",\n        name: \"ReservationDetail\",\n        component: () => import(\"@/views/seat/ReservationDetail.vue\"),\n        meta: { title: \"预约详情\", requiresAuth: true },\n      },\n    ],\n  },\n  {\n    path: \"/help\",\n    component: Layout,\n    children: [\n      {\n        path: \"\",\n        name: \"Help\",\n        component: () => import(\"@/views/Help.vue\"),\n        meta: { title: \"帮助中心\" },\n      },\n    ],\n  },\n  {\n    path: \"/:pathMatch(.*)*\",\n    name: \"NotFound\",\n    component: () => import(\"@/views/NotFound.vue\"),\n    meta: { title: \"页面不存在\" },\n  },\n];\n\nconst router = createRouter({\n  history: createWebHashHistory(),\n  routes,\n});\n\n// 全局前置守卫\nrouter.beforeEach((to, from, next) => {\n  // 设置页面标题\n  document.title = to.meta.title\n    ? `${to.meta.title} - 基于国密算法的图书馆自习室座位管理系统`\n    : \"基于国密算法的图书馆自习室座位管理系统\";\n\n  // 检查是否需要登录\n  if (to.matched.some((record) => record.meta.requiresAuth)) {\n    // 检查用户是否已登录\n    const isLoggedIn = localStorage.getItem(\"token\") !== null;\n\n    if (!isLoggedIn) {\n      // 未登录，重定向到登录页\n      next({\n        path: \"/login\",\n        query: { redirect: to.fullPath },\n      });\n    } else {\n      // 已登录，允许访问\n      next();\n    }\n  } else {\n    // 不需要登录，允许访问\n    next();\n  }\n});\n\nexport default router;\n"], "mappings": ";;AAAA,SAASA,YAAY,EAAEC,oBAAoB,QAAQ,YAAY;AAC/D,OAAOC,MAAM,MAAM,gCAAgC;;AAEnD;AACA,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,SAAS,EAAEH,MAAM;EACjBI,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE,CACR;IACEH,IAAI,EAAE,WAAW;IACjBI,IAAI,EAAE,WAAW;IACjBH,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC;IAChDI,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAK;EAC1C,CAAC;AAEL,CAAC,EACD;EACEP,IAAI,EAAE,QAAQ;EACdI,IAAI,EAAE,OAAO;EACbH,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC;EACjDI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAK;AACtB,CAAC,EACD;EACEN,IAAI,EAAE,WAAW;EACjBI,IAAI,EAAE,UAAU;EAChBH,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;EACpDI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAK;AACtB,CAAC,EACD;EACEN,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEH,MAAM;EACjBI,QAAQ,EAAE,eAAe;EACzBG,IAAI,EAAE;IAAEE,YAAY,EAAE;EAAK,CAAC;EAC5BJ,QAAQ,EAAE,CACR;IACEH,IAAI,EAAE,SAAS;IACfI,IAAI,EAAE,aAAa;IACnBH,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC;IACvDI,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAK;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBI,IAAI,EAAE,gBAAgB;IACtBH,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC;IAC1DI,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAK;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfI,IAAI,EAAE,kBAAkB;IACxBH,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC;IAC5DI,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAK;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdI,IAAI,EAAE,eAAe;IACrBH,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDI,IAAI,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEC,YAAY,EAAE;IAAK;EAC7C,CAAC;AAEL,CAAC,EACD;EACEP,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEH,MAAM;EACjBI,QAAQ,EAAE,aAAa;EACvBG,IAAI,EAAE;IAAEE,YAAY,EAAE;EAAK,CAAC;EAC5BJ,QAAQ,EAAE,CACR;IACEH,IAAI,EAAE,OAAO;IACbI,IAAI,EAAE,UAAU;IAChBH,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;IACpDI,IAAI,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEC,YAAY,EAAE;IAAK;EAC7C,CAAC,EACD;IACEP,IAAI,EAAE,KAAK;IACXI,IAAI,EAAE,SAAS;IACfH,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC;IACnDI,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAK;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,aAAa;IACnBI,IAAI,EAAE,iBAAiB;IACvBH,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DI,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAK;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,iBAAiB;IACvBI,IAAI,EAAE,mBAAmB;IACzBH,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC7DI,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAK;EAC5C,CAAC;AAEL,CAAC,EACD;EACEP,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEH,MAAM;EACjBK,QAAQ,EAAE,CACR;IACEH,IAAI,EAAE,EAAE;IACRI,IAAI,EAAE,MAAM;IACZH,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC;IAC3CI,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC;AAEL,CAAC,EACD;EACEN,IAAI,EAAE,kBAAkB;EACxBI,IAAI,EAAE,UAAU;EAChBH,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;EAC/CI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAQ;AACzB,CAAC,CACF;AAED,MAAME,MAAM,GAAGZ,YAAY,CAAC;EAC1Ba,OAAO,EAAEZ,oBAAoB,CAAC,CAAC;EAC/BE;AACF,CAAC,CAAC;;AAEF;AACAS,MAAM,CAACE,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC;EACAC,QAAQ,CAACR,KAAK,GAAGK,EAAE,CAACN,IAAI,CAACC,KAAK,GAC1B,GAAGK,EAAE,CAACN,IAAI,CAACC,KAAK,wBAAwB,GACxC,qBAAqB;;EAEzB;EACA,IAAIK,EAAE,CAACI,OAAO,CAACC,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACZ,IAAI,CAACE,YAAY,CAAC,EAAE;IACzD;IACA,MAAMW,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAI;IAEzD,IAAI,CAACF,UAAU,EAAE;MACf;MACAL,IAAI,CAAC;QACHb,IAAI,EAAE,QAAQ;QACdqB,KAAK,EAAE;UAAEnB,QAAQ,EAAES,EAAE,CAACW;QAAS;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAT,IAAI,CAAC,CAAC;IACR;EACF,CAAC,MAAM;IACL;IACAA,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AAEF,eAAeL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}