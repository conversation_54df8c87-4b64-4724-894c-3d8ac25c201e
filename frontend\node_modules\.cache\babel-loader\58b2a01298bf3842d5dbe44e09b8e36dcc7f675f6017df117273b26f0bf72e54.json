{"ast": null, "code": "import { inject, getCurrentInstance, ref, unref, onMounted, reactive, onUnmounted } from 'vue';\nimport { carouselContextKey, CAROUSEL_ITEM_NAME } from './constants.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { isUndefined } from '../../../utils/types.mjs';\nconst useCarouselItem = props => {\n  const carouselContext = inject(carouselContextKey);\n  const instance = getCurrentInstance();\n  if (!carouselContext) {\n    debugWarn(CAROUSEL_ITEM_NAME, \"usage: <el-carousel></el-carousel-item></el-carousel>\");\n  }\n  if (!instance) {\n    debugWarn(CAROUSEL_ITEM_NAME, \"compositional hook can only be invoked inside setups\");\n  }\n  const carouselItemRef = ref();\n  const hover = ref(false);\n  const translate = ref(0);\n  const scale = ref(1);\n  const active = ref(false);\n  const ready = ref(false);\n  const inStage = ref(false);\n  const animating = ref(false);\n  const {\n    isCardType,\n    isVertical,\n    cardScale\n  } = carouselContext;\n  function processIndex(index, activeIndex, length) {\n    const lastItemIndex = length - 1;\n    const prevItemIndex = activeIndex - 1;\n    const nextItemIndex = activeIndex + 1;\n    const halfItemIndex = length / 2;\n    if (activeIndex === 0 && index === lastItemIndex) {\n      return -1;\n    } else if (activeIndex === lastItemIndex && index === 0) {\n      return length;\n    } else if (index < prevItemIndex && activeIndex - index >= halfItemIndex) {\n      return length + 1;\n    } else if (index > nextItemIndex && index - activeIndex >= halfItemIndex) {\n      return -2;\n    }\n    return index;\n  }\n  function calcCardTranslate(index, activeIndex) {\n    var _a, _b;\n    const parentWidth = unref(isVertical) ? ((_a = carouselContext.root.value) == null ? void 0 : _a.offsetHeight) || 0 : ((_b = carouselContext.root.value) == null ? void 0 : _b.offsetWidth) || 0;\n    if (inStage.value) {\n      return parentWidth * ((2 - cardScale) * (index - activeIndex) + 1) / 4;\n    } else if (index < activeIndex) {\n      return -(1 + cardScale) * parentWidth / 4;\n    } else {\n      return (3 + cardScale) * parentWidth / 4;\n    }\n  }\n  function calcTranslate(index, activeIndex, isVertical2) {\n    const rootEl = carouselContext.root.value;\n    if (!rootEl) return 0;\n    const distance = (isVertical2 ? rootEl.offsetHeight : rootEl.offsetWidth) || 0;\n    return distance * (index - activeIndex);\n  }\n  const translateItem = (index, activeIndex, oldIndex) => {\n    var _a;\n    const _isCardType = unref(isCardType);\n    const carouselItemLength = (_a = carouselContext.items.value.length) != null ? _a : Number.NaN;\n    const isActive = index === activeIndex;\n    if (!_isCardType && !isUndefined(oldIndex)) {\n      animating.value = isActive || index === oldIndex;\n    }\n    if (!isActive && carouselItemLength > 2 && carouselContext.loop) {\n      index = processIndex(index, activeIndex, carouselItemLength);\n    }\n    const _isVertical = unref(isVertical);\n    active.value = isActive;\n    if (_isCardType) {\n      inStage.value = Math.round(Math.abs(index - activeIndex)) <= 1;\n      translate.value = calcCardTranslate(index, activeIndex);\n      scale.value = unref(active) ? 1 : cardScale;\n    } else {\n      translate.value = calcTranslate(index, activeIndex, _isVertical);\n    }\n    ready.value = true;\n    if (isActive && carouselItemRef.value) {\n      carouselContext.setContainerHeight(carouselItemRef.value.offsetHeight);\n    }\n  };\n  function handleItemClick() {\n    if (carouselContext && unref(isCardType)) {\n      const index = carouselContext.items.value.findIndex(({\n        uid\n      }) => uid === instance.uid);\n      carouselContext.setActiveItem(index);\n    }\n  }\n  onMounted(() => {\n    carouselContext.addItem({\n      props,\n      states: reactive({\n        hover,\n        translate,\n        scale,\n        active,\n        ready,\n        inStage,\n        animating\n      }),\n      uid: instance.uid,\n      translateItem\n    });\n  });\n  onUnmounted(() => {\n    carouselContext.removeItem(instance.uid);\n  });\n  return {\n    carouselItemRef,\n    active,\n    animating,\n    hover,\n    inStage,\n    isVertical,\n    translate,\n    isCardType,\n    scale,\n    ready,\n    handleItemClick\n  };\n};\nexport { useCarouselItem };", "map": {"version": 3, "names": ["useCarouselItem", "props", "carouselContext", "inject", "carouselContextKey", "instance", "getCurrentInstance", "debugWarn", "CAROUSEL_ITEM_NAME", "carouselItemRef", "ref", "hover", "translate", "scale", "active", "ready", "inStage", "animating", "isCardType", "isVertical", "cardScale", "processIndex", "index", "activeIndex", "length", "lastItemIndex", "prevItemIndex", "nextItemIndex", "halfItemIndex", "calcCardTranslate", "_a", "_b", "parentWidth", "unref", "root", "value", "offsetHeight", "offsetWidth", "calcTranslate", "isVertical2", "rootEl", "distance", "translateItem", "oldIndex", "_isCardType", "carouselItemLength", "items", "Number", "NaN", "isActive", "isUndefined", "loop", "_isVertical", "Math", "round", "abs", "setContainerHeight", "handleItemClick", "findIndex", "uid", "setActiveItem", "onMounted", "addItem", "states", "reactive", "onUnmounted", "removeItem"], "sources": ["../../../../../../packages/components/carousel/src/use-carousel-item.ts"], "sourcesContent": ["import {\n  getCurrentInstance,\n  inject,\n  onMounted,\n  onUnmounted,\n  reactive,\n  ref,\n  unref,\n} from 'vue'\nimport { debugWarn, isUndefined } from '@element-plus/utils'\nimport { CAROUSEL_ITEM_NAME, carouselContextKey } from './constants'\n\nimport type { CarouselItemProps } from './carousel-item'\n\nexport const useCarouselItem = (props: CarouselItemProps) => {\n  const carouselContext = inject(carouselContextKey)!\n  // instance\n  const instance = getCurrentInstance()!\n  if (!carouselContext) {\n    debugWarn(\n      CAROUSEL_ITEM_NAME,\n      'usage: <el-carousel></el-carousel-item></el-carousel>'\n    )\n  }\n\n  if (!instance) {\n    debugWarn(\n      CAROUSEL_ITEM_NAME,\n      'compositional hook can only be invoked inside setups'\n    )\n  }\n\n  const carouselItemRef = ref<HTMLElement>()\n  const hover = ref(false)\n  const translate = ref(0)\n  const scale = ref(1)\n  const active = ref(false)\n  const ready = ref(false)\n  const inStage = ref(false)\n  const animating = ref(false)\n\n  // computed\n  const { isCardType, isVertical, cardScale } = carouselContext\n\n  // methods\n\n  function processIndex(index: number, activeIndex: number, length: number) {\n    const lastItemIndex = length - 1\n    const prevItemIndex = activeIndex - 1\n    const nextItemIndex = activeIndex + 1\n    const halfItemIndex = length / 2\n\n    if (activeIndex === 0 && index === lastItemIndex) {\n      return -1\n    } else if (activeIndex === lastItemIndex && index === 0) {\n      return length\n    } else if (index < prevItemIndex && activeIndex - index >= halfItemIndex) {\n      return length + 1\n    } else if (index > nextItemIndex && index - activeIndex >= halfItemIndex) {\n      return -2\n    }\n    return index\n  }\n\n  function calcCardTranslate(index: number, activeIndex: number) {\n    const parentWidth = unref(isVertical)\n      ? carouselContext.root.value?.offsetHeight || 0\n      : carouselContext.root.value?.offsetWidth || 0\n\n    if (inStage.value) {\n      return (parentWidth * ((2 - cardScale) * (index - activeIndex) + 1)) / 4\n    } else if (index < activeIndex) {\n      return (-(1 + cardScale) * parentWidth) / 4\n    } else {\n      return ((3 + cardScale) * parentWidth) / 4\n    }\n  }\n\n  function calcTranslate(\n    index: number,\n    activeIndex: number,\n    isVertical: boolean\n  ) {\n    const rootEl = carouselContext.root.value\n    if (!rootEl) return 0\n\n    const distance =\n      (isVertical ? rootEl.offsetHeight : rootEl.offsetWidth) || 0\n    return distance * (index - activeIndex)\n  }\n\n  const translateItem = (\n    index: number,\n    activeIndex: number,\n    oldIndex?: number\n  ) => {\n    const _isCardType = unref(isCardType)\n    const carouselItemLength = carouselContext.items.value.length ?? Number.NaN\n\n    const isActive = index === activeIndex\n    if (!_isCardType && !isUndefined(oldIndex)) {\n      animating.value = isActive || index === oldIndex\n    }\n\n    if (!isActive && carouselItemLength > 2 && carouselContext.loop) {\n      index = processIndex(index, activeIndex, carouselItemLength)\n    }\n\n    const _isVertical = unref(isVertical)\n    active.value = isActive\n\n    if (_isCardType) {\n      inStage.value = Math.round(Math.abs(index - activeIndex)) <= 1\n      translate.value = calcCardTranslate(index, activeIndex)\n      scale.value = unref(active) ? 1 : cardScale\n    } else {\n      translate.value = calcTranslate(index, activeIndex, _isVertical)\n    }\n\n    ready.value = true\n\n    if (isActive && carouselItemRef.value) {\n      carouselContext.setContainerHeight(carouselItemRef.value.offsetHeight)\n    }\n  }\n\n  function handleItemClick() {\n    if (carouselContext && unref(isCardType)) {\n      const index = carouselContext.items.value.findIndex(\n        ({ uid }) => uid === instance.uid\n      )\n      carouselContext.setActiveItem(index)\n    }\n  }\n\n  // lifecycle\n  onMounted(() => {\n    carouselContext.addItem({\n      props,\n      states: reactive({\n        hover,\n        translate,\n        scale,\n        active,\n        ready,\n        inStage,\n        animating,\n      }),\n      uid: instance.uid,\n      translateItem,\n    })\n  })\n\n  onUnmounted(() => {\n    carouselContext.removeItem(instance.uid)\n  })\n\n  return {\n    carouselItemRef,\n    active,\n    animating,\n    hover,\n    inStage,\n    isVertical,\n    translate,\n    isCardType,\n    scale,\n    ready,\n    handleItemClick,\n  }\n}\n"], "mappings": ";;;;AAWY,MAACA,eAAe,GAAIC,KAAK,IAAK;EACxC,MAAMC,eAAe,GAAGC,MAAM,CAACC,kBAAkB,CAAC;EAClD,MAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,IAAI,CAACJ,eAAe,EAAE;IACpBK,SAAS,CAACC,kBAAkB,EAAE,uDAAuD,CAAC;EAC1F;EACE,IAAI,CAACH,QAAQ,EAAE;IACbE,SAAS,CAACC,kBAAkB,EAAE,sDAAsD,CAAC;EACzF;EACE,MAAMC,eAAe,GAAGC,GAAG,EAAE;EAC7B,MAAMC,KAAK,GAAGD,GAAG,CAAC,KAAK,CAAC;EACxB,MAAME,SAAS,GAAGF,GAAG,CAAC,CAAC,CAAC;EACxB,MAAMG,KAAK,GAAGH,GAAG,CAAC,CAAC,CAAC;EACpB,MAAMI,MAAM,GAAGJ,GAAG,CAAC,KAAK,CAAC;EACzB,MAAMK,KAAK,GAAGL,GAAG,CAAC,KAAK,CAAC;EACxB,MAAMM,OAAO,GAAGN,GAAG,CAAC,KAAK,CAAC;EAC1B,MAAMO,SAAS,GAAGP,GAAG,CAAC,KAAK,CAAC;EAC5B,MAAM;IAAEQ,UAAU;IAAEC,UAAU;IAAEC;EAAS,CAAE,GAAGlB,eAAe;EAC7D,SAASmB,YAAYA,CAACC,KAAK,EAAEC,WAAW,EAAEC,MAAM,EAAE;IAChD,MAAMC,aAAa,GAAGD,MAAM,GAAG,CAAC;IAChC,MAAME,aAAa,GAAGH,WAAW,GAAG,CAAC;IACrC,MAAMI,aAAa,GAAGJ,WAAW,GAAG,CAAC;IACrC,MAAMK,aAAa,GAAGJ,MAAM,GAAG,CAAC;IAChC,IAAID,WAAW,KAAK,CAAC,IAAID,KAAK,KAAKG,aAAa,EAAE;MAChD,OAAO,CAAC,CAAC;IACf,CAAK,MAAM,IAAIF,WAAW,KAAKE,aAAa,IAAIH,KAAK,KAAK,CAAC,EAAE;MACvD,OAAOE,MAAM;IACnB,CAAK,MAAM,IAAIF,KAAK,GAAGI,aAAa,IAAIH,WAAW,GAAGD,KAAK,IAAIM,aAAa,EAAE;MACxE,OAAOJ,MAAM,GAAG,CAAC;IACvB,CAAK,MAAM,IAAIF,KAAK,GAAGK,aAAa,IAAIL,KAAK,GAAGC,WAAW,IAAIK,aAAa,EAAE;MACxE,OAAO,CAAC,CAAC;IACf;IACI,OAAON,KAAK;EAChB;EACE,SAASO,iBAAiBA,CAACP,KAAK,EAAEC,WAAW,EAAE;IAC7C,IAAIO,EAAE,EAAEC,EAAE;IACV,MAAMC,WAAW,GAAGC,KAAK,CAACd,UAAU,CAAC,GAAG,CAAC,CAACW,EAAE,GAAG5B,eAAe,CAACgC,IAAI,CAACC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGL,EAAE,CAACM,YAAY,KAAK,CAAC,GAAG,CAAC,CAACL,EAAE,GAAG7B,eAAe,CAACgC,IAAI,CAACC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,EAAE,CAACM,WAAW,KAAK,CAAC;IAChM,IAAIrB,OAAO,CAACmB,KAAK,EAAE;MACjB,OAAOH,WAAW,IAAI,CAAC,CAAC,GAAGZ,SAAS,KAAKE,KAAK,GAAGC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC5E,CAAK,MAAM,IAAID,KAAK,GAAGC,WAAW,EAAE;MAC9B,OAAO,EAAE,CAAC,GAAGH,SAAS,CAAC,GAAGY,WAAW,GAAG,CAAC;IAC/C,CAAK,MAAM;MACL,OAAO,CAAC,CAAC,GAAGZ,SAAS,IAAIY,WAAW,GAAG,CAAC;IAC9C;EACA;EACE,SAASM,aAAaA,CAAChB,KAAK,EAAEC,WAAW,EAAEgB,WAAW,EAAE;IACtD,MAAMC,MAAM,GAAGtC,eAAe,CAACgC,IAAI,CAACC,KAAK;IACzC,IAAI,CAACK,MAAM,EACT,OAAO,CAAC;IACV,MAAMC,QAAQ,GAAG,CAACF,WAAW,GAAGC,MAAM,CAACJ,YAAY,GAAGI,MAAM,CAACH,WAAW,KAAK,CAAC;IAC9E,OAAOI,QAAQ,IAAInB,KAAK,GAAGC,WAAW,CAAC;EAC3C;EACE,MAAMmB,aAAa,GAAGA,CAACpB,KAAK,EAAEC,WAAW,EAAEoB,QAAQ,KAAK;IACtD,IAAIb,EAAE;IACN,MAAMc,WAAW,GAAGX,KAAK,CAACf,UAAU,CAAC;IACrC,MAAM2B,kBAAkB,GAAG,CAACf,EAAE,GAAG5B,eAAe,CAAC4C,KAAK,CAACX,KAAK,CAACX,MAAM,KAAK,IAAI,GAAGM,EAAE,GAAGiB,MAAM,CAACC,GAAG;IAC9F,MAAMC,QAAQ,GAAG3B,KAAK,KAAKC,WAAW;IACtC,IAAI,CAACqB,WAAW,IAAI,CAACM,WAAW,CAACP,QAAQ,CAAC,EAAE;MAC1C1B,SAAS,CAACkB,KAAK,GAAGc,QAAQ,IAAI3B,KAAK,KAAKqB,QAAQ;IACtD;IACI,IAAI,CAACM,QAAQ,IAAIJ,kBAAkB,GAAG,CAAC,IAAI3C,eAAe,CAACiD,IAAI,EAAE;MAC/D7B,KAAK,GAAGD,YAAY,CAACC,KAAK,EAAEC,WAAW,EAAEsB,kBAAkB,CAAC;IAClE;IACI,MAAMO,WAAW,GAAGnB,KAAK,CAACd,UAAU,CAAC;IACrCL,MAAM,CAACqB,KAAK,GAAGc,QAAQ;IACvB,IAAIL,WAAW,EAAE;MACf5B,OAAO,CAACmB,KAAK,GAAGkB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACjC,KAAK,GAAGC,WAAW,CAAC,CAAC,IAAI,CAAC;MAC9DX,SAAS,CAACuB,KAAK,GAAGN,iBAAiB,CAACP,KAAK,EAAEC,WAAW,CAAC;MACvDV,KAAK,CAACsB,KAAK,GAAGF,KAAK,CAACnB,MAAM,CAAC,GAAG,CAAC,GAAGM,SAAS;IACjD,CAAK,MAAM;MACLR,SAAS,CAACuB,KAAK,GAAGG,aAAa,CAAChB,KAAK,EAAEC,WAAW,EAAE6B,WAAW,CAAC;IACtE;IACIrC,KAAK,CAACoB,KAAK,GAAG,IAAI;IAClB,IAAIc,QAAQ,IAAIxC,eAAe,CAAC0B,KAAK,EAAE;MACrCjC,eAAe,CAACsD,kBAAkB,CAAC/C,eAAe,CAAC0B,KAAK,CAACC,YAAY,CAAC;IAC5E;EACA,CAAG;EACD,SAASqB,eAAeA,CAAA,EAAG;IACzB,IAAIvD,eAAe,IAAI+B,KAAK,CAACf,UAAU,CAAC,EAAE;MACxC,MAAMI,KAAK,GAAGpB,eAAe,CAAC4C,KAAK,CAACX,KAAK,CAACuB,SAAS,CAAC,CAAC;QAAEC;MAAG,CAAE,KAAKA,GAAG,KAAKtD,QAAQ,CAACsD,GAAG,CAAC;MACtFzD,eAAe,CAAC0D,aAAa,CAACtC,KAAK,CAAC;IAC1C;EACA;EACEuC,SAAS,CAAC,MAAM;IACd3D,eAAe,CAAC4D,OAAO,CAAC;MACtB7D,KAAK;MACL8D,MAAM,EAAEC,QAAQ,CAAC;QACfrD,KAAK;QACLC,SAAS;QACTC,KAAK;QACLC,MAAM;QACNC,KAAK;QACLC,OAAO;QACPC;MACR,CAAO,CAAC;MACF0C,GAAG,EAAEtD,QAAQ,CAACsD,GAAG;MACjBjB;IACN,CAAK,CAAC;EACN,CAAG,CAAC;EACFuB,WAAW,CAAC,MAAM;IAChB/D,eAAe,CAACgE,UAAU,CAAC7D,QAAQ,CAACsD,GAAG,CAAC;EAC5C,CAAG,CAAC;EACF,OAAO;IACLlD,eAAe;IACfK,MAAM;IACNG,SAAS;IACTN,KAAK;IACLK,OAAO;IACPG,UAAU;IACVP,SAAS;IACTM,UAAU;IACVL,KAAK;IACLE,KAAK;IACL0C;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}