{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { getCurrentInstance, unref, nextTick } from 'vue';\nimport { isNull } from 'lodash-unified';\nimport useWatcher from './watcher.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nfunction replaceColumn(array, column) {\n  return array.map(item => {\n    var _a;\n    if (item.id === column.id) {\n      return column;\n    } else if ((_a = item.children) == null ? void 0 : _a.length) {\n      item.children = replaceColumn(item.children, column);\n    }\n    return item;\n  });\n}\nfunction sortColumn(array) {\n  array.forEach(item => {\n    var _a, _b;\n    item.no = (_a = item.getColumnIndex) == null ? void 0 : _a.call(item);\n    if ((_b = item.children) == null ? void 0 : _b.length) {\n      sortColumn(item.children);\n    }\n  });\n  array.sort((cur, pre) => cur.no - pre.no);\n}\nfunction useStore() {\n  const instance = getCurrentInstance();\n  const watcher = useWatcher();\n  const ns = useNamespace(\"table\");\n  const mutations = {\n    setData(states, data) {\n      const dataInstanceChanged = unref(states._data) !== data;\n      states.data.value = data;\n      states._data.value = data;\n      instance.store.execQuery();\n      instance.store.updateCurrentRowData();\n      instance.store.updateExpandRows();\n      instance.store.updateTreeData(instance.store.states.defaultExpandAll.value);\n      if (unref(states.reserveSelection)) {\n        instance.store.assertRowKey();\n        instance.store.updateSelectionByRowKey();\n      } else {\n        if (dataInstanceChanged) {\n          instance.store.clearSelection();\n        } else {\n          instance.store.cleanSelection();\n        }\n      }\n      instance.store.updateAllSelected();\n      if (instance.$ready) {\n        instance.store.scheduleLayout();\n      }\n    },\n    insertColumn(states, column, parent, updateColumnOrder) {\n      const array = unref(states._columns);\n      let newColumns = [];\n      if (!parent) {\n        array.push(column);\n        newColumns = array;\n      } else {\n        if (parent && !parent.children) {\n          parent.children = [];\n        }\n        parent.children.push(column);\n        newColumns = replaceColumn(array, parent);\n      }\n      sortColumn(newColumns);\n      states._columns.value = newColumns;\n      states.updateOrderFns.push(updateColumnOrder);\n      if (column.type === \"selection\") {\n        states.selectable.value = column.selectable;\n        states.reserveSelection.value = column.reserveSelection;\n      }\n      if (instance.$ready) {\n        instance.store.updateColumns();\n        instance.store.scheduleLayout();\n      }\n    },\n    updateColumnOrder(states, column) {\n      var _a;\n      const newColumnIndex = (_a = column.getColumnIndex) == null ? void 0 : _a.call(column);\n      if (newColumnIndex === column.no) return;\n      sortColumn(states._columns.value);\n      if (instance.$ready) {\n        instance.store.updateColumns();\n      }\n    },\n    removeColumn(states, column, parent, updateColumnOrder) {\n      const array = unref(states._columns) || [];\n      if (parent) {\n        parent.children.splice(parent.children.findIndex(item => item.id === column.id), 1);\n        nextTick(() => {\n          var _a;\n          if (((_a = parent.children) == null ? void 0 : _a.length) === 0) {\n            delete parent.children;\n          }\n        });\n        states._columns.value = replaceColumn(array, parent);\n      } else {\n        const index = array.indexOf(column);\n        if (index > -1) {\n          array.splice(index, 1);\n          states._columns.value = array;\n        }\n      }\n      const updateFnIndex = states.updateOrderFns.indexOf(updateColumnOrder);\n      updateFnIndex > -1 && states.updateOrderFns.splice(updateFnIndex, 1);\n      if (instance.$ready) {\n        instance.store.updateColumns();\n        instance.store.scheduleLayout();\n      }\n    },\n    sort(states, options) {\n      const {\n        prop,\n        order,\n        init\n      } = options;\n      if (prop) {\n        const column = unref(states.columns).find(column2 => column2.property === prop);\n        if (column) {\n          column.order = order;\n          instance.store.updateSort(column, prop, order);\n          instance.store.commit(\"changeSortCondition\", {\n            init\n          });\n        }\n      }\n    },\n    changeSortCondition(states, options) {\n      const {\n        sortingColumn,\n        sortProp,\n        sortOrder\n      } = states;\n      const columnValue = unref(sortingColumn),\n        propValue = unref(sortProp),\n        orderValue = unref(sortOrder);\n      if (isNull(orderValue)) {\n        states.sortingColumn.value = null;\n        states.sortProp.value = null;\n      }\n      const ignore = {\n        filter: true\n      };\n      instance.store.execQuery(ignore);\n      if (!options || !(options.silent || options.init)) {\n        instance.emit(\"sort-change\", {\n          column: columnValue,\n          prop: propValue,\n          order: orderValue\n        });\n      }\n      instance.store.updateTableScrollY();\n    },\n    filterChange(_states, options) {\n      const {\n        column,\n        values,\n        silent\n      } = options;\n      const newFilters = instance.store.updateFilters(column, values);\n      instance.store.execQuery();\n      if (!silent) {\n        instance.emit(\"filter-change\", newFilters);\n      }\n      instance.store.updateTableScrollY();\n    },\n    toggleAllSelection() {\n      instance.store.toggleAllSelection();\n    },\n    rowSelectedChanged(_states, row) {\n      instance.store.toggleRowSelection(row);\n      instance.store.updateAllSelected();\n    },\n    setHoverRow(states, row) {\n      states.hoverRow.value = row;\n    },\n    setCurrentRow(_states, row) {\n      instance.store.updateCurrentRow(row);\n    }\n  };\n  const commit = function (name, ...args) {\n    const mutations2 = instance.store.mutations;\n    if (mutations2[name]) {\n      mutations2[name].apply(instance, [instance.store.states].concat(args));\n    } else {\n      throw new Error(`Action not found: ${name}`);\n    }\n  };\n  const updateTableScrollY = function () {\n    nextTick(() => instance.layout.updateScrollY.apply(instance.layout));\n  };\n  return {\n    ns,\n    ...watcher,\n    mutations,\n    commit,\n    updateTableScrollY\n  };\n}\nexport { useStore as default };", "map": {"version": 3, "names": ["replaceColumn", "array", "column", "map", "item", "_a", "id", "children", "length", "sortColumn", "for<PERSON>ach", "_b", "no", "getColumnIndex", "call", "sort", "cur", "pre", "useStore", "instance", "getCurrentInstance", "watcher", "useWatcher", "ns", "useNamespace", "mutations", "setData", "states", "data", "dataInstanceChanged", "unref", "_data", "value", "store", "execQ<PERSON>y", "updateCurrentRowData", "updateExpandRows", "updateTreeData", "defaultExpandAll", "reserveSelection", "assertRowKey", "updateSelectionByRowKey", "clearSelection", "cleanSelection", "updateAllSelected", "$ready", "scheduleLayout", "insertColumn", "parent", "updateColumnOrder", "_columns", "newColumns", "push", "updateOrderFns", "type", "selectable", "updateColumns", "newColumnIndex", "removeColumn", "splice", "findIndex", "nextTick", "index", "indexOf", "updateFnIndex", "options", "prop", "order", "init", "columns", "find", "column2", "property", "updateSort", "commit", "changeSortCondition", "sortingColumn", "sortProp", "sortOrder", "columnValue", "propValue", "orderValue", "isNull", "ignore", "filter", "silent", "emit", "updateTableScrollY", "filterChange", "_states", "values", "newFilters", "updateFilters", "toggleAllSelection", "rowSelectedChanged", "row", "toggleRowSelection", "setHoverRow", "hoverRow", "setCurrentRow", "updateCurrentRow", "name", "args", "mutations2", "apply", "concat", "Error", "layout", "updateScrollY"], "sources": ["../../../../../../../packages/components/table/src/store/index.ts"], "sourcesContent": ["// @ts-nocheck\nimport { getCurrentInstance, nextTick, unref } from 'vue'\nimport { isNull } from 'lodash-unified'\nimport { useNamespace } from '@element-plus/hooks'\nimport useWatcher from './watcher'\n\nimport type { Ref } from 'vue'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { Filter, Sort, Table } from '../table/defaults'\n\ninterface WatcherPropsData<T> {\n  data: Ref<T[]>\n  rowKey: Ref<string>\n}\n\nfunction replaceColumn<T>(\n  array: TableColumnCtx<T>[],\n  column: TableColumnCtx<T>\n) {\n  return array.map((item) => {\n    if (item.id === column.id) {\n      return column\n    } else if (item.children?.length) {\n      item.children = replaceColumn(item.children, column)\n    }\n    return item\n  })\n}\n\nfunction sortColumn<T>(array: TableColumnCtx<T>[]) {\n  array.forEach((item) => {\n    item.no = item.getColumnIndex?.()\n    if (item.children?.length) {\n      sortColumn(item.children)\n    }\n  })\n  array.sort((cur, pre) => cur.no - pre.no)\n}\n\nfunction useStore<T>() {\n  const instance = getCurrentInstance() as Table<T>\n  const watcher = useWatcher<T>()\n  const ns = useNamespace('table')\n  type StoreStates = typeof watcher.states\n  const mutations = {\n    setData(states: StoreStates, data: T[]) {\n      const dataInstanceChanged = unref(states._data) !== data\n      states.data.value = data\n      states._data.value = data\n      instance.store.execQuery()\n      // 数据变化，更新部分数据。\n      // 没有使用 computed，而是手动更新部分数据 https://github.com/vuejs/vue/issues/6660#issuecomment-331417140\n      instance.store.updateCurrentRowData()\n      instance.store.updateExpandRows()\n      instance.store.updateTreeData(\n        instance.store.states.defaultExpandAll.value\n      )\n      if (unref(states.reserveSelection)) {\n        instance.store.assertRowKey()\n        instance.store.updateSelectionByRowKey()\n      } else {\n        if (dataInstanceChanged) {\n          instance.store.clearSelection()\n        } else {\n          instance.store.cleanSelection()\n        }\n      }\n      instance.store.updateAllSelected()\n      if (instance.$ready) {\n        instance.store.scheduleLayout()\n      }\n    },\n\n    insertColumn(\n      states: StoreStates,\n      column: TableColumnCtx<T>,\n      parent: TableColumnCtx<T>,\n      updateColumnOrder: () => void\n    ) {\n      const array = unref(states._columns)\n      let newColumns = []\n      if (!parent) {\n        array.push(column)\n        newColumns = array\n      } else {\n        if (parent && !parent.children) {\n          parent.children = []\n        }\n        parent.children.push(column)\n        newColumns = replaceColumn(array, parent)\n      }\n      sortColumn(newColumns)\n      states._columns.value = newColumns\n      states.updateOrderFns.push(updateColumnOrder)\n      if (column.type === 'selection') {\n        states.selectable.value = column.selectable\n        states.reserveSelection.value = column.reserveSelection\n      }\n      if (instance.$ready) {\n        instance.store.updateColumns() // hack for dynamics insert column\n        instance.store.scheduleLayout()\n      }\n    },\n\n    updateColumnOrder(states: StoreStates, column: TableColumnCtx<T>) {\n      const newColumnIndex = column.getColumnIndex?.()\n      if (newColumnIndex === column.no) return\n\n      sortColumn(states._columns.value)\n\n      if (instance.$ready) {\n        instance.store.updateColumns()\n      }\n    },\n\n    removeColumn(\n      states: StoreStates,\n      column: TableColumnCtx<T>,\n      parent: TableColumnCtx<T>,\n      updateColumnOrder: () => void\n    ) {\n      const array = unref(states._columns) || []\n      if (parent) {\n        parent.children.splice(\n          parent.children.findIndex((item) => item.id === column.id),\n          1\n        )\n        // fix #10699, delete parent.children immediately will trigger again\n        nextTick(() => {\n          if (parent.children?.length === 0) {\n            delete parent.children\n          }\n        })\n        states._columns.value = replaceColumn(array, parent)\n      } else {\n        const index = array.indexOf(column)\n        if (index > -1) {\n          array.splice(index, 1)\n          states._columns.value = array\n        }\n      }\n\n      const updateFnIndex = states.updateOrderFns.indexOf(updateColumnOrder)\n      updateFnIndex > -1 && states.updateOrderFns.splice(updateFnIndex, 1)\n\n      if (instance.$ready) {\n        instance.store.updateColumns() // hack for dynamics remove column\n        instance.store.scheduleLayout()\n      }\n    },\n\n    sort(states: StoreStates, options: Sort) {\n      const { prop, order, init } = options\n      if (prop) {\n        const column = unref(states.columns).find(\n          (column) => column.property === prop\n        )\n        if (column) {\n          column.order = order\n          instance.store.updateSort(column, prop, order)\n          instance.store.commit('changeSortCondition', { init })\n        }\n      }\n    },\n\n    changeSortCondition(states: StoreStates, options: Sort) {\n      // 修复 pr https://github.com/ElemeFE/element/pull/15012 导致的 bug\n      // https://github.com/element-plus/element-plus/pull/4640\n      const { sortingColumn, sortProp, sortOrder } = states\n      const columnValue = unref(sortingColumn),\n        propValue = unref(sortProp),\n        orderValue = unref(sortOrder)\n      if (isNull(orderValue)) {\n        states.sortingColumn.value = null\n        states.sortProp.value = null\n      }\n      const ignore = { filter: true }\n      instance.store.execQuery(ignore)\n\n      if (!options || !(options.silent || options.init)) {\n        instance.emit('sort-change', {\n          column: columnValue,\n          prop: propValue,\n          order: orderValue,\n        })\n      }\n\n      instance.store.updateTableScrollY()\n    },\n\n    filterChange(_states: StoreStates, options: Filter<T>) {\n      const { column, values, silent } = options\n      const newFilters = instance.store.updateFilters(column, values)\n      instance.store.execQuery()\n\n      if (!silent) {\n        instance.emit('filter-change', newFilters)\n      }\n      instance.store.updateTableScrollY()\n    },\n\n    toggleAllSelection() {\n      instance.store.toggleAllSelection()\n    },\n\n    rowSelectedChanged(_states, row: T) {\n      instance.store.toggleRowSelection(row)\n      instance.store.updateAllSelected()\n    },\n\n    setHoverRow(states: StoreStates, row: T) {\n      states.hoverRow.value = row\n    },\n\n    setCurrentRow(_states, row: T) {\n      instance.store.updateCurrentRow(row)\n    },\n  }\n  const commit = function (name: keyof typeof mutations, ...args) {\n    const mutations = instance.store.mutations\n    if (mutations[name]) {\n      mutations[name].apply(instance, [instance.store.states].concat(args))\n    } else {\n      throw new Error(`Action not found: ${name}`)\n    }\n  }\n  const updateTableScrollY = function () {\n    nextTick(() => instance.layout.updateScrollY.apply(instance.layout))\n  }\n  return {\n    ns,\n    ...watcher,\n    mutations,\n    commit,\n    updateTableScrollY,\n  }\n}\n\nexport default useStore\n\nclass HelperStore<T> {\n  Return = useStore<T>()\n}\n\ntype StoreFilter = Record<string, string[]>\ntype Store<T> = HelperStore<T>['Return']\nexport type { WatcherPropsData, Store, StoreFilter }\n"], "mappings": ";;;;;;;;;AAIA,SAASA,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACpC,OAAOD,KAAK,CAACE,GAAG,CAAEC,IAAI,IAAK;IACzB,IAAIC,EAAE;IACN,IAAID,IAAI,CAACE,EAAE,KAAKJ,MAAM,CAACI,EAAE,EAAE;MACzB,OAAOJ,MAAM;IACnB,CAAK,MAAM,IAAI,CAACG,EAAE,GAAGD,IAAI,CAACG,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,MAAM,EAAE;MAC5DJ,IAAI,CAACG,QAAQ,GAAGP,aAAa,CAACI,IAAI,CAACG,QAAQ,EAAEL,MAAM,CAAC;IAC1D;IACI,OAAOE,IAAI;EACf,CAAG,CAAC;AACJ;AACA,SAASK,UAAUA,CAACR,KAAK,EAAE;EACzBA,KAAK,CAACS,OAAO,CAAEN,IAAI,IAAK;IACtB,IAAIC,EAAE,EAAEM,EAAE;IACVP,IAAI,CAACQ,EAAE,GAAG,CAACP,EAAE,GAAGD,IAAI,CAACS,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGR,EAAE,CAACS,IAAI,CAACV,IAAI,CAAC;IACrE,IAAI,CAACO,EAAE,GAAGP,IAAI,CAACG,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,EAAE,CAACH,MAAM,EAAE;MACrDC,UAAU,CAACL,IAAI,CAACG,QAAQ,CAAC;IAC/B;EACA,CAAG,CAAC;EACFN,KAAK,CAACc,IAAI,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,CAACJ,EAAE,GAAGK,GAAG,CAACL,EAAE,CAAC;AAC3C;AACA,SAASM,QAAQA,CAAA,EAAG;EAClB,MAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,MAAMC,OAAO,GAAGC,UAAU,EAAE;EAC5B,MAAMC,EAAE,GAAGC,YAAY,CAAC,OAAO,CAAC;EAChC,MAAMC,SAAS,GAAG;IAChBC,OAAOA,CAACC,MAAM,EAAEC,IAAI,EAAE;MACpB,MAAMC,mBAAmB,GAAGC,KAAK,CAACH,MAAM,CAACI,KAAK,CAAC,KAAKH,IAAI;MACxDD,MAAM,CAACC,IAAI,CAACI,KAAK,GAAGJ,IAAI;MACxBD,MAAM,CAACI,KAAK,CAACC,KAAK,GAAGJ,IAAI;MACzBT,QAAQ,CAACc,KAAK,CAACC,SAAS,EAAE;MAC1Bf,QAAQ,CAACc,KAAK,CAACE,oBAAoB,EAAE;MACrChB,QAAQ,CAACc,KAAK,CAACG,gBAAgB,EAAE;MACjCjB,QAAQ,CAACc,KAAK,CAACI,cAAc,CAAClB,QAAQ,CAACc,KAAK,CAACN,MAAM,CAACW,gBAAgB,CAACN,KAAK,CAAC;MAC3E,IAAIF,KAAK,CAACH,MAAM,CAACY,gBAAgB,CAAC,EAAE;QAClCpB,QAAQ,CAACc,KAAK,CAACO,YAAY,EAAE;QAC7BrB,QAAQ,CAACc,KAAK,CAACQ,uBAAuB,EAAE;MAChD,CAAO,MAAM;QACL,IAAIZ,mBAAmB,EAAE;UACvBV,QAAQ,CAACc,KAAK,CAACS,cAAc,EAAE;QACzC,CAAS,MAAM;UACLvB,QAAQ,CAACc,KAAK,CAACU,cAAc,EAAE;QACzC;MACA;MACMxB,QAAQ,CAACc,KAAK,CAACW,iBAAiB,EAAE;MAClC,IAAIzB,QAAQ,CAAC0B,MAAM,EAAE;QACnB1B,QAAQ,CAACc,KAAK,CAACa,cAAc,EAAE;MACvC;IACA,CAAK;IACDC,YAAYA,CAACpB,MAAM,EAAEzB,MAAM,EAAE8C,MAAM,EAAEC,iBAAiB,EAAE;MACtD,MAAMhD,KAAK,GAAG6B,KAAK,CAACH,MAAM,CAACuB,QAAQ,CAAC;MACpC,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAI,CAACH,MAAM,EAAE;QACX/C,KAAK,CAACmD,IAAI,CAAClD,MAAM,CAAC;QAClBiD,UAAU,GAAGlD,KAAK;MAC1B,CAAO,MAAM;QACL,IAAI+C,MAAM,IAAI,CAACA,MAAM,CAACzC,QAAQ,EAAE;UAC9ByC,MAAM,CAACzC,QAAQ,GAAG,EAAE;QAC9B;QACQyC,MAAM,CAACzC,QAAQ,CAAC6C,IAAI,CAAClD,MAAM,CAAC;QAC5BiD,UAAU,GAAGnD,aAAa,CAACC,KAAK,EAAE+C,MAAM,CAAC;MACjD;MACMvC,UAAU,CAAC0C,UAAU,CAAC;MACtBxB,MAAM,CAACuB,QAAQ,CAAClB,KAAK,GAAGmB,UAAU;MAClCxB,MAAM,CAAC0B,cAAc,CAACD,IAAI,CAACH,iBAAiB,CAAC;MAC7C,IAAI/C,MAAM,CAACoD,IAAI,KAAK,WAAW,EAAE;QAC/B3B,MAAM,CAAC4B,UAAU,CAACvB,KAAK,GAAG9B,MAAM,CAACqD,UAAU;QAC3C5B,MAAM,CAACY,gBAAgB,CAACP,KAAK,GAAG9B,MAAM,CAACqC,gBAAgB;MAC/D;MACM,IAAIpB,QAAQ,CAAC0B,MAAM,EAAE;QACnB1B,QAAQ,CAACc,KAAK,CAACuB,aAAa,EAAE;QAC9BrC,QAAQ,CAACc,KAAK,CAACa,cAAc,EAAE;MACvC;IACA,CAAK;IACDG,iBAAiBA,CAACtB,MAAM,EAAEzB,MAAM,EAAE;MAChC,IAAIG,EAAE;MACN,MAAMoD,cAAc,GAAG,CAACpD,EAAE,GAAGH,MAAM,CAACW,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGR,EAAE,CAACS,IAAI,CAACZ,MAAM,CAAC;MACtF,IAAIuD,cAAc,KAAKvD,MAAM,CAACU,EAAE,EAC9B;MACFH,UAAU,CAACkB,MAAM,CAACuB,QAAQ,CAAClB,KAAK,CAAC;MACjC,IAAIb,QAAQ,CAAC0B,MAAM,EAAE;QACnB1B,QAAQ,CAACc,KAAK,CAACuB,aAAa,EAAE;MACtC;IACA,CAAK;IACDE,YAAYA,CAAC/B,MAAM,EAAEzB,MAAM,EAAE8C,MAAM,EAAEC,iBAAiB,EAAE;MACtD,MAAMhD,KAAK,GAAG6B,KAAK,CAACH,MAAM,CAACuB,QAAQ,CAAC,IAAI,EAAE;MAC1C,IAAIF,MAAM,EAAE;QACVA,MAAM,CAACzC,QAAQ,CAACoD,MAAM,CAACX,MAAM,CAACzC,QAAQ,CAACqD,SAAS,CAAExD,IAAI,IAAKA,IAAI,CAACE,EAAE,KAAKJ,MAAM,CAACI,EAAE,CAAC,EAAE,CAAC,CAAC;QACrFuD,QAAQ,CAAC,MAAM;UACb,IAAIxD,EAAE;UACN,IAAI,CAAC,CAACA,EAAE,GAAG2C,MAAM,CAACzC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,MAAM,MAAM,CAAC,EAAE;YAC/D,OAAOwC,MAAM,CAACzC,QAAQ;UAClC;QACA,CAAS,CAAC;QACFoB,MAAM,CAACuB,QAAQ,CAAClB,KAAK,GAAGhC,aAAa,CAACC,KAAK,EAAE+C,MAAM,CAAC;MAC5D,CAAO,MAAM;QACL,MAAMc,KAAK,GAAG7D,KAAK,CAAC8D,OAAO,CAAC7D,MAAM,CAAC;QACnC,IAAI4D,KAAK,GAAG,CAAC,CAAC,EAAE;UACd7D,KAAK,CAAC0D,MAAM,CAACG,KAAK,EAAE,CAAC,CAAC;UACtBnC,MAAM,CAACuB,QAAQ,CAAClB,KAAK,GAAG/B,KAAK;QACvC;MACA;MACM,MAAM+D,aAAa,GAAGrC,MAAM,CAAC0B,cAAc,CAACU,OAAO,CAACd,iBAAiB,CAAC;MACtEe,aAAa,GAAG,CAAC,CAAC,IAAIrC,MAAM,CAAC0B,cAAc,CAACM,MAAM,CAACK,aAAa,EAAE,CAAC,CAAC;MACpE,IAAI7C,QAAQ,CAAC0B,MAAM,EAAE;QACnB1B,QAAQ,CAACc,KAAK,CAACuB,aAAa,EAAE;QAC9BrC,QAAQ,CAACc,KAAK,CAACa,cAAc,EAAE;MACvC;IACA,CAAK;IACD/B,IAAIA,CAACY,MAAM,EAAEsC,OAAO,EAAE;MACpB,MAAM;QAAEC,IAAI;QAAEC,KAAK;QAAEC;MAAI,CAAE,GAAGH,OAAO;MACrC,IAAIC,IAAI,EAAE;QACR,MAAMhE,MAAM,GAAG4B,KAAK,CAACH,MAAM,CAAC0C,OAAO,CAAC,CAACC,IAAI,CAAEC,OAAO,IAAKA,OAAO,CAACC,QAAQ,KAAKN,IAAI,CAAC;QACjF,IAAIhE,MAAM,EAAE;UACVA,MAAM,CAACiE,KAAK,GAAGA,KAAK;UACpBhD,QAAQ,CAACc,KAAK,CAACwC,UAAU,CAACvE,MAAM,EAAEgE,IAAI,EAAEC,KAAK,CAAC;UAC9ChD,QAAQ,CAACc,KAAK,CAACyC,MAAM,CAAC,qBAAqB,EAAE;YAAEN;UAAI,CAAE,CAAC;QAChE;MACA;IACA,CAAK;IACDO,mBAAmBA,CAAChD,MAAM,EAAEsC,OAAO,EAAE;MACnC,MAAM;QAAEW,aAAa;QAAEC,QAAQ;QAAEC;MAAS,CAAE,GAAGnD,MAAM;MACrD,MAAMoD,WAAW,GAAGjD,KAAK,CAAC8C,aAAa,CAAC;QAAEI,SAAS,GAAGlD,KAAK,CAAC+C,QAAQ,CAAC;QAAEI,UAAU,GAAGnD,KAAK,CAACgD,SAAS,CAAC;MACpG,IAAII,MAAM,CAACD,UAAU,CAAC,EAAE;QACtBtD,MAAM,CAACiD,aAAa,CAAC5C,KAAK,GAAG,IAAI;QACjCL,MAAM,CAACkD,QAAQ,CAAC7C,KAAK,GAAG,IAAI;MACpC;MACM,MAAMmD,MAAM,GAAG;QAAEC,MAAM,EAAE;MAAI,CAAE;MAC/BjE,QAAQ,CAACc,KAAK,CAACC,SAAS,CAACiD,MAAM,CAAC;MAChC,IAAI,CAAClB,OAAO,IAAI,EAAEA,OAAO,CAACoB,MAAM,IAAIpB,OAAO,CAACG,IAAI,CAAC,EAAE;QACjDjD,QAAQ,CAACmE,IAAI,CAAC,aAAa,EAAE;UAC3BpF,MAAM,EAAE6E,WAAW;UACnBb,IAAI,EAAEc,SAAS;UACfb,KAAK,EAAEc;QACjB,CAAS,CAAC;MACV;MACM9D,QAAQ,CAACc,KAAK,CAACsD,kBAAkB,EAAE;IACzC,CAAK;IACDC,YAAYA,CAACC,OAAO,EAAExB,OAAO,EAAE;MAC7B,MAAM;QAAE/D,MAAM;QAAEwF,MAAM;QAAEL;MAAM,CAAE,GAAGpB,OAAO;MAC1C,MAAM0B,UAAU,GAAGxE,QAAQ,CAACc,KAAK,CAAC2D,aAAa,CAAC1F,MAAM,EAAEwF,MAAM,CAAC;MAC/DvE,QAAQ,CAACc,KAAK,CAACC,SAAS,EAAE;MAC1B,IAAI,CAACmD,MAAM,EAAE;QACXlE,QAAQ,CAACmE,IAAI,CAAC,eAAe,EAAEK,UAAU,CAAC;MAClD;MACMxE,QAAQ,CAACc,KAAK,CAACsD,kBAAkB,EAAE;IACzC,CAAK;IACDM,kBAAkBA,CAAA,EAAG;MACnB1E,QAAQ,CAACc,KAAK,CAAC4D,kBAAkB,EAAE;IACzC,CAAK;IACDC,kBAAkBA,CAACL,OAAO,EAAEM,GAAG,EAAE;MAC/B5E,QAAQ,CAACc,KAAK,CAAC+D,kBAAkB,CAACD,GAAG,CAAC;MACtC5E,QAAQ,CAACc,KAAK,CAACW,iBAAiB,EAAE;IACxC,CAAK;IACDqD,WAAWA,CAACtE,MAAM,EAAEoE,GAAG,EAAE;MACvBpE,MAAM,CAACuE,QAAQ,CAAClE,KAAK,GAAG+D,GAAG;IACjC,CAAK;IACDI,aAAaA,CAACV,OAAO,EAAEM,GAAG,EAAE;MAC1B5E,QAAQ,CAACc,KAAK,CAACmE,gBAAgB,CAACL,GAAG,CAAC;IAC1C;EACA,CAAG;EACD,MAAMrB,MAAM,GAAG,SAAAA,CAAS2B,IAAI,EAAE,GAAGC,IAAI,EAAE;IACrC,MAAMC,UAAU,GAAGpF,QAAQ,CAACc,KAAK,CAACR,SAAS;IAC3C,IAAI8E,UAAU,CAACF,IAAI,CAAC,EAAE;MACpBE,UAAU,CAACF,IAAI,CAAC,CAACG,KAAK,CAACrF,QAAQ,EAAE,CAACA,QAAQ,CAACc,KAAK,CAACN,MAAM,CAAC,CAAC8E,MAAM,CAACH,IAAI,CAAC,CAAC;IAC5E,CAAK,MAAM;MACL,MAAM,IAAII,KAAK,CAAC,qBAAqBL,IAAI,EAAE,CAAC;IAClD;EACA,CAAG;EACD,MAAMd,kBAAkB,GAAG,SAAAA,CAAA,EAAW;IACpC1B,QAAQ,CAAC,MAAM1C,QAAQ,CAACwF,MAAM,CAACC,aAAa,CAACJ,KAAK,CAACrF,QAAQ,CAACwF,MAAM,CAAC,CAAC;EACxE,CAAG;EACD,OAAO;IACLpF,EAAE;IACF,GAAGF,OAAO;IACVI,SAAS;IACTiD,MAAM;IACNa;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}