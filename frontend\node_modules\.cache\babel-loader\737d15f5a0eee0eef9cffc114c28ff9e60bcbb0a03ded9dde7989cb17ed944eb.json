{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { ref, computed, onMounted, reactive, nextTick } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { Refresh } from \"@element-plus/icons-vue\";\nimport QRCode from \"qrcodejs2\";\nexport default {\n  name: \"MyReservations\",\n  components: {\n    Refresh\n  },\n  setup() {\n    const store = useStore();\n    const router = useRouter();\n    const loading = ref(true);\n    const processing = ref(false);\n    const qrCodeRef = ref(null);\n    const checkInDialogVisible = ref(false);\n    const checkOutDialogVisible = ref(false);\n    const cancelDialogVisible = ref(false);\n    const selectedReservation = ref(null);\n    const filters = reactive({\n      status: \"\",\n      date: \"\"\n    });\n    const sortBy = ref(\"start_time\");\n\n    // 状态选项\n    const statusOptions = [{\n      value: \"pending\",\n      label: \"待签到\"\n    }, {\n      value: \"checked_in\",\n      label: \"已签到\"\n    }, {\n      value: \"completed\",\n      label: \"已完成\"\n    }, {\n      value: \"cancelled\",\n      label: \"已取消\"\n    }, {\n      value: \"timeout\",\n      label: \"已超时\"\n    }];\n\n    // 排序选项\n    const sortOptions = [{\n      value: \"start_time\",\n      label: \"按开始时间排序\"\n    }, {\n      value: \"created_at\",\n      label: \"按创建时间排序\"\n    }, {\n      value: \"status\",\n      label: \"按状态排序\"\n    }];\n\n    // 获取预约列表\n    const getReservations = async () => {\n      try {\n        loading.value = true;\n        await store.dispatch(\"seat/getMyReservations\");\n      } catch (error) {\n        ElMessage.error(\"获取预约列表失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 刷新预约列表\n    const refreshReservations = () => {\n      getReservations();\n    };\n\n    // 过滤后的预约列表\n    const filteredReservations = computed(() => {\n      let result = store.getters[\"seat/myReservations\"];\n\n      // 状态过滤\n      if (filters.status) {\n        result = result.filter(reservation => reservation.status === filters.status);\n      }\n\n      // 日期过滤\n      if (filters.date) {\n        const filterDate = new Date(filters.date);\n        filterDate.setHours(0, 0, 0, 0);\n        const nextDay = new Date(filterDate);\n        nextDay.setDate(nextDay.getDate() + 1);\n        result = result.filter(reservation => {\n          const startTime = new Date(reservation.start_time);\n          return startTime >= filterDate && startTime < nextDay;\n        });\n      }\n\n      // 排序\n      result = [...result].sort((a, b) => {\n        switch (sortBy.value) {\n          case \"created_at\":\n            return new Date(b.created_at) - new Date(a.created_at);\n          case \"status\":\n            return getStatusPriority(a.status) - getStatusPriority(b.status);\n          case \"start_time\":\n          default:\n            return new Date(a.start_time) - new Date(b.start_time);\n        }\n      });\n      return result;\n    });\n\n    // 获取状态优先级（用于排序）\n    const getStatusPriority = status => {\n      switch (status) {\n        case \"checked_in\":\n          return 1;\n        case \"pending\":\n          return 2;\n        case \"completed\":\n          return 3;\n        case \"cancelled\":\n          return 4;\n        case \"timeout\":\n          return 5;\n        default:\n          return 6;\n      }\n    };\n\n    // 处理过滤变化\n    const handleFilterChange = () => {\n      // 过滤逻辑已在计算属性中实现\n    };\n\n    // 处理排序变化\n    const handleSortChange = () => {\n      // 排序逻辑已在计算属性中实现\n    };\n\n    // 是否是活跃预约（待签到或已签到）\n    const isActiveReservation = reservation => {\n      return reservation.status === \"pending\" || reservation.status === \"checked_in\";\n    };\n\n    // 显示签到对话框\n    const showCheckInDialog = reservation => {\n      selectedReservation.value = reservation;\n      checkInDialogVisible.value = true;\n\n      // 生成二维码\n      nextTick(() => {\n        if (qrCodeRef.value) {\n          // 清空容器\n          qrCodeRef.value.innerHTML = \"\";\n\n          // 生成二维码\n          new QRCode(qrCodeRef.value, {\n            text: reservation.reservation_code,\n            width: 200,\n            height: 200,\n            colorDark: \"#000000\",\n            colorLight: \"#ffffff\",\n            correctLevel: QRCode.CorrectLevel.H\n          });\n        }\n      });\n    };\n\n    // 显示签退对话框\n    const showCheckOutDialog = reservation => {\n      selectedReservation.value = reservation;\n      checkOutDialogVisible.value = true;\n    };\n\n    // 显示取消对话框\n    const showCancelDialog = reservation => {\n      selectedReservation.value = reservation;\n      cancelDialogVisible.value = true;\n    };\n\n    // 显示预约详情\n    const showReservationDetail = async reservation => {\n      try {\n        // 获取预约详情\n        await store.dispatch(\"seat/getReservationById\", reservation.id);\n\n        // 跳转到预约详情页面\n        router.push({\n          path: `/seat/reservation/${reservation.id}`,\n          query: {\n            mode: \"view\"\n          }\n        });\n      } catch (error) {\n        ElMessage.error(\"获取预约详情失败\");\n      }\n    };\n\n    // 处理签到\n    const handleCheckIn = async () => {\n      if (!selectedReservation.value) return;\n      try {\n        processing.value = true;\n        await store.dispatch(\"seat/checkIn\", {\n          reservationId: selectedReservation.value.id\n        });\n        ElMessage.success(\"签到成功\");\n        checkInDialogVisible.value = false;\n        refreshReservations();\n      } catch (error) {\n        ElMessage.error(error.message || \"签到失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 处理签退\n    const handleCheckOut = async () => {\n      if (!selectedReservation.value) return;\n      try {\n        processing.value = true;\n        await store.dispatch(\"seat/checkOut\", {\n          reservationId: selectedReservation.value.id\n        });\n        ElMessage.success(\"签退成功\");\n        checkOutDialogVisible.value = false;\n        refreshReservations();\n      } catch (error) {\n        ElMessage.error(error.message || \"签退失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 处理取消预约\n    const handleCancel = async () => {\n      if (!selectedReservation.value) return;\n      try {\n        processing.value = true;\n        await store.dispatch(\"seat/cancelReservation\", {\n          reservationId: selectedReservation.value.id\n        });\n        ElMessage.success(\"预约已取消\");\n        cancelDialogVisible.value = false;\n        refreshReservations();\n      } catch (error) {\n        ElMessage.error(error.message || \"取消预约失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 获取预约状态类型\n    const getReservationStatusType = status => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getReservationStatusText = status => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化日期\n    const formatDate = dateString => {\n      if (!dateString) return \"\";\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`;\n    };\n\n    // 格式化日期时间\n    const formatDateTime = dateTimeString => {\n      if (!dateTimeString) return \"\";\n      const date = new Date(dateTimeString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 计算时长\n    const calculateDuration = (startTime, endTime) => {\n      if (!startTime || !endTime) return \"\";\n      const start = new Date(startTime);\n      const end = new Date(endTime);\n      const diffMs = end - start;\n      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffMins = Math.floor(diffMs % (1000 * 60 * 60) / (1000 * 60));\n      return `${diffHrs}小时${diffMins}分钟`;\n    };\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n    onMounted(() => {\n      getReservations();\n    });\n    return {\n      loading,\n      processing,\n      qrCodeRef,\n      filters,\n      sortBy,\n      statusOptions,\n      sortOptions,\n      checkInDialogVisible,\n      checkOutDialogVisible,\n      cancelDialogVisible,\n      selectedReservation,\n      filteredReservations,\n      refreshReservations,\n      handleFilterChange,\n      handleSortChange,\n      isActiveReservation,\n      showCheckInDialog,\n      showCheckOutDialog,\n      showCancelDialog,\n      showReservationDetail,\n      handleCheckIn,\n      handleCheckOut,\n      handleCancel,\n      getReservationStatusType,\n      getReservationStatusText,\n      formatDate,\n      formatDateTime,\n      calculateDuration\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "reactive", "nextTick", "useStore", "useRouter", "ElMessage", "Refresh", "QRCode", "name", "components", "setup", "store", "router", "loading", "processing", "qrCodeRef", "checkInDialogVisible", "checkOutDialogVisible", "cancelDialogVisible", "selectedReservation", "filters", "status", "date", "sortBy", "statusOptions", "value", "label", "sortOptions", "getReservations", "dispatch", "error", "refreshReservations", "filteredReservations", "result", "getters", "filter", "reservation", "filterDate", "Date", "setHours", "nextDay", "setDate", "getDate", "startTime", "start_time", "sort", "a", "b", "created_at", "getStatusPriority", "handleFilterChange", "handleSortChange", "isActiveReservation", "showCheckInDialog", "innerHTML", "text", "reservation_code", "width", "height", "colorDark", "colorLight", "correctLevel", "CorrectLevel", "H", "showCheckOutDialog", "showCancelDialog", "showReservationDetail", "id", "push", "path", "query", "mode", "handleCheckIn", "reservationId", "success", "message", "handleCheckOut", "handleCancel", "getReservationStatusType", "getReservationStatusText", "formatDate", "dateString", "getFullYear", "padZero", "getMonth", "formatDateTime", "dateTimeString", "getHours", "getMinutes", "calculateDuration", "endTime", "start", "end", "diffMs", "diffHrs", "Math", "floor", "diffMins", "num"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\MyReservations.vue"], "sourcesContent": ["<template>\n  <div class=\"my-reservations\">\n    <div class=\"page-header\">\n      <h2>我的预约</h2>\n      <div class=\"header-actions\">\n        <el-button type=\"primary\" @click=\"refreshReservations\">\n          <el-icon><Refresh /></el-icon>\n          刷新\n        </el-button>\n      </div>\n    </div>\n\n    <div class=\"filter-section\">\n      <el-card shadow=\"never\">\n        <div class=\"filter-container\">\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">状态：</span>\n            <el-select\n              v-model=\"filters.status\"\n              placeholder=\"全部状态\"\n              clearable\n              @change=\"handleFilterChange\"\n            >\n              <el-option\n                v-for=\"status in statusOptions\"\n                :key=\"status.value\"\n                :label=\"status.label\"\n                :value=\"status.value\"\n              />\n            </el-select>\n          </div>\n\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">日期：</span>\n            <el-date-picker\n              v-model=\"filters.date\"\n              type=\"date\"\n              placeholder=\"选择日期\"\n              format=\"YYYY-MM-DD\"\n              value-format=\"YYYY-MM-DD\"\n              @change=\"handleFilterChange\"\n            />\n          </div>\n\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">排序：</span>\n            <el-select v-model=\"sortBy\" placeholder=\"排序方式\" @change=\"handleSortChange\">\n              <el-option\n                v-for=\"option in sortOptions\"\n                :key=\"option.value\"\n                :label=\"option.label\"\n                :value=\"option.value\"\n              />\n            </el-select>\n          </div>\n        </div>\n      </el-card>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"3\" animated />\n      <el-skeleton :rows=\"3\" animated style=\"margin-top: 20px\" />\n    </div>\n\n    <div v-else-if=\"filteredReservations.length === 0\" class=\"empty-container\">\n      <el-empty description=\"没有找到符合条件的预约记录\" />\n    </div>\n\n    <div v-else class=\"reservation-list\">\n      <el-card\n        v-for=\"reservation in filteredReservations\"\n        :key=\"reservation.id\"\n        class=\"reservation-card\"\n        :class=\"{ 'reservation-active': isActiveReservation(reservation) }\"\n      >\n        <div class=\"reservation-header\">\n          <div class=\"reservation-status\">\n            <el-tag :type=\"getReservationStatusType(reservation.status)\">\n              {{ getReservationStatusText(reservation.status) }}\n            </el-tag>\n            <span class=\"reservation-id\">预约号: {{ reservation.id }}</span>\n          </div>\n          <div class=\"reservation-time\">\n            {{ formatDate(reservation.created_at) }}\n          </div>\n        </div>\n\n        <div class=\"reservation-content\">\n          <div class=\"seat-info\">\n            <h3>{{ reservation.seat.room.name }}</h3>\n            <p class=\"seat-location\">{{ reservation.seat.room.location }}</p>\n            <p class=\"seat-number\">\n              座位号: {{ reservation.seat.seat_number }}\n              <el-tag v-if=\"reservation.seat.is_power_outlet\" size=\"small\" effect=\"plain\"\n                >有电源</el-tag\n              >\n              <el-tag v-if=\"reservation.seat.is_window_seat\" size=\"small\" effect=\"plain\"\n                >靠窗</el-tag\n              >\n            </p>\n          </div>\n\n          <div class=\"time-info\">\n            <p class=\"time-label\">预约时间</p>\n            <p class=\"time-value\">\n              {{ formatDateTime(reservation.start_time) }} -\n              {{ formatDateTime(reservation.end_time) }}\n            </p>\n            <p class=\"time-duration\">\n              {{ calculateDuration(reservation.start_time, reservation.end_time) }}\n            </p>\n          </div>\n        </div>\n\n        <div class=\"reservation-footer\">\n          <template v-if=\"reservation.status === 'pending'\">\n            <el-button type=\"primary\" @click=\"showCheckInDialog(reservation)\">签到</el-button>\n            <el-button type=\"danger\" @click=\"showCancelDialog(reservation)\">取消预约</el-button>\n          </template>\n\n          <template v-else-if=\"reservation.status === 'checked_in'\">\n            <el-button type=\"success\" @click=\"showCheckOutDialog(reservation)\">签退</el-button>\n          </template>\n\n          <template v-else>\n            <el-button type=\"info\" @click=\"showReservationDetail(reservation)\">查看详情</el-button>\n          </template>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 签到对话框 -->\n    <el-dialog v-model=\"checkInDialogVisible\" title=\"座位签到\" width=\"400px\">\n      <div v-if=\"selectedReservation\" class=\"check-in-dialog\">\n        <div class=\"qr-code-container\">\n          <div class=\"qr-code\" ref=\"qrCodeRef\"></div>\n        </div>\n\n        <div class=\"check-in-info\">\n          <p>预约号: {{ selectedReservation.id }}</p>\n          <p>座位号: {{ selectedReservation.seat?.seat_number }}</p>\n          <p>签到码: {{ selectedReservation.reservation_code }}</p>\n          <p class=\"check-in-tip\">请使用自习室终端扫描二维码完成签到</p>\n        </div>\n\n        <div class=\"manual-check-in\">\n          <el-divider>或手动签到</el-divider>\n          <el-button type=\"primary\" @click=\"handleCheckIn\">手动签到</el-button>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 签退对话框 -->\n    <el-dialog v-model=\"checkOutDialogVisible\" title=\"座位签退\" width=\"400px\">\n      <div v-if=\"selectedReservation\" class=\"check-out-dialog\">\n        <p>您确定要签退座位吗？</p>\n        <p>座位号: {{ selectedReservation.seat?.seat_number }}</p>\n        <p>\n          预约时间: {{ formatDateTime(selectedReservation.start_time) }} -\n          {{ formatDateTime(selectedReservation.end_time) }}\n        </p>\n\n        <div class=\"dialog-footer\">\n          <el-button @click=\"checkOutDialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"handleCheckOut\" :loading=\"processing\"\n            >确认签退</el-button\n          >\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 取消预约对话框 -->\n    <el-dialog v-model=\"cancelDialogVisible\" title=\"取消预约\" width=\"400px\">\n      <div v-if=\"selectedReservation\" class=\"cancel-dialog\">\n        <p>您确定要取消此预约吗？</p>\n        <p>座位号: {{ selectedReservation.seat?.seat_number }}</p>\n        <p>\n          预约时间: {{ formatDateTime(selectedReservation.start_time) }} -\n          {{ formatDateTime(selectedReservation.end_time) }}\n        </p>\n\n        <div class=\"cancel-warning\">\n          <el-alert\n            title=\"取消预约提示\"\n            type=\"warning\"\n            description=\"距离预约开始时间不足30分钟取消，可能会影响您的信誉分。\"\n            show-icon\n            :closable=\"false\"\n          />\n        </div>\n\n        <div class=\"dialog-footer\">\n          <el-button @click=\"cancelDialogVisible = false\">返回</el-button>\n          <el-button type=\"danger\" @click=\"handleCancel\" :loading=\"processing\">确认取消</el-button>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted, reactive, nextTick } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { Refresh } from \"@element-plus/icons-vue\";\nimport QRCode from \"qrcodejs2\";\n\nexport default {\n  name: \"MyReservations\",\n  components: {\n    Refresh,\n  },\n  setup() {\n    const store = useStore();\n    const router = useRouter();\n\n    const loading = ref(true);\n    const processing = ref(false);\n    const qrCodeRef = ref(null);\n\n    const checkInDialogVisible = ref(false);\n    const checkOutDialogVisible = ref(false);\n    const cancelDialogVisible = ref(false);\n    const selectedReservation = ref(null);\n\n    const filters = reactive({\n      status: \"\",\n      date: \"\",\n    });\n\n    const sortBy = ref(\"start_time\");\n\n    // 状态选项\n    const statusOptions = [\n      { value: \"pending\", label: \"待签到\" },\n      { value: \"checked_in\", label: \"已签到\" },\n      { value: \"completed\", label: \"已完成\" },\n      { value: \"cancelled\", label: \"已取消\" },\n      { value: \"timeout\", label: \"已超时\" },\n    ];\n\n    // 排序选项\n    const sortOptions = [\n      { value: \"start_time\", label: \"按开始时间排序\" },\n      { value: \"created_at\", label: \"按创建时间排序\" },\n      { value: \"status\", label: \"按状态排序\" },\n    ];\n\n    // 获取预约列表\n    const getReservations = async () => {\n      try {\n        loading.value = true;\n        await store.dispatch(\"seat/getMyReservations\");\n      } catch (error) {\n        ElMessage.error(\"获取预约列表失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 刷新预约列表\n    const refreshReservations = () => {\n      getReservations();\n    };\n\n    // 过滤后的预约列表\n    const filteredReservations = computed(() => {\n      let result = store.getters[\"seat/myReservations\"];\n\n      // 状态过滤\n      if (filters.status) {\n        result = result.filter((reservation) => reservation.status === filters.status);\n      }\n\n      // 日期过滤\n      if (filters.date) {\n        const filterDate = new Date(filters.date);\n        filterDate.setHours(0, 0, 0, 0);\n\n        const nextDay = new Date(filterDate);\n        nextDay.setDate(nextDay.getDate() + 1);\n\n        result = result.filter((reservation) => {\n          const startTime = new Date(reservation.start_time);\n          return startTime >= filterDate && startTime < nextDay;\n        });\n      }\n\n      // 排序\n      result = [...result].sort((a, b) => {\n        switch (sortBy.value) {\n          case \"created_at\":\n            return new Date(b.created_at) - new Date(a.created_at);\n          case \"status\":\n            return getStatusPriority(a.status) - getStatusPriority(b.status);\n          case \"start_time\":\n          default:\n            return new Date(a.start_time) - new Date(b.start_time);\n        }\n      });\n\n      return result;\n    });\n\n    // 获取状态优先级（用于排序）\n    const getStatusPriority = (status) => {\n      switch (status) {\n        case \"checked_in\":\n          return 1;\n        case \"pending\":\n          return 2;\n        case \"completed\":\n          return 3;\n        case \"cancelled\":\n          return 4;\n        case \"timeout\":\n          return 5;\n        default:\n          return 6;\n      }\n    };\n\n    // 处理过滤变化\n    const handleFilterChange = () => {\n      // 过滤逻辑已在计算属性中实现\n    };\n\n    // 处理排序变化\n    const handleSortChange = () => {\n      // 排序逻辑已在计算属性中实现\n    };\n\n    // 是否是活跃预约（待签到或已签到）\n    const isActiveReservation = (reservation) => {\n      return reservation.status === \"pending\" || reservation.status === \"checked_in\";\n    };\n\n    // 显示签到对话框\n    const showCheckInDialog = (reservation) => {\n      selectedReservation.value = reservation;\n      checkInDialogVisible.value = true;\n\n      // 生成二维码\n      nextTick(() => {\n        if (qrCodeRef.value) {\n          // 清空容器\n          qrCodeRef.value.innerHTML = \"\";\n\n          // 生成二维码\n          new QRCode(qrCodeRef.value, {\n            text: reservation.reservation_code,\n            width: 200,\n            height: 200,\n            colorDark: \"#000000\",\n            colorLight: \"#ffffff\",\n            correctLevel: QRCode.CorrectLevel.H,\n          });\n        }\n      });\n    };\n\n    // 显示签退对话框\n    const showCheckOutDialog = (reservation) => {\n      selectedReservation.value = reservation;\n      checkOutDialogVisible.value = true;\n    };\n\n    // 显示取消对话框\n    const showCancelDialog = (reservation) => {\n      selectedReservation.value = reservation;\n      cancelDialogVisible.value = true;\n    };\n\n    // 显示预约详情\n    const showReservationDetail = async (reservation) => {\n      try {\n        // 获取预约详情\n        await store.dispatch(\"seat/getReservationById\", reservation.id);\n\n        // 跳转到预约详情页面\n        router.push({\n          path: `/seat/reservation/${reservation.id}`,\n          query: { mode: \"view\" },\n        });\n      } catch (error) {\n        ElMessage.error(\"获取预约详情失败\");\n      }\n    };\n\n    // 处理签到\n    const handleCheckIn = async () => {\n      if (!selectedReservation.value) return;\n\n      try {\n        processing.value = true;\n\n        await store.dispatch(\"seat/checkIn\", {\n          reservationId: selectedReservation.value.id,\n        });\n\n        ElMessage.success(\"签到成功\");\n        checkInDialogVisible.value = false;\n        refreshReservations();\n      } catch (error) {\n        ElMessage.error(error.message || \"签到失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 处理签退\n    const handleCheckOut = async () => {\n      if (!selectedReservation.value) return;\n\n      try {\n        processing.value = true;\n\n        await store.dispatch(\"seat/checkOut\", {\n          reservationId: selectedReservation.value.id,\n        });\n\n        ElMessage.success(\"签退成功\");\n        checkOutDialogVisible.value = false;\n        refreshReservations();\n      } catch (error) {\n        ElMessage.error(error.message || \"签退失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 处理取消预约\n    const handleCancel = async () => {\n      if (!selectedReservation.value) return;\n\n      try {\n        processing.value = true;\n\n        await store.dispatch(\"seat/cancelReservation\", {\n          reservationId: selectedReservation.value.id,\n        });\n\n        ElMessage.success(\"预约已取消\");\n        cancelDialogVisible.value = false;\n        refreshReservations();\n      } catch (error) {\n        ElMessage.error(error.message || \"取消预约失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 获取预约状态类型\n    const getReservationStatusType = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getReservationStatusText = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化日期\n    const formatDate = (dateString) => {\n      if (!dateString) return \"\";\n\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`;\n    };\n\n    // 格式化日期时间\n    const formatDateTime = (dateTimeString) => {\n      if (!dateTimeString) return \"\";\n\n      const date = new Date(dateTimeString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(\n        date.getDate()\n      )} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 计算时长\n    const calculateDuration = (startTime, endTime) => {\n      if (!startTime || !endTime) return \"\";\n\n      const start = new Date(startTime);\n      const end = new Date(endTime);\n      const diffMs = end - start;\n      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\n\n      return `${diffHrs}小时${diffMins}分钟`;\n    };\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n\n    onMounted(() => {\n      getReservations();\n    });\n\n    return {\n      loading,\n      processing,\n      qrCodeRef,\n      filters,\n      sortBy,\n      statusOptions,\n      sortOptions,\n      checkInDialogVisible,\n      checkOutDialogVisible,\n      cancelDialogVisible,\n      selectedReservation,\n      filteredReservations,\n      refreshReservations,\n      handleFilterChange,\n      handleSortChange,\n      isActiveReservation,\n      showCheckInDialog,\n      showCheckOutDialog,\n      showCancelDialog,\n      showReservationDetail,\n      handleCheckIn,\n      handleCheckOut,\n      handleCancel,\n      getReservationStatusType,\n      getReservationStatusText,\n      formatDate,\n      formatDateTime,\n      calculateDuration,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.my-reservations {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  h2 {\n    margin: 0;\n  }\n}\n\n.filter-section {\n  margin-bottom: 20px;\n\n  .filter-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 20px;\n\n    .filter-item {\n      display: flex;\n      align-items: center;\n\n      .filter-label {\n        margin-right: 10px;\n        white-space: nowrap;\n      }\n    }\n  }\n}\n\n.loading-container,\n.empty-container {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.reservation-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.reservation-card {\n  transition: transform 0.3s, box-shadow 0.3s;\n\n  &.reservation-active {\n    border-left: 4px solid #409eff;\n\n    &:hover {\n      transform: translateX(5px);\n    }\n  }\n\n  &:hover {\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n  }\n\n  .reservation-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n\n    .reservation-status {\n      display: flex;\n      align-items: center;\n      gap: 10px;\n\n      .reservation-id {\n        color: #909399;\n        font-size: 14px;\n      }\n    }\n\n    .reservation-time {\n      color: #909399;\n      font-size: 14px;\n    }\n  }\n\n  .reservation-content {\n    display: flex;\n    margin-bottom: 15px;\n\n    .seat-info {\n      flex: 1;\n\n      h3 {\n        margin: 0 0 5px 0;\n      }\n\n      .seat-location {\n        margin: 0 0 5px 0;\n        color: #606266;\n      }\n\n      .seat-number {\n        margin: 0;\n        display: flex;\n        align-items: center;\n        gap: 5px;\n      }\n    }\n\n    .time-info {\n      flex: 1;\n      text-align: right;\n\n      .time-label {\n        margin: 0 0 5px 0;\n        color: #909399;\n      }\n\n      .time-value {\n        margin: 0 0 5px 0;\n        font-weight: bold;\n      }\n\n      .time-duration {\n        margin: 0;\n        color: #606266;\n      }\n    }\n  }\n\n  .reservation-footer {\n    display: flex;\n    justify-content: flex-end;\n    gap: 10px;\n  }\n}\n\n.check-in-dialog {\n  .qr-code-container {\n    display: flex;\n    justify-content: center;\n    margin-bottom: 20px;\n\n    .qr-code {\n      padding: 10px;\n      background-color: #fff;\n      border-radius: 4px;\n      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n    }\n  }\n\n  .check-in-info {\n    text-align: center;\n    margin-bottom: 20px;\n\n    p {\n      margin: 5px 0;\n    }\n\n    .check-in-tip {\n      color: #909399;\n      font-size: 14px;\n      margin-top: 10px;\n    }\n  }\n\n  .manual-check-in {\n    text-align: center;\n    margin-top: 20px;\n  }\n}\n\n.check-out-dialog,\n.cancel-dialog {\n  p {\n    margin: 10px 0;\n  }\n\n  .cancel-warning {\n    margin: 20px 0;\n  }\n\n  .dialog-footer {\n    margin-top: 20px;\n    display: flex;\n    justify-content: flex-end;\n    gap: 10px;\n  }\n}\n</style>\n"], "mappings": ";;;AAyMA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAO,QAAS,KAAK;AAClE,SAASC,QAAO,QAAS,MAAM;AAC/B,SAASC,SAAQ,QAAS,YAAY;AACtC,SAASC,SAAQ,QAAS,cAAc;AACxC,SAASC,OAAM,QAAS,yBAAyB;AACjD,OAAOC,MAAK,MAAO,WAAW;AAE9B,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;IACVH;EACF,CAAC;EACDI,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIR,QAAQ,CAAC,CAAC;IACxB,MAAMS,MAAK,GAAIR,SAAS,CAAC,CAAC;IAE1B,MAAMS,OAAM,GAAIf,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMgB,UAAS,GAAIhB,GAAG,CAAC,KAAK,CAAC;IAC7B,MAAMiB,SAAQ,GAAIjB,GAAG,CAAC,IAAI,CAAC;IAE3B,MAAMkB,oBAAmB,GAAIlB,GAAG,CAAC,KAAK,CAAC;IACvC,MAAMmB,qBAAoB,GAAInB,GAAG,CAAC,KAAK,CAAC;IACxC,MAAMoB,mBAAkB,GAAIpB,GAAG,CAAC,KAAK,CAAC;IACtC,MAAMqB,mBAAkB,GAAIrB,GAAG,CAAC,IAAI,CAAC;IAErC,MAAMsB,OAAM,GAAInB,QAAQ,CAAC;MACvBoB,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE;IACR,CAAC,CAAC;IAEF,MAAMC,MAAK,GAAIzB,GAAG,CAAC,YAAY,CAAC;;IAEhC;IACA,MAAM0B,aAAY,GAAI,CACpB;MAAEC,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAM,CAAC,EAClC;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAM,CAAC,EACrC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAM,CAAC,EACpC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAM,CAAC,EACpC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAM,CAAC,CACnC;;IAED;IACA,MAAMC,WAAU,GAAI,CAClB;MAAEF,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAU,CAAC,EACzC;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAU,CAAC,EACzC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAC,CACpC;;IAED;IACA,MAAME,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAI;QACFf,OAAO,CAACY,KAAI,GAAI,IAAI;QACpB,MAAMd,KAAK,CAACkB,QAAQ,CAAC,wBAAwB,CAAC;MAChD,EAAE,OAAOC,KAAK,EAAE;QACdzB,SAAS,CAACyB,KAAK,CAAC,UAAU,CAAC;MAC7B,UAAU;QACRjB,OAAO,CAACY,KAAI,GAAI,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAMM,mBAAkB,GAAIA,CAAA,KAAM;MAChCH,eAAe,CAAC,CAAC;IACnB,CAAC;;IAED;IACA,MAAMI,oBAAmB,GAAIjC,QAAQ,CAAC,MAAM;MAC1C,IAAIkC,MAAK,GAAItB,KAAK,CAACuB,OAAO,CAAC,qBAAqB,CAAC;;MAEjD;MACA,IAAId,OAAO,CAACC,MAAM,EAAE;QAClBY,MAAK,GAAIA,MAAM,CAACE,MAAM,CAAEC,WAAW,IAAKA,WAAW,CAACf,MAAK,KAAMD,OAAO,CAACC,MAAM,CAAC;MAChF;;MAEA;MACA,IAAID,OAAO,CAACE,IAAI,EAAE;QAChB,MAAMe,UAAS,GAAI,IAAIC,IAAI,CAAClB,OAAO,CAACE,IAAI,CAAC;QACzCe,UAAU,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAE/B,MAAMC,OAAM,GAAI,IAAIF,IAAI,CAACD,UAAU,CAAC;QACpCG,OAAO,CAACC,OAAO,CAACD,OAAO,CAACE,OAAO,CAAC,IAAI,CAAC,CAAC;QAEtCT,MAAK,GAAIA,MAAM,CAACE,MAAM,CAAEC,WAAW,IAAK;UACtC,MAAMO,SAAQ,GAAI,IAAIL,IAAI,CAACF,WAAW,CAACQ,UAAU,CAAC;UAClD,OAAOD,SAAQ,IAAKN,UAAS,IAAKM,SAAQ,GAAIH,OAAO;QACvD,CAAC,CAAC;MACJ;;MAEA;MACAP,MAAK,GAAI,CAAC,GAAGA,MAAM,CAAC,CAACY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAClC,QAAQxB,MAAM,CAACE,KAAK;UAClB,KAAK,YAAY;YACf,OAAO,IAAIa,IAAI,CAACS,CAAC,CAACC,UAAU,IAAI,IAAIV,IAAI,CAACQ,CAAC,CAACE,UAAU,CAAC;UACxD,KAAK,QAAQ;YACX,OAAOC,iBAAiB,CAACH,CAAC,CAACzB,MAAM,IAAI4B,iBAAiB,CAACF,CAAC,CAAC1B,MAAM,CAAC;UAClE,KAAK,YAAY;UACjB;YACE,OAAO,IAAIiB,IAAI,CAACQ,CAAC,CAACF,UAAU,IAAI,IAAIN,IAAI,CAACS,CAAC,CAACH,UAAU,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,OAAOX,MAAM;IACf,CAAC,CAAC;;IAEF;IACA,MAAMgB,iBAAgB,GAAK5B,MAAM,IAAK;MACpC,QAAQA,MAAM;QACZ,KAAK,YAAY;UACf,OAAO,CAAC;QACV,KAAK,SAAS;UACZ,OAAO,CAAC;QACV,KAAK,WAAW;UACd,OAAO,CAAC;QACV,KAAK,WAAW;UACd,OAAO,CAAC;QACV,KAAK,SAAS;UACZ,OAAO,CAAC;QACV;UACE,OAAO,CAAC;MACZ;IACF,CAAC;;IAED;IACA,MAAM6B,kBAAiB,GAAIA,CAAA,KAAM;MAC/B;IAAA,CACD;;IAED;IACA,MAAMC,gBAAe,GAAIA,CAAA,KAAM;MAC7B;IAAA,CACD;;IAED;IACA,MAAMC,mBAAkB,GAAKhB,WAAW,IAAK;MAC3C,OAAOA,WAAW,CAACf,MAAK,KAAM,SAAQ,IAAKe,WAAW,CAACf,MAAK,KAAM,YAAY;IAChF,CAAC;;IAED;IACA,MAAMgC,iBAAgB,GAAKjB,WAAW,IAAK;MACzCjB,mBAAmB,CAACM,KAAI,GAAIW,WAAW;MACvCpB,oBAAoB,CAACS,KAAI,GAAI,IAAI;;MAEjC;MACAvB,QAAQ,CAAC,MAAM;QACb,IAAIa,SAAS,CAACU,KAAK,EAAE;UACnB;UACAV,SAAS,CAACU,KAAK,CAAC6B,SAAQ,GAAI,EAAE;;UAE9B;UACA,IAAI/C,MAAM,CAACQ,SAAS,CAACU,KAAK,EAAE;YAC1B8B,IAAI,EAAEnB,WAAW,CAACoB,gBAAgB;YAClCC,KAAK,EAAE,GAAG;YACVC,MAAM,EAAE,GAAG;YACXC,SAAS,EAAE,SAAS;YACpBC,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAEtD,MAAM,CAACuD,YAAY,CAACC;UACpC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMC,kBAAiB,GAAK5B,WAAW,IAAK;MAC1CjB,mBAAmB,CAACM,KAAI,GAAIW,WAAW;MACvCnB,qBAAqB,CAACQ,KAAI,GAAI,IAAI;IACpC,CAAC;;IAED;IACA,MAAMwC,gBAAe,GAAK7B,WAAW,IAAK;MACxCjB,mBAAmB,CAACM,KAAI,GAAIW,WAAW;MACvClB,mBAAmB,CAACO,KAAI,GAAI,IAAI;IAClC,CAAC;;IAED;IACA,MAAMyC,qBAAoB,GAAI,MAAO9B,WAAW,IAAK;MACnD,IAAI;QACF;QACA,MAAMzB,KAAK,CAACkB,QAAQ,CAAC,yBAAyB,EAAEO,WAAW,CAAC+B,EAAE,CAAC;;QAE/D;QACAvD,MAAM,CAACwD,IAAI,CAAC;UACVC,IAAI,EAAE,qBAAqBjC,WAAW,CAAC+B,EAAE,EAAE;UAC3CG,KAAK,EAAE;YAAEC,IAAI,EAAE;UAAO;QACxB,CAAC,CAAC;MACJ,EAAE,OAAOzC,KAAK,EAAE;QACdzB,SAAS,CAACyB,KAAK,CAAC,UAAU,CAAC;MAC7B;IACF,CAAC;;IAED;IACA,MAAM0C,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI,CAACrD,mBAAmB,CAACM,KAAK,EAAE;MAEhC,IAAI;QACFX,UAAU,CAACW,KAAI,GAAI,IAAI;QAEvB,MAAMd,KAAK,CAACkB,QAAQ,CAAC,cAAc,EAAE;UACnC4C,aAAa,EAAEtD,mBAAmB,CAACM,KAAK,CAAC0C;QAC3C,CAAC,CAAC;QAEF9D,SAAS,CAACqE,OAAO,CAAC,MAAM,CAAC;QACzB1D,oBAAoB,CAACS,KAAI,GAAI,KAAK;QAClCM,mBAAmB,CAAC,CAAC;MACvB,EAAE,OAAOD,KAAK,EAAE;QACdzB,SAAS,CAACyB,KAAK,CAACA,KAAK,CAAC6C,OAAM,IAAK,MAAM,CAAC;MAC1C,UAAU;QACR7D,UAAU,CAACW,KAAI,GAAI,KAAK;MAC1B;IACF,CAAC;;IAED;IACA,MAAMmD,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI,CAACzD,mBAAmB,CAACM,KAAK,EAAE;MAEhC,IAAI;QACFX,UAAU,CAACW,KAAI,GAAI,IAAI;QAEvB,MAAMd,KAAK,CAACkB,QAAQ,CAAC,eAAe,EAAE;UACpC4C,aAAa,EAAEtD,mBAAmB,CAACM,KAAK,CAAC0C;QAC3C,CAAC,CAAC;QAEF9D,SAAS,CAACqE,OAAO,CAAC,MAAM,CAAC;QACzBzD,qBAAqB,CAACQ,KAAI,GAAI,KAAK;QACnCM,mBAAmB,CAAC,CAAC;MACvB,EAAE,OAAOD,KAAK,EAAE;QACdzB,SAAS,CAACyB,KAAK,CAACA,KAAK,CAAC6C,OAAM,IAAK,MAAM,CAAC;MAC1C,UAAU;QACR7D,UAAU,CAACW,KAAI,GAAI,KAAK;MAC1B;IACF,CAAC;;IAED;IACA,MAAMoD,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/B,IAAI,CAAC1D,mBAAmB,CAACM,KAAK,EAAE;MAEhC,IAAI;QACFX,UAAU,CAACW,KAAI,GAAI,IAAI;QAEvB,MAAMd,KAAK,CAACkB,QAAQ,CAAC,wBAAwB,EAAE;UAC7C4C,aAAa,EAAEtD,mBAAmB,CAACM,KAAK,CAAC0C;QAC3C,CAAC,CAAC;QAEF9D,SAAS,CAACqE,OAAO,CAAC,OAAO,CAAC;QAC1BxD,mBAAmB,CAACO,KAAI,GAAI,KAAK;QACjCM,mBAAmB,CAAC,CAAC;MACvB,EAAE,OAAOD,KAAK,EAAE;QACdzB,SAAS,CAACyB,KAAK,CAACA,KAAK,CAAC6C,OAAM,IAAK,QAAQ,CAAC;MAC5C,UAAU;QACR7D,UAAU,CAACW,KAAI,GAAI,KAAK;MAC1B;IACF,CAAC;;IAED;IACA,MAAMqD,wBAAuB,GAAKzD,MAAM,IAAK;MAC3C,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ,OAAO,SAAS;QAClB,KAAK,YAAY;UACf,OAAO,SAAS;QAClB,KAAK,WAAW;UACd,OAAO,MAAM;QACf,KAAK,WAAW;UACd,OAAO,QAAQ;QACjB,KAAK,SAAS;UACZ,OAAO,QAAQ;QACjB;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAM0D,wBAAuB,GAAK1D,MAAM,IAAK;MAC3C,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ,OAAO,KAAK;QACd,KAAK,YAAY;UACf,OAAO,KAAK;QACd,KAAK,WAAW;UACd,OAAO,KAAK;QACd,KAAK,WAAW;UACd,OAAO,KAAK;QACd,KAAK,SAAS;UACZ,OAAO,KAAK;QACd;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAM2D,UAAS,GAAKC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAE1B,MAAM3D,IAAG,GAAI,IAAIgB,IAAI,CAAC2C,UAAU,CAAC;MACjC,OAAO,GAAG3D,IAAI,CAAC4D,WAAW,CAAC,CAAC,IAAIC,OAAO,CAAC7D,IAAI,CAAC8D,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAID,OAAO,CAAC7D,IAAI,CAACoB,OAAO,CAAC,CAAC,CAAC,EAAE;IAC3F,CAAC;;IAED;IACA,MAAM2C,cAAa,GAAKC,cAAc,IAAK;MACzC,IAAI,CAACA,cAAc,EAAE,OAAO,EAAE;MAE9B,MAAMhE,IAAG,GAAI,IAAIgB,IAAI,CAACgD,cAAc,CAAC;MACrC,OAAO,GAAGhE,IAAI,CAAC4D,WAAW,CAAC,CAAC,IAAIC,OAAO,CAAC7D,IAAI,CAAC8D,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAID,OAAO,CACrE7D,IAAI,CAACoB,OAAO,CAAC,CACf,CAAC,IAAIyC,OAAO,CAAC7D,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAAC,IAAIJ,OAAO,CAAC7D,IAAI,CAACkE,UAAU,CAAC,CAAC,CAAC,EAAE;IAC/D,CAAC;;IAED;IACA,MAAMC,iBAAgB,GAAIA,CAAC9C,SAAS,EAAE+C,OAAO,KAAK;MAChD,IAAI,CAAC/C,SAAQ,IAAK,CAAC+C,OAAO,EAAE,OAAO,EAAE;MAErC,MAAMC,KAAI,GAAI,IAAIrD,IAAI,CAACK,SAAS,CAAC;MACjC,MAAMiD,GAAE,GAAI,IAAItD,IAAI,CAACoD,OAAO,CAAC;MAC7B,MAAMG,MAAK,GAAID,GAAE,GAAID,KAAK;MAC1B,MAAMG,OAAM,GAAIC,IAAI,CAACC,KAAK,CAACH,MAAK,IAAK,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC,CAAC;MACrD,MAAMI,QAAO,GAAIF,IAAI,CAACC,KAAK,CAAEH,MAAK,IAAK,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC,IAAK,IAAG,GAAI,EAAE,CAAC,CAAC;MAEtE,OAAO,GAAGC,OAAO,KAAKG,QAAQ,IAAI;IACpC,CAAC;;IAED;IACA,SAASd,OAAOA,CAACe,GAAG,EAAE;MACpB,OAAOA,GAAE,GAAI,EAAC,GAAI,IAAIA,GAAG,EAAC,GAAIA,GAAG;IACnC;IAEAlG,SAAS,CAAC,MAAM;MACd4B,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,OAAO;MACLf,OAAO;MACPC,UAAU;MACVC,SAAS;MACTK,OAAO;MACPG,MAAM;MACNC,aAAa;MACbG,WAAW;MACXX,oBAAoB;MACpBC,qBAAqB;MACrBC,mBAAmB;MACnBC,mBAAmB;MACnBa,oBAAoB;MACpBD,mBAAmB;MACnBmB,kBAAkB;MAClBC,gBAAgB;MAChBC,mBAAmB;MACnBC,iBAAiB;MACjBW,kBAAkB;MAClBC,gBAAgB;MAChBC,qBAAqB;MACrBM,aAAa;MACbI,cAAc;MACdC,YAAY;MACZC,wBAAwB;MACxBC,wBAAwB;MACxBC,UAAU;MACVK,cAAc;MACdI;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}