{"ast": null, "code": "import { computed, onBeforeMount } from 'vue';\nimport { useGetDerivedNamespace } from '../use-namespace/index.mjs';\nimport { useIdInjection } from '../use-id/index.mjs';\nimport { isClient } from '@vueuse/core';\nconst usePopperContainerId = () => {\n  const namespace = useGetDerivedNamespace();\n  const idInjection = useIdInjection();\n  const id = computed(() => {\n    return `${namespace.value}-popper-container-${idInjection.prefix}`;\n  });\n  const selector = computed(() => `#${id.value}`);\n  return {\n    id,\n    selector\n  };\n};\nconst createContainer = id => {\n  const container = document.createElement(\"div\");\n  container.id = id;\n  document.body.appendChild(container);\n  return container;\n};\nconst usePopperContainer = () => {\n  const {\n    id,\n    selector\n  } = usePopperContainerId();\n  onBeforeMount(() => {\n    if (!isClient) return;\n    if (process.env.NODE_ENV === \"test\" || !document.body.querySelector(selector.value)) {\n      createContainer(id.value);\n    }\n  });\n  return {\n    id,\n    selector\n  };\n};\nexport { usePopperContainer, usePopperContainerId };", "map": {"version": 3, "names": ["usePopperContainerId", "namespace", "useGetDerivedNamespace", "idInjection", "useIdInjection", "id", "computed", "value", "prefix", "selector", "createContainer", "container", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "usePopperContainer", "onBeforeMount", "isClient", "process", "env", "NODE_ENV", "querySelector"], "sources": ["../../../../../packages/hooks/use-popper-container/index.ts"], "sourcesContent": ["import { computed, onBeforeMount } from 'vue'\nimport { isClient } from '@element-plus/utils'\nimport { useGetDerivedNamespace } from '../use-namespace'\nimport { useIdInjection } from '../use-id'\n\nexport const usePopperContainerId = () => {\n  const namespace = useGetDerivedNamespace()\n  const idInjection = useIdInjection()\n\n  const id = computed(() => {\n    return `${namespace.value}-popper-container-${idInjection.prefix}`\n  })\n  const selector = computed(() => `#${id.value}`)\n\n  return {\n    id,\n    selector,\n  }\n}\n\nconst createContainer = (id: string) => {\n  const container = document.createElement('div')\n  container.id = id\n  document.body.appendChild(container)\n  return container\n}\n\nexport const usePopperContainer = () => {\n  const { id, selector } = usePopperContainerId()\n  onBeforeMount(() => {\n    if (!isClient) return\n\n    // This is for bypassing the error that when under testing env, we often encounter\n    // document.body.innerHTML = '' situation\n    // for this we need to disable the caching since it's not really needed\n    if (\n      process.env.NODE_ENV === 'test' ||\n      !document.body.querySelector(selector.value)\n    ) {\n      createContainer(id.value)\n    }\n  })\n\n  return {\n    id,\n    selector,\n  }\n}\n"], "mappings": ";;;;AAIY,MAACA,oBAAoB,GAAGA,CAAA,KAAM;EACxC,MAAMC,SAAS,GAAGC,sBAAsB,EAAE;EAC1C,MAAMC,WAAW,GAAGC,cAAc,EAAE;EACpC,MAAMC,EAAE,GAAGC,QAAQ,CAAC,MAAM;IACxB,OAAO,GAAGL,SAAS,CAACM,KAAK,qBAAqBJ,WAAW,CAACK,MAAM,EAAE;EACtE,CAAG,CAAC;EACF,MAAMC,QAAQ,GAAGH,QAAQ,CAAC,MAAM,IAAID,EAAE,CAACE,KAAK,EAAE,CAAC;EAC/C,OAAO;IACLF,EAAE;IACFI;EACJ,CAAG;AACH;AACA,MAAMC,eAAe,GAAIL,EAAE,IAAK;EAC9B,MAAMM,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC/CF,SAAS,CAACN,EAAE,GAAGA,EAAE;EACjBO,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,SAAS,CAAC;EACpC,OAAOA,SAAS;AAClB,CAAC;AACW,MAACK,kBAAkB,GAAGA,CAAA,KAAM;EACtC,MAAM;IAAEX,EAAE;IAAEI;EAAQ,CAAE,GAAGT,oBAAoB,EAAE;EAC/CiB,aAAa,CAAC,MAAM;IAClB,IAAI,CAACC,QAAQ,EACX;IACF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAI,CAACT,QAAQ,CAACE,IAAI,CAACQ,aAAa,CAACb,QAAQ,CAACF,KAAK,CAAC,EAAE;MACnFG,eAAe,CAACL,EAAE,CAACE,KAAK,CAAC;IAC/B;EACA,CAAG,CAAC;EACF,OAAO;IACLF,EAAE;IACFI;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}