{"ast": null, "code": "import { ref, provide, inject, onMounted, unref, onBeforeUnmount } from 'vue';\nimport Collection from './collection2.mjs';\nimport CollectionItem from './collection-item.mjs';\nconst COLLECTION_ITEM_SIGN = `data-el-collection-item`;\nconst createCollectionWithScope = name => {\n  const COLLECTION_NAME = `El${name}Collection`;\n  const COLLECTION_ITEM_NAME = `${COLLECTION_NAME}Item`;\n  const COLLECTION_INJECTION_KEY = Symbol(COLLECTION_NAME);\n  const COLLECTION_ITEM_INJECTION_KEY = Symbol(COLLECTION_ITEM_NAME);\n  const ElCollection = {\n    ...Collection,\n    name: COLLECTION_NAME,\n    setup() {\n      const collectionRef = ref();\n      const itemMap = /* @__PURE__ */new Map();\n      const getItems = () => {\n        const collectionEl = unref(collectionRef);\n        if (!collectionEl) return [];\n        const orderedNodes = Array.from(collectionEl.querySelectorAll(`[${COLLECTION_ITEM_SIGN}]`));\n        const items = [...itemMap.values()];\n        return items.sort((a, b) => orderedNodes.indexOf(a.ref) - orderedNodes.indexOf(b.ref));\n      };\n      provide(COLLECTION_INJECTION_KEY, {\n        itemMap,\n        getItems,\n        collectionRef\n      });\n    }\n  };\n  const ElCollectionItem = {\n    ...CollectionItem,\n    name: COLLECTION_ITEM_NAME,\n    setup(_, {\n      attrs\n    }) {\n      const collectionItemRef = ref();\n      const collectionInjection = inject(COLLECTION_INJECTION_KEY, void 0);\n      provide(COLLECTION_ITEM_INJECTION_KEY, {\n        collectionItemRef\n      });\n      onMounted(() => {\n        const collectionItemEl = unref(collectionItemRef);\n        if (collectionItemEl) {\n          collectionInjection.itemMap.set(collectionItemEl, {\n            ref: collectionItemEl,\n            ...attrs\n          });\n        }\n      });\n      onBeforeUnmount(() => {\n        const collectionItemEl = unref(collectionItemRef);\n        collectionInjection.itemMap.delete(collectionItemEl);\n      });\n    }\n  };\n  return {\n    COLLECTION_INJECTION_KEY,\n    COLLECTION_ITEM_INJECTION_KEY,\n    ElCollection,\n    ElCollectionItem\n  };\n};\nexport { COLLECTION_ITEM_SIGN, createCollectionWithScope };", "map": {"version": 3, "names": ["COLLECTION_ITEM_SIGN", "createCollectionWithScope", "name", "COLLECTION_NAME", "COLLECTION_ITEM_NAME", "COLLECTION_INJECTION_KEY", "Symbol", "COLLECTION_ITEM_INJECTION_KEY", "ElCollection", "Collection", "setup", "collectionRef", "ref", "itemMap", "Map", "getItems", "collectionEl", "unref", "orderedNodes", "Array", "from", "querySelectorAll", "items", "values", "sort", "a", "b", "indexOf", "provide", "ElCollectionItem", "CollectionItem", "_", "attrs", "collectionItemRef", "collectionInjection", "inject", "onMounted", "collectionItemEl", "set", "onBeforeUnmount", "delete"], "sources": ["../../../../../../packages/components/collection/src/collection.ts"], "sourcesContent": ["import { inject, onBeforeUnmount, onMounted, provide, ref, unref } from 'vue'\nimport Collection from './collection.vue'\nimport CollectionItem from './collection-item.vue'\n\nimport type { InjectionKey, SetupContext } from 'vue'\nimport type {\n  ElCollectionInjectionContext,\n  ElCollectionItemInjectionContext,\n} from './tokens'\n\nexport const COLLECTION_ITEM_SIGN = `data-el-collection-item`\n\n// Make sure the first letter of name is capitalized\nexport const createCollectionWithScope = (name: string) => {\n  const COLLECTION_NAME = `El${name}Collection`\n  const COLLECTION_ITEM_NAME = `${COLLECTION_NAME}Item`\n  const COLLECTION_INJECTION_KEY: InjectionKey<ElCollectionInjectionContext> =\n    Symbol(COLLECTION_NAME)\n  const COLLECTION_ITEM_INJECTION_KEY: InjectionKey<ElCollectionItemInjectionContext> =\n    Symbol(COLLECTION_ITEM_NAME)\n\n  const ElCollection = {\n    ...Collection,\n    name: COLLECTION_NAME,\n    setup() {\n      const collectionRef = ref<HTMLElement>()\n      const itemMap: ElCollectionInjectionContext['itemMap'] = new Map()\n      const getItems = () => {\n        const collectionEl = unref(collectionRef)\n\n        if (!collectionEl) return []\n        const orderedNodes = Array.from(\n          collectionEl.querySelectorAll(`[${COLLECTION_ITEM_SIGN}]`)\n        )\n\n        const items = [...itemMap.values()]\n\n        return items.sort(\n          (a, b) => orderedNodes.indexOf(a.ref!) - orderedNodes.indexOf(b.ref!)\n        )\n      }\n\n      provide(COLLECTION_INJECTION_KEY, {\n        itemMap,\n        getItems,\n        collectionRef,\n      })\n    },\n  }\n\n  const ElCollectionItem = {\n    ...CollectionItem,\n    name: COLLECTION_ITEM_NAME,\n    setup(_: unknown, { attrs }: SetupContext) {\n      const collectionItemRef = ref<HTMLElement>()\n      const collectionInjection = inject(COLLECTION_INJECTION_KEY, undefined)!\n\n      provide(COLLECTION_ITEM_INJECTION_KEY, {\n        collectionItemRef,\n      })\n\n      onMounted(() => {\n        const collectionItemEl = unref(collectionItemRef)\n        if (collectionItemEl) {\n          collectionInjection.itemMap.set(collectionItemEl, {\n            ref: collectionItemEl,\n            ...attrs,\n          })\n        }\n      })\n\n      onBeforeUnmount(() => {\n        const collectionItemEl = unref(collectionItemRef)!\n        collectionInjection.itemMap.delete(collectionItemEl)\n      })\n    },\n  }\n\n  return {\n    COLLECTION_INJECTION_KEY,\n    COLLECTION_ITEM_INJECTION_KEY,\n    ElCollection,\n    ElCollectionItem,\n  }\n}\n"], "mappings": ";;;AAGY,MAACA,oBAAoB,GAAG,yBAAwB;AAChD,MAACC,yBAAyB,GAAIC,IAAI,IAAK;EACjD,MAAMC,eAAe,GAAG,KAAKD,IAAI,YAAY;EAC7C,MAAME,oBAAoB,GAAG,GAAGD,eAAe,MAAM;EACrD,MAAME,wBAAwB,GAAGC,MAAM,CAACH,eAAe,CAAC;EACxD,MAAMI,6BAA6B,GAAGD,MAAM,CAACF,oBAAoB,CAAC;EAClE,MAAMI,YAAY,GAAG;IACnB,GAAGC,UAAU;IACbP,IAAI,EAAEC,eAAe;IACrBO,KAAKA,CAAA,EAAG;MACN,MAAMC,aAAa,GAAGC,GAAG,EAAE;MAC3B,MAAMC,OAAO,kBAAmB,IAAIC,GAAG,EAAE;MACzC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;QACrB,MAAMC,YAAY,GAAGC,KAAK,CAACN,aAAa,CAAC;QACzC,IAAI,CAACK,YAAY,EACf,OAAO,EAAE;QACX,MAAME,YAAY,GAAGC,KAAK,CAACC,IAAI,CAACJ,YAAY,CAACK,gBAAgB,CAAC,IAAIrB,oBAAoB,GAAG,CAAC,CAAC;QAC3F,MAAMsB,KAAK,GAAG,CAAC,GAAGT,OAAO,CAACU,MAAM,EAAE,CAAC;QACnC,OAAOD,KAAK,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKR,YAAY,CAACS,OAAO,CAACF,CAAC,CAACb,GAAG,CAAC,GAAGM,YAAY,CAACS,OAAO,CAACD,CAAC,CAACd,GAAG,CAAC,CAAC;MAC9F,CAAO;MACDgB,OAAO,CAACvB,wBAAwB,EAAE;QAChCQ,OAAO;QACPE,QAAQ;QACRJ;MACR,CAAO,CAAC;IACR;EACA,CAAG;EACD,MAAMkB,gBAAgB,GAAG;IACvB,GAAGC,cAAc;IACjB5B,IAAI,EAAEE,oBAAoB;IAC1BM,KAAKA,CAACqB,CAAC,EAAE;MAAEC;IAAK,CAAE,EAAE;MAClB,MAAMC,iBAAiB,GAAGrB,GAAG,EAAE;MAC/B,MAAMsB,mBAAmB,GAAGC,MAAM,CAAC9B,wBAAwB,EAAE,KAAK,CAAC,CAAC;MACpEuB,OAAO,CAACrB,6BAA6B,EAAE;QACrC0B;MACR,CAAO,CAAC;MACFG,SAAS,CAAC,MAAM;QACd,MAAMC,gBAAgB,GAAGpB,KAAK,CAACgB,iBAAiB,CAAC;QACjD,IAAII,gBAAgB,EAAE;UACpBH,mBAAmB,CAACrB,OAAO,CAACyB,GAAG,CAACD,gBAAgB,EAAE;YAChDzB,GAAG,EAAEyB,gBAAgB;YACrB,GAAGL;UACf,CAAW,CAAC;QACZ;MACA,CAAO,CAAC;MACFO,eAAe,CAAC,MAAM;QACpB,MAAMF,gBAAgB,GAAGpB,KAAK,CAACgB,iBAAiB,CAAC;QACjDC,mBAAmB,CAACrB,OAAO,CAAC2B,MAAM,CAACH,gBAAgB,CAAC;MAC5D,CAAO,CAAC;IACR;EACA,CAAG;EACD,OAAO;IACLhC,wBAAwB;IACxBE,6BAA6B;IAC7BC,YAAY;IACZqB;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}