{"ast": null, "code": "import Empty from './src/empty2.mjs';\nexport { emptyProps } from './src/empty.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElEmpty = withInstall(Empty);\nexport { ElEmpty, ElEmpty as default };", "map": {"version": 3, "names": ["ElEmpty", "withInstall", "Empty"], "sources": ["../../../../../packages/components/empty/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Empty from './src/empty.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElEmpty: SFCWithInstall<typeof Empty> = withInstall(Empty)\nexport default ElEmpty\n\nexport * from './src/empty'\nexport type { EmptyInstance } from './src/instance'\n"], "mappings": ";;;AAEY,MAACA,OAAO,GAAGC,WAAW,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}