{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { componentSizes } from '../../../constants/size.mjs';\nconst tagProps = buildProps({\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"info\", \"warning\", \"danger\"],\n    default: \"primary\"\n  },\n  closable: Boolean,\n  disableTransitions: <PERSON><PERSON>an,\n  hit: Boolean,\n  color: String,\n  size: {\n    type: String,\n    values: componentSizes\n  },\n  effect: {\n    type: String,\n    values: [\"dark\", \"light\", \"plain\"],\n    default: \"light\"\n  },\n  round: Boolean\n});\nconst tagEmits = {\n  close: evt => evt instanceof MouseEvent,\n  click: evt => evt instanceof MouseEvent\n};\nexport { tagEmits, tagProps };", "map": {"version": 3, "names": ["tagProps", "buildProps", "type", "String", "values", "default", "closable", "Boolean", "disableTransitions", "hit", "color", "size", "componentSizes", "effect", "round", "tagEmits", "close", "evt", "MouseEvent", "click"], "sources": ["../../../../../../packages/components/tag/src/tag.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { componentSizes } from '@element-plus/constants'\nimport type Tag from './tag.vue'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const tagProps = buildProps({\n  /**\n   * @description type of Tag\n   */\n  type: {\n    type: String,\n    values: ['primary', 'success', 'info', 'warning', 'danger'],\n    default: 'primary',\n  },\n  /**\n   * @description whether Tag can be removed\n   */\n  closable: Boolean,\n  /**\n   * @description whether to disable animations\n   */\n  disableTransitions: Boolean,\n  /**\n   * @description whether Tag has a highlighted border\n   */\n  hit: Boolean,\n  /**\n   * @description background color of the Tag\n   */\n  color: String,\n  /**\n   * @description size of Tag\n   */\n  size: {\n    type: String,\n    values: componentSizes,\n  },\n  /**\n   * @description theme of Tag\n   */\n  effect: {\n    type: String,\n    values: ['dark', 'light', 'plain'],\n    default: 'light',\n  },\n  /**\n   * @description whether Tag is rounded\n   */\n  round: Boolean,\n} as const)\nexport type TagProps = ExtractPropTypes<typeof tagProps>\n\nexport const tagEmits = {\n  close: (evt: MouseEvent) => evt instanceof MouseEvent,\n  click: (evt: MouseEvent) => evt instanceof MouseEvent,\n}\nexport type TagEmits = typeof tagEmits\n\nexport type TagInstance = InstanceType<typeof Tag> & unknown\n"], "mappings": ";;AAEY,MAACA,QAAQ,GAAGC,UAAU,CAAC;EACjCC,IAAI,EAAE;IACJA,IAAI,EAAEC,MAAM;IACZC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC;IAC3DC,OAAO,EAAE;EACb,CAAG;EACDC,QAAQ,EAAEC,OAAO;EACjBC,kBAAkB,EAAED,OAAO;EAC3BE,GAAG,EAAEF,OAAO;EACZG,KAAK,EAAEP,MAAM;EACbQ,IAAI,EAAE;IACJT,IAAI,EAAEC,MAAM;IACZC,MAAM,EAAEQ;EACZ,CAAG;EACDC,MAAM,EAAE;IACNX,IAAI,EAAEC,MAAM;IACZC,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;IAClCC,OAAO,EAAE;EACb,CAAG;EACDS,KAAK,EAAEP;AACT,CAAC;AACW,MAACQ,QAAQ,GAAG;EACtBC,KAAK,EAAGC,GAAG,IAAKA,GAAG,YAAYC,UAAU;EACzCC,KAAK,EAAGF,GAAG,IAAKA,GAAG,YAAYC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}