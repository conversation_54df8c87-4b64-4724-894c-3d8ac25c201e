{"ast": null, "code": "import createMathOperation from './_createMathOperation.js';\n\n/**\n * Adds two numbers.\n *\n * @static\n * @memberOf _\n * @since 3.4.0\n * @category Math\n * @param {number} augend The first number in an addition.\n * @param {number} addend The second number in an addition.\n * @returns {number} Returns the total.\n * @example\n *\n * _.add(6, 4);\n * // => 10\n */\nvar add = createMathOperation(function (augend, addend) {\n  return augend + addend;\n}, 0);\nexport default add;", "map": {"version": 3, "names": ["createMathOperation", "add", "augend", "addend"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/add.js"], "sourcesContent": ["import createMathOperation from './_createMathOperation.js';\n\n/**\n * Adds two numbers.\n *\n * @static\n * @memberOf _\n * @since 3.4.0\n * @category Math\n * @param {number} augend The first number in an addition.\n * @param {number} addend The second number in an addition.\n * @returns {number} Returns the total.\n * @example\n *\n * _.add(6, 4);\n * // => 10\n */\nvar add = createMathOperation(function(augend, addend) {\n  return augend + addend;\n}, 0);\n\nexport default add;\n"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,2BAA2B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,GAAG,GAAGD,mBAAmB,CAAC,UAASE,MAAM,EAAEC,MAAM,EAAE;EACrD,OAAOD,MAAM,GAAGC,MAAM;AACxB,CAAC,EAAE,CAAC,CAAC;AAEL,eAAeF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}