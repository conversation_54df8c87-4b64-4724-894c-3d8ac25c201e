{"ast": null, "code": "import { ArrowRight } from '@element-plus/icons-vue';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst collapseItemProps = buildProps({\n  title: {\n    type: String,\n    default: \"\"\n  },\n  name: {\n    type: definePropType([String, Number]),\n    default: void 0\n  },\n  icon: {\n    type: iconPropType,\n    default: ArrowRight\n  },\n  disabled: Boolean\n});\nexport { collapseItemProps };", "map": {"version": 3, "names": ["collapseItemProps", "buildProps", "title", "type", "String", "default", "name", "definePropType", "Number", "icon", "iconPropType", "ArrowRight", "disabled", "Boolean"], "sources": ["../../../../../../packages/components/collapse/src/collapse-item.ts"], "sourcesContent": ["import { buildProps, definePropType, iconPropType } from '@element-plus/utils'\nimport { ArrowRight } from '@element-plus/icons-vue'\nimport type { ExtractPropTypes } from 'vue'\nimport type { CollapseActiveName } from './collapse'\n\nexport const collapseItemProps = buildProps({\n  /**\n   * @description title of the panel\n   */\n  title: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description unique identification of the panel\n   */\n  name: {\n    type: definePropType<CollapseActiveName>([String, Number]),\n    default: undefined,\n  },\n  /**\n   * @description icon of the collapse item\n   */\n  icon: {\n    type: iconPropType,\n    default: ArrowRight,\n  },\n  /**\n   * @description disable the collapse item\n   */\n  disabled: Boolean,\n} as const)\nexport type CollapseItemProps = ExtractPropTypes<typeof collapseItemProps>\n"], "mappings": ";;;AAEY,MAACA,iBAAiB,GAAGC,UAAU,CAAC;EAC1CC,KAAK,EAAE;IACLC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,IAAI,EAAE;IACJH,IAAI,EAAEI,cAAc,CAAC,CAACH,MAAM,EAAEI,MAAM,CAAC,CAAC;IACtCH,OAAO,EAAE,KAAK;EAClB,CAAG;EACDI,IAAI,EAAE;IACJN,IAAI,EAAEO,YAAY;IAClBL,OAAO,EAAEM;EACb,CAAG;EACDC,QAAQ,EAAEC;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}