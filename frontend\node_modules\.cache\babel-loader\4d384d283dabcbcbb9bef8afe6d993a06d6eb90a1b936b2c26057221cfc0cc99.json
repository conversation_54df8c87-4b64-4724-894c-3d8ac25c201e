{"ast": null, "code": "import Drawer from './src/drawer.mjs';\nexport { drawerEmits, drawerProps } from './src/drawer2.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElDrawer = withInstall(Drawer);\nexport { ElDrawer, ElDrawer as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "withInstall", "Drawer"], "sources": ["../../../../../packages/components/drawer/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Drawer from './src/drawer.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElDrawer: SFCWithInstall<typeof Drawer> = withInstall(Drawer)\nexport default ElDrawer\n\nexport * from './src/drawer'\n"], "mappings": ";;;AAEY,MAACA,QAAQ,GAAGC,WAAW,CAACC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}