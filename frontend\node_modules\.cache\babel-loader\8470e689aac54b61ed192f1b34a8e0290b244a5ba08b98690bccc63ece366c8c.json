{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, shallowRef, computed, onBeforeUnmount, provide, toRef, openBlock, createElementBlock, unref, createBlock, createSlots, withCtx, createVNode, mergeProps, renderSlot, createCommentVNode } from 'vue';\nimport { uploadContextKey } from './constants.mjs';\nimport UploadList from './upload-list2.mjs';\nimport UploadContent from './upload-content.mjs';\nimport { useHandlers } from './use-handlers.mjs';\nimport { uploadProps } from './upload2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nconst __default__ = defineComponent({\n  name: \"ElUpload\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: uploadProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const disabled = useFormDisabled();\n    const uploadRef = shallowRef();\n    const {\n      abort,\n      submit,\n      clearFiles,\n      uploadFiles,\n      handleStart,\n      handleError,\n      handleRemove,\n      handleSuccess,\n      handleProgress,\n      revokeFileObjectURL\n    } = useHandlers(props, uploadRef);\n    const isPictureCard = computed(() => props.listType === \"picture-card\");\n    const uploadContentProps = computed(() => ({\n      ...props,\n      fileList: uploadFiles.value,\n      onStart: handleStart,\n      onProgress: handleProgress,\n      onSuccess: handleSuccess,\n      onError: handleError,\n      onRemove: handleRemove\n    }));\n    onBeforeUnmount(() => {\n      uploadFiles.value.forEach(revokeFileObjectURL);\n    });\n    provide(uploadContextKey, {\n      accept: toRef(props, \"accept\")\n    });\n    expose({\n      abort,\n      submit,\n      clearFiles,\n      handleStart,\n      handleRemove\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", null, [unref(isPictureCard) && _ctx.showFileList ? (openBlock(), createBlock(UploadList, {\n        key: 0,\n        disabled: unref(disabled),\n        \"list-type\": _ctx.listType,\n        files: unref(uploadFiles),\n        crossorigin: _ctx.crossorigin,\n        \"handle-preview\": _ctx.onPreview,\n        onRemove: unref(handleRemove)\n      }, createSlots({\n        append: withCtx(() => [createVNode(UploadContent, mergeProps({\n          ref_key: \"uploadRef\",\n          ref: uploadRef\n        }, unref(uploadContentProps)), {\n          default: withCtx(() => [_ctx.$slots.trigger ? renderSlot(_ctx.$slots, \"trigger\", {\n            key: 0\n          }) : createCommentVNode(\"v-if\", true), !_ctx.$slots.trigger && _ctx.$slots.default ? renderSlot(_ctx.$slots, \"default\", {\n            key: 1\n          }) : createCommentVNode(\"v-if\", true)]),\n          _: 3\n        }, 16)]),\n        _: 2\n      }, [_ctx.$slots.file ? {\n        name: \"default\",\n        fn: withCtx(({\n          file,\n          index\n        }) => [renderSlot(_ctx.$slots, \"file\", {\n          file,\n          index\n        })])\n      } : void 0]), 1032, [\"disabled\", \"list-type\", \"files\", \"crossorigin\", \"handle-preview\", \"onRemove\"])) : createCommentVNode(\"v-if\", true), !unref(isPictureCard) || unref(isPictureCard) && !_ctx.showFileList ? (openBlock(), createBlock(UploadContent, mergeProps({\n        key: 1,\n        ref_key: \"uploadRef\",\n        ref: uploadRef\n      }, unref(uploadContentProps)), {\n        default: withCtx(() => [_ctx.$slots.trigger ? renderSlot(_ctx.$slots, \"trigger\", {\n          key: 0\n        }) : createCommentVNode(\"v-if\", true), !_ctx.$slots.trigger && _ctx.$slots.default ? renderSlot(_ctx.$slots, \"default\", {\n          key: 1\n        }) : createCommentVNode(\"v-if\", true)]),\n        _: 3\n      }, 16)) : createCommentVNode(\"v-if\", true), _ctx.$slots.trigger ? renderSlot(_ctx.$slots, \"default\", {\n        key: 2\n      }) : createCommentVNode(\"v-if\", true), renderSlot(_ctx.$slots, \"tip\"), !unref(isPictureCard) && _ctx.showFileList ? (openBlock(), createBlock(UploadList, {\n        key: 3,\n        disabled: unref(disabled),\n        \"list-type\": _ctx.listType,\n        files: unref(uploadFiles),\n        crossorigin: _ctx.crossorigin,\n        \"handle-preview\": _ctx.onPreview,\n        onRemove: unref(handleRemove)\n      }, createSlots({\n        _: 2\n      }, [_ctx.$slots.file ? {\n        name: \"default\",\n        fn: withCtx(({\n          file,\n          index\n        }) => [renderSlot(_ctx.$slots, \"file\", {\n          file,\n          index\n        })])\n      } : void 0]), 1032, [\"disabled\", \"list-type\", \"files\", \"crossorigin\", \"handle-preview\", \"onRemove\"])) : createCommentVNode(\"v-if\", true)]);\n    };\n  }\n});\nvar Upload = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"upload.vue\"]]);\nexport { Upload as default };", "map": {"version": 3, "names": ["name", "disabled", "useFormDisabled", "uploadRef", "shallowRef", "abort", "submit", "clearFiles", "uploadFiles", "handleStart", "handleError", "handleRemove", "handleSuccess", "handleProgress", "revokeFileObjectURL", "useHandlers", "props", "isPictureCard", "computed", "listType", "uploadContentProps", "fileList", "value", "onStart", "onProgress", "onSuccess", "onError", "onRemove", "onBeforeUnmount", "for<PERSON>ach", "provide", "uploadContextKey", "accept", "toRef", "expose", "_ctx", "_cache", "openBlock", "createElementBlock", "unref", "showFileList", "createBlock", "UploadList", "key"], "sources": ["../../../../../../packages/components/upload/src/upload.vue"], "sourcesContent": ["<template>\n  <div>\n    <upload-list\n      v-if=\"isPictureCard && showFileList\"\n      :disabled=\"disabled\"\n      :list-type=\"listType\"\n      :files=\"uploadFiles\"\n      :crossorigin=\"crossorigin\"\n      :handle-preview=\"onPreview\"\n      @remove=\"handleRemove\"\n    >\n      <template v-if=\"$slots.file\" #default=\"{ file, index }\">\n        <slot name=\"file\" :file=\"file\" :index=\"index\" />\n      </template>\n      <template #append>\n        <upload-content ref=\"uploadRef\" v-bind=\"uploadContentProps\">\n          <slot v-if=\"$slots.trigger\" name=\"trigger\" />\n          <slot v-if=\"!$slots.trigger && $slots.default\" />\n        </upload-content>\n      </template>\n    </upload-list>\n\n    <upload-content\n      v-if=\"!isPictureCard || (isPictureCard && !showFileList)\"\n      ref=\"uploadRef\"\n      v-bind=\"uploadContentProps\"\n    >\n      <slot v-if=\"$slots.trigger\" name=\"trigger\" />\n      <slot v-if=\"!$slots.trigger && $slots.default\" />\n    </upload-content>\n\n    <slot v-if=\"$slots.trigger\" />\n    <slot name=\"tip\" />\n    <upload-list\n      v-if=\"!isPictureCard && showFileList\"\n      :disabled=\"disabled\"\n      :list-type=\"listType\"\n      :files=\"uploadFiles\"\n      :crossorigin=\"crossorigin\"\n      :handle-preview=\"onPreview\"\n      @remove=\"handleRemove\"\n    >\n      <template v-if=\"$slots.file\" #default=\"{ file, index }\">\n        <slot name=\"file\" :file=\"file\" :index=\"index\" />\n      </template>\n    </upload-list>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, onBeforeUnmount, provide, shallowRef, toRef } from 'vue'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { uploadContextKey } from './constants'\nimport UploadList from './upload-list.vue'\nimport UploadContent from './upload-content.vue'\nimport { useHandlers } from './use-handlers'\nimport { uploadProps } from './upload'\n\nimport type {\n  UploadContentInstance,\n  UploadContentProps,\n} from './upload-content'\n\ndefineOptions({\n  name: 'ElUpload',\n})\n\nconst props = defineProps(uploadProps)\n\nconst disabled = useFormDisabled()\n\nconst uploadRef = shallowRef<UploadContentInstance>()\nconst {\n  abort,\n  submit,\n  clearFiles,\n  uploadFiles,\n  handleStart,\n  handleError,\n  handleRemove,\n  handleSuccess,\n  handleProgress,\n  revokeFileObjectURL,\n} = useHandlers(props, uploadRef)\n\nconst isPictureCard = computed(() => props.listType === 'picture-card')\n\nconst uploadContentProps = computed<UploadContentProps>(() => ({\n  ...props,\n  fileList: uploadFiles.value,\n  onStart: handleStart,\n  onProgress: handleProgress,\n  onSuccess: handleSuccess,\n  onError: handleError,\n  onRemove: handleRemove,\n}))\n\nonBeforeUnmount(() => {\n  uploadFiles.value.forEach(revokeFileObjectURL)\n})\n\nprovide(uploadContextKey, {\n  accept: toRef(props, 'accept'),\n})\n\ndefineExpose({\n  /** @description cancel upload request */\n  abort,\n  /** @description upload the file list manually */\n  submit,\n  /** @description clear the file list  */\n  clearFiles,\n  /** @description select the file manually */\n  handleStart,\n  /** @description remove the file manually */\n  handleRemove,\n})\n</script>\n"], "mappings": ";;;;;;;;;;mCA+Dc;EACZA,IAAM;AACR;;;;;;;;IAIA,MAAMC,QAAA,GAAWC,eAAgB;IAEjC,MAAMC,SAAA,GAAYC,UAAkC;IAC9C;MACJC,KAAA;MACAC,MAAA;MACAC,UAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,YAAA;MACAC,aAAA;MACAC,cAAA;MACAC;IAAA,CACF,GAAIC,WAAY,CAAAC,KAAA,EAAOb,SAAS;IAEhC,MAAMc,aAAgB,GAAAC,QAAA,CAAS,MAAMF,KAAA,CAAMG,QAAA,KAAa,cAAc;IAEhE,MAAAC,kBAAA,GAAqBF,QAAA,CAA6B,OAAO;MAC7D,GAAGF,KAAA;MACHK,QAAA,EAAUb,WAAY,CAAAc,KAAA;MACtBC,OAAS,EAAAd,WAAA;MACTe,UAAY,EAAAX,cAAA;MACZY,SAAW,EAAAb,aAAA;MACXc,OAAS,EAAAhB,WAAA;MACTiB,QAAU,EAAAhB;IAAA,CACV;IAEFiB,eAAA,CAAgB,MAAM;MACRpB,WAAA,CAAAc,KAAA,CAAMO,OAAA,CAAQf,mBAAmB;IAAA,CAC9C;IAEDgB,OAAA,CAAQC,gBAAkB;MACxBC,MAAA,EAAQC,KAAM,CAAAjB,KAAA,EAAO,QAAQ;IAAA,CAC9B;IAEYkB,MAAA;MAAA7B,KAAA;MAEXC,MAAA;MAAAC,UAAA;MAEAE,WAAA;MAAAE;IAAA,CAEA;IAAA,QAAAwB,IAAA,EAAAC,MAAA;MAEA,OAAAC,SAAA,IAAAC,kBAAA,eAAAC,KAAA,CAAAtB,aAAA,KAAAkB,IAAA,CAAAK,YAAA,IAAAH,SAAA,IAAAI,WAAA,CAAAC,UAAA;QAEAC,GAAA;QACD1C,QAAA,EAAAsC,KAAA,CAAAtC,QAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}