{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { unref, ref, onMounted, watchEffect, isRef } from 'vue';\nimport { isClient, unrefElement } from '@vueuse/core';\nimport { isNil } from 'lodash-unified';\nimport { arrow, computePosition } from '@floating-ui/dom';\nimport { keysOf } from '../../utils/objects.mjs';\nimport { buildProps } from '../../utils/vue/props/runtime.mjs';\nconst useFloatingProps = buildProps({});\nconst unrefReference = elRef => {\n  if (!isClient) return;\n  if (!elRef) return elRef;\n  const unrefEl = unrefElement(elRef);\n  if (unrefEl) return unrefEl;\n  return isRef(elRef) ? unrefEl : elRef;\n};\nconst getPositionDataWithUnit = (record, key) => {\n  const value = record == null ? void 0 : record[key];\n  return isNil(value) ? \"\" : `${value}px`;\n};\nconst useFloating = ({\n  middleware,\n  placement,\n  strategy\n}) => {\n  const referenceRef = ref();\n  const contentRef = ref();\n  const x = ref();\n  const y = ref();\n  const middlewareData = ref({});\n  const states = {\n    x,\n    y,\n    placement,\n    strategy,\n    middlewareData\n  };\n  const update = async () => {\n    if (!isClient) return;\n    const referenceEl = unrefReference(referenceRef);\n    const contentEl = unrefElement(contentRef);\n    if (!referenceEl || !contentEl) return;\n    const data = await computePosition(referenceEl, contentEl, {\n      placement: unref(placement),\n      strategy: unref(strategy),\n      middleware: unref(middleware)\n    });\n    keysOf(states).forEach(key => {\n      states[key].value = data[key];\n    });\n  };\n  onMounted(() => {\n    watchEffect(() => {\n      update();\n    });\n  });\n  return {\n    ...states,\n    update,\n    referenceRef,\n    contentRef\n  };\n};\nconst arrowMiddleware = ({\n  arrowRef,\n  padding\n}) => {\n  return {\n    name: \"arrow\",\n    options: {\n      element: arrowRef,\n      padding\n    },\n    fn(args) {\n      const arrowEl = unref(arrowRef);\n      if (!arrowEl) return {};\n      return arrow({\n        element: arrowEl,\n        padding\n      }).fn(args);\n    }\n  };\n};\nexport { arrowMiddleware, getPositionDataWithUnit, useFloating, useFloatingProps };", "map": {"version": 3, "names": ["useFloatingProps", "buildProps", "unrefReference", "elRef", "isClient", "unrefEl", "unrefElement", "isRef", "getPositionDataWithUnit", "record", "key", "value", "isNil", "useFloating", "middleware", "placement", "strategy", "referenceRef", "ref", "contentRef", "x", "y", "middlewareData", "states", "update", "referenceEl", "contentEl", "data", "computePosition", "unref", "keysOf", "for<PERSON>ach", "onMounted", "watchEffect", "arrowMiddleware", "arrowRef", "padding", "name", "options", "element", "fn", "args", "arrowEl", "arrow"], "sources": ["../../../../../packages/hooks/use-floating/index.ts"], "sourcesContent": ["import { isRef, onMounted, ref, unref, watchEffect } from 'vue'\nimport { unrefElement } from '@vueuse/core'\nimport { isNil } from 'lodash-unified'\nimport { arrow as arrowCore, computePosition } from '@floating-ui/dom'\nimport { buildProps, isClient, keysOf } from '@element-plus/utils'\n\nimport type { Ref, ToRefs } from 'vue'\nimport type {\n  ComputePositionReturn,\n  Middleware,\n  Placement,\n  SideObject,\n  Strategy,\n  VirtualElement,\n} from '@floating-ui/dom'\n\nexport const useFloatingProps = buildProps({} as const)\n\nexport type UseFloatingProps = ToRefs<{\n  middleware: Array<Middleware>\n  placement: Placement\n  strategy: Strategy\n}>\n\ntype ElementRef = Parameters<typeof unrefElement>['0']\n\nconst unrefReference = (\n  elRef: ElementRef | Ref<VirtualElement | undefined>\n) => {\n  if (!isClient) return\n  if (!elRef) return elRef\n  const unrefEl = unrefElement(elRef as ElementRef)\n  if (unrefEl) return unrefEl\n  return isRef(elRef) ? unrefEl : (elRef as VirtualElement)\n}\n\nexport const getPositionDataWithUnit = <T extends Record<string, number>>(\n  record: T | undefined,\n  key: keyof T\n) => {\n  const value = record?.[key]\n  return isNil(value) ? '' : `${value}px`\n}\n\nexport const useFloating = ({\n  middleware,\n  placement,\n  strategy,\n}: UseFloatingProps) => {\n  const referenceRef = ref<HTMLElement | VirtualElement>()\n  const contentRef = ref<HTMLElement>()\n  const x = ref<number>()\n  const y = ref<number>()\n  const middlewareData = ref<ComputePositionReturn['middlewareData']>({})\n\n  const states = {\n    x,\n    y,\n    placement,\n    strategy,\n    middlewareData,\n  } as const\n\n  const update = async () => {\n    if (!isClient) return\n\n    const referenceEl = unrefReference(referenceRef)\n    const contentEl = unrefElement(contentRef)\n    if (!referenceEl || !contentEl) return\n\n    const data = await computePosition(referenceEl, contentEl, {\n      placement: unref(placement),\n      strategy: unref(strategy),\n      middleware: unref(middleware),\n    })\n\n    keysOf(states).forEach((key) => {\n      states[key].value = data[key]\n    })\n  }\n\n  onMounted(() => {\n    watchEffect(() => {\n      update()\n    })\n  })\n\n  return {\n    ...states,\n    update,\n    referenceRef,\n    contentRef,\n  }\n}\n\nexport type ArrowMiddlewareProps = {\n  arrowRef: Ref<HTMLElement | null | undefined>\n  padding?: number | SideObject\n}\n\nexport const arrowMiddleware = ({\n  arrowRef,\n  padding,\n}: ArrowMiddlewareProps): Middleware => {\n  return {\n    name: 'arrow',\n    options: {\n      element: arrowRef,\n      padding,\n    },\n\n    fn(args) {\n      const arrowEl = unref(arrowRef)\n      if (!arrowEl) return {}\n\n      return arrowCore({\n        element: arrowEl,\n        padding,\n      }).fn(args)\n    },\n  }\n}\n"], "mappings": ";;;;;;;;AAKY,MAACA,gBAAgB,GAAGC,UAAU,CAAC,EAAE;AAC7C,MAAMC,cAAc,GAAIC,KAAK,IAAK;EAChC,IAAI,CAACC,QAAQ,EACX;EACF,IAAI,CAACD,KAAK,EACR,OAAOA,KAAK;EACd,MAAME,OAAO,GAAGC,YAAY,CAACH,KAAK,CAAC;EACnC,IAAIE,OAAO,EACT,OAAOA,OAAO;EAChB,OAAOE,KAAK,CAACJ,KAAK,CAAC,GAAGE,OAAO,GAAGF,KAAK;AACvC,CAAC;AACW,MAACK,uBAAuB,GAAGA,CAACC,MAAM,EAAEC,GAAG,KAAK;EACtD,MAAMC,KAAK,GAAGF,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,GAAG,CAAC;EACnD,OAAOE,KAAK,CAACD,KAAK,CAAC,GAAG,EAAE,GAAG,GAAGA,KAAK,IAAI;AACzC;AACY,MAACE,WAAW,GAAGA,CAAC;EAC1BC,UAAU;EACVC,SAAS;EACTC;AACF,CAAC,KAAK;EACJ,MAAMC,YAAY,GAAGC,GAAG,EAAE;EAC1B,MAAMC,UAAU,GAAGD,GAAG,EAAE;EACxB,MAAME,CAAC,GAAGF,GAAG,EAAE;EACf,MAAMG,CAAC,GAAGH,GAAG,EAAE;EACf,MAAMI,cAAc,GAAGJ,GAAG,CAAC,EAAE,CAAC;EAC9B,MAAMK,MAAM,GAAG;IACbH,CAAC;IACDC,CAAC;IACDN,SAAS;IACTC,QAAQ;IACRM;EACJ,CAAG;EACD,MAAME,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI,CAACpB,QAAQ,EACX;IACF,MAAMqB,WAAW,GAAGvB,cAAc,CAACe,YAAY,CAAC;IAChD,MAAMS,SAAS,GAAGpB,YAAY,CAACa,UAAU,CAAC;IAC1C,IAAI,CAACM,WAAW,IAAI,CAACC,SAAS,EAC5B;IACF,MAAMC,IAAI,GAAG,MAAMC,eAAe,CAACH,WAAW,EAAEC,SAAS,EAAE;MACzDX,SAAS,EAAEc,KAAK,CAACd,SAAS,CAAC;MAC3BC,QAAQ,EAAEa,KAAK,CAACb,QAAQ,CAAC;MACzBF,UAAU,EAAEe,KAAK,CAACf,UAAU;IAClC,CAAK,CAAC;IACFgB,MAAM,CAACP,MAAM,CAAC,CAACQ,OAAO,CAAErB,GAAG,IAAK;MAC9Ba,MAAM,CAACb,GAAG,CAAC,CAACC,KAAK,GAAGgB,IAAI,CAACjB,GAAG,CAAC;IACnC,CAAK,CAAC;EACN,CAAG;EACDsB,SAAS,CAAC,MAAM;IACdC,WAAW,CAAC,MAAM;MAChBT,MAAM,EAAE;IACd,CAAK,CAAC;EACN,CAAG,CAAC;EACF,OAAO;IACL,GAAGD,MAAM;IACTC,MAAM;IACNP,YAAY;IACZE;EACJ,CAAG;AACH;AACY,MAACe,eAAe,GAAGA,CAAC;EAC9BC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,OAAO;IACLC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE;MACPC,OAAO,EAAEJ,QAAQ;MACjBC;IACN,CAAK;IACDI,EAAEA,CAACC,IAAI,EAAE;MACP,MAAMC,OAAO,GAAGb,KAAK,CAACM,QAAQ,CAAC;MAC/B,IAAI,CAACO,OAAO,EACV,OAAO,EAAE;MACX,OAAOC,KAAS,CAAC;QACfJ,OAAO,EAAEG,OAAO;QAChBN;MACR,CAAO,CAAC,CAACI,EAAE,CAACC,IAAI,CAAC;IACjB;EACA,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}