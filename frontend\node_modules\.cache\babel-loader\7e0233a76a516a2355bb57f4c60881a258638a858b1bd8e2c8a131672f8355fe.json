{"ast": null, "code": "import { computed } from \"vue\";\nimport { useStore } from \"vuex\";\nimport AppHeader from \"./Header.vue\";\nimport AppSidebar from \"./Sidebar.vue\";\nimport AppFooter from \"./Footer.vue\";\nexport default {\n  name: \"AppLayout\",\n  components: {\n    AppHeader,\n    AppSidebar,\n    AppFooter\n  },\n  setup() {\n    const store = useStore();\n    const isLoggedIn = computed(() => store.getters[\"user/isLoggedIn\"]);\n    return {\n      isLoggedIn\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "useStore", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AppSidebar", "AppFooter", "name", "components", "setup", "store", "isLoggedIn", "getters"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Layout.vue"], "sourcesContent": ["<template>\n  <div class=\"app-layout\">\n    <app-header />\n    <div class=\"main-container\">\n      <app-sidebar v-if=\"isLoggedIn\" />\n      <div class=\"content-container\">\n        <router-view />\n      </div>\n    </div>\n    <app-footer />\n  </div>\n</template>\n\n<script>\nimport { computed } from \"vue\";\nimport { useStore } from \"vuex\";\nimport AppHeader from \"./Header.vue\";\nimport AppSidebar from \"./Sidebar.vue\";\nimport AppFooter from \"./Footer.vue\";\n\nexport default {\n  name: \"AppLayout\",\n  components: {\n    AppHeader,\n    AppSidebar,\n    AppFooter\n  },\n  setup() {\n    const store = useStore();\n    const isLoggedIn = computed(() => store.getters[\"user/isLoggedIn\"]);\n\n    return {\n      isLoggedIn\n    };\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-layout {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n}\n\n.main-container {\n  display: flex;\n  flex: 1;\n}\n\n.content-container {\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n}\n</style>\n"], "mappings": "AAcA,SAASA,QAAO,QAAS,KAAK;AAC9B,SAASC,QAAO,QAAS,MAAM;AAC/B,OAAOC,SAAQ,MAAO,cAAc;AACpC,OAAOC,UAAS,MAAO,eAAe;AACtC,OAAOC,SAAQ,MAAO,cAAc;AAEpC,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE;IACVJ,SAAS;IACTC,UAAU;IACVC;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIP,QAAQ,CAAC,CAAC;IACxB,MAAMQ,UAAS,GAAIT,QAAQ,CAAC,MAAMQ,KAAK,CAACE,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAEnE,OAAO;MACLD;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}