{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { computed } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRouter } from \"vue-router\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nimport { ArrowDown } from \"@element-plus/icons-vue\";\nexport default {\n  name: \"AppHeader\",\n  components: {\n    ArrowDown\n  },\n  setup() {\n    const store = useStore();\n    const router = useRouter();\n    const isLoggedIn = computed(() => store.getters[\"user/isLoggedIn\"]);\n    const userInfo = computed(() => store.getters[\"user/userInfo\"]);\n    const creditScoreClass = computed(() => {\n      const score = userInfo.value.creditScore || 100;\n      if (score >= 90) return \"score-excellent\";\n      if (score >= 70) return \"score-good\";\n      if (score >= 50) return \"score-warning\";\n      return \"score-danger\";\n    });\n    const handleCommand = async command => {\n      switch (command) {\n        case \"profile\":\n          router.push(\"/user/profile\");\n          break;\n        case \"reservations\":\n          router.push(\"/user/reservations\");\n          break;\n        case \"records\":\n          router.push(\"/user/records\");\n          break;\n        case \"logout\":\n          try {\n            await ElMessageBox.confirm(\"确定要退出登录吗？\", \"提示\", {\n              confirmButtonText: \"确定\",\n              cancelButtonText: \"取消\",\n              type: \"warning\"\n            });\n            await store.dispatch(\"user/logout\");\n            ElMessage.success(\"退出登录成功\");\n            router.push(\"/login\");\n          } catch (error) {\n            // 用户取消操作\n          }\n          break;\n      }\n    };\n    return {\n      isLoggedIn,\n      userInfo,\n      creditScoreClass,\n      handleCommand\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "useStore", "useRouter", "ElMessage", "ElMessageBox", "ArrowDown", "name", "components", "setup", "store", "router", "isLoggedIn", "getters", "userInfo", "creditScoreClass", "score", "value", "creditScore", "handleCommand", "command", "push", "confirm", "confirmButtonText", "cancelButtonText", "type", "dispatch", "success", "error"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Header.vue"], "sourcesContent": ["<template>\n  <header class=\"app-header\">\n    <div class=\"logo-container\">\n      <router-link to=\"/\">\n        <img src=\"@/assets/logo.png\" alt=\"图书馆自习室管理系统\" class=\"logo\" />\n        <h1 class=\"site-title\">图书馆自习室管理系统</h1>\n      </router-link>\n    </div>\n    <div class=\"nav-container\">\n      <template v-if=\"isLoggedIn\">\n        <el-dropdown trigger=\"click\" @command=\"handleCommand\">\n          <span class=\"user-info\">\n            {{ userInfo.studentIdHash || \"用户\" }}\n            <el-icon><arrow-down /></el-icon>\n          </span>\n          <template #dropdown>\n            <el-dropdown-menu>\n              <el-dropdown-item command=\"profile\">个人中心</el-dropdown-item>\n              <el-dropdown-item command=\"reservations\">我的预约</el-dropdown-item>\n              <el-dropdown-item command=\"records\">操作记录</el-dropdown-item>\n              <el-dropdown-item divided command=\"logout\">退出登录</el-dropdown-item>\n            </el-dropdown-menu>\n          </template>\n        </el-dropdown>\n        <div class=\"credit-score\">\n          信誉分:\n          <span :class=\"creditScoreClass\">{{ userInfo.creditScore || 100 }}</span>\n        </div>\n      </template>\n      <template v-else>\n        <el-button type=\"primary\" @click=\"$router.push('/login')\">登录</el-button>\n        <el-button @click=\"$router.push('/register')\">注册</el-button>\n      </template>\n    </div>\n  </header>\n</template>\n\n<script>\n  import { computed } from \"vue\";\n  import { useStore } from \"vuex\";\n  import { useRouter } from \"vue-router\";\n  import { ElMessage, ElMessageBox } from \"element-plus\";\n  import { ArrowDown } from \"@element-plus/icons-vue\";\n\n  export default {\n    name: \"AppHeader\",\n    components: {\n      ArrowDown,\n    },\n    setup() {\n      const store = useStore();\n      const router = useRouter();\n\n      const isLoggedIn = computed(() => store.getters[\"user/isLoggedIn\"]);\n      const userInfo = computed(() => store.getters[\"user/userInfo\"]);\n\n      const creditScoreClass = computed(() => {\n        const score = userInfo.value.creditScore || 100;\n        if (score >= 90) return \"score-excellent\";\n        if (score >= 70) return \"score-good\";\n        if (score >= 50) return \"score-warning\";\n        return \"score-danger\";\n      });\n\n      const handleCommand = async (command) => {\n        switch (command) {\n          case \"profile\":\n            router.push(\"/user/profile\");\n            break;\n          case \"reservations\":\n            router.push(\"/user/reservations\");\n            break;\n          case \"records\":\n            router.push(\"/user/records\");\n            break;\n          case \"logout\":\n            try {\n              await ElMessageBox.confirm(\"确定要退出登录吗？\", \"提示\", {\n                confirmButtonText: \"确定\",\n                cancelButtonText: \"取消\",\n                type: \"warning\",\n              });\n              await store.dispatch(\"user/logout\");\n              ElMessage.success(\"退出登录成功\");\n              router.push(\"/login\");\n            } catch (error) {\n              // 用户取消操作\n            }\n            break;\n        }\n      };\n\n      return {\n        isLoggedIn,\n        userInfo,\n        creditScoreClass,\n        handleCommand,\n      };\n    },\n  };\n</script>\n\n<style lang=\"scss\" scoped>\n  .app-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 0 20px;\n    height: 60px;\n    background-color: #fff;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    position: relative;\n    z-index: 10;\n  }\n\n  .logo-container {\n    display: flex;\n    align-items: center;\n\n    a {\n      display: flex;\n      align-items: center;\n      text-decoration: none;\n      color: inherit;\n    }\n\n    .logo {\n      height: 40px;\n      margin-right: 10px;\n    }\n\n    .site-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin: 0;\n    }\n  }\n\n  .nav-container {\n    display: flex;\n    align-items: center;\n    gap: 20px;\n  }\n\n  .user-info {\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n    font-weight: 500;\n\n    .el-icon {\n      margin-left: 5px;\n    }\n  }\n\n  .credit-score {\n    margin-left: 15px;\n    font-size: 0.9rem;\n\n    .score-excellent {\n      color: #67c23a;\n      font-weight: bold;\n    }\n\n    .score-good {\n      color: #409eff;\n      font-weight: bold;\n    }\n\n    .score-warning {\n      color: #e6a23c;\n      font-weight: bold;\n    }\n\n    .score-danger {\n      color: #f56c6c;\n      font-weight: bold;\n    }\n  }\n</style>\n"], "mappings": ";AAsCE,SAASA,QAAO,QAAS,KAAK;AAC9B,SAASC,QAAO,QAAS,MAAM;AAC/B,SAASC,SAAQ,QAAS,YAAY;AACtC,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAc;AACtD,SAASC,SAAQ,QAAS,yBAAyB;AAEnD,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIR,QAAQ,CAAC,CAAC;IACxB,MAAMS,MAAK,GAAIR,SAAS,CAAC,CAAC;IAE1B,MAAMS,UAAS,GAAIX,QAAQ,CAAC,MAAMS,KAAK,CAACG,OAAO,CAAC,iBAAiB,CAAC,CAAC;IACnE,MAAMC,QAAO,GAAIb,QAAQ,CAAC,MAAMS,KAAK,CAACG,OAAO,CAAC,eAAe,CAAC,CAAC;IAE/D,MAAME,gBAAe,GAAId,QAAQ,CAAC,MAAM;MACtC,MAAMe,KAAI,GAAIF,QAAQ,CAACG,KAAK,CAACC,WAAU,IAAK,GAAG;MAC/C,IAAIF,KAAI,IAAK,EAAE,EAAE,OAAO,iBAAiB;MACzC,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,YAAY;MACpC,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,eAAe;MACvC,OAAO,cAAc;IACvB,CAAC,CAAC;IAEF,MAAMG,aAAY,GAAI,MAAOC,OAAO,IAAK;MACvC,QAAQA,OAAO;QACb,KAAK,SAAS;UACZT,MAAM,CAACU,IAAI,CAAC,eAAe,CAAC;UAC5B;QACF,KAAK,cAAc;UACjBV,MAAM,CAACU,IAAI,CAAC,oBAAoB,CAAC;UACjC;QACF,KAAK,SAAS;UACZV,MAAM,CAACU,IAAI,CAAC,eAAe,CAAC;UAC5B;QACF,KAAK,QAAQ;UACX,IAAI;YACF,MAAMhB,YAAY,CAACiB,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE;cAC5CC,iBAAiB,EAAE,IAAI;cACvBC,gBAAgB,EAAE,IAAI;cACtBC,IAAI,EAAE;YACR,CAAC,CAAC;YACF,MAAMf,KAAK,CAACgB,QAAQ,CAAC,aAAa,CAAC;YACnCtB,SAAS,CAACuB,OAAO,CAAC,QAAQ,CAAC;YAC3BhB,MAAM,CAACU,IAAI,CAAC,QAAQ,CAAC;UACvB,EAAE,OAAOO,KAAK,EAAE;YACd;UAAA;UAEF;MACJ;IACF,CAAC;IAED,OAAO;MACLhB,UAAU;MACVE,QAAQ;MACRC,gBAAgB;MAChBI;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}