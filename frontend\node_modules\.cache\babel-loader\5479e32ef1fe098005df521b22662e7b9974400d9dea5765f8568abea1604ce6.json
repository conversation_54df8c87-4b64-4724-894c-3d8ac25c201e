{"ast": null, "code": "import { createVNode, mergeProps, isVNode } from 'vue';\nimport Table from '../table-grid.mjs';\nfunction _isSlot(s) {\n  return typeof s === \"function\" || Object.prototype.toString.call(s) === \"[object Object]\" && !isVNode(s);\n}\nconst MainTable = (props, {\n  slots\n}) => {\n  const {\n    mainTableRef,\n    ...rest\n  } = props;\n  return createVNode(Table, mergeProps({\n    \"ref\": mainTableRef\n  }, rest), _isSlot(slots) ? slots : {\n    default: () => [slots]\n  });\n};\nvar MainTable$1 = MainTable;\nexport { MainTable$1 as default };", "map": {"version": 3, "names": ["mainTableRef", "rest", "props", "createVNode", "Table", "mergeProps", "_isSlot", "slots", "default", "MainTable$1", "MainTable"], "sources": ["../../../../../../../packages/components/table-v2/src/renderers/main-table.tsx"], "sourcesContent": ["import Table from '../table-grid'\n\nimport type { FunctionalComponent, Ref } from 'vue'\nimport type { TableV2GridProps } from '../grid'\nimport type { TableGridInstance } from '../table-grid'\n\nexport type MainTableRendererProps = TableV2GridProps & {\n  mainTableRef: Ref<TableGridInstance | undefined>\n}\n\nconst MainTable: FunctionalComponent<MainTableRendererProps> = (\n  props: MainTableRendererProps,\n  { slots }\n) => {\n  const { mainTableRef, ...rest } = props\n  return (\n    <Table ref={mainTableRef} {...rest}>\n      {slots}\n    </Table>\n  )\n}\n\nexport default MainTable\n"], "mappings": ";;;;;;;AAUA,MAAM;EAEF;IACCA,YAAA;IACG,GAAAC;MAAAC,KAAA;SAAmBC,WAAA,CAAAC,KAAA,EAAAC,UAAA;IAAnB,KAAN,EAAAL;EACA,GAAAC,IAAA,GAAAK,OAAA,CAAAC,KAAA,IAAAA,KAAA;IAAAC,OACc,EAAAA,CAAA,MAAAD,KAAA;EADd;AAAA;AAAA,IAAAE,WAAA,GAAAC,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}