{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, useAttrs, ref, computed, watch, nextTick, onMounted, openBlock, createBlock, unref, withCtx, withDirectives, createElementBlock, normalizeClass, normalizeStyle, createVNode, createSlots, withModifiers, renderSlot, Fragment, renderList, toDisplayString, createElementVNode, withKeys, vModelText, createCommentVNode, isRef, vShow } from 'vue';\nimport { cloneDeep, debounce } from 'lodash-unified';\nimport { useCssVar, useResizeObserver, isClient } from '@vueuse/core';\nimport { ElCascaderPanel } from '../../cascader-panel/index.mjs';\nimport { ElInput } from '../../input/index.mjs';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { ElScrollbar } from '../../scrollbar/index.mjs';\nimport { ElTag } from '../../tag/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { CircleClose, ArrowDown, Check } from '@element-plus/icons-vue';\nimport { cascaderProps, cascaderEmits } from './cascader2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport ClickOutside from '../../../directives/click-outside/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useFormItem } from '../../form/src/hooks/use-form-item.mjs';\nimport { useEmptyValues } from '../../../hooks/use-empty-values/index.mjs';\nimport { useComposition } from '../../../hooks/use-composition/index.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { isPromise } from '@vue/shared';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { focusNode, getSibling } from '../../../utils/dom/aria.mjs';\nconst COMPONENT_NAME = \"ElCascader\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: cascaderProps,\n  emits: cascaderEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const popperOptions = {\n      modifiers: [{\n        name: \"arrowPosition\",\n        enabled: true,\n        phase: \"main\",\n        fn: ({\n          state\n        }) => {\n          const {\n            modifiersData,\n            placement\n          } = state;\n          if ([\"right\", \"left\", \"bottom\", \"top\"].includes(placement)) return;\n          modifiersData.arrow.x = 35;\n        },\n        requires: [\"arrow\"]\n      }]\n    };\n    const attrs = useAttrs();\n    let inputInitialHeight = 0;\n    let pressDeleteCount = 0;\n    const nsCascader = useNamespace(\"cascader\");\n    const nsInput = useNamespace(\"input\");\n    const {\n      t\n    } = useLocale();\n    const {\n      form,\n      formItem\n    } = useFormItem();\n    const {\n      valueOnClear\n    } = useEmptyValues(props);\n    const {\n      isComposing,\n      handleComposition\n    } = useComposition({\n      afterComposition(event) {\n        var _a;\n        const text = (_a = event.target) == null ? void 0 : _a.value;\n        handleInput(text);\n      }\n    });\n    const tooltipRef = ref(null);\n    const input = ref(null);\n    const tagWrapper = ref(null);\n    const cascaderPanelRef = ref(null);\n    const suggestionPanel = ref(null);\n    const popperVisible = ref(false);\n    const inputHover = ref(false);\n    const filtering = ref(false);\n    const filterFocus = ref(false);\n    const inputValue = ref(\"\");\n    const searchInputValue = ref(\"\");\n    const presentTags = ref([]);\n    const allPresentTags = ref([]);\n    const suggestions = ref([]);\n    const cascaderStyle = computed(() => {\n      return attrs.style;\n    });\n    const isDisabled = computed(() => props.disabled || (form == null ? void 0 : form.disabled));\n    const inputPlaceholder = computed(() => props.placeholder || t(\"el.cascader.placeholder\"));\n    const currentPlaceholder = computed(() => searchInputValue.value || presentTags.value.length > 0 || isComposing.value ? \"\" : inputPlaceholder.value);\n    const realSize = useFormSize();\n    const tagSize = computed(() => realSize.value === \"small\" ? \"small\" : \"default\");\n    const multiple = computed(() => !!props.props.multiple);\n    const readonly = computed(() => !props.filterable || multiple.value);\n    const searchKeyword = computed(() => multiple.value ? searchInputValue.value : inputValue.value);\n    const checkedNodes = computed(() => {\n      var _a;\n      return ((_a = cascaderPanelRef.value) == null ? void 0 : _a.checkedNodes) || [];\n    });\n    const clearBtnVisible = computed(() => {\n      if (!props.clearable || isDisabled.value || filtering.value || !inputHover.value) return false;\n      return !!checkedNodes.value.length;\n    });\n    const presentText = computed(() => {\n      const {\n        showAllLevels,\n        separator\n      } = props;\n      const nodes = checkedNodes.value;\n      return nodes.length ? multiple.value ? \"\" : nodes[0].calcText(showAllLevels, separator) : \"\";\n    });\n    const validateState = computed(() => (formItem == null ? void 0 : formItem.validateState) || \"\");\n    const checkedValue = computed({\n      get() {\n        return cloneDeep(props.modelValue);\n      },\n      set(val) {\n        const value = val != null ? val : valueOnClear.value;\n        emit(UPDATE_MODEL_EVENT, value);\n        emit(CHANGE_EVENT, value);\n        if (props.validateEvent) {\n          formItem == null ? void 0 : formItem.validate(\"change\").catch(err => debugWarn(err));\n        }\n      }\n    });\n    const cascaderKls = computed(() => {\n      return [nsCascader.b(), nsCascader.m(realSize.value), nsCascader.is(\"disabled\", isDisabled.value), attrs.class];\n    });\n    const cascaderIconKls = computed(() => {\n      return [nsInput.e(\"icon\"), \"icon-arrow-down\", nsCascader.is(\"reverse\", popperVisible.value)];\n    });\n    const inputClass = computed(() => {\n      return nsCascader.is(\"focus\", popperVisible.value || filterFocus.value);\n    });\n    const contentRef = computed(() => {\n      var _a, _b;\n      return (_b = (_a = tooltipRef.value) == null ? void 0 : _a.popperRef) == null ? void 0 : _b.contentRef;\n    });\n    const togglePopperVisible = visible => {\n      var _a, _b, _c;\n      if (isDisabled.value) return;\n      visible = visible != null ? visible : !popperVisible.value;\n      if (visible !== popperVisible.value) {\n        popperVisible.value = visible;\n        (_b = (_a = input.value) == null ? void 0 : _a.input) == null ? void 0 : _b.setAttribute(\"aria-expanded\", `${visible}`);\n        if (visible) {\n          updatePopperPosition();\n          nextTick((_c = cascaderPanelRef.value) == null ? void 0 : _c.scrollToExpandingNode);\n        } else if (props.filterable) {\n          syncPresentTextValue();\n        }\n        emit(\"visibleChange\", visible);\n      }\n    };\n    const updatePopperPosition = () => {\n      nextTick(() => {\n        var _a;\n        (_a = tooltipRef.value) == null ? void 0 : _a.updatePopper();\n      });\n    };\n    const hideSuggestionPanel = () => {\n      filtering.value = false;\n    };\n    const genTag = node => {\n      const {\n        showAllLevels,\n        separator\n      } = props;\n      return {\n        node,\n        key: node.uid,\n        text: node.calcText(showAllLevels, separator),\n        hitState: false,\n        closable: !isDisabled.value && !node.isDisabled,\n        isCollapseTag: false\n      };\n    };\n    const deleteTag = tag => {\n      var _a;\n      const node = tag.node;\n      node.doCheck(false);\n      (_a = cascaderPanelRef.value) == null ? void 0 : _a.calculateCheckedValue();\n      emit(\"removeTag\", node.valueByOption);\n    };\n    const calculatePresentTags = () => {\n      if (!multiple.value) return;\n      const nodes = checkedNodes.value;\n      const tags = [];\n      const allTags = [];\n      nodes.forEach(node => allTags.push(genTag(node)));\n      allPresentTags.value = allTags;\n      if (nodes.length) {\n        nodes.slice(0, props.maxCollapseTags).forEach(node => tags.push(genTag(node)));\n        const rest = nodes.slice(props.maxCollapseTags);\n        const restCount = rest.length;\n        if (restCount) {\n          if (props.collapseTags) {\n            tags.push({\n              key: -1,\n              text: `+ ${restCount}`,\n              closable: false,\n              isCollapseTag: true\n            });\n          } else {\n            rest.forEach(node => tags.push(genTag(node)));\n          }\n        }\n      }\n      presentTags.value = tags;\n    };\n    const calculateSuggestions = () => {\n      var _a, _b;\n      const {\n        filterMethod,\n        showAllLevels,\n        separator\n      } = props;\n      const res = (_b = (_a = cascaderPanelRef.value) == null ? void 0 : _a.getFlattedNodes(!props.props.checkStrictly)) == null ? void 0 : _b.filter(node => {\n        if (node.isDisabled) return false;\n        node.calcText(showAllLevels, separator);\n        return filterMethod(node, searchKeyword.value);\n      });\n      if (multiple.value) {\n        presentTags.value.forEach(tag => {\n          tag.hitState = false;\n        });\n        allPresentTags.value.forEach(tag => {\n          tag.hitState = false;\n        });\n      }\n      filtering.value = true;\n      suggestions.value = res;\n      updatePopperPosition();\n    };\n    const focusFirstNode = () => {\n      var _a;\n      let firstNode;\n      if (filtering.value && suggestionPanel.value) {\n        firstNode = suggestionPanel.value.$el.querySelector(`.${nsCascader.e(\"suggestion-item\")}`);\n      } else {\n        firstNode = (_a = cascaderPanelRef.value) == null ? void 0 : _a.$el.querySelector(`.${nsCascader.b(\"node\")}[tabindex=\"-1\"]`);\n      }\n      if (firstNode) {\n        firstNode.focus();\n        !filtering.value && firstNode.click();\n      }\n    };\n    const updateStyle = () => {\n      var _a, _b;\n      const inputInner = (_a = input.value) == null ? void 0 : _a.input;\n      const tagWrapperEl = tagWrapper.value;\n      const suggestionPanelEl = (_b = suggestionPanel.value) == null ? void 0 : _b.$el;\n      if (!isClient || !inputInner) return;\n      if (suggestionPanelEl) {\n        const suggestionList = suggestionPanelEl.querySelector(`.${nsCascader.e(\"suggestion-list\")}`);\n        suggestionList.style.minWidth = `${inputInner.offsetWidth}px`;\n      }\n      if (tagWrapperEl) {\n        const {\n          offsetHeight\n        } = tagWrapperEl;\n        const height = presentTags.value.length > 0 ? `${Math.max(offsetHeight, inputInitialHeight) - 2}px` : `${inputInitialHeight}px`;\n        inputInner.style.height = height;\n        updatePopperPosition();\n      }\n    };\n    const getCheckedNodes = leafOnly => {\n      var _a;\n      return (_a = cascaderPanelRef.value) == null ? void 0 : _a.getCheckedNodes(leafOnly);\n    };\n    const handleExpandChange = value => {\n      updatePopperPosition();\n      emit(\"expandChange\", value);\n    };\n    const handleKeyDown = e => {\n      if (isComposing.value) return;\n      switch (e.code) {\n        case EVENT_CODE.enter:\n        case EVENT_CODE.numpadEnter:\n          togglePopperVisible();\n          break;\n        case EVENT_CODE.down:\n          togglePopperVisible(true);\n          nextTick(focusFirstNode);\n          e.preventDefault();\n          break;\n        case EVENT_CODE.esc:\n          if (popperVisible.value === true) {\n            e.preventDefault();\n            e.stopPropagation();\n            togglePopperVisible(false);\n          }\n          break;\n        case EVENT_CODE.tab:\n          togglePopperVisible(false);\n          break;\n      }\n    };\n    const handleClear = () => {\n      var _a;\n      (_a = cascaderPanelRef.value) == null ? void 0 : _a.clearCheckedNodes();\n      if (!popperVisible.value && props.filterable) {\n        syncPresentTextValue();\n      }\n      togglePopperVisible(false);\n      emit(\"clear\");\n    };\n    const syncPresentTextValue = () => {\n      const {\n        value\n      } = presentText;\n      inputValue.value = value;\n      searchInputValue.value = value;\n    };\n    const handleSuggestionClick = node => {\n      var _a, _b;\n      const {\n        checked\n      } = node;\n      if (multiple.value) {\n        (_a = cascaderPanelRef.value) == null ? void 0 : _a.handleCheckChange(node, !checked, false);\n      } else {\n        !checked && ((_b = cascaderPanelRef.value) == null ? void 0 : _b.handleCheckChange(node, true, false));\n        togglePopperVisible(false);\n      }\n    };\n    const handleSuggestionKeyDown = e => {\n      const target = e.target;\n      const {\n        code\n      } = e;\n      switch (code) {\n        case EVENT_CODE.up:\n        case EVENT_CODE.down:\n          {\n            e.preventDefault();\n            const distance = code === EVENT_CODE.up ? -1 : 1;\n            focusNode(getSibling(target, distance, `.${nsCascader.e(\"suggestion-item\")}[tabindex=\"-1\"]`));\n            break;\n          }\n        case EVENT_CODE.enter:\n        case EVENT_CODE.numpadEnter:\n          target.click();\n          break;\n      }\n    };\n    const handleDelete = () => {\n      const tags = presentTags.value;\n      const lastTag = tags[tags.length - 1];\n      pressDeleteCount = searchInputValue.value ? 0 : pressDeleteCount + 1;\n      if (!lastTag || !pressDeleteCount || props.collapseTags && tags.length > 1) return;\n      if (lastTag.hitState) {\n        deleteTag(lastTag);\n      } else {\n        lastTag.hitState = true;\n      }\n    };\n    const handleFocus = e => {\n      const el = e.target;\n      const name = nsCascader.e(\"search-input\");\n      if (el.className === name) {\n        filterFocus.value = true;\n      }\n      emit(\"focus\", e);\n    };\n    const handleBlur = e => {\n      filterFocus.value = false;\n      emit(\"blur\", e);\n    };\n    const handleFilter = debounce(() => {\n      const {\n        value\n      } = searchKeyword;\n      if (!value) return;\n      const passed = props.beforeFilter(value);\n      if (isPromise(passed)) {\n        passed.then(calculateSuggestions).catch(() => {});\n      } else if (passed !== false) {\n        calculateSuggestions();\n      } else {\n        hideSuggestionPanel();\n      }\n    }, props.debounce);\n    const handleInput = (val, e) => {\n      !popperVisible.value && togglePopperVisible(true);\n      if (e == null ? void 0 : e.isComposing) return;\n      val ? handleFilter() : hideSuggestionPanel();\n    };\n    const getInputInnerHeight = inputInner => Number.parseFloat(useCssVar(nsInput.cssVarName(\"input-height\"), inputInner).value) - 2;\n    watch(filtering, updatePopperPosition);\n    watch([checkedNodes, isDisabled, () => props.collapseTags], calculatePresentTags);\n    watch(presentTags, () => {\n      nextTick(() => updateStyle());\n    });\n    watch(realSize, async () => {\n      await nextTick();\n      const inputInner = input.value.input;\n      inputInitialHeight = getInputInnerHeight(inputInner) || inputInitialHeight;\n      updateStyle();\n    });\n    watch(presentText, syncPresentTextValue, {\n      immediate: true\n    });\n    onMounted(() => {\n      const inputInner = input.value.input;\n      const inputInnerHeight = getInputInnerHeight(inputInner);\n      inputInitialHeight = inputInner.offsetHeight || inputInnerHeight;\n      useResizeObserver(inputInner, updateStyle);\n    });\n    expose({\n      getCheckedNodes,\n      cascaderPanelRef,\n      togglePopperVisible,\n      contentRef,\n      presentText\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTooltip), {\n        ref_key: \"tooltipRef\",\n        ref: tooltipRef,\n        visible: popperVisible.value,\n        teleported: _ctx.teleported,\n        \"popper-class\": [unref(nsCascader).e(\"dropdown\"), _ctx.popperClass],\n        \"popper-options\": popperOptions,\n        \"fallback-placements\": _ctx.fallbackPlacements,\n        \"stop-popper-mouse-event\": false,\n        \"gpu-acceleration\": false,\n        placement: _ctx.placement,\n        transition: `${unref(nsCascader).namespace.value}-zoom-in-top`,\n        effect: \"light\",\n        pure: \"\",\n        persistent: _ctx.persistent,\n        onHide: hideSuggestionPanel\n      }, {\n        default: withCtx(() => [withDirectives((openBlock(), createElementBlock(\"div\", {\n          class: normalizeClass(unref(cascaderKls)),\n          style: normalizeStyle(unref(cascaderStyle)),\n          onClick: () => togglePopperVisible(unref(readonly) ? void 0 : true),\n          onKeydown: handleKeyDown,\n          onMouseenter: $event => inputHover.value = true,\n          onMouseleave: $event => inputHover.value = false\n        }, [createVNode(unref(ElInput), {\n          ref_key: \"input\",\n          ref: input,\n          modelValue: inputValue.value,\n          \"onUpdate:modelValue\": $event => inputValue.value = $event,\n          placeholder: unref(currentPlaceholder),\n          readonly: unref(readonly),\n          disabled: unref(isDisabled),\n          \"validate-event\": false,\n          size: unref(realSize),\n          class: normalizeClass(unref(inputClass)),\n          tabindex: unref(multiple) && _ctx.filterable && !unref(isDisabled) ? -1 : void 0,\n          onCompositionstart: unref(handleComposition),\n          onCompositionupdate: unref(handleComposition),\n          onCompositionend: unref(handleComposition),\n          onFocus: handleFocus,\n          onBlur: handleBlur,\n          onInput: handleInput\n        }, createSlots({\n          suffix: withCtx(() => [unref(clearBtnVisible) ? (openBlock(), createBlock(unref(ElIcon), {\n            key: \"clear\",\n            class: normalizeClass([unref(nsInput).e(\"icon\"), \"icon-circle-close\"]),\n            onClick: withModifiers(handleClear, [\"stop\"])\n          }, {\n            default: withCtx(() => [createVNode(unref(CircleClose))]),\n            _: 1\n          }, 8, [\"class\", \"onClick\"])) : (openBlock(), createBlock(unref(ElIcon), {\n            key: \"arrow-down\",\n            class: normalizeClass(unref(cascaderIconKls)),\n            onClick: withModifiers($event => togglePopperVisible(), [\"stop\"])\n          }, {\n            default: withCtx(() => [createVNode(unref(ArrowDown))]),\n            _: 1\n          }, 8, [\"class\", \"onClick\"]))]),\n          _: 2\n        }, [_ctx.$slots.prefix ? {\n          name: \"prefix\",\n          fn: withCtx(() => [renderSlot(_ctx.$slots, \"prefix\")])\n        } : void 0]), 1032, [\"modelValue\", \"onUpdate:modelValue\", \"placeholder\", \"readonly\", \"disabled\", \"size\", \"class\", \"tabindex\", \"onCompositionstart\", \"onCompositionupdate\", \"onCompositionend\"]), unref(multiple) ? (openBlock(), createElementBlock(\"div\", {\n          key: 0,\n          ref_key: \"tagWrapper\",\n          ref: tagWrapper,\n          class: normalizeClass([unref(nsCascader).e(\"tags\"), unref(nsCascader).is(\"validate\", Boolean(unref(validateState)))])\n        }, [(openBlock(true), createElementBlock(Fragment, null, renderList(presentTags.value, tag => {\n          return openBlock(), createBlock(unref(ElTag), {\n            key: tag.key,\n            type: _ctx.tagType,\n            size: unref(tagSize),\n            effect: _ctx.tagEffect,\n            hit: tag.hitState,\n            closable: tag.closable,\n            \"disable-transitions\": \"\",\n            onClose: $event => deleteTag(tag)\n          }, {\n            default: withCtx(() => [tag.isCollapseTag === false ? (openBlock(), createElementBlock(\"span\", {\n              key: 0\n            }, toDisplayString(tag.text), 1)) : (openBlock(), createBlock(unref(ElTooltip), {\n              key: 1,\n              disabled: popperVisible.value || !_ctx.collapseTagsTooltip,\n              \"fallback-placements\": [\"bottom\", \"top\", \"right\", \"left\"],\n              placement: \"bottom\",\n              effect: \"light\"\n            }, {\n              default: withCtx(() => [createElementVNode(\"span\", null, toDisplayString(tag.text), 1)]),\n              content: withCtx(() => [createElementVNode(\"div\", {\n                class: normalizeClass(unref(nsCascader).e(\"collapse-tags\"))\n              }, [(openBlock(true), createElementBlock(Fragment, null, renderList(allPresentTags.value.slice(_ctx.maxCollapseTags), (tag2, idx) => {\n                return openBlock(), createElementBlock(\"div\", {\n                  key: idx,\n                  class: normalizeClass(unref(nsCascader).e(\"collapse-tag\"))\n                }, [(openBlock(), createBlock(unref(ElTag), {\n                  key: tag2.key,\n                  class: \"in-tooltip\",\n                  type: _ctx.tagType,\n                  size: unref(tagSize),\n                  effect: _ctx.tagEffect,\n                  hit: tag2.hitState,\n                  closable: tag2.closable,\n                  \"disable-transitions\": \"\",\n                  onClose: $event => deleteTag(tag2)\n                }, {\n                  default: withCtx(() => [createElementVNode(\"span\", null, toDisplayString(tag2.text), 1)]),\n                  _: 2\n                }, 1032, [\"type\", \"size\", \"effect\", \"hit\", \"closable\", \"onClose\"]))], 2);\n              }), 128))], 2)]),\n              _: 2\n            }, 1032, [\"disabled\"]))]),\n            _: 2\n          }, 1032, [\"type\", \"size\", \"effect\", \"hit\", \"closable\", \"onClose\"]);\n        }), 128)), _ctx.filterable && !unref(isDisabled) ? withDirectives((openBlock(), createElementBlock(\"input\", {\n          key: 0,\n          \"onUpdate:modelValue\": $event => searchInputValue.value = $event,\n          type: \"text\",\n          class: normalizeClass(unref(nsCascader).e(\"search-input\")),\n          placeholder: unref(presentText) ? \"\" : unref(inputPlaceholder),\n          onInput: e => handleInput(searchInputValue.value, e),\n          onClick: withModifiers($event => togglePopperVisible(true), [\"stop\"]),\n          onKeydown: withKeys(handleDelete, [\"delete\"]),\n          onCompositionstart: unref(handleComposition),\n          onCompositionupdate: unref(handleComposition),\n          onCompositionend: unref(handleComposition),\n          onFocus: handleFocus,\n          onBlur: handleBlur\n        }, null, 42, [\"onUpdate:modelValue\", \"placeholder\", \"onInput\", \"onClick\", \"onKeydown\", \"onCompositionstart\", \"onCompositionupdate\", \"onCompositionend\"])), [[vModelText, searchInputValue.value]]) : createCommentVNode(\"v-if\", true)], 2)) : createCommentVNode(\"v-if\", true)], 46, [\"onClick\", \"onMouseenter\", \"onMouseleave\"])), [[unref(ClickOutside), () => togglePopperVisible(false), unref(contentRef)]])]),\n        content: withCtx(() => [withDirectives(createVNode(unref(ElCascaderPanel), {\n          ref_key: \"cascaderPanelRef\",\n          ref: cascaderPanelRef,\n          modelValue: unref(checkedValue),\n          \"onUpdate:modelValue\": $event => isRef(checkedValue) ? checkedValue.value = $event : null,\n          options: _ctx.options,\n          props: props.props,\n          border: false,\n          \"render-label\": _ctx.$slots.default,\n          onExpandChange: handleExpandChange,\n          onClose: $event => _ctx.$nextTick(() => togglePopperVisible(false))\n        }, {\n          empty: withCtx(() => [renderSlot(_ctx.$slots, \"empty\")]),\n          _: 3\n        }, 8, [\"modelValue\", \"onUpdate:modelValue\", \"options\", \"props\", \"render-label\", \"onClose\"]), [[vShow, !filtering.value]]), _ctx.filterable ? withDirectives((openBlock(), createBlock(unref(ElScrollbar), {\n          key: 0,\n          ref_key: \"suggestionPanel\",\n          ref: suggestionPanel,\n          tag: \"ul\",\n          class: normalizeClass(unref(nsCascader).e(\"suggestion-panel\")),\n          \"view-class\": unref(nsCascader).e(\"suggestion-list\"),\n          onKeydown: handleSuggestionKeyDown\n        }, {\n          default: withCtx(() => [suggestions.value.length ? (openBlock(true), createElementBlock(Fragment, {\n            key: 0\n          }, renderList(suggestions.value, item => {\n            return openBlock(), createElementBlock(\"li\", {\n              key: item.uid,\n              class: normalizeClass([unref(nsCascader).e(\"suggestion-item\"), unref(nsCascader).is(\"checked\", item.checked)]),\n              tabindex: -1,\n              onClick: $event => handleSuggestionClick(item)\n            }, [renderSlot(_ctx.$slots, \"suggestion-item\", {\n              item\n            }, () => [createElementVNode(\"span\", null, toDisplayString(item.text), 1), item.checked ? (openBlock(), createBlock(unref(ElIcon), {\n              key: 0\n            }, {\n              default: withCtx(() => [createVNode(unref(Check))]),\n              _: 1\n            })) : createCommentVNode(\"v-if\", true)])], 10, [\"onClick\"]);\n          }), 128)) : renderSlot(_ctx.$slots, \"empty\", {\n            key: 1\n          }, () => [createElementVNode(\"li\", {\n            class: normalizeClass(unref(nsCascader).e(\"empty-text\"))\n          }, toDisplayString(unref(t)(\"el.cascader.noMatch\")), 3)])]),\n          _: 3\n        }, 8, [\"class\", \"view-class\"])), [[vShow, filtering.value]]) : createCommentVNode(\"v-if\", true)]),\n        _: 3\n      }, 8, [\"visible\", \"teleported\", \"popper-class\", \"fallback-placements\", \"placement\", \"transition\", \"persistent\"]);\n    };\n  }\n});\nvar Cascader = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"cascader.vue\"]]);\nexport { Cascader as default };", "map": {"version": 3, "names": ["name", "COMPONENT_NAME", "popperOptions", "modifiers", "enabled", "phase", "fn", "state", "modifiersData", "placement", "includes", "arrow", "x", "requires", "attrs", "useAttrs", "inputInitialHeight", "pressDeleteCount", "nsCascader", "useNamespace", "nsInput", "t", "useLocale", "form", "formItem", "useFormItem", "valueOnClear", "useEmptyValues", "props", "isComposing", "handleComposition", "useComposition", "afterComposition", "event", "_a", "text", "target", "value", "handleInput", "tooltipRef", "ref", "input", "tagWrapper", "cascaderPanelRef", "suggestion<PERSON>anel", "popperVisible", "inputHover", "filtering", "filterFocus", "inputValue", "searchInputValue", "presentTags", "allPresentTags", "suggestions", "cascaderStyle", "computed", "style", "isDisabled", "disabled", "inputPlaceholder", "placeholder", "currentPlaceholder", "length", "realSize", "useFormSize", "tagSize", "multiple", "readonly", "filterable", "searchKeyword", "checkedNodes", "clearBtnVisible", "clearable", "presentText", "showAllLevels", "separator", "nodes", "calcText", "validateState", "checkedValue", "get", "cloneDeep", "modelValue", "set", "val", "emit", "UPDATE_MODEL_EVENT", "CHANGE_EVENT", "validateEvent", "validate", "catch", "err", "debugWarn", "cascaderKls", "b", "m", "is", "class", "cascaderIconKls", "e", "inputClass", "contentRef", "_b", "popperRef", "togglePopperVisible", "visible", "_c", "setAttribute", "updatePopperPosition", "nextTick", "scrollToExpandingNode", "syncPresentTextValue", "updatePopper", "hideSuggestionPanel", "genTag", "node", "key", "uid", "hitState", "closable", "isCollapseTag", "deleteTag", "tag", "do<PERSON><PERSON><PERSON>", "calculateCheckedValue", "valueByOption", "calculatePresentTags", "tags", "allTags", "for<PERSON>ach", "push", "slice", "maxCollapseTags", "rest", "restCount", "collapseTags", "calculateSuggestions", "filterMethod", "res", "getFlattedNodes", "checkStrictly", "filter", "focusFirstNode", "firstNode", "$el", "querySelector", "focus", "click", "updateStyle", "inputInner", "tagWrapperEl", "suggestionPanelEl", "isClient", "suggestionList", "min<PERSON><PERSON><PERSON>", "offsetWidth", "offsetHeight", "height", "Math", "max", "getCheckedNodes", "leafOnly", "handleExpandChange", "handleKeyDown", "code", "EVENT_CODE", "enter", "numpadEnter", "down", "preventDefault", "esc", "stopPropagation", "tab", "handleClear", "clearCheckedNodes", "handleSuggestionClick", "checked", "handleCheckChange", "handleSuggestionKeyDown", "up", "distance", "focusNode", "getSibling", "handleDelete", "lastTag", "handleFocus", "el", "className", "handleBlur", "handleFilter", "debounce", "passed", "beforeFilter", "isPromise", "then", "getInputInnerHeight", "Number", "parseFloat", "useCssVar", "cssVarName", "watch", "immediate", "onMounted", "inputInnerHeight", "useResizeObserver", "expose", "_ctx", "_cache", "openBlock", "createBlock", "unref", "ElTooltip", "ref_key", "teleported", "popperClass", "fallbackPlacements", "transition", "namespace", "effect", "pure", "persistent", "onHide", "default", "withCtx", "withDirectives", "createElementBlock"], "sources": ["../../../../../../packages/components/cascader/src/cascader.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"tooltipRef\"\n    :visible=\"popperVisible\"\n    :teleported=\"teleported\"\n    :popper-class=\"[nsCascader.e('dropdown'), popperClass]\"\n    :popper-options=\"popperOptions\"\n    :fallback-placements=\"fallbackPlacements\"\n    :stop-popper-mouse-event=\"false\"\n    :gpu-acceleration=\"false\"\n    :placement=\"placement\"\n    :transition=\"`${nsCascader.namespace.value}-zoom-in-top`\"\n    effect=\"light\"\n    pure\n    :persistent=\"persistent\"\n    @hide=\"hideSuggestionPanel\"\n  >\n    <template #default>\n      <div\n        v-clickoutside:[contentRef]=\"() => togglePopperVisible(false)\"\n        :class=\"cascaderKls\"\n        :style=\"cascaderStyle\"\n        @click=\"() => togglePopperVisible(readonly ? undefined : true)\"\n        @keydown=\"handleKeyDown\"\n        @mouseenter=\"inputHover = true\"\n        @mouseleave=\"inputHover = false\"\n      >\n        <el-input\n          ref=\"input\"\n          v-model=\"inputValue\"\n          :placeholder=\"currentPlaceholder\"\n          :readonly=\"readonly\"\n          :disabled=\"isDisabled\"\n          :validate-event=\"false\"\n          :size=\"realSize\"\n          :class=\"inputClass\"\n          :tabindex=\"multiple && filterable && !isDisabled ? -1 : undefined\"\n          @compositionstart=\"handleComposition\"\n          @compositionupdate=\"handleComposition\"\n          @compositionend=\"handleComposition\"\n          @focus=\"handleFocus\"\n          @blur=\"handleBlur\"\n          @input=\"handleInput\"\n        >\n          <template v-if=\"$slots.prefix\" #prefix>\n            <slot name=\"prefix\" />\n          </template>\n          <template #suffix>\n            <el-icon\n              v-if=\"clearBtnVisible\"\n              key=\"clear\"\n              :class=\"[nsInput.e('icon'), 'icon-circle-close']\"\n              @click.stop=\"handleClear\"\n            >\n              <circle-close />\n            </el-icon>\n            <el-icon\n              v-else\n              key=\"arrow-down\"\n              :class=\"cascaderIconKls\"\n              @click.stop=\"togglePopperVisible()\"\n            >\n              <arrow-down />\n            </el-icon>\n          </template>\n        </el-input>\n\n        <div\n          v-if=\"multiple\"\n          ref=\"tagWrapper\"\n          :class=\"[\n            nsCascader.e('tags'),\n            nsCascader.is('validate', Boolean(validateState)),\n          ]\"\n        >\n          <el-tag\n            v-for=\"tag in presentTags\"\n            :key=\"tag.key\"\n            :type=\"tagType\"\n            :size=\"tagSize\"\n            :effect=\"tagEffect\"\n            :hit=\"tag.hitState\"\n            :closable=\"tag.closable\"\n            disable-transitions\n            @close=\"deleteTag(tag)\"\n          >\n            <template v-if=\"tag.isCollapseTag === false\">\n              <span>{{ tag.text }}</span>\n            </template>\n            <template v-else>\n              <el-tooltip\n                :disabled=\"popperVisible || !collapseTagsTooltip\"\n                :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n                placement=\"bottom\"\n                effect=\"light\"\n              >\n                <template #default>\n                  <span>{{ tag.text }}</span>\n                </template>\n                <template #content>\n                  <div :class=\"nsCascader.e('collapse-tags')\">\n                    <div\n                      v-for=\"(tag2, idx) in allPresentTags.slice(\n                        maxCollapseTags\n                      )\"\n                      :key=\"idx\"\n                      :class=\"nsCascader.e('collapse-tag')\"\n                    >\n                      <el-tag\n                        :key=\"tag2.key\"\n                        class=\"in-tooltip\"\n                        :type=\"tagType\"\n                        :size=\"tagSize\"\n                        :effect=\"tagEffect\"\n                        :hit=\"tag2.hitState\"\n                        :closable=\"tag2.closable\"\n                        disable-transitions\n                        @close=\"deleteTag(tag2)\"\n                      >\n                        <span>{{ tag2.text }}</span>\n                      </el-tag>\n                    </div>\n                  </div>\n                </template>\n              </el-tooltip>\n            </template>\n          </el-tag>\n          <input\n            v-if=\"filterable && !isDisabled\"\n            v-model=\"searchInputValue\"\n            type=\"text\"\n            :class=\"nsCascader.e('search-input')\"\n            :placeholder=\"presentText ? '' : inputPlaceholder\"\n            @input=\"(e) => handleInput(searchInputValue, e as KeyboardEvent)\"\n            @click.stop=\"togglePopperVisible(true)\"\n            @keydown.delete=\"handleDelete\"\n            @compositionstart=\"handleComposition\"\n            @compositionupdate=\"handleComposition\"\n            @compositionend=\"handleComposition\"\n            @focus=\"handleFocus\"\n            @blur=\"handleBlur\"\n          />\n        </div>\n      </div>\n    </template>\n\n    <template #content>\n      <el-cascader-panel\n        v-show=\"!filtering\"\n        ref=\"cascaderPanelRef\"\n        v-model=\"checkedValue\"\n        :options=\"options\"\n        :props=\"props.props\"\n        :border=\"false\"\n        :render-label=\"$slots.default\"\n        @expand-change=\"handleExpandChange\"\n        @close=\"$nextTick(() => togglePopperVisible(false))\"\n      >\n        <template #empty>\n          <slot name=\"empty\" />\n        </template>\n      </el-cascader-panel>\n      <el-scrollbar\n        v-if=\"filterable\"\n        v-show=\"filtering\"\n        ref=\"suggestionPanel\"\n        tag=\"ul\"\n        :class=\"nsCascader.e('suggestion-panel')\"\n        :view-class=\"nsCascader.e('suggestion-list')\"\n        @keydown=\"handleSuggestionKeyDown\"\n      >\n        <template v-if=\"suggestions.length\">\n          <li\n            v-for=\"item in suggestions\"\n            :key=\"item.uid\"\n            :class=\"[\n              nsCascader.e('suggestion-item'),\n              nsCascader.is('checked', item.checked),\n            ]\"\n            :tabindex=\"-1\"\n            @click=\"handleSuggestionClick(item)\"\n          >\n            <slot name=\"suggestion-item\" :item=\"item\">\n              <span>{{ item.text }}</span>\n              <el-icon v-if=\"item.checked\">\n                <check />\n              </el-icon>\n            </slot>\n          </li>\n        </template>\n        <slot v-else name=\"empty\">\n          <li :class=\"nsCascader.e('empty-text')\">\n            {{ t('el.cascader.noMatch') }}\n          </li>\n        </slot>\n      </el-scrollbar>\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, onMounted, ref, useAttrs, watch } from 'vue'\nimport { cloneDeep, debounce } from 'lodash-unified'\nimport { useCssVar, useResizeObserver } from '@vueuse/core'\nimport {\n  debugWarn,\n  focusNode,\n  getSibling,\n  isClient,\n  isPromise,\n} from '@element-plus/utils'\nimport ElCascaderPanel from '@element-plus/components/cascader-panel'\nimport ElInput from '@element-plus/components/input'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElTag from '@element-plus/components/tag'\nimport ElIcon from '@element-plus/components/icon'\nimport { useFormItem, useFormSize } from '@element-plus/components/form'\nimport { ClickOutside as vClickoutside } from '@element-plus/directives'\nimport {\n  useComposition,\n  useEmptyValues,\n  useLocale,\n  useNamespace,\n} from '@element-plus/hooks'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { ArrowDown, Check, CircleClose } from '@element-plus/icons-vue'\nimport { cascaderEmits, cascaderProps } from './cascader'\n\nimport type { Options } from '@element-plus/components/popper'\nimport type { ComputedRef, Ref, StyleValue } from 'vue'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\nimport type { InputInstance } from '@element-plus/components/input'\nimport type { ScrollbarInstance } from '@element-plus/components/scrollbar'\nimport type {\n  CascaderNode,\n  CascaderPanelInstance,\n  CascaderValue,\n  Tag,\n} from '@element-plus/components/cascader-panel'\n\nconst popperOptions: Partial<Options> = {\n  modifiers: [\n    {\n      name: 'arrowPosition',\n      enabled: true,\n      phase: 'main',\n      fn: ({ state }) => {\n        const { modifiersData, placement } = state as any\n        if (['right', 'left', 'bottom', 'top'].includes(placement)) return\n        modifiersData.arrow.x = 35\n      },\n      requires: ['arrow'],\n    },\n  ],\n}\nconst COMPONENT_NAME = 'ElCascader'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(cascaderProps)\nconst emit = defineEmits(cascaderEmits)\nconst attrs = useAttrs()\n\nlet inputInitialHeight = 0\nlet pressDeleteCount = 0\n\nconst nsCascader = useNamespace('cascader')\nconst nsInput = useNamespace('input')\n\nconst { t } = useLocale()\nconst { form, formItem } = useFormItem()\nconst { valueOnClear } = useEmptyValues(props)\nconst { isComposing, handleComposition } = useComposition({\n  afterComposition(event) {\n    const text = (event.target as HTMLInputElement)?.value\n    handleInput(text)\n  },\n})\n\nconst tooltipRef: Ref<TooltipInstance | null> = ref(null)\nconst input: Ref<InputInstance | null> = ref(null)\nconst tagWrapper = ref(null)\nconst cascaderPanelRef: Ref<CascaderPanelInstance | null> = ref(null)\nconst suggestionPanel: Ref<ScrollbarInstance | null> = ref(null)\nconst popperVisible = ref(false)\nconst inputHover = ref(false)\nconst filtering = ref(false)\nconst filterFocus = ref(false)\nconst inputValue = ref('')\nconst searchInputValue = ref('')\nconst presentTags: Ref<Tag[]> = ref([])\nconst allPresentTags: Ref<Tag[]> = ref([])\nconst suggestions: Ref<CascaderNode[]> = ref([])\n\nconst cascaderStyle = computed<StyleValue>(() => {\n  return attrs.style as StyleValue\n})\n\nconst isDisabled = computed(() => props.disabled || form?.disabled)\nconst inputPlaceholder = computed(\n  () => props.placeholder || t('el.cascader.placeholder')\n)\nconst currentPlaceholder = computed(() =>\n  searchInputValue.value || presentTags.value.length > 0 || isComposing.value\n    ? ''\n    : inputPlaceholder.value\n)\nconst realSize = useFormSize()\nconst tagSize = computed(() =>\n  realSize.value === 'small' ? 'small' : 'default'\n)\nconst multiple = computed(() => !!props.props.multiple)\nconst readonly = computed(() => !props.filterable || multiple.value)\nconst searchKeyword = computed(() =>\n  multiple.value ? searchInputValue.value : inputValue.value\n)\nconst checkedNodes: ComputedRef<CascaderNode[]> = computed(\n  () => cascaderPanelRef.value?.checkedNodes || []\n)\nconst clearBtnVisible = computed(() => {\n  if (\n    !props.clearable ||\n    isDisabled.value ||\n    filtering.value ||\n    !inputHover.value\n  )\n    return false\n\n  return !!checkedNodes.value.length\n})\nconst presentText = computed(() => {\n  const { showAllLevels, separator } = props\n  const nodes = checkedNodes.value\n  return nodes.length\n    ? multiple.value\n      ? ''\n      : nodes[0].calcText(showAllLevels, separator)\n    : ''\n})\n\nconst validateState = computed(() => formItem?.validateState || '')\n\nconst checkedValue = computed<CascaderValue>({\n  get() {\n    return cloneDeep(props.modelValue) as CascaderValue\n  },\n  set(val) {\n    // https://github.com/element-plus/element-plus/issues/17647\n    const value = val ?? valueOnClear.value\n    emit(UPDATE_MODEL_EVENT, value)\n    emit(CHANGE_EVENT, value)\n    if (props.validateEvent) {\n      formItem?.validate('change').catch((err) => debugWarn(err))\n    }\n  },\n})\n\nconst cascaderKls = computed(() => {\n  return [\n    nsCascader.b(),\n    nsCascader.m(realSize.value),\n    nsCascader.is('disabled', isDisabled.value),\n    attrs.class,\n  ]\n})\n\nconst cascaderIconKls = computed(() => {\n  return [\n    nsInput.e('icon'),\n    'icon-arrow-down',\n    nsCascader.is('reverse', popperVisible.value),\n  ]\n})\n\nconst inputClass = computed(() => {\n  return nsCascader.is('focus', popperVisible.value || filterFocus.value)\n})\n\nconst contentRef = computed(() => {\n  return tooltipRef.value?.popperRef?.contentRef\n})\n\nconst togglePopperVisible = (visible?: boolean) => {\n  if (isDisabled.value) return\n\n  visible = visible ?? !popperVisible.value\n\n  if (visible !== popperVisible.value) {\n    popperVisible.value = visible\n    input.value?.input?.setAttribute('aria-expanded', `${visible}`)\n\n    if (visible) {\n      updatePopperPosition()\n      nextTick(cascaderPanelRef.value?.scrollToExpandingNode)\n    } else if (props.filterable) {\n      syncPresentTextValue()\n    }\n\n    emit('visibleChange', visible)\n  }\n}\n\nconst updatePopperPosition = () => {\n  nextTick(() => {\n    tooltipRef.value?.updatePopper()\n  })\n}\nconst hideSuggestionPanel = () => {\n  filtering.value = false\n}\n\nconst genTag = (node: CascaderNode): Tag => {\n  const { showAllLevels, separator } = props\n  return {\n    node,\n    key: node.uid,\n    text: node.calcText(showAllLevels, separator),\n    hitState: false,\n    closable: !isDisabled.value && !node.isDisabled,\n    isCollapseTag: false,\n  }\n}\n\nconst deleteTag = (tag: Tag) => {\n  const node = tag.node as CascaderNode\n  node.doCheck(false)\n  cascaderPanelRef.value?.calculateCheckedValue()\n  emit('removeTag', node.valueByOption)\n}\n\nconst calculatePresentTags = () => {\n  if (!multiple.value) return\n\n  const nodes = checkedNodes.value\n  const tags: Tag[] = []\n\n  const allTags: Tag[] = []\n  nodes.forEach((node) => allTags.push(genTag(node)))\n  allPresentTags.value = allTags\n\n  if (nodes.length) {\n    nodes\n      .slice(0, props.maxCollapseTags)\n      .forEach((node) => tags.push(genTag(node)))\n    const rest = nodes.slice(props.maxCollapseTags)\n    const restCount = rest.length\n\n    if (restCount) {\n      if (props.collapseTags) {\n        tags.push({\n          key: -1,\n          text: `+ ${restCount}`,\n          closable: false,\n          isCollapseTag: true,\n        })\n      } else {\n        rest.forEach((node) => tags.push(genTag(node)))\n      }\n    }\n  }\n\n  presentTags.value = tags\n}\n\nconst calculateSuggestions = () => {\n  const { filterMethod, showAllLevels, separator } = props\n  const res = cascaderPanelRef.value\n    ?.getFlattedNodes(!props.props.checkStrictly)\n    ?.filter((node) => {\n      if (node.isDisabled) return false\n      node.calcText(showAllLevels, separator)\n      return filterMethod(node, searchKeyword.value)\n    })\n\n  if (multiple.value) {\n    presentTags.value.forEach((tag) => {\n      tag.hitState = false\n    })\n    allPresentTags.value.forEach((tag) => {\n      tag.hitState = false\n    })\n  }\n\n  filtering.value = true\n  suggestions.value = res!\n  updatePopperPosition()\n}\n\nconst focusFirstNode = () => {\n  let firstNode!: HTMLElement\n\n  if (filtering.value && suggestionPanel.value) {\n    firstNode = suggestionPanel.value.$el.querySelector(\n      `.${nsCascader.e('suggestion-item')}`\n    )\n  } else {\n    firstNode = cascaderPanelRef.value?.$el.querySelector(\n      `.${nsCascader.b('node')}[tabindex=\"-1\"]`\n    )\n  }\n\n  if (firstNode) {\n    firstNode.focus()\n    !filtering.value && firstNode.click()\n  }\n}\n\nconst updateStyle = () => {\n  const inputInner = input.value?.input\n  const tagWrapperEl = tagWrapper.value\n  const suggestionPanelEl = suggestionPanel.value?.$el\n\n  if (!isClient || !inputInner) return\n\n  if (suggestionPanelEl) {\n    const suggestionList = suggestionPanelEl.querySelector(\n      `.${nsCascader.e('suggestion-list')}`\n    )\n    suggestionList.style.minWidth = `${inputInner.offsetWidth}px`\n  }\n\n  if (tagWrapperEl) {\n    const { offsetHeight } = tagWrapperEl\n    // 2 is el-input__wrapper padding\n    const height =\n      presentTags.value.length > 0\n        ? `${Math.max(offsetHeight, inputInitialHeight) - 2}px`\n        : `${inputInitialHeight}px`\n    inputInner.style.height = height\n    updatePopperPosition()\n  }\n}\n\nconst getCheckedNodes = (leafOnly: boolean) => {\n  return cascaderPanelRef.value?.getCheckedNodes(leafOnly)\n}\n\nconst handleExpandChange = (value: CascaderValue) => {\n  updatePopperPosition()\n  emit('expandChange', value)\n}\n\nconst handleKeyDown = (e: KeyboardEvent) => {\n  if (isComposing.value) return\n\n  switch (e.code) {\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n      togglePopperVisible()\n      break\n    case EVENT_CODE.down:\n      togglePopperVisible(true)\n      nextTick(focusFirstNode)\n      e.preventDefault()\n      break\n    case EVENT_CODE.esc:\n      if (popperVisible.value === true) {\n        e.preventDefault()\n        e.stopPropagation()\n        togglePopperVisible(false)\n      }\n      break\n    case EVENT_CODE.tab:\n      togglePopperVisible(false)\n      break\n  }\n}\n\nconst handleClear = () => {\n  cascaderPanelRef.value?.clearCheckedNodes()\n  if (!popperVisible.value && props.filterable) {\n    syncPresentTextValue()\n  }\n  togglePopperVisible(false)\n  emit('clear')\n}\n\nconst syncPresentTextValue = () => {\n  const { value } = presentText\n  inputValue.value = value\n  searchInputValue.value = value\n}\n\nconst handleSuggestionClick = (node: CascaderNode) => {\n  const { checked } = node\n\n  if (multiple.value) {\n    cascaderPanelRef.value?.handleCheckChange(node, !checked, false)\n  } else {\n    !checked && cascaderPanelRef.value?.handleCheckChange(node, true, false)\n    togglePopperVisible(false)\n  }\n}\n\nconst handleSuggestionKeyDown = (e: KeyboardEvent) => {\n  const target = e.target as HTMLElement\n  const { code } = e\n\n  switch (code) {\n    case EVENT_CODE.up:\n    case EVENT_CODE.down: {\n      e.preventDefault()\n      const distance = code === EVENT_CODE.up ? -1 : 1\n      focusNode(\n        getSibling(\n          target,\n          distance,\n          `.${nsCascader.e('suggestion-item')}[tabindex=\"-1\"]`\n        ) as HTMLElement\n      )\n      break\n    }\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n      target.click()\n      break\n  }\n}\n\nconst handleDelete = () => {\n  const tags = presentTags.value\n  const lastTag = tags[tags.length - 1]\n  pressDeleteCount = searchInputValue.value ? 0 : pressDeleteCount + 1\n\n  if (!lastTag || !pressDeleteCount || (props.collapseTags && tags.length > 1))\n    return\n\n  if (lastTag.hitState) {\n    deleteTag(lastTag)\n  } else {\n    lastTag.hitState = true\n  }\n}\n\nconst handleFocus = (e: FocusEvent) => {\n  const el = e.target as HTMLInputElement\n  const name = nsCascader.e('search-input')\n  if (el.className === name) {\n    filterFocus.value = true\n  }\n  emit('focus', e)\n}\n\nconst handleBlur = (e: FocusEvent) => {\n  filterFocus.value = false\n  emit('blur', e)\n}\n\nconst handleFilter = debounce(() => {\n  const { value } = searchKeyword\n\n  if (!value) return\n\n  const passed = props.beforeFilter(value)\n\n  if (isPromise(passed)) {\n    passed.then(calculateSuggestions).catch(() => {\n      /* prevent log error */\n    })\n  } else if (passed !== false) {\n    calculateSuggestions()\n  } else {\n    hideSuggestionPanel()\n  }\n}, props.debounce)\n\nconst handleInput = (val: string, e?: KeyboardEvent) => {\n  !popperVisible.value && togglePopperVisible(true)\n\n  if (e?.isComposing) return\n\n  val ? handleFilter() : hideSuggestionPanel()\n}\n\nconst getInputInnerHeight = (inputInner: HTMLElement): number =>\n  Number.parseFloat(\n    useCssVar(nsInput.cssVarName('input-height'), inputInner).value\n  ) - 2\n\nwatch(filtering, updatePopperPosition)\n\nwatch(\n  [checkedNodes, isDisabled, () => props.collapseTags],\n  calculatePresentTags\n)\n\nwatch(presentTags, () => {\n  nextTick(() => updateStyle())\n})\n\nwatch(realSize, async () => {\n  await nextTick()\n  const inputInner = input.value!.input!\n  inputInitialHeight = getInputInnerHeight(inputInner) || inputInitialHeight\n  updateStyle()\n})\n\nwatch(presentText, syncPresentTextValue, { immediate: true })\n\nonMounted(() => {\n  const inputInner = input.value!.input!\n\n  const inputInnerHeight = getInputInnerHeight(inputInner)\n\n  inputInitialHeight = inputInner.offsetHeight || inputInnerHeight\n  useResizeObserver(inputInner, updateStyle)\n})\n\ndefineExpose({\n  /**\n   * @description get an array of currently selected node,(leafOnly) whether only return the leaf checked nodes, default is `false`\n   */\n  getCheckedNodes,\n  /**\n   * @description cascader panel ref\n   */\n  cascaderPanelRef,\n  /**\n   * @description toggle the visible of popper\n   */\n  togglePopperVisible,\n  /**\n   * @description cascader content ref\n   */\n  contentRef,\n  /**\n   * @description selected content text\n   */\n  presentText,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAsQc;EACZA,IAAM,EAAAC;AACR;;;;;;;;;;IAnBA,MAAMC,aAAkC;MACtCC,SAAW,GACT;QACEH,IAAM;QACNI,OAAS;QACTC,KAAO;QACPC,EAAI,EAAAA,CAAC;UAAEC;QAAA,CAAY;UACX;YAAEC,aAAe;YAAAC;UAAA,CAAc,GAAAF,KAAA;UACjC,KAAC,SAAS,MAAQ,YAAU,KAAK,CAAE,CAAAG,QAAA,CAASD,SAAS,CAAG,EAC5D;UACFD,aAAA,CAAAG,KAAA,CAAAC,CAAA;QAAA,CACA;QACFC,QAAA;MAAA,CACF;IAUF;IAEA,MAAyBC,KAAA,GAAAC,QAAA;IACzB,IAAIC,kBAAmB;IAEjB,IAAAC,gBAAA;IACA,MAAAC,UAAU,GAAAC,YAAoB;IAE9B,MAAAC,OAAI,GAAcD,YAAA;IACxB,MAAM;MAAEE;IAAA,IAAMC,SAAS;IACvB,MAAM;MAAEC,IAAA;MAAAC;IAAiB,IAAAC,WAAA,EAAe;IACxC,MAAM;MAAEC;IAAA,CAA+B,GAAAC,cAAA,CAAAC,KAAmB;IAAA;MAAAC,WACvC;MAAOC;IAAA,IAAAC,cAAA;MAChBC,iBAAAC,KAA2C;QACjD,IAAAC,EAAA;QACF,MAAAC,IAAA,IAAAD,EAAA,GAAAD,KAAA,CAAAG,MAAA,qBAAAF,EAAA,CAAAG,KAAA;QACDC,WAAA,CAAAH,IAAA;MAED;IACA,CAAM;IACA,MAAAI,UAAA,GAAaC,GAAA,CAAI,IAAI;IACrB,MAAAC,KAAA,GAAAD,GAAA;IACA,MAAAE,UAAA,GAAAF,GAAA,MAAqD;IACrD,MAAAG,gBAAgB,GAAAH,GAAS;IACzB,MAAAI,eAAA,GAAsBJ,GAAA;IACtB,MAAAK,aAAA,GAAgBL,GAAK;IACrB,MAAAM,UAAA,GAAAN,GAAA,MAAuB;IACvB,MAAAO,SAAA,GAAAP,GAAA,MAAmB;IACnB,MAAAQ,WAAA,GAAAR,GAAA,MAAuB,CAAE;IACzB,MAAAS,UAAA,GAAAT,GAA8B,GAAC,CAAC;IAChC,MAAAU,gBAAA,GAAiCV,GAAC,CAAC;IACnC,MAAAW,WAAA,GAAmCX,GAAI,GAAE;IAEzC,MAAAY,cAAA,GAAAZ,GAAA;IACJ,MAAAa,WAAa,GAAAb,GAAA;IACf,MAACc,aAAA,GAAAC,QAAA;MAED,OAAAzC,KAAA,CAAA0C,KAA4B;IAC5B;IAAyB,MACjBC,UAAqB,GAAAF,QAAA,OAA2B3B,KAAA,CAAA8B,QAAA,KAAAnC,IAAA,oBAAAA,IAAA,CAAAmC,QAAA;IACxD,MAAAC,gBAAA,GAAAJ,QAAA,OAAA3B,KAAA,CAAAgC,WAAA,IAAAvC,CAAA;IACA,MAAMwC,kBAAqB,GAAAN,QAAA,OAAAL,gBAAA,CAAAb,KAAA,IAAAc,WAAA,CAAAd,KAAA,CAAAyB,MAAA,QAAAjC,WAAA,CAAAQ,KAAA,QAAAsB,gBAAA,CAAAtB,KAAA;IAAS,MAAA0B,QACjB,GAAAC,WAAA;IAGnB,MAAAC,OAAA,GAAAV,QAAA,OAAAQ,QAAA,CAAA1B,KAAA;IACA,MAAM6B,QAAA,GAAWX,QAAY,SAAA3B,KAAA,CAAAA,KAAA,CAAAsC,QAAA;IAC7B,MAAMC,QAAU,GAAAZ,QAAA,QAAA3B,KAAA,CAAAwC,UAAA,IAAAF,QAAA,CAAA7B,KAAA;IAAA,MACdgC,aAAmB,GAAAd,QAAA,OAAUW,QAAU,CAAA7B,KAAA,GAAAa,gBAAA,CAAAb,KAAA,GAAAY,UAAA,CAAAZ,KAAA;IACzC,MAAAiC,YAAA,GAAAf,QAAA;MACA,IAAMrB,EAAA;MACN,SAAAA,EAAA,GAAAS,gBAAgC,CAACN,KAAM,qBAAAH,EAAA,CAAuBoC,YAAK;IACnE;IAAsB,MACpBC,eAAiB,GAAAhB,QAAA;MACnB,KAAA3B,KAAA,CAAA4C,SAAA,IAAAf,UAAA,CAAApB,KAAA,IAAAU,SAAA,CAAAV,KAAA,KAAAS,UAAA,CAAAT,KAAA,EACA,OAAkD;MAChD,OAAM,EAAAiC,YAAA,CAAAjC,KAAwB,CAAAyB,MAAA;IAAiB,CACjD;IACM,MAAAW,WAAA,GAAAlB,QAAA,OAAiC;MAEnC;QAAAmB,aACA;QAAAC;MAAA,IAAA/C,KAAA;MAIO,MAAAgD,KAAA,GAAAN,YAAA,CAAAjC,KAAA;MAEF,OAAAuC,KAAE,CAAAd,MAAA,GAAaI,QAAM,CAAA7B,KAAA,QAAAuC,KAAA,IAAAC,QAAA,CAAAH,aAAA,EAAAC,SAAA;IAAA,CAC7B;IACK,MAAAG,aAAA,GAAAvB,QAA6B,QAAA/B,QAAA,oBAAAA,QAAA,CAAAsD,aAAA;IAC3B,MAAAC,YAAiB,GAAAxB,QAAA;MACvByB,IAAA,EAAM;QACC,OAAAC,SACH,CAAArD,KAAA,CAAAsD,UACE;MAEF,CACL;MAEDC,IAAMC,GAAgB;QAEtB,MAAA/C,KAAA,GAAA+C,GAAqB,IAAwB,OAAAA,GAAA,GAAA1D,YAAA,CAAAW,KAAA;QACrCgD,IAAA,CAAAC,kBAAA,EAAAjD,KAAA;QACGgD,IAAA,CAAAE,YAAU,EAAAlD,KAAgB;QACnC,IAAAT,KAAA,CAAA4D,aAAA;UACIhE,QAAK,oBAAAA,QAAA,CAAAiE,QAAA,WAAAC,KAAA,CAAAC,GAAA,IAAAC,SAAA,CAAAD,GAAA;QAEP;MACA;IACA;IACA,MAAAE,WAAyB,GAAAtC,QAAA;MACb,QACZrC,UAAA,CAAA4E,CAAA,IACF5E,UAAA,CAAA6E,CAAA,CAAAhC,QAAA,CAAA1B,KAAA,GACDnB,UAAA,CAAA8E,EAAA,aAAAvC,UAAA,CAAApB,KAAA,GAEKvB,KAAA,CAAAmF,KAAA,CACG;IAAA;IAEL,MAAAC,eAAa,GAAA3C,QAAc;MAAA,OAChB,CACXnC,OAAM,CAAA+E,CAAA,UACR,mBACDjF,UAAA,CAAA8E,EAAA,YAAAnD,aAAA,CAAAR,KAAA,EAEK;IACJ,CAAO;IACL,MAAA+D,UAAgB,GAAA7C,QAAA;MAChB,OAAArC,UAAA,CAAA8E,EAAA,UAAAnD,aAAA,CAAAR,KAAA,IAAAW,WAAA,CAAAX,KAAA;IAAA;IAEF,MAAAgE,UAAA,GAAA9C,QAAA;MACD,IAAArB,EAAA,EAAAoE,EAAA;MAEK,QAAAA,EAAA,IAAApE,EAAA,GAAAK,UAA4B,CAAAF,KAAA,qBAAAH,EAAA,CAAAqE,SAAA,qBAAAD,EAAA,CAAAD,UAAA;IAChC;IACF,MAACG,mBAAA,GAAAC,OAAA;MAEK,IAAAvE,EAAA,EAAAoE,EAAA,EAAAI,EAAA;MACG,IAAAjD,UAAA,CAAApB,KAAA,EACR;MAEKoE,OAAA,GAAAA,OAAA,WAA6CA,OAAA,IAAA5D,aAAA,CAAAR,KAAA;MACjD,IAAIoE,OAAA,KAAkB5D,aAAA,CAAAR,KAAA;QAEZQ,aAAA,CAAAR,KAAW,GAAeoE,OAAA;QAEhC,CAAAH,EAAA,IAAApE,EAAA,GAAAO,KAAA,CAAAJ,KAAA,KAA0B,IAAO,YAAAH,EAAA,CAAAO,KAAA,qBAAA6D,EAAA,CAAAK,YAAA,qBAAAF,OAAA;QACnC,IAAAA,OAAA;UACAG,oBAAoB;UAEpBC,QAAa,EAAAH,EAAA,GAAA/D,gBAAA,CAAAN,KAAA,qBAAAqE,EAAA,CAAAI,qBAAA;QACX,CAAqB,UAAAlF,KAAA,CAAAwC,UAAA;UACZ2C,oBAAA;QAA6C;QAEjC1B,IAAA,kBAAAoB,OAAA;MAAA;IAGvB;IACF,MAAAG,oBAAA,GAAAA,CAAA;MACFC,QAAA;QAEA,IAAA3E,EAAA;QACE,CAAAA,EAAA,GAAAK,UAAe,CAAAF,KAAA,qBAAAH,EAAA,CAAA8E,YAAA;MACb;IAA+B;IAEnC,MAAAC,mBAAA,GAAAA,CAAA;MACAlE,SAAA,CAAAV,KAAA;IACE;IACF,MAAA6E,MAAA,GAAAC,IAAA;MAEM;QAAAzC,aAAsC;QAAAC;MAAA,IAAA/C,KAAA;MACpC;QACCuF,IAAA;QACLC,GAAA,EAAAD,IAAA,CAAAE,GAAA;QACAlF,IAAA,EAAUgF,IAAA,CAAAtC,QAAA,CAAAH,aAAA,EAAAC,SAAA;QACV2C,QAAM,OAAc;QACpBC,QAAU,GAAA9D,UAAA,CAAApB,KAAA,KAAA8E,IAAA,CAAA1D,UAAA;QACV+D,aAAsB;MAAe;IACtB,CACjB;IACF,MAAAC,SAAA,GAAAC,GAAA;MAEM,IAAAxF,EAAA;MACJ,MAAMiF,IAAA,GAAOO,GAAI,CAAAP,IAAA;MACjBA,IAAA,CAAKQ,OAAA,CAAQ,KAAK;MAClB,CAAAzF,EAAA,GAAAS,gBAAA,CAAAN,KAA8C,qBAAAH,EAAA,CAAA0F,qBAAA;MACzCvC,IAAA,cAAa8B,IAAA,CAAKU,aAAa;IAAA,CACtC;IAEA,MAAMC,oBAAA,GAAuBA,CAAA,KAAM;MAC7B,KAAC5D,QAAA,CAAS7B,KAAO,EAErB;MACA,MAAMuC,KAAA,GAAeN,YAAA,CAAAjC,KAAA;MAErB,MAAM0F,IAAA;MACA,MAAAC,OAAA,GAAkB;MACxBpD,KAAA,CAAAqD,OAAA,CAAed,IAAQ,IAAAa,OAAA,CAAAE,IAAA,CAAAhB,MAAA,CAAAC,IAAA;MAEvB/D,cAAkB,CAAAf,KAAA,GAAA2F,OAAA;MAChB,IAAApD,KACS,CAAAd,MAAA,EAAS;QAElBc,KAAA,CAAMuD,KAAO,IAAAvG,KAAY,CAAAwG,eAAqB,EAAAH,OAAA,CAAAd,IAAA,IAAAY,IAAA,CAAAG,IAAA,CAAAhB,MAAA,CAAAC,IAAA;QAC9C,MAAMkB,IAAA,GAAAzD,KAAY,CAAKuD,KAAA,CAAAvG,KAAA,CAAAwG,eAAA;QAEvB,MAAeE,SAAA,GAAAD,IAAA,CAAAvE,MAAA;QACb,IAAAwE,SAAwB;UACtB,IAAA1G,KAAU,CAAA2G,YAAA;YAAAR,IACH,CAAAG,IAAA;cACLd,GAAA,GAAM;cACNjF,IAAU,OAAAmG,SAAA;cACVf,QAAe;cAChBC,aAAA;YAAA,CACI;UACL,CAAK;YACPa,IAAA,CAAAJ,OAAA,CAAAd,IAAA,IAAAY,IAAA,CAAAG,IAAA,CAAAhB,MAAA,CAAAC,IAAA;UAAA;QACF;MAGF;MACFhE,WAAA,CAAAd,KAAA,GAAA0F,IAAA;IAEA;IACE,MAAAS,oBAAqC,GAAAA,CAAA;MAC/B,IAAAtG,EAAA,EAAAoE,EAAA;MAGE;QAAAmC,YAAA;QAAiB/D,aAAO;QAAAC;MAAA,IAAA/C,KAAA;MACvB,MAAA8G,GAAA,IAAApC,EAAA,IAAApE,EAAA,GAAAS,gBAAiC,CAAAN,KAAA,qBAAAH,EAAA,CAAAyG,eAAA,EAAA/G,KAAA,CAAAA,KAAA,CAAAgH,aAAA,sBAAAtC,EAAA,CAAAuC,MAAA,CAAA1B,IAAA;QAC/B,IAAAA,IAAA,CAAA1D,UAAA,EACR;QAEH0D,IAAA,CAAAtC,QAAoB,CAAAH,aAAA,EAAAC,SAAA;QACN,OAAA8D,YAAc,CAAAtB,IAAA,EAAA9C,aAAS,CAAAhC,KAAA;MACjC;MAAe,IAChB6B,QAAA,CAAA7B,KAAA;QACcc,WAAA,CAAAd,KAAA,CAAA4F,OAAc,CAAAP,GAAC,IAAQ;UACpCA,GAAA,CAAIJ,QAAW;QAAA,CAChB;QACHlE,cAAA,CAAAf,KAAA,CAAA4F,OAAA,CAAAP,GAAA;UAEAA,GAAA,CAAAJ,QAAkB;QAClB;MACA;MACFvE,SAAA,CAAAV,KAAA;MAEAgB,WAAA,CAAAhB,KAAA,GAAAqG,GAA6B;MACvB9B,oBAAA;IAEJ,CAAI;IACU,MAAAkC,cAAA,GAAAA,CAAA;MAA0B,IACpC5G,EAAI;MACN,IAAA6G,SAAA;MACF,IAAOhG,SAAA,CAAAV,KAAA,IAAAO,eAAA,CAAAP,KAAA;QACO0G,SAAA,GAAAnG,eAAA,CAAAP,KAAA,CAAA2G,GAA4B,CAAAC,aAAA,KAAA/H,UAAA,CAAAiF,CAAA;MAAA,OAClC;QACN4C,SAAA,IAAA7G,EAAA,GAAAS,gBAAA,CAAAN,KAAA,qBAAAH,EAAA,CAAA8G,GAAA,CAAAC,aAAA,KAAA/H,UAAA,CAAA4E,CAAA;MAAA;MAGF,IAAIiD,SAAW;QACbA,SAAA,CAAUG,KAAM;QACf,CAAAnG,SAAA,CAAUV,KAAS,IAAA0G,SAAA,CAAUI,KAAM;MAAA;IACtC,CACF;IAEA,MAAMC,WAAA,GAAcA,CAAA,KAAM;MAClB,IAAAlH,EAAA,EAAAoE,EAAA;MACN,MAAM+C,UAAA,IAAAnH,EAA0B,GAAAO,KAAA,CAAAJ,KAAA,qBAAAH,EAAA,CAAAO,KAAA;MAC1B,MAAA6G,YAAA,GAAA5G,UAAA,CAAAL,KAAA;MAEF,MAAakH,iBAAa,IAAAjD,EAAA,GAAA1D,eAAA,CAAAP,KAAA,qBAAAiE,EAAA,CAAA0C,GAAA;MAE9B,IAAI,CAAmBQ,QAAA,KAAAH,UAAA,EACrB;MAAyC,IACvCE,iBAAiB;QACnB,MAAAE,cAAA,GAAAF,iBAAA,CAAAN,aAAA,KAAA/H,UAAA,CAAAiF,CAAA;QACAsD,cAAA,CAAejG,KAAM,CAAAkG,QAAA,GAAW,GAAGL,UAAA,CAAWM,WAAW;MAAA;MAG3D,IAAIL,YAAc;QACV;UAAEM;QAAA,CAAiB,GAAAN,YAAA;QAEzB,MAAMO,MACJ,GAAA1G,WAAA,CAAYd,KAAM,CAAAyB,MAAA,GAAS,IACvB,GAAGgG,IAAA,CAAKC,GAAI,CAAAH,YAAA,EAAc5I,kBAAkB,IAAI,CAAC,OACjD,GAAGA,kBAAkB;QAC3BqI,UAAA,CAAW7F,KAAA,CAAMqG,MAAS,GAAAA,MAAA;QACLjD,oBAAA;MAAA;IACvB,CACF;IAEM,MAAAoD,eAAA,GAAmBC,QAAsB;MACtC,IAAA/H,EAAA;MACT,QAAAA,EAAA,GAAAS,gBAAA,CAAAN,KAAA,qBAAAH,EAAA,CAAA8H,eAAA,CAAAC,QAAA;IAEA,CAAM;IACiB,MAAAC,kBAAA,GAAA7H,KAAA;MACrBuE,oBAAA,EAA0B;MAC5BvB,IAAA,iBAAAhD,KAAA;IAEA,CAAM;IACJ,MAAI8H,aAAmB,GAAAhE,CAAA;MAEvB,IAAAtE,WAAgB,CAAAQ,KAAA,EACd;MAAgB,QACA8D,CAAA,CAAAiE,IAAA;QACM,KAAAC,UAAA,CAAAC,KAAA;QACpB,KAAAD,UAAA,CAAAE,WAAA;UAAA/D,mBACc;UACd;QACA,KAAA6D,UAAuB,CAAAG,IAAA;UACvBhE,mBAAiB;UACjBK,QAAA,CAAAiC,cAAA;UAAA3C,CAAA,CAAAsE,cACc;UACV;QACF,KAAAJ,UAAiB,CAAAK,GAAA;UACjB,IAAE7H,aAAgB,CAAAR,KAAA;YAClB8D,CAAA,CAAAsE,cAAA;YACFtE,CAAA,CAAAwE,eAAA;YACAnE,mBAAA;UAAA;UAEA;QACA,KAAA6D,UAAA,CAAAO,GAAA;UACJpE,mBAAA;UACF;MAEA;IACE;IACA,MAAIqE,WAAC,GAAAA,CAAA,KAAuB;MACL,IAAA3I,EAAA;MACvB,CAAAA,EAAA,GAAAS,gBAAA,CAAAN,KAAA,qBAAAH,EAAA,CAAA4I,iBAAA;MACA,KAAAjI,aAAA,CAAAR,KAAyB,IAAAT,KAAA,CAAAwC,UAAA;QACzB2C,oBAAY;MAAA;MAGdP,mBAAA;MACQnB,IAAA;IACN;IACA,MAAA0B,oBAAyB,GAAAA,CAAA;MAC3B;QAAA1E;MAAA,IAAAoC,WAAA;MAEMxB,UAAA,CAAAZ,KAAA,GAAAA,KAAA;MACEa,gBAAU,CAAIb,KAAA,GAAAA,KAAA;IAEpB;IACE,MAAA0I,qBAAwB,GAAA5D,IAAA;MAC1B,IAAOjF,EAAA,EAAAoE,EAAA;MACL;QAAA0E;MAA6B,IAAA7D,IAAA;MAC7B,IAAAjD,QAAA,CAAA7B,KAAA;QACF,CAAAH,EAAA,GAAAS,gBAAA,CAAAN,KAAA,qBAAAH,EAAA,CAAA+I,iBAAA,CAAA9D,IAAA,GAAA6D,OAAA;MAAA,CACF;QAEM,CAAAA,OAAA,MAAA1E,EAAA,GAAA3D,gBAAgD,CAAAN,KAAA,qBAAAiE,EAAA,CAAA2E,iBAAA,CAAA9D,IAAA;QACpDX,mBAAiB;MACjB;IAEA;IAAc,MAAA0E,uBACI,GAAA/E,CAAA;MAChB,MAAA/D,MAAA,GAAA+D,CAAA,CAAA/D,MAAsB;MACpB,MAAE;QAAegI;MAAA,IAAAjE,CAAA;MACjB,QAAAiE,IAAiB;QACjB,KAAAC,UAAA,CAAAc,EAAA;QACE,KAAAd,UAAA,CAAAG,IAAA;UAAA;YACErE,CAAA,CAAAsE,cAAA;YACA,MAAAW,QAAA,GAAAhB,IAAA,KAAAC,UAAA,CAAAc,EAAA;YAAAE,SACI,CAAAC,UAAa,CAAAlJ,MAAA,EAAAgJ,QAAA,EAAkB,IAAAlK,UAAA,CAAAiF,CAAA;YACrC;UAAA;QAEF,KAAAkE,UAAA,CAAAC,KAAA;QACF,KAAAD,UAAA,CAAAE,WAAA;UAAAnI,MACgB,CAAA+G,KAAA;UAAA;MAEd;IACA;IACJ,MAAAoC,YAAA,GAAAA,CAAA;MACF,MAAAxD,IAAA,GAAA5E,WAAA,CAAAd,KAAA;MAEA,MAAAmJ,OAAA,GAAAzD,IAA2B,CAAAA,IAAA,CAAAjE,MAAA;MACzB7C,gBAAyB,GAAAiC,gBAAA,CAAAb,KAAA,OAAApB,gBAAA;MACzB,KAAAuK,OAAgB,KAAAvK,gBAAU,IAAUW,KAAA,CAAA2G,YAAA,IAAAR,IAAA,CAAAjE,MAAA,MACjB;MAEnB,IAAI0H,OAAY,CAAAlE,QAAA;QACdG,SAAA,CAAA+D,OAAA;MAEF;QACEA,OAAA,CAAAlE,QAAiB;MAAA;IAEjB;IACF,MAAAmE,WAAA,GAAAtF,CAAA;MACF,MAAAuF,EAAA,GAAAvF,CAAA,CAAA/D,MAAA;MAEM,MAAApC,IAAA,GAAAkB,UAAiC,CAAAiF,CAAA;MACrC,IAAAuF,EAAM,CAAAC,SAAO,KAAA3L,IAAA;QACPgD,WAAO,CAAWX,KAAA,OAAE;MAC1B;MACEgD,IAAA,UAAYc,CAAQ;IAAA,CACtB;IACA,MAAAyF,UAAc,GAACzF,CAAA;MACjBnD,WAAA,CAAAX,KAAA;MAEMgD,IAAA,SAAAc,CAAA;IACJ;IACA,MAAA0F,YAAc,GAAAC,QAAA;MAChB;QAAAzJ;MAAA,IAAAgC,aAAA;MAEM,KAAAhC,KAAA,EACE;MAEN,MAAY0J,MAAA,GAAAnK,KAAA,CAAAoK,YAAA,CAAA3J,KAAA;MAEN,IAAA4J,SAAA,CAAAF,MAAe;QAEjBA,MAAA,CAAAG,IAAA,CAAU1D,oBAAS,EAAA9C,KAAA,QACrB;MAA8C,OAE7C,IAAAqG,MAAA;QACHvD,oBAAsB,EAAO;MAC3B,CAAqB;QAChBvB,mBAAA;MACL;IAAoB,CACtB,EAAArF,KAAA,CAAAkK,QAAA;IACF,MAAAxJ,WAAiB,GAAAA,CAAA8C,GAAA,EAAAe,CAAA;MAEX,CAAAtD,aAAA,CAAAR,KAAe,IAAamE,mBAAsB;MACrD,IAAAL,CAAA,WAAuB,SAAAA,CAAA,CAAAtE,WAAA,EAExB;MAEMuD,GAAA,GAAAyG,YAAA,KAAiB5E,mBAAoB;IAAA,CAC7C;IAEM,MAAAkF,mBAAA,GAAuB9C,UAAA,IAC3B+C,MAAO,CAAAC,UAAA,CAAAC,SAAA,CAAAlL,OAAA,CAAAmL,UAAA,kBAAAlD,UAAA,EAAAhH,KAAA;IAAAmK,KAAA,CAAAzJ,SACa,EAAA6D,oBAAyB;IAC7C4F,KAAI,EAAAlI,YAAA,EAAAb,UAAA,QAAA7B,KAAA,CAAA2G,YAAA,GAAAT,oBAAA;IAEN0E,KAAA,CAAMrJ,WAAW,EAAoB;MAErC0D,QAAA,OAAAuC,WAAA;IAAA,EACE;IACAoD,KAAA,CAAAzI,QAAA;MACF,MAAA8C,QAAA;MAEA,MAAAwC,UAAA,GAAyB5G,KAAA,CAAAJ,KAAA,CAAAI,KAAA;MACdzB,kBAAA,GAAAmL,mBAAmB,CAAA9C,UAAA,KAAArI,kBAAA;MAC7BoI,WAAA;IAED;IACEoD,KAAA,CAAA/H,WAAe,EAAAsC,oBAAA;MAAA0F,SAAA;IAAA;IACTC,SAAA;MACe,MAAArD,UAAA,GAAA5G,KAAA,CAAAJ,KAAA,CAAAI,KAAA;MACT,MAAAkK,gBAAA,GAAAR,mBAAA,CAAA9C,UAAA;MACbrI,kBAAA,GAAAqI,UAAA,CAAAO,YAAA,IAAA+C,gBAAA;MAEDC,iBAAmB,CAAAvD,UAAA,EAAAD,WAAwB;IAE3C;IACQyD,MAAA;MAEA7C,eAAA;MAENrH,gBAAA;MACA6D,mBAAA;MACDH,UAAA;MAEY5B;IAAA;IAAA,QAAAqI,IAAA,EAAAC,MAAA;MAAA,OAAAC,SAAA,IAAAC,WAAA,CAAAC,KAAA,CAAAC,SAAA;QAIXC,OAAA;QAAA5K,GAAA,EAAAD,UAAA;QAAAkE,OAAA,EAAA5D,aAAA,CAAAR,KAAA;QAAAgL,UAAA,EAAAP,IAAA,CAAAO,UAAA;QAIA,iBAAAH,KAAA,CAAAhM,UAAA,EAAAiF,CAAA,cAAA2G,IAAA,CAAAQ,WAAA;QAAA,kBAAApN,aAAA;QAAA,uBAAA4M,IAAA,CAAAS,kBAAA;QAAA;QAIA;QAAA9M,SAAA,EAAAqM,IAAA,CAAArM,SAAA;QAAA+M,UAAA,KAAAN,KAAA,CAAAhM,UAAA,EAAAuM,SAAA,CAAApL,KAAA;QAAAqL,MAAA;QAIAC,IAAA;QAAAC,UAAA,EAAAd,IAAA,CAAAc,UAAA;QAAAC,MAAA,EAAA5G;MAAA;QAIA6G,OAAA,EAAAC,OAAA,QACDC,cAAA,EAAAhB,SAAA,IAAAiB,kBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}