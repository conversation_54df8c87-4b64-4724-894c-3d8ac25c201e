{"ast": null, "code": "import { defineComponent, ref, computed, provide, renderSlot } from 'vue';\nimport { POPPER_INJECTION_KEY } from './constants.mjs';\nimport { popperProps } from './popper.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPopper\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: popperProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const triggerRef = ref();\n    const popperInstanceRef = ref();\n    const contentRef = ref();\n    const referenceRef = ref();\n    const role = computed(() => props.role);\n    const popperProvides = {\n      triggerRef,\n      popperInstanceRef,\n      contentRef,\n      referenceRef,\n      role\n    };\n    expose(popperProvides);\n    provide(POPPER_INJECTION_KEY, popperProvides);\n    return (_ctx, _cache) => {\n      return renderSlot(_ctx.$slots, \"default\");\n    };\n  }\n});\nvar Popper = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"popper.vue\"]]);\nexport { Popper as default };", "map": {"version": 3, "names": ["name", "inheritAttrs", "triggerRef", "ref", "popperInstanceRef", "contentRef", "referenceRef", "role", "computed", "props", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expose", "provide", "POPPER_INJECTION_KEY", "_ctx", "_cache", "renderSlot", "$slots", "<PERSON><PERSON>", "_export_sfc", "_sfc_main"], "sources": ["../../../../../../packages/components/popper/src/popper.vue"], "sourcesContent": ["<template>\n  <slot />\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, provide, ref } from 'vue'\nimport { POPPER_INJECTION_KEY } from './constants'\nimport { popperProps } from './popper'\n\nimport type { Instance as PopperInstance } from '@popperjs/core'\nimport type { ElPopperInjectionContext } from './constants'\n\ndefineOptions({\n  name: '<PERSON>Pop<PERSON>',\n  inheritAttrs: false,\n})\nconst props = defineProps(popperProps)\n\nconst triggerRef = ref<HTMLElement>()\nconst popperInstanceRef = ref<PopperInstance>()\nconst contentRef = ref<HTMLElement>()\nconst referenceRef = ref<HTMLElement>()\nconst role = computed(() => props.role)\n\nconst popperProvides = {\n  /**\n   * @description trigger element\n   */\n  triggerRef,\n  /**\n   * @description popperjs instance\n   */\n  popperInstanceRef,\n  /**\n   * @description popper content element\n   */\n  contentRef,\n  /**\n   * @description popper reference element\n   */\n  referenceRef,\n  /**\n   * @description role determines how aria attributes are distributed\n   */\n  role,\n} as ElPopperInjectionContext\n\ndefineExpose(popperProvides)\n\nprovide(POPPER_INJECTION_KEY, popperProvides)\n</script>\n"], "mappings": ";;;;mCAYc;EACZA,IAAM;EACNC,YAAc;AAChB;;;;;;;;IAGA,MAAMC,UAAA,GAAaC,GAAiB;IACpC,MAAMC,iBAAA,GAAoBD,GAAoB;IAC9C,MAAME,UAAA,GAAaF,GAAiB;IACpC,MAAMG,YAAA,GAAeH,GAAiB;IACtC,MAAMI,IAAO,GAAAC,QAAA,CAAS,MAAMC,KAAA,CAAMF,IAAI;IAEtC,MAAMG,cAAiB;MAAAR,UAAA;MAAAE,iBAAA;MAAAC,UAAA;MAIrBC,YAAA;MAAAC;IAAA;IAAAI,MAAA,CAAAD,cAAA;IAIAE,OAAA,CAAAC,oBAAA,EAAAH,cAAA;IAAA,QAAAI,IAAA,EAAAC,MAAA;MAAA,OAAAC,UAAA,CAAAF,IAAA,CAAAG,MAAA;IAAA;EAAA;AAIA;AAAA,IAAAC,MAAA,kBAAAC,WAAA,CAAAC,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}