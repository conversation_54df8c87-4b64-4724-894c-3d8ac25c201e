{"ast": null, "code": "import { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../../utils/vue/icon.mjs';\nconst paginationNextProps = buildProps({\n  disabled: Boolean,\n  currentPage: {\n    type: Number,\n    default: 1\n  },\n  pageCount: {\n    type: Number,\n    default: 50\n  },\n  nextText: {\n    type: String\n  },\n  nextIcon: {\n    type: iconPropType\n  }\n});\nexport { paginationNextProps };", "map": {"version": 3, "names": ["paginationNextProps", "buildProps", "disabled", "Boolean", "currentPage", "type", "Number", "default", "pageCount", "nextText", "String", "nextIcon", "iconPropType"], "sources": ["../../../../../../../packages/components/pagination/src/components/next.ts"], "sourcesContent": ["import { buildProps, iconPropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Next from './next.vue'\n\nexport const paginationNextProps = buildProps({\n  disabled: Boolean,\n  currentPage: {\n    type: Number,\n    default: 1,\n  },\n  pageCount: {\n    type: Number,\n    default: 50,\n  },\n  nextText: {\n    type: String,\n  },\n  nextIcon: {\n    type: iconPropType,\n  },\n} as const)\n\nexport type PaginationNextProps = ExtractPropTypes<typeof paginationNextProps>\n\nexport type NextInstance = InstanceType<typeof Next> & unknown\n"], "mappings": ";;AACY,MAACA,mBAAmB,GAAGC,UAAU,CAAC;EAC5CC,QAAQ,EAAEC,OAAO;EACjBC,WAAW,EAAE;IACXC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,SAAS,EAAE;IACTH,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDE,QAAQ,EAAE;IACRJ,IAAI,EAAEK;EACV,CAAG;EACDC,QAAQ,EAAE;IACRN,IAAI,EAAEO;EACV;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}