{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"help-container\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_collapse_item = _resolveComponent(\"el-collapse-item\");\n  const _component_el_collapse = _resolveComponent(\"el-collapse\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => _cache[1] || (_cache[1] = [_createElementVNode(\"div\", {\n      class: \"card-header\"\n    }, [_createElementVNode(\"h2\", null, \"帮助中心\")], -1 /* HOISTED */)])),\n    default: _withCtx(() => [_createVNode(_component_el_collapse, {\n      modelValue: $setup.activeNames,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.activeNames = $event)\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_collapse_item, {\n        title: \"如何预约座位？\",\n        name: \"1\"\n      }, {\n        default: _withCtx(() => _cache[2] || (_cache[2] = [_createElementVNode(\"div\", {\n          class: \"help-content\"\n        }, [_createElementVNode(\"p\", null, \"预约座位的步骤如下：\"), _createElementVNode(\"ol\", null, [_createElementVNode(\"li\", null, \"在侧边栏菜单中点击\\\"座位管理\\\" -> \\\"预约座位\\\"\"), _createElementVNode(\"li\", null, \"选择您想要预约的自习室\"), _createElementVNode(\"li\", null, \"在座位地图上选择一个可用的座位（绿色表示可用）\"), _createElementVNode(\"li\", null, \"选择预约的时间段\"), _createElementVNode(\"li\", null, \"点击\\\"确认预约\\\"按钮\"), _createElementVNode(\"li\", null, \" 预约成功后，您可以在\\\"个人中心\\\" -> \\\"我的预约\\\"中查看预约详情 \")])], -1 /* HOISTED */)])),\n        _: 1 /* STABLE */,\n        __: [2]\n      }), _createVNode(_component_el_collapse_item, {\n        title: \"如何取消预约？\",\n        name: \"2\"\n      }, {\n        default: _withCtx(() => _cache[3] || (_cache[3] = [_createElementVNode(\"div\", {\n          class: \"help-content\"\n        }, [_createElementVNode(\"p\", null, \"取消预约的步骤如下：\"), _createElementVNode(\"ol\", null, [_createElementVNode(\"li\", null, \"在侧边栏菜单中点击\\\"个人中心\\\" -> \\\"我的预约\\\"\"), _createElementVNode(\"li\", null, \"找到您想要取消的预约记录\"), _createElementVNode(\"li\", null, \"点击\\\"取消预约\\\"按钮\"), _createElementVNode(\"li\", null, \"在弹出的确认对话框中点击\\\"确认\\\"\")]), _createElementVNode(\"p\", {\n          class: \"warning\"\n        }, \" 注意：预约开始前30分钟内取消预约将会扣除信誉分，请提前安排好您的时间。 \")], -1 /* HOISTED */)])),\n        _: 1 /* STABLE */,\n        __: [3]\n      }), _createVNode(_component_el_collapse_item, {\n        title: \"如何签到和签退？\",\n        name: \"3\"\n      }, {\n        default: _withCtx(() => _cache[4] || (_cache[4] = [_createElementVNode(\"div\", {\n          class: \"help-content\"\n        }, [_createElementVNode(\"p\", null, \"签到和签退的步骤如下：\"), _createElementVNode(\"ol\", null, [_createElementVNode(\"li\", null, \"在侧边栏菜单中点击\\\"个人中心\\\" -> \\\"我的预约\\\"\"), _createElementVNode(\"li\", null, \"找到当前正在进行的预约记录\"), _createElementVNode(\"li\", null, \"点击\\\"签到\\\"或\\\"签退\\\"按钮\"), _createElementVNode(\"li\", null, \"使用手机扫描生成的二维码完成操作\")]), _createElementVNode(\"p\", {\n          class: \"warning\"\n        }, \" 注意：预约开始后15分钟内未签到将视为爽约，会扣除信誉分。 \")], -1 /* HOISTED */)])),\n        _: 1 /* STABLE */,\n        __: [4]\n      }), _createVNode(_component_el_collapse_item, {\n        title: \"信誉分是什么？\",\n        name: \"4\"\n      }, {\n        default: _withCtx(() => _cache[5] || (_cache[5] = [_createElementVNode(\"div\", {\n          class: \"help-content\"\n        }, [_createElementVNode(\"p\", null, \"信誉分是衡量用户使用自习室行为的指标：\"), _createElementVNode(\"ul\", null, [_createElementVNode(\"li\", null, \"新用户初始信誉分为100分\"), _createElementVNode(\"li\", null, \"按时签到签退、正常使用座位会维持或提高信誉分\"), _createElementVNode(\"li\", null, \"爽约、迟到、提前离开等行为会扣除信誉分\"), _createElementVNode(\"li\", null, \"信誉分低于60分将限制预约功能\"), _createElementVNode(\"li\", null, \"信誉分低于30分将暂停使用系统的权限\")]), _createElementVNode(\"p\", null, \" 您可以在\\\"个人中心\\\" -> \\\"信誉分记录\\\"中查看详细的信誉分变动记录。 \")], -1 /* HOISTED */)])),\n        _: 1 /* STABLE */,\n        __: [5]\n      }), _createVNode(_component_el_collapse_item, {\n        title: \"联系管理员\",\n        name: \"5\"\n      }, {\n        default: _withCtx(() => _cache[6] || (_cache[6] = [_createElementVNode(\"div\", {\n          class: \"help-content\"\n        }, [_createElementVNode(\"p\", null, \"如果您在使用过程中遇到任何问题，可以通过以下方式联系管理员：\"), _createElementVNode(\"ul\", null, [_createElementVNode(\"li\", null, \"电子邮件：<EMAIL>\"), _createElementVNode(\"li\", null, \"电话：123-4567-8910\"), _createElementVNode(\"li\", null, \"前台服务台：图书馆一楼大厅\")]), _createElementVNode(\"p\", null, \"服务时间：周一至周日 8:00-22:00\")], -1 /* HOISTED */)])),\n        _: 1 /* STABLE */,\n        __: [6]\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_cache", "_createElementVNode", "default", "_component_el_collapse", "modelValue", "$setup", "activeNames", "$event", "_component_el_collapse_item", "title", "name", "_", "__"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Help.vue"], "sourcesContent": ["<template>\n  <div class=\"help-container\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h2>帮助中心</h2>\n        </div>\n      </template>\n\n      <el-collapse v-model=\"activeNames\">\n        <el-collapse-item title=\"如何预约座位？\" name=\"1\">\n          <div class=\"help-content\">\n            <p>预约座位的步骤如下：</p>\n            <ol>\n              <li>在侧边栏菜单中点击\"座位管理\" -> \"预约座位\"</li>\n              <li>选择您想要预约的自习室</li>\n              <li>在座位地图上选择一个可用的座位（绿色表示可用）</li>\n              <li>选择预约的时间段</li>\n              <li>点击\"确认预约\"按钮</li>\n              <li>\n                预约成功后，您可以在\"个人中心\" -> \"我的预约\"中查看预约详情\n              </li>\n            </ol>\n          </div>\n        </el-collapse-item>\n\n        <el-collapse-item title=\"如何取消预约？\" name=\"2\">\n          <div class=\"help-content\">\n            <p>取消预约的步骤如下：</p>\n            <ol>\n              <li>在侧边栏菜单中点击\"个人中心\" -> \"我的预约\"</li>\n              <li>找到您想要取消的预约记录</li>\n              <li>点击\"取消预约\"按钮</li>\n              <li>在弹出的确认对话框中点击\"确认\"</li>\n            </ol>\n            <p class=\"warning\">\n              注意：预约开始前30分钟内取消预约将会扣除信誉分，请提前安排好您的时间。\n            </p>\n          </div>\n        </el-collapse-item>\n\n        <el-collapse-item title=\"如何签到和签退？\" name=\"3\">\n          <div class=\"help-content\">\n            <p>签到和签退的步骤如下：</p>\n            <ol>\n              <li>在侧边栏菜单中点击\"个人中心\" -> \"我的预约\"</li>\n              <li>找到当前正在进行的预约记录</li>\n              <li>点击\"签到\"或\"签退\"按钮</li>\n              <li>使用手机扫描生成的二维码完成操作</li>\n            </ol>\n            <p class=\"warning\">\n              注意：预约开始后15分钟内未签到将视为爽约，会扣除信誉分。\n            </p>\n          </div>\n        </el-collapse-item>\n\n        <el-collapse-item title=\"信誉分是什么？\" name=\"4\">\n          <div class=\"help-content\">\n            <p>信誉分是衡量用户使用自习室行为的指标：</p>\n            <ul>\n              <li>新用户初始信誉分为100分</li>\n              <li>按时签到签退、正常使用座位会维持或提高信誉分</li>\n              <li>爽约、迟到、提前离开等行为会扣除信誉分</li>\n              <li>信誉分低于60分将限制预约功能</li>\n              <li>信誉分低于30分将暂停使用系统的权限</li>\n            </ul>\n            <p>\n              您可以在\"个人中心\" -> \"信誉分记录\"中查看详细的信誉分变动记录。\n            </p>\n          </div>\n        </el-collapse-item>\n\n        <el-collapse-item title=\"联系管理员\" name=\"5\">\n          <div class=\"help-content\">\n            <p>如果您在使用过程中遇到任何问题，可以通过以下方式联系管理员：</p>\n            <ul>\n              <li>电子邮件：<EMAIL></li>\n              <li>电话：123-4567-8910</li>\n              <li>前台服务台：图书馆一楼大厅</li>\n            </ul>\n            <p>服务时间：周一至周日 8:00-22:00</p>\n          </div>\n        </el-collapse-item>\n      </el-collapse>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref } from \"vue\";\n\nexport default {\n  name: \"HelpView\",\n  setup() {\n    const activeNames = ref([\"1\"]);\n\n    return {\n      activeNames,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.help-container {\n  padding: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.help-content {\n  padding: 10px;\n  line-height: 1.6;\n\n  ol,\n  ul {\n    padding-left: 20px;\n    margin: 10px 0;\n  }\n\n  .warning {\n    color: #e6a23c;\n    font-weight: bold;\n    margin: 10px 0;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;;;;uBAA3BC,mBAAA,CAoFM,OApFNC,UAoFM,GAnFJC,YAAA,CAkFUC,kBAAA;IAjFGC,MAAM,EAAAC,QAAA,CACf,MAEMC,MAAA,QAAAA,MAAA,OAFNC,mBAAA,CAEM;MAFDR,KAAK,EAAC;IAAa,IACtBQ,mBAAA,CAAa,YAAT,MAAI,E;IALlBC,OAAA,EAAAH,QAAA,CASM,MA0Ec,CA1EdH,YAAA,CA0EcO,sBAAA;MAnFpBC,UAAA,EAS4BC,MAAA,CAAAC,WAAW;MATvC,uBAAAN,MAAA,QAAAA,MAAA,MAAAO,MAAA,IAS4BF,MAAA,CAAAC,WAAW,GAAAC,MAAA;;MATvCL,OAAA,EAAAH,QAAA,CAUQ,MAcmB,CAdnBH,YAAA,CAcmBY,2BAAA;QAdDC,KAAK,EAAC,SAAS;QAACC,IAAI,EAAC;;QAV/CR,OAAA,EAAAH,QAAA,CAWU,MAYMC,MAAA,QAAAA,MAAA,OAZNC,mBAAA,CAYM;UAZDR,KAAK,EAAC;QAAc,IACvBQ,mBAAA,CAAiB,WAAd,YAAU,GACbA,mBAAA,CASK,aARHA,mBAAA,CAAkC,YAA9B,+BAAyB,GAC7BA,mBAAA,CAAoB,YAAhB,aAAW,GACfA,mBAAA,CAAgC,YAA5B,yBAAuB,GAC3BA,mBAAA,CAAiB,YAAb,UAAQ,GACZA,mBAAA,CAAmB,YAAf,cAAU,GACdA,mBAAA,CAEK,YAFD,yCAEJ,E;QArBdU,CAAA;QAAAC,EAAA;UA0BQhB,YAAA,CAamBY,2BAAA;QAbDC,KAAK,EAAC,SAAS;QAACC,IAAI,EAAC;;QA1B/CR,OAAA,EAAAH,QAAA,CA2BU,MAWMC,MAAA,QAAAA,MAAA,OAXNC,mBAAA,CAWM;UAXDR,KAAK,EAAC;QAAc,IACvBQ,mBAAA,CAAiB,WAAd,YAAU,GACbA,mBAAA,CAKK,aAJHA,mBAAA,CAAkC,YAA9B,+BAAyB,GAC7BA,mBAAA,CAAqB,YAAjB,cAAY,GAChBA,mBAAA,CAAmB,YAAf,cAAU,GACdA,mBAAA,CAAyB,YAArB,oBAAgB,E,GAEtBA,mBAAA,CAEI;UAFDR,KAAK,EAAC;QAAS,GAAC,wCAEnB,E;QArCZkB,CAAA;QAAAC,EAAA;UAyCQhB,YAAA,CAamBY,2BAAA;QAbDC,KAAK,EAAC,UAAU;QAACC,IAAI,EAAC;;QAzChDR,OAAA,EAAAH,QAAA,CA0CU,MAWMC,MAAA,QAAAA,MAAA,OAXNC,mBAAA,CAWM;UAXDR,KAAK,EAAC;QAAc,IACvBQ,mBAAA,CAAkB,WAAf,aAAW,GACdA,mBAAA,CAKK,aAJHA,mBAAA,CAAkC,YAA9B,+BAAyB,GAC7BA,mBAAA,CAAsB,YAAlB,eAAa,GACjBA,mBAAA,CAAsB,YAAlB,mBAAa,GACjBA,mBAAA,CAAyB,YAArB,kBAAgB,E,GAEtBA,mBAAA,CAEI;UAFDR,KAAK,EAAC;QAAS,GAAC,iCAEnB,E;QApDZkB,CAAA;QAAAC,EAAA;UAwDQhB,YAAA,CAcmBY,2BAAA;QAdDC,KAAK,EAAC,SAAS;QAACC,IAAI,EAAC;;QAxD/CR,OAAA,EAAAH,QAAA,CAyDU,MAYMC,MAAA,QAAAA,MAAA,OAZNC,mBAAA,CAYM;UAZDR,KAAK,EAAC;QAAc,IACvBQ,mBAAA,CAA0B,WAAvB,qBAAmB,GACtBA,mBAAA,CAMK,aALHA,mBAAA,CAAsB,YAAlB,eAAa,GACjBA,mBAAA,CAA+B,YAA3B,wBAAsB,GAC1BA,mBAAA,CAA4B,YAAxB,qBAAmB,GACvBA,mBAAA,CAAwB,YAApB,iBAAe,GACnBA,mBAAA,CAA2B,YAAvB,oBAAkB,E,GAExBA,mBAAA,CAEI,WAFD,2CAEH,E;QApEZU,CAAA;QAAAC,EAAA;UAwEQhB,YAAA,CAUmBY,2BAAA;QAVDC,KAAK,EAAC,OAAO;QAACC,IAAI,EAAC;;QAxE7CR,OAAA,EAAAH,QAAA,CAyEU,MAQMC,MAAA,QAAAA,MAAA,OARNC,mBAAA,CAQM;UARDR,KAAK,EAAC;QAAc,IACvBQ,mBAAA,CAAqC,WAAlC,gCAA8B,GACjCA,mBAAA,CAIK,aAHHA,mBAAA,CAAkC,YAA9B,2BAAyB,GAC7BA,mBAAA,CAAyB,YAArB,kBAAgB,GACpBA,mBAAA,CAAsB,YAAlB,eAAa,E,GAEnBA,mBAAA,CAA4B,WAAzB,uBAAqB,E;QAhFpCU,CAAA;QAAAC,EAAA;;MAAAD,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}