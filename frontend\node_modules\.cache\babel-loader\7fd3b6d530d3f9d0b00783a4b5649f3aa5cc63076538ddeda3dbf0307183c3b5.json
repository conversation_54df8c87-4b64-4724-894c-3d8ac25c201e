{"ast": null, "code": "import { computed, unref, ref } from 'vue';\nimport { useZIndex } from '../../../../hooks/use-z-index/index.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isNumber } from '../../../../utils/types.mjs';\nconst usePopperContentDOM = (props, {\n  attributes,\n  styles,\n  role\n}) => {\n  const {\n    nextZIndex\n  } = useZIndex();\n  const ns = useNamespace(\"popper\");\n  const contentAttrs = computed(() => unref(attributes).popper);\n  const contentZIndex = ref(isNumber(props.zIndex) ? props.zIndex : nextZIndex());\n  const contentClass = computed(() => [ns.b(), ns.is(\"pure\", props.pure), ns.is(props.effect), props.popperClass]);\n  const contentStyle = computed(() => {\n    return [{\n      zIndex: unref(contentZIndex)\n    }, unref(styles).popper, props.popperStyle || {}];\n  });\n  const ariaModal = computed(() => role.value === \"dialog\" ? \"false\" : void 0);\n  const arrowStyle = computed(() => unref(styles).arrow || {});\n  const updateZIndex = () => {\n    contentZIndex.value = isNumber(props.zIndex) ? props.zIndex : nextZIndex();\n  };\n  return {\n    ariaModal,\n    arrowStyle,\n    contentAttrs,\n    contentClass,\n    contentStyle,\n    contentZIndex,\n    updateZIndex\n  };\n};\nexport { usePopperContentDOM };", "map": {"version": 3, "names": ["usePopperContentDOM", "props", "attributes", "styles", "role", "nextZIndex", "useZIndex", "ns", "useNamespace", "contentAttrs", "computed", "unref", "popper", "contentZIndex", "ref", "isNumber", "zIndex", "contentClass", "b", "is", "pure", "effect", "popperClass", "contentStyle", "popperStyle", "ariaModal", "value", "arrowStyle", "arrow", "updateZIndex"], "sources": ["../../../../../../../packages/components/popper/src/composables/use-content-dom.ts"], "sourcesContent": ["import { computed, ref, unref } from 'vue'\nimport { useNamespace, useZIndex } from '@element-plus/hooks'\n\nimport { isNumber } from '@element-plus/utils'\nimport type { CSSProperties, StyleValue } from 'vue'\nimport type { UsePopperReturn } from '@element-plus/hooks'\nimport type { UsePopperContentReturn } from './use-content'\nimport type { PopperContentProps } from '../content'\n\nexport const usePopperContentDOM = (\n  props: PopperContentProps,\n  {\n    attributes,\n    styles,\n    role,\n  }: Pick<UsePopperReturn, 'attributes' | 'styles'> &\n    Pick<UsePopperContentReturn, 'role'>\n) => {\n  const { nextZIndex } = useZIndex()\n  const ns = useNamespace('popper')\n\n  const contentAttrs = computed(() => unref(attributes).popper)\n  const contentZIndex = ref<number>(\n    isNumber(props.zIndex) ? props.zIndex : nextZIndex()\n  )\n  const contentClass = computed(() => [\n    ns.b(),\n    ns.is('pure', props.pure),\n    ns.is(props.effect),\n    props.popperClass,\n  ])\n  const contentStyle = computed<StyleValue[]>(() => {\n    return [\n      { zIndex: unref(contentZIndex) } as CSSProperties,\n      unref(styles).popper as CSSProperties,\n      props.popperStyle || {},\n    ]\n  })\n  const ariaModal = computed<string | undefined>(() =>\n    role.value === 'dialog' ? 'false' : undefined\n  )\n  const arrowStyle = computed(\n    () => (unref(styles).arrow || {}) as CSSProperties\n  )\n\n  const updateZIndex = () => {\n    contentZIndex.value = isNumber(props.zIndex) ? props.zIndex : nextZIndex()\n  }\n\n  return {\n    ariaModal,\n    arrowStyle,\n    contentAttrs,\n    contentClass,\n    contentStyle,\n    contentZIndex,\n\n    updateZIndex,\n  }\n}\n\nexport type UsePopperContentDOMReturn = ReturnType<typeof usePopperContentDOM>\n"], "mappings": ";;;;AAGY,MAACA,mBAAmB,GAAGA,CAACC,KAAK,EAAE;EACzCC,UAAU;EACVC,MAAM;EACNC;AACF,CAAC,KAAK;EACJ,MAAM;IAAEC;EAAU,CAAE,GAAGC,SAAS,EAAE;EAClC,MAAMC,EAAE,GAAGC,YAAY,CAAC,QAAQ,CAAC;EACjC,MAAMC,YAAY,GAAGC,QAAQ,CAAC,MAAMC,KAAK,CAACT,UAAU,CAAC,CAACU,MAAM,CAAC;EAC7D,MAAMC,aAAa,GAAGC,GAAG,CAACC,QAAQ,CAACd,KAAK,CAACe,MAAM,CAAC,GAAGf,KAAK,CAACe,MAAM,GAAGX,UAAU,EAAE,CAAC;EAC/E,MAAMY,YAAY,GAAGP,QAAQ,CAAC,MAAM,CAClCH,EAAE,CAACW,CAAC,EAAE,EACNX,EAAE,CAACY,EAAE,CAAC,MAAM,EAAElB,KAAK,CAACmB,IAAI,CAAC,EACzBb,EAAE,CAACY,EAAE,CAAClB,KAAK,CAACoB,MAAM,CAAC,EACnBpB,KAAK,CAACqB,WAAW,CAClB,CAAC;EACF,MAAMC,YAAY,GAAGb,QAAQ,CAAC,MAAM;IAClC,OAAO,CACL;MAAEM,MAAM,EAAEL,KAAK,CAACE,aAAa;IAAC,CAAE,EAChCF,KAAK,CAACR,MAAM,CAAC,CAACS,MAAM,EACpBX,KAAK,CAACuB,WAAW,IAAI,EAAE,CACxB;EACL,CAAG,CAAC;EACF,MAAMC,SAAS,GAAGf,QAAQ,CAAC,MAAMN,IAAI,CAACsB,KAAK,KAAK,QAAQ,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC;EAC5E,MAAMC,UAAU,GAAGjB,QAAQ,CAAC,MAAMC,KAAK,CAACR,MAAM,CAAC,CAACyB,KAAK,IAAI,EAAE,CAAC;EAC5D,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBhB,aAAa,CAACa,KAAK,GAAGX,QAAQ,CAACd,KAAK,CAACe,MAAM,CAAC,GAAGf,KAAK,CAACe,MAAM,GAAGX,UAAU,EAAE;EAC9E,CAAG;EACD,OAAO;IACLoB,SAAS;IACTE,UAAU;IACVlB,YAAY;IACZQ,YAAY;IACZM,YAAY;IACZV,aAAa;IACbgB;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}