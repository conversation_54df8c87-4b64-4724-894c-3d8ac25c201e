{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport { getCurrentInstance, ref, unref } from 'vue';\nimport { isNull } from 'lodash-unified';\nimport { getRowIdentity } from '../util.mjs';\nfunction useCurrent(watcherData) {\n  const instance = getCurrentInstance();\n  const _currentRowKey = ref(null);\n  const currentRow = ref(null);\n  const setCurrentRowKey = key => {\n    instance.store.assertRowKey();\n    _currentRowKey.value = key;\n    setCurrentRowByKey(key);\n  };\n  const restoreCurrentRowKey = () => {\n    _currentRowKey.value = null;\n  };\n  const setCurrentRowByKey = key => {\n    const {\n      data,\n      rowKey\n    } = watcherData;\n    let _currentRow = null;\n    if (rowKey.value) {\n      _currentRow = (unref(data) || []).find(item => getRowIdentity(item, rowKey.value) === key);\n    }\n    currentRow.value = _currentRow;\n    instance.emit(\"current-change\", currentRow.value, null);\n  };\n  const updateCurrentRow = _currentRow => {\n    const oldCurrentRow = currentRow.value;\n    if (_currentRow && _currentRow !== oldCurrentRow) {\n      currentRow.value = _currentRow;\n      instance.emit(\"current-change\", currentRow.value, oldCurrentRow);\n      return;\n    }\n    if (!_currentRow && oldCurrentRow) {\n      currentRow.value = null;\n      instance.emit(\"current-change\", null, oldCurrentRow);\n    }\n  };\n  const updateCurrentRowData = () => {\n    const rowKey = watcherData.rowKey.value;\n    const data = watcherData.data.value || [];\n    const oldCurrentRow = currentRow.value;\n    if (!data.includes(oldCurrentRow) && oldCurrentRow) {\n      if (rowKey) {\n        const currentRowKey = getRowIdentity(oldCurrentRow, rowKey);\n        setCurrentRowByKey(currentRowKey);\n      } else {\n        currentRow.value = null;\n      }\n      if (isNull(currentRow.value)) {\n        instance.emit(\"current-change\", null, oldCurrentRow);\n      }\n    } else if (_currentRowKey.value) {\n      setCurrentRowByKey(_currentRowKey.value);\n      restoreCurrentRowKey();\n    }\n  };\n  return {\n    setCurrentRowKey,\n    restoreCurrentRowKey,\n    setCurrentRowByKey,\n    updateCurrentRow,\n    updateCurrentRowData,\n    states: {\n      _currentRowKey,\n      currentRow\n    }\n  };\n}\nexport { useCurrent as default };", "map": {"version": 3, "names": ["useCurrent", "watcherData", "instance", "getCurrentInstance", "_currentRowKey", "ref", "currentRow", "setCurrentRowKey", "key", "store", "assertRowKey", "value", "setCurrentRowByKey", "restoreCurrentRowKey", "data", "<PERSON><PERSON><PERSON>", "_currentRow", "unref", "find", "item", "getRowIdentity", "emit", "updateCurrentRow", "oldCurrentRow", "updateCurrentRowData", "includes", "currentRowKey", "isNull", "states"], "sources": ["../../../../../../../packages/components/table/src/store/current.ts"], "sourcesContent": ["// @ts-nocheck\nimport { getCurrentInstance, ref, unref } from 'vue'\nimport { isNull } from 'lodash-unified'\nimport { getRowIdentity } from '../util'\n\nimport type { Ref } from 'vue'\nimport type { Table } from '../table/defaults'\nimport type { WatcherPropsData } from '.'\n\nfunction useCurrent<T>(watcherData: WatcherPropsData<T>) {\n  const instance = getCurrentInstance() as Table<T>\n  const _currentRowKey = ref<string>(null)\n  const currentRow: Ref<T> = ref(null)\n\n  const setCurrentRowKey = (key: string) => {\n    instance.store.assertRowKey()\n    _currentRowKey.value = key\n    setCurrentRowByKey(key)\n  }\n\n  const restoreCurrentRowKey = () => {\n    _currentRowKey.value = null\n  }\n\n  const setCurrentRowByKey = (key: string) => {\n    const { data, rowKey } = watcherData\n    let _currentRow = null\n    if (rowKey.value) {\n      _currentRow = (unref(data) || []).find(\n        (item) => getRowIdentity(item, rowKey.value) === key\n      )\n    }\n    currentRow.value = _currentRow\n    instance.emit('current-change', currentRow.value, null)\n  }\n\n  const updateCurrentRow = (_currentRow: T) => {\n    const oldCurrentRow = currentRow.value\n    if (_currentRow && _currentRow !== oldCurrentRow) {\n      currentRow.value = _currentRow\n      instance.emit('current-change', currentRow.value, oldCurrentRow)\n      return\n    }\n    if (!_currentRow && oldCurrentRow) {\n      currentRow.value = null\n      instance.emit('current-change', null, oldCurrentRow)\n    }\n  }\n\n  const updateCurrentRowData = () => {\n    const rowKey = watcherData.rowKey.value\n    // data 为 null 时，解构时的默认值会被忽略\n    const data = watcherData.data.value || []\n    const oldCurrentRow = currentRow.value\n    // 当 currentRow 不在 data 中时尝试更新数据\n    if (!data.includes(oldCurrentRow) && oldCurrentRow) {\n      if (rowKey) {\n        const currentRowKey = getRowIdentity(oldCurrentRow, rowKey)\n        setCurrentRowByKey(currentRowKey)\n      } else {\n        currentRow.value = null\n      }\n      if (isNull(currentRow.value)) {\n        instance.emit('current-change', null, oldCurrentRow)\n      }\n    } else if (_currentRowKey.value) {\n      // 把初始时下设置的 rowKey 转化成 rowData\n      setCurrentRowByKey(_currentRowKey.value)\n      restoreCurrentRowKey()\n    }\n  }\n\n  return {\n    setCurrentRowKey,\n    restoreCurrentRowKey,\n    setCurrentRowByKey,\n    updateCurrentRow,\n    updateCurrentRowData,\n    states: {\n      _currentRowKey,\n      currentRow,\n    },\n  }\n}\n\nexport default useCurrent\n"], "mappings": ";;;;;AAGA,SAASA,UAAUA,CAACC,WAAW,EAAE;EAC/B,MAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,MAAMC,cAAc,GAAGC,GAAG,CAAC,IAAI,CAAC;EAChC,MAAMC,UAAU,GAAGD,GAAG,CAAC,IAAI,CAAC;EAC5B,MAAME,gBAAgB,GAAIC,GAAG,IAAK;IAChCN,QAAQ,CAACO,KAAK,CAACC,YAAY,EAAE;IAC7BN,cAAc,CAACO,KAAK,GAAGH,GAAG;IAC1BI,kBAAkB,CAACJ,GAAG,CAAC;EAC3B,CAAG;EACD,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjCT,cAAc,CAACO,KAAK,GAAG,IAAI;EAC/B,CAAG;EACD,MAAMC,kBAAkB,GAAIJ,GAAG,IAAK;IAClC,MAAM;MAAEM,IAAI;MAAEC;IAAM,CAAE,GAAGd,WAAW;IACpC,IAAIe,WAAW,GAAG,IAAI;IACtB,IAAID,MAAM,CAACJ,KAAK,EAAE;MAChBK,WAAW,GAAG,CAACC,KAAK,CAACH,IAAI,CAAC,IAAI,EAAE,EAAEI,IAAI,CAAEC,IAAI,IAAKC,cAAc,CAACD,IAAI,EAAEJ,MAAM,CAACJ,KAAK,CAAC,KAAKH,GAAG,CAAC;IAClG;IACIF,UAAU,CAACK,KAAK,GAAGK,WAAW;IAC9Bd,QAAQ,CAACmB,IAAI,CAAC,gBAAgB,EAAEf,UAAU,CAACK,KAAK,EAAE,IAAI,CAAC;EAC3D,CAAG;EACD,MAAMW,gBAAgB,GAAIN,WAAW,IAAK;IACxC,MAAMO,aAAa,GAAGjB,UAAU,CAACK,KAAK;IACtC,IAAIK,WAAW,IAAIA,WAAW,KAAKO,aAAa,EAAE;MAChDjB,UAAU,CAACK,KAAK,GAAGK,WAAW;MAC9Bd,QAAQ,CAACmB,IAAI,CAAC,gBAAgB,EAAEf,UAAU,CAACK,KAAK,EAAEY,aAAa,CAAC;MAChE;IACN;IACI,IAAI,CAACP,WAAW,IAAIO,aAAa,EAAE;MACjCjB,UAAU,CAACK,KAAK,GAAG,IAAI;MACvBT,QAAQ,CAACmB,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAEE,aAAa,CAAC;IAC1D;EACA,CAAG;EACD,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMT,MAAM,GAAGd,WAAW,CAACc,MAAM,CAACJ,KAAK;IACvC,MAAMG,IAAI,GAAGb,WAAW,CAACa,IAAI,CAACH,KAAK,IAAI,EAAE;IACzC,MAAMY,aAAa,GAAGjB,UAAU,CAACK,KAAK;IACtC,IAAI,CAACG,IAAI,CAACW,QAAQ,CAACF,aAAa,CAAC,IAAIA,aAAa,EAAE;MAClD,IAAIR,MAAM,EAAE;QACV,MAAMW,aAAa,GAAGN,cAAc,CAACG,aAAa,EAAER,MAAM,CAAC;QAC3DH,kBAAkB,CAACc,aAAa,CAAC;MACzC,CAAO,MAAM;QACLpB,UAAU,CAACK,KAAK,GAAG,IAAI;MAC/B;MACM,IAAIgB,MAAM,CAACrB,UAAU,CAACK,KAAK,CAAC,EAAE;QAC5BT,QAAQ,CAACmB,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAEE,aAAa,CAAC;MAC5D;IACA,CAAK,MAAM,IAAInB,cAAc,CAACO,KAAK,EAAE;MAC/BC,kBAAkB,CAACR,cAAc,CAACO,KAAK,CAAC;MACxCE,oBAAoB,EAAE;IAC5B;EACA,CAAG;EACD,OAAO;IACLN,gBAAgB;IAChBM,oBAAoB;IACpBD,kBAAkB;IAClBU,gBAAgB;IAChBE,oBAAoB;IACpBI,MAAM,EAAE;MACNxB,cAAc;MACdE;IACN;EACA,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}