{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isSameOrAfter = t();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, t) {\n    t.prototype.isSameOrAfter = function (e, t) {\n      return this.isSame(e, t) || this.isAfter(e, t);\n    };\n  };\n});", "map": {"version": 3, "names": ["e", "t", "exports", "module", "define", "amd", "globalThis", "self", "dayjs_plugin_isSameOrAfter", "prototype", "isSameOrAfter", "isSame", "isAfter"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/dayjs/plugin/isSameOrAfter.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isSameOrAfter=t()}(this,(function(){\"use strict\";return function(e,t){t.prototype.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)}}}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAACH,CAAC,CAAC,GAAC,CAACD,CAAC,GAAC,WAAW,IAAE,OAAOM,UAAU,GAACA,UAAU,GAACN,CAAC,IAAEO,IAAI,EAAEC,0BAA0B,GAACP,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC,IAAI,EAAE,YAAU;EAAC,YAAY;;EAAC,OAAO,UAASD,CAAC,EAACC,CAAC,EAAC;IAACA,CAAC,CAACQ,SAAS,CAACC,aAAa,GAAC,UAASV,CAAC,EAACC,CAAC,EAAC;MAAC,OAAO,IAAI,CAACU,MAAM,CAACX,CAAC,EAACC,CAAC,CAAC,IAAE,IAAI,CAACW,OAAO,CAACZ,CAAC,EAACC,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}