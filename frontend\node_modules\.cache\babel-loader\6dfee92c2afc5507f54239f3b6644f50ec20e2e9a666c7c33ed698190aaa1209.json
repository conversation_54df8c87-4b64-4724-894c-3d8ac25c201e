{"ast": null, "code": "import { computed, ref, watchEffect } from 'vue';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isArray } from '@vue/shared';\nimport { isNumber } from '../../../utils/types.mjs';\nconst SIZE_MAP = {\n  small: 8,\n  default: 12,\n  large: 16\n};\nfunction useSpace(props) {\n  const ns = useNamespace(\"space\");\n  const classes = computed(() => [ns.b(), ns.m(props.direction), props.class]);\n  const horizontalSize = ref(0);\n  const verticalSize = ref(0);\n  const containerStyle = computed(() => {\n    const wrapKls = props.wrap || props.fill ? {\n      flexWrap: \"wrap\"\n    } : {};\n    const alignment = {\n      alignItems: props.alignment\n    };\n    const gap = {\n      rowGap: `${verticalSize.value}px`,\n      columnGap: `${horizontalSize.value}px`\n    };\n    return [wrapKls, alignment, gap, props.style];\n  });\n  const itemStyle = computed(() => {\n    return props.fill ? {\n      flexGrow: 1,\n      minWidth: `${props.fillRatio}%`\n    } : {};\n  });\n  watchEffect(() => {\n    const {\n      size = \"small\",\n      wrap,\n      direction: dir,\n      fill\n    } = props;\n    if (isArray(size)) {\n      const [h = 0, v = 0] = size;\n      horizontalSize.value = h;\n      verticalSize.value = v;\n    } else {\n      let val;\n      if (isNumber(size)) {\n        val = size;\n      } else {\n        val = SIZE_MAP[size || \"small\"] || SIZE_MAP.small;\n      }\n      if ((wrap || fill) && dir === \"horizontal\") {\n        horizontalSize.value = verticalSize.value = val;\n      } else {\n        if (dir === \"horizontal\") {\n          horizontalSize.value = val;\n          verticalSize.value = 0;\n        } else {\n          verticalSize.value = val;\n          horizontalSize.value = 0;\n        }\n      }\n    }\n  });\n  return {\n    classes,\n    containerStyle,\n    itemStyle\n  };\n}\nexport { useSpace };", "map": {"version": 3, "names": ["SIZE_MAP", "small", "default", "large", "useSpace", "props", "ns", "useNamespace", "classes", "computed", "b", "m", "direction", "class", "horizontalSize", "ref", "verticalSize", "containerStyle", "wrapKls", "wrap", "fill", "flexWrap", "alignment", "alignItems", "gap", "rowGap", "value", "columnGap", "style", "itemStyle", "flexGrow", "min<PERSON><PERSON><PERSON>", "fillRatio", "watchEffect", "size", "dir", "isArray", "h", "v", "val", "isNumber"], "sources": ["../../../../../../packages/components/space/src/use-space.ts"], "sourcesContent": ["import { computed, ref, watchEffect } from 'vue'\nimport { isArray, isNumber } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { SpaceProps } from './space'\nimport type { CSSProperties, StyleValue } from 'vue'\n\nconst SIZE_MAP = {\n  small: 8,\n  default: 12,\n  large: 16,\n} as const\n\nexport function useSpace(props: SpaceProps) {\n  const ns = useNamespace('space')\n\n  const classes = computed(() => [ns.b(), ns.m(props.direction), props.class])\n\n  const horizontalSize = ref(0)\n  const verticalSize = ref(0)\n\n  const containerStyle = computed<StyleValue>(() => {\n    const wrapKls: CSSProperties =\n      props.wrap || props.fill ? { flexWrap: 'wrap' } : {}\n    const alignment: CSSProperties = {\n      alignItems: props.alignment,\n    }\n    const gap: CSSProperties = {\n      rowGap: `${verticalSize.value}px`,\n      columnGap: `${horizontalSize.value}px`,\n    }\n    return [wrapKls, alignment, gap, props.style]\n  })\n\n  const itemStyle = computed<StyleValue>(() => {\n    return props.fill ? { flexGrow: 1, minWidth: `${props.fillRatio}%` } : {}\n  })\n\n  watchEffect(() => {\n    const { size = 'small', wrap, direction: dir, fill } = props\n\n    // when the specified size have been given\n    if (isArray(size)) {\n      const [h = 0, v = 0] = size\n      horizontalSize.value = h\n      verticalSize.value = v\n    } else {\n      let val: number\n      if (isNumber(size)) {\n        val = size\n      } else {\n        val = SIZE_MAP[size || 'small'] || SIZE_MAP.small\n      }\n\n      if ((wrap || fill) && dir === 'horizontal') {\n        horizontalSize.value = verticalSize.value = val\n      } else {\n        if (dir === 'horizontal') {\n          horizontalSize.value = val\n          verticalSize.value = 0\n        } else {\n          verticalSize.value = val\n          horizontalSize.value = 0\n        }\n      }\n    }\n  })\n\n  return {\n    classes,\n    containerStyle,\n    itemStyle,\n  }\n}\n"], "mappings": ";;;;AAGA,MAAMA,QAAQ,GAAG;EACfC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE,EAAE;EACXC,KAAK,EAAE;AACT,CAAC;AACM,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,MAAMC,EAAE,GAAGC,YAAY,CAAC,OAAO,CAAC;EAChC,MAAMC,OAAO,GAAGC,QAAQ,CAAC,MAAM,CAACH,EAAE,CAACI,CAAC,EAAE,EAAEJ,EAAE,CAACK,CAAC,CAACN,KAAK,CAACO,SAAS,CAAC,EAAEP,KAAK,CAACQ,KAAK,CAAC,CAAC;EAC5E,MAAMC,cAAc,GAAGC,GAAG,CAAC,CAAC,CAAC;EAC7B,MAAMC,YAAY,GAAGD,GAAG,CAAC,CAAC,CAAC;EAC3B,MAAME,cAAc,GAAGR,QAAQ,CAAC,MAAM;IACpC,MAAMS,OAAO,GAAGb,KAAK,CAACc,IAAI,IAAId,KAAK,CAACe,IAAI,GAAG;MAAEC,QAAQ,EAAE;IAAM,CAAE,GAAG,EAAE;IACpE,MAAMC,SAAS,GAAG;MAChBC,UAAU,EAAElB,KAAK,CAACiB;IACxB,CAAK;IACD,MAAME,GAAG,GAAG;MACVC,MAAM,EAAE,GAAGT,YAAY,CAACU,KAAK,IAAI;MACjCC,SAAS,EAAE,GAAGb,cAAc,CAACY,KAAK;IACxC,CAAK;IACD,OAAO,CAACR,OAAO,EAAEI,SAAS,EAAEE,GAAG,EAAEnB,KAAK,CAACuB,KAAK,CAAC;EACjD,CAAG,CAAC;EACF,MAAMC,SAAS,GAAGpB,QAAQ,CAAC,MAAM;IAC/B,OAAOJ,KAAK,CAACe,IAAI,GAAG;MAAEU,QAAQ,EAAE,CAAC;MAAEC,QAAQ,EAAE,GAAG1B,KAAK,CAAC2B,SAAS;IAAG,CAAE,GAAG,EAAE;EAC7E,CAAG,CAAC;EACFC,WAAW,CAAC,MAAM;IAChB,MAAM;MAAEC,IAAI,GAAG,OAAO;MAAEf,IAAI;MAAEP,SAAS,EAAEuB,GAAG;MAAEf;IAAI,CAAE,GAAGf,KAAK;IAC5D,IAAI+B,OAAO,CAACF,IAAI,CAAC,EAAE;MACjB,MAAM,CAACG,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,GAAGJ,IAAI;MAC3BpB,cAAc,CAACY,KAAK,GAAGW,CAAC;MACxBrB,YAAY,CAACU,KAAK,GAAGY,CAAC;IAC5B,CAAK,MAAM;MACL,IAAIC,GAAG;MACP,IAAIC,QAAQ,CAACN,IAAI,CAAC,EAAE;QAClBK,GAAG,GAAGL,IAAI;MAClB,CAAO,MAAM;QACLK,GAAG,GAAGvC,QAAQ,CAACkC,IAAI,IAAI,OAAO,CAAC,IAAIlC,QAAQ,CAACC,KAAK;MACzD;MACM,IAAI,CAACkB,IAAI,IAAIC,IAAI,KAAKe,GAAG,KAAK,YAAY,EAAE;QAC1CrB,cAAc,CAACY,KAAK,GAAGV,YAAY,CAACU,KAAK,GAAGa,GAAG;MACvD,CAAO,MAAM;QACL,IAAIJ,GAAG,KAAK,YAAY,EAAE;UACxBrB,cAAc,CAACY,KAAK,GAAGa,GAAG;UAC1BvB,YAAY,CAACU,KAAK,GAAG,CAAC;QAChC,CAAS,MAAM;UACLV,YAAY,CAACU,KAAK,GAAGa,GAAG;UACxBzB,cAAc,CAACY,KAAK,GAAG,CAAC;QAClC;MACA;IACA;EACA,CAAG,CAAC;EACF,OAAO;IACLlB,OAAO;IACPS,cAAc;IACdY;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}