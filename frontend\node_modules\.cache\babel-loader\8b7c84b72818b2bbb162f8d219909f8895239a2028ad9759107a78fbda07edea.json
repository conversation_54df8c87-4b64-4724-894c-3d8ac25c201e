{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, Fragment as _Fragment, toDisplayString as _toDisplayString, withModifiers as _withModifiers } from \"vue\";\nimport _imports_0 from '@/assets/logo.png';\nconst _hoisted_1 = {\n  class: \"register-container\"\n};\nconst _hoisted_2 = {\n  class: \"register-card\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"step-content\"\n};\nconst _hoisted_4 = {\n  class: \"step-content\"\n};\nconst _hoisted_5 = {\n  class: \"key-generation\"\n};\nconst _hoisted_6 = {\n  class: \"key-box\"\n};\nconst _hoisted_7 = {\n  class: \"key-label\"\n};\nconst _hoisted_8 = {\n  class: \"key-box\"\n};\nconst _hoisted_9 = {\n  class: \"key-label\"\n};\nconst _hoisted_10 = {\n  class: \"key-actions\"\n};\nconst _hoisted_11 = {\n  class: \"step-content\"\n};\nconst _hoisted_12 = {\n  class: \"register-confirm\"\n};\nconst _hoisted_13 = {\n  class: \"confirm-actions\"\n};\nconst _hoisted_14 = {\n  class: \"button-group\"\n};\nconst _hoisted_15 = {\n  class: \"step-content\"\n};\nconst _hoisted_16 = {\n  class: \"register-success\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_step = _resolveComponent(\"el-step\");\n  const _component_el_steps = _resolveComponent(\"el-steps\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_el_result = _resolveComponent(\"el-result\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[30] || (_cache[30] = _createElementVNode(\"div\", {\n    class: \"register-header\"\n  }, [_createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"Logo\",\n    class: \"register-logo\"\n  }), _createElementVNode(\"h2\", null, \"用户注册\")], -1 /* HOISTED */)), _createVNode(_component_el_steps, {\n    active: $setup.currentStep,\n    \"finish-status\": \"success\",\n    simple: \"\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_step, {\n      title: \"填写信息\"\n    }), _createVNode(_component_el_step, {\n      title: \"生成密钥\"\n    }), _createVNode(_component_el_step, {\n      title: \"完成注册\"\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"active\"]), _createCommentVNode(\" 步骤1：填写基本信息 \"), $setup.currentStep === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    \"label-position\": \"top\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"学号\",\n      prop: \"studentId\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.studentId,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.form.studentId = $event),\n        placeholder: \"请输入学号\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"密码\",\n      prop: \"password\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.password,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.form.password = $event),\n        type: \"password\",\n        placeholder: \"请输入密码\",\n        \"show-password\": \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"确认密码\",\n      prop: \"confirmPassword\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.confirmPassword,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.form.confirmPassword = $event),\n        type: \"password\",\n        placeholder: \"请再次输入密码\",\n        \"show-password\": \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"邮箱\",\n      prop: \"email\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.email,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.form.email = $event),\n        placeholder: \"请输入邮箱\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"手机号\",\n      prop: \"phone\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.phone,\n        \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.form.phone = $event),\n        placeholder: \"请输入手机号\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.nextStep\n      }, {\n        default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"下一步\")])),\n        _: 1 /* STABLE */,\n        __: [12]\n      }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n        onClick: _cache[5] || (_cache[5] = $event => _ctx.$router.push('/login'))\n      }, {\n        default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"返回登录\")])),\n        _: 1 /* STABLE */,\n        __: [13]\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])])) : $setup.currentStep === 1 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 步骤2：生成SM2密钥对 \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_cache[21] || (_cache[21] = _createElementVNode(\"h3\", null, \"SM2密钥对生成\", -1 /* HOISTED */)), _cache[22] || (_cache[22] = _createElementVNode(\"p\", {\n    class: \"description\"\n  }, \" SM2密钥对用于安全登录和数据签名，请妥善保管您的私钥，公钥将上传至服务器。 \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_cache[15] || (_cache[15] = _createElementVNode(\"span\", null, \"公钥\", -1 /* HOISTED */)), _createVNode(_component_el_button, {\n    type: \"primary\",\n    size: \"small\",\n    onClick: $setup.copyPublicKey\n  }, {\n    default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\"复制\")])),\n    _: 1 /* STABLE */,\n    __: [14]\n  }, 8 /* PROPS */, [\"onClick\"])]), _createVNode(_component_el_input, {\n    modelValue: $setup.keyPair.publicKey,\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.keyPair.publicKey = $event),\n    type: \"textarea\",\n    rows: 3,\n    readonly: \"\"\n  }, null, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_cache[17] || (_cache[17] = _createElementVNode(\"span\", null, \"私钥\", -1 /* HOISTED */)), _createVNode(_component_el_button, {\n    type: \"primary\",\n    size: \"small\",\n    onClick: $setup.copyPrivateKey\n  }, {\n    default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\"复制\")])),\n    _: 1 /* STABLE */,\n    __: [16]\n  }, 8 /* PROPS */, [\"onClick\"])]), _createVNode(_component_el_input, {\n    modelValue: $setup.keyPair.privateKey,\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.keyPair.privateKey = $event),\n    type: \"textarea\",\n    rows: 3,\n    readonly: \"\",\n    \"show-password\": \"\"\n  }, null, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.regenerateKeyPair\n  }, {\n    default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\"重新生成\")])),\n    _: 1 /* STABLE */,\n    __: [18]\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    type: \"success\",\n    onClick: $setup.nextStep\n  }, {\n    default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\"下一步\")])),\n    _: 1 /* STABLE */,\n    __: [19]\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    onClick: $setup.prevStep\n  }, {\n    default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\"上一步\")])),\n    _: 1 /* STABLE */,\n    __: [20]\n  }, 8 /* PROPS */, [\"onClick\"])]), _createVNode(_component_el_alert, {\n    title: \"重要提示\",\n    type: \"warning\",\n    description: \"请务必保存您的私钥，私钥将不会存储在服务器上，丢失后无法找回。\",\n    \"show-icon\": \"\",\n    closable: false,\n    class: \"key-warning\"\n  })])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.currentStep === 2 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" 步骤3：完成注册 \"), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_cache[28] || (_cache[28] = _createElementVNode(\"h3\", null, \"确认注册信息\", -1 /* HOISTED */)), _createVNode(_component_el_descriptions, {\n    column: 1,\n    border: \"\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n      label: \"学号\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.form.studentId), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_descriptions_item, {\n      label: \"邮箱\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.form.email), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_descriptions_item, {\n      label: \"手机号\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.form.phone), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_descriptions_item, {\n      label: \"是否使用SM2密钥\"\n    }, {\n      default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"是\")])),\n      _: 1 /* STABLE */,\n      __: [23]\n    })]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_checkbox, {\n    modelValue: $setup.agreement,\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.agreement = $event)\n  }, {\n    default: _withCtx(() => [_cache[24] || (_cache[24] = _createTextVNode(\" 我已阅读并同意 \")), _createElementVNode(\"a\", {\n      href: \"#\",\n      onClick: _cache[8] || (_cache[8] = _withModifiers((...args) => $setup.showTerms && $setup.showTerms(...args), [\"prevent\"]))\n    }, \"《用户协议》\"), _cache[25] || (_cache[25] = _createTextVNode(\" 和 \")), _createElementVNode(\"a\", {\n      href: \"#\",\n      onClick: _cache[9] || (_cache[9] = _withModifiers((...args) => $setup.showPrivacy && $setup.showPrivacy(...args), [\"prevent\"]))\n    }, \"《隐私政策》\")]),\n    _: 1 /* STABLE */,\n    __: [24, 25]\n  }, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    loading: $setup.loading,\n    disabled: !$setup.agreement,\n    onClick: $setup.handleRegister\n  }, {\n    default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\" 提交注册 \")])),\n    _: 1 /* STABLE */,\n    __: [26]\n  }, 8 /* PROPS */, [\"loading\", \"disabled\", \"onClick\"]), _createVNode(_component_el_button, {\n    onClick: $setup.prevStep\n  }, {\n    default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"上一步\")])),\n    _: 1 /* STABLE */,\n    __: [27]\n  }, 8 /* PROPS */, [\"onClick\"])])])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.currentStep === 3 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 3\n  }, [_createCommentVNode(\" 步骤4：注册成功 \"), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_el_result, {\n    icon: \"success\",\n    title: \"注册成功\",\n    \"sub-title\": \"您已成功注册图书馆自习室管理系统账号\"\n  }, {\n    extra: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[11] || (_cache[11] = $event => _ctx.$router.push('/login'))\n    }, {\n      default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\"前往登录\")])),\n      _: 1 /* STABLE */,\n      __: [29]\n    })]),\n    _: 1 /* STABLE */\n  })])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "src", "alt", "_createVNode", "_component_el_steps", "active", "$setup", "currentStep", "simple", "default", "_withCtx", "_component_el_step", "title", "_", "_createCommentVNode", "_hoisted_3", "_component_el_form", "ref", "model", "form", "rules", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "studentId", "_cache", "$event", "placeholder", "password", "type", "confirmPassword", "email", "phone", "_component_el_button", "onClick", "nextStep", "_createTextVNode", "__", "_ctx", "$router", "push", "_Fragment", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "size", "copyPublicKey", "keyPair", "public<PERSON>ey", "rows", "readonly", "_hoisted_8", "_hoisted_9", "copyPrivateKey", "privateKey", "_hoisted_10", "regenerateKeyPair", "prevStep", "_component_el_alert", "description", "closable", "_hoisted_11", "_hoisted_12", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "_toDisplayString", "_hoisted_13", "_component_el_checkbox", "agreement", "href", "_withModifiers", "args", "showTerms", "showPrivacy", "_hoisted_14", "loading", "disabled", "handleRegister", "_hoisted_15", "_hoisted_16", "_component_el_result", "icon", "extra"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Register.vue"], "sourcesContent": ["<template>\n  <div class=\"register-container\">\n    <div class=\"register-card\">\n      <div class=\"register-header\">\n        <img src=\"@/assets/logo.png\" alt=\"Logo\" class=\"register-logo\" />\n        <h2>用户注册</h2>\n      </div>\n\n      <el-steps :active=\"currentStep\" finish-status=\"success\" simple>\n        <el-step title=\"填写信息\" />\n        <el-step title=\"生成密钥\" />\n        <el-step title=\"完成注册\" />\n      </el-steps>\n\n      <!-- 步骤1：填写基本信息 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" label-position=\"top\">\n          <el-form-item label=\"学号\" prop=\"studentId\">\n            <el-input v-model=\"form.studentId\" placeholder=\"请输入学号\" />\n          </el-form-item>\n\n          <el-form-item label=\"密码\" prop=\"password\">\n            <el-input\n              v-model=\"form.password\"\n              type=\"password\"\n              placeholder=\"请输入密码\"\n              show-password\n            />\n          </el-form-item>\n\n          <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\n            <el-input\n              v-model=\"form.confirmPassword\"\n              type=\"password\"\n              placeholder=\"请再次输入密码\"\n              show-password\n            />\n          </el-form-item>\n\n          <el-form-item label=\"邮箱\" prop=\"email\">\n            <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" />\n          </el-form-item>\n\n          <el-form-item label=\"手机号\" prop=\"phone\">\n            <el-input v-model=\"form.phone\" placeholder=\"请输入手机号\" />\n          </el-form-item>\n\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"nextStep\">下一步</el-button>\n            <el-button @click=\"$router.push('/login')\">返回登录</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <!-- 步骤2：生成SM2密钥对 -->\n      <div v-else-if=\"currentStep === 1\" class=\"step-content\">\n        <div class=\"key-generation\">\n          <h3>SM2密钥对生成</h3>\n          <p class=\"description\">\n            SM2密钥对用于安全登录和数据签名，请妥善保管您的私钥，公钥将上传至服务器。\n          </p>\n\n          <div class=\"key-box\">\n            <div class=\"key-label\">\n              <span>公钥</span>\n              <el-button type=\"primary\" size=\"small\" @click=\"copyPublicKey\">复制</el-button>\n            </div>\n            <el-input v-model=\"keyPair.publicKey\" type=\"textarea\" :rows=\"3\" readonly />\n          </div>\n\n          <div class=\"key-box\">\n            <div class=\"key-label\">\n              <span>私钥</span>\n              <el-button type=\"primary\" size=\"small\" @click=\"copyPrivateKey\">复制</el-button>\n            </div>\n            <el-input\n              v-model=\"keyPair.privateKey\"\n              type=\"textarea\"\n              :rows=\"3\"\n              readonly\n              show-password\n            />\n          </div>\n\n          <div class=\"key-actions\">\n            <el-button type=\"primary\" @click=\"regenerateKeyPair\">重新生成</el-button>\n            <el-button type=\"success\" @click=\"nextStep\">下一步</el-button>\n            <el-button @click=\"prevStep\">上一步</el-button>\n          </div>\n\n          <el-alert\n            title=\"重要提示\"\n            type=\"warning\"\n            description=\"请务必保存您的私钥，私钥将不会存储在服务器上，丢失后无法找回。\"\n            show-icon\n            :closable=\"false\"\n            class=\"key-warning\"\n          />\n        </div>\n      </div>\n\n      <!-- 步骤3：完成注册 -->\n      <div v-else-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"register-confirm\">\n          <h3>确认注册信息</h3>\n\n          <el-descriptions :column=\"1\" border>\n            <el-descriptions-item label=\"学号\">{{ form.studentId }}</el-descriptions-item>\n            <el-descriptions-item label=\"邮箱\">{{ form.email }}</el-descriptions-item>\n            <el-descriptions-item label=\"手机号\">{{ form.phone }}</el-descriptions-item>\n            <el-descriptions-item label=\"是否使用SM2密钥\">是</el-descriptions-item>\n          </el-descriptions>\n\n          <div class=\"confirm-actions\">\n            <el-checkbox v-model=\"agreement\">\n              我已阅读并同意\n              <a href=\"#\" @click.prevent=\"showTerms\">《用户协议》</a>\n              和\n              <a href=\"#\" @click.prevent=\"showPrivacy\">《隐私政策》</a>\n            </el-checkbox>\n\n            <div class=\"button-group\">\n              <el-button\n                type=\"primary\"\n                :loading=\"loading\"\n                :disabled=\"!agreement\"\n                @click=\"handleRegister\"\n              >\n                提交注册\n              </el-button>\n              <el-button @click=\"prevStep\">上一步</el-button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 步骤4：注册成功 -->\n      <div v-else-if=\"currentStep === 3\" class=\"step-content\">\n        <div class=\"register-success\">\n          <el-result\n            icon=\"success\"\n            title=\"注册成功\"\n            sub-title=\"您已成功注册图书馆自习室管理系统账号\"\n          >\n            <template #extra>\n              <el-button type=\"primary\" @click=\"$router.push('/login')\">前往登录</el-button>\n            </template>\n          </el-result>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\n  import { ref, reactive } from \"vue\";\n  import { useStore } from \"vuex\";\n\n  import { ElMessage, ElMessageBox } from \"element-plus\";\n  import { SM2Crypto, SM3Hasher } from \"@/utils/crypto\";\n\n  export default {\n    name: \"RegisterView\",\n    setup() {\n      const store = useStore();\n\n      const formRef = ref(null);\n      const currentStep = ref(0);\n      const loading = ref(false);\n      const agreement = ref(false);\n\n      // 注册表单\n      const form = reactive({\n        studentId: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        email: \"\",\n        phone: \"\",\n      });\n\n      // SM2密钥对\n      const keyPair = reactive({\n        publicKey: \"\",\n        privateKey: \"\",\n      });\n\n      // 表单验证规则\n      const validatePass = (rule, value, callback) => {\n        if (value === \"\") {\n          callback(new Error(\"请输入密码\"));\n        } else if (value.length < 6) {\n          callback(new Error(\"密码长度不能小于6位\"));\n        } else {\n          if (form.confirmPassword !== \"\") {\n            formRef.value.validateField(\"confirmPassword\");\n          }\n          callback();\n        }\n      };\n\n      const validatePass2 = (rule, value, callback) => {\n        if (value === \"\") {\n          callback(new Error(\"请再次输入密码\"));\n        } else if (value !== form.password) {\n          callback(new Error(\"两次输入密码不一致\"));\n        } else {\n          callback();\n        }\n      };\n\n      const rules = {\n        studentId: [\n          { required: true, message: \"请输入学号\", trigger: \"blur\" },\n          { min: 5, max: 20, message: \"学号长度应为5-20个字符\", trigger: \"blur\" },\n        ],\n        password: [{ validator: validatePass, trigger: \"blur\" }],\n        confirmPassword: [{ validator: validatePass2, trigger: \"blur\" }],\n        email: [\n          { required: true, message: \"请输入邮箱\", trigger: \"blur\" },\n          { type: \"email\", message: \"请输入正确的邮箱格式\", trigger: \"blur\" },\n        ],\n        phone: [\n          { required: true, message: \"请输入手机号\", trigger: \"blur\" },\n          {\n            pattern: /^1[3-9]\\d{9}$/,\n            message: \"请输入正确的手机号格式\",\n            trigger: \"blur\",\n          },\n        ],\n      };\n\n      // 生成SM2密钥对\n      const generateKeyPair = () => {\n        const newKeyPair = SM2Crypto.generateKeyPair();\n        keyPair.publicKey = newKeyPair.publicKey;\n        keyPair.privateKey = newKeyPair.privateKey;\n      };\n\n      // 重新生成SM2密钥对\n      const regenerateKeyPair = () => {\n        ElMessageBox.confirm(\"重新生成密钥对将覆盖当前密钥，确定要继续吗？\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        })\n          .then(() => {\n            generateKeyPair();\n            ElMessage.success(\"密钥对已重新生成\");\n          })\n          .catch(() => {\n            // 用户取消操作\n          });\n      };\n\n      // 复制公钥\n      const copyPublicKey = () => {\n        navigator.clipboard\n          .writeText(keyPair.publicKey)\n          .then(() => {\n            ElMessage.success(\"公钥已复制到剪贴板\");\n          })\n          .catch(() => {\n            ElMessage.error(\"复制失败，请手动复制\");\n          });\n      };\n\n      // 复制私钥\n      const copyPrivateKey = () => {\n        navigator.clipboard\n          .writeText(keyPair.privateKey)\n          .then(() => {\n            ElMessage.success(\"私钥已复制到剪贴板\");\n          })\n          .catch(() => {\n            ElMessage.error(\"复制失败，请手动复制\");\n          });\n      };\n\n      // 下一步\n      const nextStep = async () => {\n        if (currentStep.value === 0) {\n          if (!formRef.value) return;\n\n          await formRef.value.validate(async (valid) => {\n            if (valid) {\n              currentStep.value++;\n\n              // 如果还没有生成密钥对，则生成\n              if (!keyPair.publicKey || !keyPair.privateKey) {\n                generateKeyPair();\n              }\n            }\n          });\n        } else {\n          currentStep.value++;\n        }\n      };\n\n      // 上一步\n      const prevStep = () => {\n        currentStep.value--;\n      };\n\n      // 显示用户协议\n      const showTerms = () => {\n        ElMessageBox.alert(\"这里是用户协议内容...\", \"用户协议\", {\n          confirmButtonText: \"确定\",\n        });\n      };\n\n      // 显示隐私政策\n      const showPrivacy = () => {\n        ElMessageBox.alert(\"这里是隐私政策内容...\", \"隐私政策\", {\n          confirmButtonText: \"确定\",\n        });\n      };\n\n      // 提交注册\n      const handleRegister = async () => {\n        try {\n          loading.value = true;\n\n          // 对密码进行SM3哈希\n          const hashedPassword = SM3Hasher.hash(form.password);\n\n          // 调用注册接口\n          await store.dispatch(\"user/register\", {\n            studentId: form.studentId,\n            password: hashedPassword,\n            email: form.email,\n            phone: form.phone,\n            publicKey: keyPair.publicKey,\n          });\n\n          // 注册成功，进入下一步\n          currentStep.value = 3;\n          ElMessage.success(\"注册成功\");\n        } catch (error) {\n          ElMessage.error(error.message || \"注册失败，请稍后重试\");\n        } finally {\n          loading.value = false;\n        }\n      };\n\n      return {\n        formRef,\n        currentStep,\n        loading,\n        agreement,\n        form,\n        keyPair,\n        rules,\n        nextStep,\n        prevStep,\n        generateKeyPair,\n        regenerateKeyPair,\n        copyPublicKey,\n        copyPrivateKey,\n        showTerms,\n        showPrivacy,\n        handleRegister,\n      };\n    },\n  };\n</script>\n\n<style lang=\"scss\" scoped>\n  .register-container {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    min-height: 100vh;\n    background-color: #f5f7fa;\n    padding: 20px;\n  }\n\n  .register-card {\n    width: 500px;\n    padding: 30px;\n    background-color: #fff;\n    border-radius: 8px;\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  }\n\n  .register-header {\n    text-align: center;\n    margin-bottom: 30px;\n\n    .register-logo {\n      width: 80px;\n      height: 80px;\n      margin-bottom: 15px;\n    }\n\n    h2 {\n      font-size: 24px;\n      color: #303133;\n      margin: 0;\n    }\n  }\n\n  .step-content {\n    margin-top: 30px;\n  }\n\n  .key-generation {\n    .description {\n      margin-bottom: 20px;\n      color: #606266;\n    }\n\n    .key-box {\n      margin-bottom: 20px;\n\n      .key-label {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 5px;\n        font-weight: bold;\n      }\n    }\n\n    .key-actions {\n      display: flex;\n      gap: 10px;\n      margin-bottom: 20px;\n    }\n\n    .key-warning {\n      margin-top: 20px;\n    }\n  }\n\n  .register-confirm {\n    .confirm-actions {\n      margin-top: 30px;\n\n      .button-group {\n        margin-top: 20px;\n        display: flex;\n        gap: 10px;\n      }\n    }\n  }\n\n  .register-success {\n    padding: 20px 0;\n  }\n</style>\n"], "mappings": ";;OAIaA,UAAuB;;EAH7BC,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAe;;EAF9BC,GAAA;EAeoCD,KAAK,EAAC;;;EAwCDA,KAAK,EAAC;AAAc;;EAChDA,KAAK,EAAC;AAAgB;;EAMpBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAW;;EAOnBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAW;;EAanBA,KAAK,EAAC;AAAa;;EAkBOA,KAAK,EAAC;AAAc;;EAChDA,KAAK,EAAC;AAAkB;;EAUtBA,KAAK,EAAC;AAAiB;;EAQrBA,KAAK,EAAC;AAAc;;EAgBIA,KAAK,EAAC;AAAc;;EAChDA,KAAK,EAAC;AAAkB;;;;;;;;;;;;;uBAzInCE,mBAAA,CAsJM,OAtJNC,UAsJM,GArJJC,mBAAA,CAoJM,OApJNC,UAoJM,G,4BAnJJD,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAiB,IAC1BI,mBAAA,CAAgE;IAA3DE,GAAuB,EAAvBP,UAAuB;IAACQ,GAAG,EAAC,MAAM;IAACP,KAAK,EAAC;MAC9CI,mBAAA,CAAa,YAAT,MAAI,E,sBAGVI,YAAA,CAIWC,mBAAA;IAJAC,MAAM,EAAEC,MAAA,CAAAC,WAAW;IAAE,eAAa,EAAC,SAAS;IAACC,MAAM,EAAN;;IAR9DC,OAAA,EAAAC,QAAA,CASQ,MAAwB,CAAxBP,YAAA,CAAwBQ,kBAAA;MAAfC,KAAK,EAAC;IAAM,IACrBT,YAAA,CAAwBQ,kBAAA;MAAfC,KAAK,EAAC;IAAM,IACrBT,YAAA,CAAwBQ,kBAAA;MAAfC,KAAK,EAAC;IAAM,G;IAX7BC,CAAA;iCAcMC,mBAAA,gBAAmB,EACRR,MAAA,CAAAC,WAAW,U,cAAtBV,mBAAA,CAqCM,OArCNkB,UAqCM,GApCJZ,YAAA,CAmCUa,kBAAA;IAnCDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEZ,MAAA,CAAAa,IAAI;IAAGC,KAAK,EAAEd,MAAA,CAAAc,KAAK;IAAE,gBAAc,EAAC;;IAhB3EX,OAAA,EAAAC,QAAA,CAiBU,MAEe,CAFfP,YAAA,CAEekB,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;MAjBxCd,OAAA,EAAAC,QAAA,CAkBY,MAAyD,CAAzDP,YAAA,CAAyDqB,mBAAA;QAlBrEC,UAAA,EAkB+BnB,MAAA,CAAAa,IAAI,CAACO,SAAS;QAlB7C,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAkB+BtB,MAAA,CAAAa,IAAI,CAACO,SAAS,GAAAE,MAAA;QAAEC,WAAW,EAAC;;MAlB3DhB,CAAA;QAqBUV,YAAA,CAOekB,uBAAA;MAPDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;MArBxCd,OAAA,EAAAC,QAAA,CAsBY,MAKE,CALFP,YAAA,CAKEqB,mBAAA;QA3BdC,UAAA,EAuBuBnB,MAAA,CAAAa,IAAI,CAACW,QAAQ;QAvBpC,uBAAAH,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAuBuBtB,MAAA,CAAAa,IAAI,CAACW,QAAQ,GAAAF,MAAA;QACtBG,IAAI,EAAC,UAAU;QACfF,WAAW,EAAC,OAAO;QACnB,eAAa,EAAb;;MA1BdhB,CAAA;QA8BUV,YAAA,CAOekB,uBAAA;MAPDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MA9B1Cd,OAAA,EAAAC,QAAA,CA+BY,MAKE,CALFP,YAAA,CAKEqB,mBAAA;QApCdC,UAAA,EAgCuBnB,MAAA,CAAAa,IAAI,CAACa,eAAe;QAhC3C,uBAAAL,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAgCuBtB,MAAA,CAAAa,IAAI,CAACa,eAAe,GAAAJ,MAAA;QAC7BG,IAAI,EAAC,UAAU;QACfF,WAAW,EAAC,SAAS;QACrB,eAAa,EAAb;;MAnCdhB,CAAA;QAuCUV,YAAA,CAEekB,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;MAvCxCd,OAAA,EAAAC,QAAA,CAwCY,MAAqD,CAArDP,YAAA,CAAqDqB,mBAAA;QAxCjEC,UAAA,EAwC+BnB,MAAA,CAAAa,IAAI,CAACc,KAAK;QAxCzC,uBAAAN,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAwC+BtB,MAAA,CAAAa,IAAI,CAACc,KAAK,GAAAL,MAAA;QAAEC,WAAW,EAAC;;MAxCvDhB,CAAA;QA2CUV,YAAA,CAEekB,uBAAA;MAFDC,KAAK,EAAC,KAAK;MAACC,IAAI,EAAC;;MA3CzCd,OAAA,EAAAC,QAAA,CA4CY,MAAsD,CAAtDP,YAAA,CAAsDqB,mBAAA;QA5ClEC,UAAA,EA4C+BnB,MAAA,CAAAa,IAAI,CAACe,KAAK;QA5CzC,uBAAAP,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA4C+BtB,MAAA,CAAAa,IAAI,CAACe,KAAK,GAAAN,MAAA;QAAEC,WAAW,EAAC;;MA5CvDhB,CAAA;QA+CUV,YAAA,CAGekB,uBAAA;MAlDzBZ,OAAA,EAAAC,QAAA,CAgDY,MAA2D,CAA3DP,YAAA,CAA2DgC,oBAAA;QAAhDJ,IAAI,EAAC,SAAS;QAAEK,OAAK,EAAE9B,MAAA,CAAA+B;;QAhD9C5B,OAAA,EAAAC,QAAA,CAgDwD,MAAGiB,MAAA,SAAAA,MAAA,QAhD3DW,gBAAA,CAgDwD,KAAG,E;QAhD3DzB,CAAA;QAAA0B,EAAA;sCAiDYpC,YAAA,CAA2DgC,oBAAA;QAA/CC,OAAK,EAAAT,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEY,IAAA,CAAAC,OAAO,CAACC,IAAI;;QAjD3CjC,OAAA,EAAAC,QAAA,CAiDuD,MAAIiB,MAAA,SAAAA,MAAA,QAjD3DW,gBAAA,CAiDuD,MAAI,E;QAjD3DzB,CAAA;QAAA0B,EAAA;;MAAA1B,CAAA;;IAAAA,CAAA;6CAuDsBP,MAAA,CAAAC,WAAW,U,cAA3BV,mBAAA,CA4CM8C,SAAA;IAnGZ/C,GAAA;EAAA,IAsDMkB,mBAAA,kBAAqB,EACrBf,mBAAA,CA4CM,OA5CN6C,UA4CM,GA3CJ7C,mBAAA,CA0CM,OA1CN8C,UA0CM,G,4BAzCJ9C,mBAAA,CAAiB,YAAb,UAAQ,sB,4BACZA,mBAAA,CAEI;IAFDJ,KAAK,EAAC;EAAa,GAAC,0CAEvB,sBAEAI,mBAAA,CAMM,OANN+C,UAMM,GALJ/C,mBAAA,CAGM,OAHNgD,UAGM,G,4BAFJhD,mBAAA,CAAe,cAAT,IAAE,sBACRI,YAAA,CAA4EgC,oBAAA;IAAjEJ,IAAI,EAAC,SAAS;IAACiB,IAAI,EAAC,OAAO;IAAEZ,OAAK,EAAE9B,MAAA,CAAA2C;;IAjE7DxC,OAAA,EAAAC,QAAA,CAiE4E,MAAEiB,MAAA,SAAAA,MAAA,QAjE9EW,gBAAA,CAiE4E,IAAE,E;IAjE9EzB,CAAA;IAAA0B,EAAA;oCAmEYpC,YAAA,CAA2EqB,mBAAA;IAnEvFC,UAAA,EAmE+BnB,MAAA,CAAA4C,OAAO,CAACC,SAAS;IAnEhD,uBAAAxB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAmE+BtB,MAAA,CAAA4C,OAAO,CAACC,SAAS,GAAAvB,MAAA;IAAEG,IAAI,EAAC,UAAU;IAAEqB,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAR;6CAGlEtD,mBAAA,CAYM,OAZNuD,UAYM,GAXJvD,mBAAA,CAGM,OAHNwD,UAGM,G,4BAFJxD,mBAAA,CAAe,cAAT,IAAE,sBACRI,YAAA,CAA6EgC,oBAAA;IAAlEJ,IAAI,EAAC,SAAS;IAACiB,IAAI,EAAC,OAAO;IAAEZ,OAAK,EAAE9B,MAAA,CAAAkD;;IAzE7D/C,OAAA,EAAAC,QAAA,CAyE6E,MAAEiB,MAAA,SAAAA,MAAA,QAzE/EW,gBAAA,CAyE6E,IAAE,E;IAzE/EzB,CAAA;IAAA0B,EAAA;oCA2EYpC,YAAA,CAMEqB,mBAAA;IAjFdC,UAAA,EA4EuBnB,MAAA,CAAA4C,OAAO,CAACO,UAAU;IA5EzC,uBAAA9B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA4EuBtB,MAAA,CAAA4C,OAAO,CAACO,UAAU,GAAA7B,MAAA;IAC3BG,IAAI,EAAC,UAAU;IACdqB,IAAI,EAAE,CAAC;IACRC,QAAQ,EAAR,EAAQ;IACR,eAAa,EAAb;6CAIJtD,mBAAA,CAIM,OAJN2D,WAIM,GAHJvD,YAAA,CAAqEgC,oBAAA;IAA1DJ,IAAI,EAAC,SAAS;IAAEK,OAAK,EAAE9B,MAAA,CAAAqD;;IArF9ClD,OAAA,EAAAC,QAAA,CAqFiE,MAAIiB,MAAA,SAAAA,MAAA,QArFrEW,gBAAA,CAqFiE,MAAI,E;IArFrEzB,CAAA;IAAA0B,EAAA;kCAsFYpC,YAAA,CAA2DgC,oBAAA;IAAhDJ,IAAI,EAAC,SAAS;IAAEK,OAAK,EAAE9B,MAAA,CAAA+B;;IAtF9C5B,OAAA,EAAAC,QAAA,CAsFwD,MAAGiB,MAAA,SAAAA,MAAA,QAtF3DW,gBAAA,CAsFwD,KAAG,E;IAtF3DzB,CAAA;IAAA0B,EAAA;kCAuFYpC,YAAA,CAA4CgC,oBAAA;IAAhCC,OAAK,EAAE9B,MAAA,CAAAsD;EAAQ;IAvFvCnD,OAAA,EAAAC,QAAA,CAuFyC,MAAGiB,MAAA,SAAAA,MAAA,QAvF5CW,gBAAA,CAuFyC,KAAG,E;IAvF5CzB,CAAA;IAAA0B,EAAA;oCA0FUpC,YAAA,CAOE0D,mBAAA;IANAjD,KAAK,EAAC,MAAM;IACZmB,IAAI,EAAC,SAAS;IACd+B,WAAW,EAAC,iCAAiC;IAC7C,WAAS,EAAT,EAAS;IACRC,QAAQ,EAAE,KAAK;IAChBpE,KAAK,EAAC;6DAMIW,MAAA,CAAAC,WAAW,U,cAA3BV,mBAAA,CAgCM8C,SAAA;IAtIZ/C,GAAA;EAAA,IAqGMkB,mBAAA,cAAiB,EACjBf,mBAAA,CAgCM,OAhCNiE,WAgCM,GA/BJjE,mBAAA,CA8BM,OA9BNkE,WA8BM,G,4BA7BJlE,mBAAA,CAAe,YAAX,QAAM,sBAEVI,YAAA,CAKkB+D,0BAAA;IALAC,MAAM,EAAE,CAAC;IAAEC,MAAM,EAAN;;IA1GvC3D,OAAA,EAAAC,QAAA,CA2GY,MAA4E,CAA5EP,YAAA,CAA4EkE,+BAAA;MAAtD/C,KAAK,EAAC;IAAI;MA3G5Cb,OAAA,EAAAC,QAAA,CA2G6C,MAAoB,CA3GjE4B,gBAAA,CAAAgC,gBAAA,CA2GgDhE,MAAA,CAAAa,IAAI,CAACO,SAAS,iB;MA3G9Db,CAAA;QA4GYV,YAAA,CAAwEkE,+BAAA;MAAlD/C,KAAK,EAAC;IAAI;MA5G5Cb,OAAA,EAAAC,QAAA,CA4G6C,MAAgB,CA5G7D4B,gBAAA,CAAAgC,gBAAA,CA4GgDhE,MAAA,CAAAa,IAAI,CAACc,KAAK,iB;MA5G1DpB,CAAA;QA6GYV,YAAA,CAAyEkE,+BAAA;MAAnD/C,KAAK,EAAC;IAAK;MA7G7Cb,OAAA,EAAAC,QAAA,CA6G8C,MAAgB,CA7G9D4B,gBAAA,CAAAgC,gBAAA,CA6GiDhE,MAAA,CAAAa,IAAI,CAACe,KAAK,iB;MA7G3DrB,CAAA;QA8GYV,YAAA,CAAgEkE,+BAAA;MAA1C/C,KAAK,EAAC;IAAW;MA9GnDb,OAAA,EAAAC,QAAA,CA8GoD,MAACiB,MAAA,SAAAA,MAAA,QA9GrDW,gBAAA,CA8GoD,GAAC,E;MA9GrDzB,CAAA;MAAA0B,EAAA;;IAAA1B,CAAA;MAiHUd,mBAAA,CAmBM,OAnBNwE,WAmBM,GAlBJpE,YAAA,CAKcqE,sBAAA;IAvH1B/C,UAAA,EAkHkCnB,MAAA,CAAAmE,SAAS;IAlH3C,uBAAA9C,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAkHkCtB,MAAA,CAAAmE,SAAS,GAAA7C,MAAA;;IAlH3CnB,OAAA,EAAAC,QAAA,CAkH6C,MAE/B,C,4BApHd4B,gBAAA,CAkH6C,WAE/B,IAAAvC,mBAAA,CAAiD;MAA9C2E,IAAI,EAAC,GAAG;MAAEtC,OAAK,EAAAT,MAAA,QAAAA,MAAA,MApHhCgD,cAAA,KAAAC,IAAA,KAoH0CtE,MAAA,CAAAuE,SAAA,IAAAvE,MAAA,CAAAuE,SAAA,IAAAD,IAAA,CAAS;OAAE,QAAM,G,4BApH3DtC,gBAAA,CAoH+D,KAEjD,IAAAvC,mBAAA,CAAmD;MAAhD2E,IAAI,EAAC,GAAG;MAAEtC,OAAK,EAAAT,MAAA,QAAAA,MAAA,MAtHhCgD,cAAA,KAAAC,IAAA,KAsH0CtE,MAAA,CAAAwE,WAAA,IAAAxE,MAAA,CAAAwE,WAAA,IAAAF,IAAA,CAAW;OAAE,QAAM,E;IAtH7D/D,CAAA;IAAA0B,EAAA;qCAyHYxC,mBAAA,CAUM,OAVNgF,WAUM,GATJ5E,YAAA,CAOYgC,oBAAA;IANVJ,IAAI,EAAC,SAAS;IACbiD,OAAO,EAAE1E,MAAA,CAAA0E,OAAO;IAChBC,QAAQ,GAAG3E,MAAA,CAAAmE,SAAS;IACpBrC,OAAK,EAAE9B,MAAA,CAAA4E;;IA9HxBzE,OAAA,EAAAC,QAAA,CA+He,MAEDiB,MAAA,SAAAA,MAAA,QAjIdW,gBAAA,CA+He,QAED,E;IAjIdzB,CAAA;IAAA0B,EAAA;yDAkIcpC,YAAA,CAA4CgC,oBAAA;IAAhCC,OAAK,EAAE9B,MAAA,CAAAsD;EAAQ;IAlIzCnD,OAAA,EAAAC,QAAA,CAkI2C,MAAGiB,MAAA,SAAAA,MAAA,QAlI9CW,gBAAA,CAkI2C,KAAG,E;IAlI9CzB,CAAA;IAAA0B,EAAA;6FAyIsBjC,MAAA,CAAAC,WAAW,U,cAA3BV,mBAAA,CAYM8C,SAAA;IArJZ/C,GAAA;EAAA,IAwIMkB,mBAAA,cAAiB,EACjBf,mBAAA,CAYM,OAZNoF,WAYM,GAXJpF,mBAAA,CAUM,OAVNqF,WAUM,GATJjF,YAAA,CAQYkF,oBAAA;IAPVC,IAAI,EAAC,SAAS;IACd1E,KAAK,EAAC,MAAM;IACZ,WAAS,EAAC;;IAEC2E,KAAK,EAAA7E,QAAA,CACd,MAA0E,CAA1EP,YAAA,CAA0EgC,oBAAA;MAA/DJ,IAAI,EAAC,SAAS;MAAEK,OAAK,EAAAT,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEY,IAAA,CAAAC,OAAO,CAACC,IAAI;;MAjJ5DjC,OAAA,EAAAC,QAAA,CAiJwE,MAAIiB,MAAA,SAAAA,MAAA,QAjJ5EW,gBAAA,CAiJwE,MAAI,E;MAjJ5EzB,CAAA;MAAA0B,EAAA;;IAAA1B,CAAA;6DAAAC,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}