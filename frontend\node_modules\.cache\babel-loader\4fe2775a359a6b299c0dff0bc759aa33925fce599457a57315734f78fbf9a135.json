{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport axios from \"axios\";\nimport { ElMessage } from \"element-plus\";\nimport router from \"@/router\";\nimport { SM4Crypto } from \"@/utils/crypto\";\n\n// 创建axios实例\nconst http = axios.create({\n  baseURL: process.env.VUE_APP_API_URL || \"/api\",\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\"\n  }\n});\n\n// 请求拦截器\nhttp.interceptors.request.use(config => {\n  // 从localStorage获取token\n  const token = localStorage.getItem(\"token\");\n\n  // 如果有token，则添加到请求头\n  if (token) {\n    config.headers[\"Authorization\"] = `Bearer ${token}`;\n  }\n\n  // 检查是否需要加密请求数据\n  if (config.encrypt && config.data) {\n    // 获取SM4密钥\n    const sm4Key = localStorage.getItem(\"sm4Key\");\n    if (sm4Key) {\n      // 加密请求数据\n      const encryptedData = SM4Crypto.encrypt(JSON.stringify(config.data), sm4Key);\n\n      // 替换请求数据\n      config.data = {\n        encrypted: encryptedData\n      };\n\n      // 添加加密标记\n      config.headers[\"X-Encrypted\"] = \"true\";\n    }\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\nhttp.interceptors.response.use(response => {\n  // 检查是否是加密响应\n  if (response.headers[\"x-encrypted\"] === \"true\" && response.data.encrypted) {\n    // 获取SM4密钥\n    const sm4Key = localStorage.getItem(\"sm4Key\");\n    if (sm4Key) {\n      try {\n        // 解密响应数据\n        const decryptedData = SM4Crypto.decrypt(response.data.encrypted, sm4Key);\n\n        // 解析JSON\n        response.data = JSON.parse(decryptedData);\n      } catch (error) {\n        console.error(\"解密响应数据失败:\", error);\n        return Promise.reject(new Error(\"解密响应数据失败\"));\n      }\n    }\n  }\n  return response;\n}, error => {\n  if (error.response) {\n    // 处理响应错误\n    switch (error.response.status) {\n      case 401:\n        // 未授权，清除token并跳转到登录页\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"userInfo\");\n\n        // 如果不是登录页，则跳转到登录页\n        if (router.currentRoute.value.path !== \"/login\") {\n          ElMessage.error(\"登录已过期，请重新登录\");\n          router.push(\"/login\");\n        }\n        break;\n      case 403:\n        // 禁止访问\n        ElMessage.error(\"没有权限访问该资源\");\n        break;\n      case 404:\n        // 资源不存在\n        ElMessage.error(\"请求的资源不存在\");\n        break;\n      case 500:\n        // 服务器错误\n        ElMessage.error(\"服务器错误，请稍后重试\");\n        break;\n      default:\n        // 其他错误\n        if (error.response.data && error.response.data.message) {\n          ElMessage.error(error.response.data.message);\n        } else {\n          ElMessage.error(\"请求失败，请稍后重试\");\n        }\n    }\n  } else if (error.request) {\n    // 请求发送但没有收到响应\n    ElMessage.error(\"网络错误，请检查网络连接\");\n  } else {\n    // 请求配置错误\n    ElMessage.error(\"请求配置错误\");\n  }\n  return Promise.reject(error);\n});\n\n// 导出请求方法\nexport default {\n  // GET请求\n  get(url, params = {}, config = {}) {\n    return http.get(url, {\n      params,\n      ...config\n    });\n  },\n  // POST请求\n  post(url, data = {}, config = {}) {\n    return http.post(url, data, config);\n  },\n  // PUT请求\n  put(url, data = {}, config = {}) {\n    return http.put(url, data, config);\n  },\n  // DELETE请求\n  delete(url, config = {}) {\n    return http.delete(url, config);\n  },\n  // 加密POST请求\n  encryptedPost(url, data = {}, config = {}) {\n    return http.post(url, data, {\n      ...config,\n      encrypt: true\n    });\n  },\n  // 加密PUT请求\n  encryptedPut(url, data = {}, config = {}) {\n    return http.put(url, data, {\n      ...config,\n      encrypt: true\n    });\n  }\n};", "map": {"version": 3, "names": ["axios", "ElMessage", "router", "SM4Crypto", "http", "create", "baseURL", "process", "env", "VUE_APP_API_URL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "encrypt", "data", "sm4Key", "encryptedData", "JSON", "stringify", "encrypted", "error", "Promise", "reject", "response", "decryptedData", "decrypt", "parse", "console", "Error", "status", "removeItem", "currentRoute", "value", "path", "push", "message", "get", "url", "params", "post", "put", "delete", "encryptedPost", "encryptedPut"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/src/api/http.js"], "sourcesContent": ["import axios from \"axios\";\nimport { ElMessage } from \"element-plus\";\nimport router from \"@/router\";\nimport { SM4Crypto } from \"@/utils/crypto\";\n\n// 创建axios实例\nconst http = axios.create({\n  baseURL: process.env.VUE_APP_API_URL || \"/api\",\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\"\n  }\n});\n\n// 请求拦截器\nhttp.interceptors.request.use(\n  (config) => {\n    // 从localStorage获取token\n    const token = localStorage.getItem(\"token\");\n\n    // 如果有token，则添加到请求头\n    if (token) {\n      config.headers[\"Authorization\"] = `Bearer ${token}`;\n    }\n\n    // 检查是否需要加密请求数据\n    if (config.encrypt && config.data) {\n      // 获取SM4密钥\n      const sm4Key = localStorage.getItem(\"sm4Key\");\n\n      if (sm4Key) {\n        // 加密请求数据\n        const encryptedData = SM4Crypto.encrypt(JSON.stringify(config.data), sm4Key);\n\n        // 替换请求数据\n        config.data = { encrypted: encryptedData };\n\n        // 添加加密标记\n        config.headers[\"X-Encrypted\"] = \"true\";\n      }\n    }\n\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\nhttp.interceptors.response.use(\n  (response) => {\n    // 检查是否是加密响应\n    if (response.headers[\"x-encrypted\"] === \"true\" && response.data.encrypted) {\n      // 获取SM4密钥\n      const sm4Key = localStorage.getItem(\"sm4Key\");\n\n      if (sm4Key) {\n        try {\n          // 解密响应数据\n          const decryptedData = SM4Crypto.decrypt(response.data.encrypted, sm4Key);\n\n          // 解析JSON\n          response.data = JSON.parse(decryptedData);\n        } catch (error) {\n          console.error(\"解密响应数据失败:\", error);\n          return Promise.reject(new Error(\"解密响应数据失败\"));\n        }\n      }\n    }\n\n    return response;\n  },\n  (error) => {\n    if (error.response) {\n      // 处理响应错误\n      switch (error.response.status) {\n        case 401:\n          // 未授权，清除token并跳转到登录页\n          localStorage.removeItem(\"token\");\n          localStorage.removeItem(\"userInfo\");\n\n          // 如果不是登录页，则跳转到登录页\n          if (router.currentRoute.value.path !== \"/login\") {\n            ElMessage.error(\"登录已过期，请重新登录\");\n            router.push(\"/login\");\n          }\n          break;\n\n        case 403:\n          // 禁止访问\n          ElMessage.error(\"没有权限访问该资源\");\n          break;\n\n        case 404:\n          // 资源不存在\n          ElMessage.error(\"请求的资源不存在\");\n          break;\n\n        case 500:\n          // 服务器错误\n          ElMessage.error(\"服务器错误，请稍后重试\");\n          break;\n\n        default:\n          // 其他错误\n          if (error.response.data && error.response.data.message) {\n            ElMessage.error(error.response.data.message);\n          } else {\n            ElMessage.error(\"请求失败，请稍后重试\");\n          }\n      }\n    } else if (error.request) {\n      // 请求发送但没有收到响应\n      ElMessage.error(\"网络错误，请检查网络连接\");\n    } else {\n      // 请求配置错误\n      ElMessage.error(\"请求配置错误\");\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// 导出请求方法\nexport default {\n  // GET请求\n  get(url, params = {}, config = {}) {\n    return http.get(url, { params, ...config });\n  },\n\n  // POST请求\n  post(url, data = {}, config = {}) {\n    return http.post(url, data, config);\n  },\n\n  // PUT请求\n  put(url, data = {}, config = {}) {\n    return http.put(url, data, config);\n  },\n\n  // DELETE请求\n  delete(url, config = {}) {\n    return http.delete(url, config);\n  },\n\n  // 加密POST请求\n  encryptedPost(url, data = {}, config = {}) {\n    return http.post(url, data, { ...config, encrypt: true });\n  },\n\n  // 加密PUT请求\n  encryptedPut(url, data = {}, config = {}) {\n    return http.put(url, data, { ...config, encrypt: true });\n  }\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AACA,MAAMC,IAAI,GAAGJ,KAAK,CAACK,MAAM,CAAC;EACxBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,MAAM;EAC9CC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,IAAI,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CAC1BC,MAAM,IAAK;EACV;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;EAE3C;EACA,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUK,KAAK,EAAE;EACrD;;EAEA;EACA,IAAID,MAAM,CAACI,OAAO,IAAIJ,MAAM,CAACK,IAAI,EAAE;IACjC;IACA,MAAMC,MAAM,GAAGJ,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAE7C,IAAIG,MAAM,EAAE;MACV;MACA,MAAMC,aAAa,GAAGnB,SAAS,CAACgB,OAAO,CAACI,IAAI,CAACC,SAAS,CAACT,MAAM,CAACK,IAAI,CAAC,EAAEC,MAAM,CAAC;;MAE5E;MACAN,MAAM,CAACK,IAAI,GAAG;QAAEK,SAAS,EAAEH;MAAc,CAAC;;MAE1C;MACAP,MAAM,CAACJ,OAAO,CAAC,aAAa,CAAC,GAAG,MAAM;IACxC;EACF;EAEA,OAAOI,MAAM;AACf,CAAC,EACAW,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAtB,IAAI,CAACQ,YAAY,CAACiB,QAAQ,CAACf,GAAG,CAC3Be,QAAQ,IAAK;EACZ;EACA,IAAIA,QAAQ,CAAClB,OAAO,CAAC,aAAa,CAAC,KAAK,MAAM,IAAIkB,QAAQ,CAACT,IAAI,CAACK,SAAS,EAAE;IACzE;IACA,MAAMJ,MAAM,GAAGJ,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAE7C,IAAIG,MAAM,EAAE;MACV,IAAI;QACF;QACA,MAAMS,aAAa,GAAG3B,SAAS,CAAC4B,OAAO,CAACF,QAAQ,CAACT,IAAI,CAACK,SAAS,EAAEJ,MAAM,CAAC;;QAExE;QACAQ,QAAQ,CAACT,IAAI,GAAGG,IAAI,CAACS,KAAK,CAACF,aAAa,CAAC;MAC3C,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdO,OAAO,CAACP,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIM,KAAK,CAAC,UAAU,CAAC,CAAC;MAC9C;IACF;EACF;EAEA,OAAOL,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT,IAAIA,KAAK,CAACG,QAAQ,EAAE;IAClB;IACA,QAAQH,KAAK,CAACG,QAAQ,CAACM,MAAM;MAC3B,KAAK,GAAG;QACN;QACAlB,YAAY,CAACmB,UAAU,CAAC,OAAO,CAAC;QAChCnB,YAAY,CAACmB,UAAU,CAAC,UAAU,CAAC;;QAEnC;QACA,IAAIlC,MAAM,CAACmC,YAAY,CAACC,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;UAC/CtC,SAAS,CAACyB,KAAK,CAAC,aAAa,CAAC;UAC9BxB,MAAM,CAACsC,IAAI,CAAC,QAAQ,CAAC;QACvB;QACA;MAEF,KAAK,GAAG;QACN;QACAvC,SAAS,CAACyB,KAAK,CAAC,WAAW,CAAC;QAC5B;MAEF,KAAK,GAAG;QACN;QACAzB,SAAS,CAACyB,KAAK,CAAC,UAAU,CAAC;QAC3B;MAEF,KAAK,GAAG;QACN;QACAzB,SAAS,CAACyB,KAAK,CAAC,aAAa,CAAC;QAC9B;MAEF;QACE;QACA,IAAIA,KAAK,CAACG,QAAQ,CAACT,IAAI,IAAIM,KAAK,CAACG,QAAQ,CAACT,IAAI,CAACqB,OAAO,EAAE;UACtDxC,SAAS,CAACyB,KAAK,CAACA,KAAK,CAACG,QAAQ,CAACT,IAAI,CAACqB,OAAO,CAAC;QAC9C,CAAC,MAAM;UACLxC,SAAS,CAACyB,KAAK,CAAC,YAAY,CAAC;QAC/B;IACJ;EACF,CAAC,MAAM,IAAIA,KAAK,CAACb,OAAO,EAAE;IACxB;IACAZ,SAAS,CAACyB,KAAK,CAAC,cAAc,CAAC;EACjC,CAAC,MAAM;IACL;IACAzB,SAAS,CAACyB,KAAK,CAAC,QAAQ,CAAC;EAC3B;EAEA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,eAAe;EACb;EACAgB,GAAGA,CAACC,GAAG,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE7B,MAAM,GAAG,CAAC,CAAC,EAAE;IACjC,OAAOX,IAAI,CAACsC,GAAG,CAACC,GAAG,EAAE;MAAEC,MAAM;MAAE,GAAG7B;IAAO,CAAC,CAAC;EAC7C,CAAC;EAED;EACA8B,IAAIA,CAACF,GAAG,EAAEvB,IAAI,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC,EAAE;IAChC,OAAOX,IAAI,CAACyC,IAAI,CAACF,GAAG,EAAEvB,IAAI,EAAEL,MAAM,CAAC;EACrC,CAAC;EAED;EACA+B,GAAGA,CAACH,GAAG,EAAEvB,IAAI,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC,EAAE;IAC/B,OAAOX,IAAI,CAAC0C,GAAG,CAACH,GAAG,EAAEvB,IAAI,EAAEL,MAAM,CAAC;EACpC,CAAC;EAED;EACAgC,MAAMA,CAACJ,GAAG,EAAE5B,MAAM,GAAG,CAAC,CAAC,EAAE;IACvB,OAAOX,IAAI,CAAC2C,MAAM,CAACJ,GAAG,EAAE5B,MAAM,CAAC;EACjC,CAAC;EAED;EACAiC,aAAaA,CAACL,GAAG,EAAEvB,IAAI,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC,EAAE;IACzC,OAAOX,IAAI,CAACyC,IAAI,CAACF,GAAG,EAAEvB,IAAI,EAAE;MAAE,GAAGL,MAAM;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;EAC3D,CAAC;EAED;EACA8B,YAAYA,CAACN,GAAG,EAAEvB,IAAI,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC,EAAE;IACxC,OAAOX,IAAI,CAAC0C,GAAG,CAACH,GAAG,EAAEvB,IAAI,EAAE;MAAE,GAAGL,MAAM;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;EAC1D;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}