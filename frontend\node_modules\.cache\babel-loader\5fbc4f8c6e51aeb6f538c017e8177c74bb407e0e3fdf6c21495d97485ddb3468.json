{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, inject, ref, computed, unref, onUpdated, createVNode, nextTick } from 'vue';\nimport { tableV2HeaderProps } from '../header.mjs';\nimport { enforceUnit } from '../utils.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { castArray } from 'lodash-unified';\nconst COMPONENT_NAME = \"ElTableV2Header\";\nconst TableV2Header = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2HeaderProps,\n  setup(props, {\n    slots,\n    expose\n  }) {\n    const ns = useNamespace(\"table-v2\");\n    const scrollLeftInfo = inject(\"tableV2GridScrollLeft\");\n    const headerRef = ref();\n    const headerStyle = computed(() => enforceUnit({\n      width: props.width,\n      height: props.height\n    }));\n    const rowStyle = computed(() => enforceUnit({\n      width: props.rowWidth,\n      height: props.height\n    }));\n    const headerHeights = computed(() => castArray(unref(props.headerHeight)));\n    const scrollToLeft = left => {\n      const headerEl = unref(headerRef);\n      nextTick(() => {\n        (headerEl == null ? void 0 : headerEl.scroll) && headerEl.scroll({\n          left\n        });\n      });\n    };\n    const renderFixedRows = () => {\n      const fixedRowClassName = ns.e(\"fixed-header-row\");\n      const {\n        columns,\n        fixedHeaderData,\n        rowHeight\n      } = props;\n      return fixedHeaderData == null ? void 0 : fixedHeaderData.map((fixedRowData, fixedRowIndex) => {\n        var _a;\n        const style = enforceUnit({\n          height: rowHeight,\n          width: \"100%\"\n        });\n        return (_a = slots.fixed) == null ? void 0 : _a.call(slots, {\n          class: fixedRowClassName,\n          columns,\n          rowData: fixedRowData,\n          rowIndex: -(fixedRowIndex + 1),\n          style\n        });\n      });\n    };\n    const renderDynamicRows = () => {\n      const dynamicRowClassName = ns.e(\"dynamic-header-row\");\n      const {\n        columns\n      } = props;\n      return unref(headerHeights).map((rowHeight, rowIndex) => {\n        var _a;\n        const style = enforceUnit({\n          width: \"100%\",\n          height: rowHeight\n        });\n        return (_a = slots.dynamic) == null ? void 0 : _a.call(slots, {\n          class: dynamicRowClassName,\n          columns,\n          headerIndex: rowIndex,\n          style\n        });\n      });\n    };\n    onUpdated(() => {\n      if (scrollLeftInfo == null ? void 0 : scrollLeftInfo.value) {\n        scrollToLeft(scrollLeftInfo.value);\n      }\n    });\n    expose({\n      scrollToLeft\n    });\n    return () => {\n      if (props.height <= 0) return;\n      return createVNode(\"div\", {\n        \"ref\": headerRef,\n        \"class\": props.class,\n        \"style\": unref(headerStyle),\n        \"role\": \"rowgroup\"\n      }, [createVNode(\"div\", {\n        \"style\": unref(rowStyle),\n        \"class\": ns.e(\"header\")\n      }, [renderDynamicRows(), renderFixedRows()])]);\n    };\n  }\n});\nvar Header = TableV2Header;\nexport { Header as default };", "map": {"version": 3, "names": ["COMPONENT_NAME", "TableV2Header", "defineComponent", "name", "props", "tableV2HeaderProps", "slots", "expose", "ns", "useNamespace", "scrollLeftInfo", "inject", "headerRef", "ref", "headerStyle", "computed", "enforceUnit", "width", "height", "rowStyle", "row<PERSON>id<PERSON>", "headerHeights", "<PERSON><PERSON><PERSON><PERSON>", "unref", "headerHeight", "scrollToLeft", "left", "nextTick", "headerEl", "scroll", "renderFixedRows", "fixedRowClassName", "e", "columns", "fixedHeaderData", "rowHeight", "map", "fixedRowData", "fixedRowIndex", "_a", "style", "fixed", "call", "class", "rowData", "rowIndex", "renderDynamicRows", "dynamicRowClassName", "dynamic", "headerIndex", "onUpdated", "value", "createVNode", "Header"], "sources": ["../../../../../../../packages/components/table-v2/src/components/header.tsx"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  inject,\n  nextTick,\n  onUpdated,\n  ref,\n  unref,\n} from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { ensureArray } from '@element-plus/utils'\nimport { tableV2HeaderProps } from '../header'\nimport { enforceUnit } from '../utils'\n\nimport type { CSSProperties, Ref, UnwrapRef } from 'vue'\nimport type { TableV2HeaderProps } from '../header'\nimport type { UseColumnsReturn } from '../composables/use-columns'\n\nconst COMPONENT_NAME = 'ElTableV2Header'\nconst TableV2Header = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2HeaderProps,\n  setup(props, { slots, expose }) {\n    const ns = useNamespace('table-v2')\n    const scrollLeftInfo = inject<Ref<number>>('tableV2GridScrollLeft')\n\n    const headerRef = ref<HTMLElement>()\n\n    const headerStyle = computed(() =>\n      enforceUnit({\n        width: props.width,\n        height: props.height,\n      })\n    )\n\n    const rowStyle = computed(() =>\n      enforceUnit({\n        width: props.rowWidth,\n        height: props.height,\n      })\n    )\n\n    const headerHeights = computed(() => ensureArray(unref(props.headerHeight)))\n\n    const scrollToLeft = (left?: number) => {\n      const headerEl = unref(headerRef)\n      nextTick(() => {\n        headerEl?.scroll &&\n          headerEl.scroll({\n            left,\n          })\n      })\n    }\n\n    const renderFixedRows = () => {\n      const fixedRowClassName = ns.e('fixed-header-row')\n\n      const { columns, fixedHeaderData, rowHeight } = props\n\n      return fixedHeaderData?.map((fixedRowData, fixedRowIndex) => {\n        const style: CSSProperties = enforceUnit({\n          height: rowHeight,\n          width: '100%',\n        })\n\n        return slots.fixed?.({\n          class: fixedRowClassName,\n          columns,\n          rowData: fixedRowData,\n          rowIndex: -(fixedRowIndex + 1),\n          style,\n        })\n      })\n    }\n\n    const renderDynamicRows = () => {\n      const dynamicRowClassName = ns.e('dynamic-header-row')\n      const { columns } = props\n\n      return unref(headerHeights).map((rowHeight, rowIndex) => {\n        const style: CSSProperties = enforceUnit({\n          width: '100%',\n          height: rowHeight,\n        })\n\n        return slots.dynamic?.({\n          class: dynamicRowClassName,\n          columns,\n          headerIndex: rowIndex,\n          style,\n        })\n      })\n    }\n\n    onUpdated(() => {\n      if (scrollLeftInfo?.value) {\n        scrollToLeft(scrollLeftInfo.value)\n      }\n    })\n    expose({\n      /**\n       * @description scroll to position based on the provided value\n       */\n      scrollToLeft,\n    })\n\n    return () => {\n      if (props.height <= 0) return\n\n      return (\n        <div\n          ref={headerRef}\n          class={props.class}\n          style={unref(headerStyle)}\n          role=\"rowgroup\"\n        >\n          <div style={unref(rowStyle)} class={ns.e('header')}>\n            {renderDynamicRows()}\n            {renderFixedRows()}\n          </div>\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableV2Header\n\nexport type TableV2HeaderInstance = InstanceType<typeof TableV2Header> & {\n  /**\n   * @description scroll to position based on the provided value\n   */\n  scrollToLeft: (left?: number) => void\n}\n\nexport type TableV2HeaderRendererParams = {\n  class: string\n  columns: TableV2HeaderProps['columns']\n  columnsStyles: UnwrapRef<UseColumnsReturn['columnsStyles']>\n  headerIndex: number\n  style: CSSProperties\n}\n\nexport type TableV2HeaderRowRendererParams = {\n  rowData: any\n  rowIndex: number\n} & Omit<TableV2HeaderRendererParams, 'headerIndex'>\n"], "mappings": ";;;;;;;AAkBA,MAAMA,cAAc,GAAG,iBAAvB;AACA,MAAMC,aAAa,GAAGC,eAAe,CAAC;EACpCC,IAAI,EAAEH,cAD8B;EAEpCI,KAAK,EAAEC,kBAF6B;;IAG/BC,KAAA;IAAUC;EAAO;IAAU,MAAAC,EAAA,GAAAC,YAAA;IAC9B,MAAMC,cAAiB,GAAAC,MAAA,wBAAvB;IACA,MAAMC,SAAc,GAAAC,GAAA,EAAG;IAEvB,MAAMC,WAAY,GAAAC,QAAlB,OAAAC,WAAA;MAEAC,KAAA,EAAAb,KAAA,CAAAa,KAAoB;MAEhBC,MAAK,EAAOd,KAAC,CADHc;MAEV,CAAM;IAFI,MADdC,QAAA,GAAAJ,QAAA,OAAAC,WAAA;MAOAC,KAAA,EAAAb,KAAc,CAAGgB,QAAA;MAEbF,MAAK,EAAOd,KAAC,CADHc;MAEV,CAAM;IAFI,MADdG,aAAA,GAAAN,QAAA,OAAAO,SAAA,CAAAC,KAAA,CAAAnB,KAAA,CAAAoB,YAAA;IAOA,MAAMC,YAAa,GAAGC,IAAQ,IAAC;;MAEzBC,QAAA;QACJ,CAAAC,QAAc,QAAQ,YAAAA,QAAtB,CAAAC,MAAA,KAAAD,QAAA,CAAAC,MAAA;UACAH;QACE;MAEI;IADc;IAGnB,MALDI,eAAA,GAAAA,CAAA;MAFF,MAAAC,iBAAA,GAAAvB,EAAA,CAAAwB,CAAA;;QAUMC,OAAA;QACJC,eAAA;QAEMC;UAAA/B,KAAA;aAAA8B,eAAA,oBAAAA,eAAA,CAAAE,GAAA,EAAAC,YAAA,EAAAC,aAAA;QAA4B,IAAAC,EAAA;QAA5B,MAANC,KAAA,GAAAxB,WAAA;UAEOE,MAAA,EAAAiB,SAAA;UACClB,KAAA;QACJ;QACA,OAAK,CAAEsB,EAAA,GAAAjC,KAAA,CAAAmC,KAAA,qBAAAF,EAAA,CAAAG,IAAA,CAAApC,KAAA;UAFTqC,KAAA,EAAAZ,iBAAA;UAKOE,OAAA;UACLW,OAAO,EADYP,YAAA;UAEnBQ,QAFmB,IAAAP,aAAA;UAGnBE;QACA;MACA;IALmB;IAOtB,MAbDM,iBAAA,GAAAA,CAAA;MALF,MAAAC,mBAAA,GAAAvC,EAAA,CAAAwB,CAAA;;QAqBMC;MACJ,IAAA7B,KAAA;MACA,OAAMmB,KAAA,CAAAF,aAAA,EAAAe,GAAA,EAAAD,SAAA,EAAAU,QAAA;QAAE,IAAAN,EAAA;QAAF,MAANC,KAAA,GAAAxB,WAAA;UAEOC,KAAA,QAAM;UACLC,MAAA,EAAAiB;QACJ;QACA,QAAMI,EAAE,GAAAjC,KAAA,CAAA0C,OAAA,qBAAAT,EAAA,CAAAG,IAAA,CAAApC,KAAA;UAFVqC,KAAA,EAAAI,mBAAA;UAKOd,OAAA;UACLgB,WADqB,EAAAJ,QAAA;UAErBL;QACA;MACA;IAJqB;IAMxBU,SAZD;MAJF,IAAAxC,cAAA,oBAAAA,cAAA,CAAAyC,KAAA;;MAmBA;MACE;IACE5C,MAAA;MACDkB;IACF,CAJQ,CAAT;IAKA,OAAO;MACL,IAAArB,KAAA,CAAAc,MAAA,OACN;MACA,OAAAkC,WAAA;QACM,OAAAxC,SAAA;QAJF,SAAAR,KAAA,CAAAuC,KAAA;QAOA,OAAa,EAAApB,KAAA,CAAAT,WAAA;QACX,MAAS,EAAC;MAEV,IAAAsC,WAAA;QAAA,OAES,EAFT7B,KAAA,CAAAJ,QAAA;QAAA,OAGW,EAAAX,EAAA,CAAAwB,CAAA,CAAK,QAHhB;SAIW,CAAAc,iBAAM,IAAAhB,eAJjB;;EAAA;;AAAA,IAAAuB,MAAA,GAAApD,aAOwC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}