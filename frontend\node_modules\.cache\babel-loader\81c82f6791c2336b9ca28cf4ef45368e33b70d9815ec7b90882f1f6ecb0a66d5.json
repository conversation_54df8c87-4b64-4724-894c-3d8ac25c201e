{"ast": null, "code": "import baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport hasUnicode from './_hasUnicode.js';\nimport isObject from './isObject.js';\nimport isRegExp from './isRegExp.js';\nimport stringSize from './_stringSize.js';\nimport stringToArray from './_stringToArray.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/** Used as default options for `_.truncate`. */\nvar DEFAULT_TRUNC_LENGTH = 30,\n  DEFAULT_TRUNC_OMISSION = '...';\n\n/** Used to match `RegExp` flags from their coerced string values. */\nvar reFlags = /\\w*$/;\n\n/**\n * Truncates `string` if it's longer than the given maximum string length.\n * The last characters of the truncated string are replaced with the omission\n * string which defaults to \"...\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to truncate.\n * @param {Object} [options={}] The options object.\n * @param {number} [options.length=30] The maximum string length.\n * @param {string} [options.omission='...'] The string to indicate text is omitted.\n * @param {RegExp|string} [options.separator] The separator pattern to truncate to.\n * @returns {string} Returns the truncated string.\n * @example\n *\n * _.truncate('hi-diddly-ho there, neighborino');\n * // => 'hi-diddly-ho there, neighbo...'\n *\n * _.truncate('hi-diddly-ho there, neighborino', {\n *   'length': 24,\n *   'separator': ' '\n * });\n * // => 'hi-diddly-ho there,...'\n *\n * _.truncate('hi-diddly-ho there, neighborino', {\n *   'length': 24,\n *   'separator': /,? +/\n * });\n * // => 'hi-diddly-ho there...'\n *\n * _.truncate('hi-diddly-ho there, neighborino', {\n *   'omission': ' [...]'\n * });\n * // => 'hi-diddly-ho there, neig [...]'\n */\nfunction truncate(string, options) {\n  var length = DEFAULT_TRUNC_LENGTH,\n    omission = DEFAULT_TRUNC_OMISSION;\n  if (isObject(options)) {\n    var separator = 'separator' in options ? options.separator : separator;\n    length = 'length' in options ? toInteger(options.length) : length;\n    omission = 'omission' in options ? baseToString(options.omission) : omission;\n  }\n  string = toString(string);\n  var strLength = string.length;\n  if (hasUnicode(string)) {\n    var strSymbols = stringToArray(string);\n    strLength = strSymbols.length;\n  }\n  if (length >= strLength) {\n    return string;\n  }\n  var end = length - stringSize(omission);\n  if (end < 1) {\n    return omission;\n  }\n  var result = strSymbols ? castSlice(strSymbols, 0, end).join('') : string.slice(0, end);\n  if (separator === undefined) {\n    return result + omission;\n  }\n  if (strSymbols) {\n    end += result.length - end;\n  }\n  if (isRegExp(separator)) {\n    if (string.slice(end).search(separator)) {\n      var match,\n        substring = result;\n      if (!separator.global) {\n        separator = RegExp(separator.source, toString(reFlags.exec(separator)) + 'g');\n      }\n      separator.lastIndex = 0;\n      while (match = separator.exec(substring)) {\n        var newEnd = match.index;\n      }\n      result = result.slice(0, newEnd === undefined ? end : newEnd);\n    }\n  } else if (string.indexOf(baseToString(separator), end) != end) {\n    var index = result.lastIndexOf(separator);\n    if (index > -1) {\n      result = result.slice(0, index);\n    }\n  }\n  return result + omission;\n}\nexport default truncate;", "map": {"version": 3, "names": ["baseToString", "castSlice", "hasUnicode", "isObject", "isRegExp", "stringSize", "stringToArray", "toInteger", "toString", "DEFAULT_TRUNC_LENGTH", "DEFAULT_TRUNC_OMISSION", "reFlags", "truncate", "string", "options", "length", "omission", "separator", "str<PERSON><PERSON><PERSON>", "strSymbols", "end", "result", "join", "slice", "undefined", "search", "match", "substring", "global", "RegExp", "source", "exec", "lastIndex", "newEnd", "index", "indexOf", "lastIndexOf"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/truncate.js"], "sourcesContent": ["import baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport hasUnicode from './_hasUnicode.js';\nimport isObject from './isObject.js';\nimport isRegExp from './isRegExp.js';\nimport stringSize from './_stringSize.js';\nimport stringToArray from './_stringToArray.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/** Used as default options for `_.truncate`. */\nvar DEFAULT_TRUNC_LENGTH = 30,\n    DEFAULT_TRUNC_OMISSION = '...';\n\n/** Used to match `RegExp` flags from their coerced string values. */\nvar reFlags = /\\w*$/;\n\n/**\n * Truncates `string` if it's longer than the given maximum string length.\n * The last characters of the truncated string are replaced with the omission\n * string which defaults to \"...\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to truncate.\n * @param {Object} [options={}] The options object.\n * @param {number} [options.length=30] The maximum string length.\n * @param {string} [options.omission='...'] The string to indicate text is omitted.\n * @param {RegExp|string} [options.separator] The separator pattern to truncate to.\n * @returns {string} Returns the truncated string.\n * @example\n *\n * _.truncate('hi-diddly-ho there, neighborino');\n * // => 'hi-diddly-ho there, neighbo...'\n *\n * _.truncate('hi-diddly-ho there, neighborino', {\n *   'length': 24,\n *   'separator': ' '\n * });\n * // => 'hi-diddly-ho there,...'\n *\n * _.truncate('hi-diddly-ho there, neighborino', {\n *   'length': 24,\n *   'separator': /,? +/\n * });\n * // => 'hi-diddly-ho there...'\n *\n * _.truncate('hi-diddly-ho there, neighborino', {\n *   'omission': ' [...]'\n * });\n * // => 'hi-diddly-ho there, neig [...]'\n */\nfunction truncate(string, options) {\n  var length = DEFAULT_TRUNC_LENGTH,\n      omission = DEFAULT_TRUNC_OMISSION;\n\n  if (isObject(options)) {\n    var separator = 'separator' in options ? options.separator : separator;\n    length = 'length' in options ? toInteger(options.length) : length;\n    omission = 'omission' in options ? baseToString(options.omission) : omission;\n  }\n  string = toString(string);\n\n  var strLength = string.length;\n  if (hasUnicode(string)) {\n    var strSymbols = stringToArray(string);\n    strLength = strSymbols.length;\n  }\n  if (length >= strLength) {\n    return string;\n  }\n  var end = length - stringSize(omission);\n  if (end < 1) {\n    return omission;\n  }\n  var result = strSymbols\n    ? castSlice(strSymbols, 0, end).join('')\n    : string.slice(0, end);\n\n  if (separator === undefined) {\n    return result + omission;\n  }\n  if (strSymbols) {\n    end += (result.length - end);\n  }\n  if (isRegExp(separator)) {\n    if (string.slice(end).search(separator)) {\n      var match,\n          substring = result;\n\n      if (!separator.global) {\n        separator = RegExp(separator.source, toString(reFlags.exec(separator)) + 'g');\n      }\n      separator.lastIndex = 0;\n      while ((match = separator.exec(substring))) {\n        var newEnd = match.index;\n      }\n      result = result.slice(0, newEnd === undefined ? end : newEnd);\n    }\n  } else if (string.indexOf(baseToString(separator), end) != end) {\n    var index = result.lastIndexOf(separator);\n    if (index > -1) {\n      result = result.slice(0, index);\n    }\n  }\n  return result + omission;\n}\n\nexport default truncate;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,oBAAoB,GAAG,EAAE;EACzBC,sBAAsB,GAAG,KAAK;;AAElC;AACA,IAAIC,OAAO,GAAG,MAAM;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACjC,IAAIC,MAAM,GAAGN,oBAAoB;IAC7BO,QAAQ,GAAGN,sBAAsB;EAErC,IAAIP,QAAQ,CAACW,OAAO,CAAC,EAAE;IACrB,IAAIG,SAAS,GAAG,WAAW,IAAIH,OAAO,GAAGA,OAAO,CAACG,SAAS,GAAGA,SAAS;IACtEF,MAAM,GAAG,QAAQ,IAAID,OAAO,GAAGP,SAAS,CAACO,OAAO,CAACC,MAAM,CAAC,GAAGA,MAAM;IACjEC,QAAQ,GAAG,UAAU,IAAIF,OAAO,GAAGd,YAAY,CAACc,OAAO,CAACE,QAAQ,CAAC,GAAGA,QAAQ;EAC9E;EACAH,MAAM,GAAGL,QAAQ,CAACK,MAAM,CAAC;EAEzB,IAAIK,SAAS,GAAGL,MAAM,CAACE,MAAM;EAC7B,IAAIb,UAAU,CAACW,MAAM,CAAC,EAAE;IACtB,IAAIM,UAAU,GAAGb,aAAa,CAACO,MAAM,CAAC;IACtCK,SAAS,GAAGC,UAAU,CAACJ,MAAM;EAC/B;EACA,IAAIA,MAAM,IAAIG,SAAS,EAAE;IACvB,OAAOL,MAAM;EACf;EACA,IAAIO,GAAG,GAAGL,MAAM,GAAGV,UAAU,CAACW,QAAQ,CAAC;EACvC,IAAII,GAAG,GAAG,CAAC,EAAE;IACX,OAAOJ,QAAQ;EACjB;EACA,IAAIK,MAAM,GAAGF,UAAU,GACnBlB,SAAS,CAACkB,UAAU,EAAE,CAAC,EAAEC,GAAG,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC,GACtCT,MAAM,CAACU,KAAK,CAAC,CAAC,EAAEH,GAAG,CAAC;EAExB,IAAIH,SAAS,KAAKO,SAAS,EAAE;IAC3B,OAAOH,MAAM,GAAGL,QAAQ;EAC1B;EACA,IAAIG,UAAU,EAAE;IACdC,GAAG,IAAKC,MAAM,CAACN,MAAM,GAAGK,GAAI;EAC9B;EACA,IAAIhB,QAAQ,CAACa,SAAS,CAAC,EAAE;IACvB,IAAIJ,MAAM,CAACU,KAAK,CAACH,GAAG,CAAC,CAACK,MAAM,CAACR,SAAS,CAAC,EAAE;MACvC,IAAIS,KAAK;QACLC,SAAS,GAAGN,MAAM;MAEtB,IAAI,CAACJ,SAAS,CAACW,MAAM,EAAE;QACrBX,SAAS,GAAGY,MAAM,CAACZ,SAAS,CAACa,MAAM,EAAEtB,QAAQ,CAACG,OAAO,CAACoB,IAAI,CAACd,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC;MAC/E;MACAA,SAAS,CAACe,SAAS,GAAG,CAAC;MACvB,OAAQN,KAAK,GAAGT,SAAS,CAACc,IAAI,CAACJ,SAAS,CAAC,EAAG;QAC1C,IAAIM,MAAM,GAAGP,KAAK,CAACQ,KAAK;MAC1B;MACAb,MAAM,GAAGA,MAAM,CAACE,KAAK,CAAC,CAAC,EAAEU,MAAM,KAAKT,SAAS,GAAGJ,GAAG,GAAGa,MAAM,CAAC;IAC/D;EACF,CAAC,MAAM,IAAIpB,MAAM,CAACsB,OAAO,CAACnC,YAAY,CAACiB,SAAS,CAAC,EAAEG,GAAG,CAAC,IAAIA,GAAG,EAAE;IAC9D,IAAIc,KAAK,GAAGb,MAAM,CAACe,WAAW,CAACnB,SAAS,CAAC;IACzC,IAAIiB,KAAK,GAAG,CAAC,CAAC,EAAE;MACdb,MAAM,GAAGA,MAAM,CAACE,KAAK,CAAC,CAAC,EAAEW,KAAK,CAAC;IACjC;EACF;EACA,OAAOb,MAAM,GAAGL,QAAQ;AAC1B;AAEA,eAAeJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}