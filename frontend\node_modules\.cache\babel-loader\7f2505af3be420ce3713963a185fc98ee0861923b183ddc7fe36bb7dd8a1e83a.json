{"ast": null, "code": "require(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.map.js\");\n/* eslint-disable no-bitwise, no-mixed-operators, complexity */\nconst DECRYPT = 0;\nconst ROUND = 32;\nconst BLOCK = 16;\nconst Sbox = [0xd6, 0x90, 0xe9, 0xfe, 0xcc, 0xe1, 0x3d, 0xb7, 0x16, 0xb6, 0x14, 0xc2, 0x28, 0xfb, 0x2c, 0x05, 0x2b, 0x67, 0x9a, 0x76, 0x2a, 0xbe, 0x04, 0xc3, 0xaa, 0x44, 0x13, 0x26, 0x49, 0x86, 0x06, 0x99, 0x9c, 0x42, 0x50, 0xf4, 0x91, 0xef, 0x98, 0x7a, 0x33, 0x54, 0x0b, 0x43, 0xed, 0xcf, 0xac, 0x62, 0xe4, 0xb3, 0x1c, 0xa9, 0xc9, 0x08, 0xe8, 0x95, 0x80, 0xdf, 0x94, 0xfa, 0x75, 0x8f, 0x3f, 0xa6, 0x47, 0x07, 0xa7, 0xfc, 0xf3, 0x73, 0x17, 0xba, 0x83, 0x59, 0x3c, 0x19, 0xe6, 0x85, 0x4f, 0xa8, 0x68, 0x6b, 0x81, 0xb2, 0x71, 0x64, 0xda, 0x8b, 0xf8, 0xeb, 0x0f, 0x4b, 0x70, 0x56, 0x9d, 0x35, 0x1e, 0x24, 0x0e, 0x5e, 0x63, 0x58, 0xd1, 0xa2, 0x25, 0x22, 0x7c, 0x3b, 0x01, 0x21, 0x78, 0x87, 0xd4, 0x00, 0x46, 0x57, 0x9f, 0xd3, 0x27, 0x52, 0x4c, 0x36, 0x02, 0xe7, 0xa0, 0xc4, 0xc8, 0x9e, 0xea, 0xbf, 0x8a, 0xd2, 0x40, 0xc7, 0x38, 0xb5, 0xa3, 0xf7, 0xf2, 0xce, 0xf9, 0x61, 0x15, 0xa1, 0xe0, 0xae, 0x5d, 0xa4, 0x9b, 0x34, 0x1a, 0x55, 0xad, 0x93, 0x32, 0x30, 0xf5, 0x8c, 0xb1, 0xe3, 0x1d, 0xf6, 0xe2, 0x2e, 0x82, 0x66, 0xca, 0x60, 0xc0, 0x29, 0x23, 0xab, 0x0d, 0x53, 0x4e, 0x6f, 0xd5, 0xdb, 0x37, 0x45, 0xde, 0xfd, 0x8e, 0x2f, 0x03, 0xff, 0x6a, 0x72, 0x6d, 0x6c, 0x5b, 0x51, 0x8d, 0x1b, 0xaf, 0x92, 0xbb, 0xdd, 0xbc, 0x7f, 0x11, 0xd9, 0x5c, 0x41, 0x1f, 0x10, 0x5a, 0xd8, 0x0a, 0xc1, 0x31, 0x88, 0xa5, 0xcd, 0x7b, 0xbd, 0x2d, 0x74, 0xd0, 0x12, 0xb8, 0xe5, 0xb4, 0xb0, 0x89, 0x69, 0x97, 0x4a, 0x0c, 0x96, 0x77, 0x7e, 0x65, 0xb9, 0xf1, 0x09, 0xc5, 0x6e, 0xc6, 0x84, 0x18, 0xf0, 0x7d, 0xec, 0x3a, 0xdc, 0x4d, 0x20, 0x79, 0xee, 0x5f, 0x3e, 0xd7, 0xcb, 0x39, 0x48];\nconst CK = [0x00070e15, 0x1c232a31, 0x383f464d, 0x545b6269, 0x70777e85, 0x8c939aa1, 0xa8afb6bd, 0xc4cbd2d9, 0xe0e7eef5, 0xfc030a11, 0x181f262d, 0x343b4249, 0x50575e65, 0x6c737a81, 0x888f969d, 0xa4abb2b9, 0xc0c7ced5, 0xdce3eaf1, 0xf8ff060d, 0x141b2229, 0x30373e45, 0x4c535a61, 0x686f767d, 0x848b9299, 0xa0a7aeb5, 0xbcc3cad1, 0xd8dfe6ed, 0xf4fb0209, 0x10171e25, 0x2c333a41, 0x484f565d, 0x646b7279];\n\n/**\r\n * 16 进制串转字节数组\r\n */\nfunction hexToArray(str) {\n  const arr = [];\n  for (let i = 0, len = str.length; i < len; i += 2) {\n    arr.push(parseInt(str.substr(i, 2), 16));\n  }\n  return arr;\n}\n\n/**\r\n * 字节数组转 16 进制串\r\n */\nfunction ArrayToHex(arr) {\n  return arr.map(item => {\n    item = item.toString(16);\n    return item.length === 1 ? '0' + item : item;\n  }).join('');\n}\n\n/**\r\n * utf8 串转字节数组\r\n */\nfunction utf8ToArray(str) {\n  const arr = [];\n  for (let i = 0, len = str.length; i < len; i++) {\n    const point = str.codePointAt(i);\n    if (point <= 0x007f) {\n      // 单字节，标量值：00000000 00000000 0zzzzzzz\n      arr.push(point);\n    } else if (point <= 0x07ff) {\n      // 双字节，标量值：00000000 00000yyy yyzzzzzz\n      arr.push(0xc0 | point >>> 6); // 110yyyyy（0xc0-0xdf）\n      arr.push(0x80 | point & 0x3f); // 10zzzzzz（0x80-0xbf）\n    } else if (point <= 0xD7FF || point >= 0xE000 && point <= 0xFFFF) {\n      // 三字节：标量值：00000000 xxxxyyyy yyzzzzzz\n      arr.push(0xe0 | point >>> 12); // 1110xxxx（0xe0-0xef）\n      arr.push(0x80 | point >>> 6 & 0x3f); // 10yyyyyy（0x80-0xbf）\n      arr.push(0x80 | point & 0x3f); // 10zzzzzz（0x80-0xbf）\n    } else if (point >= 0x010000 && point <= 0x10FFFF) {\n      // 四字节：标量值：000wwwxx xxxxyyyy yyzzzzzz\n      i++;\n      arr.push(0xf0 | point >>> 18 & 0x1c); // 11110www（0xf0-0xf7）\n      arr.push(0x80 | point >>> 12 & 0x3f); // 10xxxxxx（0x80-0xbf）\n      arr.push(0x80 | point >>> 6 & 0x3f); // 10yyyyyy（0x80-0xbf）\n      arr.push(0x80 | point & 0x3f); // 10zzzzzz（0x80-0xbf）\n    } else {\n      // 五、六字节，暂时不支持\n      arr.push(point);\n      throw new Error('input is not supported');\n    }\n  }\n  return arr;\n}\n\n/**\r\n * 字节数组转 utf8 串\r\n */\nfunction arrayToUtf8(arr) {\n  const str = [];\n  for (let i = 0, len = arr.length; i < len; i++) {\n    if (arr[i] >= 0xf0 && arr[i] <= 0xf7) {\n      // 四字节\n      str.push(String.fromCodePoint(((arr[i] & 0x07) << 18) + ((arr[i + 1] & 0x3f) << 12) + ((arr[i + 2] & 0x3f) << 6) + (arr[i + 3] & 0x3f)));\n      i += 3;\n    } else if (arr[i] >= 0xe0 && arr[i] <= 0xef) {\n      // 三字节\n      str.push(String.fromCodePoint(((arr[i] & 0x0f) << 12) + ((arr[i + 1] & 0x3f) << 6) + (arr[i + 2] & 0x3f)));\n      i += 2;\n    } else if (arr[i] >= 0xc0 && arr[i] <= 0xdf) {\n      // 双字节\n      str.push(String.fromCodePoint(((arr[i] & 0x1f) << 6) + (arr[i + 1] & 0x3f)));\n      i++;\n    } else {\n      // 单字节\n      str.push(String.fromCodePoint(arr[i]));\n    }\n  }\n  return str.join('');\n}\n\n/**\r\n * 32 比特循环左移\r\n */\nfunction rotl(x, n) {\n  const s = n & 31;\n  return x << s | x >>> 32 - s;\n}\n\n/**\r\n * 非线性变换\r\n */\nfunction byteSub(a) {\n  return (Sbox[a >>> 24 & 0xFF] & 0xFF) << 24 | (Sbox[a >>> 16 & 0xFF] & 0xFF) << 16 | (Sbox[a >>> 8 & 0xFF] & 0xFF) << 8 | Sbox[a & 0xFF] & 0xFF;\n}\n\n/**\r\n * 线性变换，加密/解密用\r\n */\nfunction l1(b) {\n  return b ^ rotl(b, 2) ^ rotl(b, 10) ^ rotl(b, 18) ^ rotl(b, 24);\n}\n\n/**\r\n * 线性变换，生成轮密钥用\r\n */\nfunction l2(b) {\n  return b ^ rotl(b, 13) ^ rotl(b, 23);\n}\n\n/**\r\n * 以一组 128 比特进行加密/解密操作\r\n */\nfunction sms4Crypt(input, output, roundKey) {\n  const x = new Array(4);\n\n  // 字节数组转成字数组（此处 1 字 = 32 比特）\n  const tmp = new Array(4);\n  for (let i = 0; i < 4; i++) {\n    tmp[0] = input[4 * i] & 0xff;\n    tmp[1] = input[4 * i + 1] & 0xff;\n    tmp[2] = input[4 * i + 2] & 0xff;\n    tmp[3] = input[4 * i + 3] & 0xff;\n    x[i] = tmp[0] << 24 | tmp[1] << 16 | tmp[2] << 8 | tmp[3];\n  }\n\n  // x[i + 4] = x[i] ^ l1(byteSub(x[i + 1] ^ x[i + 2] ^ x[i + 3] ^ roundKey[i]))\n  for (let r = 0, mid; r < 32; r += 4) {\n    mid = x[1] ^ x[2] ^ x[3] ^ roundKey[r + 0];\n    x[0] ^= l1(byteSub(mid)); // x[4]\n\n    mid = x[2] ^ x[3] ^ x[0] ^ roundKey[r + 1];\n    x[1] ^= l1(byteSub(mid)); // x[5]\n\n    mid = x[3] ^ x[0] ^ x[1] ^ roundKey[r + 2];\n    x[2] ^= l1(byteSub(mid)); // x[6]\n\n    mid = x[0] ^ x[1] ^ x[2] ^ roundKey[r + 3];\n    x[3] ^= l1(byteSub(mid)); // x[7]\n  }\n\n  // 反序变换\n  for (let j = 0; j < 16; j += 4) {\n    output[j] = x[3 - j / 4] >>> 24 & 0xff;\n    output[j + 1] = x[3 - j / 4] >>> 16 & 0xff;\n    output[j + 2] = x[3 - j / 4] >>> 8 & 0xff;\n    output[j + 3] = x[3 - j / 4] & 0xff;\n  }\n}\n\n/**\r\n * 密钥扩展算法\r\n */\nfunction sms4KeyExt(key, roundKey, cryptFlag) {\n  const x = new Array(4);\n\n  // 字节数组转成字数组（此处 1 字 = 32 比特）\n  const tmp = new Array(4);\n  for (let i = 0; i < 4; i++) {\n    tmp[0] = key[0 + 4 * i] & 0xff;\n    tmp[1] = key[1 + 4 * i] & 0xff;\n    tmp[2] = key[2 + 4 * i] & 0xff;\n    tmp[3] = key[3 + 4 * i] & 0xff;\n    x[i] = tmp[0] << 24 | tmp[1] << 16 | tmp[2] << 8 | tmp[3];\n  }\n\n  // 与系统参数做异或\n  x[0] ^= 0xa3b1bac6;\n  x[1] ^= 0x56aa3350;\n  x[2] ^= 0x677d9197;\n  x[3] ^= 0xb27022dc;\n\n  // roundKey[i] = x[i + 4] = x[i] ^ l2(byteSub(x[i + 1] ^ x[i + 2] ^ x[i + 3] ^ CK[i]))\n  for (let r = 0, mid; r < 32; r += 4) {\n    mid = x[1] ^ x[2] ^ x[3] ^ CK[r + 0];\n    roundKey[r + 0] = x[0] ^= l2(byteSub(mid)); // x[4]\n\n    mid = x[2] ^ x[3] ^ x[0] ^ CK[r + 1];\n    roundKey[r + 1] = x[1] ^= l2(byteSub(mid)); // x[5]\n\n    mid = x[3] ^ x[0] ^ x[1] ^ CK[r + 2];\n    roundKey[r + 2] = x[2] ^= l2(byteSub(mid)); // x[6]\n\n    mid = x[0] ^ x[1] ^ x[2] ^ CK[r + 3];\n    roundKey[r + 3] = x[3] ^= l2(byteSub(mid)); // x[7]\n  }\n\n  // 解密时使用反序的轮密钥\n  if (cryptFlag === DECRYPT) {\n    for (let r = 0, mid; r < 16; r++) {\n      mid = roundKey[r];\n      roundKey[r] = roundKey[31 - r];\n      roundKey[31 - r] = mid;\n    }\n  }\n}\nfunction sm4(inArray, key, cryptFlag, {\n  padding = 'pkcs#7',\n  mode,\n  iv = [],\n  output = 'string'\n} = {}) {\n  if (mode === 'cbc') {\n    // CBC 模式，默认走 ECB 模式\n    if (typeof iv === 'string') iv = hexToArray(iv);\n    if (iv.length !== 128 / 8) {\n      // iv 不是 128 比特\n      throw new Error('iv is invalid');\n    }\n  }\n\n  // 检查 key\n  if (typeof key === 'string') key = hexToArray(key);\n  if (key.length !== 128 / 8) {\n    // key 不是 128 比特\n    throw new Error('key is invalid');\n  }\n\n  // 检查输入\n  if (typeof inArray === 'string') {\n    if (cryptFlag !== DECRYPT) {\n      // 加密，输入为 utf8 串\n      inArray = utf8ToArray(inArray);\n    } else {\n      // 解密，输入为 16 进制串\n      inArray = hexToArray(inArray);\n    }\n  } else {\n    inArray = [...inArray];\n  }\n\n  // 新增填充，sm4 是 16 个字节一个分组，所以统一走到 pkcs#7\n  if ((padding === 'pkcs#5' || padding === 'pkcs#7') && cryptFlag !== DECRYPT) {\n    const paddingCount = BLOCK - inArray.length % BLOCK;\n    for (let i = 0; i < paddingCount; i++) inArray.push(paddingCount);\n  }\n\n  // 生成轮密钥\n  const roundKey = new Array(ROUND);\n  sms4KeyExt(key, roundKey, cryptFlag);\n  const outArray = [];\n  let lastVector = iv;\n  let restLen = inArray.length;\n  let point = 0;\n  while (restLen >= BLOCK) {\n    const input = inArray.slice(point, point + 16);\n    const output = new Array(16);\n    if (mode === 'cbc') {\n      for (let i = 0; i < BLOCK; i++) {\n        if (cryptFlag !== DECRYPT) {\n          // 加密过程在组加密前进行异或\n          input[i] ^= lastVector[i];\n        }\n      }\n    }\n    sms4Crypt(input, output, roundKey);\n    for (let i = 0; i < BLOCK; i++) {\n      if (mode === 'cbc') {\n        if (cryptFlag === DECRYPT) {\n          // 解密过程在组解密后进行异或\n          output[i] ^= lastVector[i];\n        }\n      }\n      outArray[point + i] = output[i];\n    }\n    if (mode === 'cbc') {\n      if (cryptFlag !== DECRYPT) {\n        // 使用上一次输出作为加密向量\n        lastVector = output;\n      } else {\n        // 使用上一次输入作为解密向量\n        lastVector = input;\n      }\n    }\n    restLen -= BLOCK;\n    point += BLOCK;\n  }\n\n  // 去除填充，sm4 是 16 个字节一个分组，所以统一走到 pkcs#7\n  if ((padding === 'pkcs#5' || padding === 'pkcs#7') && cryptFlag === DECRYPT) {\n    const len = outArray.length;\n    const paddingCount = outArray[len - 1];\n    for (let i = 1; i <= paddingCount; i++) {\n      if (outArray[len - i] !== paddingCount) throw new Error('padding is invalid');\n    }\n    outArray.splice(len - paddingCount, paddingCount);\n  }\n\n  // 调整输出\n  if (output !== 'array') {\n    if (cryptFlag !== DECRYPT) {\n      // 加密，输出转 16 进制串\n      return ArrayToHex(outArray);\n    } else {\n      // 解密，输出转 utf8 串\n      return arrayToUtf8(outArray);\n    }\n  } else {\n    return outArray;\n  }\n}\nmodule.exports = {\n  encrypt(inArray, key, options) {\n    return sm4(inArray, key, 1, options);\n  },\n  decrypt(inArray, key, options) {\n    return sm4(inArray, key, 0, options);\n  }\n};", "map": {"version": 3, "names": ["DECRYPT", "ROUND", "BLOCK", "Sbox", "CK", "hexToArray", "str", "arr", "i", "len", "length", "push", "parseInt", "substr", "ArrayToHex", "map", "item", "toString", "join", "utf8ToArray", "point", "codePointAt", "Error", "arrayToUtf8", "String", "fromCodePoint", "rotl", "x", "n", "s", "byteSub", "a", "l1", "b", "l2", "sms4Crypt", "input", "output", "roundKey", "Array", "tmp", "r", "mid", "j", "sms4KeyExt", "key", "cryptFlag", "sm4", "inArray", "padding", "mode", "iv", "paddingCount", "outArray", "lastVector", "restLen", "slice", "splice", "module", "exports", "encrypt", "options", "decrypt"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/sm-crypto/src/sm4/index.js"], "sourcesContent": ["/* eslint-disable no-bitwise, no-mixed-operators, complexity */\r\nconst DECRYPT = 0\r\nconst ROUND = 32\r\nconst BLOCK = 16\r\n\r\nconst Sbox = [\r\n  0xd6, 0x90, 0xe9, 0xfe, 0xcc, 0xe1, 0x3d, 0xb7, 0x16, 0xb6, 0x14, 0xc2, 0x28, 0xfb, 0x2c, 0x05,\r\n  0x2b, 0x67, 0x9a, 0x76, 0x2a, 0xbe, 0x04, 0xc3, 0xaa, 0x44, 0x13, 0x26, 0x49, 0x86, 0x06, 0x99,\r\n  0x9c, 0x42, 0x50, 0xf4, 0x91, 0xef, 0x98, 0x7a, 0x33, 0x54, 0x0b, 0x43, 0xed, 0xcf, 0xac, 0x62,\r\n  0xe4, 0xb3, 0x1c, 0xa9, 0xc9, 0x08, 0xe8, 0x95, 0x80, 0xdf, 0x94, 0xfa, 0x75, 0x8f, 0x3f, 0xa6,\r\n  0x47, 0x07, 0xa7, 0xfc, 0xf3, 0x73, 0x17, 0xba, 0x83, 0x59, 0x3c, 0x19, 0xe6, 0x85, 0x4f, 0xa8,\r\n  0x68, 0x6b, 0x81, 0xb2, 0x71, 0x64, 0xda, 0x8b, 0xf8, 0xeb, 0x0f, 0x4b, 0x70, 0x56, 0x9d, 0x35,\r\n  0x1e, 0x24, 0x0e, 0x5e, 0x63, 0x58, 0xd1, 0xa2, 0x25, 0x22, 0x7c, 0x3b, 0x01, 0x21, 0x78, 0x87,\r\n  0xd4, 0x00, 0x46, 0x57, 0x9f, 0xd3, 0x27, 0x52, 0x4c, 0x36, 0x02, 0xe7, 0xa0, 0xc4, 0xc8, 0x9e,\r\n  0xea, 0xbf, 0x8a, 0xd2, 0x40, 0xc7, 0x38, 0xb5, 0xa3, 0xf7, 0xf2, 0xce, 0xf9, 0x61, 0x15, 0xa1,\r\n  0xe0, 0xae, 0x5d, 0xa4, 0x9b, 0x34, 0x1a, 0x55, 0xad, 0x93, 0x32, 0x30, 0xf5, 0x8c, 0xb1, 0xe3,\r\n  0x1d, 0xf6, 0xe2, 0x2e, 0x82, 0x66, 0xca, 0x60, 0xc0, 0x29, 0x23, 0xab, 0x0d, 0x53, 0x4e, 0x6f,\r\n  0xd5, 0xdb, 0x37, 0x45, 0xde, 0xfd, 0x8e, 0x2f, 0x03, 0xff, 0x6a, 0x72, 0x6d, 0x6c, 0x5b, 0x51,\r\n  0x8d, 0x1b, 0xaf, 0x92, 0xbb, 0xdd, 0xbc, 0x7f, 0x11, 0xd9, 0x5c, 0x41, 0x1f, 0x10, 0x5a, 0xd8,\r\n  0x0a, 0xc1, 0x31, 0x88, 0xa5, 0xcd, 0x7b, 0xbd, 0x2d, 0x74, 0xd0, 0x12, 0xb8, 0xe5, 0xb4, 0xb0,\r\n  0x89, 0x69, 0x97, 0x4a, 0x0c, 0x96, 0x77, 0x7e, 0x65, 0xb9, 0xf1, 0x09, 0xc5, 0x6e, 0xc6, 0x84,\r\n  0x18, 0xf0, 0x7d, 0xec, 0x3a, 0xdc, 0x4d, 0x20, 0x79, 0xee, 0x5f, 0x3e, 0xd7, 0xcb, 0x39, 0x48\r\n]\r\n\r\nconst CK = [\r\n  0x00070e15, 0x1c232a31, 0x383f464d, 0x545b6269,\r\n  0x70777e85, 0x8c939aa1, 0xa8afb6bd, 0xc4cbd2d9,\r\n  0xe0e7eef5, 0xfc030a11, 0x181f262d, 0x343b4249,\r\n  0x50575e65, 0x6c737a81, 0x888f969d, 0xa4abb2b9,\r\n  0xc0c7ced5, 0xdce3eaf1, 0xf8ff060d, 0x141b2229,\r\n  0x30373e45, 0x4c535a61, 0x686f767d, 0x848b9299,\r\n  0xa0a7aeb5, 0xbcc3cad1, 0xd8dfe6ed, 0xf4fb0209,\r\n  0x10171e25, 0x2c333a41, 0x484f565d, 0x646b7279\r\n]\r\n\r\n/**\r\n * 16 进制串转字节数组\r\n */\r\nfunction hexToArray(str) {\r\n  const arr = []\r\n  for (let i = 0, len = str.length; i < len; i += 2) {\r\n    arr.push(parseInt(str.substr(i, 2), 16))\r\n  }\r\n  return arr\r\n}\r\n\r\n/**\r\n * 字节数组转 16 进制串\r\n */\r\nfunction ArrayToHex(arr) {\r\n  return arr.map(item => {\r\n    item = item.toString(16)\r\n    return item.length === 1 ? '0' + item : item\r\n  }).join('')\r\n}\r\n\r\n/**\r\n * utf8 串转字节数组\r\n */\r\nfunction utf8ToArray(str) {\r\n  const arr = []\r\n\r\n  for (let i = 0, len = str.length; i < len; i++) {\r\n    const point = str.codePointAt(i)\r\n\r\n    if (point <= 0x007f) {\r\n      // 单字节，标量值：00000000 00000000 0zzzzzzz\r\n      arr.push(point)\r\n    } else if (point <= 0x07ff) {\r\n      // 双字节，标量值：00000000 00000yyy yyzzzzzz\r\n      arr.push(0xc0 | (point >>> 6)) // 110yyyyy（0xc0-0xdf）\r\n      arr.push(0x80 | (point & 0x3f)) // 10zzzzzz（0x80-0xbf）\r\n    } else if (point <= 0xD7FF || (point >= 0xE000 && point <= 0xFFFF)) {\r\n      // 三字节：标量值：00000000 xxxxyyyy yyzzzzzz\r\n      arr.push(0xe0 | (point >>> 12)) // 1110xxxx（0xe0-0xef）\r\n      arr.push(0x80 | ((point >>> 6) & 0x3f)) // 10yyyyyy（0x80-0xbf）\r\n      arr.push(0x80 | (point & 0x3f)) // 10zzzzzz（0x80-0xbf）\r\n    } else if (point >= 0x010000 && point <= 0x10FFFF) {\r\n      // 四字节：标量值：000wwwxx xxxxyyyy yyzzzzzz\r\n      i++\r\n      arr.push((0xf0 | (point >>> 18) & 0x1c)) // 11110www（0xf0-0xf7）\r\n      arr.push((0x80 | ((point >>> 12) & 0x3f))) // 10xxxxxx（0x80-0xbf）\r\n      arr.push((0x80 | ((point >>> 6) & 0x3f))) // 10yyyyyy（0x80-0xbf）\r\n      arr.push((0x80 | (point & 0x3f))) // 10zzzzzz（0x80-0xbf）\r\n    } else {\r\n      // 五、六字节，暂时不支持\r\n      arr.push(point)\r\n      throw new Error('input is not supported')\r\n    }\r\n  }\r\n\r\n  return arr\r\n}\r\n\r\n/**\r\n * 字节数组转 utf8 串\r\n */\r\nfunction arrayToUtf8(arr) {\r\n  const str = []\r\n  for (let i = 0, len = arr.length; i < len; i++) {\r\n    if (arr[i] >= 0xf0 && arr[i] <= 0xf7) {\r\n      // 四字节\r\n      str.push(String.fromCodePoint(((arr[i] & 0x07) << 18) + ((arr[i + 1] & 0x3f) << 12) + ((arr[i + 2] & 0x3f) << 6) + (arr[i + 3] & 0x3f)))\r\n      i += 3\r\n    } else if (arr[i] >= 0xe0 && arr[i] <= 0xef) {\r\n      // 三字节\r\n      str.push(String.fromCodePoint(((arr[i] & 0x0f) << 12) + ((arr[i + 1] & 0x3f) << 6) + (arr[i + 2] & 0x3f)))\r\n      i += 2\r\n    } else if (arr[i] >= 0xc0 && arr[i] <= 0xdf) {\r\n      // 双字节\r\n      str.push(String.fromCodePoint(((arr[i] & 0x1f) << 6) + (arr[i + 1] & 0x3f)))\r\n      i++\r\n    } else {\r\n      // 单字节\r\n      str.push(String.fromCodePoint(arr[i]))\r\n    }\r\n  }\r\n\r\n  return str.join('')\r\n}\r\n\r\n/**\r\n * 32 比特循环左移\r\n */\r\nfunction rotl(x, n) {\r\n  const s = n & 31\r\n  return (x << s) | (x >>> (32 - s))\r\n}\r\n\r\n/**\r\n * 非线性变换\r\n */\r\nfunction byteSub(a) {\r\n  return (Sbox[a >>> 24 & 0xFF] & 0xFF) << 24 |\r\n    (Sbox[a >>> 16 & 0xFF] & 0xFF) << 16 |\r\n    (Sbox[a >>> 8 & 0xFF] & 0xFF) << 8 |\r\n    (Sbox[a & 0xFF] & 0xFF)\r\n}\r\n\r\n/**\r\n * 线性变换，加密/解密用\r\n */\r\nfunction l1(b) {\r\n  return b ^ rotl(b, 2) ^ rotl(b, 10) ^ rotl(b, 18) ^ rotl(b, 24)\r\n}\r\n\r\n/**\r\n * 线性变换，生成轮密钥用\r\n */\r\nfunction l2(b) {\r\n  return b ^ rotl(b, 13) ^ rotl(b, 23)\r\n}\r\n\r\n/**\r\n * 以一组 128 比特进行加密/解密操作\r\n */\r\nfunction sms4Crypt(input, output, roundKey) {\r\n  const x = new Array(4)\r\n\r\n  // 字节数组转成字数组（此处 1 字 = 32 比特）\r\n  const tmp = new Array(4)\r\n  for (let i = 0; i < 4; i++) {\r\n    tmp[0] = input[4 * i] & 0xff\r\n    tmp[1] = input[4 * i + 1] & 0xff\r\n    tmp[2] = input[4 * i + 2] & 0xff\r\n    tmp[3] = input[4 * i + 3] & 0xff\r\n    x[i] = tmp[0] << 24 | tmp[1] << 16 | tmp[2] << 8 | tmp[3]\r\n  }\r\n\r\n  // x[i + 4] = x[i] ^ l1(byteSub(x[i + 1] ^ x[i + 2] ^ x[i + 3] ^ roundKey[i]))\r\n  for (let r = 0, mid; r < 32; r += 4) {\r\n    mid = x[1] ^ x[2] ^ x[3] ^ roundKey[r + 0]\r\n    x[0] ^= l1(byteSub(mid)) // x[4]\r\n\r\n    mid = x[2] ^ x[3] ^ x[0] ^ roundKey[r + 1]\r\n    x[1] ^= l1(byteSub(mid)) // x[5]\r\n\r\n    mid = x[3] ^ x[0] ^ x[1] ^ roundKey[r + 2]\r\n    x[2] ^= l1(byteSub(mid)) // x[6]\r\n\r\n    mid = x[0] ^ x[1] ^ x[2] ^ roundKey[r + 3]\r\n    x[3] ^= l1(byteSub(mid)) // x[7]\r\n  }\r\n\r\n  // 反序变换\r\n  for (let j = 0; j < 16; j += 4) {\r\n    output[j] = x[3 - j / 4] >>> 24 & 0xff\r\n    output[j + 1] = x[3 - j / 4] >>> 16 & 0xff\r\n    output[j + 2] = x[3 - j / 4] >>> 8 & 0xff\r\n    output[j + 3] = x[3 - j / 4] & 0xff\r\n  }\r\n}\r\n\r\n/**\r\n * 密钥扩展算法\r\n */\r\nfunction sms4KeyExt(key, roundKey, cryptFlag) {\r\n  const x = new Array(4)\r\n\r\n  // 字节数组转成字数组（此处 1 字 = 32 比特）\r\n  const tmp = new Array(4)\r\n  for (let i = 0; i < 4; i++) {\r\n    tmp[0] = key[0 + 4 * i] & 0xff\r\n    tmp[1] = key[1 + 4 * i] & 0xff\r\n    tmp[2] = key[2 + 4 * i] & 0xff\r\n    tmp[3] = key[3 + 4 * i] & 0xff\r\n    x[i] = tmp[0] << 24 | tmp[1] << 16 | tmp[2] << 8 | tmp[3]\r\n  }\r\n\r\n  // 与系统参数做异或\r\n  x[0] ^= 0xa3b1bac6\r\n  x[1] ^= 0x56aa3350\r\n  x[2] ^= 0x677d9197\r\n  x[3] ^= 0xb27022dc\r\n\r\n  // roundKey[i] = x[i + 4] = x[i] ^ l2(byteSub(x[i + 1] ^ x[i + 2] ^ x[i + 3] ^ CK[i]))\r\n  for (let r = 0, mid; r < 32; r += 4) {\r\n    mid = x[1] ^ x[2] ^ x[3] ^ CK[r + 0]\r\n    roundKey[r + 0] = x[0] ^= l2(byteSub(mid)) // x[4]\r\n\r\n    mid = x[2] ^ x[3] ^ x[0] ^ CK[r + 1]\r\n    roundKey[r + 1] = x[1] ^= l2(byteSub(mid)) // x[5]\r\n\r\n    mid = x[3] ^ x[0] ^ x[1] ^ CK[r + 2]\r\n    roundKey[r + 2] = x[2] ^= l2(byteSub(mid)) // x[6]\r\n\r\n    mid = x[0] ^ x[1] ^ x[2] ^ CK[r + 3]\r\n    roundKey[r + 3] = x[3] ^= l2(byteSub(mid)) // x[7]\r\n  }\r\n\r\n  // 解密时使用反序的轮密钥\r\n  if (cryptFlag === DECRYPT) {\r\n    for (let r = 0, mid; r < 16; r++) {\r\n      mid = roundKey[r]\r\n      roundKey[r] = roundKey[31 - r]\r\n      roundKey[31 - r] = mid\r\n    }\r\n  }\r\n}\r\n\r\nfunction sm4(inArray, key, cryptFlag, {\r\n  padding = 'pkcs#7', mode, iv = [], output = 'string'\r\n} = {}) {\r\n  if (mode === 'cbc') {\r\n    // CBC 模式，默认走 ECB 模式\r\n    if (typeof iv === 'string') iv = hexToArray(iv)\r\n    if (iv.length !== (128 / 8)) {\r\n      // iv 不是 128 比特\r\n      throw new Error('iv is invalid')\r\n    }\r\n  }\r\n\r\n  // 检查 key\r\n  if (typeof key === 'string') key = hexToArray(key)\r\n  if (key.length !== (128 / 8)) {\r\n    // key 不是 128 比特\r\n    throw new Error('key is invalid')\r\n  }\r\n\r\n  // 检查输入\r\n  if (typeof inArray === 'string') {\r\n    if (cryptFlag !== DECRYPT) {\r\n      // 加密，输入为 utf8 串\r\n      inArray = utf8ToArray(inArray)\r\n    } else {\r\n      // 解密，输入为 16 进制串\r\n      inArray = hexToArray(inArray)\r\n    }\r\n  } else {\r\n    inArray = [...inArray]\r\n  }\r\n\r\n  // 新增填充，sm4 是 16 个字节一个分组，所以统一走到 pkcs#7\r\n  if ((padding === 'pkcs#5' || padding === 'pkcs#7') && cryptFlag !== DECRYPT) {\r\n    const paddingCount = BLOCK - inArray.length % BLOCK\r\n    for (let i = 0; i < paddingCount; i++) inArray.push(paddingCount)\r\n  }\r\n\r\n  // 生成轮密钥\r\n  const roundKey = new Array(ROUND)\r\n  sms4KeyExt(key, roundKey, cryptFlag)\r\n\r\n  const outArray = []\r\n  let lastVector = iv\r\n  let restLen = inArray.length\r\n  let point = 0\r\n  while (restLen >= BLOCK) {\r\n    const input = inArray.slice(point, point + 16)\r\n    const output = new Array(16)\r\n\r\n    if (mode === 'cbc') {\r\n      for (let i = 0; i < BLOCK; i++) {\r\n        if (cryptFlag !== DECRYPT) {\r\n          // 加密过程在组加密前进行异或\r\n          input[i] ^= lastVector[i]\r\n        }\r\n      }\r\n    }\r\n\r\n    sms4Crypt(input, output, roundKey)\r\n\r\n\r\n    for (let i = 0; i < BLOCK; i++) {\r\n      if (mode === 'cbc') {\r\n        if (cryptFlag === DECRYPT) {\r\n          // 解密过程在组解密后进行异或\r\n          output[i] ^= lastVector[i]\r\n        }\r\n      }\r\n\r\n      outArray[point + i] = output[i]\r\n    }\r\n\r\n    if (mode === 'cbc') {\r\n      if (cryptFlag !== DECRYPT) {\r\n        // 使用上一次输出作为加密向量\r\n        lastVector = output\r\n      } else {\r\n        // 使用上一次输入作为解密向量\r\n        lastVector = input\r\n      }\r\n    }\r\n\r\n    restLen -= BLOCK\r\n    point += BLOCK\r\n  }\r\n\r\n  // 去除填充，sm4 是 16 个字节一个分组，所以统一走到 pkcs#7\r\n  if ((padding === 'pkcs#5' || padding === 'pkcs#7') && cryptFlag === DECRYPT) {\r\n    const len = outArray.length\r\n    const paddingCount = outArray[len - 1]\r\n    for (let i = 1; i <= paddingCount; i++) {\r\n      if (outArray[len - i] !== paddingCount) throw new Error('padding is invalid')\r\n    }\r\n    outArray.splice(len - paddingCount, paddingCount)\r\n  }\r\n\r\n  // 调整输出\r\n  if (output !== 'array') {\r\n    if (cryptFlag !== DECRYPT) {\r\n      // 加密，输出转 16 进制串\r\n      return ArrayToHex(outArray)\r\n    } else {\r\n      // 解密，输出转 utf8 串\r\n      return arrayToUtf8(outArray)\r\n    }\r\n  } else {\r\n    return outArray\r\n  }\r\n}\r\n\r\nmodule.exports = {\r\n  encrypt(inArray, key, options) {\r\n    return sm4(inArray, key, 1, options)\r\n  },\r\n  decrypt(inArray, key, options) {\r\n    return sm4(inArray, key, 0, options)\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA;AACA,MAAMA,OAAO,GAAG,CAAC;AACjB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAEhB,MAAMC,IAAI,GAAG,CACX,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9F,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAC/F;AAED,MAAMC,EAAE,GAAG,CACT,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAC/C;;AAED;AACA;AACA;AACA,SAASC,UAAUA,CAACC,GAAG,EAAE;EACvB,MAAMC,GAAG,GAAG,EAAE;EACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGH,GAAG,CAACI,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,IAAI,CAAC,EAAE;IACjDD,GAAG,CAACI,IAAI,CAACC,QAAQ,CAACN,GAAG,CAACO,MAAM,CAACL,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1C;EACA,OAAOD,GAAG;AACZ;;AAEA;AACA;AACA;AACA,SAASO,UAAUA,CAACP,GAAG,EAAE;EACvB,OAAOA,GAAG,CAACQ,GAAG,CAACC,IAAI,IAAI;IACrBA,IAAI,GAAGA,IAAI,CAACC,QAAQ,CAAC,EAAE,CAAC;IACxB,OAAOD,IAAI,CAACN,MAAM,KAAK,CAAC,GAAG,GAAG,GAAGM,IAAI,GAAGA,IAAI;EAC9C,CAAC,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;AACb;;AAEA;AACA;AACA;AACA,SAASC,WAAWA,CAACb,GAAG,EAAE;EACxB,MAAMC,GAAG,GAAG,EAAE;EAEd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGH,GAAG,CAACI,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC9C,MAAMY,KAAK,GAAGd,GAAG,CAACe,WAAW,CAACb,CAAC,CAAC;IAEhC,IAAIY,KAAK,IAAI,MAAM,EAAE;MACnB;MACAb,GAAG,CAACI,IAAI,CAACS,KAAK,CAAC;IACjB,CAAC,MAAM,IAAIA,KAAK,IAAI,MAAM,EAAE;MAC1B;MACAb,GAAG,CAACI,IAAI,CAAC,IAAI,GAAIS,KAAK,KAAK,CAAE,CAAC,EAAC;MAC/Bb,GAAG,CAACI,IAAI,CAAC,IAAI,GAAIS,KAAK,GAAG,IAAK,CAAC,EAAC;IAClC,CAAC,MAAM,IAAIA,KAAK,IAAI,MAAM,IAAKA,KAAK,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAO,EAAE;MAClE;MACAb,GAAG,CAACI,IAAI,CAAC,IAAI,GAAIS,KAAK,KAAK,EAAG,CAAC,EAAC;MAChCb,GAAG,CAACI,IAAI,CAAC,IAAI,GAAKS,KAAK,KAAK,CAAC,GAAI,IAAK,CAAC,EAAC;MACxCb,GAAG,CAACI,IAAI,CAAC,IAAI,GAAIS,KAAK,GAAG,IAAK,CAAC,EAAC;IAClC,CAAC,MAAM,IAAIA,KAAK,IAAI,QAAQ,IAAIA,KAAK,IAAI,QAAQ,EAAE;MACjD;MACAZ,CAAC,EAAE;MACHD,GAAG,CAACI,IAAI,CAAE,IAAI,GAAIS,KAAK,KAAK,EAAE,GAAI,IAAK,CAAC,EAAC;MACzCb,GAAG,CAACI,IAAI,CAAE,IAAI,GAAKS,KAAK,KAAK,EAAE,GAAI,IAAM,CAAC,EAAC;MAC3Cb,GAAG,CAACI,IAAI,CAAE,IAAI,GAAKS,KAAK,KAAK,CAAC,GAAI,IAAM,CAAC,EAAC;MAC1Cb,GAAG,CAACI,IAAI,CAAE,IAAI,GAAIS,KAAK,GAAG,IAAM,CAAC,EAAC;IACpC,CAAC,MAAM;MACL;MACAb,GAAG,CAACI,IAAI,CAACS,KAAK,CAAC;MACf,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;IAC3C;EACF;EAEA,OAAOf,GAAG;AACZ;;AAEA;AACA;AACA;AACA,SAASgB,WAAWA,CAAChB,GAAG,EAAE;EACxB,MAAMD,GAAG,GAAG,EAAE;EACd,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGF,GAAG,CAACG,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAID,GAAG,CAACC,CAAC,CAAC,IAAI,IAAI,IAAID,GAAG,CAACC,CAAC,CAAC,IAAI,IAAI,EAAE;MACpC;MACAF,GAAG,CAACK,IAAI,CAACa,MAAM,CAACC,aAAa,CAAC,CAAC,CAAClB,GAAG,CAACC,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,KAAK,CAACD,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,CAAC,IAAI,CAACD,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,IAAID,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;MACxIA,CAAC,IAAI,CAAC;IACR,CAAC,MAAM,IAAID,GAAG,CAACC,CAAC,CAAC,IAAI,IAAI,IAAID,GAAG,CAACC,CAAC,CAAC,IAAI,IAAI,EAAE;MAC3C;MACAF,GAAG,CAACK,IAAI,CAACa,MAAM,CAACC,aAAa,CAAC,CAAC,CAAClB,GAAG,CAACC,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,KAAK,CAACD,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,IAAID,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;MAC1GA,CAAC,IAAI,CAAC;IACR,CAAC,MAAM,IAAID,GAAG,CAACC,CAAC,CAAC,IAAI,IAAI,IAAID,GAAG,CAACC,CAAC,CAAC,IAAI,IAAI,EAAE;MAC3C;MACAF,GAAG,CAACK,IAAI,CAACa,MAAM,CAACC,aAAa,CAAC,CAAC,CAAClB,GAAG,CAACC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,KAAKD,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;MAC5EA,CAAC,EAAE;IACL,CAAC,MAAM;MACL;MACAF,GAAG,CAACK,IAAI,CAACa,MAAM,CAACC,aAAa,CAAClB,GAAG,CAACC,CAAC,CAAC,CAAC,CAAC;IACxC;EACF;EAEA,OAAOF,GAAG,CAACY,IAAI,CAAC,EAAE,CAAC;AACrB;;AAEA;AACA;AACA;AACA,SAASQ,IAAIA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAClB,MAAMC,CAAC,GAAGD,CAAC,GAAG,EAAE;EAChB,OAAQD,CAAC,IAAIE,CAAC,GAAKF,CAAC,KAAM,EAAE,GAAGE,CAAG;AACpC;;AAEA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,CAAC,EAAE;EAClB,OAAO,CAAC5B,IAAI,CAAC4B,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,EAAE,GACzC,CAAC5B,IAAI,CAAC4B,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,EAAE,GACpC,CAAC5B,IAAI,CAAC4B,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,GACjC5B,IAAI,CAAC4B,CAAC,GAAG,IAAI,CAAC,GAAG,IAAK;AAC3B;;AAEA;AACA;AACA;AACA,SAASC,EAAEA,CAACC,CAAC,EAAE;EACb,OAAOA,CAAC,GAAGP,IAAI,CAACO,CAAC,EAAE,CAAC,CAAC,GAAGP,IAAI,CAACO,CAAC,EAAE,EAAE,CAAC,GAAGP,IAAI,CAACO,CAAC,EAAE,EAAE,CAAC,GAAGP,IAAI,CAACO,CAAC,EAAE,EAAE,CAAC;AACjE;;AAEA;AACA;AACA;AACA,SAASC,EAAEA,CAACD,CAAC,EAAE;EACb,OAAOA,CAAC,GAAGP,IAAI,CAACO,CAAC,EAAE,EAAE,CAAC,GAAGP,IAAI,CAACO,CAAC,EAAE,EAAE,CAAC;AACtC;;AAEA;AACA;AACA;AACA,SAASE,SAASA,CAACC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAC1C,MAAMX,CAAC,GAAG,IAAIY,KAAK,CAAC,CAAC,CAAC;;EAEtB;EACA,MAAMC,GAAG,GAAG,IAAID,KAAK,CAAC,CAAC,CAAC;EACxB,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1BgC,GAAG,CAAC,CAAC,CAAC,GAAGJ,KAAK,CAAC,CAAC,GAAG5B,CAAC,CAAC,GAAG,IAAI;IAC5BgC,GAAG,CAAC,CAAC,CAAC,GAAGJ,KAAK,CAAC,CAAC,GAAG5B,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;IAChCgC,GAAG,CAAC,CAAC,CAAC,GAAGJ,KAAK,CAAC,CAAC,GAAG5B,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;IAChCgC,GAAG,CAAC,CAAC,CAAC,GAAGJ,KAAK,CAAC,CAAC,GAAG5B,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;IAChCmB,CAAC,CAACnB,CAAC,CAAC,GAAGgC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAGA,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAGA,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC;EAC3D;;EAEA;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,EAAED,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAE;IACnCC,GAAG,GAAGf,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGW,QAAQ,CAACG,CAAC,GAAG,CAAC,CAAC;IAC1Cd,CAAC,CAAC,CAAC,CAAC,IAAIK,EAAE,CAACF,OAAO,CAACY,GAAG,CAAC,CAAC,EAAC;;IAEzBA,GAAG,GAAGf,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGW,QAAQ,CAACG,CAAC,GAAG,CAAC,CAAC;IAC1Cd,CAAC,CAAC,CAAC,CAAC,IAAIK,EAAE,CAACF,OAAO,CAACY,GAAG,CAAC,CAAC,EAAC;;IAEzBA,GAAG,GAAGf,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGW,QAAQ,CAACG,CAAC,GAAG,CAAC,CAAC;IAC1Cd,CAAC,CAAC,CAAC,CAAC,IAAIK,EAAE,CAACF,OAAO,CAACY,GAAG,CAAC,CAAC,EAAC;;IAEzBA,GAAG,GAAGf,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGW,QAAQ,CAACG,CAAC,GAAG,CAAC,CAAC;IAC1Cd,CAAC,CAAC,CAAC,CAAC,IAAIK,EAAE,CAACF,OAAO,CAACY,GAAG,CAAC,CAAC,EAAC;EAC3B;;EAEA;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAE;IAC9BN,MAAM,CAACM,CAAC,CAAC,GAAGhB,CAAC,CAAC,CAAC,GAAGgB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,IAAI;IACtCN,MAAM,CAACM,CAAC,GAAG,CAAC,CAAC,GAAGhB,CAAC,CAAC,CAAC,GAAGgB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,IAAI;IAC1CN,MAAM,CAACM,CAAC,GAAG,CAAC,CAAC,GAAGhB,CAAC,CAAC,CAAC,GAAGgB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI;IACzCN,MAAM,CAACM,CAAC,GAAG,CAAC,CAAC,GAAGhB,CAAC,CAAC,CAAC,GAAGgB,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;EACrC;AACF;;AAEA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,GAAG,EAAEP,QAAQ,EAAEQ,SAAS,EAAE;EAC5C,MAAMnB,CAAC,GAAG,IAAIY,KAAK,CAAC,CAAC,CAAC;;EAEtB;EACA,MAAMC,GAAG,GAAG,IAAID,KAAK,CAAC,CAAC,CAAC;EACxB,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1BgC,GAAG,CAAC,CAAC,CAAC,GAAGK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGrC,CAAC,CAAC,GAAG,IAAI;IAC9BgC,GAAG,CAAC,CAAC,CAAC,GAAGK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGrC,CAAC,CAAC,GAAG,IAAI;IAC9BgC,GAAG,CAAC,CAAC,CAAC,GAAGK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGrC,CAAC,CAAC,GAAG,IAAI;IAC9BgC,GAAG,CAAC,CAAC,CAAC,GAAGK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGrC,CAAC,CAAC,GAAG,IAAI;IAC9BmB,CAAC,CAACnB,CAAC,CAAC,GAAGgC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAGA,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAGA,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC;EAC3D;;EAEA;EACAb,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU;EAClBA,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU;EAClBA,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU;EAClBA,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU;;EAElB;EACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEC,GAAG,EAAED,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAE;IACnCC,GAAG,GAAGf,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGvB,EAAE,CAACqC,CAAC,GAAG,CAAC,CAAC;IACpCH,QAAQ,CAACG,CAAC,GAAG,CAAC,CAAC,GAAGd,CAAC,CAAC,CAAC,CAAC,IAAIO,EAAE,CAACJ,OAAO,CAACY,GAAG,CAAC,CAAC,EAAC;;IAE3CA,GAAG,GAAGf,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGvB,EAAE,CAACqC,CAAC,GAAG,CAAC,CAAC;IACpCH,QAAQ,CAACG,CAAC,GAAG,CAAC,CAAC,GAAGd,CAAC,CAAC,CAAC,CAAC,IAAIO,EAAE,CAACJ,OAAO,CAACY,GAAG,CAAC,CAAC,EAAC;;IAE3CA,GAAG,GAAGf,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGvB,EAAE,CAACqC,CAAC,GAAG,CAAC,CAAC;IACpCH,QAAQ,CAACG,CAAC,GAAG,CAAC,CAAC,GAAGd,CAAC,CAAC,CAAC,CAAC,IAAIO,EAAE,CAACJ,OAAO,CAACY,GAAG,CAAC,CAAC,EAAC;;IAE3CA,GAAG,GAAGf,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGvB,EAAE,CAACqC,CAAC,GAAG,CAAC,CAAC;IACpCH,QAAQ,CAACG,CAAC,GAAG,CAAC,CAAC,GAAGd,CAAC,CAAC,CAAC,CAAC,IAAIO,EAAE,CAACJ,OAAO,CAACY,GAAG,CAAC,CAAC,EAAC;EAC7C;;EAEA;EACA,IAAII,SAAS,KAAK9C,OAAO,EAAE;IACzB,KAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEC,GAAG,EAAED,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAChCC,GAAG,GAAGJ,QAAQ,CAACG,CAAC,CAAC;MACjBH,QAAQ,CAACG,CAAC,CAAC,GAAGH,QAAQ,CAAC,EAAE,GAAGG,CAAC,CAAC;MAC9BH,QAAQ,CAAC,EAAE,GAAGG,CAAC,CAAC,GAAGC,GAAG;IACxB;EACF;AACF;AAEA,SAASK,GAAGA,CAACC,OAAO,EAAEH,GAAG,EAAEC,SAAS,EAAE;EACpCG,OAAO,GAAG,QAAQ;EAAEC,IAAI;EAAEC,EAAE,GAAG,EAAE;EAAEd,MAAM,GAAG;AAC9C,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,IAAIa,IAAI,KAAK,KAAK,EAAE;IAClB;IACA,IAAI,OAAOC,EAAE,KAAK,QAAQ,EAAEA,EAAE,GAAG9C,UAAU,CAAC8C,EAAE,CAAC;IAC/C,IAAIA,EAAE,CAACzC,MAAM,KAAM,GAAG,GAAG,CAAE,EAAE;MAC3B;MACA,MAAM,IAAIY,KAAK,CAAC,eAAe,CAAC;IAClC;EACF;;EAEA;EACA,IAAI,OAAOuB,GAAG,KAAK,QAAQ,EAAEA,GAAG,GAAGxC,UAAU,CAACwC,GAAG,CAAC;EAClD,IAAIA,GAAG,CAACnC,MAAM,KAAM,GAAG,GAAG,CAAE,EAAE;IAC5B;IACA,MAAM,IAAIY,KAAK,CAAC,gBAAgB,CAAC;EACnC;;EAEA;EACA,IAAI,OAAO0B,OAAO,KAAK,QAAQ,EAAE;IAC/B,IAAIF,SAAS,KAAK9C,OAAO,EAAE;MACzB;MACAgD,OAAO,GAAG7B,WAAW,CAAC6B,OAAO,CAAC;IAChC,CAAC,MAAM;MACL;MACAA,OAAO,GAAG3C,UAAU,CAAC2C,OAAO,CAAC;IAC/B;EACF,CAAC,MAAM;IACLA,OAAO,GAAG,CAAC,GAAGA,OAAO,CAAC;EACxB;;EAEA;EACA,IAAI,CAACC,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,KAAKH,SAAS,KAAK9C,OAAO,EAAE;IAC3E,MAAMoD,YAAY,GAAGlD,KAAK,GAAG8C,OAAO,CAACtC,MAAM,GAAGR,KAAK;IACnD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4C,YAAY,EAAE5C,CAAC,EAAE,EAAEwC,OAAO,CAACrC,IAAI,CAACyC,YAAY,CAAC;EACnE;;EAEA;EACA,MAAMd,QAAQ,GAAG,IAAIC,KAAK,CAACtC,KAAK,CAAC;EACjC2C,UAAU,CAACC,GAAG,EAAEP,QAAQ,EAAEQ,SAAS,CAAC;EAEpC,MAAMO,QAAQ,GAAG,EAAE;EACnB,IAAIC,UAAU,GAAGH,EAAE;EACnB,IAAII,OAAO,GAAGP,OAAO,CAACtC,MAAM;EAC5B,IAAIU,KAAK,GAAG,CAAC;EACb,OAAOmC,OAAO,IAAIrD,KAAK,EAAE;IACvB,MAAMkC,KAAK,GAAGY,OAAO,CAACQ,KAAK,CAACpC,KAAK,EAAEA,KAAK,GAAG,EAAE,CAAC;IAC9C,MAAMiB,MAAM,GAAG,IAAIE,KAAK,CAAC,EAAE,CAAC;IAE5B,IAAIW,IAAI,KAAK,KAAK,EAAE;MAClB,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,EAAEM,CAAC,EAAE,EAAE;QAC9B,IAAIsC,SAAS,KAAK9C,OAAO,EAAE;UACzB;UACAoC,KAAK,CAAC5B,CAAC,CAAC,IAAI8C,UAAU,CAAC9C,CAAC,CAAC;QAC3B;MACF;IACF;IAEA2B,SAAS,CAACC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,CAAC;IAGlC,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,EAAEM,CAAC,EAAE,EAAE;MAC9B,IAAI0C,IAAI,KAAK,KAAK,EAAE;QAClB,IAAIJ,SAAS,KAAK9C,OAAO,EAAE;UACzB;UACAqC,MAAM,CAAC7B,CAAC,CAAC,IAAI8C,UAAU,CAAC9C,CAAC,CAAC;QAC5B;MACF;MAEA6C,QAAQ,CAACjC,KAAK,GAAGZ,CAAC,CAAC,GAAG6B,MAAM,CAAC7B,CAAC,CAAC;IACjC;IAEA,IAAI0C,IAAI,KAAK,KAAK,EAAE;MAClB,IAAIJ,SAAS,KAAK9C,OAAO,EAAE;QACzB;QACAsD,UAAU,GAAGjB,MAAM;MACrB,CAAC,MAAM;QACL;QACAiB,UAAU,GAAGlB,KAAK;MACpB;IACF;IAEAmB,OAAO,IAAIrD,KAAK;IAChBkB,KAAK,IAAIlB,KAAK;EAChB;;EAEA;EACA,IAAI,CAAC+C,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,KAAKH,SAAS,KAAK9C,OAAO,EAAE;IAC3E,MAAMS,GAAG,GAAG4C,QAAQ,CAAC3C,MAAM;IAC3B,MAAM0C,YAAY,GAAGC,QAAQ,CAAC5C,GAAG,GAAG,CAAC,CAAC;IACtC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI4C,YAAY,EAAE5C,CAAC,EAAE,EAAE;MACtC,IAAI6C,QAAQ,CAAC5C,GAAG,GAAGD,CAAC,CAAC,KAAK4C,YAAY,EAAE,MAAM,IAAI9B,KAAK,CAAC,oBAAoB,CAAC;IAC/E;IACA+B,QAAQ,CAACI,MAAM,CAAChD,GAAG,GAAG2C,YAAY,EAAEA,YAAY,CAAC;EACnD;;EAEA;EACA,IAAIf,MAAM,KAAK,OAAO,EAAE;IACtB,IAAIS,SAAS,KAAK9C,OAAO,EAAE;MACzB;MACA,OAAOc,UAAU,CAACuC,QAAQ,CAAC;IAC7B,CAAC,MAAM;MACL;MACA,OAAO9B,WAAW,CAAC8B,QAAQ,CAAC;IAC9B;EACF,CAAC,MAAM;IACL,OAAOA,QAAQ;EACjB;AACF;AAEAK,MAAM,CAACC,OAAO,GAAG;EACfC,OAAOA,CAACZ,OAAO,EAAEH,GAAG,EAAEgB,OAAO,EAAE;IAC7B,OAAOd,GAAG,CAACC,OAAO,EAAEH,GAAG,EAAE,CAAC,EAAEgB,OAAO,CAAC;EACtC,CAAC;EACDC,OAAOA,CAACd,OAAO,EAAEH,GAAG,EAAEgB,OAAO,EAAE;IAC7B,OAAOd,GAAG,CAACC,OAAO,EAAEH,GAAG,EAAE,CAAC,EAAEgB,OAAO,CAAC;EACtC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}