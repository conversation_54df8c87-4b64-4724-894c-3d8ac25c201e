{"ast": null, "code": "import { ref, reactive } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nimport { SM2Crypto, SM3Hasher } from \"@/utils/crypto\";\nexport default {\n  name: \"RegisterView\",\n  setup() {\n    const store = useStore();\n    const formRef = ref(null);\n    const currentStep = ref(0);\n    const loading = ref(false);\n    const agreement = ref(false);\n\n    // 注册表单\n    const form = reactive({\n      studentId: \"\",\n      password: \"\",\n      confirmPassword: \"\",\n      email: \"\",\n      phone: \"\"\n    });\n\n    // SM2密钥对\n    const keyPair = reactive({\n      publicKey: \"\",\n      privateKey: \"\"\n    });\n\n    // 表单验证规则\n    const validatePass = (rule, value, callback) => {\n      if (value === \"\") {\n        callback(new Error(\"请输入密码\"));\n      } else if (value.length < 6) {\n        callback(new Error(\"密码长度不能小于6位\"));\n      } else {\n        if (form.confirmPassword !== \"\") {\n          formRef.value.validateField(\"confirmPassword\");\n        }\n        callback();\n      }\n    };\n    const validatePass2 = (rule, value, callback) => {\n      if (value === \"\") {\n        callback(new Error(\"请再次输入密码\"));\n      } else if (value !== form.password) {\n        callback(new Error(\"两次输入密码不一致\"));\n      } else {\n        callback();\n      }\n    };\n    const rules = {\n      studentId: [{\n        required: true,\n        message: \"请输入学号\",\n        trigger: \"blur\"\n      }, {\n        min: 5,\n        max: 20,\n        message: \"学号长度应为5-20个字符\",\n        trigger: \"blur\"\n      }],\n      password: [{\n        validator: validatePass,\n        trigger: \"blur\"\n      }],\n      confirmPassword: [{\n        validator: validatePass2,\n        trigger: \"blur\"\n      }],\n      email: [{\n        required: true,\n        message: \"请输入邮箱\",\n        trigger: \"blur\"\n      }, {\n        type: \"email\",\n        message: \"请输入正确的邮箱格式\",\n        trigger: \"blur\"\n      }],\n      phone: [{\n        required: true,\n        message: \"请输入手机号\",\n        trigger: \"blur\"\n      }, {\n        pattern: /^1[3-9]\\d{9}$/,\n        message: \"请输入正确的手机号格式\",\n        trigger: \"blur\"\n      }]\n    };\n\n    // 生成SM2密钥对\n    const generateKeyPair = () => {\n      const newKeyPair = SM2Crypto.generateKeyPair();\n      keyPair.publicKey = newKeyPair.publicKey;\n      keyPair.privateKey = newKeyPair.privateKey;\n    };\n\n    // 重新生成SM2密钥对\n    const regenerateKeyPair = () => {\n      ElMessageBox.confirm(\"重新生成密钥对将覆盖当前密钥，确定要继续吗？\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(() => {\n        generateKeyPair();\n        ElMessage.success(\"密钥对已重新生成\");\n      }).catch(() => {\n        // 用户取消操作\n      });\n    };\n\n    // 复制公钥\n    const copyPublicKey = () => {\n      navigator.clipboard.writeText(keyPair.publicKey).then(() => {\n        ElMessage.success(\"公钥已复制到剪贴板\");\n      }).catch(() => {\n        ElMessage.error(\"复制失败，请手动复制\");\n      });\n    };\n\n    // 复制私钥\n    const copyPrivateKey = () => {\n      navigator.clipboard.writeText(keyPair.privateKey).then(() => {\n        ElMessage.success(\"私钥已复制到剪贴板\");\n      }).catch(() => {\n        ElMessage.error(\"复制失败，请手动复制\");\n      });\n    };\n\n    // 下一步\n    const nextStep = async () => {\n      if (currentStep.value === 0) {\n        if (!formRef.value) return;\n        await formRef.value.validate(async valid => {\n          if (valid) {\n            currentStep.value++;\n\n            // 如果还没有生成密钥对，则生成\n            if (!keyPair.publicKey || !keyPair.privateKey) {\n              generateKeyPair();\n            }\n          }\n        });\n      } else {\n        currentStep.value++;\n      }\n    };\n\n    // 上一步\n    const prevStep = () => {\n      currentStep.value--;\n    };\n\n    // 显示用户协议\n    const showTerms = () => {\n      ElMessageBox.alert(\"这里是用户协议内容...\", \"用户协议\", {\n        confirmButtonText: \"确定\"\n      });\n    };\n\n    // 显示隐私政策\n    const showPrivacy = () => {\n      ElMessageBox.alert(\"这里是隐私政策内容...\", \"隐私政策\", {\n        confirmButtonText: \"确定\"\n      });\n    };\n\n    // 提交注册\n    const handleRegister = async () => {\n      try {\n        loading.value = true;\n\n        // 对密码进行SM3哈希\n        const hashedPassword = SM3Hasher.hash(form.password);\n\n        // 调用注册接口\n        await store.dispatch(\"user/register\", {\n          studentId: form.studentId,\n          password: hashedPassword,\n          email: form.email,\n          phone: form.phone,\n          publicKey: keyPair.publicKey\n        });\n\n        // 注册成功，进入下一步\n        currentStep.value = 3;\n        ElMessage.success(\"注册成功\");\n      } catch (error) {\n        ElMessage.error(error.message || \"注册失败，请稍后重试\");\n      } finally {\n        loading.value = false;\n      }\n    };\n    return {\n      formRef,\n      currentStep,\n      loading,\n      agreement,\n      form,\n      keyPair,\n      rules,\n      nextStep,\n      prevStep,\n      generateKeyPair,\n      regenerateKeyPair,\n      copyPublicKey,\n      copyPrivateKey,\n      showTerms,\n      showPrivacy,\n      handleRegister\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "useStore", "ElMessage", "ElMessageBox", "SM2Crypto", "SM3Hasher", "name", "setup", "store", "formRef", "currentStep", "loading", "agreement", "form", "studentId", "password", "confirmPassword", "email", "phone", "keyPair", "public<PERSON>ey", "privateKey", "validatePass", "rule", "value", "callback", "Error", "length", "validateField", "validatePass2", "rules", "required", "message", "trigger", "min", "max", "validator", "type", "pattern", "generateKeyPair", "newKeyPair", "regenerateKeyPair", "confirm", "confirmButtonText", "cancelButtonText", "then", "success", "catch", "copyPublicKey", "navigator", "clipboard", "writeText", "error", "copyPrivateKey", "nextStep", "validate", "valid", "prevStep", "showTerms", "alert", "showPrivacy", "handleRegister", "hashedPassword", "hash", "dispatch"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Register.vue"], "sourcesContent": ["<template>\n  <div class=\"register-container\">\n    <div class=\"register-card\">\n      <div class=\"register-header\">\n        <img src=\"@/assets/logo.png\" alt=\"Logo\" class=\"register-logo\" />\n        <h2>用户注册</h2>\n      </div>\n\n      <el-steps :active=\"currentStep\" finish-status=\"success\" simple>\n        <el-step title=\"填写信息\" />\n        <el-step title=\"生成密钥\" />\n        <el-step title=\"完成注册\" />\n      </el-steps>\n\n      <!-- 步骤1：填写基本信息 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" label-position=\"top\">\n          <el-form-item label=\"学号\" prop=\"studentId\">\n            <el-input v-model=\"form.studentId\" placeholder=\"请输入学号\" />\n          </el-form-item>\n\n          <el-form-item label=\"密码\" prop=\"password\">\n            <el-input\n              v-model=\"form.password\"\n              type=\"password\"\n              placeholder=\"请输入密码\"\n              show-password\n            />\n          </el-form-item>\n\n          <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\n            <el-input\n              v-model=\"form.confirmPassword\"\n              type=\"password\"\n              placeholder=\"请再次输入密码\"\n              show-password\n            />\n          </el-form-item>\n\n          <el-form-item label=\"邮箱\" prop=\"email\">\n            <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" />\n          </el-form-item>\n\n          <el-form-item label=\"手机号\" prop=\"phone\">\n            <el-input v-model=\"form.phone\" placeholder=\"请输入手机号\" />\n          </el-form-item>\n\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"nextStep\">下一步</el-button>\n            <el-button @click=\"$router.push('/login')\">返回登录</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <!-- 步骤2：生成SM2密钥对 -->\n      <div v-else-if=\"currentStep === 1\" class=\"step-content\">\n        <div class=\"key-generation\">\n          <h3>SM2密钥对生成</h3>\n          <p class=\"description\">\n            SM2密钥对用于安全登录和数据签名，请妥善保管您的私钥，公钥将上传至服务器。\n          </p>\n\n          <div class=\"key-box\">\n            <div class=\"key-label\">\n              <span>公钥</span>\n              <el-button type=\"primary\" size=\"small\" @click=\"copyPublicKey\"> 复制 </el-button>\n            </div>\n            <el-input v-model=\"keyPair.publicKey\" type=\"textarea\" :rows=\"3\" readonly />\n          </div>\n\n          <div class=\"key-box\">\n            <div class=\"key-label\">\n              <span>私钥</span>\n              <el-button type=\"primary\" size=\"small\" @click=\"copyPrivateKey\"> 复制 </el-button>\n            </div>\n            <el-input\n              v-model=\"keyPair.privateKey\"\n              type=\"textarea\"\n              :rows=\"3\"\n              readonly\n              show-password\n            />\n          </div>\n\n          <div class=\"key-actions\">\n            <el-button type=\"primary\" @click=\"regenerateKeyPair\"> 重新生成 </el-button>\n            <el-button type=\"success\" @click=\"nextStep\"> 下一步 </el-button>\n            <el-button @click=\"prevStep\"> 上一步 </el-button>\n          </div>\n\n          <el-alert\n            title=\"重要提示\"\n            type=\"warning\"\n            description=\"请务必保存您的私钥，私钥将不会存储在服务器上，丢失后无法找回。\"\n            show-icon\n            :closable=\"false\"\n            class=\"key-warning\"\n          />\n        </div>\n      </div>\n\n      <!-- 步骤3：完成注册 -->\n      <div v-else-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"register-confirm\">\n          <h3>确认注册信息</h3>\n\n          <el-descriptions :column=\"1\" border>\n            <el-descriptions-item label=\"学号\">{{ form.studentId }}</el-descriptions-item>\n            <el-descriptions-item label=\"邮箱\">{{ form.email }}</el-descriptions-item>\n            <el-descriptions-item label=\"手机号\">{{ form.phone }}</el-descriptions-item>\n            <el-descriptions-item label=\"是否使用SM2密钥\">是</el-descriptions-item>\n          </el-descriptions>\n\n          <div class=\"confirm-actions\">\n            <el-checkbox v-model=\"agreement\">\n              我已阅读并同意<a href=\"#\" @click.prevent=\"showTerms\">《用户协议》</a>和<a\n                href=\"#\"\n                @click.prevent=\"showPrivacy\"\n                >《隐私政策》</a\n              >\n            </el-checkbox>\n\n            <div class=\"button-group\">\n              <el-button\n                type=\"primary\"\n                :loading=\"loading\"\n                :disabled=\"!agreement\"\n                @click=\"handleRegister\"\n              >\n                提交注册\n              </el-button>\n              <el-button @click=\"prevStep\"> 上一步 </el-button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 步骤4：注册成功 -->\n      <div v-else-if=\"currentStep === 3\" class=\"step-content\">\n        <div class=\"register-success\">\n          <el-result\n            icon=\"success\"\n            title=\"注册成功\"\n            sub-title=\"您已成功注册图书馆自习室管理系统账号\"\n          >\n            <template #extra>\n              <el-button type=\"primary\" @click=\"$router.push('/login')\"> 前往登录 </el-button>\n            </template>\n          </el-result>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive } from \"vue\";\nimport { useStore } from \"vuex\";\n\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nimport { SM2Crypto, SM3Hasher } from \"@/utils/crypto\";\n\nexport default {\n  name: \"RegisterView\",\n  setup() {\n    const store = useStore();\n\n    const formRef = ref(null);\n    const currentStep = ref(0);\n    const loading = ref(false);\n    const agreement = ref(false);\n\n    // 注册表单\n    const form = reactive({\n      studentId: \"\",\n      password: \"\",\n      confirmPassword: \"\",\n      email: \"\",\n      phone: \"\",\n    });\n\n    // SM2密钥对\n    const keyPair = reactive({\n      publicKey: \"\",\n      privateKey: \"\",\n    });\n\n    // 表单验证规则\n    const validatePass = (rule, value, callback) => {\n      if (value === \"\") {\n        callback(new Error(\"请输入密码\"));\n      } else if (value.length < 6) {\n        callback(new Error(\"密码长度不能小于6位\"));\n      } else {\n        if (form.confirmPassword !== \"\") {\n          formRef.value.validateField(\"confirmPassword\");\n        }\n        callback();\n      }\n    };\n\n    const validatePass2 = (rule, value, callback) => {\n      if (value === \"\") {\n        callback(new Error(\"请再次输入密码\"));\n      } else if (value !== form.password) {\n        callback(new Error(\"两次输入密码不一致\"));\n      } else {\n        callback();\n      }\n    };\n\n    const rules = {\n      studentId: [\n        { required: true, message: \"请输入学号\", trigger: \"blur\" },\n        { min: 5, max: 20, message: \"学号长度应为5-20个字符\", trigger: \"blur\" },\n      ],\n      password: [{ validator: validatePass, trigger: \"blur\" }],\n      confirmPassword: [{ validator: validatePass2, trigger: \"blur\" }],\n      email: [\n        { required: true, message: \"请输入邮箱\", trigger: \"blur\" },\n        { type: \"email\", message: \"请输入正确的邮箱格式\", trigger: \"blur\" },\n      ],\n      phone: [\n        { required: true, message: \"请输入手机号\", trigger: \"blur\" },\n        {\n          pattern: /^1[3-9]\\d{9}$/,\n          message: \"请输入正确的手机号格式\",\n          trigger: \"blur\",\n        },\n      ],\n    };\n\n    // 生成SM2密钥对\n    const generateKeyPair = () => {\n      const newKeyPair = SM2Crypto.generateKeyPair();\n      keyPair.publicKey = newKeyPair.publicKey;\n      keyPair.privateKey = newKeyPair.privateKey;\n    };\n\n    // 重新生成SM2密钥对\n    const regenerateKeyPair = () => {\n      ElMessageBox.confirm(\"重新生成密钥对将覆盖当前密钥，确定要继续吗？\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(() => {\n          generateKeyPair();\n          ElMessage.success(\"密钥对已重新生成\");\n        })\n        .catch(() => {\n          // 用户取消操作\n        });\n    };\n\n    // 复制公钥\n    const copyPublicKey = () => {\n      navigator.clipboard\n        .writeText(keyPair.publicKey)\n        .then(() => {\n          ElMessage.success(\"公钥已复制到剪贴板\");\n        })\n        .catch(() => {\n          ElMessage.error(\"复制失败，请手动复制\");\n        });\n    };\n\n    // 复制私钥\n    const copyPrivateKey = () => {\n      navigator.clipboard\n        .writeText(keyPair.privateKey)\n        .then(() => {\n          ElMessage.success(\"私钥已复制到剪贴板\");\n        })\n        .catch(() => {\n          ElMessage.error(\"复制失败，请手动复制\");\n        });\n    };\n\n    // 下一步\n    const nextStep = async () => {\n      if (currentStep.value === 0) {\n        if (!formRef.value) return;\n\n        await formRef.value.validate(async valid => {\n          if (valid) {\n            currentStep.value++;\n\n            // 如果还没有生成密钥对，则生成\n            if (!keyPair.publicKey || !keyPair.privateKey) {\n              generateKeyPair();\n            }\n          }\n        });\n      } else {\n        currentStep.value++;\n      }\n    };\n\n    // 上一步\n    const prevStep = () => {\n      currentStep.value--;\n    };\n\n    // 显示用户协议\n    const showTerms = () => {\n      ElMessageBox.alert(\"这里是用户协议内容...\", \"用户协议\", {\n        confirmButtonText: \"确定\",\n      });\n    };\n\n    // 显示隐私政策\n    const showPrivacy = () => {\n      ElMessageBox.alert(\"这里是隐私政策内容...\", \"隐私政策\", {\n        confirmButtonText: \"确定\",\n      });\n    };\n\n    // 提交注册\n    const handleRegister = async () => {\n      try {\n        loading.value = true;\n\n        // 对密码进行SM3哈希\n        const hashedPassword = SM3Hasher.hash(form.password);\n\n        // 调用注册接口\n        await store.dispatch(\"user/register\", {\n          studentId: form.studentId,\n          password: hashedPassword,\n          email: form.email,\n          phone: form.phone,\n          publicKey: keyPair.publicKey,\n        });\n\n        // 注册成功，进入下一步\n        currentStep.value = 3;\n        ElMessage.success(\"注册成功\");\n      } catch (error) {\n        ElMessage.error(error.message || \"注册失败，请稍后重试\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    return {\n      formRef,\n      currentStep,\n      loading,\n      agreement,\n      form,\n      keyPair,\n      rules,\n      nextStep,\n      prevStep,\n      generateKeyPair,\n      regenerateKeyPair,\n      copyPublicKey,\n      copyPrivateKey,\n      showTerms,\n      showPrivacy,\n      handleRegister,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.register-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding: 20px;\n}\n\n.register-card {\n  width: 500px;\n  padding: 30px;\n  background-color: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.register-header {\n  text-align: center;\n  margin-bottom: 30px;\n\n  .register-logo {\n    width: 80px;\n    height: 80px;\n    margin-bottom: 15px;\n  }\n\n  h2 {\n    font-size: 24px;\n    color: #303133;\n    margin: 0;\n  }\n}\n\n.step-content {\n  margin-top: 30px;\n}\n\n.key-generation {\n  .description {\n    margin-bottom: 20px;\n    color: #606266;\n  }\n\n  .key-box {\n    margin-bottom: 20px;\n\n    .key-label {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 5px;\n      font-weight: bold;\n    }\n  }\n\n  .key-actions {\n    display: flex;\n    gap: 10px;\n    margin-bottom: 20px;\n  }\n\n  .key-warning {\n    margin-top: 20px;\n  }\n}\n\n.register-confirm {\n  .confirm-actions {\n    margin-top: 30px;\n\n    .button-group {\n      margin-top: 20px;\n      display: flex;\n      gap: 10px;\n    }\n  }\n}\n\n.register-success {\n  padding: 20px 0;\n}\n</style>\n"], "mappings": "AA4JA,SAASA,GAAG,EAAEC,QAAO,QAAS,KAAK;AACnC,SAASC,QAAO,QAAS,MAAM;AAE/B,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAc;AACtD,SAASC,SAAS,EAAEC,SAAQ,QAAS,gBAAgB;AAErD,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIP,QAAQ,CAAC,CAAC;IAExB,MAAMQ,OAAM,GAAIV,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMW,WAAU,GAAIX,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMY,OAAM,GAAIZ,GAAG,CAAC,KAAK,CAAC;IAC1B,MAAMa,SAAQ,GAAIb,GAAG,CAAC,KAAK,CAAC;;IAE5B;IACA,MAAMc,IAAG,GAAIb,QAAQ,CAAC;MACpBc,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE,EAAE;MACnBC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE;IACT,CAAC,CAAC;;IAEF;IACA,MAAMC,OAAM,GAAInB,QAAQ,CAAC;MACvBoB,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE;IACd,CAAC,CAAC;;IAEF;IACA,MAAMC,YAAW,GAAIA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,KAAK;MAC9C,IAAID,KAAI,KAAM,EAAE,EAAE;QAChBC,QAAQ,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;MAC9B,OAAO,IAAIF,KAAK,CAACG,MAAK,GAAI,CAAC,EAAE;QAC3BF,QAAQ,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;MACnC,OAAO;QACL,IAAIb,IAAI,CAACG,eAAc,KAAM,EAAE,EAAE;UAC/BP,OAAO,CAACe,KAAK,CAACI,aAAa,CAAC,iBAAiB,CAAC;QAChD;QACAH,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC;IAED,MAAMI,aAAY,GAAIA,CAACN,IAAI,EAAEC,KAAK,EAAEC,QAAQ,KAAK;MAC/C,IAAID,KAAI,KAAM,EAAE,EAAE;QAChBC,QAAQ,CAAC,IAAIC,KAAK,CAAC,SAAS,CAAC,CAAC;MAChC,OAAO,IAAIF,KAAI,KAAMX,IAAI,CAACE,QAAQ,EAAE;QAClCU,QAAQ,CAAC,IAAIC,KAAK,CAAC,WAAW,CAAC,CAAC;MAClC,OAAO;QACLD,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC;IAED,MAAMK,KAAI,GAAI;MACZhB,SAAS,EAAE,CACT;QAAEiB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,eAAe;QAAEC,OAAO,EAAE;MAAO,CAAC,CAC/D;MACDlB,QAAQ,EAAE,CAAC;QAAEqB,SAAS,EAAEd,YAAY;QAAEW,OAAO,EAAE;MAAO,CAAC,CAAC;MACxDjB,eAAe,EAAE,CAAC;QAAEoB,SAAS,EAAEP,aAAa;QAAEI,OAAO,EAAE;MAAO,CAAC,CAAC;MAChEhB,KAAK,EAAE,CACL;QAAEc,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEI,IAAI,EAAE,OAAO;QAAEL,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,CAAC,CAC1D;MACDf,KAAK,EAAE,CACL;QAAEa,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QACEK,OAAO,EAAE,eAAe;QACxBN,OAAO,EAAE,aAAa;QACtBC,OAAO,EAAE;MACX,CAAC;IAEL,CAAC;;IAED;IACA,MAAMM,eAAc,GAAIA,CAAA,KAAM;MAC5B,MAAMC,UAAS,GAAIpC,SAAS,CAACmC,eAAe,CAAC,CAAC;MAC9CpB,OAAO,CAACC,SAAQ,GAAIoB,UAAU,CAACpB,SAAS;MACxCD,OAAO,CAACE,UAAS,GAAImB,UAAU,CAACnB,UAAU;IAC5C,CAAC;;IAED;IACA,MAAMoB,iBAAgB,GAAIA,CAAA,KAAM;MAC9BtC,YAAY,CAACuC,OAAO,CAAC,wBAAwB,EAAE,IAAI,EAAE;QACnDC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBP,IAAI,EAAE;MACR,CAAC,EACEQ,IAAI,CAAC,MAAM;QACVN,eAAe,CAAC,CAAC;QACjBrC,SAAS,CAAC4C,OAAO,CAAC,UAAU,CAAC;MAC/B,CAAC,EACAC,KAAK,CAAC,MAAM;QACX;MAAA,CACD,CAAC;IACN,CAAC;;IAED;IACA,MAAMC,aAAY,GAAIA,CAAA,KAAM;MAC1BC,SAAS,CAACC,SAAQ,CACfC,SAAS,CAAChC,OAAO,CAACC,SAAS,EAC3ByB,IAAI,CAAC,MAAM;QACV3C,SAAS,CAAC4C,OAAO,CAAC,WAAW,CAAC;MAChC,CAAC,EACAC,KAAK,CAAC,MAAM;QACX7C,SAAS,CAACkD,KAAK,CAAC,YAAY,CAAC;MAC/B,CAAC,CAAC;IACN,CAAC;;IAED;IACA,MAAMC,cAAa,GAAIA,CAAA,KAAM;MAC3BJ,SAAS,CAACC,SAAQ,CACfC,SAAS,CAAChC,OAAO,CAACE,UAAU,EAC5BwB,IAAI,CAAC,MAAM;QACV3C,SAAS,CAAC4C,OAAO,CAAC,WAAW,CAAC;MAChC,CAAC,EACAC,KAAK,CAAC,MAAM;QACX7C,SAAS,CAACkD,KAAK,CAAC,YAAY,CAAC;MAC/B,CAAC,CAAC;IACN,CAAC;;IAED;IACA,MAAME,QAAO,GAAI,MAAAA,CAAA,KAAY;MAC3B,IAAI5C,WAAW,CAACc,KAAI,KAAM,CAAC,EAAE;QAC3B,IAAI,CAACf,OAAO,CAACe,KAAK,EAAE;QAEpB,MAAMf,OAAO,CAACe,KAAK,CAAC+B,QAAQ,CAAC,MAAMC,KAAI,IAAK;UAC1C,IAAIA,KAAK,EAAE;YACT9C,WAAW,CAACc,KAAK,EAAE;;YAEnB;YACA,IAAI,CAACL,OAAO,CAACC,SAAQ,IAAK,CAACD,OAAO,CAACE,UAAU,EAAE;cAC7CkB,eAAe,CAAC,CAAC;YACnB;UACF;QACF,CAAC,CAAC;MACJ,OAAO;QACL7B,WAAW,CAACc,KAAK,EAAE;MACrB;IACF,CAAC;;IAED;IACA,MAAMiC,QAAO,GAAIA,CAAA,KAAM;MACrB/C,WAAW,CAACc,KAAK,EAAE;IACrB,CAAC;;IAED;IACA,MAAMkC,SAAQ,GAAIA,CAAA,KAAM;MACtBvD,YAAY,CAACwD,KAAK,CAAC,cAAc,EAAE,MAAM,EAAE;QACzChB,iBAAiB,EAAE;MACrB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMiB,WAAU,GAAIA,CAAA,KAAM;MACxBzD,YAAY,CAACwD,KAAK,CAAC,cAAc,EAAE,MAAM,EAAE;QACzChB,iBAAiB,EAAE;MACrB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMkB,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFlD,OAAO,CAACa,KAAI,GAAI,IAAI;;QAEpB;QACA,MAAMsC,cAAa,GAAIzD,SAAS,CAAC0D,IAAI,CAAClD,IAAI,CAACE,QAAQ,CAAC;;QAEpD;QACA,MAAMP,KAAK,CAACwD,QAAQ,CAAC,eAAe,EAAE;UACpClD,SAAS,EAAED,IAAI,CAACC,SAAS;UACzBC,QAAQ,EAAE+C,cAAc;UACxB7C,KAAK,EAAEJ,IAAI,CAACI,KAAK;UACjBC,KAAK,EAAEL,IAAI,CAACK,KAAK;UACjBE,SAAS,EAAED,OAAO,CAACC;QACrB,CAAC,CAAC;;QAEF;QACAV,WAAW,CAACc,KAAI,GAAI,CAAC;QACrBtB,SAAS,CAAC4C,OAAO,CAAC,MAAM,CAAC;MAC3B,EAAE,OAAOM,KAAK,EAAE;QACdlD,SAAS,CAACkD,KAAK,CAACA,KAAK,CAACpB,OAAM,IAAK,YAAY,CAAC;MAChD,UAAU;QACRrB,OAAO,CAACa,KAAI,GAAI,KAAK;MACvB;IACF,CAAC;IAED,OAAO;MACLf,OAAO;MACPC,WAAW;MACXC,OAAO;MACPC,SAAS;MACTC,IAAI;MACJM,OAAO;MACPW,KAAK;MACLwB,QAAQ;MACRG,QAAQ;MACRlB,eAAe;MACfE,iBAAiB;MACjBO,aAAa;MACbK,cAAc;MACdK,SAAS;MACTE,WAAW;MACXC;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}