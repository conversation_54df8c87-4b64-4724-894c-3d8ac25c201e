{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, unref, createVNode, mergeProps, inject, ref, computed, onMounted, nextTick } from 'vue';\nimport { tableV2RowProps } from '../row.mjs';\nimport { TableV2InjectionKey } from '../tokens.mjs';\nimport { placeholderSign } from '../private.mjs';\nimport { isArray, isFunction } from '@vue/shared';\nimport { isNumber } from '../../../../utils/types.mjs';\nconst useTableRow = props => {\n  const {\n    isScrolling\n  } = inject(TableV2InjectionKey);\n  const measured = ref(false);\n  const rowRef = ref();\n  const measurable = computed(() => {\n    return isNumber(props.estimatedRowHeight) && props.rowIndex >= 0;\n  });\n  const doMeasure = (isInit = false) => {\n    const $rowRef = unref(rowRef);\n    if (!$rowRef) return;\n    const {\n      columns,\n      onRowHeightChange,\n      rowKey,\n      rowIndex,\n      style\n    } = props;\n    const {\n      height\n    } = $rowRef.getBoundingClientRect();\n    measured.value = true;\n    nextTick(() => {\n      if (isInit || height !== Number.parseInt(style.height)) {\n        const firstColumn = columns[0];\n        const isPlaceholder = (firstColumn == null ? void 0 : firstColumn.placeholderSign) === placeholderSign;\n        onRowHeightChange == null ? void 0 : onRowHeightChange({\n          rowKey,\n          height,\n          rowIndex\n        }, firstColumn && !isPlaceholder && firstColumn.fixed);\n      }\n    });\n  };\n  const eventHandlers = computed(() => {\n    const {\n      rowData,\n      rowIndex,\n      rowKey,\n      onRowHover\n    } = props;\n    const handlers = props.rowEventHandlers || {};\n    const eventHandlers2 = {};\n    Object.entries(handlers).forEach(([eventName, handler]) => {\n      if (isFunction(handler)) {\n        eventHandlers2[eventName] = event => {\n          handler({\n            event,\n            rowData,\n            rowIndex,\n            rowKey\n          });\n        };\n      }\n    });\n    if (onRowHover) {\n      [{\n        name: \"onMouseleave\",\n        hovered: false\n      }, {\n        name: \"onMouseenter\",\n        hovered: true\n      }].forEach(({\n        name,\n        hovered\n      }) => {\n        const existedHandler = eventHandlers2[name];\n        eventHandlers2[name] = event => {\n          onRowHover({\n            event,\n            hovered,\n            rowData,\n            rowIndex,\n            rowKey\n          });\n          existedHandler == null ? void 0 : existedHandler(event);\n        };\n      });\n    }\n    return eventHandlers2;\n  });\n  const onExpand = expanded => {\n    const {\n      onRowExpand,\n      rowData,\n      rowIndex,\n      rowKey\n    } = props;\n    onRowExpand == null ? void 0 : onRowExpand({\n      expanded,\n      rowData,\n      rowIndex,\n      rowKey\n    });\n  };\n  onMounted(() => {\n    if (unref(measurable)) {\n      doMeasure(true);\n    }\n  });\n  return {\n    isScrolling,\n    measurable,\n    measured,\n    rowRef,\n    eventHandlers,\n    onExpand\n  };\n};\nconst COMPONENT_NAME = \"ElTableV2TableRow\";\nconst TableV2Row = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2RowProps,\n  setup(props, {\n    expose,\n    slots,\n    attrs\n  }) {\n    const {\n      eventHandlers,\n      isScrolling,\n      measurable,\n      measured,\n      rowRef,\n      onExpand\n    } = useTableRow(props);\n    expose({\n      onExpand\n    });\n    return () => {\n      const {\n        columns,\n        columnsStyles,\n        expandColumnKey,\n        depth,\n        rowData,\n        rowIndex,\n        style\n      } = props;\n      let ColumnCells = columns.map((column, columnIndex) => {\n        const expandable = isArray(rowData.children) && rowData.children.length > 0 && column.key === expandColumnKey;\n        return slots.cell({\n          column,\n          columns,\n          columnIndex,\n          depth,\n          style: columnsStyles[column.key],\n          rowData,\n          rowIndex,\n          isScrolling: unref(isScrolling),\n          expandIconProps: expandable ? {\n            rowData,\n            rowIndex,\n            onExpand\n          } : void 0\n        });\n      });\n      if (slots.row) {\n        ColumnCells = slots.row({\n          cells: ColumnCells.map(node => {\n            if (isArray(node) && node.length === 1) {\n              return node[0];\n            }\n            return node;\n          }),\n          style,\n          columns,\n          depth,\n          rowData,\n          rowIndex,\n          isScrolling: unref(isScrolling)\n        });\n      }\n      if (unref(measurable)) {\n        const {\n          height,\n          ...exceptHeightStyle\n        } = style || {};\n        const _measured = unref(measured);\n        return createVNode(\"div\", mergeProps({\n          \"ref\": rowRef,\n          \"class\": props.class,\n          \"style\": _measured ? style : exceptHeightStyle,\n          \"role\": \"row\"\n        }, attrs, unref(eventHandlers)), [ColumnCells]);\n      }\n      return createVNode(\"div\", mergeProps(attrs, {\n        \"ref\": rowRef,\n        \"class\": props.class,\n        \"style\": style,\n        \"role\": \"row\"\n      }, unref(eventHandlers)), [ColumnCells]);\n    };\n  }\n});\nvar Row = TableV2Row;\nexport { Row as default };", "map": {"version": 3, "names": ["isScrolling", "inject", "TableV2InjectionKey", "measured", "ref", "rowRef", "measurable", "computed", "isNumber", "props", "estimatedRowHeight", "rowIndex", "doMeasure", "isInit", "$rowRef", "columns", "onRowHeightChange", "<PERSON><PERSON><PERSON>", "style", "height", "getBoundingClientRect", "value", "nextTick", "Number", "parseInt", "firstColumn", "isPlaceholder", "placeholderSign", "fixed", "eventHandlers", "rowData", "onRowHover", "handlers", "rowEventHandlers", "eventHandlers2", "Object", "entries", "for<PERSON>ach", "eventName", "handler", "isFunction", "event", "name", "hovered", "<PERSON><PERSON><PERSON><PERSON>", "onExpand", "expanded", "onRowExpand", "unref", "COMPONENT_NAME", "TableV2Row", "defineComponent", "tableV2RowProps", "setup", "expose", "slots", "useTableRow", "columnsStyles", "expandColumnKey", "depth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "column", "columnIndex", "expandable", "isArray", "children", "length", "key", "cell", "expandIconProps", "row", "cells", "node", "_measured", "createVNode", "mergeProps", "class", "exceptHeightStyle", "attrs", "Row"], "sources": ["../../../../../../../packages/components/table-v2/src/components/row.tsx"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  inject,\n  nextTick,\n  onMounted,\n  ref,\n  unref,\n} from 'vue'\nimport { isArray, isFunction, isNumber } from '@element-plus/utils'\nimport { tableV2RowProps } from '../row'\nimport { TableV2InjectionKey } from '../tokens'\nimport { placeholderSign } from '../private'\n\nimport type { CSSProperties, RendererElement, RendererNode, VNode } from 'vue'\nimport type { RowEventHandlers, TableV2RowProps } from '../row'\n\ntype CustomizedCellsType = VNode<\n  RendererNode,\n  RendererElement,\n  {\n    [key: string]: any\n  }\n>[]\n\ntype DefaultCellsType = VNode<\n  RendererNode,\n  RendererElement,\n  {\n    [key: string]: any\n  }\n>[][]\n\ntype ColumnCellsType = DefaultCellsType | CustomizedCellsType\n\nconst useTableRow = (props: TableV2RowProps) => {\n  const { isScrolling } = inject(TableV2InjectionKey)!\n\n  const measured = ref(false)\n  const rowRef = ref<HTMLElement>()\n  const measurable = computed(() => {\n    return isNumber(props.estimatedRowHeight) && props.rowIndex >= 0\n  })\n\n  const doMeasure = (isInit = false) => {\n    const $rowRef = unref(rowRef)\n    if (!$rowRef) return\n    const { columns, onRowHeightChange, rowKey, rowIndex, style } = props\n    const { height } = $rowRef.getBoundingClientRect()\n    measured.value = true\n\n    nextTick(() => {\n      if (isInit || height !== Number.parseInt(style!.height as string)) {\n        const firstColumn = columns[0]\n        const isPlaceholder = firstColumn?.placeholderSign === placeholderSign\n        onRowHeightChange?.(\n          { rowKey, height, rowIndex },\n          firstColumn && !isPlaceholder && firstColumn.fixed\n        )\n      }\n    })\n  }\n\n  const eventHandlers = computed(() => {\n    const { rowData, rowIndex, rowKey, onRowHover } = props\n    const handlers = props.rowEventHandlers || ({} as RowEventHandlers)\n    const eventHandlers = {} as {\n      [key in keyof RowEventHandlers]: (e: Event) => void\n    }\n\n    Object.entries(handlers).forEach(([eventName, handler]) => {\n      if (isFunction(handler)) {\n        eventHandlers[eventName as keyof RowEventHandlers] = (event: Event) => {\n          handler({\n            event,\n            rowData,\n            rowIndex,\n            rowKey,\n          })\n        }\n      }\n    })\n\n    if (onRowHover) {\n      ;(\n        [\n          { name: 'onMouseleave', hovered: false },\n          { name: 'onMouseenter', hovered: true },\n        ] as const\n      ).forEach(({ name, hovered }) => {\n        const existedHandler = eventHandlers[name]\n        eventHandlers[name] = ((event: MouseEvent) => {\n          onRowHover({\n            event,\n            hovered,\n            rowData,\n            rowIndex,\n            rowKey,\n          })\n\n          existedHandler?.(event)\n        }) as any\n      })\n    }\n    return eventHandlers\n  })\n\n  const onExpand = (expanded: boolean) => {\n    const { onRowExpand, rowData, rowIndex, rowKey } = props\n\n    onRowExpand?.({\n      expanded,\n      rowData,\n      rowIndex,\n      rowKey,\n    })\n  }\n\n  onMounted(() => {\n    if (unref(measurable)) {\n      doMeasure(true)\n    }\n  })\n\n  return { isScrolling, measurable, measured, rowRef, eventHandlers, onExpand }\n}\n\nconst COMPONENT_NAME = 'ElTableV2TableRow'\n\nconst TableV2Row = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2RowProps,\n  setup(props, { expose, slots, attrs }) {\n    const {\n      eventHandlers,\n      isScrolling,\n      measurable,\n      measured,\n      rowRef,\n\n      onExpand,\n    } = useTableRow(props)\n\n    expose({\n      /**\n       * @description manually dispatching expand action on row.\n       */\n      onExpand,\n    })\n\n    return () => {\n      const {\n        columns,\n        columnsStyles,\n        expandColumnKey,\n        depth,\n        rowData,\n        rowIndex,\n        style,\n      } = props\n\n      let ColumnCells: ColumnCellsType = columns.map((column, columnIndex) => {\n        const expandable =\n          isArray(rowData.children) &&\n          rowData.children.length > 0 &&\n          column.key === expandColumnKey\n\n        return slots.cell!({\n          column,\n          columns,\n          columnIndex,\n          depth,\n          style: columnsStyles[column.key!],\n          rowData,\n          rowIndex,\n          isScrolling: unref(isScrolling),\n          expandIconProps: expandable\n            ? {\n                rowData,\n                rowIndex,\n                onExpand,\n              }\n            : undefined,\n        })\n      })\n\n      if (slots.row) {\n        ColumnCells = slots.row({\n          cells: ColumnCells.map((node) => {\n            if (isArray(node) && node.length === 1) {\n              return node[0]\n            }\n            return node\n          }),\n          style,\n          columns,\n          depth,\n          rowData,\n          rowIndex,\n          isScrolling: unref(isScrolling),\n        })\n      }\n\n      if (unref(measurable)) {\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const { height, ...exceptHeightStyle } = style || {}\n        const _measured = unref(measured)\n        return (\n          <div\n            ref={rowRef}\n            class={props.class}\n            style={_measured ? style : exceptHeightStyle}\n            role=\"row\"\n            {...attrs}\n            {...unref(eventHandlers)}\n          >\n            {ColumnCells}\n          </div>\n        )\n      }\n\n      return (\n        <div\n          {...attrs}\n          ref={rowRef}\n          class={props.class}\n          style={style}\n          role=\"row\"\n          {...unref(eventHandlers)}\n        >\n          {ColumnCells}\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableV2Row\n\nexport type TableV2RowCellRenderParam = {\n  column: TableV2RowProps['columns'][number]\n  columns: TableV2RowProps['columns']\n  columnIndex: number\n  depth: number\n  style: CSSProperties\n  rowData: any\n  rowIndex: number\n  isScrolling: boolean\n  expandIconProps?: {\n    rowData: any\n    rowIndex: number\n    onExpand: (expand: boolean) => void\n  }\n}\n"], "mappings": ";;;;;;;;;;EAmCM;IACEA;EAAE,IAAAC,MAAA,CAAAC,mBAAA;EAAF,MAAkBC,QAAO,GAAAC,GAAA;EAE/B,MAAMC,MAAQ,GAAAD,GAAM;EACpB,MAAME,UAAS,GAAfC,QAAA;IACA,OAAgBC,QAAA,CAAAC,KAAW,CAAAC,kBAAO,KAAAD,KAAA,CAAAE,QAAA;IAChC;EACD,MAFDC,SAAA,GAAAA,CAAAC,MAAA;;IAIA,IAAM,CAAAC,OAAA,EACJ;IACA;MACMC,OAAA;MAAEC,iBAAF;MAAWC,MAAX;MAA8BN,QAA9B;MAAsCO;IAAU,IAAAT,KAAA;IAAhD,MAAN;MACMU;IAAE,IAAAL,OAAA,CAAAM,qBAAA;IAAFjB,QAAa,CAAAkB,KAAQ;IAC3BC,QAAQ,CAAC,MAAT;MAEA,IAAAT,MAAe,IAAAM,MAAA,KAAAI,MAAA,CAAAC,QAAA,CAAAN,KAAA,CAAAC,MAAA;QACb,MAAUM,WAAU,GAAAV,OAAW,GAAC;QAC9B,MAAMW,aAAW,GAAU,CAAAD,WAA3B,oBAAAA,WAAA,CAAAE,eAAA,MAAAA,eAAA;QACAX,iBAAA,IAAsB,YAAW,CAAE,GAAAA,iBAAA,CAAoB;UACvDC,MAAA;UACIE,MAAF;UAAUR;QAAQ,GAAAc,WAAA,KAAAC,aAAA,IAAAD,WAAA,CAAAG,KAAA;;IAGrB;EACF;EACF,MAjBDC,aAAA,GAAAtB,QAAA;;MAmBAuB,OAAmB;MACXnB,QAAA;MAAEM,MAAF;MAAWc;QAAXtB,KAAA;IAA6B,MAAAuB,QAAA,GAAAvB,KAAA,CAAAwB,gBAAA;IAA7B,MAANC,cAAA;IACAC,MAAM,CAAAC,OAAQ,CAAGJ,QAAM,EAAAK,OAAA,GAAAC,SAAvB,EAAAC,OAAA;MACM,IAAAC,UAAA,CAAAD,OAAN;QAIAL,cAAe,CAAAI,SAAU,IAAQG,KAAE;UAC7BF,OAAU;YACZE,KAAA;YACEX,OAAQ;YACNnB,QADM;YAENM;YACA;QACA;MAJM;;IAOX,IAAAc,UAAA;;QAGCW,IAAA,gBAAY;QACdC,OAAA;OAEI;QAAED,IAAI,EAAE,cAAR;QAAwBC,OAAO,EAAE;MAAjC,CADF,CAEE,CAAAN,OAAA;QAAEK,IAAI;QAAkBC;MAAxB,CAFF,KADD;QAKY,MAAFC,cAAA,GAAAV,cAAA,CAAAQ,IAAA;QAAQR,cAAA,CAAAQ,IAAA,IAAAD,KAAA;UAAcV,UAAA;YAC/BU,KAAoB;;YACpBX,OAAA;YACEnB,QAAU;YACRM;YACA;wBAFS,oBAAA2B,cAAA,CAAAH,KAAA;;MAKT;IALS;WAQGP,cAAA;;QAhBjBW,QAAA,GAAAC,QAAA;IAmBF;;MACDhB,OAAA;MAzCFnB,QAAA;;KA4CM,GAAAF,KAAA;IACJsC,WAAM,oBAAAA,WAAA;MAAED,QAAF;MAAehB,OAAf;MAAwBnB,QAAxB;MAAkCM;IAAlC;EAEN;WAAc;QAAA+B,KAAA,CAAA1C,UAAA;MAGZM,SAHY;IAIZ;EAJY;EAMf,OATD;;IAWAN,UAAgB;IACdH,QAAS;UACE;IACV0B,aAAA;IAHHgB;GAMO;;MAAAI,cAAA;MAAAC,UAAA,GAAAC,eAAA;MAAA,EAAAF,cAAA;OAAA,EAAAG,eAAA;EAA4DC,MAAA5C,KAAA;IAAnE6C,MAAA;IAzFFC,KAAA;;EA4FA,CAAM;IAEA;MACA1B,aAD6B;MAEjC7B,WAFiC;;MAG5BG,QAAQ;MAAAE,MAAA;MAAAwC;IAAiB,IAAAW,WAAA,CAAA/C,KAAA;IAAS6C,MAAA;MAC/BT;MACJ;WADI;MAGJ,MAHI;QAAA9B,OAAA;QAAA0C,aAAA;QAOJC,eAAA;QACEC,KAAA;QAEJ7B,OAAO;QACLnB,QAAA;QACNO;MACA,IAAAT,KAAA;MACM,IAAAmD,WAAA,GAAA7C,OAAA,CAAA8C,GAAA,EAAAC,MAAA,EAAAC,WAAA;QAJF,MAAAC,UAAA,GAAAC,OAAA,CAAAnC,OAAA,CAAAoC,QAAA,KAAApC,OAAA,CAAAoC,QAAA,CAAAC,MAAA,QAAAL,MAAA,CAAAM,GAAA,KAAAV,eAAA;QAOA,OAAaH,KAAA,CAAAc,IAAA;UACLP,MAAA;UAAA/C,OAAA;UAAAgD,WAAA;UAAAJ,KAAA;UAAAzC,KAAA,EAAAuC,aAAA,CAAAK,MAAA,CAAAM,GAAA;UAAAtC,OAAA;UAAAnB,QAAA;UAOJX,WAAA,EAAAgD,KAAA,CAAAhD,WAAA;UACEsE,eARJ,EAAAN,UAAA;YAUIlC,OAAA;YACInB,QAAA;YAKCkC;WAAY;UAEjB;;UAEAU,KAJiB,CAAAgB,GAAA;QAKjBX,WAAoB,GAAAL,KAAA,CAAAgB,GAAC,CAAM;UAC3BC,KANiB,EAAAZ,WAAA,CAAAC,GAAA,CAAAY,IAAA;YAAA,IAAAR,OAAA,CAAAQ,IAAA,KAAAA,IAAA,CAAAN,MAAA;cAQjB,OAAWM,IAAE,GAAK;;YAGZ,OADFA,IAAA;YAEE;UACAvD,KAAA;UAHFH,OAKA;UAfN4C,KAAA;UANF7B,OAAA;;UAyBI9B,WAAW,EAAAgD,KAAA,CAAAhD,WAAA;QACb;MACE;eACM,CAAAM,UAAQ,GAAR;cACF;UACDa,MAAA;;QACD,IAAAD,KAAA;QACD,MANqBwD,SAAA,GAAA1B,KAAA,CAAA7C,QAAA;eAAAwE,WAAA,QAAAC,UAAA;UAQtB,KARsB,EAAAvE,MAAA;UAStB,OATsB,EAAAI,KAAA,CAAAoE,KAAA;UAUtB,OAVsB,EAAAH,SAAA,GAAAxD,KAAA,GAAA4D,iBAAA;UAWtB,MAXsB;WAYXC,KAAA,EAAA/B,KAAE,CAAKnB,aAAA,KAAA+B,WAAA;MAZI;MAczB,OAAAe,WAAA,QAAAC,UAAA,CAAAG,KAAA;;QAED,OAAS,EAAAtE,KAAA,CAAAoE,KAAc;QACrB,SAAA3D,KAAA;QACA,MAAM;cAAA,CAAAW,aAAA,KAAA+B,WAAA;;;;AACN,IAAAoB,GAAA,GAAe9B,UAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}