{"ast": null, "code": "import { renderSlot, createVNode } from 'vue';\nconst TableV2Cell = (props, {\n  slots\n}) => {\n  var _a;\n  const {\n    cellData,\n    style\n  } = props;\n  const displayText = ((_a = cellData == null ? void 0 : cellData.toString) == null ? void 0 : _a.call(cellData)) || \"\";\n  const defaultSlot = renderSlot(slots, \"default\", props, () => [displayText]);\n  return createVNode(\"div\", {\n    \"class\": props.class,\n    \"title\": displayText,\n    \"style\": style\n  }, [defaultSlot]);\n};\nTableV2Cell.displayName = \"ElTableV2Cell\";\nTableV2Cell.inheritAttrs = false;\nvar TableCell = TableV2Cell;\nexport { TableCell as default };", "map": {"version": 3, "names": ["slots", "_a", "cellData", "style", "props", "displayText", "toString", "call", "defaultSlot", "renderSlot", "createVNode", "class", "TableV2Cell", "inheritAttrs", "TableCell"], "sources": ["../../../../../../../packages/components/table-v2/src/components/cell.tsx"], "sourcesContent": ["import { renderSlot } from 'vue'\nimport type { FunctionalComponent } from 'vue'\nimport type { TableV2CellProps } from '../cell'\n\nconst TableV2Cell: FunctionalComponent<TableV2CellProps> = (\n  props: TableV2CellProps,\n  { slots }\n) => {\n  const { cellData, style } = props\n  const displayText = cellData?.toString?.() || ''\n  const defaultSlot = renderSlot(slots, 'default', props, () => [displayText])\n  return (\n    <div class={props.class} title={displayText} style={style}>\n      {defaultSlot}\n    </div>\n  )\n}\n\nTableV2Cell.displayName = 'ElTableV2Cell'\nTableV2Cell.inheritAttrs = false\n\nexport default TableV2Cell\n"], "mappings": ";;EAIAA;AAEI;EACC,IAAAC,EAAA;EACH,MAAM;IAAEC,QAAF;IAAYC;EAAZ,IAAsBC,KAA5B;EACA,MAAMC,WAAW,GAAG,EAAAJ,EAAA,GAAAC,QAAU,WAAgB,KAA9C,IAAAA,QAAA,CAAAI,QAAA,qBAAAL,EAAA,CAAAM,IAAA,CAAAL,QAAA;EACA,MAAMM,WAAW,GAAGC,UAAU,CAACT,KAAD,EAAQ,SAAR,EAAmBI,KAAnB,EAA0B,MAAM,CAACC,WAAD,CAAhC,CAA9B;EACA,OAAAK,WAAA;IAAA,OACc,EAAAN,KAAK,CAACO,KADpB;IAAA,SACkCN,WADlC;IAAA,OACsD,EAAAF;EADtD,IAEKK,WAFL;AAKD,CAZD;;AAcAI,WAAW,CAACC,YAAZ;AACA,IAAAC,SAAA,GAAAF,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}