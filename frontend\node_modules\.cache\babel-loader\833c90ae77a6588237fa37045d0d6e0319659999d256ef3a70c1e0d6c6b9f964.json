{"ast": null, "code": "import Transfer from './src/transfer2.mjs';\nexport { LEFT_CHECK_CHANGE_EVENT, RIGHT_CHECK_CHANGE_EVENT, transferCheckedChangeFn, transferEmits, transferProps } from './src/transfer.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElTransfer = withInstall(Transfer);\nexport { ElTransfer, ElTransfer as default };", "map": {"version": 3, "names": ["ElTransfer", "withInstall", "Transfer"], "sources": ["../../../../../packages/components/transfer/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Transfer from './src/transfer.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTransfer: SFCWithInstall<typeof Transfer> = withInstall(Transfer)\nexport default ElTransfer\n\nexport * from './src/transfer'\n"], "mappings": ";;;AAEY,MAACA,UAAU,GAAGC,WAAW,CAACC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}