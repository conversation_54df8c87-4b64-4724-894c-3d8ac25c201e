{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, with<PERSON><PERSON>s as _withKeys, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nimport _imports_0 from '@/assets/logo.png';\nconst _hoisted_1 = {\n  class: \"login-container\"\n};\nconst _hoisted_2 = {\n  class: \"login-card\"\n};\nconst _hoisted_3 = {\n  class: \"login-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_tab_pane = _resolveComponent(\"el-tab-pane\");\n  const _component_el_tabs = _resolveComponent(\"el-tabs\");\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n    class: \"login-header\"\n  }, [_createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"Logo\",\n    class: \"login-logo\"\n  }), _createElementVNode(\"h2\", null, \"图书馆自习室管理系统\")], -1 /* HOISTED */)), _createVNode(_component_el_tabs, {\n    modelValue: $setup.activeTab,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.activeTab = $event),\n    class: \"login-tabs\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_tab_pane, {\n      label: \"密码登录\",\n      name: \"password\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form, {\n        ref: \"passwordFormRef\",\n        model: $setup.passwordForm,\n        rules: $setup.passwordRules,\n        \"label-position\": \"top\",\n        onKeyup: _withKeys($setup.handlePasswordLogin, [\"enter\"])\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"学号\",\n          prop: \"studentId\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.passwordForm.studentId,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.passwordForm.studentId = $event),\n            placeholder: \"请输入学号\",\n            \"prefix-icon\": $setup.User\n          }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"密码\",\n          prop: \"password\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.passwordForm.password,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.passwordForm.password = $event),\n            type: \"password\",\n            placeholder: \"请输入密码\",\n            \"prefix-icon\": $setup.Lock,\n            \"show-password\": \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, null, {\n          default: _withCtx(() => [_createVNode(_component_el_button, {\n            type: \"primary\",\n            class: \"login-button\",\n            loading: $setup.loading,\n            onClick: $setup.handlePasswordLogin\n          }, {\n            default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\" 登录 \")])),\n            _: 1 /* STABLE */,\n            __: [5]\n          }, 8 /* PROPS */, [\"loading\", \"onClick\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\", \"rules\", \"onKeyup\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_tab_pane, {\n      label: \"证书登录\",\n      name: \"certificate\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form, {\n        ref: \"certificateFormRef\",\n        model: $setup.certificateForm,\n        rules: $setup.certificateRules,\n        \"label-position\": \"top\",\n        onKeyup: _withKeys($setup.handleCertificateLogin, [\"enter\"])\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"学号\",\n          prop: \"studentId\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.certificateForm.studentId,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.certificateForm.studentId = $event),\n            placeholder: \"请输入学号\",\n            \"prefix-icon\": $setup.User\n          }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"私钥\",\n          prop: \"privateKey\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.certificateForm.privateKey,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.certificateForm.privateKey = $event),\n            type: \"textarea\",\n            rows: 4,\n            placeholder: \"请输入SM2私钥\",\n            \"show-password\": \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, null, {\n          default: _withCtx(() => [_createVNode(_component_el_button, {\n            type: \"primary\",\n            class: \"login-button\",\n            loading: $setup.loading,\n            onClick: $setup.handleCertificateLogin\n          }, {\n            default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\" 登录 \")])),\n            _: 1 /* STABLE */,\n            __: [6]\n          }, 8 /* PROPS */, [\"loading\", \"onClick\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\", \"rules\", \"onKeyup\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"p\", null, [_cache[8] || (_cache[8] = _createTextVNode(\" 还没有账号？ \")), _createVNode(_component_router_link, {\n    to: \"/register\"\n  }, {\n    default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\"立即注册\")])),\n    _: 1 /* STABLE */,\n    __: [7]\n  })])])])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "src", "alt", "_createVNode", "_component_el_tabs", "modelValue", "$setup", "activeTab", "_cache", "$event", "default", "_withCtx", "_component_el_tab_pane", "label", "name", "_component_el_form", "ref", "model", "passwordForm", "rules", "passwordRules", "onKeyup", "_with<PERSON><PERSON><PERSON>", "handlePasswordLogin", "_component_el_form_item", "prop", "_component_el_input", "studentId", "placeholder", "User", "_", "password", "type", "Lock", "_component_el_button", "loading", "onClick", "_createTextVNode", "__", "certificateForm", "certificateRules", "handleCertificateLogin", "privateKey", "rows", "_hoisted_3", "_component_router_link", "to"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-card\">\n      <div class=\"login-header\">\n        <img src=\"@/assets/logo.png\" alt=\"Logo\" class=\"login-logo\" />\n        <h2>图书馆自习室管理系统</h2>\n      </div>\n\n      <el-tabs v-model=\"activeTab\" class=\"login-tabs\">\n        <el-tab-pane label=\"密码登录\" name=\"password\">\n          <el-form\n            ref=\"passwordFormRef\"\n            :model=\"passwordForm\"\n            :rules=\"passwordRules\"\n            label-position=\"top\"\n            @keyup.enter=\"handlePasswordLogin\"\n          >\n            <el-form-item label=\"学号\" prop=\"studentId\">\n              <el-input\n                v-model=\"passwordForm.studentId\"\n                placeholder=\"请输入学号\"\n                :prefix-icon=\"User\"\n              />\n            </el-form-item>\n\n            <el-form-item label=\"密码\" prop=\"password\">\n              <el-input\n                v-model=\"passwordForm.password\"\n                type=\"password\"\n                placeholder=\"请输入密码\"\n                :prefix-icon=\"Lock\"\n                show-password\n              />\n            </el-form-item>\n\n            <el-form-item>\n              <el-button\n                type=\"primary\"\n                class=\"login-button\"\n                :loading=\"loading\"\n                @click=\"handlePasswordLogin\"\n              >\n                登录\n              </el-button>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"证书登录\" name=\"certificate\">\n          <el-form\n            ref=\"certificateFormRef\"\n            :model=\"certificateForm\"\n            :rules=\"certificateRules\"\n            label-position=\"top\"\n            @keyup.enter=\"handleCertificateLogin\"\n          >\n            <el-form-item label=\"学号\" prop=\"studentId\">\n              <el-input\n                v-model=\"certificateForm.studentId\"\n                placeholder=\"请输入学号\"\n                :prefix-icon=\"User\"\n              />\n            </el-form-item>\n\n            <el-form-item label=\"私钥\" prop=\"privateKey\">\n              <el-input\n                v-model=\"certificateForm.privateKey\"\n                type=\"textarea\"\n                :rows=\"4\"\n                placeholder=\"请输入SM2私钥\"\n                show-password\n              />\n            </el-form-item>\n\n            <el-form-item>\n              <el-button\n                type=\"primary\"\n                class=\"login-button\"\n                :loading=\"loading\"\n                @click=\"handleCertificateLogin\"\n              >\n                登录\n              </el-button>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div class=\"login-footer\">\n        <p>\n          还没有账号？\n          <router-link to=\"/register\">立即注册</router-link>\n        </p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { User, Lock } from \"@element-plus/icons-vue\";\nimport { SM3Hasher, SM2Crypto } from \"@/utils/crypto\";\n\nexport default {\n  name: \"LoginView\",\n  setup() {\n    const store = useStore();\n    const router = useRouter();\n\n    const passwordFormRef = ref(null);\n    const certificateFormRef = ref(null);\n    const activeTab = ref(\"password\");\n    const loading = ref(false);\n\n    // 密码登录表单\n    const passwordForm = reactive({\n      studentId: \"\",\n      password: \"\",\n    });\n\n    // 证书登录表单\n    const certificateForm = reactive({\n      studentId: \"\",\n      privateKey: \"\",\n    });\n\n    // 表单验证规则\n    const passwordRules = {\n      studentId: [\n        { required: true, message: \"请输入学号\", trigger: \"blur\" },\n        { min: 5, max: 20, message: \"学号长度应为5-20个字符\", trigger: \"blur\" },\n      ],\n      password: [\n        { required: true, message: \"请输入密码\", trigger: \"blur\" },\n        { min: 6, max: 20, message: \"密码长度应为6-20个字符\", trigger: \"blur\" },\n      ],\n    };\n\n    const certificateRules = {\n      studentId: [\n        { required: true, message: \"请输入学号\", trigger: \"blur\" },\n        { min: 5, max: 20, message: \"学号长度应为5-20个字符\", trigger: \"blur\" },\n      ],\n      privateKey: [{ required: true, message: \"请输入SM2私钥\", trigger: \"blur\" }],\n    };\n\n    // 密码登录处理\n    const handlePasswordLogin = async () => {\n      if (!passwordFormRef.value) return;\n\n      await passwordFormRef.value.validate(async (valid) => {\n        if (!valid) return;\n\n        try {\n          loading.value = true;\n\n          // 对密码进行SM3哈希\n          const hashedPassword = SM3Hasher.hash(passwordForm.password);\n\n          // 调用登录接口\n          await store.dispatch(\"user/login\", {\n            studentId: passwordForm.studentId,\n            password: hashedPassword,\n          });\n\n          ElMessage.success(\"登录成功\");\n          router.push(\"/dashboard\");\n        } catch (error) {\n          ElMessage.error(error.message || \"登录失败，请检查学号和密码\");\n        } finally {\n          loading.value = false;\n        }\n      });\n    };\n\n    // 证书登录处理\n    const handleCertificateLogin = async () => {\n      if (!certificateFormRef.value) return;\n\n      await certificateFormRef.value.validate(async (valid) => {\n        if (!valid) return;\n\n        try {\n          loading.value = true;\n\n          // 获取SM2挑战值\n          const challengeResponse = await store.dispatch(\"user/getSM2Challenge\", {\n            studentId: certificateForm.studentId,\n          });\n\n          const { challenge } = challengeResponse;\n\n          // 使用私钥对挑战值进行签名\n          const signature = SM2Crypto.sign(certificateForm.privateKey, challenge);\n\n          // 调用SM2登录接口\n          await store.dispatch(\"user/sm2Login\", {\n            studentId: certificateForm.studentId,\n            signature,\n          });\n\n          ElMessage.success(\"登录成功\");\n          router.push(\"/dashboard\");\n        } catch (error) {\n          ElMessage.error(error.message || \"证书登录失败，请检查学号和私钥\");\n        } finally {\n          loading.value = false;\n        }\n      });\n    };\n\n    return {\n      passwordFormRef,\n      certificateFormRef,\n      activeTab,\n      loading,\n      passwordForm,\n      certificateForm,\n      passwordRules,\n      certificateRules,\n      handlePasswordLogin,\n      handleCertificateLogin,\n      User,\n      Lock,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.login-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background-color: #f5f7fa;\n}\n\n.login-card {\n  width: 400px;\n  padding: 30px;\n  background-color: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n\n  .login-logo {\n    width: 80px;\n    height: 80px;\n    margin-bottom: 15px;\n  }\n\n  h2 {\n    font-size: 24px;\n    color: #303133;\n    margin: 0;\n  }\n}\n\n.login-tabs {\n  margin-bottom: 20px;\n}\n\n.login-button {\n  width: 100%;\n}\n\n.login-footer {\n  margin-top: 20px;\n  text-align: center;\n  font-size: 14px;\n  color: #606266;\n\n  a {\n    color: #409eff;\n    text-decoration: none;\n\n    &:hover {\n      text-decoration: underline;\n    }\n  }\n}\n</style>\n"], "mappings": ";OAIaA,UAAuB;;EAH7BC,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAY;;EAsFhBA,KAAK,EAAC;AAAc;;;;;;;;;uBAvF7BC,mBAAA,CA8FM,OA9FNC,UA8FM,GA7FJC,mBAAA,CA4FM,OA5FNC,UA4FM,G,0BA3FJD,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAc,IACvBG,mBAAA,CAA6D;IAAxDE,GAAuB,EAAvBN,UAAuB;IAACO,GAAG,EAAC,MAAM;IAACN,KAAK,EAAC;MAC9CG,mBAAA,CAAmB,YAAf,YAAU,E,sBAGhBI,YAAA,CA8EUC,kBAAA;IAtFhBC,UAAA,EAQwBC,MAAA,CAAAC,SAAS;IARjC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAQwBH,MAAA,CAAAC,SAAS,GAAAE,MAAA;IAAEb,KAAK,EAAC;;IARzCc,OAAA,EAAAC,QAAA,CASQ,MAqCc,CArCdR,YAAA,CAqCcS,sBAAA;MArCDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MATvCJ,OAAA,EAAAC,QAAA,CAUU,MAmCU,CAnCVR,YAAA,CAmCUY,kBAAA;QAlCRC,GAAG,EAAC,iBAAiB;QACpBC,KAAK,EAAEX,MAAA,CAAAY,YAAY;QACnBC,KAAK,EAAEb,MAAA,CAAAc,aAAa;QACrB,gBAAc,EAAC,KAAK;QACnBC,OAAK,EAflBC,SAAA,CAe0BhB,MAAA,CAAAiB,mBAAmB;;QAf7Cb,OAAA,EAAAC,QAAA,CAiBY,MAMe,CANfR,YAAA,CAMeqB,uBAAA;UANDX,KAAK,EAAC,IAAI;UAACY,IAAI,EAAC;;UAjB1Cf,OAAA,EAAAC,QAAA,CAkBc,MAIE,CAJFR,YAAA,CAIEuB,mBAAA;YAtBhBrB,UAAA,EAmByBC,MAAA,CAAAY,YAAY,CAACS,SAAS;YAnB/C,uBAAAnB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAmByBH,MAAA,CAAAY,YAAY,CAACS,SAAS,GAAAlB,MAAA;YAC/BmB,WAAW,EAAC,OAAO;YAClB,aAAW,EAAEtB,MAAA,CAAAuB;;UArB9BC,CAAA;YAyBY3B,YAAA,CAQeqB,uBAAA;UARDX,KAAK,EAAC,IAAI;UAACY,IAAI,EAAC;;UAzB1Cf,OAAA,EAAAC,QAAA,CA0Bc,MAME,CANFR,YAAA,CAMEuB,mBAAA;YAhChBrB,UAAA,EA2ByBC,MAAA,CAAAY,YAAY,CAACa,QAAQ;YA3B9C,uBAAAvB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA2ByBH,MAAA,CAAAY,YAAY,CAACa,QAAQ,GAAAtB,MAAA;YAC9BuB,IAAI,EAAC,UAAU;YACfJ,WAAW,EAAC,OAAO;YAClB,aAAW,EAAEtB,MAAA,CAAA2B,IAAI;YAClB,eAAa,EAAb;;UA/BhBH,CAAA;YAmCY3B,YAAA,CASeqB,uBAAA;UA5C3Bd,OAAA,EAAAC,QAAA,CAoCc,MAOY,CAPZR,YAAA,CAOY+B,oBAAA;YANVF,IAAI,EAAC,SAAS;YACdpC,KAAK,EAAC,cAAc;YACnBuC,OAAO,EAAE7B,MAAA,CAAA6B,OAAO;YAChBC,OAAK,EAAE9B,MAAA,CAAAiB;;YAxCxBb,OAAA,EAAAC,QAAA,CAyCe,MAEDH,MAAA,QAAAA,MAAA,OA3Cd6B,gBAAA,CAyCe,MAED,E;YA3CdP,CAAA;YAAAQ,EAAA;;UAAAR,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QAgDQ3B,YAAA,CAqCcS,sBAAA;MArCDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MAhDvCJ,OAAA,EAAAC,QAAA,CAiDU,MAmCU,CAnCVR,YAAA,CAmCUY,kBAAA;QAlCRC,GAAG,EAAC,oBAAoB;QACvBC,KAAK,EAAEX,MAAA,CAAAiC,eAAe;QACtBpB,KAAK,EAAEb,MAAA,CAAAkC,gBAAgB;QACxB,gBAAc,EAAC,KAAK;QACnBnB,OAAK,EAtDlBC,SAAA,CAsD0BhB,MAAA,CAAAmC,sBAAsB;;QAtDhD/B,OAAA,EAAAC,QAAA,CAwDY,MAMe,CANfR,YAAA,CAMeqB,uBAAA;UANDX,KAAK,EAAC,IAAI;UAACY,IAAI,EAAC;;UAxD1Cf,OAAA,EAAAC,QAAA,CAyDc,MAIE,CAJFR,YAAA,CAIEuB,mBAAA;YA7DhBrB,UAAA,EA0DyBC,MAAA,CAAAiC,eAAe,CAACZ,SAAS;YA1DlD,uBAAAnB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA0DyBH,MAAA,CAAAiC,eAAe,CAACZ,SAAS,GAAAlB,MAAA;YAClCmB,WAAW,EAAC,OAAO;YAClB,aAAW,EAAEtB,MAAA,CAAAuB;;UA5D9BC,CAAA;YAgEY3B,YAAA,CAQeqB,uBAAA;UARDX,KAAK,EAAC,IAAI;UAACY,IAAI,EAAC;;UAhE1Cf,OAAA,EAAAC,QAAA,CAiEc,MAME,CANFR,YAAA,CAMEuB,mBAAA;YAvEhBrB,UAAA,EAkEyBC,MAAA,CAAAiC,eAAe,CAACG,UAAU;YAlEnD,uBAAAlC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAkEyBH,MAAA,CAAAiC,eAAe,CAACG,UAAU,GAAAjC,MAAA;YACnCuB,IAAI,EAAC,UAAU;YACdW,IAAI,EAAE,CAAC;YACRf,WAAW,EAAC,UAAU;YACtB,eAAa,EAAb;;UAtEhBE,CAAA;YA0EY3B,YAAA,CASeqB,uBAAA;UAnF3Bd,OAAA,EAAAC,QAAA,CA2Ec,MAOY,CAPZR,YAAA,CAOY+B,oBAAA;YANVF,IAAI,EAAC,SAAS;YACdpC,KAAK,EAAC,cAAc;YACnBuC,OAAO,EAAE7B,MAAA,CAAA6B,OAAO;YAChBC,OAAK,EAAE9B,MAAA,CAAAmC;;YA/ExB/B,OAAA,EAAAC,QAAA,CAgFe,MAEDH,MAAA,QAAAA,MAAA,OAlFd6B,gBAAA,CAgFe,MAED,E;YAlFdP,CAAA;YAAAQ,EAAA;;UAAAR,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;qCAwFM/B,mBAAA,CAKM,OALN6C,UAKM,GAJJ7C,mBAAA,CAGI,Y,0BA5FZsC,gBAAA,CAyFW,UAED,IAAAlC,YAAA,CAA8C0C,sBAAA;IAAjCC,EAAE,EAAC;EAAW;IA3FrCpC,OAAA,EAAAC,QAAA,CA2FsC,MAAIH,MAAA,QAAAA,MAAA,OA3F1C6B,gBAAA,CA2FsC,MAAI,E;IA3F1CP,CAAA;IAAAQ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}