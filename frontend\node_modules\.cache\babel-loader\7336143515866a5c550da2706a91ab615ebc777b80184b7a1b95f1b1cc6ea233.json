{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { defineComponent, getCurrentInstance, inject, watch, onUnmounted, h } from 'vue';\nimport useLayoutObserver from '../layout-observer.mjs';\nimport { removePopper } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport useRender from './render-helper.mjs';\nimport defaultProps from './defaults.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { addClass, removeClass } from '../../../../utils/dom/style.mjs';\nimport { isClient } from '@vueuse/core';\nimport { rAF } from '../../../../utils/raf.mjs';\nvar TableBody = defineComponent({\n  name: \"ElTableBody\",\n  props: defaultProps,\n  setup(props) {\n    const instance = getCurrentInstance();\n    const parent = inject(TABLE_INJECTION_KEY);\n    const ns = useNamespace(\"table\");\n    const {\n      wrappedRowRender,\n      tooltipContent,\n      tooltipTrigger\n    } = useRender(props);\n    const {\n      onColumnsChange,\n      onScrollableChange\n    } = useLayoutObserver(parent);\n    const hoveredCellList = [];\n    watch(props.store.states.hoverRow, (newVal, oldVal) => {\n      var _a;\n      const el = instance == null ? void 0 : instance.vnode.el;\n      const rows = Array.from((el == null ? void 0 : el.children) || []).filter(e => e == null ? void 0 : e.classList.contains(`${ns.e(\"row\")}`));\n      let rowNum = newVal;\n      const childNodes = (_a = rows[rowNum]) == null ? void 0 : _a.childNodes;\n      if (childNodes == null ? void 0 : childNodes.length) {\n        let control = 0;\n        const indexes = Array.from(childNodes).reduce((acc, item, index) => {\n          var _a2, _b;\n          if (((_a2 = childNodes[index]) == null ? void 0 : _a2.colSpan) > 1) {\n            control = (_b = childNodes[index]) == null ? void 0 : _b.colSpan;\n          }\n          if (item.nodeName !== \"TD\" && control === 0) {\n            acc.push(index);\n          }\n          control > 0 && control--;\n          return acc;\n        }, []);\n        indexes.forEach(rowIndex => {\n          var _a2;\n          rowNum = newVal;\n          while (rowNum > 0) {\n            const preChildNodes = (_a2 = rows[rowNum - 1]) == null ? void 0 : _a2.childNodes;\n            if (preChildNodes[rowIndex] && preChildNodes[rowIndex].nodeName === \"TD\" && preChildNodes[rowIndex].rowSpan > 1) {\n              addClass(preChildNodes[rowIndex], \"hover-cell\");\n              hoveredCellList.push(preChildNodes[rowIndex]);\n              break;\n            }\n            rowNum--;\n          }\n        });\n      } else {\n        hoveredCellList.forEach(item => removeClass(item, \"hover-cell\"));\n        hoveredCellList.length = 0;\n      }\n      if (!props.store.states.isComplex.value || !isClient) return;\n      rAF(() => {\n        const oldRow = rows[oldVal];\n        const newRow = rows[newVal];\n        if (oldRow && !oldRow.classList.contains(\"hover-fixed-row\")) {\n          removeClass(oldRow, \"hover-row\");\n        }\n        if (newRow) {\n          addClass(newRow, \"hover-row\");\n        }\n      });\n    });\n    onUnmounted(() => {\n      var _a;\n      (_a = removePopper) == null ? void 0 : _a();\n    });\n    return {\n      ns,\n      onColumnsChange,\n      onScrollableChange,\n      wrappedRowRender,\n      tooltipContent,\n      tooltipTrigger\n    };\n  },\n  render() {\n    const {\n      wrappedRowRender,\n      store\n    } = this;\n    const data = store.states.data.value || [];\n    return h(\"tbody\", {\n      tabIndex: -1\n    }, [data.reduce((acc, row) => {\n      return acc.concat(wrappedRowRender(row, acc.length));\n    }, [])]);\n  }\n});\nexport { TableBody as default };", "map": {"version": 3, "names": ["TableBody", "defineComponent", "name", "props", "defaultProps", "setup", "instance", "getCurrentInstance", "parent", "inject", "TABLE_INJECTION_KEY", "ns", "useNamespace", "wrappedRowRender", "tooltipContent", "tooltipTrigger", "useRender", "onColumnsChange", "onScrollableChange", "useLayoutObserver", "hoveredCellList", "watch", "store", "states", "hoverRow", "newVal", "oldVal", "_a", "el", "vnode", "rows", "Array", "from", "children", "filter", "e", "classList", "contains", "row<PERSON>um", "childNodes", "length", "control", "indexes", "reduce", "acc", "item", "index", "_a2", "_b", "colSpan", "nodeName", "push", "for<PERSON>ach", "rowIndex", "preChildNodes", "rowSpan", "addClass", "removeClass", "isComplex", "value", "isClient", "rAF", "oldRow", "newRow", "onUnmounted", "removePopper", "render", "data", "h", "tabIndex", "row", "concat"], "sources": ["../../../../../../../packages/components/table/src/table-body/index.ts"], "sourcesContent": ["// @ts-nocheck\nimport {\n  defineComponent,\n  getCurrentInstance,\n  h,\n  inject,\n  onUnmounted,\n  watch,\n} from 'vue'\nimport { addClass, isClient, rAF, removeClass } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport useLayoutObserver from '../layout-observer'\nimport { removePopper } from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport useRender from './render-helper'\nimport defaultProps from './defaults'\n\nimport type { VNode } from 'vue'\n\nexport default defineComponent({\n  name: 'ElTableBody',\n  props: defaultProps,\n  setup(props) {\n    const instance = getCurrentInstance()\n    const parent = inject(TABLE_INJECTION_KEY)\n    const ns = useNamespace('table')\n    const { wrappedRowRender, tooltipContent, tooltipTrigger } =\n      useRender(props)\n    const { onColumnsChange, onScrollableChange } = useLayoutObserver(parent!)\n\n    const hoveredCellList = []\n    watch(props.store.states.hoverRow, (newVal: any, oldVal: any) => {\n      const el = instance?.vnode.el as HTMLElement\n      const rows = Array.from(el?.children || []).filter((e) =>\n        e?.classList.contains(`${ns.e('row')}`)\n      )\n\n      // hover rowSpan > 1 choose the whole row\n      let rowNum = newVal\n      const childNodes = rows[rowNum]?.childNodes\n      if (childNodes?.length) {\n        let control = 0\n        const indexes = Array.from(childNodes).reduce((acc, item, index) => {\n          // drop colsSpan\n          if (childNodes[index]?.colSpan > 1) {\n            control = childNodes[index]?.colSpan\n          }\n          if (item.nodeName !== 'TD' && control === 0) {\n            acc.push(index)\n          }\n          control > 0 && control--\n          return acc\n        }, [])\n\n        indexes.forEach((rowIndex) => {\n          rowNum = newVal\n          while (rowNum > 0) {\n            // find from previous\n            const preChildNodes = rows[rowNum - 1]?.childNodes\n            if (\n              preChildNodes[rowIndex] &&\n              preChildNodes[rowIndex].nodeName === 'TD' &&\n              preChildNodes[rowIndex].rowSpan > 1\n            ) {\n              addClass(preChildNodes[rowIndex], 'hover-cell')\n              hoveredCellList.push(preChildNodes[rowIndex])\n              break\n            }\n            rowNum--\n          }\n        })\n      } else {\n        hoveredCellList.forEach((item) => removeClass(item, 'hover-cell'))\n        hoveredCellList.length = 0\n      }\n      if (!props.store.states.isComplex.value || !isClient) return\n\n      rAF(() => {\n        // just get first level children; fix #9723\n        const oldRow = rows[oldVal]\n        const newRow = rows[newVal]\n        // when there is fixed row, hover on rowSpan > 1 should not clear the class\n        if (oldRow && !oldRow.classList.contains('hover-fixed-row')) {\n          removeClass(oldRow, 'hover-row')\n        }\n        if (newRow) {\n          addClass(newRow, 'hover-row')\n        }\n      })\n    })\n\n    onUnmounted(() => {\n      removePopper?.()\n    })\n\n    return {\n      ns,\n      onColumnsChange,\n      onScrollableChange,\n      wrappedRowRender,\n      tooltipContent,\n      tooltipTrigger,\n    }\n  },\n  render() {\n    const { wrappedRowRender, store } = this\n    const data = store.states.data.value || []\n    // Why do we need tabIndex: -1 ?\n    // If you set the tabindex attribute on an element ,\n    // then its child content cannot be scrolled with the arrow keys,\n    // unless you set tabindex on the content too\n    // See https://github.com/facebook/react/issues/25462#issuecomment-1274775248 or https://developer.mozilla.org/zh-CN/docs/Web/HTML/Global_attributes/tabindex\n    return h('tbody', { tabIndex: -1 }, [\n      data.reduce((acc: VNode[], row) => {\n        return acc.concat(wrappedRowRender(row, acc.length))\n      }, []),\n    ])\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;AAeA,IAAAA,SAAA,GAAeC,eAAe,CAAC;EAC7BC,IAAI,EAAE,aAAa;EACnBC,KAAK,EAAEC,YAAY;EACnBC,KAAKA,CAACF,KAAK,EAAE;IACX,MAAMG,QAAQ,GAAGC,kBAAkB,EAAE;IACrC,MAAMC,MAAM,GAAGC,MAAM,CAACC,mBAAmB,CAAC;IAC1C,MAAMC,EAAE,GAAGC,YAAY,CAAC,OAAO,CAAC;IAChC,MAAM;MAAEC,gBAAgB;MAAEC,cAAc;MAAEC;IAAc,CAAE,GAAGC,SAAS,CAACb,KAAK,CAAC;IAC7E,MAAM;MAAEc,eAAe;MAAEC;IAAkB,CAAE,GAAGC,iBAAiB,CAACX,MAAM,CAAC;IACzE,MAAMY,eAAe,GAAG,EAAE;IAC1BC,KAAK,CAAClB,KAAK,CAACmB,KAAK,CAACC,MAAM,CAACC,QAAQ,EAAE,CAACC,MAAM,EAAEC,MAAM,KAAK;MACrD,IAAIC,EAAE;MACN,MAAMC,EAAE,GAAGtB,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACuB,KAAK,CAACD,EAAE;MACxD,MAAME,IAAI,GAAGC,KAAK,CAACC,IAAI,CAAC,CAACJ,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,QAAQ,KAAK,EAAE,CAAC,CAACC,MAAM,CAAEC,CAAC,IAAKA,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACC,SAAS,CAACC,QAAQ,CAAC,GAAG1B,EAAE,CAACwB,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;MAC7I,IAAIG,MAAM,GAAGb,MAAM;MACnB,MAAMc,UAAU,GAAG,CAACZ,EAAE,GAAGG,IAAI,CAACQ,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGX,EAAE,CAACY,UAAU;MACvE,IAAIA,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACC,MAAM,EAAE;QACnD,IAAIC,OAAO,GAAG,CAAC;QACf,MAAMC,OAAO,GAAGX,KAAK,CAACC,IAAI,CAACO,UAAU,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,EAAEC,KAAK,KAAK;UAClE,IAAIC,GAAG,EAAEC,EAAE;UACX,IAAI,CAAC,CAACD,GAAG,GAAGR,UAAU,CAACO,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,GAAG,CAACE,OAAO,IAAI,CAAC,EAAE;YAClER,OAAO,GAAG,CAACO,EAAE,GAAGT,UAAU,CAACO,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,EAAE,CAACC,OAAO;UAC5E;UACU,IAAIJ,IAAI,CAACK,QAAQ,KAAK,IAAI,IAAIT,OAAO,KAAK,CAAC,EAAE;YAC3CG,GAAG,CAACO,IAAI,CAACL,KAAK,CAAC;UAC3B;UACUL,OAAO,GAAG,CAAC,IAAIA,OAAO,EAAE;UACxB,OAAOG,GAAG;QACpB,CAAS,EAAE,EAAE,CAAC;QACNF,OAAO,CAACU,OAAO,CAAEC,QAAQ,IAAK;UAC5B,IAAIN,GAAG;UACPT,MAAM,GAAGb,MAAM;UACf,OAAOa,MAAM,GAAG,CAAC,EAAE;YACjB,MAAMgB,aAAa,GAAG,CAACP,GAAG,GAAGjB,IAAI,CAACQ,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGS,GAAG,CAACR,UAAU;YAChF,IAAIe,aAAa,CAACD,QAAQ,CAAC,IAAIC,aAAa,CAACD,QAAQ,CAAC,CAACH,QAAQ,KAAK,IAAI,IAAII,aAAa,CAACD,QAAQ,CAAC,CAACE,OAAO,GAAG,CAAC,EAAE;cAC/GC,QAAQ,CAACF,aAAa,CAACD,QAAQ,CAAC,EAAE,YAAY,CAAC;cAC/CjC,eAAe,CAAC+B,IAAI,CAACG,aAAa,CAACD,QAAQ,CAAC,CAAC;cAC7C;YACd;YACYf,MAAM,EAAE;UACpB;QACA,CAAS,CAAC;MACV,CAAO,MAAM;QACLlB,eAAe,CAACgC,OAAO,CAAEP,IAAI,IAAKY,WAAW,CAACZ,IAAI,EAAE,YAAY,CAAC,CAAC;QAClEzB,eAAe,CAACoB,MAAM,GAAG,CAAC;MAClC;MACM,IAAI,CAACrC,KAAK,CAACmB,KAAK,CAACC,MAAM,CAACmC,SAAS,CAACC,KAAK,IAAI,CAACC,QAAQ,EAClD;MACFC,GAAG,CAAC,MAAM;QACR,MAAMC,MAAM,GAAGhC,IAAI,CAACJ,MAAM,CAAC;QAC3B,MAAMqC,MAAM,GAAGjC,IAAI,CAACL,MAAM,CAAC;QAC3B,IAAIqC,MAAM,IAAI,CAACA,MAAM,CAAC1B,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAC3DoB,WAAW,CAACK,MAAM,EAAE,WAAW,CAAC;QAC1C;QACQ,IAAIC,MAAM,EAAE;UACVP,QAAQ,CAACO,MAAM,EAAE,WAAW,CAAC;QACvC;MACA,CAAO,CAAC;IACR,CAAK,CAAC;IACFC,WAAW,CAAC,MAAM;MAChB,IAAIrC,EAAE;MACN,CAACA,EAAE,GAAGsC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGtC,EAAE,EAAE;IACjD,CAAK,CAAC;IACF,OAAO;MACLhB,EAAE;MACFM,eAAe;MACfC,kBAAkB;MAClBL,gBAAgB;MAChBC,cAAc;MACdC;IACN,CAAK;EACL,CAAG;EACDmD,MAAMA,CAAA,EAAG;IACP,MAAM;MAAErD,gBAAgB;MAAES;IAAK,CAAE,GAAG,IAAI;IACxC,MAAM6C,IAAI,GAAG7C,KAAK,CAACC,MAAM,CAAC4C,IAAI,CAACR,KAAK,IAAI,EAAE;IAC1C,OAAOS,CAAC,CAAC,OAAO,EAAE;MAAEC,QAAQ,EAAE,CAAC;IAAC,CAAE,EAAE,CAClCF,IAAI,CAACxB,MAAM,CAAC,CAACC,GAAG,EAAE0B,GAAG,KAAK;MACxB,OAAO1B,GAAG,CAAC2B,MAAM,CAAC1D,gBAAgB,CAACyD,GAAG,EAAE1B,GAAG,CAACJ,MAAM,CAAC,CAAC;IAC5D,CAAO,EAAE,EAAE,CAAC,CACP,CAAC;EACN;AACA,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}