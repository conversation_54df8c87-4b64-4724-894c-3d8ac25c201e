{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withCtx as _withCtx, resolveDirective as _resolveDirective, openBlock as _openBlock, createBlock as _createBlock, withDirectives as _withDirectives, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"operation-records-container\"\n};\nconst _hoisted_2 = {\n  class: \"pagination-container\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => _cache[2] || (_cache[2] = [_createElementVNode(\"div\", {\n      class: \"card-header\"\n    }, [_createElementVNode(\"h2\", null, \"操作记录\")], -1 /* HOISTED */)])),\n    default: _withCtx(() => [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n      data: $setup.records,\n      style: {\n        \"width\": \"100%\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        prop: \"id\",\n        label: \"ID\",\n        width: \"80\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"operation_type\",\n        label: \"操作类型\",\n        width: \"120\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"operation_time\",\n        label: \"操作时间\",\n        width: \"180\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"operation_content\",\n        label: \"操作内容\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"status\",\n        label: \"状态\",\n        width: \"100\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_tag, {\n          type: scope.row.status === '成功' ? 'success' : 'danger'\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(scope.row.status), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])), [[_directive_loading, $setup.loading]]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_pagination, {\n      \"current-page\": $setup.currentPage,\n      \"onUpdate:currentPage\": _cache[0] || (_cache[0] = $event => $setup.currentPage = $event),\n      \"page-size\": $setup.pageSize,\n      \"onUpdate:pageSize\": _cache[1] || (_cache[1] = $event => $setup.pageSize = $event),\n      \"page-sizes\": [10, 20, 50, 100],\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      total: $setup.total,\n      onSizeChange: $setup.handleSizeChange,\n      onCurrentChange: $setup.handleCurrentChange\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_cache", "_createElementVNode", "default", "_createBlock", "_component_el_table", "data", "$setup", "records", "style", "_component_el_table_column", "prop", "label", "width", "scope", "_component_el_tag", "type", "row", "status", "_createTextVNode", "_toDisplayString", "_", "loading", "_hoisted_2", "_component_el_pagination", "currentPage", "$event", "pageSize", "layout", "total", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\OperationRecords.vue"], "sourcesContent": ["<template>\n  <div class=\"operation-records-container\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h2>操作记录</h2>\n        </div>\n      </template>\n      \n      <el-table\n        :data=\"records\"\n        style=\"width: 100%\"\n        v-loading=\"loading\"\n      >\n        <el-table-column\n          prop=\"id\"\n          label=\"ID\"\n          width=\"80\"\n        />\n        <el-table-column\n          prop=\"operation_type\"\n          label=\"操作类型\"\n          width=\"120\"\n        />\n        <el-table-column\n          prop=\"operation_time\"\n          label=\"操作时间\"\n          width=\"180\"\n        />\n        <el-table-column\n          prop=\"operation_content\"\n          label=\"操作内容\"\n        />\n        <el-table-column\n          prop=\"status\"\n          label=\"状态\"\n          width=\"100\"\n        >\n          <template #default=\"scope\">\n            <el-tag\n              :type=\"scope.row.status === '成功' ? 'success' : 'danger'\"\n            >\n              {{ scope.row.status }}\n            </el-tag>\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <div class=\"pagination-container\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n        />\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted } from 'vue';\nimport axios from 'axios';\n\nexport default {\n  name: 'OperationRecords',\n  setup() {\n    const records = ref([]);\n    const loading = ref(true);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    const total = ref(0);\n\n    // 模拟数据\n    const mockData = [\n      {\n        id: 1,\n        operation_type: '预约座位',\n        operation_time: '2023-10-15 09:30:45',\n        operation_content: '预约了A区101号座位',\n        status: '成功'\n      },\n      {\n        id: 2,\n        operation_type: '取消预约',\n        operation_time: '2023-10-16 14:20:30',\n        operation_content: '取消了A区105号座位的预约',\n        status: '成功'\n      },\n      {\n        id: 3,\n        operation_type: '签到',\n        operation_time: '2023-10-17 08:05:12',\n        operation_content: '在B区203号座位签到',\n        status: '成功'\n      },\n      {\n        id: 4,\n        operation_type: '签退',\n        operation_time: '2023-10-17 12:30:00',\n        operation_content: '从B区203号座位签退',\n        status: '成功'\n      },\n      {\n        id: 5,\n        operation_type: '预约座位',\n        operation_time: '2023-10-18 16:45:22',\n        operation_content: '预约了C区301号座位',\n        status: '失败'\n      }\n    ];\n\n    const fetchRecords = () => {\n      loading.value = true;\n      // 模拟API请求\n      setTimeout(() => {\n        records.value = mockData;\n        total.value = mockData.length;\n        loading.value = false;\n      }, 500);\n    };\n\n    const handleSizeChange = (val) => {\n      pageSize.value = val;\n      fetchRecords();\n    };\n\n    const handleCurrentChange = (val) => {\n      currentPage.value = val;\n      fetchRecords();\n    };\n\n    onMounted(() => {\n      fetchRecords();\n    });\n\n    return {\n      records,\n      loading,\n      currentPage,\n      pageSize,\n      total,\n      handleSizeChange,\n      handleCurrentChange\n    };\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.operation-records-container {\n  padding: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA6B;;EA+C/BA,KAAK,EAAC;AAAsB;;;;;;;;uBA/CrCC,mBAAA,CA2DM,OA3DNC,UA2DM,GA1DJC,YAAA,CAyDUC,kBAAA;IAxDGC,MAAM,EAAAC,QAAA,CACf,MAEMC,MAAA,QAAAA,MAAA,OAFNC,mBAAA,CAEM;MAFDR,KAAK,EAAC;IAAa,IACtBQ,mBAAA,CAAa,YAAT,MAAI,E;IALlBC,OAAA,EAAAH,QAAA,CASM,MAqCW,C,+BArCXI,YAAA,CAqCWC,mBAAA;MApCRC,IAAI,EAAEC,MAAA,CAAAC,OAAO;MACdC,KAAmB,EAAnB;QAAA;MAAA;;MAXRN,OAAA,EAAAH,QAAA,CAcQ,MAIE,CAJFH,YAAA,CAIEa,0BAAA;QAHAC,IAAI,EAAC,IAAI;QACTC,KAAK,EAAC,IAAI;QACVC,KAAK,EAAC;UAERhB,YAAA,CAIEa,0BAAA;QAHAC,IAAI,EAAC,gBAAgB;QACrBC,KAAK,EAAC,MAAM;QACZC,KAAK,EAAC;UAERhB,YAAA,CAIEa,0BAAA;QAHAC,IAAI,EAAC,gBAAgB;QACrBC,KAAK,EAAC,MAAM;QACZC,KAAK,EAAC;UAERhB,YAAA,CAGEa,0BAAA;QAFAC,IAAI,EAAC,mBAAmB;QACxBC,KAAK,EAAC;UAERf,YAAA,CAYkBa,0BAAA;QAXhBC,IAAI,EAAC,QAAQ;QACbC,KAAK,EAAC,IAAI;QACVC,KAAK,EAAC;;QAEKV,OAAO,EAAAH,QAAA,CAKPc,KALc,KACvBjB,YAAA,CAISkB,iBAAA;UAHNC,IAAI,EAAEF,KAAK,CAACG,GAAG,CAACC,MAAM;;UAxCrCf,OAAA,EAAAH,QAAA,CA0Cc,MAAsB,CA1CpCmB,gBAAA,CAAAC,gBAAA,CA0CiBN,KAAK,CAACG,GAAG,CAACC,MAAM,iB;UA1CjCG,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;wDAYmBd,MAAA,CAAAe,OAAO,E,GAoCpBpB,mBAAA,CAUM,OAVNqB,UAUM,GATJ1B,YAAA,CAQE2B,wBAAA;MAPQ,cAAY,EAAEjB,MAAA,CAAAkB,WAAW;MAlD3C,wBAAAxB,MAAA,QAAAA,MAAA,MAAAyB,MAAA,IAkDgCnB,MAAA,CAAAkB,WAAW,GAAAC,MAAA;MACzB,WAAS,EAAEnB,MAAA,CAAAoB,QAAQ;MAnDrC,qBAAA1B,MAAA,QAAAA,MAAA,MAAAyB,MAAA,IAmD6BnB,MAAA,CAAAoB,QAAQ,GAAAD,MAAA;MAC1B,YAAU,EAAE,iBAAiB;MAC9BE,MAAM,EAAC,yCAAyC;MAC/CC,KAAK,EAAEtB,MAAA,CAAAsB,KAAK;MACZC,YAAW,EAAEvB,MAAA,CAAAwB,gBAAgB;MAC7BC,eAAc,EAAEzB,MAAA,CAAA0B;;IAxD3BZ,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}