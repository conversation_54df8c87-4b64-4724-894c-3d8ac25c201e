{"ast": null, "code": "module.exports = {\n  sm2: require('./sm2/index'),\n  sm3: require('./sm3/index'),\n  sm4: require('./sm4/index')\n};", "map": {"version": 3, "names": ["module", "exports", "sm2", "require", "sm3", "sm4"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/sm-crypto/src/index.js"], "sourcesContent": ["module.exports = {\n  sm2: require('./sm2/index'),\n  sm3: require('./sm3/index'),\n  sm4: require('./sm4/index'),\n}\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG;EACfC,GAAG,EAAEC,OAAO,CAAC,aAAa,CAAC;EAC3BC,GAAG,EAAED,OAAO,CAAC,aAAa,CAAC;EAC3BE,GAAG,EAAEF,OAAO,CAAC,aAAa;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}