{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, unref, toDisplayString, createBlock, withCtx, resolveDynamicComponent } from 'vue';\nimport { ElIcon } from '../../../icon/index.mjs';\nimport { paginationNextProps } from './next.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPaginationNext\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: paginationNextProps,\n  emits: [\"click\"],\n  setup(__props) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const internalDisabled = computed(() => props.disabled || props.currentPage === props.pageCount || props.pageCount === 0);\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"button\", {\n        type: \"button\",\n        class: \"btn-next\",\n        disabled: unref(internalDisabled),\n        \"aria-label\": _ctx.nextText || unref(t)(\"el.pagination.next\"),\n        \"aria-disabled\": unref(internalDisabled),\n        onClick: $event => _ctx.$emit(\"click\", $event)\n      }, [_ctx.nextText ? (openBlock(), createElementBlock(\"span\", {\n        key: 0\n      }, toDisplayString(_ctx.nextText), 1)) : (openBlock(), createBlock(unref(ElIcon), {\n        key: 1\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.nextIcon)))]),\n        _: 1\n      }))], 8, [\"disabled\", \"aria-label\", \"aria-disabled\", \"onClick\"]);\n    };\n  }\n});\nvar Next = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"next.vue\"]]);\nexport { Next as default };", "map": {"version": 3, "names": ["name", "t", "useLocale", "internalDisabled", "computed", "props", "disabled", "currentPage", "pageCount", "_ctx", "_cache", "openBlock", "createElementBlock"], "sources": ["../../../../../../../packages/components/pagination/src/components/next.vue"], "sourcesContent": ["<template>\n  <button\n    type=\"button\"\n    class=\"btn-next\"\n    :disabled=\"internalDisabled\"\n    :aria-label=\"nextText || t('el.pagination.next')\"\n    :aria-disabled=\"internalDisabled\"\n    @click=\"$emit('click', $event)\"\n  >\n    <span v-if=\"nextText\">{{ nextText }}</span>\n    <el-icon v-else>\n      <component :is=\"nextIcon\" />\n    </el-icon>\n  </button>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useLocale } from '@element-plus/hooks'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { paginationNextProps } from './next'\n\ndefineOptions({\n  name: 'ElPaginationNext',\n})\n\nconst props = defineProps(paginationNextProps)\n\ndefineEmits(['click'])\n\nconst { t } = useLocale()\n\nconst internalDisabled = computed(\n  () =>\n    props.disabled ||\n    props.currentPage === props.pageCount ||\n    props.pageCount === 0\n)\n</script>\n"], "mappings": ";;;;;mCAsBc;EACZA,IAAM;AACR;;;;;;;IAMM;MAAEC;IAAE,IAAIC,SAAU;IAExB,MAAMC,gBAAmB,GAAAC,QAAA,OAAAC,KAAA,CAAAC,QAAA,IAAAD,KAAA,CAAAE,WAAA,KAAAF,KAAA,CAAAG,SAAA,IAAAH,KAAA,CAAAG,SAAA;IACvB,QACEC,IAAA,EAAMC,MACN;MAEJ,OAAAC,SAAA,IAAAC,kBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}