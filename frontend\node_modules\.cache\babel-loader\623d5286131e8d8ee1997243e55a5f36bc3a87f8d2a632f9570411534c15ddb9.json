{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst popperArrowProps = buildProps({\n  arrowOffset: {\n    type: Number,\n    default: 5\n  }\n});\nconst usePopperArrowProps = popperArrowProps;\nexport { popperArrowProps, usePopperArrowProps };", "map": {"version": 3, "names": ["popperArrowProps", "buildProps", "arrowOffset", "type", "Number", "default", "usePopperArrowProps"], "sources": ["../../../../../../packages/components/popper/src/arrow.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Arrow from './arrow.vue'\n\nexport const popperArrowProps = buildProps({\n  arrowOffset: {\n    type: Number,\n    default: 5,\n  },\n} as const)\nexport type PopperArrowProps = ExtractPropTypes<typeof popperArrowProps>\n\nexport type PopperArrowInstance = InstanceType<typeof Arrow> & unknown\n\n/** @deprecated use `popperArrowProps` instead, and it will be deprecated in the next major version */\nexport const usePopperArrowProps = popperArrowProps\n\n/** @deprecated use `PopperArrowProps` instead, and it will be deprecated in the next major version */\nexport type UsePopperArrowProps = PopperArrowProps\n\n/** @deprecated use `PopperArrowInstance` instead, and it will be deprecated in the next major version */\nexport type ElPopperArrowInstance = PopperArrowInstance\n"], "mappings": ";AACY,MAACA,gBAAgB,GAAGC,UAAU,CAAC;EACzCC,WAAW,EAAE;IACXC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb;AACA,CAAC;AACW,MAACC,mBAAmB,GAAGN,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}