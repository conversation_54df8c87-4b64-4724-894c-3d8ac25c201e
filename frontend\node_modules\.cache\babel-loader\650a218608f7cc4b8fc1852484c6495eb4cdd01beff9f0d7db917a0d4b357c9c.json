{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nconst FOCUSABLE_ELEMENT_SELECTORS = `a[href],button:not([disabled]),button:not([hidden]),:not([tabindex=\"-1\"]),input:not([disabled]),input:not([type=\"hidden\"]),select:not([disabled]),textarea:not([disabled])`;\nconst isVisible = element => {\n  if (process.env.NODE_ENV === \"test\") return true;\n  const computed = getComputedStyle(element);\n  return computed.position === \"fixed\" ? false : element.offsetParent !== null;\n};\nconst obtainAllFocusableElements = element => {\n  return Array.from(element.querySelectorAll(FOCUSABLE_ELEMENT_SELECTORS)).filter(item => isFocusable(item) && isVisible(item));\n};\nconst isFocusable = element => {\n  if (element.tabIndex > 0 || element.tabIndex === 0 && element.getAttribute(\"tabIndex\") !== null) {\n    return true;\n  }\n  if (element.tabIndex < 0 || element.hasAttribute(\"disabled\") || element.getAttribute(\"aria-disabled\") === \"true\") {\n    return false;\n  }\n  switch (element.nodeName) {\n    case \"A\":\n      {\n        return !!element.href && element.rel !== \"ignore\";\n      }\n    case \"INPUT\":\n      {\n        return !(element.type === \"hidden\" || element.type === \"file\");\n      }\n    case \"BUTTON\":\n    case \"SELECT\":\n    case \"TEXTAREA\":\n      {\n        return true;\n      }\n    default:\n      {\n        return false;\n      }\n  }\n};\nconst attemptFocus = element => {\n  var _a;\n  if (!isFocusable(element)) {\n    return false;\n  }\n  (_a = element.focus) == null ? void 0 : _a.call(element);\n  return document.activeElement === element;\n};\nconst triggerEvent = function (elm, name, ...opts) {\n  let eventName;\n  if (name.includes(\"mouse\") || name.includes(\"click\")) {\n    eventName = \"MouseEvents\";\n  } else if (name.includes(\"key\")) {\n    eventName = \"KeyboardEvent\";\n  } else {\n    eventName = \"HTMLEvents\";\n  }\n  const evt = document.createEvent(eventName);\n  evt.initEvent(name, ...opts);\n  elm.dispatchEvent(evt);\n  return elm;\n};\nconst isLeaf = el => !el.getAttribute(\"aria-owns\");\nconst getSibling = (el, distance, elClass) => {\n  const {\n    parentNode\n  } = el;\n  if (!parentNode) return null;\n  const siblings = parentNode.querySelectorAll(elClass);\n  const index = Array.prototype.indexOf.call(siblings, el);\n  return siblings[index + distance] || null;\n};\nconst focusNode = el => {\n  if (!el) return;\n  el.focus();\n  !isLeaf(el) && el.click();\n};\nexport { attemptFocus, focusNode, getSibling, isFocusable, isLeaf, isVisible, obtainAllFocusableElements, triggerEvent };", "map": {"version": 3, "names": ["FOCUSABLE_ELEMENT_SELECTORS", "isVisible", "element", "process", "env", "NODE_ENV", "computed", "getComputedStyle", "position", "offsetParent", "obtainAllFocusableElements", "Array", "from", "querySelectorAll", "filter", "item", "isFocusable", "tabIndex", "getAttribute", "hasAttribute", "nodeName", "href", "rel", "type", "attemptFocus", "_a", "focus", "call", "document", "activeElement", "triggerEvent", "elm", "name", "opts", "eventName", "includes", "evt", "createEvent", "initEvent", "dispatchEvent", "<PERSON><PERSON><PERSON><PERSON>", "el", "getSibling", "distance", "elClass", "parentNode", "siblings", "index", "prototype", "indexOf", "focusNode", "click"], "sources": ["../../../../../packages/utils/dom/aria.ts"], "sourcesContent": ["const FOCUSABLE_ELEMENT_SELECTORS = `a[href],button:not([disabled]),button:not([hidden]),:not([tabindex=\"-1\"]),input:not([disabled]),input:not([type=\"hidden\"]),select:not([disabled]),textarea:not([disabled])`\n\n/**\n * Determine if the testing element is visible on screen no matter if its on the viewport or not\n */\nexport const isVisible = (element: HTMLElement) => {\n  if (process.env.NODE_ENV === 'test') return true\n  const computed = getComputedStyle(element)\n  // element.offsetParent won't work on fix positioned\n  // WARNING: potential issue here, going to need some expert advices on this issue\n  return computed.position === 'fixed' ? false : element.offsetParent !== null\n}\n\nexport const obtainAllFocusableElements = (\n  element: HTMLElement\n): HTMLElement[] => {\n  return Array.from(\n    element.querySelectorAll<HTMLElement>(FOCUSABLE_ELEMENT_SELECTORS)\n  ).filter((item: HTMLElement) => isFocusable(item) && isVisible(item))\n}\n\n/**\n * @desc Determine if target element is focusable\n * @param element {HTMLElement}\n * @returns {Boolean} true if it is focusable\n */\nexport const isFocusable = (element: HTMLElement): boolean => {\n  if (\n    element.tabIndex > 0 ||\n    (element.tabIndex === 0 && element.getAttribute('tabIndex') !== null)\n  ) {\n    return true\n  }\n  if (\n    element.tabIndex < 0 ||\n    element.hasAttribute('disabled') ||\n    element.getAttribute('aria-disabled') === 'true'\n  ) {\n    return false\n  }\n\n  switch (element.nodeName) {\n    case 'A': {\n      // casting current element to Specific HTMLElement in order to be more type precise\n      return (\n        !!(element as HTMLAnchorElement).href &&\n        (element as HTMLAnchorElement).rel !== 'ignore'\n      )\n    }\n    case 'INPUT': {\n      return !(\n        (element as HTMLInputElement).type === 'hidden' ||\n        (element as HTMLInputElement).type === 'file'\n      )\n    }\n    case 'BUTTON':\n    case 'SELECT':\n    case 'TEXTAREA': {\n      return true\n    }\n    default: {\n      return false\n    }\n  }\n}\n\n/**\n * @desc Set Attempt to set focus on the current node.\n * @param element\n *          The node to attempt to focus on.\n * @returns\n *  true if element is focused.\n */\nexport const attemptFocus = (element: HTMLElement): boolean => {\n  if (!isFocusable(element)) {\n    return false\n  }\n  // Remove the old try catch block since there will be no error to be thrown\n  element.focus?.()\n  return document.activeElement === element\n}\n\n/**\n * Trigger an event\n * mouseenter, mouseleave, mouseover, keyup, change, click, etc.\n * @param  {HTMLElement} elm\n * @param  {String} name\n * @param  {*} opts\n */\nexport const triggerEvent = function (\n  elm: HTMLElement,\n  name: string,\n  ...opts: Array<boolean>\n): HTMLElement {\n  let eventName: string\n\n  if (name.includes('mouse') || name.includes('click')) {\n    eventName = 'MouseEvents'\n  } else if (name.includes('key')) {\n    eventName = 'KeyboardEvent'\n  } else {\n    eventName = 'HTMLEvents'\n  }\n  const evt = document.createEvent(eventName)\n\n  evt.initEvent(name, ...opts)\n  elm.dispatchEvent(evt)\n  return elm\n}\n\nexport const isLeaf = (el: HTMLElement) => !el.getAttribute('aria-owns')\n\nexport const getSibling = (\n  el: HTMLElement,\n  distance: number,\n  elClass: string\n) => {\n  const { parentNode } = el\n  if (!parentNode) return null\n  const siblings = parentNode.querySelectorAll(elClass)\n  const index = Array.prototype.indexOf.call(siblings, el)\n  return siblings[index + distance] || null\n}\n\nexport const focusNode = (el: HTMLElement) => {\n  if (!el) return\n  el.focus()\n  !isLeaf(el) && el.click()\n}\n"], "mappings": ";;AAAA,MAAMA,2BAA2B,GAAG,4KAA4K;AACpM,MAACC,SAAS,GAAIC,OAAO,IAAK;EACpC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EACjC,OAAO,IAAI;EACb,MAAMC,QAAQ,GAAGC,gBAAgB,CAACL,OAAO,CAAC;EAC1C,OAAOI,QAAQ,CAACE,QAAQ,KAAK,OAAO,GAAG,KAAK,GAAGN,OAAO,CAACO,YAAY,KAAK,IAAI;AAC9E;AACY,MAACC,0BAA0B,GAAIR,OAAO,IAAK;EACrD,OAAOS,KAAK,CAACC,IAAI,CAACV,OAAO,CAACW,gBAAgB,CAACb,2BAA2B,CAAC,CAAC,CAACc,MAAM,CAAEC,IAAI,IAAKC,WAAW,CAACD,IAAI,CAAC,IAAId,SAAS,CAACc,IAAI,CAAC,CAAC;AACjI;AACY,MAACC,WAAW,GAAId,OAAO,IAAK;EACtC,IAAIA,OAAO,CAACe,QAAQ,GAAG,CAAC,IAAIf,OAAO,CAACe,QAAQ,KAAK,CAAC,IAAIf,OAAO,CAACgB,YAAY,CAAC,UAAU,CAAC,KAAK,IAAI,EAAE;IAC/F,OAAO,IAAI;EACf;EACE,IAAIhB,OAAO,CAACe,QAAQ,GAAG,CAAC,IAAIf,OAAO,CAACiB,YAAY,CAAC,UAAU,CAAC,IAAIjB,OAAO,CAACgB,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM,EAAE;IAChH,OAAO,KAAK;EAChB;EACE,QAAQhB,OAAO,CAACkB,QAAQ;IACtB,KAAK,GAAG;MAAE;QACR,OAAO,CAAC,CAAClB,OAAO,CAACmB,IAAI,IAAInB,OAAO,CAACoB,GAAG,KAAK,QAAQ;MACvD;IACI,KAAK,OAAO;MAAE;QACZ,OAAO,EAAEpB,OAAO,CAACqB,IAAI,KAAK,QAAQ,IAAIrB,OAAO,CAACqB,IAAI,KAAK,MAAM,CAAC;MACpE;IACI,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,UAAU;MAAE;QACf,OAAO,IAAI;MACjB;IACI;MAAS;QACP,OAAO,KAAK;MAClB;EACA;AACA;AACY,MAACC,YAAY,GAAItB,OAAO,IAAK;EACvC,IAAIuB,EAAE;EACN,IAAI,CAACT,WAAW,CAACd,OAAO,CAAC,EAAE;IACzB,OAAO,KAAK;EAChB;EACE,CAACuB,EAAE,GAAGvB,OAAO,CAACwB,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAACzB,OAAO,CAAC;EACxD,OAAO0B,QAAQ,CAACC,aAAa,KAAK3B,OAAO;AAC3C;AACY,MAAC4B,YAAY,GAAG,SAAAA,CAASC,GAAG,EAAEC,IAAI,EAAE,GAAGC,IAAI,EAAE;EACvD,IAAIC,SAAS;EACb,IAAIF,IAAI,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,IAAI,CAACG,QAAQ,CAAC,OAAO,CAAC,EAAE;IACpDD,SAAS,GAAG,aAAa;EAC7B,CAAG,MAAM,IAAIF,IAAI,CAACG,QAAQ,CAAC,KAAK,CAAC,EAAE;IAC/BD,SAAS,GAAG,eAAe;EAC/B,CAAG,MAAM;IACLA,SAAS,GAAG,YAAY;EAC5B;EACE,MAAME,GAAG,GAAGR,QAAQ,CAACS,WAAW,CAACH,SAAS,CAAC;EAC3CE,GAAG,CAACE,SAAS,CAACN,IAAI,EAAE,GAAGC,IAAI,CAAC;EAC5BF,GAAG,CAACQ,aAAa,CAACH,GAAG,CAAC;EACtB,OAAOL,GAAG;AACZ;AACY,MAACS,MAAM,GAAIC,EAAE,IAAK,CAACA,EAAE,CAACvB,YAAY,CAAC,WAAW;AAC9C,MAACwB,UAAU,GAAGA,CAACD,EAAE,EAAEE,QAAQ,EAAEC,OAAO,KAAK;EACnD,MAAM;IAAEC;EAAU,CAAE,GAAGJ,EAAE;EACzB,IAAI,CAACI,UAAU,EACb,OAAO,IAAI;EACb,MAAMC,QAAQ,GAAGD,UAAU,CAAChC,gBAAgB,CAAC+B,OAAO,CAAC;EACrD,MAAMG,KAAK,GAAGpC,KAAK,CAACqC,SAAS,CAACC,OAAO,CAACtB,IAAI,CAACmB,QAAQ,EAAEL,EAAE,CAAC;EACxD,OAAOK,QAAQ,CAACC,KAAK,GAAGJ,QAAQ,CAAC,IAAI,IAAI;AAC3C;AACY,MAACO,SAAS,GAAIT,EAAE,IAAK;EAC/B,IAAI,CAACA,EAAE,EACL;EACFA,EAAE,CAACf,KAAK,EAAE;EACV,CAACc,MAAM,CAACC,EAAE,CAAC,IAAIA,EAAE,CAACU,KAAK,EAAE;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}