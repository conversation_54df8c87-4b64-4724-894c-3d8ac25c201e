{"ast": null, "code": "import { radioPropsBase } from './radio.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst radioButtonProps = buildProps({\n  ...radioPropsBase\n});\nexport { radioButtonProps };", "map": {"version": 3, "names": ["radioButtonProps", "buildProps", "radioPropsBase"], "sources": ["../../../../../../packages/components/radio/src/radio-button.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { radioPropsBase } from './radio'\nimport type { ExtractPropTypes } from 'vue'\nimport type RadioButton from './radio-button.vue'\n\nexport const radioButtonProps = buildProps({\n  ...radioPropsBase,\n} as const)\n\nexport type RadioButtonProps = ExtractPropTypes<typeof radioButtonProps>\nexport type RadioButtonInstance = InstanceType<typeof RadioButton> & unknown\n"], "mappings": ";;AAEY,MAACA,gBAAgB,GAAGC,UAAU,CAAC;EACzC,GAAGC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}