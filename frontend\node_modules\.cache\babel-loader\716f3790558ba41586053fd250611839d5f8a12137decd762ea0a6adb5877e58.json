{"ast": null, "code": "import { definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nconst classType = String;\nconst columns = {\n  type: definePropType(Array),\n  required: true\n};\nconst column = {\n  type: definePropType(Object)\n};\nconst fixedDataType = {\n  type: definePropType(Array)\n};\nconst dataType = {\n  ...fixedDataType,\n  required: true\n};\nconst expandColumnKey = String;\nconst expandKeys = {\n  type: definePropType(Array),\n  default: () => mutable([])\n};\nconst requiredNumber = {\n  type: Number,\n  required: true\n};\nconst rowKey = {\n  type: definePropType([String, Number, Symbol]),\n  default: \"id\"\n};\nconst styleType = {\n  type: definePropType(Object)\n};\nexport { classType, column, columns, dataType, expandColumnKey, expandKeys, fixedDataType, requiredNumber, rowKey, styleType };", "map": {"version": 3, "names": ["classType", "String", "columns", "type", "definePropType", "Array", "required", "column", "Object", "fixedDataType", "dataType", "expandColumnKey", "expandKeys", "default", "mutable", "requiredNumber", "Number", "<PERSON><PERSON><PERSON>", "Symbol", "styleType"], "sources": ["../../../../../../packages/components/table-v2/src/common.ts"], "sourcesContent": ["import { definePropType, mutable } from '@element-plus/utils'\n\nimport type { CSSProperties } from 'vue'\nimport type { Column, KeyType } from './types'\n\nexport type AnyColumn = Column<any>\n\n/**\n * @Note even though we can use `string[] | string` as the type but for\n * convenience here we only use `string` as the acceptable value here.\n */\nexport const classType = String\n\nexport const columns = {\n  type: definePropType<AnyColumn[]>(Array),\n  required: true,\n} as const\n\nexport const column = {\n  type: definePropType<AnyColumn>(Object),\n} as const\n\nexport const fixedDataType = {\n  type: definePropType<any[]>(Array),\n} as const\n\nexport const dataType = {\n  ...fixedDataType,\n  required: true,\n} as const\n\nexport const expandColumnKey = String\n\nexport const expandKeys = {\n  type: definePropType<KeyType[]>(Array),\n  default: () => mutable([]),\n} as const\n\nexport const requiredNumber = {\n  type: Number,\n  required: true,\n} as const\n\nexport const rowKey = {\n  type: definePropType<KeyType>([String, Number, Symbol]),\n  default: 'id',\n} as const\n\n/**\n * @note even though we can use `StyleValue` but that would be difficult for us to mapping them,\n * so we only use `CSSProperties` as the acceptable value here.\n */\nexport const styleType = {\n  type: definePropType<CSSProperties>(Object),\n}\n"], "mappings": ";;AACY,MAACA,SAAS,GAAGC,MAAA;AACb,MAACC,OAAO,GAAG;EACrBC,IAAI,EAAEC,cAAc,CAACC,KAAK,CAAC;EAC3BC,QAAQ,EAAE;AACZ;AACY,MAACC,MAAM,GAAG;EACpBJ,IAAI,EAAEC,cAAc,CAACI,MAAM;AAC7B;AACY,MAACC,aAAa,GAAG;EAC3BN,IAAI,EAAEC,cAAc,CAACC,KAAK;AAC5B;AACY,MAACK,QAAQ,GAAG;EACtB,GAAGD,aAAa;EAChBH,QAAQ,EAAE;AACZ;AACY,MAACK,eAAe,GAAGV,MAAA;AACnB,MAACW,UAAU,GAAG;EACxBT,IAAI,EAAEC,cAAc,CAACC,KAAK,CAAC;EAC3BQ,OAAO,EAAEA,CAAA,KAAMC,OAAO,CAAC,EAAE;AAC3B;AACY,MAACC,cAAc,GAAG;EAC5BZ,IAAI,EAAEa,MAAM;EACZV,QAAQ,EAAE;AACZ;AACY,MAACW,MAAM,GAAG;EACpBd,IAAI,EAAEC,cAAc,CAAC,CAACH,MAAM,EAAEe,MAAM,EAAEE,MAAM,CAAC,CAAC;EAC9CL,OAAO,EAAE;AACX;AACY,MAACM,SAAS,GAAG;EACvBhB,IAAI,EAAEC,cAAc,CAACI,MAAM;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}