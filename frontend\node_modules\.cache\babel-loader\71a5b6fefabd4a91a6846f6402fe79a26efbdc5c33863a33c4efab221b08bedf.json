{"ast": null, "code": "import { cAF, rAF } from './raf.mjs';\nfunction throttleByRaf(cb) {\n  let timer = 0;\n  const throttle = (...args) => {\n    if (timer) {\n      cAF(timer);\n    }\n    timer = rAF(() => {\n      cb(...args);\n      timer = 0;\n    });\n  };\n  throttle.cancel = () => {\n    cAF(timer);\n    timer = 0;\n  };\n  return throttle;\n}\nexport { throttleByRaf };", "map": {"version": 3, "names": ["throttleByRaf", "cb", "timer", "throttle", "args", "cAF", "rAF", "cancel"], "sources": ["../../../../packages/utils/throttleByRaf.ts"], "sourcesContent": ["import { cAF, rAF } from './raf'\n\nexport function throttleByRaf(cb: (...args: any[]) => void) {\n  let timer = 0\n\n  const throttle = (...args: any[]): void => {\n    if (timer) {\n      cAF(timer)\n    }\n    timer = rAF(() => {\n      cb(...args)\n      timer = 0\n    })\n  }\n\n  throttle.cancel = () => {\n    cAF(timer)\n    timer = 0\n  }\n\n  return throttle\n}\n"], "mappings": ";AACO,SAASA,aAAaA,CAACC,EAAE,EAAE;EAChC,IAAIC,KAAK,GAAG,CAAC;EACb,MAAMC,QAAQ,GAAGA,CAAC,GAAGC,IAAI,KAAK;IAC5B,IAAIF,KAAK,EAAE;MACTG,GAAG,CAACH,KAAK,CAAC;IAChB;IACIA,KAAK,GAAGI,GAAG,CAAC,MAAM;MAChBL,EAAE,CAAC,GAAGG,IAAI,CAAC;MACXF,KAAK,GAAG,CAAC;IACf,CAAK,CAAC;EACN,CAAG;EACDC,QAAQ,CAACI,MAAM,GAAG,MAAM;IACtBF,GAAG,CAACH,KAAK,CAAC;IACVA,KAAK,GAAG,CAAC;EACb,CAAG;EACD,OAAOC,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}