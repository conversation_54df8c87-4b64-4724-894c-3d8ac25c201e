{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { isVNode, defineComponent, renderSlot, createVNode, createTextVNode } from 'vue';\nimport SpaceItem from './item.mjs';\nimport { useSpace } from './use-space.mjs';\nimport { PatchFlags, isFragment, isValidElementNode } from '../../../utils/vue/vnode.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { isString, isArray } from '@vue/shared';\nimport { componentSizes } from '../../../constants/size.mjs';\nconst spaceProps = buildProps({\n  direction: {\n    type: String,\n    values: [\"horizontal\", \"vertical\"],\n    default: \"horizontal\"\n  },\n  class: {\n    type: definePropType([String, Object, Array]),\n    default: \"\"\n  },\n  style: {\n    type: definePropType([String, Array, Object]),\n    default: \"\"\n  },\n  alignment: {\n    type: definePropType(String),\n    default: \"center\"\n  },\n  prefixCls: {\n    type: String\n  },\n  spacer: {\n    type: definePropType([Object, String, Number, Array]),\n    default: null,\n    validator: val => isVNode(val) || isNumber(val) || isString(val)\n  },\n  wrap: Boolean,\n  fill: Boolean,\n  fillRatio: {\n    type: Number,\n    default: 100\n  },\n  size: {\n    type: [String, Array, Number],\n    values: componentSizes,\n    validator: val => {\n      return isNumber(val) || isArray(val) && val.length === 2 && val.every(isNumber);\n    }\n  }\n});\nconst Space = defineComponent({\n  name: \"ElSpace\",\n  props: spaceProps,\n  setup(props, {\n    slots\n  }) {\n    const {\n      classes,\n      containerStyle,\n      itemStyle\n    } = useSpace(props);\n    function extractChildren(children, parentKey = \"\", extractedChildren = []) {\n      const {\n        prefixCls\n      } = props;\n      children.forEach((child, loopKey) => {\n        if (isFragment(child)) {\n          if (isArray(child.children)) {\n            child.children.forEach((nested, key) => {\n              if (isFragment(nested) && isArray(nested.children)) {\n                extractChildren(nested.children, `${parentKey + key}-`, extractedChildren);\n              } else {\n                extractedChildren.push(createVNode(SpaceItem, {\n                  style: itemStyle.value,\n                  prefixCls,\n                  key: `nested-${parentKey + key}`\n                }, {\n                  default: () => [nested]\n                }, PatchFlags.PROPS | PatchFlags.STYLE, [\"style\", \"prefixCls\"]));\n              }\n            });\n          }\n        } else if (isValidElementNode(child)) {\n          extractedChildren.push(createVNode(SpaceItem, {\n            style: itemStyle.value,\n            prefixCls,\n            key: `LoopKey${parentKey + loopKey}`\n          }, {\n            default: () => [child]\n          }, PatchFlags.PROPS | PatchFlags.STYLE, [\"style\", \"prefixCls\"]));\n        }\n      });\n      return extractedChildren;\n    }\n    return () => {\n      var _a;\n      const {\n        spacer,\n        direction\n      } = props;\n      const children = renderSlot(slots, \"default\", {\n        key: 0\n      }, () => []);\n      if (((_a = children.children) != null ? _a : []).length === 0) return null;\n      if (isArray(children.children)) {\n        let extractedChildren = extractChildren(children.children);\n        if (spacer) {\n          const len = extractedChildren.length - 1;\n          extractedChildren = extractedChildren.reduce((acc, child, idx) => {\n            const children2 = [...acc, child];\n            if (idx !== len) {\n              children2.push(createVNode(\"span\", {\n                style: [itemStyle.value, direction === \"vertical\" ? \"width: 100%\" : null],\n                key: idx\n              }, [isVNode(spacer) ? spacer : createTextVNode(spacer, PatchFlags.TEXT)], PatchFlags.STYLE));\n            }\n            return children2;\n          }, []);\n        }\n        return createVNode(\"div\", {\n          class: classes.value,\n          style: containerStyle.value\n        }, extractedChildren, PatchFlags.STYLE | PatchFlags.CLASS);\n      }\n      return children.children;\n    };\n  }\n});\nexport { Space as default, spaceProps };", "map": {"version": 3, "names": ["spaceProps", "buildProps", "direction", "type", "String", "values", "default", "class", "definePropType", "Object", "Array", "style", "alignment", "prefixCls", "spacer", "Number", "validator", "val", "isVNode", "isNumber", "isString", "wrap", "Boolean", "fill", "fillRatio", "size", "componentSizes", "isArray", "length", "every", "Space", "defineComponent", "name", "props", "setup", "slots", "classes", "containerStyle", "itemStyle", "useSpace", "extractChildren", "children", "parent<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "child", "loop<PERSON>ey", "isFragment", "nested", "key", "push", "createVNode", "SpaceItem", "value", "PatchFlags", "PROPS", "STYLE", "isValidElementNode", "_a", "renderSlot", "len", "reduce", "acc", "idx", "children2", "createTextVNode", "TEXT", "CLASS"], "sources": ["../../../../../../packages/components/space/src/space.ts"], "sourcesContent": ["import {\n  createTextVNode,\n  createVNode,\n  defineComponent,\n  isVNode,\n  renderSlot,\n} from 'vue'\nimport {\n  PatchFlags,\n  buildProps,\n  definePropType,\n  isArray,\n  isFragment,\n  isNumber,\n  isString,\n  isValidElementNode,\n} from '@element-plus/utils'\nimport { componentSizes } from '@element-plus/constants'\nimport Item from './item'\nimport { useSpace } from './use-space'\n\nimport type {\n  ExtractPropTypes,\n  StyleValue,\n  VNode,\n  VNodeArrayChildren,\n  VNodeChild,\n} from 'vue'\nimport type { Arrayable } from '@element-plus/utils'\nimport type { AlignItemsProperty } from 'csstype'\n\nexport const spaceProps = buildProps({\n  /**\n   * @description Placement direction\n   */\n  direction: {\n    type: String,\n    values: ['horizontal', 'vertical'],\n    default: 'horizontal',\n  },\n  /**\n   * @description Classname\n   */\n  class: {\n    type: definePropType<Arrayable<Record<string, boolean> | string>>([\n      String,\n      Object,\n      Array,\n    ]),\n    default: '',\n  },\n  /**\n   * @description Extra style rules\n   */\n  style: {\n    type: definePropType<StyleValue>([String, Array, Object]),\n    default: '',\n  },\n  /**\n   * @description Controls the alignment of items\n   */\n  alignment: {\n    type: definePropType<AlignItemsProperty>(String),\n    default: 'center',\n  },\n  /**\n   * @description Prefix for space-items\n   */\n  prefixCls: {\n    type: String,\n  },\n  /**\n   * @description Spacer\n   */\n  spacer: {\n    type: definePropType<VNodeChild>([Object, String, Number, Array]),\n    default: null,\n    validator: (val: unknown) => isVNode(val) || isNumber(val) || isString(val),\n  },\n  /**\n   * @description Auto wrapping\n   */\n  wrap: Boolean,\n  /**\n   * @description Whether to fill the container\n   */\n  fill: Boolean,\n  /**\n   * @description Ratio of fill\n   */\n  fillRatio: {\n    type: Number,\n    default: 100,\n  },\n  /**\n   * @description Spacing size\n   */\n  size: {\n    type: [String, Array, Number],\n    values: componentSizes,\n    validator: (val: unknown): val is [number, number] | number => {\n      return (\n        isNumber(val) ||\n        (isArray(val) && val.length === 2 && val.every(isNumber))\n      )\n    },\n  },\n} as const)\nexport type SpaceProps = ExtractPropTypes<typeof spaceProps>\n\nconst Space = defineComponent({\n  name: 'ElSpace',\n\n  props: spaceProps,\n\n  setup(props, { slots }) {\n    const { classes, containerStyle, itemStyle } = useSpace(props)\n\n    // retrieve the children out via a simple for loop\n    // the edge case here is that when users uses directives like <v-for>, <v-if>\n    // we need to go deeper until the child is not the Fragment type\n    function extractChildren(\n      children: VNodeArrayChildren,\n      parentKey = '',\n      extractedChildren: VNode[] = []\n    ) {\n      const { prefixCls } = props\n      children.forEach((child, loopKey) => {\n        if (isFragment(child)) {\n          if (isArray(child.children)) {\n            child.children.forEach((nested, key) => {\n              if (isFragment(nested) && isArray(nested.children)) {\n                extractChildren(\n                  nested.children,\n                  `${parentKey + key}-`,\n                  extractedChildren\n                )\n              } else {\n                extractedChildren.push(\n                  createVNode(\n                    Item,\n                    {\n                      style: itemStyle.value,\n                      prefixCls,\n                      key: `nested-${parentKey + key}`,\n                    },\n                    {\n                      default: () => [nested],\n                    },\n                    PatchFlags.PROPS | PatchFlags.STYLE,\n                    ['style', 'prefixCls']\n                  )\n                )\n              }\n            })\n          }\n          // if the current child is valid vnode, then append this current vnode\n          // to item as child node.\n        } else if (isValidElementNode(child)) {\n          extractedChildren.push(\n            createVNode(\n              Item,\n              {\n                style: itemStyle.value,\n                prefixCls,\n                key: `LoopKey${parentKey + loopKey}`,\n              },\n              {\n                default: () => [child],\n              },\n              PatchFlags.PROPS | PatchFlags.STYLE,\n              ['style', 'prefixCls']\n            )\n          )\n        }\n      })\n\n      return extractedChildren\n    }\n\n    return () => {\n      const { spacer, direction } = props\n\n      const children = renderSlot(slots, 'default', { key: 0 }, () => [])\n\n      if ((children.children ?? []).length === 0) return null\n      // loop the children, if current children is rendered via `renderList` or `<v-for>`\n      if (isArray(children.children)) {\n        let extractedChildren = extractChildren(children.children)\n\n        if (spacer) {\n          // track the current rendering index, when encounters the last element\n          // then no need to add a spacer after it.\n          const len = extractedChildren.length - 1\n          extractedChildren = extractedChildren.reduce<VNode[]>(\n            (acc, child, idx) => {\n              const children = [...acc, child]\n              if (idx !== len) {\n                children.push(\n                  createVNode(\n                    'span',\n                    // adding width 100% for vertical alignment,\n                    // when the spacer inherit the width from the\n                    // parent, this span's width was not set, so space\n                    // might disappear\n                    {\n                      style: [\n                        itemStyle.value,\n                        direction === 'vertical' ? 'width: 100%' : null,\n                      ],\n                      key: idx,\n                    },\n                    [\n                      // if spacer is already a valid vnode, then append it to the current\n                      // span element.\n                      // otherwise, treat it as string.\n                      isVNode(spacer)\n                        ? spacer\n                        : createTextVNode(spacer as string, PatchFlags.TEXT),\n                    ],\n                    PatchFlags.STYLE\n                  )\n                )\n              }\n              return children\n            },\n            []\n          )\n        }\n\n        // spacer container.\n        return createVNode(\n          'div',\n          {\n            class: classes.value,\n            style: containerStyle.value,\n          },\n          extractedChildren,\n          PatchFlags.STYLE | PatchFlags.CLASS\n        )\n      }\n\n      return children.children\n    }\n  },\n})\n\nexport type SpaceInstance = InstanceType<typeof Space> & unknown\n\nexport default Space\n"], "mappings": ";;;;;;;;;;;;;AAoBY,MAACA,UAAU,GAAGC,UAAU,CAAC;EACnCC,SAAS,EAAE;IACTC,IAAI,EAAEC,MAAM;IACZC,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;IAClCC,OAAO,EAAE;EACb,CAAG;EACDC,KAAK,EAAE;IACLJ,IAAI,EAAEK,cAAc,CAAC,CACnBJ,MAAM,EACNK,MAAM,EACNC,KAAK,CACN,CAAC;IACFJ,OAAO,EAAE;EACb,CAAG;EACDK,KAAK,EAAE;IACLR,IAAI,EAAEK,cAAc,CAAC,CAACJ,MAAM,EAAEM,KAAK,EAAED,MAAM,CAAC,CAAC;IAC7CH,OAAO,EAAE;EACb,CAAG;EACDM,SAAS,EAAE;IACTT,IAAI,EAAEK,cAAc,CAACJ,MAAM,CAAC;IAC5BE,OAAO,EAAE;EACb,CAAG;EACDO,SAAS,EAAE;IACTV,IAAI,EAAEC;EACV,CAAG;EACDU,MAAM,EAAE;IACNX,IAAI,EAAEK,cAAc,CAAC,CAACC,MAAM,EAAEL,MAAM,EAAEW,MAAM,EAAEL,KAAK,CAAC,CAAC;IACrDJ,OAAO,EAAE,IAAI;IACbU,SAAS,EAAGC,GAAG,IAAKC,OAAO,CAACD,GAAG,CAAC,IAAIE,QAAQ,CAACF,GAAG,CAAC,IAAIG,QAAQ,CAACH,GAAG;EACrE,CAAG;EACDI,IAAI,EAAEC,OAAO;EACbC,IAAI,EAAED,OAAO;EACbE,SAAS,EAAE;IACTrB,IAAI,EAAEY,MAAM;IACZT,OAAO,EAAE;EACb,CAAG;EACDmB,IAAI,EAAE;IACJtB,IAAI,EAAE,CAACC,MAAM,EAAEM,KAAK,EAAEK,MAAM,CAAC;IAC7BV,MAAM,EAAEqB,cAAc;IACtBV,SAAS,EAAGC,GAAG,IAAK;MAClB,OAAOE,QAAQ,CAACF,GAAG,CAAC,IAAIU,OAAO,CAACV,GAAG,CAAC,IAAIA,GAAG,CAACW,MAAM,KAAK,CAAC,IAAIX,GAAG,CAACY,KAAK,CAACV,QAAQ,CAAC;IACrF;EACA;AACA,CAAC;AACI,MAACW,KAAK,GAAGC,eAAe,CAAC;EAC5BC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAEjC,UAAU;EACjBkC,KAAKA,CAACD,KAAK,EAAE;IAAEE;EAAK,CAAE,EAAE;IACtB,MAAM;MAAEC,OAAO;MAAEC,cAAc;MAAEC;IAAS,CAAE,GAAGC,QAAQ,CAACN,KAAK,CAAC;IAC9D,SAASO,eAAeA,CAACC,QAAQ,EAAEC,SAAS,GAAG,EAAE,EAAEC,iBAAiB,GAAG,EAAE,EAAE;MACzE,MAAM;QAAE9B;MAAS,CAAE,GAAGoB,KAAK;MAC3BQ,QAAQ,CAACG,OAAO,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAK;QACnC,IAAIC,UAAU,CAACF,KAAK,CAAC,EAAE;UACrB,IAAIlB,OAAO,CAACkB,KAAK,CAACJ,QAAQ,CAAC,EAAE;YAC3BI,KAAK,CAACJ,QAAQ,CAACG,OAAO,CAAC,CAACI,MAAM,EAAEC,GAAG,KAAK;cACtC,IAAIF,UAAU,CAACC,MAAM,CAAC,IAAIrB,OAAO,CAACqB,MAAM,CAACP,QAAQ,CAAC,EAAE;gBAClDD,eAAe,CAACQ,MAAM,CAACP,QAAQ,EAAE,GAAGC,SAAS,GAAGO,GAAG,GAAG,EAAEN,iBAAiB,CAAC;cAC1F,CAAe,MAAM;gBACLA,iBAAiB,CAACO,IAAI,CAACC,WAAW,CAACC,SAAI,EAAE;kBACvCzC,KAAK,EAAE2B,SAAS,CAACe,KAAK;kBACtBxC,SAAS;kBACToC,GAAG,EAAE,UAAUP,SAAS,GAAGO,GAAG;gBAChD,CAAiB,EAAE;kBACD3C,OAAO,EAAEA,CAAA,KAAM,CAAC0C,MAAM;gBACxC,CAAiB,EAAEM,UAAU,CAACC,KAAK,GAAGD,UAAU,CAACE,KAAK,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;cAChF;YACA,CAAa,CAAC;UACd;QACA,CAAS,MAAM,IAAIC,kBAAkB,CAACZ,KAAK,CAAC,EAAE;UACpCF,iBAAiB,CAACO,IAAI,CAACC,WAAW,CAACC,SAAI,EAAE;YACvCzC,KAAK,EAAE2B,SAAS,CAACe,KAAK;YACtBxC,SAAS;YACToC,GAAG,EAAE,UAAUP,SAAS,GAAGI,OAAO;UAC9C,CAAW,EAAE;YACDxC,OAAO,EAAEA,CAAA,KAAM,CAACuC,KAAK;UACjC,CAAW,EAAES,UAAU,CAACC,KAAK,GAAGD,UAAU,CAACE,KAAK,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;QAC1E;MACA,CAAO,CAAC;MACF,OAAOb,iBAAiB;IAC9B;IACI,OAAO,MAAM;MACX,IAAIe,EAAE;MACN,MAAM;QAAE5C,MAAM;QAAEZ;MAAS,CAAE,GAAG+B,KAAK;MACnC,MAAMQ,QAAQ,GAAGkB,UAAU,CAACxB,KAAK,EAAE,SAAS,EAAE;QAAEc,GAAG,EAAE;MAAC,CAAE,EAAE,MAAM,EAAE,CAAC;MACnE,IAAI,CAAC,CAACS,EAAE,GAAGjB,QAAQ,CAACA,QAAQ,KAAK,IAAI,GAAGiB,EAAE,GAAG,EAAE,EAAE9B,MAAM,KAAK,CAAC,EAC3D,OAAO,IAAI;MACb,IAAID,OAAO,CAACc,QAAQ,CAACA,QAAQ,CAAC,EAAE;QAC9B,IAAIE,iBAAiB,GAAGH,eAAe,CAACC,QAAQ,CAACA,QAAQ,CAAC;QAC1D,IAAI3B,MAAM,EAAE;UACV,MAAM8C,GAAG,GAAGjB,iBAAiB,CAACf,MAAM,GAAG,CAAC;UACxCe,iBAAiB,GAAGA,iBAAiB,CAACkB,MAAM,CAAC,CAACC,GAAG,EAAEjB,KAAK,EAAEkB,GAAG,KAAK;YAChE,MAAMC,SAAS,GAAG,CAAC,GAAGF,GAAG,EAAEjB,KAAK,CAAC;YACjC,IAAIkB,GAAG,KAAKH,GAAG,EAAE;cACfI,SAAS,CAACd,IAAI,CAACC,WAAW,CAAC,MAAM,EAAE;gBACjCxC,KAAK,EAAE,CACL2B,SAAS,CAACe,KAAK,EACfnD,SAAS,KAAK,UAAU,GAAG,aAAa,GAAG,IAAI,CAChD;gBACD+C,GAAG,EAAEc;cACrB,CAAe,EAAE,CACD7C,OAAO,CAACJ,MAAM,CAAC,GAAGA,MAAM,GAAGmD,eAAe,CAACnD,MAAM,EAAEwC,UAAU,CAACY,IAAI,CAAC,CACpE,EAAEZ,UAAU,CAACE,KAAK,CAAC,CAAC;YACnC;YACY,OAAOQ,SAAS;UAC5B,CAAW,EAAE,EAAE,CAAC;QAChB;QACQ,OAAOb,WAAW,CAAC,KAAK,EAAE;UACxB5C,KAAK,EAAE6B,OAAO,CAACiB,KAAK;UACpB1C,KAAK,EAAE0B,cAAc,CAACgB;QAChC,CAAS,EAAEV,iBAAiB,EAAEW,UAAU,CAACE,KAAK,GAAGF,UAAU,CAACa,KAAK,CAAC;MAClE;MACM,OAAO1B,QAAQ,CAACA,QAAQ;IAC9B,CAAK;EACL;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}