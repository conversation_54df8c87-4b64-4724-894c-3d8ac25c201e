{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, computed, onMounted, reactive } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRoute, useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { ArrowLeft, Refresh, Location, Clock, User, InfoFilled, Lightning, Sunny } from \"@element-plus/icons-vue\";\nexport default {\n  name: \"SeatMap\",\n  setup() {\n    const store = useStore();\n    const route = useRoute();\n    const router = useRouter();\n    const loading = ref(true);\n    const room = ref(null);\n    const seats = ref([]);\n    const selectedDate = ref(formatDateForSelect(new Date()));\n    const seatDialogVisible = ref(false);\n    const selectedSeat = ref(null);\n\n    // 过滤条件\n    const filters = reactive({\n      powerOutlet: false,\n      windowSeat: false,\n      availableOnly: false\n    });\n\n    // 日期选项\n    const dateOptions = computed(() => {\n      const options = [];\n      const today = new Date();\n      for (let i = 0; i < 7; i++) {\n        const date = new Date();\n        date.setDate(today.getDate() + i);\n        options.push({\n          value: formatDateForSelect(date),\n          label: formatDateForDisplay(date)\n        });\n      }\n      return options;\n    });\n\n    // 可用座位数量\n    const availableSeats = computed(() => {\n      return seats.value.filter(seat => seat.status === \"available\").length;\n    });\n\n    // 过滤后的座位\n    const filteredSeats = computed(() => {\n      return seats.value.filter(seat => {\n        if (filters.powerOutlet && !seat.is_power_outlet) return false;\n        if (filters.windowSeat && !seat.is_window_seat) return false;\n        if (filters.availableOnly && seat.status !== \"available\") return false;\n        return true;\n      });\n    });\n\n    // 座位网格样式\n    const gridStyle = computed(() => {\n      if (!room.value) return {};\n\n      // 找出最大行和列\n      const maxRow = Math.max(...seats.value.map(seat => seat.row));\n      const maxCol = Math.max(...seats.value.map(seat => seat.column));\n      return {\n        gridTemplateRows: `repeat(${maxRow}, 60px)`,\n        gridTemplateColumns: `repeat(${maxCol}, 60px)`\n      };\n    });\n\n    // 加载自习室和座位信息\n    const loadRoomAndSeats = async () => {\n      try {\n        loading.value = true;\n        const roomId = route.query.roomId;\n        if (!roomId) {\n          ElMessage.error(\"缺少自习室ID参数\");\n          router.push(\"/seat/rooms\");\n          return;\n        }\n\n        // 加载自习室信息\n        const roomData = await store.dispatch(\"seat/getRoomById\", roomId);\n        room.value = roomData;\n\n        // 加载座位信息\n        await loadSeats();\n      } catch (error) {\n        ElMessage.error(\"加载自习室信息失败\");\n        router.push(\"/seat/rooms\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 加载座位信息\n    const loadSeats = async () => {\n      try {\n        loading.value = true;\n        const roomId = route.query.roomId;\n        if (!roomId) return;\n\n        // 加载座位信息\n        const seatsData = await store.dispatch(\"seat/getSeatsByRoom\", {\n          roomId,\n          date: selectedDate.value\n        });\n        seats.value = seatsData;\n      } catch (error) {\n        ElMessage.error(\"加载座位信息失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 应用过滤器\n    const applyFilters = () => {\n      // 过滤逻辑已在计算属性中实现\n    };\n\n    // 选择座位\n    const selectSeat = seat => {\n      selectedSeat.value = seat;\n      seatDialogVisible.value = true;\n    };\n\n    // 预约座位\n    const reserveSeat = () => {\n      if (!selectedSeat.value) return;\n      router.push({\n        path: \"/seat/reservation\",\n        query: {\n          seatId: selectedSeat.value.id,\n          date: selectedDate.value\n        }\n      });\n    };\n\n    // 获取座位类名\n    const getSeatClasses = seat => {\n      return {\n        \"seat-available\": seat.status === \"available\",\n        \"seat-occupied\": seat.status === \"occupied\",\n        \"seat-disabled\": seat.status === \"disabled\",\n        \"seat-power\": seat.is_power_outlet,\n        \"seat-window\": seat.is_window_seat\n      };\n    };\n\n    // 获取座位样式\n    const getSeatStyle = seat => {\n      return {\n        gridRow: `${seat.row} / span 1`,\n        gridColumn: `${seat.column} / span 1`\n      };\n    };\n\n    // 获取座位状态类型\n    const getSeatStatusType = status => {\n      switch (status) {\n        case \"available\":\n          return \"success\";\n        case \"occupied\":\n          return \"danger\";\n        case \"disabled\":\n          return \"info\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取座位状态文本\n    const getSeatStatusText = status => {\n      switch (status) {\n        case \"available\":\n          return \"可用\";\n        case \"occupied\":\n          return \"已占用\";\n        case \"disabled\":\n          return \"禁用\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 获取预约状态类型\n    const getReservationStatusType = status => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getReservationStatusText = status => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化时间\n    const formatTime = timeString => {\n      if (!timeString) return \"\";\n\n      // 时间格式为 \"HH:MM:SS\"，只显示 \"HH:MM\"\n      return timeString.substring(0, 5);\n    };\n\n    // 格式化日期时间\n    const formatDateTime = dateTimeString => {\n      if (!dateTimeString) return \"\";\n      const date = new Date(dateTimeString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 格式化日期（用于选择器值）\n    function formatDateForSelect(date) {\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`;\n    }\n\n    // 格式化日期（用于显示）\n    function formatDateForDisplay(date) {\n      const today = new Date();\n      const tomorrow = new Date();\n      tomorrow.setDate(today.getDate() + 1);\n      if (date.toDateString() === today.toDateString()) {\n        return \"今天\";\n      } else if (date.toDateString() === tomorrow.toDateString()) {\n        return \"明天\";\n      } else {\n        const weekdays = [\"周日\", \"周一\", \"周二\", \"周三\", \"周四\", \"周五\", \"周六\"];\n        return `${date.getMonth() + 1}月${date.getDate()}日 ${weekdays[date.getDay()]}`;\n      }\n    }\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n    onMounted(() => {\n      loadRoomAndSeats();\n    });\n    return {\n      loading,\n      room,\n      seats,\n      selectedDate,\n      dateOptions,\n      filters,\n      seatDialogVisible,\n      selectedSeat,\n      availableSeats,\n      filteredSeats,\n      gridStyle,\n      loadSeats,\n      applyFilters,\n      selectSeat,\n      reserveSeat,\n      getSeatClasses,\n      getSeatStyle,\n      getSeatStatusType,\n      getSeatStatusText,\n      getReservationStatusType,\n      getReservationStatusText,\n      formatTime,\n      formatDateTime,\n      ArrowLeft,\n      Refresh,\n      Location,\n      Clock,\n      User,\n      InfoFilled,\n      Lightning,\n      Sunny\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "reactive", "useStore", "useRoute", "useRouter", "ElMessage", "ArrowLeft", "Refresh", "Location", "Clock", "User", "InfoFilled", "Lightning", "<PERSON>", "name", "setup", "store", "route", "router", "loading", "room", "seats", "selectedDate", "formatDateForSelect", "Date", "seatDialogVisible", "selectedSeat", "filters", "powerOutlet", "windowSeat", "availableOnly", "dateOptions", "options", "today", "i", "date", "setDate", "getDate", "push", "value", "label", "formatDateForDisplay", "availableSeats", "filter", "seat", "status", "length", "filteredSeats", "is_power_outlet", "is_window_seat", "gridStyle", "maxRow", "Math", "max", "map", "row", "maxCol", "column", "gridTemplateRows", "gridTemplateColumns", "loadRoomAndSeats", "roomId", "query", "error", "roomData", "dispatch", "loadSeats", "seatsData", "applyFilters", "selectSeat", "reserveSeat", "path", "seatId", "id", "getSeatClasses", "getSeatStyle", "gridRow", "gridColumn", "getSeatStatusType", "getSeatStatusText", "getReservationStatusType", "getReservationStatusText", "formatTime", "timeString", "substring", "formatDateTime", "dateTimeString", "getFullYear", "padZero", "getMonth", "getHours", "getMinutes", "tomorrow", "toDateString", "weekdays", "getDay", "num"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\SeatMap.vue"], "sourcesContent": ["<template>\n  <div class=\"seat-map\">\n    <div class=\"page-header\">\n      <div class=\"header-left\">\n        <el-button @click=\"$router.back()\" :icon=\"ArrowLeft\">返回</el-button>\n        <h2 v-if=\"room\">{{ room.name }} - 座位图</h2>\n      </div>\n\n      <div class=\"header-right\">\n        <el-select v-model=\"selectedDate\" placeholder=\"选择日期\" @change=\"loadSeats\">\n          <el-option\n            v-for=\"date in dateOptions\"\n            :key=\"date.value\"\n            :label=\"date.label\"\n            :value=\"date.value\"\n          />\n        </el-select>\n\n        <el-button type=\"primary\" @click=\"loadSeats\">\n          <el-icon><Refresh /></el-icon>\n          刷新\n        </el-button>\n      </div>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"10\" animated />\n    </div>\n\n    <template v-else>\n      <el-card class=\"room-info-card\" shadow=\"never\">\n        <div class=\"room-info\">\n          <div class=\"info-item\">\n            <el-icon><Location /></el-icon>\n            <span>位置: {{ room?.location }}</span>\n          </div>\n\n          <div class=\"info-item\">\n            <el-icon><Clock /></el-icon>\n            <span>\n              开放时间: {{ formatTime(room?.open_time) }} -\n              {{ formatTime(room?.close_time) }}\n            </span>\n          </div>\n\n          <div class=\"info-item\">\n            <el-icon><User /></el-icon>\n            <span>容量: {{ room?.capacity }}座</span>\n          </div>\n\n          <div class=\"info-item\">\n            <el-icon><InfoFilled /></el-icon>\n            <span>可用座位: {{ availableSeats }}/{{ room?.capacity }}</span>\n          </div>\n        </div>\n\n        <div class=\"seat-legend\">\n          <div class=\"legend-item\">\n            <div class=\"seat-icon available\"></div>\n            <span>可用</span>\n          </div>\n\n          <div class=\"legend-item\">\n            <div class=\"seat-icon occupied\"></div>\n            <span>已占用</span>\n          </div>\n\n          <div class=\"legend-item\">\n            <div class=\"seat-icon disabled\"></div>\n            <span>禁用</span>\n          </div>\n\n          <div class=\"legend-item\">\n            <div class=\"seat-icon power-outlet\"></div>\n            <span>电源</span>\n          </div>\n\n          <div class=\"legend-item\">\n            <div class=\"seat-icon window\"></div>\n            <span>靠窗</span>\n          </div>\n        </div>\n      </el-card>\n\n      <div class=\"map-container\">\n        <div class=\"seat-filter\">\n          <el-checkbox v-model=\"filters.powerOutlet\" @change=\"applyFilters\">\n            只看有电源的座位\n          </el-checkbox>\n\n          <el-checkbox v-model=\"filters.windowSeat\" @change=\"applyFilters\">\n            只看靠窗座位\n          </el-checkbox>\n\n          <el-checkbox v-model=\"filters.availableOnly\" @change=\"applyFilters\">\n            只看可用座位\n          </el-checkbox>\n        </div>\n\n        <div class=\"seat-grid\" :style=\"gridStyle\">\n          <div\n            v-for=\"seat in filteredSeats\"\n            :key=\"seat.id\"\n            class=\"seat\"\n            :class=\"getSeatClasses(seat)\"\n            :style=\"getSeatStyle(seat)\"\n            @click=\"selectSeat(seat)\"\n          >\n            <div class=\"seat-number\">{{ seat.seat_number }}</div>\n            <div class=\"seat-icons\">\n              <el-icon v-if=\"seat.is_power_outlet\" class=\"power-icon\"><Lightning /></el-icon>\n              <el-icon v-if=\"seat.is_window_seat\" class=\"window-icon\"><Sunny /></el-icon>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 座位详情对话框 -->\n      <el-dialog\n        v-model=\"seatDialogVisible\"\n        :title=\"`座位详情 - ${selectedSeat?.seat_number}`\"\n        width=\"500px\"\n      >\n        <div v-if=\"selectedSeat\" class=\"seat-detail\">\n          <el-descriptions :column=\"1\" border>\n            <el-descriptions-item label=\"座位编号\">\n              {{ selectedSeat.seat_number }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"位置\">\n              {{ `${selectedSeat.row}排${selectedSeat.column}列` }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"状态\">\n              <el-tag :type=\"getSeatStatusType(selectedSeat.status)\">\n                {{ getSeatStatusText(selectedSeat.status) }}\n              </el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item label=\"设施\">\n              <el-tag v-if=\"selectedSeat.is_power_outlet\" type=\"success\" effect=\"plain\">\n                有电源\n              </el-tag>\n              <el-tag v-if=\"selectedSeat.is_window_seat\" type=\"success\" effect=\"plain\">靠窗</el-tag>\n              <span v-if=\"!selectedSeat.is_power_outlet && !selectedSeat.is_window_seat\">\n                无特殊设施\n              </span>\n            </el-descriptions-item>\n          </el-descriptions>\n\n          <div v-if=\"selectedSeat.current_reservation\" class=\"current-reservation\">\n            <h4>当前预约信息</h4>\n            <el-descriptions :column=\"1\" border>\n              <el-descriptions-item label=\"预约状态\">\n                <el-tag :type=\"getReservationStatusType(selectedSeat.current_reservation.status)\">\n                  {{ getReservationStatusText(selectedSeat.current_reservation.status) }}\n                </el-tag>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"开始时间\">\n                {{ formatDateTime(selectedSeat.current_reservation.start_time) }}\n              </el-descriptions-item>\n              <el-descriptions-item label=\"结束时间\">\n                {{ formatDateTime(selectedSeat.current_reservation.end_time) }}\n              </el-descriptions-item>\n            </el-descriptions>\n          </div>\n\n          <div v-if=\"selectedSeat.status === 'available'\" class=\"seat-actions\">\n            <el-button type=\"primary\" @click=\"reserveSeat\">预约此座位</el-button>\n          </div>\n        </div>\n      </el-dialog>\n    </template>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted, reactive } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRoute, useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport {\n  ArrowLeft,\n  Refresh,\n  Location,\n  Clock,\n  User,\n  InfoFilled,\n  Lightning,\n  Sunny,\n} from \"@element-plus/icons-vue\";\n\nexport default {\n  name: \"SeatMap\",\n  setup() {\n    const store = useStore();\n    const route = useRoute();\n    const router = useRouter();\n\n    const loading = ref(true);\n    const room = ref(null);\n    const seats = ref([]);\n    const selectedDate = ref(formatDateForSelect(new Date()));\n    const seatDialogVisible = ref(false);\n    const selectedSeat = ref(null);\n\n    // 过滤条件\n    const filters = reactive({\n      powerOutlet: false,\n      windowSeat: false,\n      availableOnly: false,\n    });\n\n    // 日期选项\n    const dateOptions = computed(() => {\n      const options = [];\n      const today = new Date();\n\n      for (let i = 0; i < 7; i++) {\n        const date = new Date();\n        date.setDate(today.getDate() + i);\n\n        options.push({\n          value: formatDateForSelect(date),\n          label: formatDateForDisplay(date),\n        });\n      }\n\n      return options;\n    });\n\n    // 可用座位数量\n    const availableSeats = computed(() => {\n      return seats.value.filter((seat) => seat.status === \"available\").length;\n    });\n\n    // 过滤后的座位\n    const filteredSeats = computed(() => {\n      return seats.value.filter((seat) => {\n        if (filters.powerOutlet && !seat.is_power_outlet) return false;\n        if (filters.windowSeat && !seat.is_window_seat) return false;\n        if (filters.availableOnly && seat.status !== \"available\") return false;\n        return true;\n      });\n    });\n\n    // 座位网格样式\n    const gridStyle = computed(() => {\n      if (!room.value) return {};\n\n      // 找出最大行和列\n      const maxRow = Math.max(...seats.value.map((seat) => seat.row));\n      const maxCol = Math.max(...seats.value.map((seat) => seat.column));\n\n      return {\n        gridTemplateRows: `repeat(${maxRow}, 60px)`,\n        gridTemplateColumns: `repeat(${maxCol}, 60px)`,\n      };\n    });\n\n    // 加载自习室和座位信息\n    const loadRoomAndSeats = async () => {\n      try {\n        loading.value = true;\n\n        const roomId = route.query.roomId;\n        if (!roomId) {\n          ElMessage.error(\"缺少自习室ID参数\");\n          router.push(\"/seat/rooms\");\n          return;\n        }\n\n        // 加载自习室信息\n        const roomData = await store.dispatch(\"seat/getRoomById\", roomId);\n        room.value = roomData;\n\n        // 加载座位信息\n        await loadSeats();\n      } catch (error) {\n        ElMessage.error(\"加载自习室信息失败\");\n        router.push(\"/seat/rooms\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 加载座位信息\n    const loadSeats = async () => {\n      try {\n        loading.value = true;\n\n        const roomId = route.query.roomId;\n        if (!roomId) return;\n\n        // 加载座位信息\n        const seatsData = await store.dispatch(\"seat/getSeatsByRoom\", {\n          roomId,\n          date: selectedDate.value,\n        });\n\n        seats.value = seatsData;\n      } catch (error) {\n        ElMessage.error(\"加载座位信息失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 应用过滤器\n    const applyFilters = () => {\n      // 过滤逻辑已在计算属性中实现\n    };\n\n    // 选择座位\n    const selectSeat = (seat) => {\n      selectedSeat.value = seat;\n      seatDialogVisible.value = true;\n    };\n\n    // 预约座位\n    const reserveSeat = () => {\n      if (!selectedSeat.value) return;\n\n      router.push({\n        path: \"/seat/reservation\",\n        query: {\n          seatId: selectedSeat.value.id,\n          date: selectedDate.value,\n        },\n      });\n    };\n\n    // 获取座位类名\n    const getSeatClasses = (seat) => {\n      return {\n        \"seat-available\": seat.status === \"available\",\n        \"seat-occupied\": seat.status === \"occupied\",\n        \"seat-disabled\": seat.status === \"disabled\",\n        \"seat-power\": seat.is_power_outlet,\n        \"seat-window\": seat.is_window_seat,\n      };\n    };\n\n    // 获取座位样式\n    const getSeatStyle = (seat) => {\n      return {\n        gridRow: `${seat.row} / span 1`,\n        gridColumn: `${seat.column} / span 1`,\n      };\n    };\n\n    // 获取座位状态类型\n    const getSeatStatusType = (status) => {\n      switch (status) {\n        case \"available\":\n          return \"success\";\n        case \"occupied\":\n          return \"danger\";\n        case \"disabled\":\n          return \"info\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取座位状态文本\n    const getSeatStatusText = (status) => {\n      switch (status) {\n        case \"available\":\n          return \"可用\";\n        case \"occupied\":\n          return \"已占用\";\n        case \"disabled\":\n          return \"禁用\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 获取预约状态类型\n    const getReservationStatusType = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getReservationStatusText = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化时间\n    const formatTime = (timeString) => {\n      if (!timeString) return \"\";\n\n      // 时间格式为 \"HH:MM:SS\"，只显示 \"HH:MM\"\n      return timeString.substring(0, 5);\n    };\n\n    // 格式化日期时间\n    const formatDateTime = (dateTimeString) => {\n      if (!dateTimeString) return \"\";\n\n      const date = new Date(dateTimeString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(\n        date.getDate()\n      )} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 格式化日期（用于选择器值）\n    function formatDateForSelect(date) {\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`;\n    }\n\n    // 格式化日期（用于显示）\n    function formatDateForDisplay(date) {\n      const today = new Date();\n      const tomorrow = new Date();\n      tomorrow.setDate(today.getDate() + 1);\n\n      if (date.toDateString() === today.toDateString()) {\n        return \"今天\";\n      } else if (date.toDateString() === tomorrow.toDateString()) {\n        return \"明天\";\n      } else {\n        const weekdays = [\"周日\", \"周一\", \"周二\", \"周三\", \"周四\", \"周五\", \"周六\"];\n        return `${date.getMonth() + 1}月${date.getDate()}日 ${weekdays[date.getDay()]}`;\n      }\n    }\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n\n    onMounted(() => {\n      loadRoomAndSeats();\n    });\n\n    return {\n      loading,\n      room,\n      seats,\n      selectedDate,\n      dateOptions,\n      filters,\n      seatDialogVisible,\n      selectedSeat,\n      availableSeats,\n      filteredSeats,\n      gridStyle,\n      loadSeats,\n      applyFilters,\n      selectSeat,\n      reserveSeat,\n      getSeatClasses,\n      getSeatStyle,\n      getSeatStatusType,\n      getSeatStatusText,\n      getReservationStatusType,\n      getReservationStatusText,\n      formatTime,\n      formatDateTime,\n      ArrowLeft,\n      Refresh,\n      Location,\n      Clock,\n      User,\n      InfoFilled,\n      Lightning,\n      Sunny,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.seat-map {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  .header-left {\n    display: flex;\n    align-items: center;\n    gap: 15px;\n\n    h2 {\n      margin: 0;\n    }\n  }\n\n  .header-right {\n    display: flex;\n    gap: 10px;\n  }\n}\n\n.loading-container {\n  padding: 40px 0;\n}\n\n.room-info-card {\n  margin-bottom: 20px;\n\n  .room-info {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 20px;\n    margin-bottom: 15px;\n\n    .info-item {\n      display: flex;\n      align-items: center;\n\n      .el-icon {\n        margin-right: 8px;\n        color: #909399;\n      }\n    }\n  }\n\n  .seat-legend {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 15px;\n\n    .legend-item {\n      display: flex;\n      align-items: center;\n\n      .seat-icon {\n        width: 20px;\n        height: 20px;\n        border-radius: 4px;\n        margin-right: 5px;\n\n        &.available {\n          background-color: #67c23a;\n        }\n\n        &.occupied {\n          background-color: #f56c6c;\n        }\n\n        &.disabled {\n          background-color: #909399;\n        }\n\n        &.power-outlet {\n          background-color: #fff;\n          border: 1px solid #67c23a;\n          position: relative;\n\n          &::after {\n            content: \"⚡\";\n            position: absolute;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n            font-size: 12px;\n          }\n        }\n\n        &.window {\n          background-color: #fff;\n          border: 1px solid #409eff;\n          position: relative;\n\n          &::after {\n            content: \"☀\";\n            position: absolute;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n            font-size: 12px;\n          }\n        }\n      }\n    }\n  }\n}\n\n.map-container {\n  background-color: #fff;\n  border-radius: 4px;\n  padding: 20px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n\n  .seat-filter {\n    display: flex;\n    gap: 20px;\n    margin-bottom: 20px;\n  }\n\n  .seat-grid {\n    display: grid;\n    gap: 10px;\n    justify-content: center;\n    margin-top: 20px;\n\n    .seat {\n      width: 50px;\n      height: 50px;\n      border-radius: 4px;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n      cursor: pointer;\n      transition: transform 0.2s;\n      position: relative;\n\n      &:hover {\n        transform: scale(1.1);\n        z-index: 1;\n      }\n\n      &.seat-available {\n        background-color: #67c23a;\n        color: #fff;\n      }\n\n      &.seat-occupied {\n        background-color: #f56c6c;\n        color: #fff;\n      }\n\n      &.seat-disabled {\n        background-color: #909399;\n        color: #fff;\n        cursor: not-allowed;\n\n        &:hover {\n          transform: none;\n        }\n      }\n\n      .seat-number {\n        font-weight: bold;\n        font-size: 14px;\n      }\n\n      .seat-icons {\n        display: flex;\n        gap: 2px;\n        margin-top: 2px;\n\n        .power-icon,\n        .window-icon {\n          font-size: 12px;\n        }\n      }\n    }\n  }\n}\n\n.seat-detail {\n  .current-reservation {\n    margin-top: 20px;\n  }\n\n  .seat-actions {\n    margin-top: 20px;\n    text-align: center;\n  }\n}\n</style>\n"], "mappings": ";;;;AA8KA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAO,QAAS,KAAK;AACxD,SAASC,QAAO,QAAS,MAAM;AAC/B,SAASC,QAAQ,EAAEC,SAAQ,QAAS,YAAY;AAChD,SAASC,SAAQ,QAAS,cAAc;AACxC,SACEC,SAAS,EACTC,OAAO,EACPC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,SAAS,EACTC,KAAK,QACA,yBAAyB;AAEhC,eAAe;EACbC,IAAI,EAAE,SAAS;EACfC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAId,QAAQ,CAAC,CAAC;IACxB,MAAMe,KAAI,GAAId,QAAQ,CAAC,CAAC;IACxB,MAAMe,MAAK,GAAId,SAAS,CAAC,CAAC;IAE1B,MAAMe,OAAM,GAAIrB,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMsB,IAAG,GAAItB,GAAG,CAAC,IAAI,CAAC;IACtB,MAAMuB,KAAI,GAAIvB,GAAG,CAAC,EAAE,CAAC;IACrB,MAAMwB,YAAW,GAAIxB,GAAG,CAACyB,mBAAmB,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC,CAAC;IACzD,MAAMC,iBAAgB,GAAI3B,GAAG,CAAC,KAAK,CAAC;IACpC,MAAM4B,YAAW,GAAI5B,GAAG,CAAC,IAAI,CAAC;;IAE9B;IACA,MAAM6B,OAAM,GAAI1B,QAAQ,CAAC;MACvB2B,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE;IACjB,CAAC,CAAC;;IAEF;IACA,MAAMC,WAAU,GAAIhC,QAAQ,CAAC,MAAM;MACjC,MAAMiC,OAAM,GAAI,EAAE;MAClB,MAAMC,KAAI,GAAI,IAAIT,IAAI,CAAC,CAAC;MAExB,KAAK,IAAIU,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAMC,IAAG,GAAI,IAAIX,IAAI,CAAC,CAAC;QACvBW,IAAI,CAACC,OAAO,CAACH,KAAK,CAACI,OAAO,CAAC,IAAIH,CAAC,CAAC;QAEjCF,OAAO,CAACM,IAAI,CAAC;UACXC,KAAK,EAAEhB,mBAAmB,CAACY,IAAI,CAAC;UAChCK,KAAK,EAAEC,oBAAoB,CAACN,IAAI;QAClC,CAAC,CAAC;MACJ;MAEA,OAAOH,OAAO;IAChB,CAAC,CAAC;;IAEF;IACA,MAAMU,cAAa,GAAI3C,QAAQ,CAAC,MAAM;MACpC,OAAOsB,KAAK,CAACkB,KAAK,CAACI,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,MAAK,KAAM,WAAW,CAAC,CAACC,MAAM;IACzE,CAAC,CAAC;;IAEF;IACA,MAAMC,aAAY,GAAIhD,QAAQ,CAAC,MAAM;MACnC,OAAOsB,KAAK,CAACkB,KAAK,CAACI,MAAM,CAAEC,IAAI,IAAK;QAClC,IAAIjB,OAAO,CAACC,WAAU,IAAK,CAACgB,IAAI,CAACI,eAAe,EAAE,OAAO,KAAK;QAC9D,IAAIrB,OAAO,CAACE,UAAS,IAAK,CAACe,IAAI,CAACK,cAAc,EAAE,OAAO,KAAK;QAC5D,IAAItB,OAAO,CAACG,aAAY,IAAKc,IAAI,CAACC,MAAK,KAAM,WAAW,EAAE,OAAO,KAAK;QACtE,OAAO,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAMK,SAAQ,GAAInD,QAAQ,CAAC,MAAM;MAC/B,IAAI,CAACqB,IAAI,CAACmB,KAAK,EAAE,OAAO,CAAC,CAAC;;MAE1B;MACA,MAAMY,MAAK,GAAIC,IAAI,CAACC,GAAG,CAAC,GAAGhC,KAAK,CAACkB,KAAK,CAACe,GAAG,CAAEV,IAAI,IAAKA,IAAI,CAACW,GAAG,CAAC,CAAC;MAC/D,MAAMC,MAAK,GAAIJ,IAAI,CAACC,GAAG,CAAC,GAAGhC,KAAK,CAACkB,KAAK,CAACe,GAAG,CAAEV,IAAI,IAAKA,IAAI,CAACa,MAAM,CAAC,CAAC;MAElE,OAAO;QACLC,gBAAgB,EAAE,UAAUP,MAAM,SAAS;QAC3CQ,mBAAmB,EAAE,UAAUH,MAAM;MACvC,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,MAAMI,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAI;QACFzC,OAAO,CAACoB,KAAI,GAAI,IAAI;QAEpB,MAAMsB,MAAK,GAAI5C,KAAK,CAAC6C,KAAK,CAACD,MAAM;QACjC,IAAI,CAACA,MAAM,EAAE;UACXxD,SAAS,CAAC0D,KAAK,CAAC,WAAW,CAAC;UAC5B7C,MAAM,CAACoB,IAAI,CAAC,aAAa,CAAC;UAC1B;QACF;;QAEA;QACA,MAAM0B,QAAO,GAAI,MAAMhD,KAAK,CAACiD,QAAQ,CAAC,kBAAkB,EAAEJ,MAAM,CAAC;QACjEzC,IAAI,CAACmB,KAAI,GAAIyB,QAAQ;;QAErB;QACA,MAAME,SAAS,CAAC,CAAC;MACnB,EAAE,OAAOH,KAAK,EAAE;QACd1D,SAAS,CAAC0D,KAAK,CAAC,WAAW,CAAC;QAC5B7C,MAAM,CAACoB,IAAI,CAAC,aAAa,CAAC;MAC5B,UAAU;QACRnB,OAAO,CAACoB,KAAI,GAAI,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAM2B,SAAQ,GAAI,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF/C,OAAO,CAACoB,KAAI,GAAI,IAAI;QAEpB,MAAMsB,MAAK,GAAI5C,KAAK,CAAC6C,KAAK,CAACD,MAAM;QACjC,IAAI,CAACA,MAAM,EAAE;;QAEb;QACA,MAAMM,SAAQ,GAAI,MAAMnD,KAAK,CAACiD,QAAQ,CAAC,qBAAqB,EAAE;UAC5DJ,MAAM;UACN1B,IAAI,EAAEb,YAAY,CAACiB;QACrB,CAAC,CAAC;QAEFlB,KAAK,CAACkB,KAAI,GAAI4B,SAAS;MACzB,EAAE,OAAOJ,KAAK,EAAE;QACd1D,SAAS,CAAC0D,KAAK,CAAC,UAAU,CAAC;MAC7B,UAAU;QACR5C,OAAO,CAACoB,KAAI,GAAI,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAM6B,YAAW,GAAIA,CAAA,KAAM;MACzB;IAAA,CACD;;IAED;IACA,MAAMC,UAAS,GAAKzB,IAAI,IAAK;MAC3BlB,YAAY,CAACa,KAAI,GAAIK,IAAI;MACzBnB,iBAAiB,CAACc,KAAI,GAAI,IAAI;IAChC,CAAC;;IAED;IACA,MAAM+B,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAI,CAAC5C,YAAY,CAACa,KAAK,EAAE;MAEzBrB,MAAM,CAACoB,IAAI,CAAC;QACViC,IAAI,EAAE,mBAAmB;QACzBT,KAAK,EAAE;UACLU,MAAM,EAAE9C,YAAY,CAACa,KAAK,CAACkC,EAAE;UAC7BtC,IAAI,EAAEb,YAAY,CAACiB;QACrB;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMmC,cAAa,GAAK9B,IAAI,IAAK;MAC/B,OAAO;QACL,gBAAgB,EAAEA,IAAI,CAACC,MAAK,KAAM,WAAW;QAC7C,eAAe,EAAED,IAAI,CAACC,MAAK,KAAM,UAAU;QAC3C,eAAe,EAAED,IAAI,CAACC,MAAK,KAAM,UAAU;QAC3C,YAAY,EAAED,IAAI,CAACI,eAAe;QAClC,aAAa,EAAEJ,IAAI,CAACK;MACtB,CAAC;IACH,CAAC;;IAED;IACA,MAAM0B,YAAW,GAAK/B,IAAI,IAAK;MAC7B,OAAO;QACLgC,OAAO,EAAE,GAAGhC,IAAI,CAACW,GAAG,WAAW;QAC/BsB,UAAU,EAAE,GAAGjC,IAAI,CAACa,MAAM;MAC5B,CAAC;IACH,CAAC;;IAED;IACA,MAAMqB,iBAAgB,GAAKjC,MAAM,IAAK;MACpC,QAAQA,MAAM;QACZ,KAAK,WAAW;UACd,OAAO,SAAS;QAClB,KAAK,UAAU;UACb,OAAO,QAAQ;QACjB,KAAK,UAAU;UACb,OAAO,MAAM;QACf;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAMkC,iBAAgB,GAAKlC,MAAM,IAAK;MACpC,QAAQA,MAAM;QACZ,KAAK,WAAW;UACd,OAAO,IAAI;QACb,KAAK,UAAU;UACb,OAAO,KAAK;QACd,KAAK,UAAU;UACb,OAAO,IAAI;QACb;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAMmC,wBAAuB,GAAKnC,MAAM,IAAK;MAC3C,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ,OAAO,SAAS;QAClB,KAAK,YAAY;UACf,OAAO,SAAS;QAClB,KAAK,WAAW;UACd,OAAO,MAAM;QACf,KAAK,WAAW;UACd,OAAO,QAAQ;QACjB,KAAK,SAAS;UACZ,OAAO,QAAQ;QACjB;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAMoC,wBAAuB,GAAKpC,MAAM,IAAK;MAC3C,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ,OAAO,KAAK;QACd,KAAK,YAAY;UACf,OAAO,KAAK;QACd,KAAK,WAAW;UACd,OAAO,KAAK;QACd,KAAK,WAAW;UACd,OAAO,KAAK;QACd,KAAK,SAAS;UACZ,OAAO,KAAK;QACd;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAMqC,UAAS,GAAKC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;;MAE1B;MACA,OAAOA,UAAU,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC;;IAED;IACA,MAAMC,cAAa,GAAKC,cAAc,IAAK;MACzC,IAAI,CAACA,cAAc,EAAE,OAAO,EAAE;MAE9B,MAAMnD,IAAG,GAAI,IAAIX,IAAI,CAAC8D,cAAc,CAAC;MACrC,OAAO,GAAGnD,IAAI,CAACoD,WAAW,CAAC,CAAC,IAAIC,OAAO,CAACrD,IAAI,CAACsD,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAID,OAAO,CACrErD,IAAI,CAACE,OAAO,CAAC,CACf,CAAC,IAAImD,OAAO,CAACrD,IAAI,CAACuD,QAAQ,CAAC,CAAC,CAAC,IAAIF,OAAO,CAACrD,IAAI,CAACwD,UAAU,CAAC,CAAC,CAAC,EAAE;IAC/D,CAAC;;IAED;IACA,SAASpE,mBAAmBA,CAACY,IAAI,EAAE;MACjC,OAAO,GAAGA,IAAI,CAACoD,WAAW,CAAC,CAAC,IAAIC,OAAO,CAACrD,IAAI,CAACsD,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAID,OAAO,CAACrD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;IAC3F;;IAEA;IACA,SAASI,oBAAoBA,CAACN,IAAI,EAAE;MAClC,MAAMF,KAAI,GAAI,IAAIT,IAAI,CAAC,CAAC;MACxB,MAAMoE,QAAO,GAAI,IAAIpE,IAAI,CAAC,CAAC;MAC3BoE,QAAQ,CAACxD,OAAO,CAACH,KAAK,CAACI,OAAO,CAAC,IAAI,CAAC,CAAC;MAErC,IAAIF,IAAI,CAAC0D,YAAY,CAAC,MAAM5D,KAAK,CAAC4D,YAAY,CAAC,CAAC,EAAE;QAChD,OAAO,IAAI;MACb,OAAO,IAAI1D,IAAI,CAAC0D,YAAY,CAAC,MAAMD,QAAQ,CAACC,YAAY,CAAC,CAAC,EAAE;QAC1D,OAAO,IAAI;MACb,OAAO;QACL,MAAMC,QAAO,GAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QAC3D,OAAO,GAAG3D,IAAI,CAACsD,QAAQ,CAAC,IAAI,CAAC,IAAItD,IAAI,CAACE,OAAO,CAAC,CAAC,KAAKyD,QAAQ,CAAC3D,IAAI,CAAC4D,MAAM,CAAC,CAAC,CAAC,EAAE;MAC/E;IACF;;IAEA;IACA,SAASP,OAAOA,CAACQ,GAAG,EAAE;MACpB,OAAOA,GAAE,GAAI,EAAC,GAAI,IAAIA,GAAG,EAAC,GAAIA,GAAG;IACnC;IAEAhG,SAAS,CAAC,MAAM;MACd4D,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC;IAEF,OAAO;MACLzC,OAAO;MACPC,IAAI;MACJC,KAAK;MACLC,YAAY;MACZS,WAAW;MACXJ,OAAO;MACPF,iBAAiB;MACjBC,YAAY;MACZgB,cAAc;MACdK,aAAa;MACbG,SAAS;MACTgB,SAAS;MACTE,YAAY;MACZC,UAAU;MACVC,WAAW;MACXI,cAAc;MACdC,YAAY;MACZG,iBAAiB;MACjBC,iBAAiB;MACjBC,wBAAwB;MACxBC,wBAAwB;MACxBC,UAAU;MACVG,cAAc;MACd/E,SAAS;MACTC,OAAO;MACPC,QAAQ;MACRC,KAAK;MACLC,IAAI;MACJC,UAAU;MACVC,SAAS;MACTC;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}