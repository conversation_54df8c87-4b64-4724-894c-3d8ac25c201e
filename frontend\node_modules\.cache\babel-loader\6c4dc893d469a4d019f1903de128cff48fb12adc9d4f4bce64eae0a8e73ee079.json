{"ast": null, "code": "import { defineComponent, inject, onBeforeUnmount, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle } from 'vue';\nimport { POPPER_CONTENT_INJECTION_KEY } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPopperArrow\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  setup(__props, {\n    expose\n  }) {\n    const ns = useNamespace(\"popper\");\n    const {\n      arrowRef,\n      arrowStyle\n    } = inject(POPPER_CONTENT_INJECTION_KEY, void 0);\n    onBeforeUnmount(() => {\n      arrowRef.value = void 0;\n    });\n    expose({\n      arrowRef\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"span\", {\n        ref_key: \"arrowRef\",\n        ref: arrowRef,\n        class: normalizeClass(unref(ns).e(\"arrow\")),\n        style: normalizeStyle(unref(arrowStyle)),\n        \"data-popper-arrow\": \"\"\n      }, null, 6);\n    };\n  }\n});\nvar ElPopperArrow = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"arrow.vue\"]]);\nexport { ElPopperArrow as default };", "map": {"version": 3, "names": ["name", "inheritAttrs", "ns", "useNamespace", "arrowRef", "arrowStyle", "inject", "POPPER_CONTENT_INJECTION_KEY", "onBeforeUnmount", "value", "expose", "_ctx", "_cache", "openBlock", "createElementBlock", "ref_key", "ref", "class", "normalizeClass", "unref", "e", "style", "normalizeStyle"], "sources": ["../../../../../../packages/components/popper/src/arrow.vue"], "sourcesContent": ["<template>\n  <span\n    ref=\"arrowRef\"\n    :class=\"ns.e('arrow')\"\n    :style=\"arrowStyle\"\n    data-popper-arrow\n  />\n</template>\n\n<script lang=\"ts\" setup>\nimport { inject, onBeforeUnmount } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { POPPER_CONTENT_INJECTION_KEY } from './constants'\n\ndefineOptions({\n  name: 'ElPopperArrow',\n  inheritAttrs: false,\n})\n\nconst ns = useNamespace('popper')\nconst { arrowRef, arrowStyle } = inject(\n  POPPER_CONTENT_INJECTION_KEY,\n  undefined\n)!\n\nonBeforeUnmount(() => {\n  arrowRef.value = undefined\n})\n\ndefineExpose({\n  /**\n   * @description Arrow element\n   */\n  arrowRef,\n})\n</script>\n"], "mappings": ";;;;mCAcc;EACZA,IAAM;EACNC,YAAc;AAChB;;;;;;IAEM,MAAAC,EAAA,GAAKC,YAAA,CAAa,QAAQ;IAC1B;MAAEC,QAAU;MAAAC;IAAA,CAAe,GAAAC,MAAA,CAAAC,4BAAA;IAC/BC,eAAA;MACAJ,QAAA,CAAAK,KAAA;IAAA,CACF;IAEAC,MAAA;MACEN;IAAiB,CAClB;IAEY,QAAAO,IAAA,EAAAC,MAAA;MAAA,OAAAC,SAAA,IAAAC,kBAAA;QAAAC,OAAA;QAAAC,GAAA,EAAAZ,QAAA;QAIXa,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAAjB,EAAA,EAAAkB,CAAA;QACDC,KAAA,EAAAC,cAAA,CAAAH,KAAA,CAAAd,UAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}