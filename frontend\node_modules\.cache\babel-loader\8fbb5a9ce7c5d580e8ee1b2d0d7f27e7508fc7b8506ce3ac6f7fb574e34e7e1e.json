{"ast": null, "code": "import { computed, unref, isRef, ref, inject } from 'vue';\nimport { get } from 'lodash-unified';\nimport English from '../../locale/lang/en.mjs';\nconst buildTranslator = locale => (path, option) => translate(path, option, unref(locale));\nconst translate = (path, option, locale) => get(locale, path, path).replace(/\\{(\\w+)\\}/g, (_, key) => {\n  var _a;\n  return `${(_a = option == null ? void 0 : option[key]) != null ? _a : `{${key}}`}`;\n});\nconst buildLocaleContext = locale => {\n  const lang = computed(() => unref(locale).name);\n  const localeRef = isRef(locale) ? locale : ref(locale);\n  return {\n    lang,\n    locale: localeRef,\n    t: buildTranslator(locale)\n  };\n};\nconst localeContextKey = Symbol(\"localeContextKey\");\nconst useLocale = localeOverrides => {\n  const locale = localeOverrides || inject(localeContextKey, ref());\n  return buildLocaleContext(computed(() => locale.value || English));\n};\nexport { buildLocaleContext, buildTranslator, localeContextKey, translate, useLocale };", "map": {"version": 3, "names": ["buildTranslator", "locale", "path", "option", "translate", "unref", "get", "replace", "_", "key", "_a", "buildLocaleContext", "lang", "computed", "name", "localeRef", "isRef", "ref", "t", "localeContextKey", "Symbol", "useLocale", "localeOverrides", "inject", "value", "English"], "sources": ["../../../../../packages/hooks/use-locale/index.ts"], "sourcesContent": ["import { computed, inject, isRef, ref, unref } from 'vue'\nimport { get } from 'lodash-unified'\nimport English from '@element-plus/locale/lang/en'\n\nimport type { MaybeRef } from '@vueuse/core'\nimport type { InjectionKey, Ref } from 'vue'\nimport type { Language } from '@element-plus/locale'\n\nexport type TranslatorOption = Record<string, string | number>\nexport type Translator = (path: string, option?: TranslatorOption) => string\nexport type LocaleContext = {\n  locale: Ref<Language>\n  lang: Ref<string>\n  t: Translator\n}\n\nexport const buildTranslator =\n  (locale: MaybeRef<Language>): Translator =>\n  (path, option) =>\n    translate(path, option, unref(locale))\n\nexport const translate = (\n  path: string,\n  option: undefined | TranslatorOption,\n  locale: Language\n): string =>\n  (get(locale, path, path) as string).replace(\n    /\\{(\\w+)\\}/g,\n    (_, key) => `${option?.[key] ?? `{${key}}`}`\n  )\n\nexport const buildLocaleContext = (\n  locale: MaybeRef<Language>\n): LocaleContext => {\n  const lang = computed(() => unref(locale).name)\n  const localeRef = isRef(locale) ? locale : ref(locale)\n  return {\n    lang,\n    locale: localeRef,\n    t: buildTranslator(locale),\n  }\n}\n\nexport const localeContextKey: InjectionKey<Ref<Language | undefined>> =\n  Symbol('localeContextKey')\n\nexport const useLocale = (localeOverrides?: Ref<Language | undefined>) => {\n  const locale = localeOverrides || inject(localeContextKey, ref())!\n  return buildLocaleContext(computed(() => locale.value || English))\n}\n"], "mappings": ";;;AAGY,MAACA,eAAe,GAAIC,MAAM,IAAK,CAACC,IAAI,EAAEC,MAAM,KAAKC,SAAS,CAACF,IAAI,EAAEC,MAAM,EAAEE,KAAK,CAACJ,MAAM,CAAC;AACtF,MAACG,SAAS,GAAGA,CAACF,IAAI,EAAEC,MAAM,EAAEF,MAAM,KAAKK,GAAG,CAACL,MAAM,EAAEC,IAAI,EAAEA,IAAI,CAAC,CAACK,OAAO,CAAC,YAAY,EAAE,CAACC,CAAC,EAAEC,GAAG,KAAK;EAC3G,IAAIC,EAAE;EACN,OAAO,GAAG,CAACA,EAAE,GAAGP,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACM,GAAG,CAAC,KAAK,IAAI,GAAGC,EAAE,GAAG,IAAID,GAAG,GAAG,EAAE;AACpF,CAAC;AACW,MAACE,kBAAkB,GAAIV,MAAM,IAAK;EAC5C,MAAMW,IAAI,GAAGC,QAAQ,CAAC,MAAMR,KAAK,CAACJ,MAAM,CAAC,CAACa,IAAI,CAAC;EAC/C,MAAMC,SAAS,GAAGC,KAAK,CAACf,MAAM,CAAC,GAAGA,MAAM,GAAGgB,GAAG,CAAChB,MAAM,CAAC;EACtD,OAAO;IACLW,IAAI;IACJX,MAAM,EAAEc,SAAS;IACjBG,CAAC,EAAElB,eAAe,CAACC,MAAM;EAC7B,CAAG;AACH;AACY,MAACkB,gBAAgB,GAAGC,MAAM,CAAC,kBAAkB;AAC7C,MAACC,SAAS,GAAIC,eAAe,IAAK;EAC5C,MAAMrB,MAAM,GAAGqB,eAAe,IAAIC,MAAM,CAACJ,gBAAgB,EAAEF,GAAG,EAAE,CAAC;EACjE,OAAON,kBAAkB,CAACE,QAAQ,CAAC,MAAMZ,MAAM,CAACuB,KAAK,IAAIC,OAAO,CAAC,CAAC;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}