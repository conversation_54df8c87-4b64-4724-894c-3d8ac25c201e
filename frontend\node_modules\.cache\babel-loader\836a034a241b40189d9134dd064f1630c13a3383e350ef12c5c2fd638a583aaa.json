{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst stepsProps = buildProps({\n  space: {\n    type: [Number, String],\n    default: \"\"\n  },\n  active: {\n    type: Number,\n    default: 0\n  },\n  direction: {\n    type: String,\n    default: \"horizontal\",\n    values: [\"horizontal\", \"vertical\"]\n  },\n  alignCenter: {\n    type: Boolean\n  },\n  simple: {\n    type: Boolean\n  },\n  finishStatus: {\n    type: String,\n    values: [\"wait\", \"process\", \"finish\", \"error\", \"success\"],\n    default: \"finish\"\n  },\n  processStatus: {\n    type: String,\n    values: [\"wait\", \"process\", \"finish\", \"error\", \"success\"],\n    default: \"process\"\n  }\n});\nconst stepsEmits = {\n  [CHANGE_EVENT]: (newVal, oldVal) => [newVal, oldVal].every(isNumber)\n};\nexport { stepsEmits, stepsProps };", "map": {"version": 3, "names": ["stepsProps", "buildProps", "space", "type", "Number", "String", "default", "active", "direction", "values", "alignCenter", "Boolean", "simple", "finishStatus", "processStatus", "stepsEmits", "CHANGE_EVENT", "newVal", "oldVal", "every", "isNumber"], "sources": ["../../../../../../packages/components/steps/src/steps.ts"], "sourcesContent": ["import { CHANGE_EVENT } from '@element-plus/constants'\nimport { buildProps, isNumber } from '@element-plus/utils'\nimport type Steps from './steps.vue'\nimport type { ExtractPropTypes } from 'vue'\n\nexport const stepsProps = buildProps({\n  /**\n   * @description the spacing of each step, will be responsive if omitted. Supports percentage.\n   */\n  space: {\n    type: [Number, String],\n    default: '',\n  },\n  /**\n   * @description current activation step\n   */\n  active: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description display direction\n   */\n  direction: {\n    type: String,\n    default: 'horizontal',\n    values: ['horizontal', 'vertical'],\n  },\n  /**\n   * @description center title and description\n   */\n  alignCenter: {\n    type: <PERSON>olean,\n  },\n  /**\n   * @description whether to apply simple theme\n   */\n  simple: {\n    type: Boolean,\n  },\n  /**\n   * @description status of end step\n   */\n  finishStatus: {\n    type: String,\n    values: ['wait', 'process', 'finish', 'error', 'success'],\n    default: 'finish',\n  },\n  /**\n   * @description status of current step\n   */\n  processStatus: {\n    type: String,\n    values: ['wait', 'process', 'finish', 'error', 'success'],\n    default: 'process',\n  },\n} as const)\nexport type StepsProps = ExtractPropTypes<typeof stepsProps>\n\nexport const stepsEmits = {\n  [CHANGE_EVENT]: (newVal: number, oldVal: number) =>\n    [newVal, oldVal].every(isNumber),\n}\nexport type StepsEmits = typeof stepsEmits\n\nexport type StepsInstance = InstanceType<typeof Steps> & unknown\n"], "mappings": ";;;AAEY,MAACA,UAAU,GAAGC,UAAU,CAAC;EACnCC,KAAK,EAAE;IACLC,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;IACtBC,OAAO,EAAE;EACb,CAAG;EACDC,MAAM,EAAE;IACNJ,IAAI,EAAEC,MAAM;IACZE,OAAO,EAAE;EACb,CAAG;EACDE,SAAS,EAAE;IACTL,IAAI,EAAEE,MAAM;IACZC,OAAO,EAAE,YAAY;IACrBG,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU;EACrC,CAAG;EACDC,WAAW,EAAE;IACXP,IAAI,EAAEQ;EACV,CAAG;EACDC,MAAM,EAAE;IACNT,IAAI,EAAEQ;EACV,CAAG;EACDE,YAAY,EAAE;IACZV,IAAI,EAAEE,MAAM;IACZI,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;IACzDH,OAAO,EAAE;EACb,CAAG;EACDQ,aAAa,EAAE;IACbX,IAAI,EAAEE,MAAM;IACZI,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;IACzDH,OAAO,EAAE;EACb;AACA,CAAC;AACW,MAACS,UAAU,GAAG;EACxB,CAACC,YAAY,GAAG,CAACC,MAAM,EAAEC,MAAM,KAAK,CAACD,MAAM,EAAEC,MAAM,CAAC,CAACC,KAAK,CAACC,QAAQ;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}