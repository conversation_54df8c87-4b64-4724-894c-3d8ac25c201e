{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst maskProps = buildProps({\n  zIndex: {\n    type: Number,\n    default: 1001\n  },\n  visible: Boolean,\n  fill: {\n    type: String,\n    default: \"rgba(0,0,0,0.5)\"\n  },\n  pos: {\n    type: definePropType(Object)\n  },\n  targetAreaClickable: {\n    type: Boolean,\n    default: true\n  }\n});\nexport { maskProps };", "map": {"version": 3, "names": ["maskProps", "buildProps", "zIndex", "type", "Number", "default", "visible", "Boolean", "fill", "String", "pos", "definePropType", "Object", "targetAreaClickable"], "sources": ["../../../../../../packages/components/tour/src/mask.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type { PosInfo } from './types'\n\nexport const maskProps = buildProps({\n  /**\n   * @description mask's zIndex\n   */\n  zIndex: {\n    type: Number,\n    default: 1001,\n  },\n  /**\n   * @description whether to show the mask\n   */\n  visible: Boolean,\n  /**\n   * @description mask's fill\n   */\n  fill: {\n    type: String,\n    default: 'rgba(0,0,0,0.5)',\n  },\n  /***\n   * @description mask's transparent space position\n   */\n  pos: {\n    type: definePropType<PosInfo | null>(Object),\n  },\n  /**\n   * @description whether the target element can be clickable, when using mask\n   */\n  targetAreaClickable: {\n    type: Boolean,\n    default: true,\n  },\n})\n\nexport type MaskProps = ExtractPropTypes<typeof maskProps>\n"], "mappings": ";AACY,MAACA,SAAS,GAAGC,UAAU,CAAC;EAClCC,MAAM,EAAE;IACNC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,OAAO,EAAEC,OAAO;EAChBC,IAAI,EAAE;IACJL,IAAI,EAAEM,MAAM;IACZJ,OAAO,EAAE;EACb,CAAG;EACDK,GAAG,EAAE;IACHP,IAAI,EAAEQ,cAAc,CAACC,MAAM;EAC/B,CAAG;EACDC,mBAAmB,EAAE;IACnBV,IAAI,EAAEI,OAAO;IACbF,OAAO,EAAE;EACb;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}