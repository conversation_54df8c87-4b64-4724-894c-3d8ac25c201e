{"ast": null, "code": "import { get, set } from 'lodash-unified';\nexport { hasOwn } from '@vue/shared';\nconst keysOf = arr => Object.keys(arr);\nconst entriesOf = arr => Object.entries(arr);\nconst getProp = (obj, path, defaultValue) => {\n  return {\n    get value() {\n      return get(obj, path, defaultValue);\n    },\n    set value(val) {\n      set(obj, path, val);\n    }\n  };\n};\nexport { entriesOf, getProp, keysOf };", "map": {"version": 3, "names": ["keysOf", "arr", "Object", "keys", "entriesOf", "entries", "getProp", "obj", "path", "defaultValue", "value", "get", "val", "set"], "sources": ["../../../../packages/utils/objects.ts"], "sourcesContent": ["import { get, set } from 'lodash-unified'\nimport type { Entries } from 'type-fest'\nimport type { Arrayable } from '.'\n\nexport const keysOf = <T extends object>(arr: T) =>\n  Object.keys(arr) as Array<keyof T>\nexport const entriesOf = <T extends object>(arr: T) =>\n  Object.entries(arr) as Entries<T>\nexport { hasOwn } from '@vue/shared'\n\nexport const getProp = <T = any>(\n  obj: Record<string, any>,\n  path: Arrayable<string>,\n  defaultValue?: any\n): { value: T } => {\n  return {\n    get value() {\n      return get(obj, path, defaultValue)\n    },\n    set value(val: any) {\n      set(obj, path, val)\n    },\n  }\n}\n"], "mappings": ";;AACY,MAACA,MAAM,GAAIC,GAAG,IAAKC,MAAM,CAACC,IAAI,CAACF,GAAG;AAClC,MAACG,SAAS,GAAIH,GAAG,IAAKC,MAAM,CAACG,OAAO,CAACJ,GAAG;AAExC,MAACK,OAAO,GAAGA,CAACC,GAAG,EAAEC,IAAI,EAAEC,YAAY,KAAK;EAClD,OAAO;IACL,IAAIC,KAAKA,CAAA,EAAG;MACV,OAAOC,GAAG,CAACJ,GAAG,EAAEC,IAAI,EAAEC,YAAY,CAAC;IACzC,CAAK;IACD,IAAIC,KAAKA,CAACE,GAAG,EAAE;MACbC,GAAG,CAACN,GAAG,EAAEC,IAAI,EAAEI,GAAG,CAAC;IACzB;EACA,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}