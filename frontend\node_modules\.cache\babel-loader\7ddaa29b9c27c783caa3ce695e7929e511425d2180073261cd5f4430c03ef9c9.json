{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { ref, computed } from 'vue';\nimport { castArray } from 'lodash-unified';\nimport { debugWarn } from '../../../utils/error.mjs';\nconst SCOPE = \"ElForm\";\nfunction useFormLabelWidth() {\n  const potentialLabelWidthArr = ref([]);\n  const autoLabelWidth = computed(() => {\n    if (!potentialLabelWidthArr.value.length) return \"0\";\n    const max = Math.max(...potentialLabelWidthArr.value);\n    return max ? `${max}px` : \"\";\n  });\n  function getLabelWidthIndex(width) {\n    const index = potentialLabelWidthArr.value.indexOf(width);\n    if (index === -1 && autoLabelWidth.value === \"0\") {\n      debugWarn(SCOPE, `unexpected width ${width}`);\n    }\n    return index;\n  }\n  function registerLabelWidth(val, oldVal) {\n    if (val && oldVal) {\n      const index = getLabelWidthIndex(oldVal);\n      potentialLabelWidthArr.value.splice(index, 1, val);\n    } else if (val) {\n      potentialLabelWidthArr.value.push(val);\n    }\n  }\n  function deregisterLabelWidth(val) {\n    const index = getLabelWidthIndex(val);\n    if (index > -1) {\n      potentialLabelWidthArr.value.splice(index, 1);\n    }\n  }\n  return {\n    autoLabelWidth,\n    registerLabelWidth,\n    deregisterLabelWidth\n  };\n}\nconst filterFields = (fields, props) => {\n  const normalized = castArray(props);\n  return normalized.length > 0 ? fields.filter(field => field.prop && normalized.includes(field.prop)) : fields;\n};\nexport { filterFields, useFormLabelWidth };", "map": {"version": 3, "names": ["SCOPE", "useFormLabel<PERSON>th", "potentialLabel<PERSON>Arr", "ref", "autoLabel<PERSON>idth", "computed", "value", "length", "max", "Math", "getLabelWidthIndex", "width", "index", "indexOf", "debugWarn", "registerLabel<PERSON>th", "val", "oldVal", "splice", "push", "deregister<PERSON><PERSON><PERSON>", "filterFields", "fields", "props", "normalized", "<PERSON><PERSON><PERSON><PERSON>", "filter", "field", "prop", "includes"], "sources": ["../../../../../../packages/components/form/src/utils.ts"], "sourcesContent": ["import { computed, ref } from 'vue'\nimport { debugWarn, ensureArray } from '@element-plus/utils'\nimport type { Arrayable } from '@element-plus/utils'\nimport type { FormItemContext } from './types'\nimport type { FormItemProp } from './form-item'\n\nconst SCOPE = 'ElForm'\n\nexport function useFormLabelWidth() {\n  const potentialLabelWidthArr = ref<number[]>([])\n\n  const autoLabelWidth = computed(() => {\n    if (!potentialLabelWidthArr.value.length) return '0'\n    const max = Math.max(...potentialLabelWidthArr.value)\n    return max ? `${max}px` : ''\n  })\n\n  function getLabelWidthIndex(width: number) {\n    const index = potentialLabelWidthArr.value.indexOf(width)\n    if (index === -1 && autoLabelWidth.value === '0') {\n      debugWarn(SCOPE, `unexpected width ${width}`)\n    }\n    return index\n  }\n\n  function registerLabelWidth(val: number, oldVal: number) {\n    if (val && oldVal) {\n      const index = getLabelWidthIndex(oldVal)\n      potentialLabelWidthArr.value.splice(index, 1, val)\n    } else if (val) {\n      potentialLabelWidthArr.value.push(val)\n    }\n  }\n\n  function deregisterLabelWidth(val: number) {\n    const index = getLabelWidthIndex(val)\n    if (index > -1) {\n      potentialLabelWidthArr.value.splice(index, 1)\n    }\n  }\n\n  return {\n    autoLabelWidth,\n    registerLabelWidth,\n    deregisterLabelWidth,\n  }\n}\n\nexport const filterFields = (\n  fields: FormItemContext[],\n  props: Arrayable<FormItemProp>\n) => {\n  const normalized = ensureArray(props)\n  return normalized.length > 0\n    ? fields.filter((field) => field.prop && normalized.includes(field.prop))\n    : fields\n}\n"], "mappings": ";;;;;;AAEA,MAAMA,KAAK,GAAG,QAAQ;AACf,SAASC,iBAAiBA,CAAA,EAAG;EAClC,MAAMC,sBAAsB,GAAGC,GAAG,CAAC,EAAE,CAAC;EACtC,MAAMC,cAAc,GAAGC,QAAQ,CAAC,MAAM;IACpC,IAAI,CAACH,sBAAsB,CAACI,KAAK,CAACC,MAAM,EACtC,OAAO,GAAG;IACZ,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,GAAGN,sBAAsB,CAACI,KAAK,CAAC;IACrD,OAAOE,GAAG,GAAG,GAAGA,GAAG,IAAI,GAAG,EAAE;EAChC,CAAG,CAAC;EACF,SAASE,kBAAkBA,CAACC,KAAK,EAAE;IACjC,MAAMC,KAAK,GAAGV,sBAAsB,CAACI,KAAK,CAACO,OAAO,CAACF,KAAK,CAAC;IACzD,IAAIC,KAAK,KAAK,CAAC,CAAC,IAAIR,cAAc,CAACE,KAAK,KAAK,GAAG,EAAE;MAChDQ,SAAS,CAACd,KAAK,EAAE,oBAAoBW,KAAK,EAAE,CAAC;IACnD;IACI,OAAOC,KAAK;EAChB;EACE,SAASG,kBAAkBA,CAACC,GAAG,EAAEC,MAAM,EAAE;IACvC,IAAID,GAAG,IAAIC,MAAM,EAAE;MACjB,MAAML,KAAK,GAAGF,kBAAkB,CAACO,MAAM,CAAC;MACxCf,sBAAsB,CAACI,KAAK,CAACY,MAAM,CAACN,KAAK,EAAE,CAAC,EAAEI,GAAG,CAAC;IACxD,CAAK,MAAM,IAAIA,GAAG,EAAE;MACdd,sBAAsB,CAACI,KAAK,CAACa,IAAI,CAACH,GAAG,CAAC;IAC5C;EACA;EACE,SAASI,oBAAoBA,CAACJ,GAAG,EAAE;IACjC,MAAMJ,KAAK,GAAGF,kBAAkB,CAACM,GAAG,CAAC;IACrC,IAAIJ,KAAK,GAAG,CAAC,CAAC,EAAE;MACdV,sBAAsB,CAACI,KAAK,CAACY,MAAM,CAACN,KAAK,EAAE,CAAC,CAAC;IACnD;EACA;EACE,OAAO;IACLR,cAAc;IACdW,kBAAkB;IAClBK;EACJ,CAAG;AACH;AACY,MAACC,YAAY,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAC7C,MAAMC,UAAU,GAAGC,SAAW,CAACF,KAAK,CAAC;EACrC,OAAOC,UAAU,CAACjB,MAAM,GAAG,CAAC,GAAGe,MAAM,CAACI,MAAM,CAAEC,KAAK,IAAKA,KAAK,CAACC,IAAI,IAAIJ,UAAU,CAACK,QAAQ,CAACF,KAAK,CAACC,IAAI,CAAC,CAAC,GAAGN,MAAM;AACjH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}