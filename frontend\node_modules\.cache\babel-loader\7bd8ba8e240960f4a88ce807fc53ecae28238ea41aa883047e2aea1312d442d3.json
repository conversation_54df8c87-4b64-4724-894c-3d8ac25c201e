{"ast": null, "code": "import { renderSlot, createVNode } from 'vue';\nconst HeaderCell = (props, {\n  slots\n}) => renderSlot(slots, \"default\", props, () => {\n  var _a, _b;\n  return [createVNode(\"div\", {\n    \"class\": props.class,\n    \"title\": (_a = props.column) == null ? void 0 : _a.title\n  }, [(_b = props.column) == null ? void 0 : _b.title])];\n});\nHeaderCell.displayName = \"ElTableV2HeaderCell\";\nHeaderCell.inheritAttrs = false;\nvar HeaderCell$1 = HeaderCell;\nexport { HeaderCell$1 as default };", "map": {"version": 3, "names": ["slots", "renderSlot", "props", "_a", "_b", "createVNode", "class", "column", "title", "<PERSON><PERSON><PERSON><PERSON>", "displayName", "inheritAttrs"], "sources": ["../../../../../../../packages/components/table-v2/src/components/header-cell.tsx"], "sourcesContent": ["import { renderSlot } from 'vue'\nimport type { FunctionalComponent } from 'vue'\nimport type { TableV2HeaderCell } from '../header-cell'\n\nconst HeaderCell: FunctionalComponent<TableV2HeaderCell> = (props, { slots }) =>\n  renderSlot(slots, 'default', props, () => [\n    <div class={props.class} title={props.column?.title}>\n      {props.column?.title}\n    </div>,\n  ])\n\nHeaderCell.displayName = 'ElTableV2HeaderCell'\nHeaderCell.inheritAttrs = false\n\nexport default HeaderCell\n"], "mappings": ";;EAIAA;AAAqE,MAAAC,UAAA,CAAAD,KAAA,aAAAE,KAAA;EAAV,IACzDC,EAAA,EAAAC,EAAA;EAA0C,OAC5B,CAAAC,WAD4B;IACR,SAAAH,KAAM,CAAAI,KAAQ;IAC3C,OAAM,GAAAH,EAAN,GAAcD,KAAA,CAAAK,MAHrB,qBAAAJ,EAAA,CAAAK;;AAOA,CAAU;AACVC,UAAU,CAACC,WAAX;AAEAD,UAAA,CAAAE,YAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}