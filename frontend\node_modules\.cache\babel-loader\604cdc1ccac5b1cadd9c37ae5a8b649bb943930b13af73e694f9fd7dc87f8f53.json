{"ast": null, "code": "const UPDATE_MODEL_EVENT = \"update:modelValue\";\nconst CHANGE_EVENT = \"change\";\nconst INPUT_EVENT = \"input\";\nexport { CHANGE_EVENT, INPUT_EVENT, UPDATE_MODEL_EVENT };", "map": {"version": 3, "names": ["UPDATE_MODEL_EVENT", "CHANGE_EVENT", "INPUT_EVENT"], "sources": ["../../../../packages/constants/event.ts"], "sourcesContent": ["export const UPDATE_MODEL_EVENT = 'update:modelValue'\nexport const CHANGE_EVENT = 'change'\nexport const INPUT_EVENT = 'input'\n"], "mappings": "AAAY,MAACA,kBAAkB,GAAG;AACtB,MAACC,YAAY,GAAG;AAChB,MAACC,WAAW,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}