{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, createVNode } from 'vue';\nimport { tableV2HeaderRowProps } from '../header-row.mjs';\nimport { isArray } from '@vue/shared';\nconst TableV2HeaderRow = defineComponent({\n  name: \"ElTableV2HeaderRow\",\n  props: tableV2HeaderRowProps,\n  setup(props, {\n    slots\n  }) {\n    return () => {\n      const {\n        columns,\n        columnsStyles,\n        headerIndex,\n        style\n      } = props;\n      let Cells = columns.map((column, columnIndex) => {\n        return slots.cell({\n          columns,\n          column,\n          columnIndex,\n          headerIndex,\n          style: columnsStyles[column.key]\n        });\n      });\n      if (slots.header) {\n        Cells = slots.header({\n          cells: Cells.map(node => {\n            if (isArray(node) && node.length === 1) {\n              return node[0];\n            }\n            return node;\n          }),\n          columns,\n          headerIndex\n        });\n      }\n      return createVNode(\"div\", {\n        \"class\": props.class,\n        \"style\": style,\n        \"role\": \"row\"\n      }, [Cells]);\n    };\n  }\n});\nvar HeaderRow = TableV2HeaderRow;\nexport { HeaderRow as default };", "map": {"version": 3, "names": ["TableV2HeaderRow", "defineComponent", "name", "props", "tableV2HeaderRowProps", "slots", "columns", "columnsStyles", "headerIndex", "style", "Cells", "map", "column", "columnIndex", "cell", "key", "header", "cells", "node", "isArray", "length", "createVNode", "class", "HeaderRow"], "sources": ["../../../../../../../packages/components/table-v2/src/components/header-row.tsx"], "sourcesContent": ["import { defineComponent } from 'vue'\nimport { isArray } from '@element-plus/utils'\nimport { tableV2HeaderRowProps } from '../header-row'\n\nimport type { CSSProperties } from 'vue'\nimport type { ColumnCellsType } from '../types'\nimport type { TableV2HeaderRowProps } from '../header-row'\n\nconst TableV2HeaderRow = defineComponent({\n  name: 'ElTableV2HeaderRow',\n  props: tableV2HeaderRowProps,\n  setup(props, { slots }) {\n    return () => {\n      const { columns, columnsStyles, headerIndex, style } = props\n      let Cells: ColumnCellsType = columns.map((column, columnIndex) => {\n        return slots.cell!({\n          columns,\n          column,\n          columnIndex,\n          headerIndex,\n          style: columnsStyles[column.key!],\n        })\n      })\n\n      if (slots.header) {\n        Cells = slots.header({\n          cells: Cells.map((node) => {\n            if (isArray(node) && node.length === 1) {\n              return node[0]\n            }\n            return node\n          }),\n          columns,\n          headerIndex,\n        })\n      }\n\n      return (\n        <div class={props.class} style={style} role=\"row\">\n          {Cells}\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableV2HeaderRow\n\nexport type TableV2HeaderRowCellRendererParams = {\n  columns: TableV2HeaderRowProps['columns']\n  column: TableV2HeaderRowProps['columns'][number]\n  columnIndex: number\n  headerIndex: number\n  style: CSSProperties\n}\n\nexport type TableV2HeaderRowRendererParams = {\n  cells: ColumnCellsType\n  columns: TableV2HeaderRowProps['columns']\n  headerIndex: number\n}\n"], "mappings": ";;;;;AAQA,MAAMA,gBAAgB,GAAGC,eAAe,CAAC;EACvCC,IAAI,EAAE,oBADiC;EAEvCC,KAAK,EAAEC,qBAFgC;;IAGlCC;EAAU;IAAS;MACtB,MAAa;QACLC,OAAA;QAAEC,aAAF;QAAWC,WAAX;QAA0BC;MAAa,IAAAN,KAAA;MAAvC,IAAiDO,KAAvD,GAAAJ,OAAA,CAAAK,GAAA,EAAAC,MAAA,EAAAC,WAAA;QACI,OAAsBR,KAAG,CAAAS,IAAO,CAAC;UAC5BR,OAAA;UACLM,MADiB;UAEjBC,WAFiB;UAGjBL,WAHiB;UAIjBC,KAJiB,EAAAF,aAAA,CAAAK,MAAA,CAAAG,GAAA;QAKjB;MALiB;MAOpB,IARDV,KAAA,CAAAW,MAAA;;UAUIC,KAAK,EAACP,KAAV,CAAkBC,GAAA,CAAAO,IAAA;YAChB,IAAQC,OAAM,CAAAD,IAAN,CAAa,IAAAA,IAAA,CAAAE,MAAA;cACnB,OAAYF,IAAL,EAAW;;mBAEPA,IAAA;UACR;;UACDV;QACD;;MAED,OAAAa,WAAA;QARmB,OAArB,EAAAlB,KAAA,CAAAmB,KAAA;QAUD,SAAAb,KAAA;;MAED,IAAAC,KAAA;;EAAA;;AAAA,IAAAa,SAAA,GAAAvB,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}