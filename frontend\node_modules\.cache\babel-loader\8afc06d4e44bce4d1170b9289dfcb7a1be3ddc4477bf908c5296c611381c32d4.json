{"ast": null, "code": "import { unref } from 'vue';\nimport { isArray } from '@vue/shared';\nconst isTriggerType = (trigger, type) => {\n  if (isArray(trigger)) {\n    return trigger.includes(type);\n  }\n  return trigger === type;\n};\nconst whenTrigger = (trigger, type, handler) => {\n  return e => {\n    isTriggerType(unref(trigger), type) && handler(e);\n  };\n};\nexport { isTriggerType, whenTrigger };", "map": {"version": 3, "names": ["isTriggerType", "trigger", "type", "isArray", "includes", "when<PERSON><PERSON>ger", "handler", "e", "unref"], "sources": ["../../../../../../packages/components/tooltip/src/utils.ts"], "sourcesContent": ["import { unref } from 'vue'\nimport { isArray } from '@element-plus/utils'\nimport type { Arrayable } from '@element-plus/utils'\nimport type { Ref } from 'vue'\nimport type { TooltipTriggerType } from './trigger'\n\nexport const isTriggerType = (\n  trigger: Arrayable<TooltipTriggerType>,\n  type: TooltipTriggerType\n) => {\n  if (isArray(trigger)) {\n    return trigger.includes(type)\n  }\n  return trigger === type\n}\n\nexport const whenTrigger = (\n  trigger: Ref<Arrayable<TooltipTriggerType>>,\n  type: TooltipTriggerType,\n  handler: (e: Event) => void\n) => {\n  return (e: Event) => {\n    isTriggerType(unref(trigger), type) && handler(e)\n  }\n}\n"], "mappings": ";;AAEY,MAACA,aAAa,GAAGA,CAACC,OAAO,EAAEC,IAAI,KAAK;EAC9C,IAAIC,OAAO,CAACF,OAAO,CAAC,EAAE;IACpB,OAAOA,OAAO,CAACG,QAAQ,CAACF,IAAI,CAAC;EACjC;EACE,OAAOD,OAAO,KAAKC,IAAI;AACzB;AACY,MAACG,WAAW,GAAGA,CAACJ,OAAO,EAAEC,IAAI,EAAEI,OAAO,KAAK;EACrD,OAAQC,CAAC,IAAK;IACZP,aAAa,CAACQ,KAAK,CAACP,OAAO,CAAC,EAAEC,IAAI,CAAC,IAAII,OAAO,CAACC,CAAC,CAAC;EACrD,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}