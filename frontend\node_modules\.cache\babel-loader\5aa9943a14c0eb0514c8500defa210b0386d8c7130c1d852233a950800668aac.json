{"ast": null, "code": "import { defineComponent, toRefs, reactive, openBlock, createBlock, normalizeProps, guardReactiveProps, withCtx, createVNode, mergeProps, renderSlot, unref, Transition, createCommentVNode, createElementBlock, Fragment } from 'vue';\nimport { pick } from 'lodash-unified';\nimport { ElTeleport } from '../../teleport/index.mjs';\nimport { tooltipV2ArrowProps } from './arrow.mjs';\nimport { tooltipV2ContentProps } from './content2.mjs';\nimport { tooltipV2RootProps } from './root2.mjs';\nimport { tooltipV2Props } from './tooltip.mjs';\nimport { tooltipV2TriggerProps } from './trigger.mjs';\nimport TooltipV2Root from './root.mjs';\nimport TooltipV2Arrow from './arrow2.mjs';\nimport TooltipV2Content from './content.mjs';\nimport TooltipV2Trigger from './trigger2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTooltipV2\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tooltipV2Props,\n  setup(__props) {\n    const props = __props;\n    const refedProps = toRefs(props);\n    const arrowProps = reactive(pick(refedProps, Object.keys(tooltipV2ArrowProps)));\n    const contentProps = reactive(pick(refedProps, Object.keys(tooltipV2ContentProps)));\n    const rootProps = reactive(pick(refedProps, Object.keys(tooltipV2RootProps)));\n    const triggerProps = reactive(pick(refedProps, Object.keys(tooltipV2TriggerProps)));\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(TooltipV2Root, normalizeProps(guardReactiveProps(rootProps)), {\n        default: withCtx(({\n          open\n        }) => [createVNode(TooltipV2Trigger, mergeProps(triggerProps, {\n          nowrap: \"\"\n        }), {\n          default: withCtx(() => [renderSlot(_ctx.$slots, \"trigger\")]),\n          _: 3\n        }, 16), createVNode(unref(ElTeleport), {\n          to: _ctx.to,\n          disabled: !_ctx.teleported\n        }, {\n          default: withCtx(() => [_ctx.fullTransition ? (openBlock(), createBlock(Transition, normalizeProps(mergeProps({\n            key: 0\n          }, _ctx.transitionProps)), {\n            default: withCtx(() => [_ctx.alwaysOn || open ? (openBlock(), createBlock(TooltipV2Content, normalizeProps(mergeProps({\n              key: 0\n            }, contentProps)), {\n              arrow: withCtx(({\n                style,\n                side\n              }) => [_ctx.showArrow ? (openBlock(), createBlock(TooltipV2Arrow, mergeProps({\n                key: 0\n              }, arrowProps, {\n                style,\n                side\n              }), null, 16, [\"style\", \"side\"])) : createCommentVNode(\"v-if\", true)]),\n              default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n              _: 3\n            }, 16)) : createCommentVNode(\"v-if\", true)]),\n            _: 2\n          }, 1040)) : (openBlock(), createElementBlock(Fragment, {\n            key: 1\n          }, [_ctx.alwaysOn || open ? (openBlock(), createBlock(TooltipV2Content, normalizeProps(mergeProps({\n            key: 0\n          }, contentProps)), {\n            arrow: withCtx(({\n              style,\n              side\n            }) => [_ctx.showArrow ? (openBlock(), createBlock(TooltipV2Arrow, mergeProps({\n              key: 0\n            }, arrowProps, {\n              style,\n              side\n            }), null, 16, [\"style\", \"side\"])) : createCommentVNode(\"v-if\", true)]),\n            default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n            _: 3\n          }, 16)) : createCommentVNode(\"v-if\", true)], 64))]),\n          _: 2\n        }, 1032, [\"to\", \"disabled\"])]),\n        _: 3\n      }, 16);\n    };\n  }\n});\nvar TooltipV2 = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tooltip.vue\"]]);\nexport { TooltipV2 as default };", "map": {"version": 3, "names": ["name", "refedProps", "toRefs", "props", "arrowProps", "reactive", "pick", "Object", "keys", "tooltipV2ArrowProps", "contentProps", "tooltipV2ContentProps", "rootProps", "tooltipV2RootProps", "triggerProps", "tooltipV2TriggerProps", "_ctx", "_cache", "openBlock", "createBlock", "TooltipV2Root", "normalizeProps", "guardReactiveProps", "default", "withCtx", "open", "createVNode", "TooltipV2Trigger", "mergeProps", "nowrap"], "sources": ["../../../../../../packages/components/tooltip-v2/src/tooltip.vue"], "sourcesContent": ["<template>\n  <tooltip-v2-root v-bind=\"rootProps\">\n    <template #default=\"{ open }\">\n      <tooltip-v2-trigger v-bind=\"triggerProps\" nowrap>\n        <slot name=\"trigger\" />\n      </tooltip-v2-trigger>\n      <el-teleport :to=\"to\" :disabled=\"!teleported\">\n        <template v-if=\"fullTransition\">\n          <transition v-bind=\"transitionProps\">\n            <tooltip-v2-content v-if=\"alwaysOn || open\" v-bind=\"contentProps\">\n              <slot />\n              <template #arrow=\"{ style, side }\">\n                <tooltip-v2-arrow\n                  v-if=\"showArrow\"\n                  v-bind=\"arrowProps\"\n                  :style=\"style\"\n                  :side=\"side\"\n                />\n              </template>\n            </tooltip-v2-content>\n          </transition>\n        </template>\n        <template v-else>\n          <tooltip-v2-content v-if=\"alwaysOn || open\" v-bind=\"contentProps\">\n            <slot />\n            <template #arrow=\"{ style, side }\">\n              <tooltip-v2-arrow\n                v-if=\"showArrow\"\n                v-bind=\"arrowProps\"\n                :style=\"style\"\n                :side=\"side\"\n              />\n            </template>\n          </tooltip-v2-content>\n        </template>\n      </el-teleport>\n    </template>\n  </tooltip-v2-root>\n</template>\n\n<script setup lang=\"ts\">\n// @ts-nocheck\nimport { reactive, toRefs } from 'vue'\nimport { pick } from 'lodash-unified'\nimport ElTeleport from '@element-plus/components/teleport'\nimport { tooltipV2ArrowProps } from './arrow'\nimport { tooltipV2ContentProps } from './content'\nimport { tooltipV2RootProps } from './root'\nimport { tooltipV2Props } from './tooltip'\nimport { tooltipV2TriggerProps } from './trigger'\nimport TooltipV2Root from './root.vue'\nimport TooltipV2Arrow from './arrow.vue'\nimport TooltipV2Content from './content.vue'\nimport TooltipV2Trigger from './trigger.vue'\n\ndefineOptions({\n  name: 'ElTooltipV2',\n})\n\nconst props = defineProps(tooltipV2Props)\n\nconst refedProps = toRefs(props)\n\nconst arrowProps = reactive(pick(refedProps, Object.keys(tooltipV2ArrowProps)))\n\nconst contentProps = reactive(\n  pick(refedProps, Object.keys(tooltipV2ContentProps))\n)\n\nconst rootProps = reactive(pick(refedProps, Object.keys(tooltipV2RootProps)))\n\nconst triggerProps = reactive(\n  pick(refedProps, Object.keys(tooltipV2TriggerProps))\n)\n</script>\n"], "mappings": ";;;;;;;;;;;;;mCAuDc;EACZA,IAAM;AACR;;;;;;IAIM,MAAAC,UAAA,GAAaC,MAAA,CAAOC,KAAK;IAEzB,MAAAC,UAAA,GAAaC,QAAA,CAASC,IAAK,CAAAL,UAAA,EAAYM,MAAA,CAAOC,IAAK,CAAAC,mBAAmB,CAAC,CAAC;IAE9E,MAAMC,YAAe,GAAAL,QAAA,CAAAC,IAAA,CAAAL,UAAA,EAAAM,MAAA,CAAAC,IAAA,CAAAG,qBAAA;IAAA,MACdC,SAAA,GAAAP,QAAmB,CAAAC,IAAK,CAAAL,UAAA,EAAAM,MAAA,CAAAC,IAAsB,CAAAK,kBAAA;IACrD,MAAAC,YAAA,GAAAT,QAAA,CAAAC,IAAA,CAAAL,UAAA,EAAAM,MAAA,CAAAC,IAAA,CAAAO,qBAAA;IAEM,QAAAC,IAAA,EAAAC,MAAA;MAEN,OAAqBC,SAAA,IAAAC,WAAA,CAAAC,aAAA,EAAAC,cAAA,CAAAC,kBAAA,CAAAV,SAAA;QACdW,OAAA,EAAAC,OAAmB;UAAAC;QAAA,CAAK,MAC/BC,WAAA,CAAAC,gBAAA,EAAAC,UAAA,CAAAd,YAAA;UAAAe,MAAA;QAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}