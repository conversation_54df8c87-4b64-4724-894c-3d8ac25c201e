{"ast": null, "code": "import Mention from './src/mention2.mjs';\nexport { mentionEmits, mentionProps } from './src/mention.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElMention = withInstall(Mention);\nexport { ElMention, ElMention as default };", "map": {"version": 3, "names": ["ElMention", "withInstall", "Mention"], "sources": ["../../../../../packages/components/mention/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Mention from './src/mention.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElMention: SFCWithInstall<typeof Mention> = withInstall(Mention)\nexport default ElMention\n\nexport * from './src/mention'\n"], "mappings": ";;;AAEY,MAACA,SAAS,GAAGC,WAAW,CAACC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}