{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nvar defaultProps = {\n  type: {\n    type: String,\n    default: \"default\"\n  },\n  label: String,\n  className: String,\n  labelClassName: String,\n  property: String,\n  prop: String,\n  width: {\n    type: [String, Number],\n    default: \"\"\n  },\n  minWidth: {\n    type: [String, Number],\n    default: \"\"\n  },\n  renderHeader: Function,\n  sortable: {\n    type: [Boolean, String],\n    default: false\n  },\n  sortMethod: Function,\n  sortBy: [String, Function, Array],\n  resizable: {\n    type: Boolean,\n    default: true\n  },\n  columnKey: String,\n  align: String,\n  headerAlign: String,\n  showOverflowTooltip: {\n    type: [Boolean, Object],\n    default: void 0\n  },\n  tooltipFormatter: Function,\n  fixed: [Boolean, String],\n  formatter: Function,\n  selectable: Function,\n  reserveSelection: Boolean,\n  filterMethod: Function,\n  filteredValue: Array,\n  filters: Array,\n  filterPlacement: String,\n  filterMultiple: {\n    type: Boolean,\n    default: true\n  },\n  filterClassName: String,\n  index: [Number, Function],\n  sortOrders: {\n    type: Array,\n    default: () => {\n      return [\"ascending\", \"descending\", null];\n    },\n    validator: val => {\n      return val.every(order => [\"ascending\", \"descending\", null].includes(order));\n    }\n  }\n};\nexport { defaultProps as default };", "map": {"version": 3, "names": ["defaultProps", "type", "String", "default", "label", "className", "labelClassName", "property", "prop", "width", "Number", "min<PERSON><PERSON><PERSON>", "renderHeader", "Function", "sortable", "Boolean", "sortMethod", "sortBy", "Array", "resizable", "column<PERSON>ey", "align", "headerAlign", "showOverflowTooltip", "Object", "tooltipFormatter", "fixed", "formatter", "selectable", "reserveSelection", "filterMethod", "filteredValue", "filters", "filterPlacement", "filterMultiple", "filterClassName", "index", "sortOrders", "validator", "val", "every", "order", "includes"], "sources": ["../../../../../../../packages/components/table/src/table-column/defaults.ts"], "sourcesContent": ["// @ts-nocheck\nimport type { ComponentInternalInstance, PropType, Ref, VNode } from 'vue'\nimport type { DefaultRow, Table } from '../table/defaults'\nimport type {\n  TableOverflowTooltipFormatter,\n  TableOverflowTooltipOptions,\n} from '../util'\n\ntype CI<T> = { column: TableColumnCtx<T>; $index: number }\n\ntype Filters = {\n  text: string\n  value: string\n}[]\n\ntype FilterMethods<T> = (value, row: T, column: TableColumnCtx<T>) => void\n\ntype ValueOf<T> = T[keyof T]\n\ninterface TableColumnCtx<T> {\n  id: string\n  realWidth: number\n  type: string\n  label: string\n  className: string\n  labelClassName: string\n  property: string\n  prop: string\n  width: string | number\n  minWidth: string | number\n  renderHeader: (data: CI<T>) => VNode\n  sortable: boolean | string\n  sortMethod: (a: T, b: T) => number\n  sortBy: string | ((row: T, index: number) => string) | string[]\n  resizable: boolean\n  columnKey: string\n  rawColumnKey: string\n  align: string\n  headerAlign: string\n  showOverflowTooltip?: boolean | TableOverflowTooltipOptions\n  tooltipFormatter?: TableOverflowTooltipFormatter<T>\n  fixed: boolean | string\n  formatter: (\n    row: T,\n    column: TableColumnCtx<T>,\n    cellValue,\n    index: number\n  ) => VNode | string\n  selectable: (row: T, index: number) => boolean\n  reserveSelection: boolean\n  filterMethod: FilterMethods<T>\n  filteredValue: string[]\n  filters: Filters\n  filterPlacement: string\n  filterMultiple: boolean\n  filterClassName: string\n  index: number | ((index: number) => number)\n  sortOrders: ('ascending' | 'descending' | null)[]\n  renderCell: (data: any) => void\n  colSpan: number\n  rowSpan: number\n  children: TableColumnCtx<T>[]\n  level: number\n  filterable: boolean | FilterMethods<T> | Filters\n  order: string\n  isColumnGroup: boolean\n  isSubColumn: boolean\n  columns: TableColumnCtx<T>[]\n  getColumnIndex: () => number\n  no: number\n  filterOpened?: boolean\n}\n\ninterface TableColumn<T> extends ComponentInternalInstance {\n  vnode: {\n    vParent: TableColumn<T> | Table<T>\n  } & VNode\n  vParent: TableColumn<T> | Table<T>\n  columnId: string\n  columnConfig: Ref<Partial<TableColumnCtx<T>>>\n}\n\nexport type { Filters, FilterMethods, TableColumnCtx, TableColumn, ValueOf }\n\nexport default {\n  /**\n   * @description type of the column. If set to `selection`, the column will display checkbox. If set to `index`, the column will display index of the row (staring from 1). If set to `expand`, the column will display expand icon\n   */\n  type: {\n    type: String,\n    default: 'default',\n  },\n  /**\n   * @description column label\n   */\n  label: String,\n  /**\n   * @description class name of cells in the column\n   */\n  className: String,\n  /**\n   * @description class name of the label of this column\n   */\n  labelClassName: String,\n  /**\n   * @description\n   */\n  property: String,\n  /**\n   * @description field name. You can also use its alias: `property`\n   */\n  prop: String,\n  /**\n   * @description column width\n   */\n  width: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description column minimum width. Columns with `width` has a fixed width, while columns with `min-width` has a width that is distributed in proportion\n   */\n  minWidth: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description render function for table header of this column\n   */\n  renderHeader: Function as PropType<\n    TableColumnCtx<DefaultRow>['renderHeader']\n  >,\n  /**\n   * @description whether column can be sorted. Remote sorting can be done by setting this attribute to 'custom' and listening to the `sort-change` event of Table\n   */\n  sortable: {\n    type: [Boolean, String],\n    default: false,\n  },\n  /**\n   * @description sorting method, works when `sortable` is `true`. Should return a number, just like Array.sort\n   */\n  sortMethod: Function as PropType<TableColumnCtx<DefaultRow>['sortMethod']>,\n  /**\n   * @description specify which property to sort by, works when `sortable` is `true` and `sort-method` is `undefined`. If set to an Array, the column will sequentially sort by the next property if the previous one is equal\n   */\n  sortBy: [String, Function, Array] as PropType<\n    TableColumnCtx<DefaultRow>['sortBy']\n  >,\n  /**\n   * @description whether column width can be resized, works when `border` of `el-table` is `true`\n   */\n  resizable: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description column's key. If you need to use the filter-change event, you need this attribute to identify which column is being filtered\n   */\n  columnKey: String,\n  /**\n   * @description alignment, the value should be 'left' \\/ 'center' \\/ 'right'\n   */\n  align: String,\n  /**\n   * @description alignment of the table header. If omitted, the value of the above `align` attribute will be applied, the value should be 'left' \\/ 'center' \\/ 'right'\n   */\n  headerAlign: String,\n  /**\n   * @description whether to hide extra content and show them in a tooltip when hovering on the cell\n   */\n  showOverflowTooltip: {\n    type: [Boolean, Object] as PropType<\n      TableColumnCtx<DefaultRow>['showOverflowTooltip']\n    >,\n    default: undefined,\n  },\n  /**\n   * @description function that formats cell tooltip content, works when `show-overflow-tooltip` is `true`\n   */\n  tooltipFormatter: Function as PropType<\n    TableColumnCtx<DefaultRow>['tooltipFormatter']\n  >,\n  /**\n   * @description whether column is fixed at left / right. Will be fixed at left if `true`\n   */\n  fixed: [Boolean, String],\n  /**\n   * @description function that formats cell content\n   */\n  formatter: Function as PropType<TableColumnCtx<DefaultRow>['formatter']>,\n  /**\n   * @description function that determines if a certain row can be selected, works when `type` is 'selection'\n   */\n  selectable: Function as PropType<TableColumnCtx<DefaultRow>['selectable']>,\n  /**\n   * @description whether to reserve selection after data refreshing, works when `type` is 'selection'. Note that `row-key` is required for this to work\n   */\n  reserveSelection: Boolean,\n  /**\n   * @description data filtering method. If `filter-multiple` is on, this method will be called multiple times for each row, and a row will display if one of the calls returns `true`\n   */\n  filterMethod: Function as PropType<\n    TableColumnCtx<DefaultRow>['filterMethod']\n  >,\n  /**\n   * @description filter value for selected data, might be useful when table header is rendered with `render-header`\n   */\n  filteredValue: Array as PropType<TableColumnCtx<DefaultRow>['filteredValue']>,\n  /**\n   * @description an array of data filtering options. For each element in this array, `text` and `value` are required\n   */\n  filters: Array as PropType<TableColumnCtx<DefaultRow>['filters']>,\n  /**\n   * @description placement for the filter dropdown\n   */\n  filterPlacement: String,\n  /**\n   * @description whether data filtering supports multiple options\n   */\n  filterMultiple: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description className for the filter dropdown\n   */\n  filterClassName: String,\n  /**\n   * @description customize indices for each row, works on columns with `type=index`\n   */\n  index: [Number, Function] as PropType<TableColumnCtx<DefaultRow>['index']>,\n  /**\n   * @description the order of the sorting strategies used when sorting the data, works when `sortable` is `true`. Accepts an array, as the user clicks on the header, the column is sorted in order of the elements in the array\n   */\n  sortOrders: {\n    type: Array as PropType<TableColumnCtx<DefaultRow>['sortOrders']>,\n    default: () => {\n      return ['ascending', 'descending', null]\n    },\n    validator: (val: TableColumnCtx<unknown>['sortOrders']) => {\n      return val.every((order: string) =>\n        ['ascending', 'descending', null].includes(order)\n      )\n    },\n  },\n}\n"], "mappings": ";;AAAA,IAAAA,YAAA,GAAe;EACbC,IAAI,EAAE;IACJA,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,KAAK,EAAEF,MAAM;EACbG,SAAS,EAAEH,MAAM;EACjBI,cAAc,EAAEJ,MAAM;EACtBK,QAAQ,EAAEL,MAAM;EAChBM,IAAI,EAAEN,MAAM;EACZO,KAAK,EAAE;IACLR,IAAI,EAAE,CAACC,MAAM,EAAEQ,MAAM,CAAC;IACtBP,OAAO,EAAE;EACb,CAAG;EACDQ,QAAQ,EAAE;IACRV,IAAI,EAAE,CAACC,MAAM,EAAEQ,MAAM,CAAC;IACtBP,OAAO,EAAE;EACb,CAAG;EACDS,YAAY,EAAEC,QAAQ;EACtBC,QAAQ,EAAE;IACRb,IAAI,EAAE,CAACc,OAAO,EAAEb,MAAM,CAAC;IACvBC,OAAO,EAAE;EACb,CAAG;EACDa,UAAU,EAAEH,QAAQ;EACpBI,MAAM,EAAE,CAACf,MAAM,EAAEW,QAAQ,EAAEK,KAAK,CAAC;EACjCC,SAAS,EAAE;IACTlB,IAAI,EAAEc,OAAO;IACbZ,OAAO,EAAE;EACb,CAAG;EACDiB,SAAS,EAAElB,MAAM;EACjBmB,KAAK,EAAEnB,MAAM;EACboB,WAAW,EAAEpB,MAAM;EACnBqB,mBAAmB,EAAE;IACnBtB,IAAI,EAAE,CAACc,OAAO,EAAES,MAAM,CAAC;IACvBrB,OAAO,EAAE,KAAK;EAClB,CAAG;EACDsB,gBAAgB,EAAEZ,QAAQ;EAC1Ba,KAAK,EAAE,CAACX,OAAO,EAAEb,MAAM,CAAC;EACxByB,SAAS,EAAEd,QAAQ;EACnBe,UAAU,EAAEf,QAAQ;EACpBgB,gBAAgB,EAAEd,OAAO;EACzBe,YAAY,EAAEjB,QAAQ;EACtBkB,aAAa,EAAEb,KAAK;EACpBc,OAAO,EAAEd,KAAK;EACde,eAAe,EAAE/B,MAAM;EACvBgC,cAAc,EAAE;IACdjC,IAAI,EAAEc,OAAO;IACbZ,OAAO,EAAE;EACb,CAAG;EACDgC,eAAe,EAAEjC,MAAM;EACvBkC,KAAK,EAAE,CAAC1B,MAAM,EAAEG,QAAQ,CAAC;EACzBwB,UAAU,EAAE;IACVpC,IAAI,EAAEiB,KAAK;IACXf,OAAO,EAAEA,CAAA,KAAM;MACb,OAAO,CAAC,WAAW,EAAE,YAAY,EAAE,IAAI,CAAC;IAC9C,CAAK;IACDmC,SAAS,EAAGC,GAAG,IAAK;MAClB,OAAOA,GAAG,CAACC,KAAK,CAAEC,KAAK,IAAK,CAAC,WAAW,EAAE,YAAY,EAAE,IAAI,CAAC,CAACC,QAAQ,CAACD,KAAK,CAAC,CAAC;IACpF;EACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}