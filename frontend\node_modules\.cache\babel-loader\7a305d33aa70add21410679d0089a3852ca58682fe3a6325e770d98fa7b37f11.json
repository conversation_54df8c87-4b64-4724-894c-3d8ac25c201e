{"ast": null, "code": "import { defineComponent, ref, computed, openBlock, createBlock, TransitionGroup, normalizeClass, unref, withCtx, createElementBlock, Fragment, renderList, with<PERSON><PERSON><PERSON>, renderSlot, createCommentVNode, createElementVNode, withModifiers, createVNode, toDisplayString, normalizeStyle } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { Document, CircleCheck, Check, Close, ZoomIn, Delete } from '@element-plus/icons-vue';\nimport { ElProgress } from '../../progress/index.mjs';\nimport { uploadListProps, uploadListEmits } from './upload-list.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nconst __default__ = defineComponent({\n  name: \"ElUploadList\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: uploadListProps,\n  emits: uploadListEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const nsUpload = useNamespace(\"upload\");\n    const nsIcon = useNamespace(\"icon\");\n    const nsList = useNamespace(\"list\");\n    const disabled = useFormDisabled();\n    const focusing = ref(false);\n    const containerKls = computed(() => [nsUpload.b(\"list\"), nsUpload.bm(\"list\", props.listType), nsUpload.is(\"disabled\", props.disabled)]);\n    const handleRemove = file => {\n      emit(\"remove\", file);\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(TransitionGroup, {\n        tag: \"ul\",\n        class: normalizeClass(unref(containerKls)),\n        name: unref(nsList).b()\n      }, {\n        default: withCtx(() => [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.files, (file, index) => {\n          return openBlock(), createElementBlock(\"li\", {\n            key: file.uid || file.name,\n            class: normalizeClass([unref(nsUpload).be(\"list\", \"item\"), unref(nsUpload).is(file.status), {\n              focusing: focusing.value\n            }]),\n            tabindex: \"0\",\n            onKeydown: withKeys($event => !unref(disabled) && handleRemove(file), [\"delete\"]),\n            onFocus: $event => focusing.value = true,\n            onBlur: $event => focusing.value = false,\n            onClick: $event => focusing.value = false\n          }, [renderSlot(_ctx.$slots, \"default\", {\n            file,\n            index\n          }, () => [_ctx.listType === \"picture\" || file.status !== \"uploading\" && _ctx.listType === \"picture-card\" ? (openBlock(), createElementBlock(\"img\", {\n            key: 0,\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-thumbnail\")),\n            src: file.url,\n            crossorigin: _ctx.crossorigin,\n            alt: \"\"\n          }, null, 10, [\"src\", \"crossorigin\"])) : createCommentVNode(\"v-if\", true), file.status === \"uploading\" || _ctx.listType !== \"picture-card\" ? (openBlock(), createElementBlock(\"div\", {\n            key: 1,\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-info\"))\n          }, [createElementVNode(\"a\", {\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-name\")),\n            onClick: withModifiers($event => _ctx.handlePreview(file), [\"prevent\"])\n          }, [createVNode(unref(ElIcon), {\n            class: normalizeClass(unref(nsIcon).m(\"document\"))\n          }, {\n            default: withCtx(() => [createVNode(unref(Document))]),\n            _: 1\n          }, 8, [\"class\"]), createElementVNode(\"span\", {\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-file-name\")),\n            title: file.name\n          }, toDisplayString(file.name), 11, [\"title\"])], 10, [\"onClick\"]), file.status === \"uploading\" ? (openBlock(), createBlock(unref(ElProgress), {\n            key: 0,\n            type: _ctx.listType === \"picture-card\" ? \"circle\" : \"line\",\n            \"stroke-width\": _ctx.listType === \"picture-card\" ? 6 : 2,\n            percentage: Number(file.percentage),\n            style: normalizeStyle(_ctx.listType === \"picture-card\" ? \"\" : \"margin-top: 0.5rem\")\n          }, null, 8, [\"type\", \"stroke-width\", \"percentage\", \"style\"])) : createCommentVNode(\"v-if\", true)], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"label\", {\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-status-label\"))\n          }, [_ctx.listType === \"text\" ? (openBlock(), createBlock(unref(ElIcon), {\n            key: 0,\n            class: normalizeClass([unref(nsIcon).m(\"upload-success\"), unref(nsIcon).m(\"circle-check\")])\n          }, {\n            default: withCtx(() => [createVNode(unref(CircleCheck))]),\n            _: 1\n          }, 8, [\"class\"])) : [\"picture-card\", \"picture\"].includes(_ctx.listType) ? (openBlock(), createBlock(unref(ElIcon), {\n            key: 1,\n            class: normalizeClass([unref(nsIcon).m(\"upload-success\"), unref(nsIcon).m(\"check\")])\n          }, {\n            default: withCtx(() => [createVNode(unref(Check))]),\n            _: 1\n          }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)], 2), !unref(disabled) ? (openBlock(), createBlock(unref(ElIcon), {\n            key: 2,\n            class: normalizeClass(unref(nsIcon).m(\"close\")),\n            onClick: $event => handleRemove(file)\n          }, {\n            default: withCtx(() => [createVNode(unref(Close))]),\n            _: 2\n          }, 1032, [\"class\", \"onClick\"])) : createCommentVNode(\"v-if\", true), createCommentVNode(\" Due to close btn only appears when li gets focused disappears after li gets blurred, thus keyboard navigation can never reach close btn\"), createCommentVNode(\" This is a bug which needs to be fixed \"), createCommentVNode(\" TODO: Fix the incorrect navigation interaction \"), !unref(disabled) ? (openBlock(), createElementBlock(\"i\", {\n            key: 3,\n            class: normalizeClass(unref(nsIcon).m(\"close-tip\"))\n          }, toDisplayString(unref(t)(\"el.upload.deleteTip\")), 3)) : createCommentVNode(\"v-if\", true), _ctx.listType === \"picture-card\" ? (openBlock(), createElementBlock(\"span\", {\n            key: 4,\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-actions\"))\n          }, [createElementVNode(\"span\", {\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-preview\")),\n            onClick: $event => _ctx.handlePreview(file)\n          }, [createVNode(unref(ElIcon), {\n            class: normalizeClass(unref(nsIcon).m(\"zoom-in\"))\n          }, {\n            default: withCtx(() => [createVNode(unref(ZoomIn))]),\n            _: 1\n          }, 8, [\"class\"])], 10, [\"onClick\"]), !unref(disabled) ? (openBlock(), createElementBlock(\"span\", {\n            key: 0,\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-delete\")),\n            onClick: $event => handleRemove(file)\n          }, [createVNode(unref(ElIcon), {\n            class: normalizeClass(unref(nsIcon).m(\"delete\"))\n          }, {\n            default: withCtx(() => [createVNode(unref(Delete))]),\n            _: 1\n          }, 8, [\"class\"])], 10, [\"onClick\"])) : createCommentVNode(\"v-if\", true)], 2)) : createCommentVNode(\"v-if\", true)])], 42, [\"onKeydown\", \"onFocus\", \"onBlur\", \"onClick\"]);\n        }), 128)), renderSlot(_ctx.$slots, \"append\")]),\n        _: 3\n      }, 8, [\"class\", \"name\"]);\n    };\n  }\n});\nvar UploadList = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"upload-list.vue\"]]);\nexport { UploadList as default };", "map": {"version": 3, "names": ["name", "t", "useLocale", "nsUpload", "useNamespace", "nsIcon", "nsList", "disabled", "useFormDisabled", "focusing", "ref", "containerKls", "computed", "b", "bm", "props", "listType", "is", "handleRemove", "file", "emit"], "sources": ["../../../../../../packages/components/upload/src/upload-list.vue"], "sourcesContent": ["<template>\n  <transition-group tag=\"ul\" :class=\"containerKls\" :name=\"nsList.b()\">\n    <li\n      v-for=\"(file, index) in files\"\n      :key=\"file.uid || file.name\"\n      :class=\"[\n        nsUpload.be('list', 'item'),\n        nsUpload.is(file.status),\n        { focusing },\n      ]\"\n      tabindex=\"0\"\n      @keydown.delete=\"!disabled && handleRemove(file)\"\n      @focus=\"focusing = true\"\n      @blur=\"focusing = false\"\n      @click=\"focusing = false\"\n    >\n      <slot :file=\"file\" :index=\"index\">\n        <img\n          v-if=\"\n            listType === 'picture' ||\n            (file.status !== 'uploading' && listType === 'picture-card')\n          \"\n          :class=\"nsUpload.be('list', 'item-thumbnail')\"\n          :src=\"file.url\"\n          :crossorigin=\"crossorigin\"\n          alt=\"\"\n        />\n        <div\n          v-if=\"file.status === 'uploading' || listType !== 'picture-card'\"\n          :class=\"nsUpload.be('list', 'item-info')\"\n        >\n          <a\n            :class=\"nsUpload.be('list', 'item-name')\"\n            @click.prevent=\"handlePreview(file)\"\n          >\n            <el-icon :class=\"nsIcon.m('document')\">\n              <Document />\n            </el-icon>\n            <span\n              :class=\"nsUpload.be('list', 'item-file-name')\"\n              :title=\"file.name\"\n            >\n              {{ file.name }}\n            </span>\n          </a>\n          <el-progress\n            v-if=\"file.status === 'uploading'\"\n            :type=\"listType === 'picture-card' ? 'circle' : 'line'\"\n            :stroke-width=\"listType === 'picture-card' ? 6 : 2\"\n            :percentage=\"Number(file.percentage)\"\n            :style=\"listType === 'picture-card' ? '' : 'margin-top: 0.5rem'\"\n          />\n        </div>\n\n        <label :class=\"nsUpload.be('list', 'item-status-label')\">\n          <el-icon\n            v-if=\"listType === 'text'\"\n            :class=\"[nsIcon.m('upload-success'), nsIcon.m('circle-check')]\"\n          >\n            <circle-check />\n          </el-icon>\n          <el-icon\n            v-else-if=\"['picture-card', 'picture'].includes(listType)\"\n            :class=\"[nsIcon.m('upload-success'), nsIcon.m('check')]\"\n          >\n            <Check />\n          </el-icon>\n        </label>\n        <el-icon\n          v-if=\"!disabled\"\n          :class=\"nsIcon.m('close')\"\n          @click=\"handleRemove(file)\"\n        >\n          <Close />\n        </el-icon>\n        <!-- Due to close btn only appears when li gets focused disappears after li gets blurred, thus keyboard navigation can never reach close btn-->\n        <!-- This is a bug which needs to be fixed -->\n        <!-- TODO: Fix the incorrect navigation interaction -->\n        <i v-if=\"!disabled\" :class=\"nsIcon.m('close-tip')\">{{\n          t('el.upload.deleteTip')\n        }}</i>\n        <span\n          v-if=\"listType === 'picture-card'\"\n          :class=\"nsUpload.be('list', 'item-actions')\"\n        >\n          <span\n            :class=\"nsUpload.be('list', 'item-preview')\"\n            @click=\"handlePreview(file)\"\n          >\n            <el-icon :class=\"nsIcon.m('zoom-in')\"><zoom-in /></el-icon>\n          </span>\n          <span\n            v-if=\"!disabled\"\n            :class=\"nsUpload.be('list', 'item-delete')\"\n            @click=\"handleRemove(file)\"\n          >\n            <el-icon :class=\"nsIcon.m('delete')\">\n              <Delete />\n            </el-icon>\n          </span>\n        </span>\n      </slot>\n    </li>\n    <slot name=\"append\" />\n  </transition-group>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport {\n  Check,\n  CircleCheck,\n  Close,\n  Delete,\n  Document,\n  ZoomIn,\n} from '@element-plus/icons-vue'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport ElProgress from '@element-plus/components/progress'\nimport { useFormDisabled } from '@element-plus/components/form'\n\nimport { uploadListEmits, uploadListProps } from './upload-list'\nimport type { UploadFile } from './upload'\n\ndefineOptions({\n  name: 'ElUploadList',\n})\n\nconst props = defineProps(uploadListProps)\nconst emit = defineEmits(uploadListEmits)\n\nconst { t } = useLocale()\nconst nsUpload = useNamespace('upload')\nconst nsIcon = useNamespace('icon')\nconst nsList = useNamespace('list')\nconst disabled = useFormDisabled()\n\nconst focusing = ref(false)\n\nconst containerKls = computed(() => [\n  nsUpload.b('list'),\n  nsUpload.bm('list', props.listType),\n  nsUpload.is('disabled', props.disabled),\n])\n\nconst handleRemove = (file: UploadFile) => {\n  emit('remove', file)\n}\n</script>\n"], "mappings": ";;;;;;;;;mCA6Hc;EACZA,IAAM;AACR;;;;;;;;;IAKM;MAAEC;IAAE,IAAIC,SAAU;IAClB,MAAAC,QAAA,GAAWC,YAAA,CAAa,QAAQ;IAChC,MAAAC,MAAA,GAASD,YAAA,CAAa,MAAM;IAC5B,MAAAE,MAAA,GAASF,YAAA,CAAa,MAAM;IAClC,MAAMG,QAAA,GAAWC,eAAgB;IAE3B,MAAAC,QAAA,GAAWC,GAAA,CAAI,KAAK;IAEpB,MAAAC,YAAA,GAAeC,QAAA,CAAS,MAAM,CAClCT,QAAA,CAASU,CAAA,CAAE,MAAM,GACjBV,QAAS,CAAAW,EAAA,CAAG,MAAQ,EAAAC,KAAA,CAAMC,QAAQ,GAClCb,QAAS,CAAAc,EAAA,CAAG,UAAY,EAAAF,KAAA,CAAMR,QAAQ,EACvC;IAEK,MAAAW,YAAA,GAAgBC,IAAqB;MACzCC,IAAA,CAAK,UAAUD,IAAI;IAAA,CACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}