{"ast": null, "code": "import { shallowRef, ref, computed } from 'vue';\nimport { useResizeObserver } from '@vueuse/core';\nfunction useCalcInputWidth() {\n  const calculatorRef = shallowRef();\n  const calculatorWidth = ref(0);\n  const MINIMUM_INPUT_WIDTH = 11;\n  const inputStyle = computed(() => ({\n    minWidth: `${Math.max(calculatorWidth.value, MINIMUM_INPUT_WIDTH)}px`\n  }));\n  const resetCalculatorWidth = () => {\n    var _a, _b;\n    calculatorWidth.value = (_b = (_a = calculatorRef.value) == null ? void 0 : _a.getBoundingClientRect().width) != null ? _b : 0;\n  };\n  useResizeObserver(calculatorRef, resetCalculatorWidth);\n  return {\n    calculatorRef,\n    calculatorWidth,\n    inputStyle\n  };\n}\nexport { useCalcInputWidth };", "map": {"version": 3, "names": ["useCalcInputWidth", "calculatorRef", "shallowRef", "calculatorWidth", "ref", "MINIMUM_INPUT_WIDTH", "inputStyle", "computed", "min<PERSON><PERSON><PERSON>", "Math", "max", "value", "resetCalculator<PERSON>idth", "_a", "_b", "getBoundingClientRect", "width", "useResizeObserver"], "sources": ["../../../../../packages/hooks/use-calc-input-width/index.ts"], "sourcesContent": ["import { computed, ref, shallowRef } from 'vue'\nimport { useResizeObserver } from '@vueuse/core'\n\nexport function useCalcInputWidth() {\n  const calculatorRef = shallowRef<HTMLElement>()\n  const calculatorWidth = ref(0)\n  const MINIMUM_INPUT_WIDTH = 11\n\n  const inputStyle = computed(() => ({\n    minWidth: `${Math.max(calculatorWidth.value, MINIMUM_INPUT_WIDTH)}px`,\n  }))\n\n  const resetCalculatorWidth = () => {\n    calculatorWidth.value =\n      calculatorRef.value?.getBoundingClientRect().width ?? 0\n  }\n\n  useResizeObserver(calculatorRef, resetCalculatorWidth)\n\n  return {\n    calculatorRef,\n    calculatorWidth,\n    inputStyle,\n  }\n}\n"], "mappings": ";;AAEO,SAASA,iBAAiBA,CAAA,EAAG;EAClC,MAAMC,aAAa,GAAGC,UAAU,EAAE;EAClC,MAAMC,eAAe,GAAGC,GAAG,CAAC,CAAC,CAAC;EAC9B,MAAMC,mBAAmB,GAAG,EAAE;EAC9B,MAAMC,UAAU,GAAGC,QAAQ,CAAC,OAAO;IACjCC,QAAQ,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACP,eAAe,CAACQ,KAAK,EAAEN,mBAAmB,CAAC;EACrE,CAAG,CAAC,CAAC;EACH,MAAMO,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIC,EAAE,EAAEC,EAAE;IACVX,eAAe,CAACQ,KAAK,GAAG,CAACG,EAAE,GAAG,CAACD,EAAE,GAAGZ,aAAa,CAACU,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,EAAE,CAACE,qBAAqB,EAAE,CAACC,KAAK,KAAK,IAAI,GAAGF,EAAE,GAAG,CAAC;EAClI,CAAG;EACDG,iBAAiB,CAAChB,aAAa,EAAEW,oBAAoB,CAAC;EACtD,OAAO;IACLX,aAAa;IACbE,eAAe;IACfG;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}