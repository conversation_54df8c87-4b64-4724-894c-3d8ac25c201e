{"ast": null, "code": "import TreeSelect from './src/tree-select.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElTreeSelect = withInstall(TreeSelect);\nexport { ElTreeSelect, ElTreeSelect as default };", "map": {"version": 3, "names": ["ElTreeSelect", "withInstall", "TreeSelect"], "sources": ["../../../../../packages/components/tree-select/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport TreeSelect from './src/tree-select.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTreeSelect: SFCWithInstall<typeof TreeSelect> =\n  withInstall(TreeSelect)\n\nexport default ElTreeSelect\n"], "mappings": ";;AAEY,MAACA,YAAY,GAAGC,WAAW,CAACC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}