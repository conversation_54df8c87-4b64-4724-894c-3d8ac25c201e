{"ast": null, "code": "import Countdown from './src/countdown2.mjs';\nexport { countdownEmits, countdownProps } from './src/countdown.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElCountdown = withInstall(Countdown);\nexport { ElCountdown, ElCountdown as default };", "map": {"version": 3, "names": ["ElCountdown", "withInstall", "Countdown"], "sources": ["../../../../../packages/components/countdown/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Countdown from './src/countdown.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCountdown: SFCWithInstall<typeof Countdown> =\n  withInstall(Countdown)\nexport default ElCountdown\n\nexport * from './src/countdown'\n"], "mappings": ";;;AAEY,MAACA,WAAW,GAAGC,WAAW,CAACC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}