{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, renderSlot, createBlock, resolveDynamicComponent, createCommentVNode, toDisplayString } from 'vue';\nimport { resultProps, IconMap, IconComponentMap } from './result.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElResult\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: resultProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"result\");\n    const resultIcon = computed(() => {\n      const icon = props.icon;\n      const iconClass = icon && IconMap[icon] ? IconMap[icon] : \"icon-info\";\n      const iconComponent = IconComponentMap[iconClass] || IconComponentMap[\"icon-info\"];\n      return {\n        class: iconClass,\n        component: iconComponent\n      };\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(ns).b())\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"icon\"))\n      }, [renderSlot(_ctx.$slots, \"icon\", {}, () => [unref(resultIcon).component ? (openBlock(), createBlock(resolveDynamicComponent(unref(resultIcon).component), {\n        key: 0,\n        class: normalizeClass(unref(resultIcon).class)\n      }, null, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)])], 2), _ctx.title || _ctx.$slots.title ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"title\"))\n      }, [renderSlot(_ctx.$slots, \"title\", {}, () => [createElementVNode(\"p\", null, toDisplayString(_ctx.title), 1)])], 2)) : createCommentVNode(\"v-if\", true), _ctx.subTitle || _ctx.$slots[\"sub-title\"] ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"subtitle\"))\n      }, [renderSlot(_ctx.$slots, \"sub-title\", {}, () => [createElementVNode(\"p\", null, toDisplayString(_ctx.subTitle), 1)])], 2)) : createCommentVNode(\"v-if\", true), _ctx.$slots.extra ? (openBlock(), createElementBlock(\"div\", {\n        key: 2,\n        class: normalizeClass(unref(ns).e(\"extra\"))\n      }, [renderSlot(_ctx.$slots, \"extra\")], 2)) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar Result = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"result.vue\"]]);\nexport { Result as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace", "resultIcon", "computed", "icon", "props", "iconClass", "IconMap", "iconComponent", "IconComponentMap", "class", "component"], "sources": ["../../../../../../packages/components/result/src/result.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <div :class=\"ns.e('icon')\">\n      <slot name=\"icon\">\n        <component\n          :is=\"resultIcon.component\"\n          v-if=\"resultIcon.component\"\n          :class=\"resultIcon.class\"\n        />\n      </slot>\n    </div>\n    <div v-if=\"title || $slots.title\" :class=\"ns.e('title')\">\n      <slot name=\"title\">\n        <p>{{ title }}</p>\n      </slot>\n    </div>\n    <div v-if=\"subTitle || $slots['sub-title']\" :class=\"ns.e('subtitle')\">\n      <slot name=\"sub-title\">\n        <p>{{ subTitle }}</p>\n      </slot>\n    </div>\n    <div v-if=\"$slots.extra\" :class=\"ns.e('extra')\">\n      <slot name=\"extra\" />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { IconComponentMap, IconMap, resultProps } from './result'\n\ndefineOptions({\n  name: 'ElResult',\n})\n\nconst props = defineProps(resultProps)\n\nconst ns = useNamespace('result')\n\nconst resultIcon = computed(() => {\n  const icon = props.icon\n  const iconClass = icon && IconMap[icon] ? IconMap[icon] : 'icon-info'\n  const iconComponent =\n    IconComponentMap[iconClass] || IconComponentMap['icon-info']\n\n  return {\n    class: iconClass,\n    component: iconComponent,\n  }\n})\n</script>\n"], "mappings": ";;;;mCAgCc;EACZA,IAAM;AACR;;;;;;IAIM,MAAAC,EAAA,GAAKC,YAAA,CAAa,QAAQ;IAE1B,MAAAC,UAAA,GAAaC,QAAA,CAAS,MAAM;MAChC,MAAMC,IAAA,GAAOC,KAAM,CAAAD,IAAA;MACnB,MAAME,SAAA,GAAYF,IAAQ,IAAAG,OAAA,CAAQH,IAAI,CAAI,GAAAG,OAAA,CAAQH,IAAI,CAAI;MAC1D,MAAMI,aACJ,GAAAC,gBAAA,CAAiBH,SAAS,KAAKG,gBAAA,CAAiB,WAAW;MAEtD;QACLC,KAAO,EAAAJ,SAAA;QACPK,SAAW,EAAAH;MAAA,CACb;IAAA,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}