{"ast": null, "code": "var _export_sfc = (sfc, props) => {\n  const target = sfc.__vccOpts || sfc;\n  for (const [key, val] of props) {\n    target[key] = val;\n  }\n  return target;\n};\nexport { _export_sfc as default };", "map": {"version": 3, "names": ["_export_sfc", "sfc", "props", "target", "__vccOpts", "key", "val"], "sources": ["../../../../internal/build/plugin-vue:export-helper"], "sourcesContent": ["\nexport default (sfc, props) => {\n  const target = sfc.__vccOpts || sfc;\n  for (const [key, val] of props) {\n    target[key] = val;\n  }\n  return target;\n}\n"], "mappings": "AACA,IAAAA,WAAA,GAAeA,CAACC,GAAG,EAAEC,KAAK,KAAK;EAC7B,MAAMC,MAAM,GAAGF,GAAG,CAACG,SAAS,IAAIH,GAAG;EACnC,KAAK,MAAM,CAACI,GAAG,EAAEC,GAAG,CAAC,IAAIJ,KAAK,EAAE;IAC9BC,MAAM,CAACE,GAAG,CAAC,GAAGC,GAAG;EACrB;EACE,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}