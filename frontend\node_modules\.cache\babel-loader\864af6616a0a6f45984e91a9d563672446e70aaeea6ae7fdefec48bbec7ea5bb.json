{"ast": null, "code": "import Avatar from './src/avatar2.mjs';\nexport { avatarEmits, avatarProps } from './src/avatar.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElAvatar = withInstall(Avatar);\nexport { ElAvatar, ElAvatar as default };", "map": {"version": 3, "names": ["El<PERSON><PERSON><PERSON>", "withInstall", "Avatar"], "sources": ["../../../../../packages/components/avatar/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Avatar from './src/avatar.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElAvatar: SFCWithInstall<typeof Avatar> = withInstall(Avatar)\nexport default ElAvatar\n\nexport * from './src/avatar'\nexport type { AvatarInstance } from './src/instance'\n"], "mappings": ";;;AAEY,MAACA,QAAQ,GAAGC,WAAW,CAACC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}