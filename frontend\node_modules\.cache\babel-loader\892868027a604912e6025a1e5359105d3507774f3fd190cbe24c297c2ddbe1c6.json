{"ast": null, "code": "import createMathOperation from './_createMathOperation.js';\n\n/**\n * Divide two numbers.\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Math\n * @param {number} dividend The first number in a division.\n * @param {number} divisor The second number in a division.\n * @returns {number} Returns the quotient.\n * @example\n *\n * _.divide(6, 4);\n * // => 1.5\n */\nvar divide = createMathOperation(function (dividend, divisor) {\n  return dividend / divisor;\n}, 1);\nexport default divide;", "map": {"version": 3, "names": ["createMathOperation", "divide", "dividend", "divisor"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/divide.js"], "sourcesContent": ["import createMathOperation from './_createMathOperation.js';\n\n/**\n * Divide two numbers.\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Math\n * @param {number} dividend The first number in a division.\n * @param {number} divisor The second number in a division.\n * @returns {number} Returns the quotient.\n * @example\n *\n * _.divide(6, 4);\n * // => 1.5\n */\nvar divide = createMathOperation(function(dividend, divisor) {\n  return dividend / divisor;\n}, 1);\n\nexport default divide;\n"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,2BAA2B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAGD,mBAAmB,CAAC,UAASE,QAAQ,EAAEC,OAAO,EAAE;EAC3D,OAAOD,QAAQ,GAAGC,OAAO;AAC3B,CAAC,EAAE,CAAC,CAAC;AAEL,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}