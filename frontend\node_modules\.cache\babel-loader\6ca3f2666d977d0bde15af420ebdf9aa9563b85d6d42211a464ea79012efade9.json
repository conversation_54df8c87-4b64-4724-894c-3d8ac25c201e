{"ast": null, "code": "import { defineComponent, inject, ref, computed, unref, onMounted, nextTick, watch, openBlock, createElementBlock, normalizeClass, Fragment, renderList, createBlock, withCtx, createTextVNode, toDisplayString, createCommentVNode, withDirectives, createVNode, createElementVNode } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { ElScrollbar } from '../../../scrollbar/index.mjs';\nimport { ElIcon } from '../../../icon/index.mjs';\nimport { ArrowUp, ArrowDown } from '@element-plus/icons-vue';\nimport { timeUnits, DEFAULT_FORMATS_TIME } from '../constants.mjs';\nimport { buildTimeList } from '../utils.mjs';\nimport { basicTimeSpinnerProps } from '../props/basic-time-spinner.mjs';\nimport { getTimeLists } from '../composables/use-time-picker.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { vRepeatClick } from '../../../../directives/repeat-click/index.mjs';\nimport { CHANGE_EVENT } from '../../../../constants/event.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { getStyle } from '../../../../utils/dom/style.mjs';\nimport { isNumber } from '../../../../utils/types.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"basic-time-spinner\",\n  props: basicTimeSpinnerProps,\n  emits: [CHANGE_EVENT, \"select-range\", \"set-option\"],\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const pickerBase = inject(\"EP_PICKER_BASE\");\n    const {\n      isRange,\n      format\n    } = pickerBase.props;\n    const ns = useNamespace(\"time\");\n    const {\n      getHoursList,\n      getMinutesList,\n      getSecondsList\n    } = getTimeLists(props.disabledHours, props.disabledMinutes, props.disabledSeconds);\n    let isScrolling = false;\n    const currentScrollbar = ref();\n    const listHoursRef = ref();\n    const listMinutesRef = ref();\n    const listSecondsRef = ref();\n    const listRefsMap = {\n      hours: listHoursRef,\n      minutes: listMinutesRef,\n      seconds: listSecondsRef\n    };\n    const spinnerItems = computed(() => {\n      return props.showSeconds ? timeUnits : timeUnits.slice(0, 2);\n    });\n    const timePartials = computed(() => {\n      const {\n        spinnerDate\n      } = props;\n      const hours = spinnerDate.hour();\n      const minutes = spinnerDate.minute();\n      const seconds = spinnerDate.second();\n      return {\n        hours,\n        minutes,\n        seconds\n      };\n    });\n    const timeList = computed(() => {\n      const {\n        hours,\n        minutes\n      } = unref(timePartials);\n      const {\n        role,\n        spinnerDate\n      } = props;\n      const compare = !isRange ? spinnerDate : void 0;\n      return {\n        hours: getHoursList(role, compare),\n        minutes: getMinutesList(hours, role, compare),\n        seconds: getSecondsList(hours, minutes, role, compare)\n      };\n    });\n    const arrowControlTimeList = computed(() => {\n      const {\n        hours,\n        minutes,\n        seconds\n      } = unref(timePartials);\n      return {\n        hours: buildTimeList(hours, 23),\n        minutes: buildTimeList(minutes, 59),\n        seconds: buildTimeList(seconds, 59)\n      };\n    });\n    const debouncedResetScroll = debounce(type => {\n      isScrolling = false;\n      adjustCurrentSpinner(type);\n    }, 200);\n    const getAmPmFlag = hour => {\n      const shouldShowAmPm = !!props.amPmMode;\n      if (!shouldShowAmPm) return \"\";\n      const isCapital = props.amPmMode === \"A\";\n      let content = hour < 12 ? \" am\" : \" pm\";\n      if (isCapital) content = content.toUpperCase();\n      return content;\n    };\n    const emitSelectRange = type => {\n      let range = [0, 0];\n      if (!format || format === DEFAULT_FORMATS_TIME) {\n        switch (type) {\n          case \"hours\":\n            range = [0, 2];\n            break;\n          case \"minutes\":\n            range = [3, 5];\n            break;\n          case \"seconds\":\n            range = [6, 8];\n            break;\n        }\n      }\n      const [left, right] = range;\n      emit(\"select-range\", left, right);\n      currentScrollbar.value = type;\n    };\n    const adjustCurrentSpinner = type => {\n      adjustSpinner(type, unref(timePartials)[type]);\n    };\n    const adjustSpinners = () => {\n      adjustCurrentSpinner(\"hours\");\n      adjustCurrentSpinner(\"minutes\");\n      adjustCurrentSpinner(\"seconds\");\n    };\n    const getScrollbarElement = el => el.querySelector(`.${ns.namespace.value}-scrollbar__wrap`);\n    const adjustSpinner = (type, value) => {\n      if (props.arrowControl) return;\n      const scrollbar = unref(listRefsMap[type]);\n      if (scrollbar && scrollbar.$el) {\n        getScrollbarElement(scrollbar.$el).scrollTop = Math.max(0, value * typeItemHeight(type));\n      }\n    };\n    const typeItemHeight = type => {\n      const scrollbar = unref(listRefsMap[type]);\n      const listItem = scrollbar == null ? void 0 : scrollbar.$el.querySelector(\"li\");\n      if (listItem) {\n        return Number.parseFloat(getStyle(listItem, \"height\")) || 0;\n      }\n      return 0;\n    };\n    const onIncrement = () => {\n      scrollDown(1);\n    };\n    const onDecrement = () => {\n      scrollDown(-1);\n    };\n    const scrollDown = step => {\n      if (!currentScrollbar.value) {\n        emitSelectRange(\"hours\");\n      }\n      const label = currentScrollbar.value;\n      const now = unref(timePartials)[label];\n      const total = currentScrollbar.value === \"hours\" ? 24 : 60;\n      const next = findNextUnDisabled(label, now, step, total);\n      modifyDateField(label, next);\n      adjustSpinner(label, next);\n      nextTick(() => emitSelectRange(label));\n    };\n    const findNextUnDisabled = (type, now, step, total) => {\n      let next = (now + step + total) % total;\n      const list = unref(timeList)[type];\n      while (list[next] && next !== now) {\n        next = (next + step + total) % total;\n      }\n      return next;\n    };\n    const modifyDateField = (type, value) => {\n      const list = unref(timeList)[type];\n      const isDisabled = list[value];\n      if (isDisabled) return;\n      const {\n        hours,\n        minutes,\n        seconds\n      } = unref(timePartials);\n      let changeTo;\n      switch (type) {\n        case \"hours\":\n          changeTo = props.spinnerDate.hour(value).minute(minutes).second(seconds);\n          break;\n        case \"minutes\":\n          changeTo = props.spinnerDate.hour(hours).minute(value).second(seconds);\n          break;\n        case \"seconds\":\n          changeTo = props.spinnerDate.hour(hours).minute(minutes).second(value);\n          break;\n      }\n      emit(CHANGE_EVENT, changeTo);\n    };\n    const handleClick = (type, {\n      value,\n      disabled\n    }) => {\n      if (!disabled) {\n        modifyDateField(type, value);\n        emitSelectRange(type);\n        adjustSpinner(type, value);\n      }\n    };\n    const handleScroll = type => {\n      const scrollbar = unref(listRefsMap[type]);\n      if (!scrollbar) return;\n      isScrolling = true;\n      debouncedResetScroll(type);\n      const value = Math.min(Math.round((getScrollbarElement(scrollbar.$el).scrollTop - (scrollBarHeight(type) * 0.5 - 10) / typeItemHeight(type) + 3) / typeItemHeight(type)), type === \"hours\" ? 23 : 59);\n      modifyDateField(type, value);\n    };\n    const scrollBarHeight = type => {\n      return unref(listRefsMap[type]).$el.offsetHeight;\n    };\n    const bindScrollEvent = () => {\n      const bindFunction = type => {\n        const scrollbar = unref(listRefsMap[type]);\n        if (scrollbar && scrollbar.$el) {\n          getScrollbarElement(scrollbar.$el).onscroll = () => {\n            handleScroll(type);\n          };\n        }\n      };\n      bindFunction(\"hours\");\n      bindFunction(\"minutes\");\n      bindFunction(\"seconds\");\n    };\n    onMounted(() => {\n      nextTick(() => {\n        !props.arrowControl && bindScrollEvent();\n        adjustSpinners();\n        if (props.role === \"start\") emitSelectRange(\"hours\");\n      });\n    });\n    const setRef = (scrollbar, type) => {\n      listRefsMap[type].value = scrollbar != null ? scrollbar : void 0;\n    };\n    emit(\"set-option\", [`${props.role}_scrollDown`, scrollDown]);\n    emit(\"set-option\", [`${props.role}_emitSelectRange`, emitSelectRange]);\n    watch(() => props.spinnerDate, () => {\n      if (isScrolling) return;\n      adjustSpinners();\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(\"spinner\"), {\n          \"has-seconds\": _ctx.showSeconds\n        }])\n      }, [!_ctx.arrowControl ? (openBlock(true), createElementBlock(Fragment, {\n        key: 0\n      }, renderList(unref(spinnerItems), item => {\n        return openBlock(), createBlock(unref(ElScrollbar), {\n          key: item,\n          ref_for: true,\n          ref: scrollbar => setRef(scrollbar, item),\n          class: normalizeClass(unref(ns).be(\"spinner\", \"wrapper\")),\n          \"wrap-style\": \"max-height: inherit;\",\n          \"view-class\": unref(ns).be(\"spinner\", \"list\"),\n          noresize: \"\",\n          tag: \"ul\",\n          onMouseenter: $event => emitSelectRange(item),\n          onMousemove: $event => adjustCurrentSpinner(item)\n        }, {\n          default: withCtx(() => [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(timeList)[item], (disabled, key) => {\n            return openBlock(), createElementBlock(\"li\", {\n              key,\n              class: normalizeClass([unref(ns).be(\"spinner\", \"item\"), unref(ns).is(\"active\", key === unref(timePartials)[item]), unref(ns).is(\"disabled\", disabled)]),\n              onClick: $event => handleClick(item, {\n                value: key,\n                disabled\n              })\n            }, [item === \"hours\" ? (openBlock(), createElementBlock(Fragment, {\n              key: 0\n            }, [createTextVNode(toDisplayString((\"0\" + (_ctx.amPmMode ? key % 12 || 12 : key)).slice(-2)) + toDisplayString(getAmPmFlag(key)), 1)], 64)) : (openBlock(), createElementBlock(Fragment, {\n              key: 1\n            }, [createTextVNode(toDisplayString((\"0\" + key).slice(-2)), 1)], 64))], 10, [\"onClick\"]);\n          }), 128))]),\n          _: 2\n        }, 1032, [\"class\", \"view-class\", \"onMouseenter\", \"onMousemove\"]);\n      }), 128)) : createCommentVNode(\"v-if\", true), _ctx.arrowControl ? (openBlock(true), createElementBlock(Fragment, {\n        key: 1\n      }, renderList(unref(spinnerItems), item => {\n        return openBlock(), createElementBlock(\"div\", {\n          key: item,\n          class: normalizeClass([unref(ns).be(\"spinner\", \"wrapper\"), unref(ns).is(\"arrow\")]),\n          onMouseenter: $event => emitSelectRange(item)\n        }, [withDirectives((openBlock(), createBlock(unref(ElIcon), {\n          class: normalizeClass([\"arrow-up\", unref(ns).be(\"spinner\", \"arrow\")])\n        }, {\n          default: withCtx(() => [createVNode(unref(ArrowUp))]),\n          _: 1\n        }, 8, [\"class\"])), [[unref(vRepeatClick), onDecrement]]), withDirectives((openBlock(), createBlock(unref(ElIcon), {\n          class: normalizeClass([\"arrow-down\", unref(ns).be(\"spinner\", \"arrow\")])\n        }, {\n          default: withCtx(() => [createVNode(unref(ArrowDown))]),\n          _: 1\n        }, 8, [\"class\"])), [[unref(vRepeatClick), onIncrement]]), createElementVNode(\"ul\", {\n          class: normalizeClass(unref(ns).be(\"spinner\", \"list\"))\n        }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(arrowControlTimeList)[item], (time, key) => {\n          return openBlock(), createElementBlock(\"li\", {\n            key,\n            class: normalizeClass([unref(ns).be(\"spinner\", \"item\"), unref(ns).is(\"active\", time === unref(timePartials)[item]), unref(ns).is(\"disabled\", unref(timeList)[item][time])])\n          }, [unref(isNumber)(time) ? (openBlock(), createElementBlock(Fragment, {\n            key: 0\n          }, [item === \"hours\" ? (openBlock(), createElementBlock(Fragment, {\n            key: 0\n          }, [createTextVNode(toDisplayString((\"0\" + (_ctx.amPmMode ? time % 12 || 12 : time)).slice(-2)) + toDisplayString(getAmPmFlag(time)), 1)], 64)) : (openBlock(), createElementBlock(Fragment, {\n            key: 1\n          }, [createTextVNode(toDisplayString((\"0\" + time).slice(-2)), 1)], 64))], 64)) : createCommentVNode(\"v-if\", true)], 2);\n        }), 128))], 2)], 42, [\"onMouseenter\"]);\n      }), 128)) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar TimeSpinner = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"basic-time-spinner.vue\"]]);\nexport { TimeSpinner as default };", "map": {"version": 3, "names": ["pickerBase", "inject", "isRange", "format", "props", "ns", "useNamespace", "getHoursList", "getMinutesList", "getSecondsList", "getTimeLists", "disabledHours", "disabledMinutes", "disabledSeconds", "isScrolling", "currentScrollbar", "ref", "listHoursRef", "listMinutesRef", "listSecondsRef", "listRefsMap", "hours", "minutes", "seconds", "spinnerItems", "computed", "showSeconds", "timeUnits", "slice", "timePartials", "spinnerDate", "hour", "minute", "second", "timeList", "unref", "role", "compare", "arrowControlTimeList", "buildTimeList", "debouncedResetScroll", "debounce", "type", "adjustCurrentSpinner", "getAmPmFlag", "shouldShowAmPm", "amPmMode", "isCapital", "content", "toUpperCase", "emitSelectRange", "range", "DEFAULT_FORMATS_TIME", "left", "right", "emit", "value", "adjustSpinner", "adjustSpinners", "getScrollbarElement", "el", "querySelector", "namespace", "arrowControl", "scrollbar", "$el", "scrollTop", "Math", "max", "typeItemHeight", "listItem", "Number", "parseFloat", "getStyle", "onIncrement", "scrollDown", "onDecrement", "step", "label", "now", "total", "next", "findNextUnDisabled", "modifyDateField", "nextTick", "list", "isDisabled", "changeTo", "CHANGE_EVENT", "handleClick", "disabled", "handleScroll", "min", "round", "scrollBarHeight", "offsetHeight", "bindScrollEvent", "bindFunction", "onscroll", "onMounted", "setRef", "watch", "_ctx", "_cache", "openBlock", "createElementBlock", "class", "normalizeClass", "b", "Fragment", "key", "renderList", "item", "createBlock", "ElScrollbar", "ref_for"], "sources": ["../../../../../../../packages/components/time-picker/src/time-picker-com/basic-time-spinner.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.b('spinner'), { 'has-seconds': showSeconds }]\">\n    <template v-if=\"!arrowControl\">\n      <el-scrollbar\n        v-for=\"item in spinnerItems\"\n        :key=\"item\"\n        :ref=\"(scrollbar: unknown) => setRef(scrollbar as any, item)\"\n        :class=\"ns.be('spinner', 'wrapper')\"\n        wrap-style=\"max-height: inherit;\"\n        :view-class=\"ns.be('spinner', 'list')\"\n        noresize\n        tag=\"ul\"\n        @mouseenter=\"emitSelectRange(item)\"\n        @mousemove=\"adjustCurrentSpinner(item)\"\n      >\n        <li\n          v-for=\"(disabled, key) in timeList[item]\"\n          :key=\"key\"\n          :class=\"[\n            ns.be('spinner', 'item'),\n            ns.is('active', key === timePartials[item]),\n            ns.is('disabled', disabled),\n          ]\"\n          @click=\"handleClick(item, { value: key, disabled })\"\n        >\n          <template v-if=\"item === 'hours'\">\n            {{ ('0' + (amPmMode ? key % 12 || 12 : key)).slice(-2)\n            }}{{ getAmPmFlag(key) }}\n          </template>\n          <template v-else>\n            {{ ('0' + key).slice(-2) }}\n          </template>\n        </li>\n      </el-scrollbar>\n    </template>\n    <template v-if=\"arrowControl\">\n      <div\n        v-for=\"item in spinnerItems\"\n        :key=\"item\"\n        :class=\"[ns.be('spinner', 'wrapper'), ns.is('arrow')]\"\n        @mouseenter=\"emitSelectRange(item)\"\n      >\n        <el-icon\n          v-repeat-click=\"onDecrement\"\n          :class=\"['arrow-up', ns.be('spinner', 'arrow')]\"\n        >\n          <arrow-up />\n        </el-icon>\n        <el-icon\n          v-repeat-click=\"onIncrement\"\n          :class=\"['arrow-down', ns.be('spinner', 'arrow')]\"\n        >\n          <arrow-down />\n        </el-icon>\n        <ul :class=\"ns.be('spinner', 'list')\">\n          <li\n            v-for=\"(time, key) in arrowControlTimeList[item]\"\n            :key=\"key\"\n            :class=\"[\n              ns.be('spinner', 'item'),\n              ns.is('active', time === timePartials[item]),\n              ns.is('disabled', timeList[item][time!]),\n            ]\"\n          >\n            <template v-if=\"isNumber(time)\">\n              <template v-if=\"item === 'hours'\">\n                {{ ('0' + (amPmMode ? time % 12 || 12 : time)).slice(-2)\n                }}{{ getAmPmFlag(time) }}\n              </template>\n              <template v-else>\n                {{ ('0' + time).slice(-2) }}\n              </template>\n            </template>\n          </li>\n        </ul>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, nextTick, onMounted, ref, unref, watch } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { vRepeatClick } from '@element-plus/directives'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElIcon from '@element-plus/components/icon'\nimport { ArrowDown, ArrowUp } from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { getStyle, isNumber } from '@element-plus/utils'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { DEFAULT_FORMATS_TIME, timeUnits } from '../constants'\nimport { buildTimeList } from '../utils'\nimport { basicTimeSpinnerProps } from '../props/basic-time-spinner'\nimport { getTimeLists } from '../composables/use-time-picker'\n\nimport type { Ref } from 'vue'\nimport type { ScrollbarInstance } from '@element-plus/components/scrollbar'\nimport type { TimeUnit } from '../constants'\nimport type { TimeList } from '../utils'\n\nconst props = defineProps(basicTimeSpinnerProps)\nconst pickerBase = inject('EP_PICKER_BASE') as any\nconst { isRange, format } = pickerBase.props\nconst emit = defineEmits([CHANGE_EVENT, 'select-range', 'set-option'])\n\nconst ns = useNamespace('time')\n\nconst { getHoursList, getMinutesList, getSecondsList } = getTimeLists(\n  props.disabledHours,\n  props.disabledMinutes,\n  props.disabledSeconds\n)\n\n// data\nlet isScrolling = false\n\nconst currentScrollbar = ref<TimeUnit>()\nconst listHoursRef = ref<ScrollbarInstance>()\nconst listMinutesRef = ref<ScrollbarInstance>()\nconst listSecondsRef = ref<ScrollbarInstance>()\nconst listRefsMap: Record<TimeUnit, Ref<ScrollbarInstance | undefined>> = {\n  hours: listHoursRef,\n  minutes: listMinutesRef,\n  seconds: listSecondsRef,\n}\n\n// computed\nconst spinnerItems = computed(() => {\n  return props.showSeconds ? timeUnits : timeUnits.slice(0, 2)\n})\n\nconst timePartials = computed<Record<TimeUnit, number>>(() => {\n  const { spinnerDate } = props\n  const hours = spinnerDate.hour()\n  const minutes = spinnerDate.minute()\n  const seconds = spinnerDate.second()\n  return { hours, minutes, seconds }\n})\n\nconst timeList = computed(() => {\n  const { hours, minutes } = unref(timePartials)\n  const { role, spinnerDate } = props\n  const compare = !isRange ? spinnerDate : undefined\n  return {\n    hours: getHoursList(role, compare),\n    minutes: getMinutesList(hours, role, compare),\n    seconds: getSecondsList(hours, minutes, role, compare),\n  }\n})\n\nconst arrowControlTimeList = computed<Record<TimeUnit, TimeList>>(() => {\n  const { hours, minutes, seconds } = unref(timePartials)\n\n  return {\n    hours: buildTimeList(hours, 23),\n    minutes: buildTimeList(minutes, 59),\n    seconds: buildTimeList(seconds, 59),\n  }\n})\n\nconst debouncedResetScroll = debounce((type) => {\n  isScrolling = false\n  adjustCurrentSpinner(type)\n}, 200)\n\nconst getAmPmFlag = (hour: number) => {\n  const shouldShowAmPm = !!props.amPmMode\n  if (!shouldShowAmPm) return ''\n  const isCapital = props.amPmMode === 'A'\n  // todo locale\n  let content = hour < 12 ? ' am' : ' pm'\n  if (isCapital) content = content.toUpperCase()\n  return content\n}\n\nconst emitSelectRange = (type: TimeUnit) => {\n  let range = [0, 0]\n  if (!format || format === DEFAULT_FORMATS_TIME) {\n    switch (type) {\n      case 'hours':\n        range = [0, 2]\n        break\n      case 'minutes':\n        range = [3, 5]\n        break\n      case 'seconds':\n        range = [6, 8]\n        break\n    }\n  }\n  const [left, right] = range\n\n  emit('select-range', left, right)\n  currentScrollbar.value = type\n}\n\nconst adjustCurrentSpinner = (type: TimeUnit) => {\n  adjustSpinner(type, unref(timePartials)[type])\n}\n\nconst adjustSpinners = () => {\n  adjustCurrentSpinner('hours')\n  adjustCurrentSpinner('minutes')\n  adjustCurrentSpinner('seconds')\n}\n\nconst getScrollbarElement = (el: HTMLElement) =>\n  el.querySelector(`.${ns.namespace.value}-scrollbar__wrap`) as HTMLElement\n\nconst adjustSpinner = (type: TimeUnit, value: number) => {\n  if (props.arrowControl) return\n  const scrollbar = unref(listRefsMap[type])\n  if (scrollbar && scrollbar.$el) {\n    getScrollbarElement(scrollbar.$el).scrollTop = Math.max(\n      0,\n      value * typeItemHeight(type)\n    )\n  }\n}\n\nconst typeItemHeight = (type: TimeUnit): number => {\n  const scrollbar = unref(listRefsMap[type])\n  const listItem = scrollbar?.$el.querySelector('li')\n  if (listItem) {\n    return Number.parseFloat(getStyle(listItem, 'height')) || 0\n  }\n  return 0\n}\n\nconst onIncrement = () => {\n  scrollDown(1)\n}\n\nconst onDecrement = () => {\n  scrollDown(-1)\n}\n\nconst scrollDown = (step: number) => {\n  if (!currentScrollbar.value) {\n    emitSelectRange('hours')\n  }\n\n  const label = currentScrollbar.value!\n  const now = unref(timePartials)[label]\n  const total = currentScrollbar.value === 'hours' ? 24 : 60\n  const next = findNextUnDisabled(label, now, step, total)\n\n  modifyDateField(label, next)\n  adjustSpinner(label, next)\n  nextTick(() => emitSelectRange(label))\n}\n\nconst findNextUnDisabled = (\n  type: TimeUnit,\n  now: number,\n  step: number,\n  total: number\n) => {\n  let next = (now + step + total) % total\n  const list = unref(timeList)[type]\n  while (list[next] && next !== now) {\n    next = (next + step + total) % total\n  }\n  return next\n}\n\nconst modifyDateField = (type: TimeUnit, value: number) => {\n  const list = unref(timeList)[type]\n  const isDisabled = list[value]\n  if (isDisabled) return\n\n  const { hours, minutes, seconds } = unref(timePartials)\n\n  let changeTo\n  switch (type) {\n    case 'hours':\n      changeTo = props.spinnerDate.hour(value).minute(minutes).second(seconds)\n      break\n    case 'minutes':\n      changeTo = props.spinnerDate.hour(hours).minute(value).second(seconds)\n      break\n    case 'seconds':\n      changeTo = props.spinnerDate.hour(hours).minute(minutes).second(value)\n      break\n  }\n  emit(CHANGE_EVENT, changeTo)\n}\n\nconst handleClick = (\n  type: TimeUnit,\n  { value, disabled }: { value: number; disabled: boolean }\n) => {\n  if (!disabled) {\n    modifyDateField(type, value)\n    emitSelectRange(type)\n    adjustSpinner(type, value)\n  }\n}\n\nconst handleScroll = (type: TimeUnit) => {\n  const scrollbar = unref(listRefsMap[type])\n  if (!scrollbar) return\n\n  isScrolling = true\n  debouncedResetScroll(type)\n  const value = Math.min(\n    Math.round(\n      (getScrollbarElement(scrollbar.$el).scrollTop -\n        (scrollBarHeight(type) * 0.5 - 10) / typeItemHeight(type) +\n        3) /\n        typeItemHeight(type)\n    ),\n    type === 'hours' ? 23 : 59\n  )\n  modifyDateField(type, value)\n}\n\nconst scrollBarHeight = (type: TimeUnit) => {\n  return unref(listRefsMap[type])!.$el.offsetHeight\n}\n\nconst bindScrollEvent = () => {\n  const bindFunction = (type: TimeUnit) => {\n    const scrollbar = unref(listRefsMap[type])\n    if (scrollbar && scrollbar.$el) {\n      getScrollbarElement(scrollbar.$el).onscroll = () => {\n        // TODO: scroll is emitted when set scrollTop programmatically\n        // should find better solutions in the future!\n        handleScroll(type)\n      }\n    }\n  }\n  bindFunction('hours')\n  bindFunction('minutes')\n  bindFunction('seconds')\n}\n\nonMounted(() => {\n  nextTick(() => {\n    !props.arrowControl && bindScrollEvent()\n    adjustSpinners()\n    // set selection on the first hour part\n    if (props.role === 'start') emitSelectRange('hours')\n  })\n})\n\nconst setRef = (scrollbar: ScrollbarInstance | null, type: TimeUnit) => {\n  listRefsMap[type].value = scrollbar ?? undefined\n}\n\nemit('set-option', [`${props.role}_scrollDown`, scrollDown])\nemit('set-option', [`${props.role}_emitSelectRange`, emitSelectRange])\n\nwatch(\n  () => props.spinnerDate,\n  () => {\n    if (isScrolling) return\n    adjustSpinners()\n  }\n)\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IAqGM,MAAAA,UAAA,GAAaC,MAAA,CAAO,gBAAgB;IAC1C,MAAM;MAAEC,OAAA;MAASC;IAAO,IAAIH,UAAW,CAAAI,KAAA;IAGjC,MAAAC,EAAA,GAAKC,YAAA,CAAa,MAAM;IAE9B,MAAM;MAAEC,YAAA;MAAcC,cAAgB;MAAAC;IAAA,CAAmB,GAAAC,YAAA,CAAAN,KAAA,CAAAO,aAAA,EAAAP,KAAA,CAAAQ,eAAA,EAAAR,KAAA,CAAAS,eAAA;IAAA,IACjDC,WAAA;IAAA,MACAC,gBAAA,GAAAC,GAAA;IAAA,MACAC,YAAA,GAAAD,GAAA;IACR,MAAAE,cAAA,GAAAF,GAAA;IAGA,MAAkBG,cAAA,GAAAH,GAAA;IAElB,MAAMI,WAAA;MACNC,KAAA,EAAAJ,YAAqB;MACrBK,OAAA,EAAAJ,cAA8C;MAC9CK,OAAA,EAAAJ;IACA;IAA0E,MACjEK,YAAA,GAAAC,QAAA;MACP,OAASrB,KAAA,CAAAsB,WAAA,GAAAC,SAAA,GAAAA,SAAA,CAAAC,KAAA;IAAA,EACT;IACF,MAAAC,YAAA,GAAAJ,QAAA;MAGM;QAAAK;MAAe,IAAA1B,KAAe;MAClC,MAAAiB,KAAA,GAA2BS,WAAA,CAAAC,IAAA;MAC5B,MAAAT,OAAA,GAAAQ,WAAA,CAAAE,MAAA;MAEK,MAAAT,OAAA,GAAAO,WAAA,CAAkDG,MAAM;MACtD;QAAAZ,KAAA;QAAAC,OAAkB;QAAAC;MAAA;IACxB,CAAM;IACA,MAAAW,QAAA,GAAAT,QAAA,OAA6B;MAC7B;QAAAJ,KAAA;QAAAC;MAAA,IAAAa,KAA6B,CAAAN,YAAA;MAC5B;QAAAO,IAAS;QAAAN;MAAS,CAAQ,GAAA1B,KAAA;MAClC,MAAAiC,OAAA,IAAAnC,OAAA,GAAA4B,WAAA;MAEK;QACJT,KAAQ,EAAAd,YAAe,CAAA6B,IAAA,EAAAC,OAAsB;QACvCf,OAAQ,EAAAd,cAAA,CAAYa,KAAI,EAAAe,IAAA,EAAAC,OAAA;QACxBd,OAAA,EAAAd,cAAW,CAAAY,KAAwB,EAAAC,OAAA,EAAAc,IAAA,EAAAC,OAAA;MACzC,CAAO;IAAA,CACL;IAAiC,MACxBC,oBAAA,GAAsBb,QAAA,OAAa;MAAA,MACnC;QAAAJ,KAAA;QAAAC,OAAA;QAAAC;MAAsB,IAAAY,KAAA,CAASN,YAAa;MACvD;QACDR,KAAA,EAAAkB,aAAA,CAAAlB,KAAA;QAEKC,OAAA,EAAAiB,aAAA,CAAAjB,OAAA,IAA4D,CAAM;QACtEC,OAAe,EAAAgB,aAAS,CAAQhB,OAAA,IAAI;MAEpC,CAAO;IAAA,CACL;IACA,MAAAiB,oBAAuB,GAAAC,QAAW,CAAAC,IAAA;MAClC5B,WAAS,GAAc;MACzB6B,oBAAA,CAAAD,IAAA;IAAA,CACD;IAEK,MAAAE,WAAA,GAAAb,IAAA,IAAgC;MACtB,MAAAc,cAAA,KAAAzC,KAAA,CAAA0C,QAAA;MACd,KAAAD,cAAA,EACI;MAEA,MAAAE,SAAA,GAAc3C,KAAkB,CAAA0C,QAAA;MAC9B,IAAAE,OAAA,GAAAjB,IAAA,KAAiB,GAAQ;MAC3B,IAAAgB,SAAA,EACEC,OAAA,GAAAA,OAAA,CAAAC,WAA+B;MAEjC,OAAAD,OAAU;IACd,CAAI;IACG,MAAAE,eAAA,GAAAR,IAAA;MACT,IAAAS,KAAA;MAEM,KAAAhD,MAAA,IAAAA,MAAA,KAAsCiD,oBAAA;QACtC,QAAAV,IAAS;UACT,KAAW;YACbS,KAAc;YACP;UACK,cAAI;YACZA,KAAA;YACG;UACK,cAAI;YACZA,KAAA;YACG;QACH;MACA;MACJ,OAAAE,IAAA,EAAAC,KAAA,IAAAH,KAAA;MACFI,IAAA,iBAAAF,IAAA,EAAAC,KAAA;MACMvC,gBAAO,CAAAyC,KAAS,GAAAd,IAAA;IAEtB,CAAK;IACL,MAAAC,oBAAyB,GAAAD,IAAA;MAC3Be,aAAA,CAAAf,IAAA,EAAAP,KAAA,CAAAN,YAAA,EAAAa,IAAA;IAEA,CAAM;IACJ,MAAAgB,cAAoB,GAAAA,CAAA,KAAkB;MACxCf,oBAAA;MAEAA,oBAAA,CAAuB,SAAM;MAC3BA,oBAAA,CAAqB,SAAO;IAC5B;IACA,MAAAgB,mBAA8B,GAAAC,EAAA,IAAAA,EAAA,CAAAC,aAAA,KAAAxD,EAAA,CAAAyD,SAAA,CAAAN,KAAA;IAChC,MAAAC,aAAA,GAAAA,CAAAf,IAAA,EAAAc,KAAA;MAEM,IAAApD,KAAA,CAAA2D,YAAA,EAGA;MACJ,MAAAC,SAAwB,GAAA7B,KAAA,CAAAf,WAAA,CAAAsB,IAAA;MACxB,IAAAsB,SAAkB,IAAAA,SAAkB,CAAAC,GAAA;QAChCN,mBAAA,CAAAK,SAA4B,CAAAC,GAAA,EAAAC,SAAA,GAAAC,IAAA,CAAAC,GAAA,IAAAZ,KAAA,GAAAa,cAAA,CAAA3B,IAAA;MAC9B;IAAoD,CAClD;IACA,MAAA2B,cAAA,GAAA3B,IAAA,IAA2B;MAC7B,MAAAsB,SAAA,GAAA7B,KAAA,CAAAf,WAAA,CAAAsB,IAAA;MACF,MAAA4B,QAAA,GAAAN,SAAA,oBAAAA,SAAA,CAAAC,GAAA,CAAAJ,aAAA;MACF,IAAAS,QAAA;QAEM,OAAAC,MAAA,CAAAC,UAA6C,CAAAC,QAAA,CAAAH,QAAA;MACjD;MACA,OAAiB;IACjB;IACE,MAAAI,WAAA,GAAyBA,CAAA;MAC3BC,UAAA;IACA,CAAO;IACT,MAAAC,WAAA,GAAAA,CAAA;MAEAD,UAAA;IACE;IACF,MAAAA,UAAA,GAAAE,IAAA;MAEA,IAAM,CAAA9D,gBAAoB,CAAAyC,KAAA;QACxBN,eAAa;MAAA;MAGT,MAAA4B,KAAA,GAAA/D,gBAA+B,CAAAyC,KAAA;MAC/B,MAAAuB,GAAA,GAAA5C,KAAA,CAAAN,YAAyB,EAAAiD,KAAA;MAC3B,MAAAE,KAAA,GAAAjE,gBAAuB,CAAAyC,KAAA;MACzB,MAAAyB,IAAA,GAAAC,kBAAA,CAAAJ,KAAA,EAAAC,GAAA,EAAAF,IAAA,EAAAG,KAAA;MAEAG,eAA+B,CAAAL,KAAA,EAAAG,IAAA;MAC/BxB,aAAY,CAAAqB,KAAkB,EAAAG,IAAA;MAC9BG,QAAc,OAAAlC,eAAA,CAAiB4B,KAAU;IACzC;IAEA,MAAAI,kBAAA,GAA2BA,CAAAxC,IAAA,EAAAqC,GAAA,EAAAF,IAAA,EAAAG,KAAA;MAC3B,IAAAC,IAAA,IAAAF,GAAA,GAAAF,IAAyB,GAAAG,KAAA,IAAAA,KAAA;MAChB,MAAAK,IAAA,GAAAlD,KAAsB,CAAAD,QAAA,EAAAQ,IAAA;MACjC,OAAA2C,IAAA,CAAAJ,IAAA,KAAAA,IAAA,KAAAF,GAAA;QAEAE,IAA2B,IAAAA,IAAA,GAAAJ,IAAA,GAAAG,KAEzB,IAAAA,KAAA;MAIA;MACA,OAAaC,IAAA;IACb;IACU,MAAAE,eAAA,GAAAA,CAAAzC,IAAuB,EAAAc,KAAA;MACjC,MAAA6B,IAAA,GAAAlD,KAAA,CAAAD,QAAA,EAAAQ,IAAA;MACO,MAAA4C,UAAA,GAAAD,IAAA,CAAA7B,KAAA;MACT,IAAA8B,UAAA,EAEM;MACJ,MAAM;QAAOjE,KAAA;QAAAC,OAAc;QAAAC;MAAM,IAAAY,KAAA,CAAAN,YAAA;MAC3B,IAAA0D,QAAA;MACN,QAAgB7C,IAAA;QAEhB,YAAe;UAEX6C,QAAA,GAAAnF,KAAA,CAAA0B,WAAA,CAAAC,IAAA,CAAAyB,KAAA,EAAAxB,MAAA,CAAAV,OAAA,EAAAW,MAAA,CAAAV,OAAA;UACJ;QACE,KAAK;UACQgE,QAAA,GAAAnF,KAAA,CAAM0B,WAAA,CAAYC,IAAK,CAAAV,KAAK,EAAEW,MAAO,CAAAwB,KAAA,EAAOvB,MAAE,CAAAV,OAAc;UACvE;QACF,KAAK;UACQgE,QAAA,GAAAnF,KAAA,CAAM0B,WAAA,CAAYC,IAAK,CAAAV,KAAK,EAAEW,MAAO,CAAAV,OAAO,EAAAW,MAAc,CAAAuB,KAAA;UACrE;MAAA;MAEWD,IAAA,CAAAiC,YAAA,EAAAD,QAAA;IACX;IACJ,MAAAE,WAAA,GAAAA,CAAA/C,IAAA;MAAAc,KAAA;MAAAkC;IAAA;MACA,KAAKA,QAAA;QACPP,eAAA,CAAAzC,IAAA,EAAAc,KAAA;QAEAN,eAAA,CAAoBR,IAClB;QAGAe,aAAe,CAAAf,IAAA,EAAAc,KAAA;MACb;IACA;IACA,MAAAmC,YAAc,GAAAjD,IAAW;MAC3B,MAAAsB,SAAA,GAAA7B,KAAA,CAAAf,WAAA,CAAAsB,IAAA;MACF,KAAAsB,SAAA,EAEM;MACJlD,WAAkB;MAClB0B,oBAAgB,CAAAE,IAAA;MAEF,MAAAc,KAAA,GAAAW,IAAA,CAAAyB,GAAA,CAAAzB,IAAA,CAAA0B,KAAA,EAAAlC,mBAAA,CAAAK,SAAA,CAAAC,GAAA,EAAAC,SAAA,IAAA4B,eAAA,CAAApD,IAAA,gBAAA2B,cAAA,CAAA3B,IAAA,SAAA2B,cAAA,CAAA3B,IAAA,IAAAA,IAAA;MACdyC,eAAA,CAAAzC,IAAA,EAAyBc,KAAA;IACzB;IAAmB,MACZsC,eAAA,GAAApD,IAAA;MAAA,OACkBP,KAAA,CAAAf,WAAA,CAAAsB,IAAA,GAAAuB,GAAU,CAAG8B,YAAA;IAGb,CACvB;IACA,MAAAC,eAAA,GAAwBA,CAAA;MAC1B,MAAAC,YAAA,GAAAvD,IAAA;QACA,MAAAsB,SAAA,GAAA7B,KAA2B,CAAAf,WAAA,CAAAsB,IAAA;QAC7B,IAAAsB,SAAA,IAAAA,SAAA,CAAAC,GAAA;UAEMN,mBAAmB,CAAmBK,SAAA,CAAAC,GAAA,EAAAiC,QAAA;YAC1CP,YAAa,CAAAjD,IAAA;UAAwB,CACvC;QAEA;MACE,CAAM;MACJuD,YAAkB;MACdA,YAAA;MACFA,YAAA;IAGE;IACFE,SAAA;MACFf,QAAA;QACF,CAAAhF,KAAA,CAAA2D,YAAA,IAAAiC,eAAA;QACAtC,cAAoB;QACpB,IAAAtD,KAAA,CAAAgC,IAAsB,cACtBc,eAAsB;MAAA,CACxB;IAEA;IACE,MAAAkD,MAAe,GAAAA,CAAApC,SAAA,EAAAtB,IAAA;MACZtB,WAAA,CAAAsB,IAAA,EAAAc,KAAA,GAAsCQ,SAAA,WAAAA,SAAA;IACvC,CAAe;IAEfT,IAAA,aAAU,MAASnD,KAAS,CAAAgC,IAAA,eAAgBuC,UAAO;IAAApB,IACpD,mBAAAnD,KAAA,CAAAgC,IAAA,oBAAAc,eAAA;IACHmD,KAAC,OAAAjG,KAAA,CAAA0B,WAAA;MAEK,IAAAhB,WAAU,EACF;MACd4C,cAAA;IAEA;IACA,QAAA4C,IAAA,EAAAC,MAAoB;MAEpB,OAAAC,SAAA,IAAAC,kBAAA;QAAAC,KACc,EAAAC,cAAA,EAAAxE,KAAA,CAAA9B,EAAA,EAAAuG,CAAA;UAAA,eAAAN,IAAA,CAAA5E;QAAA;MAAA,CACN,GACJ,CAAA4E,IAAiB,CAAAvC,YAAA,IAAAyC,SAAA,QAAAC,kBAAA,CAAAI,QAAA;QAAAC,GAAA;MAAA,GAAAC,UAAA,CAAA5E,KAAA,CAAAX,YAAA,GAAAwF,IAAA;QACF,OAAAR,SAAA,IAAAS,WAAA,CAAA9E,KAAA,CAAA+E,WAAA;UACjBJ,GAAA,EAAAE,IAAA;UACFG,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}