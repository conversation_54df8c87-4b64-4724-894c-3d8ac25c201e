{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, withKeys as _withKeys, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"login-container\"\n};\nconst _hoisted_2 = {\n  class: \"login-card\"\n};\nconst _hoisted_3 = {\n  class: \"login-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_tab_pane = _resolveComponent(\"el-tab-pane\");\n  const _component_el_tabs = _resolveComponent(\"el-tabs\");\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n    class: \"login-header\"\n  }, [_createElementVNode(\"h2\", null, \"基于国密算法的图书馆自习室座位管理系统\")], -1 /* HOISTED */)), _createVNode(_component_el_tabs, {\n    modelValue: $setup.activeTab,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.activeTab = $event),\n    class: \"login-tabs\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_tab_pane, {\n      label: \"密码登录\",\n      name: \"password\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form, {\n        ref: \"passwordFormRef\",\n        model: $setup.passwordForm,\n        rules: $setup.passwordRules,\n        \"label-position\": \"top\",\n        onKeyup: _withKeys($setup.handlePasswordLogin, [\"enter\"])\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"学号\",\n          prop: \"studentId\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.passwordForm.studentId,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.passwordForm.studentId = $event),\n            placeholder: \"请输入学号\",\n            \"prefix-icon\": $setup.User\n          }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"密码\",\n          prop: \"password\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.passwordForm.password,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.passwordForm.password = $event),\n            type: \"password\",\n            placeholder: \"请输入密码\",\n            \"prefix-icon\": $setup.Lock,\n            \"show-password\": \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, null, {\n          default: _withCtx(() => [_createVNode(_component_el_button, {\n            type: \"primary\",\n            class: \"login-button\",\n            loading: $setup.loading,\n            onClick: $setup.handlePasswordLogin\n          }, {\n            default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\" 登录 \")])),\n            _: 1 /* STABLE */,\n            __: [5]\n          }, 8 /* PROPS */, [\"loading\", \"onClick\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\", \"rules\", \"onKeyup\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_tab_pane, {\n      label: \"证书登录\",\n      name: \"certificate\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form, {\n        ref: \"certificateFormRef\",\n        model: $setup.certificateForm,\n        rules: $setup.certificateRules,\n        \"label-position\": \"top\",\n        onKeyup: _withKeys($setup.handleCertificateLogin, [\"enter\"])\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"学号\",\n          prop: \"studentId\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.certificateForm.studentId,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.certificateForm.studentId = $event),\n            placeholder: \"请输入学号\",\n            \"prefix-icon\": $setup.User\n          }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"私钥\",\n          prop: \"privateKey\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.certificateForm.privateKey,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.certificateForm.privateKey = $event),\n            type: \"textarea\",\n            rows: 4,\n            placeholder: \"请输入SM2私钥\",\n            \"show-password\": \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, null, {\n          default: _withCtx(() => [_createVNode(_component_el_button, {\n            type: \"primary\",\n            class: \"login-button\",\n            loading: $setup.loading,\n            onClick: $setup.handleCertificateLogin\n          }, {\n            default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\" 登录 \")])),\n            _: 1 /* STABLE */,\n            __: [6]\n          }, 8 /* PROPS */, [\"loading\", \"onClick\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\", \"rules\", \"onKeyup\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"p\", null, [_cache[8] || (_cache[8] = _createTextVNode(\" 还没有账号？ \")), _createVNode(_component_router_link, {\n    to: \"/register\"\n  }, {\n    default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\"立即注册\")])),\n    _: 1 /* STABLE */,\n    __: [7]\n  })])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_tabs", "modelValue", "$setup", "activeTab", "_cache", "$event", "default", "_withCtx", "_component_el_tab_pane", "label", "name", "_component_el_form", "ref", "model", "passwordForm", "rules", "passwordRules", "onKeyup", "_with<PERSON><PERSON><PERSON>", "handlePasswordLogin", "_component_el_form_item", "prop", "_component_el_input", "studentId", "placeholder", "User", "_", "password", "type", "Lock", "_component_el_button", "loading", "onClick", "_createTextVNode", "__", "certificateForm", "certificateRules", "handleCertificateLogin", "privateKey", "rows", "_hoisted_3", "_component_router_link", "to"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-card\">\n      <div class=\"login-header\">\n        <h2>基于国密算法的图书馆自习室座位管理系统</h2>\n      </div>\n\n      <el-tabs v-model=\"activeTab\" class=\"login-tabs\">\n        <el-tab-pane label=\"密码登录\" name=\"password\">\n          <el-form\n            ref=\"passwordFormRef\"\n            :model=\"passwordForm\"\n            :rules=\"passwordRules\"\n            label-position=\"top\"\n            @keyup.enter=\"handlePasswordLogin\"\n          >\n            <el-form-item label=\"学号\" prop=\"studentId\">\n              <el-input\n                v-model=\"passwordForm.studentId\"\n                placeholder=\"请输入学号\"\n                :prefix-icon=\"User\"\n              />\n            </el-form-item>\n\n            <el-form-item label=\"密码\" prop=\"password\">\n              <el-input\n                v-model=\"passwordForm.password\"\n                type=\"password\"\n                placeholder=\"请输入密码\"\n                :prefix-icon=\"Lock\"\n                show-password\n              />\n            </el-form-item>\n\n            <el-form-item>\n              <el-button\n                type=\"primary\"\n                class=\"login-button\"\n                :loading=\"loading\"\n                @click=\"handlePasswordLogin\"\n              >\n                登录\n              </el-button>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"证书登录\" name=\"certificate\">\n          <el-form\n            ref=\"certificateFormRef\"\n            :model=\"certificateForm\"\n            :rules=\"certificateRules\"\n            label-position=\"top\"\n            @keyup.enter=\"handleCertificateLogin\"\n          >\n            <el-form-item label=\"学号\" prop=\"studentId\">\n              <el-input\n                v-model=\"certificateForm.studentId\"\n                placeholder=\"请输入学号\"\n                :prefix-icon=\"User\"\n              />\n            </el-form-item>\n\n            <el-form-item label=\"私钥\" prop=\"privateKey\">\n              <el-input\n                v-model=\"certificateForm.privateKey\"\n                type=\"textarea\"\n                :rows=\"4\"\n                placeholder=\"请输入SM2私钥\"\n                show-password\n              />\n            </el-form-item>\n\n            <el-form-item>\n              <el-button\n                type=\"primary\"\n                class=\"login-button\"\n                :loading=\"loading\"\n                @click=\"handleCertificateLogin\"\n              >\n                登录\n              </el-button>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div class=\"login-footer\">\n        <p>\n          还没有账号？\n          <router-link to=\"/register\">立即注册</router-link>\n        </p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { User, Lock } from \"@element-plus/icons-vue\";\nimport { SM3Hasher, SM2Crypto } from \"@/utils/crypto\";\n\nexport default {\n  name: \"LoginView\",\n  setup() {\n    const store = useStore();\n    const router = useRouter();\n\n    const passwordFormRef = ref(null);\n    const certificateFormRef = ref(null);\n    const activeTab = ref(\"password\");\n    const loading = ref(false);\n\n    // 密码登录表单\n    const passwordForm = reactive({\n      studentId: \"\",\n      password: \"\",\n    });\n\n    // 证书登录表单\n    const certificateForm = reactive({\n      studentId: \"\",\n      privateKey: \"\",\n    });\n\n    // 表单验证规则\n    const passwordRules = {\n      studentId: [\n        { required: true, message: \"请输入学号\", trigger: \"blur\" },\n        { min: 5, max: 20, message: \"学号长度应为5-20个字符\", trigger: \"blur\" },\n      ],\n      password: [\n        { required: true, message: \"请输入密码\", trigger: \"blur\" },\n        { min: 6, max: 20, message: \"密码长度应为6-20个字符\", trigger: \"blur\" },\n      ],\n    };\n\n    const certificateRules = {\n      studentId: [\n        { required: true, message: \"请输入学号\", trigger: \"blur\" },\n        { min: 5, max: 20, message: \"学号长度应为5-20个字符\", trigger: \"blur\" },\n      ],\n      privateKey: [\n        { required: true, message: \"请输入SM2私钥\", trigger: \"blur\" },\n      ],\n    };\n\n    // 密码登录处理\n    const handlePasswordLogin = async () => {\n      if (!passwordFormRef.value) return;\n\n      await passwordFormRef.value.validate(async (valid) => {\n        if (!valid) return;\n\n        try {\n          loading.value = true;\n\n          // 对密码进行SM3哈希\n          const hashedPassword = SM3Hasher.hash(passwordForm.password);\n\n          // 调用登录接口\n          await store.dispatch(\"user/login\", {\n            studentId: passwordForm.studentId,\n            password: hashedPassword,\n          });\n\n          ElMessage.success(\"登录成功\");\n          router.push(\"/dashboard\");\n        } catch (error) {\n          ElMessage.error(error.message || \"登录失败，请检查学号和密码\");\n        } finally {\n          loading.value = false;\n        }\n      });\n    };\n\n    // 证书登录处理\n    const handleCertificateLogin = async () => {\n      if (!certificateFormRef.value) return;\n\n      await certificateFormRef.value.validate(async (valid) => {\n        if (!valid) return;\n\n        try {\n          loading.value = true;\n\n          // 获取SM2挑战值\n          const challengeResponse = await store.dispatch(\n            \"user/getSM2Challenge\",\n            {\n              studentId: certificateForm.studentId,\n            }\n          );\n\n          const { challenge } = challengeResponse;\n\n          // 使用私钥对挑战值进行签名\n          const signature = SM2Crypto.sign(\n            certificateForm.privateKey,\n            challenge\n          );\n\n          // 调用SM2登录接口\n          await store.dispatch(\"user/sm2Login\", {\n            studentId: certificateForm.studentId,\n            signature,\n          });\n\n          ElMessage.success(\"登录成功\");\n          router.push(\"/dashboard\");\n        } catch (error) {\n          ElMessage.error(error.message || \"证书登录失败，请检查学号和私钥\");\n        } finally {\n          loading.value = false;\n        }\n      });\n    };\n\n    return {\n      passwordFormRef,\n      certificateFormRef,\n      activeTab,\n      loading,\n      passwordForm,\n      certificateForm,\n      passwordRules,\n      certificateRules,\n      handlePasswordLogin,\n      handleCertificateLogin,\n      User,\n      Lock,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.login-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background-image: url(\"@/assets/background.jpg\");\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  position: relative;\n}\n\n.login-container::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(255, 255, 255, 0.7);\n  backdrop-filter: blur(3px);\n}\n\n.login-card {\n  width: 400px;\n  padding: 30px;\n  background-color: rgba(255, 255, 255, 0.9);\n  border-radius: 8px;\n  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.2);\n  position: relative;\n  z-index: 1;\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n\n  h2 {\n    font-size: 22px;\n    color: #303133;\n    margin: 0;\n    font-weight: 600;\n    line-height: 1.4;\n  }\n}\n\n.login-tabs {\n  margin-bottom: 20px;\n}\n\n.login-button {\n  width: 100%;\n}\n\n.login-footer {\n  margin-top: 20px;\n  text-align: center;\n  font-size: 14px;\n  color: #606266;\n\n  a {\n    color: #409eff;\n    text-decoration: none;\n\n    &:hover {\n      text-decoration: underline;\n    }\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAY;;EAqFhBA,KAAK,EAAC;AAAc;;;;;;;;;uBAtF7BC,mBAAA,CA6FM,OA7FNC,UA6FM,GA5FJC,mBAAA,CA2FM,OA3FNC,UA2FM,G,0BA1FJD,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAc,IACvBG,mBAAA,CAA4B,YAAxB,qBAAmB,E,sBAGzBE,YAAA,CA8EUC,kBAAA;IArFhBC,UAAA,EAOwBC,MAAA,CAAAC,SAAS;IAPjC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAOwBH,MAAA,CAAAC,SAAS,GAAAE,MAAA;IAAEX,KAAK,EAAC;;IAPzCY,OAAA,EAAAC,QAAA,CAQQ,MAqCc,CArCdR,YAAA,CAqCcS,sBAAA;MArCDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MARvCJ,OAAA,EAAAC,QAAA,CASU,MAmCU,CAnCVR,YAAA,CAmCUY,kBAAA;QAlCRC,GAAG,EAAC,iBAAiB;QACpBC,KAAK,EAAEX,MAAA,CAAAY,YAAY;QACnBC,KAAK,EAAEb,MAAA,CAAAc,aAAa;QACrB,gBAAc,EAAC,KAAK;QACnBC,OAAK,EAdlBC,SAAA,CAc0BhB,MAAA,CAAAiB,mBAAmB;;QAd7Cb,OAAA,EAAAC,QAAA,CAgBY,MAMe,CANfR,YAAA,CAMeqB,uBAAA;UANDX,KAAK,EAAC,IAAI;UAACY,IAAI,EAAC;;UAhB1Cf,OAAA,EAAAC,QAAA,CAiBc,MAIE,CAJFR,YAAA,CAIEuB,mBAAA;YArBhBrB,UAAA,EAkByBC,MAAA,CAAAY,YAAY,CAACS,SAAS;YAlB/C,uBAAAnB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAkByBH,MAAA,CAAAY,YAAY,CAACS,SAAS,GAAAlB,MAAA;YAC/BmB,WAAW,EAAC,OAAO;YAClB,aAAW,EAAEtB,MAAA,CAAAuB;;UApB9BC,CAAA;YAwBY3B,YAAA,CAQeqB,uBAAA;UARDX,KAAK,EAAC,IAAI;UAACY,IAAI,EAAC;;UAxB1Cf,OAAA,EAAAC,QAAA,CAyBc,MAME,CANFR,YAAA,CAMEuB,mBAAA;YA/BhBrB,UAAA,EA0ByBC,MAAA,CAAAY,YAAY,CAACa,QAAQ;YA1B9C,uBAAAvB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA0ByBH,MAAA,CAAAY,YAAY,CAACa,QAAQ,GAAAtB,MAAA;YAC9BuB,IAAI,EAAC,UAAU;YACfJ,WAAW,EAAC,OAAO;YAClB,aAAW,EAAEtB,MAAA,CAAA2B,IAAI;YAClB,eAAa,EAAb;;UA9BhBH,CAAA;YAkCY3B,YAAA,CASeqB,uBAAA;UA3C3Bd,OAAA,EAAAC,QAAA,CAmCc,MAOY,CAPZR,YAAA,CAOY+B,oBAAA;YANVF,IAAI,EAAC,SAAS;YACdlC,KAAK,EAAC,cAAc;YACnBqC,OAAO,EAAE7B,MAAA,CAAA6B,OAAO;YAChBC,OAAK,EAAE9B,MAAA,CAAAiB;;YAvCxBb,OAAA,EAAAC,QAAA,CAwCe,MAEDH,MAAA,QAAAA,MAAA,OA1Cd6B,gBAAA,CAwCe,MAED,E;YA1CdP,CAAA;YAAAQ,EAAA;;UAAAR,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QA+CQ3B,YAAA,CAqCcS,sBAAA;MArCDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MA/CvCJ,OAAA,EAAAC,QAAA,CAgDU,MAmCU,CAnCVR,YAAA,CAmCUY,kBAAA;QAlCRC,GAAG,EAAC,oBAAoB;QACvBC,KAAK,EAAEX,MAAA,CAAAiC,eAAe;QACtBpB,KAAK,EAAEb,MAAA,CAAAkC,gBAAgB;QACxB,gBAAc,EAAC,KAAK;QACnBnB,OAAK,EArDlBC,SAAA,CAqD0BhB,MAAA,CAAAmC,sBAAsB;;QArDhD/B,OAAA,EAAAC,QAAA,CAuDY,MAMe,CANfR,YAAA,CAMeqB,uBAAA;UANDX,KAAK,EAAC,IAAI;UAACY,IAAI,EAAC;;UAvD1Cf,OAAA,EAAAC,QAAA,CAwDc,MAIE,CAJFR,YAAA,CAIEuB,mBAAA;YA5DhBrB,UAAA,EAyDyBC,MAAA,CAAAiC,eAAe,CAACZ,SAAS;YAzDlD,uBAAAnB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAyDyBH,MAAA,CAAAiC,eAAe,CAACZ,SAAS,GAAAlB,MAAA;YAClCmB,WAAW,EAAC,OAAO;YAClB,aAAW,EAAEtB,MAAA,CAAAuB;;UA3D9BC,CAAA;YA+DY3B,YAAA,CAQeqB,uBAAA;UARDX,KAAK,EAAC,IAAI;UAACY,IAAI,EAAC;;UA/D1Cf,OAAA,EAAAC,QAAA,CAgEc,MAME,CANFR,YAAA,CAMEuB,mBAAA;YAtEhBrB,UAAA,EAiEyBC,MAAA,CAAAiC,eAAe,CAACG,UAAU;YAjEnD,uBAAAlC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAiEyBH,MAAA,CAAAiC,eAAe,CAACG,UAAU,GAAAjC,MAAA;YACnCuB,IAAI,EAAC,UAAU;YACdW,IAAI,EAAE,CAAC;YACRf,WAAW,EAAC,UAAU;YACtB,eAAa,EAAb;;UArEhBE,CAAA;YAyEY3B,YAAA,CASeqB,uBAAA;UAlF3Bd,OAAA,EAAAC,QAAA,CA0Ec,MAOY,CAPZR,YAAA,CAOY+B,oBAAA;YANVF,IAAI,EAAC,SAAS;YACdlC,KAAK,EAAC,cAAc;YACnBqC,OAAO,EAAE7B,MAAA,CAAA6B,OAAO;YAChBC,OAAK,EAAE9B,MAAA,CAAAmC;;YA9ExB/B,OAAA,EAAAC,QAAA,CA+Ee,MAEDH,MAAA,QAAAA,MAAA,OAjFd6B,gBAAA,CA+Ee,MAED,E;YAjFdP,CAAA;YAAAQ,EAAA;;UAAAR,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;qCAuFM7B,mBAAA,CAKM,OALN2C,UAKM,GAJJ3C,mBAAA,CAGI,Y,0BA3FZoC,gBAAA,CAwFW,UAED,IAAAlC,YAAA,CAA8C0C,sBAAA;IAAjCC,EAAE,EAAC;EAAW;IA1FrCpC,OAAA,EAAAC,QAAA,CA0FsC,MAAIH,MAAA,QAAAA,MAAA,OA1F1C6B,gBAAA,CA0FsC,MAAI,E;IA1F1CP,CAAA;IAAAQ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}