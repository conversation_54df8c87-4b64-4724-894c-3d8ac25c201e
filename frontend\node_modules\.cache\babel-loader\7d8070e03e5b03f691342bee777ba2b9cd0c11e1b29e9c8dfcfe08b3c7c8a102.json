{"ast": null, "code": "import { dialogContentProps } from './dialog-content.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { teleportProps } from '../../teleport/src/teleport2.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isBoolean } from '../../../utils/types.mjs';\nconst dialogProps = buildProps({\n  ...dialogContentProps,\n  appendToBody: Boolean,\n  appendTo: {\n    type: teleportProps.to.type,\n    default: \"body\"\n  },\n  beforeClose: {\n    type: definePropType(Function)\n  },\n  destroyOnClose: Boolean,\n  closeOnClickModal: {\n    type: Boolean,\n    default: true\n  },\n  closeOnPressEscape: {\n    type: Boolean,\n    default: true\n  },\n  lockScroll: {\n    type: Boolean,\n    default: true\n  },\n  modal: {\n    type: Boolean,\n    default: true\n  },\n  openDelay: {\n    type: Number,\n    default: 0\n  },\n  closeDelay: {\n    type: Number,\n    default: 0\n  },\n  top: {\n    type: String\n  },\n  modelValue: Boolean,\n  modalClass: String,\n  headerClass: String,\n  bodyClass: String,\n  footerClass: String,\n  width: {\n    type: [String, Number]\n  },\n  zIndex: {\n    type: Number\n  },\n  trapFocus: Boolean,\n  headerAriaLevel: {\n    type: String,\n    default: \"2\"\n  }\n});\nconst dialogEmits = {\n  open: () => true,\n  opened: () => true,\n  close: () => true,\n  closed: () => true,\n  [UPDATE_MODEL_EVENT]: value => isBoolean(value),\n  openAutoFocus: () => true,\n  closeAutoFocus: () => true\n};\nexport { dialogEmits, dialogProps };", "map": {"version": 3, "names": ["dialogProps", "buildProps", "dialogContentProps", "appendToBody", "Boolean", "appendTo", "type", "teleportProps", "to", "default", "beforeClose", "definePropType", "Function", "destroyOnClose", "closeOnClickModal", "closeOnPressEscape", "lockScroll", "modal", "openDelay", "Number", "close<PERSON><PERSON><PERSON>", "top", "String", "modelValue", "modalClass", "headerClass", "bodyClass", "footerClass", "width", "zIndex", "trapFocus", "headerAriaLevel", "dialogEmits", "open", "opened", "close", "closed", "UPDATE_MODEL_EVENT", "value", "isBoolean", "openAutoFocus", "closeAutoFocus"], "sources": ["../../../../../../packages/components/dialog/src/dialog.ts"], "sourcesContent": ["import { buildProps, definePropType, isBoolean } from '@element-plus/utils'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { teleportProps } from '@element-plus/components/teleport'\nimport { dialogContentProps } from './dialog-content'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Dialog from './dialog.vue'\n\ntype DoneFn = (cancel?: boolean) => void\nexport type DialogBeforeCloseFn = (done: DoneFn) => void\n\nexport const dialogProps = buildProps({\n  ...dialogContentProps,\n  /**\n   * @description whether to append Dialog itself to body. A nested Dialog should have this attribute set to `true`\n   */\n  appendToBody: Boolean,\n  /**\n   * @description which element the Dialog appends to\n   */\n  appendTo: {\n    type: teleportProps.to.type,\n    default: 'body',\n  },\n  /**\n   * @description callback before Dialog closes, and it will prevent Dialog from closing, use done to close the dialog\n   */\n  beforeClose: {\n    type: definePropType<DialogBeforeCloseFn>(Function),\n  },\n  /**\n   * @description destroy elements in Dialog when closed\n   */\n  destroyOnClose: Boolean,\n  /**\n   * @description whether the Dialog can be closed by clicking the mask\n   */\n  closeOnClickModal: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether the Dialog can be closed by pressing ESC\n   */\n  closeOnPressEscape: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether scroll of body is disabled while Dialog is displayed\n   */\n  lockScroll: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether a mask is displayed\n   */\n  modal: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description the Time(milliseconds) before open\n   */\n  openDelay: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description the Time(milliseconds) before close\n   */\n  closeDelay: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description value for `margin-top` of Dialog CSS, default is 15vh\n   */\n  top: {\n    type: String,\n  },\n  /**\n   * @description visibility of Dialog\n   */\n  modelValue: Boolean,\n  /**\n   * @description custom class names for mask\n   */\n  modalClass: String,\n  /**\n   * @description custom class names for header wrapper\n   */\n  headerClass: String,\n  /**\n   * @description custom class names for body wrapper\n   */\n  bodyClass: String,\n  /**\n   * @description custom class names for footer wrapper\n   */\n  footerClass: String,\n  /**\n   * @description width of Dialog, default is 50%\n   */\n  width: {\n    type: [String, Number],\n  },\n  /**\n   * @description same as z-index in native CSS, z-order of dialog\n   */\n  zIndex: {\n    type: Number,\n  },\n  trapFocus: Boolean,\n  /**\n   * @description header's aria-level attribute\n   */\n  headerAriaLevel: {\n    type: String,\n    default: '2',\n  },\n} as const)\n\nexport type DialogProps = ExtractPropTypes<typeof dialogProps>\n\nexport const dialogEmits = {\n  open: () => true,\n  opened: () => true,\n  close: () => true,\n  closed: () => true,\n  [UPDATE_MODEL_EVENT]: (value: boolean) => isBoolean(value),\n  openAutoFocus: () => true,\n  closeAutoFocus: () => true,\n}\nexport type DialogEmits = typeof dialogEmits\nexport type DialogInstance = InstanceType<typeof Dialog> & unknown\n"], "mappings": ";;;;;AAIY,MAACA,WAAW,GAAGC,UAAU,CAAC;EACpC,GAAGC,kBAAkB;EACrBC,YAAY,EAAEC,OAAO;EACrBC,QAAQ,EAAE;IACRC,IAAI,EAAEC,aAAa,CAACC,EAAE,CAACF,IAAI;IAC3BG,OAAO,EAAE;EACb,CAAG;EACDC,WAAW,EAAE;IACXJ,IAAI,EAAEK,cAAc,CAACC,QAAQ;EACjC,CAAG;EACDC,cAAc,EAAET,OAAO;EACvBU,iBAAiB,EAAE;IACjBR,IAAI,EAAEF,OAAO;IACbK,OAAO,EAAE;EACb,CAAG;EACDM,kBAAkB,EAAE;IAClBT,IAAI,EAAEF,OAAO;IACbK,OAAO,EAAE;EACb,CAAG;EACDO,UAAU,EAAE;IACVV,IAAI,EAAEF,OAAO;IACbK,OAAO,EAAE;EACb,CAAG;EACDQ,KAAK,EAAE;IACLX,IAAI,EAAEF,OAAO;IACbK,OAAO,EAAE;EACb,CAAG;EACDS,SAAS,EAAE;IACTZ,IAAI,EAAEa,MAAM;IACZV,OAAO,EAAE;EACb,CAAG;EACDW,UAAU,EAAE;IACVd,IAAI,EAAEa,MAAM;IACZV,OAAO,EAAE;EACb,CAAG;EACDY,GAAG,EAAE;IACHf,IAAI,EAAEgB;EACV,CAAG;EACDC,UAAU,EAAEnB,OAAO;EACnBoB,UAAU,EAAEF,MAAM;EAClBG,WAAW,EAAEH,MAAM;EACnBI,SAAS,EAAEJ,MAAM;EACjBK,WAAW,EAAEL,MAAM;EACnBM,KAAK,EAAE;IACLtB,IAAI,EAAE,CAACgB,MAAM,EAAEH,MAAM;EACzB,CAAG;EACDU,MAAM,EAAE;IACNvB,IAAI,EAAEa;EACV,CAAG;EACDW,SAAS,EAAE1B,OAAO;EAClB2B,eAAe,EAAE;IACfzB,IAAI,EAAEgB,MAAM;IACZb,OAAO,EAAE;EACb;AACA,CAAC;AACW,MAACuB,WAAW,GAAG;EACzBC,IAAI,EAAEA,CAAA,KAAM,IAAI;EAChBC,MAAM,EAAEA,CAAA,KAAM,IAAI;EAClBC,KAAK,EAAEA,CAAA,KAAM,IAAI;EACjBC,MAAM,EAAEA,CAAA,KAAM,IAAI;EAClB,CAACC,kBAAkB,GAAIC,KAAK,IAAKC,SAAS,CAACD,KAAK,CAAC;EACjDE,aAAa,EAAEA,CAAA,KAAM,IAAI;EACzBC,cAAc,EAAEA,CAAA,KAAM;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}