{"ast": null, "code": "import arraySample from './_arraySample.js';\nimport values from './values.js';\n\n/**\n * The base implementation of `_.sample`.\n *\n * @private\n * @param {Array|Object} collection The collection to sample.\n * @returns {*} Returns the random element.\n */\nfunction baseSample(collection) {\n  return arraySample(values(collection));\n}\nexport default baseSample;", "map": {"version": 3, "names": ["arraySample", "values", "baseSample", "collection"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/_baseSample.js"], "sourcesContent": ["import arraySample from './_arraySample.js';\nimport values from './values.js';\n\n/**\n * The base implementation of `_.sample`.\n *\n * @private\n * @param {Array|Object} collection The collection to sample.\n * @returns {*} Returns the random element.\n */\nfunction baseSample(collection) {\n  return arraySample(values(collection));\n}\n\nexport default baseSample;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,aAAa;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,UAAU,EAAE;EAC9B,OAAOH,WAAW,CAACC,MAAM,CAACE,UAAU,CAAC,CAAC;AACxC;AAEA,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}