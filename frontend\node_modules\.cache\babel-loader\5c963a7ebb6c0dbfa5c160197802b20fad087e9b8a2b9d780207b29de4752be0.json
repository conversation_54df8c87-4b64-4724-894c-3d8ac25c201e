{"ast": null, "code": "import { defineComponent, getCurrentInstance, ref, computed, watch, onMounted, openBlock, createElementBlock, normalizeClass, createElementVNode, normalizeStyle } from 'vue';\nimport { draggable } from '../utils/draggable.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { getClientXY } from '../../../../utils/dom/position.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElColorHueSlider\",\n  props: {\n    color: {\n      type: Object,\n      required: true\n    },\n    vertical: Boolean\n  },\n  setup(props) {\n    const ns = useNamespace(\"color-hue-slider\");\n    const instance = getCurrentInstance();\n    const thumb = ref();\n    const bar = ref();\n    const thumbLeft = ref(0);\n    const thumbTop = ref(0);\n    const hueValue = computed(() => {\n      return props.color.get(\"hue\");\n    });\n    watch(() => hueValue.value, () => {\n      update();\n    });\n    function handleClick(event) {\n      const target = event.target;\n      if (target !== thumb.value) {\n        handleDrag(event);\n      }\n    }\n    function handleDrag(event) {\n      if (!bar.value || !thumb.value) return;\n      const el = instance.vnode.el;\n      const rect = el.getBoundingClientRect();\n      const {\n        clientX,\n        clientY\n      } = getClientXY(event);\n      let hue;\n      if (!props.vertical) {\n        let left = clientX - rect.left;\n        left = Math.min(left, rect.width - thumb.value.offsetWidth / 2);\n        left = Math.max(thumb.value.offsetWidth / 2, left);\n        hue = Math.round((left - thumb.value.offsetWidth / 2) / (rect.width - thumb.value.offsetWidth) * 360);\n      } else {\n        let top = clientY - rect.top;\n        top = Math.min(top, rect.height - thumb.value.offsetHeight / 2);\n        top = Math.max(thumb.value.offsetHeight / 2, top);\n        hue = Math.round((top - thumb.value.offsetHeight / 2) / (rect.height - thumb.value.offsetHeight) * 360);\n      }\n      props.color.set(\"hue\", hue);\n    }\n    function getThumbLeft() {\n      if (!thumb.value) return 0;\n      const el = instance.vnode.el;\n      if (props.vertical) return 0;\n      const hue = props.color.get(\"hue\");\n      if (!el) return 0;\n      return Math.round(hue * (el.offsetWidth - thumb.value.offsetWidth / 2) / 360);\n    }\n    function getThumbTop() {\n      if (!thumb.value) return 0;\n      const el = instance.vnode.el;\n      if (!props.vertical) return 0;\n      const hue = props.color.get(\"hue\");\n      if (!el) return 0;\n      return Math.round(hue * (el.offsetHeight - thumb.value.offsetHeight / 2) / 360);\n    }\n    function update() {\n      thumbLeft.value = getThumbLeft();\n      thumbTop.value = getThumbTop();\n    }\n    onMounted(() => {\n      if (!bar.value || !thumb.value) return;\n      const dragConfig = {\n        drag: event => {\n          handleDrag(event);\n        },\n        end: event => {\n          handleDrag(event);\n        }\n      };\n      draggable(bar.value, dragConfig);\n      draggable(thumb.value, dragConfig);\n      update();\n    });\n    return {\n      bar,\n      thumb,\n      thumbLeft,\n      thumbTop,\n      hueValue,\n      handleClick,\n      update,\n      ns\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass([_ctx.ns.b(), _ctx.ns.is(\"vertical\", _ctx.vertical)])\n  }, [createElementVNode(\"div\", {\n    ref: \"bar\",\n    class: normalizeClass(_ctx.ns.e(\"bar\")),\n    onClick: _ctx.handleClick\n  }, null, 10, [\"onClick\"]), createElementVNode(\"div\", {\n    ref: \"thumb\",\n    class: normalizeClass(_ctx.ns.e(\"thumb\")),\n    style: normalizeStyle({\n      left: _ctx.thumbLeft + \"px\",\n      top: _ctx.thumbTop + \"px\"\n    })\n  }, null, 6)], 2);\n}\nvar HueSlider = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"hue-slider.vue\"]]);\nexport { HueSlider as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "name", "props", "color", "type", "Object", "required", "vertical", "Boolean", "setup", "ns", "useNamespace", "instance", "getCurrentInstance", "thumb", "ref", "bar", "thumbLeft", "thumbTop", "hueValue", "computed", "get", "watch", "value", "update", "handleClick", "event", "target", "handleDrag", "el", "vnode", "rect", "getBoundingClientRect", "clientX", "clientY", "getClientXY", "hue", "left", "Math", "min", "width", "offsetWidth", "max", "round", "top", "height", "offsetHeight", "set", "getThumbLeft", "getThumbTop", "onMounted", "dragConfig", "drag", "end", "draggable", "_sfc_render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "openBlock", "createElementBlock", "createElementVNode", "class", "normalizeClass", "e", "onClick", "style", "normalizeStyle", "HueSlider", "_export_sfc"], "sources": ["../../../../../../../packages/components/color-picker/src/components/hue-slider.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.b(), ns.is('vertical', vertical)]\">\n    <div ref=\"bar\" :class=\"ns.e('bar')\" @click=\"handleClick\" />\n    <div\n      ref=\"thumb\"\n      :class=\"ns.e('thumb')\"\n      :style=\"{\n        left: thumbLeft + 'px',\n        top: thumbTop + 'px',\n      }\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  onMounted,\n  ref,\n  watch,\n} from 'vue'\nimport { getClientXY } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { draggable } from '../utils/draggable'\n\nimport type { PropType } from 'vue'\nimport type Color from '../utils/color'\n\nexport default defineComponent({\n  name: 'ElColorHueSlider',\n\n  props: {\n    color: {\n      type: Object as PropType<Color>,\n      required: true,\n    },\n\n    vertical: <PERSON><PERSON><PERSON>,\n  },\n  setup(props) {\n    const ns = useNamespace('color-hue-slider')\n    const instance = getCurrentInstance()!\n    // ref\n    const thumb = ref<HTMLElement>()\n    const bar = ref<HTMLElement>()\n    // data\n    const thumbLeft = ref(0)\n    const thumbTop = ref(0)\n    // computed\n    const hueValue = computed(() => {\n      return props.color.get('hue')\n    })\n    // watch\n    watch(\n      () => hueValue.value,\n      () => {\n        update()\n      }\n    )\n\n    // methods\n    function handleClick(event: MouseEvent | TouchEvent) {\n      const target = event.target\n\n      if (target !== thumb.value) {\n        handleDrag(event)\n      }\n    }\n\n    function handleDrag(event: MouseEvent | TouchEvent) {\n      if (!bar.value || !thumb.value) return\n\n      const el = instance.vnode.el as HTMLElement\n      const rect = el.getBoundingClientRect()\n      const { clientX, clientY } = getClientXY(event)\n      let hue\n\n      if (!props.vertical) {\n        let left = clientX - rect.left\n        left = Math.min(left, rect.width - thumb.value.offsetWidth / 2)\n        left = Math.max(thumb.value.offsetWidth / 2, left)\n\n        hue = Math.round(\n          ((left - thumb.value.offsetWidth / 2) /\n            (rect.width - thumb.value.offsetWidth)) *\n            360\n        )\n      } else {\n        let top = clientY - rect.top\n\n        top = Math.min(top, rect.height - thumb.value.offsetHeight / 2)\n        top = Math.max(thumb.value.offsetHeight / 2, top)\n        hue = Math.round(\n          ((top - thumb.value.offsetHeight / 2) /\n            (rect.height - thumb.value.offsetHeight)) *\n            360\n        )\n      }\n      props.color.set('hue', hue)\n    }\n\n    function getThumbLeft() {\n      if (!thumb.value) return 0\n\n      const el = instance.vnode.el\n\n      if (props.vertical) return 0\n      const hue = props.color.get('hue')\n\n      if (!el) return 0\n      return Math.round(\n        (hue * (el.offsetWidth - thumb.value.offsetWidth / 2)) / 360\n      )\n    }\n\n    function getThumbTop() {\n      if (!thumb.value) return 0\n\n      const el = instance.vnode.el as HTMLElement\n      if (!props.vertical) return 0\n      const hue = props.color.get('hue')\n\n      if (!el) return 0\n      return Math.round(\n        (hue * (el.offsetHeight - thumb.value.offsetHeight / 2)) / 360\n      )\n    }\n\n    function update() {\n      thumbLeft.value = getThumbLeft()\n      thumbTop.value = getThumbTop()\n    }\n\n    // mounded\n    onMounted(() => {\n      if (!bar.value || !thumb.value) return\n\n      const dragConfig = {\n        drag: (event: MouseEvent | TouchEvent) => {\n          handleDrag(event)\n        },\n        end: (event: MouseEvent | TouchEvent) => {\n          handleDrag(event)\n        },\n      }\n\n      draggable(bar.value, dragConfig)\n      draggable(thumb.value, dragConfig)\n      update()\n    })\n\n    return {\n      bar,\n      thumb,\n      thumbLeft,\n      thumbTop,\n      hueValue,\n      handleClick,\n      update,\n      ns,\n    }\n  },\n})\n</script>\n"], "mappings": ";;;;;AA8BA,MAAKA,SAAA,GAAaC,eAAa;EAC7BC,IAAM;EAENC,KAAO;IACLC,KAAO;MACLC,IAAM,EAAAC,MAAA;MACNC,QAAU;IAAA,CACZ;IAEAC,QAAU,EAAAC;EAAA,CACZ;EACAC,MAAMP,KAAO;IACL,MAAAQ,EAAA,GAAKC,YAAA,CAAa,kBAAkB;IAC1C,MAAMC,QAAA,GAAWC,kBAAmB;IAEpC,MAAMC,KAAA,GAAQC,GAAiB;IAC/B,MAAMC,GAAA,GAAMD,GAAiB;IAEvB,MAAAE,SAAA,GAAYF,GAAA,CAAI,CAAC;IACjB,MAAAG,QAAA,GAAWH,GAAA,CAAI,CAAC;IAEhB,MAAAI,QAAA,GAAWC,QAAA,CAAS,MAAM;MACvB,OAAAlB,KAAA,CAAMC,KAAM,CAAAkB,GAAA,CAAI,KAAK;IAAA,CAC7B;IAEDC,KAAA,OAAAH,QAAA,CAAAI,KAAA;MACEC,MAAM,EAAS;IAAA,EACf;IACS,SAAAC,YAAAC,KAAA;MACT,MAAAC,MAAA,GAAAD,KAAA,CAAAC,MAAA;MACF,IAAAA,MAAA,KAAAb,KAAA,CAAAS,KAAA;QAGAK,UAAA,CAAAF,KAAA,CAAqB;MACnB;IAEA;IACE,SAAAE,UAAgBA,CAAAF,KAAA;MAClB,KAAAV,GAAA,CAAAO,KAAA,KAAAT,KAAA,CAAAS,KAAA,EACF;MAEA,MAAAM,EAAA,GAAAjB,QAAoD,CAAAkB,KAAA,CAAAD,EAAA;MAClD,MAAKE,IAAa,GAAAF,EAAA,CAAAG,qBAAc;MAE1B;QAAAC,OAAA;QAAAC;MAAoB,IAAAC,WAAA,CAAAT,KAAA;MACpB,IAAAU,GAAA;MACN,KAAAlC,KAAQ,CAAAK,QAAiB;QACrB,IAAA8B,IAAA,GAAAJ,OAAA,GAAAF,IAAA,CAAAM,IAAA;QAEAA,IAAA,GAAAC,IAAiB,CAAAC,GAAA,CAAAF,IAAA,EAAAN,IAAA,CAAAS,KAAA,GAAA1B,KAAA,CAAAS,KAAA,CAAAkB,WAAA;QACfJ,IAAA,GAAAC,IAAO,CAAAI,GAAA,CAAA5B,KAAU,CAAKS,KAAA,CAAAkB,WAAA,MAAAJ,IAAA;QACnBD,GAAA,GAAAE,IAAA,CAAAK,KAAS,CAAM,CAAAN,IAAA,GAAAvB,KAAA,CAAAS,KAAmB,CAAAkB,WAAA,SAAAV,IAAoB,CAACS,KAAA,GAAA1B,KAAA,CAAAS,KAAA,CAAAkB,WAAA;MAC9D;QAEA,IAAAG,GAAW,GAAAV,OAAA,GAAAH,IAAA,CAAAa,GAAA;QACPA,GAAA,GAAAN,IAAO,CAAAC,GAAA,CAAAK,GAAY,EAAAb,IAAA,CAAAc,MAAA,GAAA/B,KAAA,CAAAS,KACb,CAAAuB,YAAc;QAExBF,GAAA,GAAAN,IAAA,CAAAI,GAAA,CAAA5B,KAAA,CAAAS,KAAA,CAAAuB,YAAA,MAAAF,GAAA;QACKR,GAAA,GAAAE,IAAA,CAAAK,KAAA,EAAAC,GAAA,GAAA9B,KAAA,CAAAS,KAAA,CAAAuB,YAAA,SAAAf,IAAA,CAAAc,MAAA,GAAA/B,KAAA,CAAAS,KAAA,CAAAuB,YAAA;MACL;MAEM5C,KAAA,CAAAC,KAAA,CAAA4C,GAAA,MAAc,EAAAX,GAAA,CAAK;IACzB;IACA,SAAAY,YAAWA,CAAA;MACP,KAAAlC,KAAA,CAAMS,KAAA,EAGV;MACF,MAAAM,EAAA,GAAAjB,QAAA,CAAAkB,KAAA,CAAAD,EAAA;MACM,IAAA3B,KAAA,CAAAK,QAAU,EAClB;MAEA,MAAA6B,GAAwB,GAAAlC,KAAA,CAAAC,KAAA,CAAAkB,GAAA;MAClB,KAACQ,EAAM,EAEL;MAEF,OAAAS,IAAA,CAAAK,KAAA,CAAAP,GAAuB,IAAAP,EAAA,CAAAY,WAAA,GAAA3B,KAAA,CAAAS,KAAA,CAAAkB,WAAA;IAC3B;IAEI,SAAAQ,WAAYA,CAAA;MAChB,KAAAnC,KAAY,CAAAS,KAAA,EACT,OAAO,CAAG;MACb,MAAAM,EAAA,GAAAjB,QAAA,CAAAkB,KAAA,CAAAD,EAAA;MACF,KAAA3B,KAAA,CAAAK,QAAA,EAEA,OAAuB;MACjB,MAAO6B,GAAA,GAAAlC,KAAO,CAAOC,KAAA,CAAAkB,GAAA;MAEnB,KAAAQ,EAAA,EACF,OAAO;MACX,OAAYS,IAAA,CAAAK,KAAA,CAAMP,GAAM,IAAAP,EAAA,CAAIiB,YAAK,GAAAhC,KAAA,CAAAS,KAAA,CAAAuB,YAAA;IAEjC;IACA,SAAOtB,MAAKA,CAAA;MAAAP,SACF,CAAGM,KAAA,GAAAyB,YAAqB;MAClC9B,QAAA,CAAAK,KAAA,GAAA0B,WAAA;IAAA;IAGFC,SAAS,CAAS;MAChB,KAAAlC,GAAA,CAAAO,KAAA,IAAkB,CAAaT,KAAA,CAAAS,KAAA,EAC/B;MACF,MAAA4B,UAAA;QAGAC,IAAA,EAAgB1B,KAAA;UACVE,UAAc,CAAAF,KAAA;QAElB;QACE2B,GAAA,EAAM3B,KAAoC;UACxCE,UAAA,CAAWF,KAAK;QAAA;MAClB,CACA;MACE4B,SAAA,CAAAtC,GAAA,CAAAO,KAAgB,EAAA4B,UAAA;MAClBG,SAAA,CAAAxC,KAAA,CAAAS,KAAA,EAAA4B,UAAA;MACF3B,MAAA;IAEA,CAAU;IACA;MACHR,GAAA;MACRF,KAAA;MAEMG,SAAA;MACLC,QAAA;MACAC,QAAA;MACAM,WAAA;MACAD,MAAA;MACAd;IAAA,CACA;EAAA;AACA,CACA;AACF,SACF6C,YAAAC,IAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,QAAA;EACD,OAAAC,SAAA,IAAAC,kBAAA;;MAnKCC,kBAAA;IAUMjD,GAAA;IAAAkD,KAAA,EAAAC,cAAA,CAAAV,IAAA,CAAA9C,EAAA,CAAAyD,CAAA;IAVAC,OAAK,EAAAZ,IAAA,CAAA/B;EAAqC,2B;IAC9CV,GAA2D;IAAAkD,KAAlD,EAAAC,cAAA,CAAAV,IAAA,CAAA9C,EAAA,CAAAyD,CAAA;IAAOE,KAAA,EAAAC,cAAO;MAAcjC,IAAO,EAAAmB,IAAA,CAAAvC,SAAA;MAAA2B,GAAA,EAAAY,IAAA,CAAAtC,QAAA;IAC5C;EAAA,CAOE;AAAA;AALY,IAAAqD,SAAA,GACN,eAAAC,WAAA,CAAAzE,SAAA,cAAAwD,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}