{"ast": null, "code": "import { getCurrentInstance, ref, computed, watch, nextTick, onMounted } from 'vue';\nimport { useTimeoutFn, isClient } from '@vueuse/core';\nimport { isUndefined } from 'lodash-unified';\nimport { useLockscreen } from '../../../hooks/use-lockscreen/index.mjs';\nimport { useZIndex } from '../../../hooks/use-z-index/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { useGlobalConfig } from '../../config-provider/src/hooks/use-global-config.mjs';\nimport { defaultNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nconst useDialog = (props, targetRef) => {\n  var _a;\n  const instance = getCurrentInstance();\n  const emit = instance.emit;\n  const {\n    nextZIndex\n  } = useZIndex();\n  let lastPosition = \"\";\n  const titleId = useId();\n  const bodyId = useId();\n  const visible = ref(false);\n  const closed = ref(false);\n  const rendered = ref(false);\n  const zIndex = ref((_a = props.zIndex) != null ? _a : nextZIndex());\n  let openTimer = void 0;\n  let closeTimer = void 0;\n  const namespace = useGlobalConfig(\"namespace\", defaultNamespace);\n  const style = computed(() => {\n    const style2 = {};\n    const varPrefix = `--${namespace.value}-dialog`;\n    if (!props.fullscreen) {\n      if (props.top) {\n        style2[`${varPrefix}-margin-top`] = props.top;\n      }\n      if (props.width) {\n        style2[`${varPrefix}-width`] = addUnit(props.width);\n      }\n    }\n    return style2;\n  });\n  const overlayDialogStyle = computed(() => {\n    if (props.alignCenter) {\n      return {\n        display: \"flex\"\n      };\n    }\n    return {};\n  });\n  function afterEnter() {\n    emit(\"opened\");\n  }\n  function afterLeave() {\n    emit(\"closed\");\n    emit(UPDATE_MODEL_EVENT, false);\n    if (props.destroyOnClose) {\n      rendered.value = false;\n    }\n  }\n  function beforeLeave() {\n    emit(\"close\");\n  }\n  function open() {\n    closeTimer == null ? void 0 : closeTimer();\n    openTimer == null ? void 0 : openTimer();\n    if (props.openDelay && props.openDelay > 0) {\n      ({\n        stop: openTimer\n      } = useTimeoutFn(() => doOpen(), props.openDelay));\n    } else {\n      doOpen();\n    }\n  }\n  function close() {\n    openTimer == null ? void 0 : openTimer();\n    closeTimer == null ? void 0 : closeTimer();\n    if (props.closeDelay && props.closeDelay > 0) {\n      ({\n        stop: closeTimer\n      } = useTimeoutFn(() => doClose(), props.closeDelay));\n    } else {\n      doClose();\n    }\n  }\n  function handleClose() {\n    function hide(shouldCancel) {\n      if (shouldCancel) return;\n      closed.value = true;\n      visible.value = false;\n    }\n    if (props.beforeClose) {\n      props.beforeClose(hide);\n    } else {\n      close();\n    }\n  }\n  function onModalClick() {\n    if (props.closeOnClickModal) {\n      handleClose();\n    }\n  }\n  function doOpen() {\n    if (!isClient) return;\n    visible.value = true;\n  }\n  function doClose() {\n    visible.value = false;\n  }\n  function onOpenAutoFocus() {\n    emit(\"openAutoFocus\");\n  }\n  function onCloseAutoFocus() {\n    emit(\"closeAutoFocus\");\n  }\n  function onFocusoutPrevented(event) {\n    var _a2;\n    if (((_a2 = event.detail) == null ? void 0 : _a2.focusReason) === \"pointer\") {\n      event.preventDefault();\n    }\n  }\n  if (props.lockScroll) {\n    useLockscreen(visible);\n  }\n  function onCloseRequested() {\n    if (props.closeOnPressEscape) {\n      handleClose();\n    }\n  }\n  watch(() => props.modelValue, val => {\n    if (val) {\n      closed.value = false;\n      open();\n      rendered.value = true;\n      zIndex.value = isUndefined(props.zIndex) ? nextZIndex() : zIndex.value++;\n      nextTick(() => {\n        emit(\"open\");\n        if (targetRef.value) {\n          targetRef.value.parentElement.scrollTop = 0;\n          targetRef.value.parentElement.scrollLeft = 0;\n          targetRef.value.scrollTop = 0;\n        }\n      });\n    } else {\n      if (visible.value) {\n        close();\n      }\n    }\n  });\n  watch(() => props.fullscreen, val => {\n    if (!targetRef.value) return;\n    if (val) {\n      lastPosition = targetRef.value.style.transform;\n      targetRef.value.style.transform = \"\";\n    } else {\n      targetRef.value.style.transform = lastPosition;\n    }\n  });\n  onMounted(() => {\n    if (props.modelValue) {\n      visible.value = true;\n      rendered.value = true;\n      open();\n    }\n  });\n  return {\n    afterEnter,\n    afterLeave,\n    beforeLeave,\n    handleClose,\n    onModalClick,\n    close,\n    doClose,\n    onOpenAutoFocus,\n    onCloseAutoFocus,\n    onCloseRequested,\n    onFocusoutPrevented,\n    titleId,\n    bodyId,\n    closed,\n    style,\n    overlayDialogStyle,\n    rendered,\n    visible,\n    zIndex\n  };\n};\nexport { useDialog };", "map": {"version": 3, "names": ["useDialog", "props", "targetRef", "_a", "instance", "getCurrentInstance", "emit", "nextZIndex", "useZIndex", "lastPosition", "titleId", "useId", "bodyId", "visible", "ref", "closed", "rendered", "zIndex", "openTimer", "closeTimer", "namespace", "useGlobalConfig", "defaultNamespace", "style", "computed", "style2", "varPrefix", "value", "fullscreen", "top", "width", "addUnit", "overlayDialogStyle", "alignCenter", "display", "afterEnter", "afterLeave", "UPDATE_MODEL_EVENT", "destroyOnClose", "beforeLeave", "open", "openDelay", "stop", "useTimeoutFn", "doOpen", "close", "close<PERSON><PERSON><PERSON>", "doClose", "handleClose", "hide", "shouldCancel", "beforeClose", "onModalClick", "closeOnClickModal", "isClient", "onOpenAutoFocus", "onCloseAutoFocus", "onFocusoutPrevented", "event", "_a2", "detail", "focusReason", "preventDefault", "lockScroll", "useLockscreen", "onCloseRequested", "closeOnPressEscape", "watch", "modelValue", "val", "isUndefined", "nextTick", "parentElement", "scrollTop", "scrollLeft", "transform", "onMounted"], "sources": ["../../../../../../packages/components/dialog/src/use-dialog.ts"], "sourcesContent": ["import {\n  computed,\n  getCurrentInstance,\n  nextTick,\n  onMounted,\n  ref,\n  watch,\n} from 'vue'\nimport { useTimeoutFn } from '@vueuse/core'\n\nimport { isUndefined } from 'lodash-unified'\nimport {\n  defaultNamespace,\n  useId,\n  useLockscreen,\n  useZIndex,\n} from '@element-plus/hooks'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { addUnit, isClient } from '@element-plus/utils'\nimport { useGlobalConfig } from '@element-plus/components/config-provider'\n\nimport type { CSSProperties, Ref, SetupContext } from 'vue'\nimport type { DialogEmits, DialogProps } from './dialog'\n\nexport const useDialog = (\n  props: DialogProps,\n  targetRef: Ref<HTMLElement | undefined>\n) => {\n  const instance = getCurrentInstance()!\n  const emit = instance.emit as SetupContext<DialogEmits>['emit']\n  const { nextZIndex } = useZIndex()\n\n  let lastPosition = ''\n  const titleId = useId()\n  const bodyId = useId()\n  const visible = ref(false)\n  const closed = ref(false)\n  const rendered = ref(false) // when desctroyOnClose is true, we initialize it as false vise versa\n  const zIndex = ref(props.zIndex ?? nextZIndex())\n\n  let openTimer: (() => void) | undefined = undefined\n  let closeTimer: (() => void) | undefined = undefined\n\n  const namespace = useGlobalConfig('namespace', defaultNamespace)\n\n  const style = computed<CSSProperties>(() => {\n    const style: CSSProperties = {}\n    const varPrefix = `--${namespace.value}-dialog` as const\n    if (!props.fullscreen) {\n      if (props.top) {\n        style[`${varPrefix}-margin-top`] = props.top\n      }\n      if (props.width) {\n        style[`${varPrefix}-width`] = addUnit(props.width)\n      }\n    }\n    return style\n  })\n\n  const overlayDialogStyle = computed<CSSProperties>(() => {\n    if (props.alignCenter) {\n      return { display: 'flex' }\n    }\n    return {}\n  })\n\n  function afterEnter() {\n    emit('opened')\n  }\n\n  function afterLeave() {\n    emit('closed')\n    emit(UPDATE_MODEL_EVENT, false)\n    if (props.destroyOnClose) {\n      rendered.value = false\n    }\n  }\n\n  function beforeLeave() {\n    emit('close')\n  }\n\n  function open() {\n    closeTimer?.()\n    openTimer?.()\n\n    if (props.openDelay && props.openDelay > 0) {\n      ;({ stop: openTimer } = useTimeoutFn(() => doOpen(), props.openDelay))\n    } else {\n      doOpen()\n    }\n  }\n\n  function close() {\n    openTimer?.()\n    closeTimer?.()\n\n    if (props.closeDelay && props.closeDelay > 0) {\n      ;({ stop: closeTimer } = useTimeoutFn(() => doClose(), props.closeDelay))\n    } else {\n      doClose()\n    }\n  }\n\n  function handleClose() {\n    function hide(shouldCancel?: boolean) {\n      if (shouldCancel) return\n      closed.value = true\n      visible.value = false\n    }\n\n    if (props.beforeClose) {\n      props.beforeClose(hide)\n    } else {\n      close()\n    }\n  }\n\n  function onModalClick() {\n    if (props.closeOnClickModal) {\n      handleClose()\n    }\n  }\n\n  function doOpen() {\n    if (!isClient) return\n    visible.value = true\n  }\n\n  function doClose() {\n    visible.value = false\n  }\n\n  function onOpenAutoFocus() {\n    emit('openAutoFocus')\n  }\n\n  function onCloseAutoFocus() {\n    emit('closeAutoFocus')\n  }\n\n  function onFocusoutPrevented(event: CustomEvent) {\n    if (event.detail?.focusReason === 'pointer') {\n      event.preventDefault()\n    }\n  }\n\n  if (props.lockScroll) {\n    useLockscreen(visible)\n  }\n\n  function onCloseRequested() {\n    if (props.closeOnPressEscape) {\n      handleClose()\n    }\n  }\n\n  watch(\n    () => props.modelValue,\n    (val) => {\n      if (val) {\n        closed.value = false\n        open()\n        rendered.value = true // enables lazy rendering\n        zIndex.value = isUndefined(props.zIndex) ? nextZIndex() : zIndex.value++\n        // this.$el.addEventListener('scroll', this.updatePopper)\n        nextTick(() => {\n          emit('open')\n          if (targetRef.value) {\n            targetRef.value.parentElement!.scrollTop = 0\n            targetRef.value.parentElement!.scrollLeft = 0\n            targetRef.value.scrollTop = 0\n          }\n        })\n      } else {\n        // this.$el.removeEventListener('scroll', this.updatePopper\n        if (visible.value) {\n          close()\n        }\n      }\n    }\n  )\n\n  watch(\n    () => props.fullscreen,\n    (val) => {\n      if (!targetRef.value) return\n      if (val) {\n        lastPosition = targetRef.value.style.transform\n        targetRef.value.style.transform = ''\n      } else {\n        targetRef.value.style.transform = lastPosition\n      }\n    }\n  )\n\n  onMounted(() => {\n    if (props.modelValue) {\n      visible.value = true\n      rendered.value = true // enables lazy rendering\n      open()\n    }\n  })\n\n  return {\n    afterEnter,\n    afterLeave,\n    beforeLeave,\n    handleClose,\n    onModalClick,\n    close,\n    doClose,\n    onOpenAutoFocus,\n    onCloseAutoFocus,\n    onCloseRequested,\n    onFocusoutPrevented,\n    titleId,\n    bodyId,\n    closed,\n    style,\n    overlayDialogStyle,\n    rendered,\n    visible,\n    zIndex,\n  }\n}\n"], "mappings": ";;;;;;;;;;AAmBY,MAACA,SAAS,GAAGA,CAACC,KAAK,EAAEC,SAAS,KAAK;EAC7C,IAAIC,EAAE;EACN,MAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,MAAMC,IAAI,GAAGF,QAAQ,CAACE,IAAI;EAC1B,MAAM;IAAEC;EAAU,CAAE,GAAGC,SAAS,EAAE;EAClC,IAAIC,YAAY,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGC,KAAK,EAAE;EACvB,MAAMC,MAAM,GAAGD,KAAK,EAAE;EACtB,MAAME,OAAO,GAAGC,GAAG,CAAC,KAAK,CAAC;EAC1B,MAAMC,MAAM,GAAGD,GAAG,CAAC,KAAK,CAAC;EACzB,MAAME,QAAQ,GAAGF,GAAG,CAAC,KAAK,CAAC;EAC3B,MAAMG,MAAM,GAAGH,GAAG,CAAC,CAACX,EAAE,GAAGF,KAAK,CAACgB,MAAM,KAAK,IAAI,GAAGd,EAAE,GAAGI,UAAU,EAAE,CAAC;EACnE,IAAIW,SAAS,GAAG,KAAK,CAAC;EACtB,IAAIC,UAAU,GAAG,KAAK,CAAC;EACvB,MAAMC,SAAS,GAAGC,eAAe,CAAC,WAAW,EAAEC,gBAAgB,CAAC;EAChE,MAAMC,KAAK,GAAGC,QAAQ,CAAC,MAAM;IAC3B,MAAMC,MAAM,GAAG,EAAE;IACjB,MAAMC,SAAS,GAAG,KAAKN,SAAS,CAACO,KAAK,SAAS;IAC/C,IAAI,CAAC1B,KAAK,CAAC2B,UAAU,EAAE;MACrB,IAAI3B,KAAK,CAAC4B,GAAG,EAAE;QACbJ,MAAM,CAAC,GAAGC,SAAS,aAAa,CAAC,GAAGzB,KAAK,CAAC4B,GAAG;MACrD;MACM,IAAI5B,KAAK,CAAC6B,KAAK,EAAE;QACfL,MAAM,CAAC,GAAGC,SAAS,QAAQ,CAAC,GAAGK,OAAO,CAAC9B,KAAK,CAAC6B,KAAK,CAAC;MAC3D;IACA;IACI,OAAOL,MAAM;EACjB,CAAG,CAAC;EACF,MAAMO,kBAAkB,GAAGR,QAAQ,CAAC,MAAM;IACxC,IAAIvB,KAAK,CAACgC,WAAW,EAAE;MACrB,OAAO;QAAEC,OAAO,EAAE;MAAM,CAAE;IAChC;IACI,OAAO,EAAE;EACb,CAAG,CAAC;EACF,SAASC,UAAUA,CAAA,EAAG;IACpB7B,IAAI,CAAC,QAAQ,CAAC;EAClB;EACE,SAAS8B,UAAUA,CAAA,EAAG;IACpB9B,IAAI,CAAC,QAAQ,CAAC;IACdA,IAAI,CAAC+B,kBAAkB,EAAE,KAAK,CAAC;IAC/B,IAAIpC,KAAK,CAACqC,cAAc,EAAE;MACxBtB,QAAQ,CAACW,KAAK,GAAG,KAAK;IAC5B;EACA;EACE,SAASY,WAAWA,CAAA,EAAG;IACrBjC,IAAI,CAAC,OAAO,CAAC;EACjB;EACE,SAASkC,IAAIA,CAAA,EAAG;IACdrB,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,EAAE;IAC1CD,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,EAAE;IACxC,IAAIjB,KAAK,CAACwC,SAAS,IAAIxC,KAAK,CAACwC,SAAS,GAAG,CAAC,EAAE;MAE1C,CAAC;QAAEC,IAAI,EAAExB;MAAS,CAAE,GAAGyB,YAAY,CAAC,MAAMC,MAAM,EAAE,EAAE3C,KAAK,CAACwC,SAAS,CAAC;IAC1E,CAAK,MAAM;MACLG,MAAM,EAAE;IACd;EACA;EACE,SAASC,KAAKA,CAAA,EAAG;IACf3B,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,EAAE;IACxCC,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,EAAE;IAC1C,IAAIlB,KAAK,CAAC6C,UAAU,IAAI7C,KAAK,CAAC6C,UAAU,GAAG,CAAC,EAAE;MAE5C,CAAC;QAAEJ,IAAI,EAAEvB;MAAU,CAAE,GAAGwB,YAAY,CAAC,MAAMI,OAAO,EAAE,EAAE9C,KAAK,CAAC6C,UAAU,CAAC;IAC7E,CAAK,MAAM;MACLC,OAAO,EAAE;IACf;EACA;EACE,SAASC,WAAWA,CAAA,EAAG;IACrB,SAASC,IAAIA,CAACC,YAAY,EAAE;MAC1B,IAAIA,YAAY,EACd;MACFnC,MAAM,CAACY,KAAK,GAAG,IAAI;MACnBd,OAAO,CAACc,KAAK,GAAG,KAAK;IAC3B;IACI,IAAI1B,KAAK,CAACkD,WAAW,EAAE;MACrBlD,KAAK,CAACkD,WAAW,CAACF,IAAI,CAAC;IAC7B,CAAK,MAAM;MACLJ,KAAK,EAAE;IACb;EACA;EACE,SAASO,YAAYA,CAAA,EAAG;IACtB,IAAInD,KAAK,CAACoD,iBAAiB,EAAE;MAC3BL,WAAW,EAAE;IACnB;EACA;EACE,SAASJ,MAAMA,CAAA,EAAG;IAChB,IAAI,CAACU,QAAQ,EACX;IACFzC,OAAO,CAACc,KAAK,GAAG,IAAI;EACxB;EACE,SAASoB,OAAOA,CAAA,EAAG;IACjBlC,OAAO,CAACc,KAAK,GAAG,KAAK;EACzB;EACE,SAAS4B,eAAeA,CAAA,EAAG;IACzBjD,IAAI,CAAC,eAAe,CAAC;EACzB;EACE,SAASkD,gBAAgBA,CAAA,EAAG;IAC1BlD,IAAI,CAAC,gBAAgB,CAAC;EAC1B;EACE,SAASmD,mBAAmBA,CAACC,KAAK,EAAE;IAClC,IAAIC,GAAG;IACP,IAAI,CAAC,CAACA,GAAG,GAAGD,KAAK,CAACE,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,GAAG,CAACE,WAAW,MAAM,SAAS,EAAE;MAC3EH,KAAK,CAACI,cAAc,EAAE;IAC5B;EACA;EACE,IAAI7D,KAAK,CAAC8D,UAAU,EAAE;IACpBC,aAAa,CAACnD,OAAO,CAAC;EAC1B;EACE,SAASoD,gBAAgBA,CAAA,EAAG;IAC1B,IAAIhE,KAAK,CAACiE,kBAAkB,EAAE;MAC5BlB,WAAW,EAAE;IACnB;EACA;EACEmB,KAAK,CAAC,MAAMlE,KAAK,CAACmE,UAAU,EAAGC,GAAG,IAAK;IACrC,IAAIA,GAAG,EAAE;MACPtD,MAAM,CAACY,KAAK,GAAG,KAAK;MACpBa,IAAI,EAAE;MACNxB,QAAQ,CAACW,KAAK,GAAG,IAAI;MACrBV,MAAM,CAACU,KAAK,GAAG2C,WAAW,CAACrE,KAAK,CAACgB,MAAM,CAAC,GAAGV,UAAU,EAAE,GAAGU,MAAM,CAACU,KAAK,EAAE;MACxE4C,QAAQ,CAAC,MAAM;QACbjE,IAAI,CAAC,MAAM,CAAC;QACZ,IAAIJ,SAAS,CAACyB,KAAK,EAAE;UACnBzB,SAAS,CAACyB,KAAK,CAAC6C,aAAa,CAACC,SAAS,GAAG,CAAC;UAC3CvE,SAAS,CAACyB,KAAK,CAAC6C,aAAa,CAACE,UAAU,GAAG,CAAC;UAC5CxE,SAAS,CAACyB,KAAK,CAAC8C,SAAS,GAAG,CAAC;QACvC;MACA,CAAO,CAAC;IACR,CAAK,MAAM;MACL,IAAI5D,OAAO,CAACc,KAAK,EAAE;QACjBkB,KAAK,EAAE;MACf;IACA;EACA,CAAG,CAAC;EACFsB,KAAK,CAAC,MAAMlE,KAAK,CAAC2B,UAAU,EAAGyC,GAAG,IAAK;IACrC,IAAI,CAACnE,SAAS,CAACyB,KAAK,EAClB;IACF,IAAI0C,GAAG,EAAE;MACP5D,YAAY,GAAGP,SAAS,CAACyB,KAAK,CAACJ,KAAK,CAACoD,SAAS;MAC9CzE,SAAS,CAACyB,KAAK,CAACJ,KAAK,CAACoD,SAAS,GAAG,EAAE;IAC1C,CAAK,MAAM;MACLzE,SAAS,CAACyB,KAAK,CAACJ,KAAK,CAACoD,SAAS,GAAGlE,YAAY;IACpD;EACA,CAAG,CAAC;EACFmE,SAAS,CAAC,MAAM;IACd,IAAI3E,KAAK,CAACmE,UAAU,EAAE;MACpBvD,OAAO,CAACc,KAAK,GAAG,IAAI;MACpBX,QAAQ,CAACW,KAAK,GAAG,IAAI;MACrBa,IAAI,EAAE;IACZ;EACA,CAAG,CAAC;EACF,OAAO;IACLL,UAAU;IACVC,UAAU;IACVG,WAAW;IACXS,WAAW;IACXI,YAAY;IACZP,KAAK;IACLE,OAAO;IACPQ,eAAe;IACfC,gBAAgB;IAChBS,gBAAgB;IAChBR,mBAAmB;IACnB/C,OAAO;IACPE,MAAM;IACNG,MAAM;IACNQ,KAAK;IACLS,kBAAkB;IAClBhB,QAAQ;IACRH,OAAO;IACPI;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}