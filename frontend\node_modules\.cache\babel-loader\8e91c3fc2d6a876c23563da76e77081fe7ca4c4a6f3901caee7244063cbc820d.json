{"ast": null, "code": "const radioGroupKey = Symbol(\"radioGroupKey\");\nexport { radioGroupKey };", "map": {"version": 3, "names": ["radioGroupKey", "Symbol"], "sources": ["../../../../../../packages/components/radio/src/constants.ts"], "sourcesContent": ["import type { InjectionKey } from 'vue'\nimport type { RadioGroupProps } from './radio-group'\n\nexport interface RadioGroupContext extends RadioGroupProps {\n  changeEvent: (val: RadioGroupProps['modelValue']) => void\n}\n\nexport const radioGroupKey: InjectionKey<RadioGroupContext> =\n  Symbol('radioGroupKey')\n"], "mappings": "AAAY,MAACA,aAAa,GAAGC,MAAM,CAAC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}