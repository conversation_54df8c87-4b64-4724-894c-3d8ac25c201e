{"ast": null, "code": "import { HORIZONTAL, VERTICAL } from '../defaults.mjs';\nimport { cAF, rAF } from '../../../../utils/raf.mjs';\nimport { isFirefox } from '../../../../utils/browser.mjs';\nconst LayoutKeys = {\n  [HORIZONTAL]: \"deltaX\",\n  [VERTICAL]: \"deltaY\"\n};\nconst useWheel = ({\n  atEndEdge,\n  atStartEdge,\n  layout\n}, onWheelDelta) => {\n  let frameHandle;\n  let offset = 0;\n  const hasReachedEdge = offset2 => {\n    const edgeReached = offset2 < 0 && atStartEdge.value || offset2 > 0 && atEndEdge.value;\n    return edgeReached;\n  };\n  const onWheel = e => {\n    cAF(frameHandle);\n    const newOffset = e[LayoutKeys[layout.value]];\n    if (hasReachedEdge(offset) && hasReachedEdge(offset + newOffset)) return;\n    offset += newOffset;\n    if (!isFirefox()) {\n      e.preventDefault();\n    }\n    frameHandle = rAF(() => {\n      onWheelDelta(offset);\n      offset = 0;\n    });\n  };\n  return {\n    hasReachedEdge,\n    onWheel\n  };\n};\nexport { useWheel as default };", "map": {"version": 3, "names": ["LayoutKeys", "HORIZONTAL", "VERTICAL", "useWheel", "atEndEdge", "atStartEdge", "layout", "onWheelDelta", "frameHandle", "offset", "hasReached<PERSON><PERSON>", "offset2", "edgeReached", "value", "onWheel", "e", "cAF", "newOffset", "isFirefox", "preventDefault", "rAF"], "sources": ["../../../../../../../packages/components/virtual-list/src/hooks/use-wheel.ts"], "sourcesContent": ["import { cAF, isFirefox, rAF } from '@element-plus/utils'\nimport { HORIZONTAL, VERTICAL } from '../defaults'\n\nimport type { ComputedRef } from 'vue'\nimport type { LayoutDirection } from '../types'\n\nconst LayoutKeys = {\n  [HORIZONTAL]: 'deltaX',\n  [VERTICAL]: 'deltaY',\n} as const\n\ninterface ListWheelState {\n  atStartEdge: ComputedRef<boolean> // exclusive to reachEnd\n  atEndEdge: ComputedRef<boolean>\n  layout: ComputedRef<LayoutDirection>\n}\n\ntype ListWheelHandler = (offset: number) => void\n\nconst useWheel = (\n  { atEndEdge, atStartEdge, layout }: ListWheelState,\n  onWheelDelta: ListWheelHandler\n) => {\n  let frameHandle: number\n  let offset = 0\n\n  const hasReachedEdge = (offset: number) => {\n    const edgeReached =\n      (offset < 0 && atStartEdge.value) || (offset > 0 && atEndEdge.value)\n\n    return edgeReached\n  }\n\n  const onWheel = (e: WheelEvent) => {\n    cAF(frameHandle)\n\n    const newOffset = e[LayoutKeys[layout.value]]\n\n    if (hasReachedEdge(offset) && hasReachedEdge(offset + newOffset)) return\n\n    offset += newOffset\n\n    if (!isFirefox()) {\n      e.preventDefault()\n    }\n\n    frameHandle = rAF(() => {\n      onWheelDelta(offset)\n      offset = 0\n    })\n  }\n\n  return {\n    hasReachedEdge,\n    onWheel,\n  }\n}\n\nexport default useWheel\n"], "mappings": ";;;AAEA,MAAMA,UAAU,GAAG;EACjB,CAACC,UAAU,GAAG,QAAQ;EACtB,CAACC,QAAQ,GAAG;AACd,CAAC;AACI,MAACC,QAAQ,GAAGA,CAAC;EAAEC,SAAS;EAAEC,WAAW;EAAEC;AAAM,CAAE,EAAEC,YAAY,KAAK;EACrE,IAAIC,WAAW;EACf,IAAIC,MAAM,GAAG,CAAC;EACd,MAAMC,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,WAAW,GAAGD,OAAO,GAAG,CAAC,IAAIN,WAAW,CAACQ,KAAK,IAAIF,OAAO,GAAG,CAAC,IAAIP,SAAS,CAACS,KAAK;IACtF,OAAOD,WAAW;EACtB,CAAG;EACD,MAAME,OAAO,GAAIC,CAAC,IAAK;IACrBC,GAAG,CAACR,WAAW,CAAC;IAChB,MAAMS,SAAS,GAAGF,CAAC,CAACf,UAAU,CAACM,MAAM,CAACO,KAAK,CAAC,CAAC;IAC7C,IAAIH,cAAc,CAACD,MAAM,CAAC,IAAIC,cAAc,CAACD,MAAM,GAAGQ,SAAS,CAAC,EAC9D;IACFR,MAAM,IAAIQ,SAAS;IACnB,IAAI,CAACC,SAAS,EAAE,EAAE;MAChBH,CAAC,CAACI,cAAc,EAAE;IACxB;IACIX,WAAW,GAAGY,GAAG,CAAC,MAAM;MACtBb,YAAY,CAACE,MAAM,CAAC;MACpBA,MAAM,GAAG,CAAC;IAChB,CAAK,CAAC;EACN,CAAG;EACD,OAAO;IACLC,cAAc;IACdI;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}