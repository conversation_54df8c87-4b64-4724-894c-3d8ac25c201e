{"ast": null, "code": "import { defineComponent, getCurrentInstance, useSlots, inject, ref, computed, watch, reactive, onMounted, onUnmounted, unref, withDirectives, openBlock, createElementBlock, normalizeClass, renderSlot, vShow, createCommentVNode } from 'vue';\nimport { eagerComputed } from '@vueuse/core';\nimport { tabsRootContextKey } from './constants.mjs';\nimport { tabPaneProps } from './tab-pane.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst COMPONENT_NAME = \"ElTabPane\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tabPaneProps,\n  setup(__props) {\n    const props = __props;\n    const instance = getCurrentInstance();\n    const slots = useSlots();\n    const tabsRoot = inject(tabsRootContextKey);\n    if (!tabsRoot) throwError(COMPONENT_NAME, \"usage: <el-tabs><el-tab-pane /></el-tabs/>\");\n    const ns = useNamespace(\"tab-pane\");\n    const index = ref();\n    const isClosable = computed(() => props.closable || tabsRoot.props.closable);\n    const active = eagerComputed(() => {\n      var _a;\n      return tabsRoot.currentName.value === ((_a = props.name) != null ? _a : index.value);\n    });\n    const loaded = ref(active.value);\n    const paneName = computed(() => {\n      var _a;\n      return (_a = props.name) != null ? _a : index.value;\n    });\n    const shouldBeRender = eagerComputed(() => !props.lazy || loaded.value || active.value);\n    watch(active, val => {\n      if (val) loaded.value = true;\n    });\n    const pane = reactive({\n      uid: instance.uid,\n      slots,\n      props,\n      paneName,\n      active,\n      index,\n      isClosable\n    });\n    tabsRoot.registerPane(pane);\n    onMounted(() => {\n      tabsRoot.sortPane(pane);\n    });\n    onUnmounted(() => {\n      tabsRoot.unregisterPane(pane.uid);\n    });\n    return (_ctx, _cache) => {\n      return unref(shouldBeRender) ? withDirectives((openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        id: `pane-${unref(paneName)}`,\n        class: normalizeClass(unref(ns).b()),\n        role: \"tabpanel\",\n        \"aria-hidden\": !unref(active),\n        \"aria-labelledby\": `tab-${unref(paneName)}`\n      }, [renderSlot(_ctx.$slots, \"default\")], 10, [\"id\", \"aria-hidden\", \"aria-labelledby\"])), [[vShow, unref(active)]]) : createCommentVNode(\"v-if\", true);\n    };\n  }\n});\nvar TabPane = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tab-pane.vue\"]]);\nexport { TabPane as default };", "map": {"version": 3, "names": ["name", "COMPONENT_NAME", "instance", "getCurrentInstance", "slots", "useSlots", "tabsRoot", "inject", "tabsRootContextKey", "throwError", "ns", "useNamespace", "index", "ref", "isClosable", "computed", "props", "closable", "active", "eagerComputed", "_a", "currentName", "value", "loaded", "paneName", "shouldBeRender", "lazy", "watch", "val", "pane", "reactive", "uid", "registerPane", "onMounted", "sortPane"], "sources": ["../../../../../../packages/components/tabs/src/tab-pane.vue"], "sourcesContent": ["<template>\n  <div\n    v-if=\"shouldBeRender\"\n    v-show=\"active\"\n    :id=\"`pane-${paneName}`\"\n    :class=\"ns.b()\"\n    role=\"tabpanel\"\n    :aria-hidden=\"!active\"\n    :aria-labelledby=\"`tab-${paneName}`\"\n  >\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  getCurrentInstance,\n  inject,\n  onMounted,\n  onUnmounted,\n  reactive,\n  ref,\n  useSlots,\n  watch,\n} from 'vue'\nimport { eagerComputed } from '@vueuse/core'\nimport { throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { tabsRootContextKey } from './constants'\nimport { tabPaneProps } from './tab-pane'\n\nconst COMPONENT_NAME = 'ElTabPane'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\nconst props = defineProps(tabPaneProps)\n\nconst instance = getCurrentInstance()!\nconst slots = useSlots()\n\nconst tabsRoot = inject(tabsRootContextKey)\nif (!tabsRoot)\n  throwError(COMPONENT_NAME, 'usage: <el-tabs><el-tab-pane /></el-tabs/>')\n\nconst ns = useNamespace('tab-pane')\n\nconst index = ref<string>()\nconst isClosable = computed(() => props.closable || tabsRoot.props.closable)\nconst active = eagerComputed(\n  () => tabsRoot.currentName.value === (props.name ?? index.value)\n)\nconst loaded = ref(active.value)\nconst paneName = computed(() => props.name ?? index.value)\nconst shouldBeRender = eagerComputed(\n  () => !props.lazy || loaded.value || active.value\n)\n\nwatch(active, (val) => {\n  if (val) loaded.value = true\n})\n\nconst pane = reactive({\n  uid: instance.uid,\n  slots,\n  props,\n  paneName,\n  active,\n  index,\n  isClosable,\n})\n\ntabsRoot.registerPane(pane)\nonMounted(() => {\n  tabsRoot.sortPane(pane)\n})\n\nonUnmounted(() => {\n  tabsRoot.unregisterPane(pane.uid)\n})\n</script>\n"], "mappings": ";;;;;;;;mCAiCc;EACZA,IAAM,EAAAC;AACR;;;;;;IAGA,MAAMC,QAAA,GAAWC,kBAAmB;IACpC,MAAMC,KAAA,GAAQC,QAAS;IAEjB,MAAAC,QAAA,GAAWC,MAAA,CAAOC,kBAAkB;IAC1C,IAAI,CAACF,QAAA,EACHG,UAAA,CAAWR,cAAA,EAAgB,4CAA4C;IAEnE,MAAAS,EAAA,GAAKC,YAAA,CAAa,UAAU;IAElC,MAAMC,KAAA,GAAQC,GAAY;IAC1B,MAAMC,UAAA,GAAaC,QAAS,OAAMC,KAAA,CAAMC,QAAY,IAAAX,QAAA,CAASU,KAAA,CAAMC,QAAQ;IAC3E,MAAMC,MAAS,GAAAC,aAAA;MACb,IAAAC,EAAM;MACR,OAAAd,QAAA,CAAAe,WAAA,CAAAC,KAAA,OAAAF,EAAA,GAAAJ,KAAA,CAAAhB,IAAA,YAAAoB,EAAA,GAAAR,KAAA,CAAAU,KAAA;IACA,CAAM;IACN,MAAMC,MAAA,GAAAV,GAAoB,CAAAK,MAAA,CAAAI,KAAA,CAAM;IAChC,MAAME,QAAiB,GAAAT,QAAA;MACrB,IAAAK,EAAM;MACR,QAAAA,EAAA,GAAAJ,KAAA,CAAAhB,IAAA,YAAAoB,EAAA,GAAAR,KAAA,CAAAU,KAAA;IAEA,CAAM;IACA,MAAAG,cAAoB,GAAAN,aAAA,QAAAH,KAAA,CAAAU,IAAA,IAAAH,MAAA,CAAAD,KAAA,IAAAJ,MAAA,CAAAI,KAAA;IAC1BK,KAAC,CAAAT,MAAA,EAAAU,GAAA;MAED,IAAMA,GAAA,EAAgBL,MACN,CAAAD,KAAA;IAAA,CACd;IACA,MAAAO,IAAA,GAAAC,QAAA;MACAC,GAAA,EAAA7B,QAAA,CAAA6B,GAAA;MACA3B,KAAA;MACAY,KAAA;MACAQ,QAAA;MACDN,MAAA;MAEDN,KAAA;MACAE;IACE;IACFR,QAAC,CAAA0B,YAAA,CAAAH,IAAA;IAEDI,SAAA,OAAkB;MACP3B,QAAA,CAAA4B,QAAA,CAAAL,IAAA;IAAuB,CACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}