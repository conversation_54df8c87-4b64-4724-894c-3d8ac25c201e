{"ast": null, "code": "import { defineComponent, ref, watch, toRef, computed, inject, openBlock, createElementBlock, normalizeStyle, unref, normalizeClass, createVNode, withCtx, renderSlot, createCommentVNode } from 'vue';\nimport ElFocusTrap from '../../focus-trap/src/focus-trap.mjs';\nimport { tourContentProps, tourContentEmits } from './content2.mjs';\nimport { useFloating, tourKey } from './helper.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTourContent\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tourContentProps,\n  emits: tourContentEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const placement = ref(props.placement);\n    const strategy = ref(props.strategy);\n    const contentRef = ref(null);\n    const arrowRef = ref(null);\n    watch(() => props.placement, () => {\n      placement.value = props.placement;\n    });\n    const {\n      contentStyle,\n      arrowStyle\n    } = useFloating(toRef(props, \"reference\"), contentRef, arrowRef, placement, strategy, toRef(props, \"offset\"), toRef(props, \"zIndex\"), toRef(props, \"showArrow\"));\n    const side = computed(() => {\n      return placement.value.split(\"-\")[0];\n    });\n    const {\n      ns\n    } = inject(tourKey);\n    const onCloseRequested = () => {\n      emit(\"close\");\n    };\n    const onFocusoutPrevented = event => {\n      if (event.detail.focusReason === \"pointer\") {\n        event.preventDefault();\n      }\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"contentRef\",\n        ref: contentRef,\n        style: normalizeStyle(unref(contentStyle)),\n        class: normalizeClass(unref(ns).e(\"content\")),\n        \"data-side\": unref(side),\n        tabindex: \"-1\"\n      }, [createVNode(unref(ElFocusTrap), {\n        loop: \"\",\n        trapped: \"\",\n        \"focus-start-el\": \"container\",\n        \"focus-trap-el\": contentRef.value || void 0,\n        onReleaseRequested: onCloseRequested,\n        onFocusoutPrevented\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 8, [\"focus-trap-el\"]), _ctx.showArrow ? (openBlock(), createElementBlock(\"span\", {\n        key: 0,\n        ref_key: \"arrowRef\",\n        ref: arrowRef,\n        style: normalizeStyle(unref(arrowStyle)),\n        class: normalizeClass(unref(ns).e(\"arrow\"))\n      }, null, 6)) : createCommentVNode(\"v-if\", true)], 14, [\"data-side\"]);\n    };\n  }\n});\nvar ElTourContent = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"content.vue\"]]);\nexport { ElTourContent as default };", "map": {"version": 3, "names": ["name", "placement", "ref", "props", "strategy", "contentRef", "arrowRef", "watch", "value", "contentStyle", "arrowStyle", "useFloating", "toRef", "side", "computed", "split", "ns", "inject", "tourKey", "onCloseRequested", "emit", "onFocusoutPrevented", "event", "detail", "focusReason", "preventDefault", "_ctx", "_cache", "openBlock", "createElementBlock", "ref_key", "style", "normalizeStyle", "unref", "class", "normalizeClass", "e", "tabindex", "createVNode", "ElFocusTrap", "loop", "trapped"], "sources": ["../../../../../../packages/components/tour/src/content.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"contentRef\"\n    :style=\"contentStyle\"\n    :class=\"ns.e('content')\"\n    :data-side=\"side\"\n    tabindex=\"-1\"\n  >\n    <el-focus-trap\n      loop\n      trapped\n      focus-start-el=\"container\"\n      :focus-trap-el=\"contentRef || undefined\"\n      @release-requested=\"onCloseRequested\"\n      @focusout-prevented=\"onFocusoutPrevented\"\n    >\n      <slot />\n    </el-focus-trap>\n    <span\n      v-if=\"showArrow\"\n      ref=\"arrowRef\"\n      :style=\"arrowStyle\"\n      :class=\"ns.e('arrow')\"\n    />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject, ref, toRef, watch } from 'vue'\nimport ElFocusTrap from '@element-plus/components/focus-trap'\nimport { tourContentEmits, tourContentProps } from './content'\nimport { tourKey, useFloating } from './helper'\n\ndefineOptions({\n  name: 'ElTourContent',\n})\n\nconst props = defineProps(tourContentProps)\nconst emit = defineEmits(tourContentEmits)\n\nconst placement = ref(props.placement)\nconst strategy = ref(props.strategy)\nconst contentRef = ref<HTMLElement | null>(null)\nconst arrowRef = ref<HTMLElement | null>(null)\n\nwatch(\n  () => props.placement,\n  () => {\n    placement.value = props.placement\n  }\n)\n\nconst { contentStyle, arrowStyle } = useFloating(\n  toRef(props, 'reference'),\n  contentRef,\n  arrowRef,\n  placement,\n  strategy,\n  toRef(props, 'offset'),\n  toRef(props, 'zIndex'),\n  toRef(props, 'showArrow')\n)\n\nconst side = computed(() => {\n  return placement.value.split('-')[0]\n})\n\nconst { ns } = inject(tourKey)!\n\nconst onCloseRequested = () => {\n  emit('close')\n}\n\nconst onFocusoutPrevented = (event: CustomEvent) => {\n  if (event.detail.focusReason === 'pointer') {\n    event.preventDefault()\n  }\n}\n</script>\n"], "mappings": ";;;;;mCAiCc;EACZA,IAAM;AACR;;;;;;;;;IAKM,MAAAC,SAAA,GAAYC,GAAI,CAAAC,KAAA,CAAMF,SAAS;IAC/B,MAAAG,QAAA,GAAWF,GAAI,CAAAC,KAAA,CAAMC,QAAQ;IAC7B,MAAAC,UAAA,GAAaH,GAAA,CAAwB,IAAI;IACzC,MAAAI,QAAA,GAAWJ,GAAA,CAAwB,IAAI;IAE7CK,KAAA,OAAAJ,KAAA,CAAAF,SAAA;MACEA,SAAY,CAAAO,KAAA,GAAAL,KAAA,CAAAF,SAAA;IAAA,EACZ;IACE;MAAAQ,YAAA;MAAkBC;IAAM,IAAAC,WAAA,CAAAC,KAAA,CAAAT,KAAA,gBAAAE,UAAA,EAAAC,QAAA,EAAAL,SAAA,EAAAG,QAAA,EAAAQ,KAAA,CAAAT,KAAA,aAAAS,KAAA,CAAAT,KAAA,aAAAS,KAAA,CAAAT,KAAA;IAC1B,MAAAU,IAAA,GAAAC,QAAA;MACF,OAAAb,SAAA,CAAAO,KAAA,CAAAO,KAAA;IAEA,CAAM;IACJ;MAAMC;IAAA,IAAOC,MAAW,CAAAC,OAAA;IACxB,MAAAC,gBAAA,GAAAA,CAAA;MACAC,IAAA;IAAA,CACA;IACA,MAAAC,mBAAA,GAAAC,KAAA;MACA,IAAAA,KAAA,CAAAC,MAAqB,CAAAC,WAAA;QACrBF,KAAA,CAAAG,cAAqB;MAAA;IACG,CAC1B;IAEM,QAAAC,IAAA,EAAAC,MAAA,KAAsB;MAC1B,OAAOC,SAAU,IAAAC,kBAAkB;QACpCC,OAAA;QAED5B,GAAQ,EAAAG,UAAO;QAEf0B,KAAA,EAAAC,cAA+B,CAAAC,KAAA,CAAAxB,YAAA;QAC7ByB,KAAY,EAAAC,cAAA,CAAAF,KAAA,CAAAjB,EAAA,EAAAoB,CAAA;QACd,aAAAH,KAAA,CAAApB,IAAA;QAEMwB,QAAA;MACJ,CAAI,GACFC,WAAqB,CAAAL,KAAA,CAAAM,WAAA;QACvBC,IAAA;QACFC,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}