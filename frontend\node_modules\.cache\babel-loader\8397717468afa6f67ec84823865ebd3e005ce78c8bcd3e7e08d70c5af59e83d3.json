{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, computed, getCurrentInstance, ref, watch, nextTick, provide, createVNode, renderSlot } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { Plus } from '@element-plus/icons-vue';\nimport { tabsRootContextKey } from './constants.mjs';\nimport TabNav from './tab-nav.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useOrderedChildren } from '../../../hooks/use-ordered-children/index.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { isString } from '@vue/shared';\nimport { isNumber, isUndefined } from '../../../utils/types.mjs';\nconst tabsProps = buildProps({\n  type: {\n    type: String,\n    values: [\"card\", \"border-card\", \"\"],\n    default: \"\"\n  },\n  closable: Boolean,\n  addable: Boolean,\n  modelValue: {\n    type: [String, Number]\n  },\n  editable: Boolean,\n  tabPosition: {\n    type: String,\n    values: [\"top\", \"right\", \"bottom\", \"left\"],\n    default: \"top\"\n  },\n  beforeLeave: {\n    type: definePropType(Function),\n    default: () => true\n  },\n  stretch: Boolean\n});\nconst isPaneName = value => isString(value) || isNumber(value);\nconst tabsEmits = {\n  [UPDATE_MODEL_EVENT]: name => isPaneName(name),\n  tabClick: (pane, ev) => ev instanceof Event,\n  tabChange: name => isPaneName(name),\n  edit: (paneName, action) => [\"remove\", \"add\"].includes(action),\n  tabRemove: name => isPaneName(name),\n  tabAdd: () => true\n};\nconst Tabs = defineComponent({\n  name: \"ElTabs\",\n  props: tabsProps,\n  emits: tabsEmits,\n  setup(props, {\n    emit,\n    slots,\n    expose\n  }) {\n    var _a;\n    const ns = useNamespace(\"tabs\");\n    const isVertical = computed(() => [\"left\", \"right\"].includes(props.tabPosition));\n    const {\n      children: panes,\n      addChild: sortPane,\n      removeChild: unregisterPane\n    } = useOrderedChildren(getCurrentInstance(), \"ElTabPane\");\n    const nav$ = ref();\n    const currentName = ref((_a = props.modelValue) != null ? _a : \"0\");\n    const setCurrentName = async (value, trigger = false) => {\n      var _a2, _b;\n      if (currentName.value === value || isUndefined(value)) return;\n      try {\n        let canLeave;\n        if (props.beforeLeave) {\n          const result = props.beforeLeave(value, currentName.value);\n          canLeave = result instanceof Promise ? await result : result;\n        } else {\n          canLeave = true;\n        }\n        if (canLeave !== false) {\n          currentName.value = value;\n          if (trigger) {\n            emit(UPDATE_MODEL_EVENT, value);\n            emit(\"tabChange\", value);\n          }\n          (_b = (_a2 = nav$.value) == null ? void 0 : _a2.removeFocus) == null ? void 0 : _b.call(_a2);\n        }\n      } catch (e) {}\n    };\n    const handleTabClick = (tab, tabName, event) => {\n      if (tab.props.disabled) return;\n      emit(\"tabClick\", tab, event);\n      setCurrentName(tabName, true);\n    };\n    const handleTabRemove = (pane, ev) => {\n      if (pane.props.disabled || isUndefined(pane.props.name)) return;\n      ev.stopPropagation();\n      emit(\"edit\", pane.props.name, \"remove\");\n      emit(\"tabRemove\", pane.props.name);\n    };\n    const handleTabAdd = () => {\n      emit(\"edit\", void 0, \"add\");\n      emit(\"tabAdd\");\n    };\n    watch(() => props.modelValue, modelValue => setCurrentName(modelValue));\n    watch(currentName, async () => {\n      var _a2;\n      await nextTick();\n      (_a2 = nav$.value) == null ? void 0 : _a2.scrollToActiveTab();\n    });\n    provide(tabsRootContextKey, {\n      props,\n      currentName,\n      registerPane: pane => {\n        panes.value.push(pane);\n      },\n      sortPane,\n      unregisterPane\n    });\n    expose({\n      currentName,\n      tabNavRef: nav$\n    });\n    const TabNavRenderer = ({\n      render\n    }) => {\n      return render();\n    };\n    return () => {\n      const addSlot = slots[\"add-icon\"];\n      const newButton = props.editable || props.addable ? createVNode(\"div\", {\n        \"class\": [ns.e(\"new-tab\"), isVertical.value && ns.e(\"new-tab-vertical\")],\n        \"tabindex\": \"0\",\n        \"onClick\": handleTabAdd,\n        \"onKeydown\": ev => {\n          if ([EVENT_CODE.enter, EVENT_CODE.numpadEnter].includes(ev.code)) handleTabAdd();\n        }\n      }, [addSlot ? renderSlot(slots, \"add-icon\") : createVNode(ElIcon, {\n        \"class\": ns.is(\"icon-plus\")\n      }, {\n        default: () => [createVNode(Plus, null, null)]\n      })]) : null;\n      const header = createVNode(\"div\", {\n        \"class\": [ns.e(\"header\"), isVertical.value && ns.e(\"header-vertical\"), ns.is(props.tabPosition)]\n      }, [createVNode(TabNavRenderer, {\n        \"render\": () => {\n          const hasLabelSlot = panes.value.some(pane => pane.slots.label);\n          return createVNode(TabNav, {\n            ref: nav$,\n            currentName: currentName.value,\n            editable: props.editable,\n            type: props.type,\n            panes: panes.value,\n            stretch: props.stretch,\n            onTabClick: handleTabClick,\n            onTabRemove: handleTabRemove\n          }, {\n            $stable: !hasLabelSlot\n          });\n        }\n      }, null), newButton]);\n      const panels = createVNode(\"div\", {\n        \"class\": ns.e(\"content\")\n      }, [renderSlot(slots, \"default\")]);\n      return createVNode(\"div\", {\n        \"class\": [ns.b(), ns.m(props.tabPosition), {\n          [ns.m(\"card\")]: props.type === \"card\",\n          [ns.m(\"border-card\")]: props.type === \"border-card\"\n        }]\n      }, [panels, header]);\n    };\n  }\n});\nvar Tabs$1 = Tabs;\nexport { Tabs$1 as default, tabsEmits, tabsProps };", "map": {"version": 3, "names": ["tabsProps", "buildProps", "type", "String", "values", "default", "closable", "Boolean", "addable", "modelValue", "editable", "tabPosition", "beforeLeave", "definePropType", "Function", "stretch", "isPaneName", "value", "isString", "isNumber", "tabsEmits", "UPDATE_MODEL_EVENT", "name", "tabChange", "edit", "paneName", "action", "includes", "tabRemove", "tabAdd", "Tabs", "defineComponent", "props", "emits", "setup", "emit", "slots", "expose", "_a", "ns", "useNamespace", "isVertical", "computed", "children", "panes", "<PERSON><PERSON><PERSON><PERSON>", "sortPane", "<PERSON><PERSON><PERSON><PERSON>", "unregisterPane", "nav$", "ref", "currentName", "setCurrentName", "trigger", "_a2", "_b", "isUndefined", "result", "canLeave", "Promise", "e", "handleTabClick", "tab", "tabName", "event", "disabled", "handleTabRemove", "pane", "ev", "stopPropagation", "handleTabAdd", "nextTick", "scrollToActiveTab", "provide", "tabsRootContextKey", "tabNavRef", "TabNav<PERSON><PERSON><PERSON>", "render", "addSlot", "EVENT_CODE", "enter", "numpadEnter", "code", "renderSlot", "createVNode", "ElIcon", "is", "Plus", "header", "hasLabelSlot", "some", "label", "TabNav", "onTabClick", "onTabRemove", "$stable", "panels", "b", "m", "Tabs$1"], "sources": ["../../../../../../packages/components/tabs/src/tabs.tsx"], "sourcesContent": ["import {\n  computed,\n  createVNode,\n  defineComponent,\n  getCurrentInstance,\n  nextTick,\n  provide,\n  ref,\n  renderSlot,\n  watch,\n} from 'vue'\nimport {\n  buildProps,\n  definePropType,\n  isNumber,\n  isString,\n  isUndefined,\n} from '@element-plus/utils'\nimport { EVENT_CODE, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport ElIcon from '@element-plus/components/icon'\nimport { Plus } from '@element-plus/icons-vue'\nimport { useNamespace, useOrderedChildren } from '@element-plus/hooks'\nimport { tabsRootContextKey } from './constants'\nimport TabNav from './tab-nav'\n\nimport type { TabNavInstance } from './tab-nav'\nimport type { TabsPaneContext } from './constants'\nimport type { ExtractPropTypes, FunctionalComponent, VNode } from 'vue'\nimport type { Awaitable } from '@element-plus/utils'\n\nexport type TabPaneName = string | number\n\nexport const tabsProps = buildProps({\n  /**\n   * @description type of Tab\n   */\n  type: {\n    type: String,\n    values: ['card', 'border-card', ''],\n    default: '',\n  },\n  /**\n   * @description whether Tab is closable\n   */\n  closable: Boolean,\n  /**\n   * @description whether Tab is addable\n   */\n  addable: Boolean,\n  /**\n   * @description binding value, name of the selected tab\n   */\n  modelValue: {\n    type: [String, Number],\n  },\n  /**\n   * @description whether Tab is addable and closable\n   */\n  editable: Boolean,\n  /**\n   * @description position of tabs\n   */\n  tabPosition: {\n    type: String,\n    values: ['top', 'right', 'bottom', 'left'],\n    default: 'top',\n  },\n  /**\n   * @description hook function before switching tab. If `false` is returned or a `Promise` is returned and then is rejected, switching will be prevented\n   */\n  beforeLeave: {\n    type: definePropType<\n      (newName: TabPaneName, oldName: TabPaneName) => Awaitable<void | boolean>\n    >(Function),\n    default: () => true,\n  },\n  /**\n   * @description whether width of tab automatically fits its container\n   */\n  stretch: Boolean,\n} as const)\nexport type TabsProps = ExtractPropTypes<typeof tabsProps>\n\nconst isPaneName = (value: unknown): value is string | number =>\n  isString(value) || isNumber(value)\n\nexport const tabsEmits = {\n  [UPDATE_MODEL_EVENT]: (name: TabPaneName) => isPaneName(name),\n  tabClick: (pane: TabsPaneContext, ev: Event) => ev instanceof Event,\n  tabChange: (name: TabPaneName) => isPaneName(name),\n  edit: (paneName: TabPaneName | undefined, action: 'remove' | 'add') =>\n    ['remove', 'add'].includes(action),\n  tabRemove: (name: TabPaneName) => isPaneName(name),\n  tabAdd: () => true,\n}\nexport type TabsEmits = typeof tabsEmits\n\nexport type TabsPanes = Record<number, TabsPaneContext>\n\nconst Tabs = defineComponent({\n  name: 'ElTabs',\n\n  props: tabsProps,\n  emits: tabsEmits,\n\n  setup(props, { emit, slots, expose }) {\n    const ns = useNamespace('tabs')\n\n    const isVertical = computed(() =>\n      ['left', 'right'].includes(props.tabPosition)\n    )\n\n    const {\n      children: panes,\n      addChild: sortPane,\n      removeChild: unregisterPane,\n    } = useOrderedChildren<TabsPaneContext>(getCurrentInstance()!, 'ElTabPane')\n\n    const nav$ = ref<TabNavInstance>()\n    const currentName = ref<TabPaneName>(props.modelValue ?? '0')\n\n    const setCurrentName = async (value?: TabPaneName, trigger = false) => {\n      // should do nothing.\n      if (currentName.value === value || isUndefined(value)) return\n\n      try {\n        let canLeave\n        if (props.beforeLeave) {\n          const result = props.beforeLeave(value, currentName.value)\n          canLeave = result instanceof Promise ? await result : result\n        } else {\n          canLeave = true\n        }\n\n        if (canLeave !== false) {\n          currentName.value = value\n          if (trigger) {\n            emit(UPDATE_MODEL_EVENT, value)\n            emit('tabChange', value)\n          }\n\n          nav$.value?.removeFocus?.()\n        }\n      } catch {}\n    }\n\n    const handleTabClick = (\n      tab: TabsPaneContext,\n      tabName: TabPaneName,\n      event: Event\n    ) => {\n      if (tab.props.disabled) return\n      emit('tabClick', tab, event)\n      setCurrentName(tabName, true)\n    }\n\n    const handleTabRemove = (pane: TabsPaneContext, ev: Event) => {\n      if (pane.props.disabled || isUndefined(pane.props.name)) return\n      ev.stopPropagation()\n      emit('edit', pane.props.name, 'remove')\n      emit('tabRemove', pane.props.name)\n    }\n\n    const handleTabAdd = () => {\n      emit('edit', undefined, 'add')\n      emit('tabAdd')\n    }\n\n    watch(\n      () => props.modelValue,\n      (modelValue) => setCurrentName(modelValue)\n    )\n\n    watch(currentName, async () => {\n      await nextTick()\n      nav$.value?.scrollToActiveTab()\n    })\n\n    provide(tabsRootContextKey, {\n      props,\n      currentName,\n      registerPane: (pane: TabsPaneContext) => {\n        panes.value.push(pane)\n      },\n      sortPane,\n      unregisterPane,\n    })\n\n    expose({\n      currentName,\n      tabNavRef: nav$,\n    })\n    const TabNavRenderer: FunctionalComponent<{ render: () => VNode }> = ({\n      render,\n    }) => {\n      return render()\n    }\n    return () => {\n      const addSlot = slots['add-icon']\n      const newButton =\n        props.editable || props.addable ? (\n          <div\n            class={[\n              ns.e('new-tab'),\n              isVertical.value && ns.e('new-tab-vertical'),\n            ]}\n            tabindex=\"0\"\n            onClick={handleTabAdd}\n            onKeydown={(ev: KeyboardEvent) => {\n              if ([EVENT_CODE.enter, EVENT_CODE.numpadEnter].includes(ev.code))\n                handleTabAdd()\n            }}\n          >\n            {addSlot ? (\n              renderSlot(slots, 'add-icon')\n            ) : (\n              <ElIcon class={ns.is('icon-plus')}>\n                <Plus />\n              </ElIcon>\n            )}\n          </div>\n        ) : null\n\n      const header = (\n        <div\n          class={[\n            ns.e('header'),\n            isVertical.value && ns.e('header-vertical'),\n            ns.is(props.tabPosition),\n          ]}\n        >\n          <TabNavRenderer\n            render={() => {\n              const hasLabelSlot = panes.value.some((pane) => pane.slots.label)\n              return createVNode(\n                TabNav,\n                {\n                  ref: nav$,\n                  currentName: currentName.value,\n                  editable: props.editable,\n                  type: props.type,\n                  panes: panes.value,\n                  stretch: props.stretch,\n                  onTabClick: handleTabClick,\n                  onTabRemove: handleTabRemove,\n                },\n                { $stable: !hasLabelSlot }\n              )\n            }}\n          />\n          {newButton}\n        </div>\n      )\n\n      const panels = (\n        <div class={ns.e('content')}>{renderSlot(slots, 'default')}</div>\n      )\n\n      return (\n        <div\n          class={[\n            ns.b(),\n            ns.m(props.tabPosition),\n            {\n              [ns.m('card')]: props.type === 'card',\n              [ns.m('border-card')]: props.type === 'border-card',\n            },\n          ]}\n        >\n          {panels}\n          {header}\n        </div>\n      )\n    }\n  },\n})\n\nexport type TabsInstance = InstanceType<typeof Tabs> & {\n  currentName: TabPaneName\n  tabNavRef: TabNavInstance | undefined\n}\n\nexport default Tabs\n"], "mappings": ";;;;;;;;;;;;;;;AAgCa,MAAAA,SAAS,GAAGC,UAAU,CAAC;EAClCC,IAAA;IACFA,IAAA,EAAAC,MAAA;IACAC,MAAA;IACEC,OAAM;EACJ;EACAC,QAAM,EAAEC,OAAA;EACRC,OAAA,EAAOD,OAAE;EAHLE,UAJ4B;;EASlC;EACFC,QAAA,EAAAH,OAAA;EACAI,WAAA;IACET,IAAA,EAAQC,MAZ0B;;IAalCE,OAAA;EACF;EACAO,WAAA;IACEV,IAAA,EAAAW,cAhBkC,CAAAC,QAAA;;EAiBlC;EACFC,OAAA,EAAAR;AACA;AACE,MAAAS,UAAY,GAAAC,KAAA,IAAAC,QAAA,CAAAD,KAAA,KAAAE,QAAA,CAAAF,KAAA;AACJ,MAAAG,SAAA;EADI,CApBsBC,kBAAA,GAAAC,IAAA,IAAAN,UAAA,CAAAM,IAAA;;EAuBlCC,SAAA,EAAAD,IAAA,IAAAN,UAAA,CAAAM,IAAA;EACFE,IAAA,EAAAA,CAAAC,QAAA,EAAAC,MAAA,uBAAAC,QAAA,CAAAD,MAAA;EACAE,SAAA,EAAAN,IAAA,IAAAN,UAAA,CAAAM,IAAA;EACEO,MAAA,EAAQnB,CAAA,KA1B0B;;AA2BlC,MAAAoB,IAAA,GAAAC,eAAA;EACFT,IAAA;EACAU,KAAA,EAAAhC,SAAA;EACEiC,KAAA,EAAAb,SAAa;EACXc,MAAIF,KADO;IAEXG,IAAM;IACNC,KAAA;IAjCgCC;;IAmClC,IAAAC,EAAA;IACF,MAAAC,EAAA,GAAAC,YAAA;IACA,MAAAC,UAAA,GAAAC,QAAA,yBAAAf,QAAA,CAAAK,KAAA,CAAArB,WAAA;IACE;MACEgC,QAAM,EAAAC,KAAA;MAGNC,QAAe,EAAAC,QAAA;MA1CiBC,WAAA,EAAAC;;IA4ClC,MAAAC,IAAA,GAAAC,GAAA;IACF,MAAAC,WAAA,GAAAD,GAAA,EAAAZ,EAAA,GAAAN,KAAA,CAAAvB,UAAA,YAAA6B,EAAA;IACA,MAAAc,cAAA,SAAAA,CAAAnC,KAAA,EAAAoC,OAAA;MACE,IAASC,GAAA,EAAAC,EAAA;MA/CJ,IAAAJ,WAAA,CAAAlC,KAAA,KAAAA,KAAA,IAAAuC,WAAA,CAAAvC,KAAA,G;MAmDD,IAAU;;QAGT,IAAAe,KAAA,CAAApB,WAAkB;UACvB,MAAA6C,MAAuB,GAAAzB,KAAA,CAAApB,WAAgC,CAACK,KADjC,EAAAkC,WAAA,CAAAlC,KAAA;UAEfyC,QAAE,GAAAD,MAAsC,YAAAE,OAFzB,SAAAF,MAAA,GAAAA,MAAA;QAGvB,OAAW;UACLC,QAAA;QAEN;QACM,IAAQA,QAAA;UAPTP,WAAA,CAAAlC,KAAA,GAAAA,KAAA;UAaG,IAAGoC,OAAA;YAAgBlB,IAAA,CAAAd,kBAAA,EAAAJ,KAAA;YAAAkB,IAAA,cAAAlB,KAAA;UAI3B;;;OAEa,QAAA2C,CAAA,G;IAAe;IAAU,MAAAC,cAAA,GAAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,KAAA;MACpC,IAAMF,GAAE,CAAA9B,KAAe,CAAAiC,QAAC,EAExB;MAIM9B,IAAA,aAAA2B,GAAA,EAAAE,KAAA;MACJZ,cADI,CAAAW,OAAA;IAEJ;IACA,MAAAG,eAAa,GAAAA,CAAAC,IAAA,EAAAC,EAAA;MAHT,IAAAD,IAAA,CAAAnC,KAAA,CAAAiC,QAIkC,IAAAT,WAAA,CAAkBW,IAAK,CAAAnC,KAAA,CAAAV,IAAzC,CAJtB,EAMM;MACA8C,EAAA,CAAAC,eAAc,EAAG;;MAEjBlC,IAAA,cAAcgC,IAAU,CAAAnC,KAAA,CAAAV,IAAP;IACrB;UACIgD,YAAY,GAAAA,CAAA,KAAU;;MAE1BnC,IAAI;IACF;;SACI,CAAAgB,WAAM,cAAa;UACrBG,GAAM;YACEiB,QAAA;MACT,CAAAjB,GAAM,GAAAL,IAAA,CAAAhC,KAAA,qBAAAqC,GAAA,CAAAkB,iBAAA;IACL;IACDC,OAAA,CAAAC,kBAAA;;iBAEG;kBACS,EAAAP,IAAX,IAAoB;;MACpB;MACErB,QAAA;MACAE;IACD;;iBAED;MACD2B,SAAA,EAAA1B;MACF;IACF,MAvBD2B,cAAA,GAAAA,CAAA;;KAyBM;MAKJ,OAAOC,MAAH;IACJ;IACA;MAPF,MAAAC,OAAA,GAAA1C,KAAA;;QAUA,UAAAG,EAAA,CAAAqB,CAAA,UAAwB,GAAwBnB,UAAc,CAAAxB,KAAA,IAAAsB,EAAA,CAAAqB,CAAA;QAC5D,UAAI;QACF,SAAF,EAAAU,YAAA;QACI,WAAS,EAAIF,EAAC,IAAL;UACT,KAAAW,UAAc,CAAAC,KAAA,EAAAD,UAAlB,CAAAE,WAAA,EAAAtD,QAAA,CAAAyC,EAAA,CAAAc,IAAA,GAJFZ,YAAA;;OAOM,GAAAQ,OAAA,GAAAK,UAAqB,CAAA/C,KAAA,gBAAAgD,WAAA,CAAAC,MAAA;QACzB,OAAI,EAAA9C,EAAS,CAAT+C,EAAA;OACA;QAFNjF,OAAA,EAAAA,CAAA,MAAA+E,WAAA,CAAAG,IAAA;;MAKA,MACEC,MAAW,GAAAJ,WADR,CAEF,KAAD;QAGG,UAAA7C,EAAA,CAAAqB,CAAA,CAAc,QAAY,GAAAnB,UAAA,CAAAxB,KAAA,IAAAsB,EAAA,CAAAqB,CAAA,qBAAArB,EAAA,CAAA+C,EAAA,CAAAtD,KAAA,CAAArB,WAAA;MAC7B,IAAAyE,WAAA,CAAAR,cAAA;QACI,QAAJ,EAAYC,CAAA,KAAZ;UAFF,MAAAY,YAAA,GAAA7C,KAAA,CAAA3B,KAAA,CAAAyE,IAAA,CAAAvB,IAAA,IAAAA,IAAA,CAAA/B,KAAA,CAAAuD,KAAA;UAKO,OAAAP,WAAA,CAAAQ,MAAqB;YAAA1C,GAAA,EAAAD,IAAA;YAAAE,WAAA,EAAAA,WAAA,CAAAlC,KAAA;YAGdP,QAAG,EAAAsB,KAA0B,CAAAtB,QAAA;YACvCR,IAAM,EAAA8B,KAAM,CAAZ9B,IAAA;YAJwB0C,KAAA,EAAAA,KAAA,CAAA3B,KAAA;YAAAF,OAAA,EAAAiB,KAAA,CAAAjB,OAAA;YAO1B8E,UAAA,EAAAhC,cAAA;YAPFiC,WAAA,EAAA5B;UAUA,CAAO;YAAA6B,OAAA,GAAAN;UAEL;QAFK;;MAID,MAAAO,MAAA,GAAAZ,WAAgE;QACpE,SAAA7C,EAAA,CAAAqB,CAAA;MADoE,CAEhE,GAAAuB,UAAA,CAAA/C,KAAA;MACJ,OAAOgD,WAAP;QAHF,UAAA7C,EAAA,CAAA0D,CAAA,IAAA1D,EAAA,CAAA2D,CAAA,CAAAlE,KAAA,CAAArB,WAAA;;UAKA,CAAO4B,EAAM,CAAA2D,CAAA,kBAAAlE,KAAA,CAAA9B,IAAA;QACX;OACM,GAAA8F,MAAA,EAAAR,MACJ,CAAK;IAAL;EAAA;AAAA;AAAA,IAAAW,MAAA,GAAArE,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}