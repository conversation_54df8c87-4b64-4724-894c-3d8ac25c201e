{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, inject, computed, ref, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, toDisplayString, createVNode, createCommentVNode } from 'vue';\nimport dayjs from 'dayjs';\nimport { union } from 'lodash-unified';\nimport { panelTimeRangeProps } from '../props/panel-time-range.mjs';\nimport { useTimePanel } from '../composables/use-time-panel.mjs';\nimport { useOldValue, buildAvailableTimeSlotGetter } from '../composables/use-time-picker.mjs';\nimport TimeSpinner from './basic-time-spinner.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isArray } from '@vue/shared';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"panel-time-range\",\n  props: panelTimeRangeProps,\n  emits: [\"pick\", \"select-range\", \"set-picker-option\"],\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const makeSelectRange = (start, end) => {\n      const result = [];\n      for (let i = start; i <= end; i++) {\n        result.push(i);\n      }\n      return result;\n    };\n    const {\n      t,\n      lang\n    } = useLocale();\n    const nsTime = useNamespace(\"time\");\n    const nsPicker = useNamespace(\"picker\");\n    const pickerBase = inject(\"EP_PICKER_BASE\");\n    const {\n      arrowControl,\n      disabledHours,\n      disabledMinutes,\n      disabledSeconds,\n      defaultValue\n    } = pickerBase.props;\n    const startContainerKls = computed(() => [nsTime.be(\"range-picker\", \"body\"), nsTime.be(\"panel\", \"content\"), nsTime.is(\"arrow\", arrowControl), showSeconds.value ? \"has-seconds\" : \"\"]);\n    const endContainerKls = computed(() => [nsTime.be(\"range-picker\", \"body\"), nsTime.be(\"panel\", \"content\"), nsTime.is(\"arrow\", arrowControl), showSeconds.value ? \"has-seconds\" : \"\"]);\n    const startTime = computed(() => props.parsedValue[0]);\n    const endTime = computed(() => props.parsedValue[1]);\n    const oldValue = useOldValue(props);\n    const handleCancel = () => {\n      emit(\"pick\", oldValue.value, false);\n    };\n    const showSeconds = computed(() => {\n      return props.format.includes(\"ss\");\n    });\n    const amPmMode = computed(() => {\n      if (props.format.includes(\"A\")) return \"A\";\n      if (props.format.includes(\"a\")) return \"a\";\n      return \"\";\n    });\n    const handleConfirm = (visible = false) => {\n      emit(\"pick\", [startTime.value, endTime.value], visible);\n    };\n    const handleMinChange = date => {\n      handleChange(date.millisecond(0), endTime.value);\n    };\n    const handleMaxChange = date => {\n      handleChange(startTime.value, date.millisecond(0));\n    };\n    const isValidValue = _date => {\n      const parsedDate = _date.map(_ => dayjs(_).locale(lang.value));\n      const result = getRangeAvailableTime(parsedDate);\n      return parsedDate[0].isSame(result[0]) && parsedDate[1].isSame(result[1]);\n    };\n    const handleChange = (start, end) => {\n      if (!props.visible) {\n        return;\n      }\n      emit(\"pick\", [start, end], true);\n    };\n    const btnConfirmDisabled = computed(() => {\n      return startTime.value > endTime.value;\n    });\n    const selectionRange = ref([0, 2]);\n    const setMinSelectionRange = (start, end) => {\n      emit(\"select-range\", start, end, \"min\");\n      selectionRange.value = [start, end];\n    };\n    const offset = computed(() => showSeconds.value ? 11 : 8);\n    const setMaxSelectionRange = (start, end) => {\n      emit(\"select-range\", start, end, \"max\");\n      const _offset = unref(offset);\n      selectionRange.value = [start + _offset, end + _offset];\n    };\n    const changeSelectionRange = step => {\n      const list = showSeconds.value ? [0, 3, 6, 11, 14, 17] : [0, 3, 8, 11];\n      const mapping = [\"hours\", \"minutes\"].concat(showSeconds.value ? [\"seconds\"] : []);\n      const index = list.indexOf(selectionRange.value[0]);\n      const next = (index + step + list.length) % list.length;\n      const half = list.length / 2;\n      if (next < half) {\n        timePickerOptions[\"start_emitSelectRange\"](mapping[next]);\n      } else {\n        timePickerOptions[\"end_emitSelectRange\"](mapping[next - half]);\n      }\n    };\n    const handleKeydown = event => {\n      const code = event.code;\n      const {\n        left,\n        right,\n        up,\n        down\n      } = EVENT_CODE;\n      if ([left, right].includes(code)) {\n        const step = code === left ? -1 : 1;\n        changeSelectionRange(step);\n        event.preventDefault();\n        return;\n      }\n      if ([up, down].includes(code)) {\n        const step = code === up ? -1 : 1;\n        const role = selectionRange.value[0] < offset.value ? \"start\" : \"end\";\n        timePickerOptions[`${role}_scrollDown`](step);\n        event.preventDefault();\n        return;\n      }\n    };\n    const disabledHours_ = (role, compare) => {\n      const defaultDisable = disabledHours ? disabledHours(role) : [];\n      const isStart = role === \"start\";\n      const compareDate = compare || (isStart ? endTime.value : startTime.value);\n      const compareHour = compareDate.hour();\n      const nextDisable = isStart ? makeSelectRange(compareHour + 1, 23) : makeSelectRange(0, compareHour - 1);\n      return union(defaultDisable, nextDisable);\n    };\n    const disabledMinutes_ = (hour, role, compare) => {\n      const defaultDisable = disabledMinutes ? disabledMinutes(hour, role) : [];\n      const isStart = role === \"start\";\n      const compareDate = compare || (isStart ? endTime.value : startTime.value);\n      const compareHour = compareDate.hour();\n      if (hour !== compareHour) {\n        return defaultDisable;\n      }\n      const compareMinute = compareDate.minute();\n      const nextDisable = isStart ? makeSelectRange(compareMinute + 1, 59) : makeSelectRange(0, compareMinute - 1);\n      return union(defaultDisable, nextDisable);\n    };\n    const disabledSeconds_ = (hour, minute, role, compare) => {\n      const defaultDisable = disabledSeconds ? disabledSeconds(hour, minute, role) : [];\n      const isStart = role === \"start\";\n      const compareDate = compare || (isStart ? endTime.value : startTime.value);\n      const compareHour = compareDate.hour();\n      const compareMinute = compareDate.minute();\n      if (hour !== compareHour || minute !== compareMinute) {\n        return defaultDisable;\n      }\n      const compareSecond = compareDate.second();\n      const nextDisable = isStart ? makeSelectRange(compareSecond + 1, 59) : makeSelectRange(0, compareSecond - 1);\n      return union(defaultDisable, nextDisable);\n    };\n    const getRangeAvailableTime = ([start, end]) => {\n      return [getAvailableTime(start, \"start\", true, end), getAvailableTime(end, \"end\", false, start)];\n    };\n    const {\n      getAvailableHours,\n      getAvailableMinutes,\n      getAvailableSeconds\n    } = buildAvailableTimeSlotGetter(disabledHours_, disabledMinutes_, disabledSeconds_);\n    const {\n      timePickerOptions,\n      getAvailableTime,\n      onSetOption\n    } = useTimePanel({\n      getAvailableHours,\n      getAvailableMinutes,\n      getAvailableSeconds\n    });\n    const parseUserInput = days => {\n      if (!days) return null;\n      if (isArray(days)) {\n        return days.map(d => dayjs(d, props.format).locale(lang.value));\n      }\n      return dayjs(days, props.format).locale(lang.value);\n    };\n    const formatToString = days => {\n      if (!days) return null;\n      if (isArray(days)) {\n        return days.map(d => d.format(props.format));\n      }\n      return days.format(props.format);\n    };\n    const getDefaultValue = () => {\n      if (isArray(defaultValue)) {\n        return defaultValue.map(d => dayjs(d).locale(lang.value));\n      }\n      const defaultDay = dayjs(defaultValue).locale(lang.value);\n      return [defaultDay, defaultDay.add(60, \"m\")];\n    };\n    emit(\"set-picker-option\", [\"formatToString\", formatToString]);\n    emit(\"set-picker-option\", [\"parseUserInput\", parseUserInput]);\n    emit(\"set-picker-option\", [\"isValidValue\", isValidValue]);\n    emit(\"set-picker-option\", [\"handleKeydownInput\", handleKeydown]);\n    emit(\"set-picker-option\", [\"getDefaultValue\", getDefaultValue]);\n    emit(\"set-picker-option\", [\"getRangeAvailableTime\", getRangeAvailableTime]);\n    return (_ctx, _cache) => {\n      return _ctx.actualVisible ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass([unref(nsTime).b(\"range-picker\"), unref(nsPicker).b(\"panel\")])\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(nsTime).be(\"range-picker\", \"content\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(nsTime).be(\"range-picker\", \"cell\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(nsTime).be(\"range-picker\", \"header\"))\n      }, toDisplayString(unref(t)(\"el.datepicker.startTime\")), 3), createElementVNode(\"div\", {\n        class: normalizeClass(unref(startContainerKls))\n      }, [createVNode(TimeSpinner, {\n        ref: \"minSpinner\",\n        role: \"start\",\n        \"show-seconds\": unref(showSeconds),\n        \"am-pm-mode\": unref(amPmMode),\n        \"arrow-control\": unref(arrowControl),\n        \"spinner-date\": unref(startTime),\n        \"disabled-hours\": disabledHours_,\n        \"disabled-minutes\": disabledMinutes_,\n        \"disabled-seconds\": disabledSeconds_,\n        onChange: handleMinChange,\n        onSetOption: unref(onSetOption),\n        onSelectRange: setMinSelectionRange\n      }, null, 8, [\"show-seconds\", \"am-pm-mode\", \"arrow-control\", \"spinner-date\", \"onSetOption\"])], 2)], 2), createElementVNode(\"div\", {\n        class: normalizeClass(unref(nsTime).be(\"range-picker\", \"cell\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(nsTime).be(\"range-picker\", \"header\"))\n      }, toDisplayString(unref(t)(\"el.datepicker.endTime\")), 3), createElementVNode(\"div\", {\n        class: normalizeClass(unref(endContainerKls))\n      }, [createVNode(TimeSpinner, {\n        ref: \"maxSpinner\",\n        role: \"end\",\n        \"show-seconds\": unref(showSeconds),\n        \"am-pm-mode\": unref(amPmMode),\n        \"arrow-control\": unref(arrowControl),\n        \"spinner-date\": unref(endTime),\n        \"disabled-hours\": disabledHours_,\n        \"disabled-minutes\": disabledMinutes_,\n        \"disabled-seconds\": disabledSeconds_,\n        onChange: handleMaxChange,\n        onSetOption: unref(onSetOption),\n        onSelectRange: setMaxSelectionRange\n      }, null, 8, [\"show-seconds\", \"am-pm-mode\", \"arrow-control\", \"spinner-date\", \"onSetOption\"])], 2)], 2)], 2), createElementVNode(\"div\", {\n        class: normalizeClass(unref(nsTime).be(\"panel\", \"footer\"))\n      }, [createElementVNode(\"button\", {\n        type: \"button\",\n        class: normalizeClass([unref(nsTime).be(\"panel\", \"btn\"), \"cancel\"]),\n        onClick: $event => handleCancel()\n      }, toDisplayString(unref(t)(\"el.datepicker.cancel\")), 11, [\"onClick\"]), createElementVNode(\"button\", {\n        type: \"button\",\n        class: normalizeClass([unref(nsTime).be(\"panel\", \"btn\"), \"confirm\"]),\n        disabled: unref(btnConfirmDisabled),\n        onClick: $event => handleConfirm()\n      }, toDisplayString(unref(t)(\"el.datepicker.confirm\")), 11, [\"disabled\", \"onClick\"])], 2)], 2)) : createCommentVNode(\"v-if\", true);\n    };\n  }\n});\nvar TimeRangePanel = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"panel-time-range.vue\"]]);\nexport { TimeRangePanel as default };", "map": {"version": 3, "names": ["makeSelectRange", "start", "end", "result", "i", "push", "t", "lang", "useLocale", "nsTime", "useNamespace", "nsPicker", "pickerBase", "inject", "arrowControl", "disabledHours", "disabledMinutes", "disabledSeconds", "defaultValue", "props", "startContainerKls", "computed", "be", "is", "showSeconds", "value", "endContainerKls", "startTime", "parsedValue", "endTime", "oldValue", "useOldValue", "handleCancel", "emit", "format", "includes", "amPmMode", "handleConfirm", "visible", "handleMinChange", "date", "handleChange", "millisecond", "handleMaxChange", "isValidValue", "_date", "parsedDate", "map", "_", "dayjs", "locale", "getRangeAvailableTime", "isSame", "btnConfirmDisabled", "<PERSON><PERSON><PERSON><PERSON>", "ref", "setMinSelectionRange", "offset", "setMaxSelectionRange", "_offset", "unref", "changeSelectionRange", "step", "list", "mapping", "concat", "index", "indexOf", "next", "length", "half", "timePickerOptions", "handleKeydown", "event", "code", "left", "right", "up", "down", "EVENT_CODE", "preventDefault", "role", "disabledHours_", "compare", "defaultDisable", "isStart", "compareDate", "compareHour", "hour", "nextDisable", "union", "disabledMinutes_", "compareMinute", "minute", "disabledSeconds_", "compareSecond", "second", "getAvailableTime", "getAvailableHours", "getAvailableMinutes", "getAvailableSeconds", "buildAvailableTimeSlotGetter", "onSetOption", "useTimePanel", "parseUserInput", "days", "isArray", "d", "formatToString", "getDefaultValue", "defaultDay", "add", "_ctx", "_cache", "actualVisible", "openBlock", "createElementBlock"], "sources": ["../../../../../../../packages/components/time-picker/src/time-picker-com/panel-time-range.vue"], "sourcesContent": ["<template>\n  <div\n    v-if=\"actualVisible\"\n    :class=\"[nsTime.b('range-picker'), nsPicker.b('panel')]\"\n  >\n    <div :class=\"nsTime.be('range-picker', 'content')\">\n      <div :class=\"nsTime.be('range-picker', 'cell')\">\n        <div :class=\"nsTime.be('range-picker', 'header')\">\n          {{ t('el.datepicker.startTime') }}\n        </div>\n        <div :class=\"startContainerKls\">\n          <time-spinner\n            ref=\"minSpinner\"\n            role=\"start\"\n            :show-seconds=\"showSeconds\"\n            :am-pm-mode=\"amPmMode\"\n            :arrow-control=\"arrowControl\"\n            :spinner-date=\"startTime\"\n            :disabled-hours=\"disabledHours_\"\n            :disabled-minutes=\"disabledMinutes_\"\n            :disabled-seconds=\"disabledSeconds_\"\n            @change=\"handleMinChange\"\n            @set-option=\"onSetOption\"\n            @select-range=\"setMinSelectionRange\"\n          />\n        </div>\n      </div>\n      <div :class=\"nsTime.be('range-picker', 'cell')\">\n        <div :class=\"nsTime.be('range-picker', 'header')\">\n          {{ t('el.datepicker.endTime') }}\n        </div>\n        <div :class=\"endContainerKls\">\n          <time-spinner\n            ref=\"maxSpinner\"\n            role=\"end\"\n            :show-seconds=\"showSeconds\"\n            :am-pm-mode=\"amPmMode\"\n            :arrow-control=\"arrowControl\"\n            :spinner-date=\"endTime\"\n            :disabled-hours=\"disabledHours_\"\n            :disabled-minutes=\"disabledMinutes_\"\n            :disabled-seconds=\"disabledSeconds_\"\n            @change=\"handleMaxChange\"\n            @set-option=\"onSetOption\"\n            @select-range=\"setMaxSelectionRange\"\n          />\n        </div>\n      </div>\n    </div>\n    <div :class=\"nsTime.be('panel', 'footer')\">\n      <button\n        type=\"button\"\n        :class=\"[nsTime.be('panel', 'btn'), 'cancel']\"\n        @click=\"handleCancel()\"\n      >\n        {{ t('el.datepicker.cancel') }}\n      </button>\n      <button\n        type=\"button\"\n        :class=\"[nsTime.be('panel', 'btn'), 'confirm']\"\n        :disabled=\"btnConfirmDisabled\"\n        @click=\"handleConfirm()\"\n      >\n        {{ t('el.datepicker.confirm') }}\n      </button>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref, unref } from 'vue'\nimport dayjs from 'dayjs'\nimport { union } from 'lodash-unified'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { isArray } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { panelTimeRangeProps } from '../props/panel-time-range'\nimport { useTimePanel } from '../composables/use-time-panel'\nimport {\n  buildAvailableTimeSlotGetter,\n  useOldValue,\n} from '../composables/use-time-picker'\nimport TimeSpinner from './basic-time-spinner.vue'\n\nimport type { Dayjs } from 'dayjs'\n\nconst props = defineProps(panelTimeRangeProps)\nconst emit = defineEmits(['pick', 'select-range', 'set-picker-option'])\n\nconst makeSelectRange = (start: number, end: number) => {\n  const result: number[] = []\n  for (let i = start; i <= end; i++) {\n    result.push(i)\n  }\n  return result\n}\n\nconst { t, lang } = useLocale()\nconst nsTime = useNamespace('time')\nconst nsPicker = useNamespace('picker')\nconst pickerBase = inject('EP_PICKER_BASE') as any\nconst {\n  arrowControl,\n  disabledHours,\n  disabledMinutes,\n  disabledSeconds,\n  defaultValue,\n} = pickerBase.props\n\nconst startContainerKls = computed(() => [\n  nsTime.be('range-picker', 'body'),\n  nsTime.be('panel', 'content'),\n  nsTime.is('arrow', arrowControl),\n  showSeconds.value ? 'has-seconds' : '',\n])\nconst endContainerKls = computed(() => [\n  nsTime.be('range-picker', 'body'),\n  nsTime.be('panel', 'content'),\n  nsTime.is('arrow', arrowControl),\n  showSeconds.value ? 'has-seconds' : '',\n])\n\nconst startTime = computed(() => props.parsedValue![0])\nconst endTime = computed(() => props.parsedValue![1])\nconst oldValue = useOldValue(props)\nconst handleCancel = () => {\n  emit('pick', oldValue.value, false)\n}\nconst showSeconds = computed(() => {\n  return props.format.includes('ss')\n})\nconst amPmMode = computed(() => {\n  if (props.format.includes('A')) return 'A'\n  if (props.format.includes('a')) return 'a'\n  return ''\n})\n\nconst handleConfirm = (visible = false) => {\n  emit('pick', [startTime.value, endTime.value], visible)\n}\n\nconst handleMinChange = (date: Dayjs) => {\n  handleChange(date.millisecond(0), endTime.value)\n}\nconst handleMaxChange = (date: Dayjs) => {\n  handleChange(startTime.value, date.millisecond(0))\n}\n\nconst isValidValue = (_date: Dayjs[]) => {\n  const parsedDate = _date.map((_) => dayjs(_).locale(lang.value))\n  const result = getRangeAvailableTime(parsedDate)\n  return parsedDate[0].isSame(result[0]) && parsedDate[1].isSame(result[1])\n}\n\nconst handleChange = (start: Dayjs, end: Dayjs) => {\n  if (!props.visible) {\n    return\n  }\n  // todo getRangeAvailableTime(_date).millisecond(0)\n  emit('pick', [start, end], true)\n}\nconst btnConfirmDisabled = computed(() => {\n  return startTime.value > endTime.value\n})\n\nconst selectionRange = ref([0, 2])\nconst setMinSelectionRange = (start: number, end: number) => {\n  emit('select-range', start, end, 'min')\n  selectionRange.value = [start, end]\n}\n\nconst offset = computed(() => (showSeconds.value ? 11 : 8))\nconst setMaxSelectionRange = (start: number, end: number) => {\n  emit('select-range', start, end, 'max')\n  const _offset = unref(offset)\n  selectionRange.value = [start + _offset, end + _offset]\n}\n\nconst changeSelectionRange = (step: number) => {\n  const list = showSeconds.value ? [0, 3, 6, 11, 14, 17] : [0, 3, 8, 11]\n  const mapping = ['hours', 'minutes'].concat(\n    showSeconds.value ? ['seconds'] : []\n  )\n  const index = list.indexOf(selectionRange.value[0])\n  const next = (index + step + list.length) % list.length\n  const half = list.length / 2\n  if (next < half) {\n    timePickerOptions['start_emitSelectRange'](mapping[next])\n  } else {\n    timePickerOptions['end_emitSelectRange'](mapping[next - half])\n  }\n}\n\nconst handleKeydown = (event: KeyboardEvent) => {\n  const code = event.code\n\n  const { left, right, up, down } = EVENT_CODE\n\n  if ([left, right].includes(code)) {\n    const step = code === left ? -1 : 1\n    changeSelectionRange(step)\n    event.preventDefault()\n    return\n  }\n\n  if ([up, down].includes(code)) {\n    const step = code === up ? -1 : 1\n    const role = selectionRange.value[0] < offset.value ? 'start' : 'end'\n    timePickerOptions[`${role}_scrollDown`](step)\n    event.preventDefault()\n    return\n  }\n}\n\nconst disabledHours_ = (role: string, compare?: Dayjs) => {\n  const defaultDisable = disabledHours ? disabledHours(role) : []\n  const isStart = role === 'start'\n  const compareDate = compare || (isStart ? endTime.value : startTime.value)\n  const compareHour = compareDate.hour()\n  const nextDisable = isStart\n    ? makeSelectRange(compareHour + 1, 23)\n    : makeSelectRange(0, compareHour - 1)\n  return union(defaultDisable, nextDisable)\n}\nconst disabledMinutes_ = (hour: number, role: string, compare?: Dayjs) => {\n  const defaultDisable = disabledMinutes ? disabledMinutes(hour, role) : []\n  const isStart = role === 'start'\n  const compareDate = compare || (isStart ? endTime.value : startTime.value)\n  const compareHour = compareDate.hour()\n  if (hour !== compareHour) {\n    return defaultDisable\n  }\n  const compareMinute = compareDate.minute()\n  const nextDisable = isStart\n    ? makeSelectRange(compareMinute + 1, 59)\n    : makeSelectRange(0, compareMinute - 1)\n  return union(defaultDisable, nextDisable)\n}\nconst disabledSeconds_ = (\n  hour: number,\n  minute: number,\n  role: string,\n  compare?: Dayjs\n) => {\n  const defaultDisable = disabledSeconds\n    ? disabledSeconds(hour, minute, role)\n    : []\n  const isStart = role === 'start'\n  const compareDate = compare || (isStart ? endTime.value : startTime.value)\n  const compareHour = compareDate.hour()\n  const compareMinute = compareDate.minute()\n  if (hour !== compareHour || minute !== compareMinute) {\n    return defaultDisable\n  }\n  const compareSecond = compareDate.second()\n  const nextDisable = isStart\n    ? makeSelectRange(compareSecond + 1, 59)\n    : makeSelectRange(0, compareSecond - 1)\n  return union(defaultDisable, nextDisable)\n}\n\nconst getRangeAvailableTime = ([start, end]: Array<Dayjs>) => {\n  return [\n    getAvailableTime(start, 'start', true, end),\n    getAvailableTime(end, 'end', false, start),\n  ] as const\n}\n\nconst { getAvailableHours, getAvailableMinutes, getAvailableSeconds } =\n  buildAvailableTimeSlotGetter(\n    disabledHours_,\n    disabledMinutes_,\n    disabledSeconds_\n  )\n\nconst {\n  timePickerOptions,\n\n  getAvailableTime,\n  onSetOption,\n} = useTimePanel({\n  getAvailableHours,\n  getAvailableMinutes,\n  getAvailableSeconds,\n})\n\nconst parseUserInput = (days: Dayjs[] | Dayjs) => {\n  if (!days) return null\n  if (isArray(days)) {\n    return days.map((d) => dayjs(d, props.format).locale(lang.value))\n  }\n  return dayjs(days, props.format).locale(lang.value)\n}\n\nconst formatToString = (days: Dayjs[] | Dayjs) => {\n  if (!days) return null\n  if (isArray(days)) {\n    return days.map((d) => d.format(props.format))\n  }\n  return days.format(props.format)\n}\n\nconst getDefaultValue = () => {\n  if (isArray(defaultValue)) {\n    return defaultValue.map((d: Date) => dayjs(d).locale(lang.value))\n  }\n  const defaultDay = dayjs(defaultValue).locale(lang.value)\n  return [defaultDay, defaultDay.add(60, 'm')]\n}\n\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['isValidValue', isValidValue])\nemit('set-picker-option', ['handleKeydownInput', handleKeydown])\nemit('set-picker-option', ['getDefaultValue', getDefaultValue])\nemit('set-picker-option', ['getRangeAvailableTime', getRangeAvailableTime])\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IAyFM,MAAAA,eAAA,GAAkBA,CAACC,KAAA,EAAeC,GAAgB;MACtD,MAAMC,MAAA,GAAmB,EAAC;MAC1B,SAASC,CAAI,GAAAH,KAAA,EAAOG,CAAK,IAAAF,GAAA,EAAKE,CAAK;QACjCD,MAAA,CAAOE,IAAA,CAAKD,CAAC;MAAA;MAER,OAAAD,MAAA;IAAA,CACT;IAEA,MAAM;MAAEG,CAAA;MAAGC;IAAK,IAAIC,SAAU;IACxB,MAAAC,MAAA,GAASC,YAAA,CAAa,MAAM;IAC5B,MAAAC,QAAA,GAAWD,YAAA,CAAa,QAAQ;IAChC,MAAAE,UAAA,GAAaC,MAAA,CAAO,gBAAgB;IACpC;MACJC,YAAA;MACAC,aAAA;MACAC,eAAA;MACAC,eAAA;MACAC;IAAA,IACEN,UAAW,CAAAO,KAAA;IAET,MAAAC,iBAAA,GAAoBC,QAAA,CAAS,MAAM,CACvCZ,MAAA,CAAOa,EAAG,iBAAgB,MAAM,GAChCb,MAAA,CAAOa,EAAG,UAAS,SAAS,GAC5Bb,MAAA,CAAOc,EAAG,UAAST,YAAY,GAC/BU,WAAA,CAAYC,KAAA,GAAQ,aAAgB,MACrC;IACK,MAAAC,eAAA,GAAkBL,QAAA,CAAS,MAAM,CACrCZ,MAAA,CAAOa,EAAG,iBAAgB,MAAM,GAChCb,MAAA,CAAOa,EAAG,UAAS,SAAS,GAC5Bb,MAAA,CAAOc,EAAG,UAAST,YAAY,GAC/BU,WAAA,CAAYC,KAAA,GAAQ,aAAgB,MACrC;IAED,MAAME,SAAA,GAAYN,QAAS,OAAMF,KAAM,CAAAS,WAAA,CAAa,CAAC,CAAC;IACtD,MAAMC,OAAA,GAAUR,QAAS,OAAMF,KAAM,CAAAS,WAAA,CAAa,CAAC,CAAC;IAC9C,MAAAE,QAAA,GAAWC,WAAA,CAAYZ,KAAK;IAClC,MAAMa,YAAA,GAAeA,CAAA,KAAM;MACpBC,IAAA,SAAQH,QAAS,CAAAL,KAAA,EAAO,KAAK;IAAA,CACpC;IACM,MAAAD,WAAA,GAAcH,QAAA,CAAS,MAAM;MAC1B,OAAAF,KAAA,CAAMe,MAAO,CAAAC,QAAA,CAAS,IAAI;IAAA,CAClC;IACK,MAAAC,QAAA,GAAWf,QAAA,CAAS,MAAM;MAC9B,IAAIF,KAAM,CAAAe,MAAA,CAAOC,QAAS,IAAG,GAC7B,OAAU;MACH,IAAAhB,KAAA,CAAAe,MAAA,CAAAC,QAAA,OACR;MAEK;IACJ;IACF,MAAAE,aAAA,GAAAA,CAAAC,OAAA;MAEML,IAAA,UAAAN,SAAmB,CAAgBF,KAAA,EAAAI,OAAA,CAAAJ,KAAA,GAAAa,OAAA;IACvC;IACF,MAAAC,eAAA,GAAAC,IAAA;MACMC,YAAA,CAAAD,IAAA,CAAAE,WAAmC,KAAAb,OAAA,CAAAJ,KAAA;IACvC;IACF,MAAAkB,eAAA,GAAAH,IAAA;MAEMC,YAAA,CAAAd,SAAmC,CAAAF,KAAA,EAAAe,IAAA,CAAAE,WAAA;IACvC,CAAM;IACA,MAAAE,YAAA,GAAAC,KAAA;MACN,MAAAC,UAAkB,GAACD,KAAE,CAAAE,GAAO,CAAAC,CAAA,IAAQC,KAAM,CAAAD,CAAA,EAAAE,MAAA,CAAA3C,IAAc,CAAOkB,KAAA;MACjE,MAAAtB,MAAA,GAAAgD,qBAAA,CAAAL,UAAA;MAEM,OAAAA,UAAA,EAAe,CAAC,CAAAM,MAAc,CAAejD,MAAA,QAAA2C,UAAA,IAAAM,MAAA,CAAAjD,MAAA;IACjD,CAAI;IACF,MAAAsC,YAAA,GAAAA,CAAAxC,KAAA,EAAAC,GAAA;MACF,KAAAiB,KAAA,CAAAmB,OAAA;QAEA;MAA+B;MAE3BL,IAAA,UAAAhC,KAAA,EAAAC,GAAA,CAAqB;IACzB,CAAO;IACT,MAACmD,kBAAA,GAAAhC,QAAA;MAED,OAAuBM,SAAA,CAAAF,KAAA,GAAII,OAAM,CAAAJ,KAAA;IACjC,CAAM;IACC,MAAA6B,cAAA,GAAgBC,GAAO;IACb,MAAAC,oBAAS,GAAAA,CAAAvD,KAAU,EAAAC,GAAA;MACpC+B,IAAA,iBAAAhC,KAAA,EAAAC,GAAA;MAEAoD,cAAwB,CAAA7B,KAAA,IAAAxB,KAAmB,EAAAC,GAAA;IAC3C,CAAM;IACC,MAAAuD,MAAA,GAAApC,QAAgB,CAAO,MAAAG,WAAU,CAAAC,KAAA;IAChC,MAAAiC,oBAAsB,GAAAA,CAAAzD,KAAA,EAAAC,GAAA;MAC5B+B,IAAA,eAAuB,EAAAhC,KAAS,EAAAC,GAAA;MAClC,MAAAyD,OAAA,GAAAC,KAAA,CAAAH,MAAA;MAEMH,cAAA,CAAA7B,KAAA,IAAAxB,KAAyC,GAAA0D,OAAA,EAAAzD,GAAA,GAAAyD,OAAA;IAC7C;IACA,MAAAE,oBAA0B,GAAAC,IAAA,IAAW;MAAA,MACvBC,IAAA,GAAAvC,WAAS,CAAAC,KAAA,OAAc;MACrC,MAAAuC,OAAA,wBAAAC,MAAA,CAAAzC,WAAA,CAAAC,KAAA;MACA,MAAMyC,KAAA,GAAQH,IAAK,CAAAI,OAAA,CAAQb,cAAe,CAAA7B,KAAA,CAAM,CAAC,CAAC;MAClD,MAAM2C,IAAQ,IAAAF,KAAA,GAAQJ,IAAO,GAAAC,IAAA,CAAKM,MAAA,IAAUN,IAAK,CAAAM,MAAA;MAC3C,MAAAC,IAAA,GAAOP,IAAA,CAAKM,MAAS;MAC3B,IAAID,IAAA,GAAOE,IAAM;QACfC,iBAAA,CAAkB,uBAAuB,EAAEP,OAAQ,CAAAI,IAAI,CAAC;MAAA,CACnD;QACLG,iBAAA,CAAkB,qBAAqB,EAAEP,OAAQ,CAAAI,IAAA,GAAOE,IAAI,CAAC;MAAA;IAC/D,CACF;IAEM,MAAAE,aAAA,GAAiBC,KAAyB;MAC9C,MAAMC,IAAA,GAAOD,KAAM,CAAAC,IAAA;MAEnB,MAAM;QAAEC,IAAA;QAAMC,KAAO;QAAAC,EAAA;QAAIC;MAAA,CAAS,GAAAC,UAAA;MAElC,IAAI,CAACJ,IAAM,EAAAC,KAAK,CAAE,CAAAzC,QAAA,CAASuC,IAAI,CAAG;QAC1B,MAAAZ,IAAA,GAAOY,IAAS,KAAAC,IAAA,GAAO,CAAK;QAClCd,oBAAA,CAAqBC,IAAI;QACzBW,KAAA,CAAMO,cAAe;QACrB;MAAA;MAGF,IAAI,CAACH,EAAI,EAAAC,IAAI,CAAE,CAAA3C,QAAA,CAASuC,IAAI,CAAG;QACvB,MAAAZ,IAAA,GAAOY,IAAS,KAAAG,EAAA,GAAK,CAAK;QAChC,MAAMI,IAAA,GAAO3B,cAAe,CAAA7B,KAAA,CAAM,CAAC,CAAI,GAAAgC,MAAA,CAAOhC,KAAA,GAAQ,OAAU;QAChE8C,iBAAA,CAAkB,GAAGU,IAAI,aAAa,EAAEnB,IAAI;QAC5CW,KAAA,CAAMO,cAAe;QACrB;MAAA;IACF,CACF;IAEM,MAAAE,cAAA,GAAiBA,CAACD,IAAA,EAAcE,OAAoB;MACxD,MAAMC,cAAiB,GAAArE,aAAA,GAAgBA,aAAc,CAAAkE,IAAI,IAAI,EAAC;MAC9D,MAAMI,OAAA,GAAUJ,IAAS;MACzB,MAAMK,WAAc,GAAAH,OAAA,KAAYE,OAAU,GAAAxD,OAAA,CAAQJ,KAAA,GAAQE,SAAU,CAAAF,KAAA;MAC9D,MAAA8D,WAAA,GAAcD,WAAA,CAAYE,IAAK;MAC/B,MAAAC,WAAA,GAAcJ,OAChB,GAAArF,eAAA,CAAgBuF,WAAc,MAAG,EAAE,CACnC,GAAAvF,eAAA,CAAgB,CAAG,EAAAuF,WAAA,GAAc,CAAC;MAC/B,OAAAG,KAAA,CAAMN,cAAA,EAAgBK,WAAW;IAAA,CAC1C;IACA,MAAME,gBAAmB,GAAAA,CAACH,IAAc,EAAAP,IAAA,EAAcE,OAAoB;MACxE,MAAMC,cAAA,GAAiBpE,eAAkB,GAAAA,eAAA,CAAgBwE,IAAM,EAAAP,IAAI,IAAI,EAAC;MACxE,MAAMI,OAAA,GAAUJ,IAAS;MACzB,MAAMK,WAAc,GAAAH,OAAA,KAAYE,OAAU,GAAAxD,OAAA,CAAQJ,KAAA,GAAQE,SAAU,CAAAF,KAAA;MAC9D,MAAA8D,WAAA,GAAcD,WAAA,CAAYE,IAAK;MACrC,IAAIA,IAAA,KAASD,WAAa;QACjB,OAAAH,cAAA;MAAA;MAEH,MAAAQ,aAAA,GAAgBN,WAAA,CAAYO,MAAO;MACnC,MAAAJ,WAAA,GAAcJ,OAChB,GAAArF,eAAA,CAAgB4F,aAAgB,MAAG,EAAE,CACrC,GAAA5F,eAAA,CAAgB,CAAG,EAAA4F,aAAA,GAAgB,CAAC;MACjC,OAAAF,KAAA,CAAMN,cAAA,EAAgBK,WAAW;IAAA,CAC1C;IACA,MAAMK,gBAAmB,GAAAA,CACvBN,IACA,EAAAK,MAAA,EACAZ,IAAA,EACAE,OACG;MACH,MAAMC,cAAA,GAAiBnE,eACnB,GAAAA,eAAA,CAAgBuE,IAAA,EAAMK,MAAQ,EAAAZ,IAAI,IAClC,EAAC;MACL,MAAMI,OAAA,GAAUJ,IAAS;MACzB,MAAMK,WAAc,GAAAH,OAAA,KAAYE,OAAU,GAAAxD,OAAA,CAAQJ,KAAA,GAAQE,SAAU,CAAAF,KAAA;MAC9D,MAAA8D,WAAA,GAAcD,WAAA,CAAYE,IAAK;MAC/B,MAAAI,aAAA,GAAgBN,WAAA,CAAYO,MAAO;MACrC,IAAAL,IAAA,KAASD,WAAe,IAAAM,MAAA,KAAWD,aAAe;QAC7C,OAAAR,cAAA;MAAA;MAEH,MAAAW,aAAA,GAAgBT,WAAA,CAAYU,MAAO;MACnC,MAAAP,WAAA,GAAcJ,OAChB,GAAArF,eAAA,CAAgB+F,aAAgB,MAAG,EAAE,CACrC,GAAA/F,eAAA,CAAgB,CAAG,EAAA+F,aAAA,GAAgB,CAAC;MACjC,OAAAL,KAAA,CAAMN,cAAA,EAAgBK,WAAW;IAAA,CAC1C;IAEA,MAAMtC,qBAAwB,GAAAA,CAAC,CAAClD,KAAA,EAAOC,GAAG,CAAoB;MACrD,QACL+F,gBAAiB,CAAAhG,KAAA,EAAO,OAAS,QAAMC,GAAG,GAC1C+F,gBAAiB,CAAA/F,GAAA,EAAK,KAAO,SAAOD,KAAK,EAC3C;IAAA,CACF;IAEA,MAAM;MAAEiG,iBAAA;MAAmBC,mBAAqB;MAAAC;IAAA,CAC9C,GAAAC,4BAAA,CAAAnB,cAAA,EAAAS,gBAAA,EAAAG,gBAAA;IACE;MACAvB,iBAAA;MACA0B,gBAAA;MACFK;IAEF,CAAM,GAAAC,YAAA;MACJL,iBAAA;MAEAC,mBAAA;MACAC;IAAA;IAEA,MAAAI,cAAA,GAAAC,IAAA;MACA,KAAAA,IAAA,EACA;MACD,IAAAC,OAAA,CAAAD,IAAA;QAEK,OAAAA,IAAA,CAAA1D,GAAA,CAAA4D,CAAA,IAA4C1D,KAAA,CAAA0D,CAAA,EAAAxF,KAAA,CAAAe,MAAA,EAAAgB,MAAA,CAAA3C,IAAA,CAAAkB,KAAA;MAChD;MACI,OAAAwB,KAAQ,CAAAwD,IAAO,EAAAtF,KAAA,CAAAe,MAAA,EAAAgB,MAAA,CAAA3C,IAAA,CAAAkB,KAAA;IACjB;IACF,MAAAmF,cAAA,GAAAH,IAAA;MACA,KAAAA,IAAA,EACF;MAEM,IAAAC,OAAA,CAAAD,IAAA;QACA,OAAAA,IAAc,CAAA1D,GAAA,CAAA4D,CAAA,IAAAA,CAAA,CAAAzE,MAAA,CAAAf,KAAA,CAAAe,MAAA;MAClB;MACS,OAAAuE,IAAA,CAAAvE,MAAS,CAACf,KAAA,CAAMe,MAAS;IAAa,CAC/C;IACO,MAAA2E,eAAY,GAAAA,CAAA,KAAY;MACjC,IAAAH,OAAA,CAAAxF,YAAA;QAEA,OAAAA,YAAA,CAAwB6B,GAAM,CAAA4D,CAAA,IAAA1D,KAAA,CAAA0D,CAAA,EAAAzD,MAAA,CAAA3C,IAAA,CAAAkB,KAAA;MAC5B;MACS,MAAAqF,UAAA,GAAA7D,KAAiB,CAAA/B,YAAa,EAAMgC,MAAG,CAAA3C,IAAY,CAAAkB,KAAA;MAC5D,QAAAqF,UAAA,EAAAA,UAAA,CAAAC,GAAA;IACA;IACA9E,IAAA,oBAAoB,qBAAuB2E,cAAA;IAC7C3E,IAAA,yCAAAuE,cAAA;IAEAvE,IAAA,CAAK,mBAAqB,GAAC,cAAkB,EAAAW,YAAA;IAC7CX,IAAA,CAAK,mBAAqB,GAAC,oBAAkB,EAAAuC,aAAe;IAC5DvC,IAAA,CAAK,mBAAqB,GAAC,iBAAgB,EAAA4E,eAAa;IACxD5E,IAAA,CAAK,mBAAqB,GAAC,uBAAsB,EAAAkB,qBAAc;IAC/D,OAA0B,CAAA6D,IAAA,EAAAC,MAAA;MAC1B,OAA0BD,IAAA,CAAAE,aAAC,IAAyBC,SAAA,IAAAC,kBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}