{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, ref, inject, computed, provide, readonly, toRef, unref, watch, renderSlot } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nimport { rovingFocusGroupProps, ROVING_FOCUS_COLLECTION_INJECTION_KEY as COLLECTION_INJECTION_KEY } from './roving-focus-group2.mjs';\nimport { ROVING_FOCUS_GROUP_INJECTION_KEY } from './tokens.mjs';\nimport { focusFirst } from './utils.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nconst CURRENT_TAB_ID_CHANGE_EVT = \"currentTabIdChange\";\nconst ENTRY_FOCUS_EVT = \"rovingFocusGroup.entryFocus\";\nconst EVT_OPTS = {\n  bubbles: false,\n  cancelable: true\n};\nconst _sfc_main = defineComponent({\n  name: \"ElRovingFocusGroupImpl\",\n  inheritAttrs: false,\n  props: rovingFocusGroupProps,\n  emits: [CURRENT_TAB_ID_CHANGE_EVT, \"entryFocus\"],\n  setup(props, {\n    emit\n  }) {\n    var _a;\n    const currentTabbedId = ref((_a = props.currentTabId || props.defaultCurrentTabId) != null ? _a : null);\n    const isBackingOut = ref(false);\n    const isClickFocus = ref(false);\n    const rovingFocusGroupRef = ref();\n    const {\n      getItems\n    } = inject(COLLECTION_INJECTION_KEY, void 0);\n    const rovingFocusGroupRootStyle = computed(() => {\n      return [{\n        outline: \"none\"\n      }, props.style];\n    });\n    const onItemFocus = tabbedId => {\n      emit(CURRENT_TAB_ID_CHANGE_EVT, tabbedId);\n    };\n    const onItemShiftTab = () => {\n      isBackingOut.value = true;\n    };\n    const onMousedown = composeEventHandlers(e => {\n      var _a2;\n      (_a2 = props.onMousedown) == null ? void 0 : _a2.call(props, e);\n    }, () => {\n      isClickFocus.value = true;\n    });\n    const onFocus = composeEventHandlers(e => {\n      var _a2;\n      (_a2 = props.onFocus) == null ? void 0 : _a2.call(props, e);\n    }, e => {\n      const isKeyboardFocus = !unref(isClickFocus);\n      const {\n        target,\n        currentTarget\n      } = e;\n      if (target === currentTarget && isKeyboardFocus && !unref(isBackingOut)) {\n        const entryFocusEvt = new Event(ENTRY_FOCUS_EVT, EVT_OPTS);\n        currentTarget == null ? void 0 : currentTarget.dispatchEvent(entryFocusEvt);\n        if (!entryFocusEvt.defaultPrevented) {\n          const items = getItems().filter(item => item.focusable);\n          const activeItem = items.find(item => item.active);\n          const currentItem = items.find(item => item.id === unref(currentTabbedId));\n          const candidates = [activeItem, currentItem, ...items].filter(Boolean);\n          const candidateNodes = candidates.map(item => item.ref);\n          focusFirst(candidateNodes);\n        }\n      }\n      isClickFocus.value = false;\n    });\n    const onBlur = composeEventHandlers(e => {\n      var _a2;\n      (_a2 = props.onBlur) == null ? void 0 : _a2.call(props, e);\n    }, () => {\n      isBackingOut.value = false;\n    });\n    const handleEntryFocus = (...args) => {\n      emit(\"entryFocus\", ...args);\n    };\n    provide(ROVING_FOCUS_GROUP_INJECTION_KEY, {\n      currentTabbedId: readonly(currentTabbedId),\n      loop: toRef(props, \"loop\"),\n      tabIndex: computed(() => {\n        return unref(isBackingOut) ? -1 : 0;\n      }),\n      rovingFocusGroupRef,\n      rovingFocusGroupRootStyle,\n      orientation: toRef(props, \"orientation\"),\n      dir: toRef(props, \"dir\"),\n      onItemFocus,\n      onItemShiftTab,\n      onBlur,\n      onFocus,\n      onMousedown\n    });\n    watch(() => props.currentTabId, val => {\n      currentTabbedId.value = val != null ? val : null;\n    });\n    useEventListener(rovingFocusGroupRef, ENTRY_FOCUS_EVT, handleEntryFocus);\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return renderSlot(_ctx.$slots, \"default\");\n}\nvar ElRovingFocusGroupImpl = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"roving-focus-group-impl.vue\"]]);\nexport { ElRovingFocusGroupImpl as default };", "map": {"version": 3, "names": ["CURRENT_TAB_ID_CHANGE_EVT", "ENTRY_FOCUS_EVT", "EVT_OPTS", "bubbles", "cancelable", "_sfc_main", "defineComponent", "name", "inheritAttrs", "props", "rovingFocusGroupProps", "emits", "setup", "emit", "_a", "currentTabbedId", "ref", "currentTabId", "defaultCurrentTabId", "isBackingOut", "isClickFocus", "rovingFocusGroupRef", "getItems", "inject", "COLLECTION_INJECTION_KEY", "rovingFocusGroupRootStyle", "computed", "outline", "style", "onItemFocus", "tabbedId", "onItemShiftTab", "value", "onMousedown", "composeEventHandlers", "e", "_a2", "call", "onFocus", "isKeyboardFocus", "unref", "target", "currentTarget", "entryFocusEvt", "Event", "dispatchEvent", "defaultPrevented", "items", "filter", "item", "focusable", "activeItem", "find", "active", "currentItem", "id", "candidates", "Boolean", "candidateNodes", "map", "focusFirst", "onBlur", "handleEntryFocus", "args", "provide", "ROVING_FOCUS_GROUP_INJECTION_KEY", "readonly", "loop", "toRef", "tabIndex", "orientation", "dir", "watch", "val", "useEventListener", "_sfc_render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "renderSlot", "$slots", "ElRovingFocusGroupImpl", "_export_sfc"], "sources": ["../../../../../../packages/components/roving-focus-group/src/roving-focus-group-impl.vue"], "sourcesContent": ["<template>\n  <slot />\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  inject,\n  provide,\n  readonly,\n  ref,\n  toRef,\n  unref,\n  watch,\n} from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { composeEventHandlers } from '@element-plus/utils'\nimport {\n  ROVING_FOCUS_COLLECTION_INJECTION_KEY,\n  rovingFocusGroupProps,\n} from './roving-focus-group'\nimport { ROVING_FOCUS_GROUP_INJECTION_KEY } from './tokens'\nimport { focusFirst } from './utils'\n\nimport type { StyleValue } from 'vue'\n\nconst CURRENT_TAB_ID_CHANGE_EVT = 'currentTabIdChange'\n\nconst ENTRY_FOCUS_EVT = 'rovingFocusGroup.entryFocus'\nconst EVT_OPTS: EventInit = { bubbles: false, cancelable: true }\nexport default defineComponent({\n  name: 'ElRovingFocusGroupImpl',\n  inheritAttrs: false,\n  props: rovingFocusGroupProps,\n  emits: [CURRENT_TAB_ID_CHANGE_EVT, 'entryFocus'],\n  setup(props, { emit }) {\n    const currentTabbedId = ref<string | null>(\n      (props.currentTabId || props.defaultCurrentTabId) ?? null\n    )\n    const isBackingOut = ref(false)\n    const isClickFocus = ref(false)\n    const rovingFocusGroupRef = ref<HTMLElement>()\n    const { getItems } = inject(\n      ROVING_FOCUS_COLLECTION_INJECTION_KEY,\n      undefined\n    )!\n    const rovingFocusGroupRootStyle = computed(() => {\n      // casting to any for fix compiler error since HTMLElement.StyleValue does not\n      // support CSSProperties\n      return [\n        {\n          outline: 'none',\n        },\n        props.style as StyleValue,\n      ] as any\n    })\n\n    const onItemFocus = (tabbedId: string) => {\n      emit(CURRENT_TAB_ID_CHANGE_EVT, tabbedId)\n    }\n\n    const onItemShiftTab = () => {\n      isBackingOut.value = true\n    }\n\n    const onMousedown = composeEventHandlers(\n      (e: Event) => {\n        props.onMousedown?.(e)\n      },\n      () => {\n        isClickFocus.value = true\n      }\n    )\n\n    const onFocus = composeEventHandlers(\n      (e: FocusEvent) => {\n        props.onFocus?.(e)\n      },\n      (e) => {\n        const isKeyboardFocus = !unref(isClickFocus)\n        const { target, currentTarget } = e\n        if (\n          target === currentTarget &&\n          isKeyboardFocus &&\n          !unref(isBackingOut)\n        ) {\n          const entryFocusEvt = new Event(ENTRY_FOCUS_EVT, EVT_OPTS)\n          currentTarget?.dispatchEvent(entryFocusEvt)\n\n          if (!entryFocusEvt.defaultPrevented) {\n            const items = getItems<{\n              id: string\n              focusable: boolean\n              active: boolean\n            }>().filter((item) => item.focusable)\n            const activeItem = items.find((item) => item.active)\n            const currentItem = items.find(\n              (item) => item.id === unref(currentTabbedId)\n            )\n            const candidates = [activeItem!, currentItem!, ...items].filter(\n              Boolean\n            )\n            const candidateNodes = candidates.map((item) => item.ref!)\n            focusFirst(candidateNodes)\n          }\n        }\n\n        isClickFocus.value = false\n      }\n    )\n\n    const onBlur = composeEventHandlers(\n      (e: Event) => {\n        props.onBlur?.(e)\n      },\n      () => {\n        isBackingOut.value = false\n      }\n    )\n\n    const handleEntryFocus = (...args: any[]) => {\n      emit('entryFocus', ...args)\n    }\n\n    provide(ROVING_FOCUS_GROUP_INJECTION_KEY, {\n      currentTabbedId: readonly(currentTabbedId),\n      loop: toRef(props, 'loop'),\n      tabIndex: computed(() => {\n        return unref(isBackingOut) ? -1 : 0\n      }),\n      rovingFocusGroupRef,\n      rovingFocusGroupRootStyle,\n      orientation: toRef(props, 'orientation'),\n      dir: toRef(props, 'dir'),\n      onItemFocus,\n      onItemShiftTab,\n      onBlur,\n      onFocus,\n      onMousedown,\n    })\n\n    watch(\n      () => props.currentTabId,\n      (val) => {\n        currentTabbedId.value = val ?? null\n      }\n    )\n\n    useEventListener(rovingFocusGroupRef, ENTRY_FOCUS_EVT, handleEntryFocus)\n  },\n})\n</script>\n"], "mappings": ";;;;;;;;;;;AA2BA,MAAMA,yBAA4B;AAElC,MAAMC,eAAkB;AACxB,MAAMC,QAAsB;EAAEC,OAAS;EAAOC,UAAA,EAAY;AAAK;AAC/D,MAAKC,SAAA,GAAaC,eAAa;EAC7BC,IAAM;EACNC,YAAc;EACdC,KAAO,EAAAC,qBAAA;EACPC,KAAA,EAAO,CAACX,yBAAA,EAA2B,YAAY;EAC/CY,KAAMA,CAAAH,KAAA,EAAO;IAAEI;EAAA,CAAQ;IACrB,IAAAC,EAAM;IACH,MAAAC,eAAsB,GAAAC,GAAA,EAAAF,EAAM,GAAwBL,KAAA,CAAAQ,YAAA,IAAAR,KAAA,CAAAS,mBAAA,YAAAJ,EAAA;IACvD,MAAAK,YAAA,GAAAH,GAAA;IACM,MAAAI,YAAA,GAAeJ,GAAA,CAAI,KAAK;IACxB,MAAAK,mBAAmB,GAAKL,GAAA;IAC9B,MAAM;MAAAM;IAAA,IAAAC,MAAA,CAAsBC,wBAAiB;IACvC,MAAAC,yBAAe,GAAAC,QAAA;MACnB,QACA;QACFC,OAAA;MACA,CAAM,EAGGlB,KAAA,CAAAmB,KAAA,CACL;IAAA;IAEA,MAAAC,WAAA,GAAAC,QAAA;MAAAjB,IACM,CAAAb,yBAAA,EAAA8B,QAAA;IAAA,CACR;IACF,MAACC,cAAA,GAAAA,CAAA;MAEKZ,YAAA,CAAAa,KAAc,GAAsB;IACxC;IACF,MAAAC,WAAA,GAAAC,oBAAA,CAAAC,CAAA;MAEA,IAAMC,GAAA;MACJ,CAAAA,GAAA,GAAA3B,KAAA,CAAawB,WAAQ,qBAAAG,GAAA,CAAAC,IAAA,CAAA5B,KAAA,EAAA0B,CAAA;IAAA,CACvB;MAEAf,YAAoB,CAAAY,KAAA;IAAA,EAClB;IACE,MAAAM,OAAA,GAAAJ,oBAAqB,CAAAC,CAAA;MACvB,IAAAC,GAAA;MACA,CAAMA,GAAA,GAAA3B,KAAA,CAAA6B,OAAA,qBAAAF,GAAA,CAAAC,IAAA,CAAA5B,KAAA,EAAA0B,CAAA;IACJ,GAAAA,CAAA;MACF,MAAAI,eAAA,IAAAC,KAAA,CAAApB,YAAA;MACF;QAAAqB,MAAA;QAAAC;MAAA,IAAAP,CAAA;MAEA,IAAMM,MAAU,KAAAC,aAAA,IAAAH,eAAA,KAAAC,KAAA,CAAArB,YAAA;QACK,MAAAwB,aAAA,OAAAC,KAAA,CAAA3C,eAAA,EAAAC,QAAA;QACjBwC,aAAA,IAAiB,gBAAAA,aAAA,CAAAG,aAAA,CAAAF,aAAA;QACnB,KAAAA,aAAA,CAAAG,gBAAA;UACO,MAAAC,KAAA,GAAAzB,QAAA,GAAA0B,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,SAAA;UACC,MAAAC,UAAA,GAAAJ,KAAmB,CAAAK,IAAM,CAAYH,IAAA,IAAAA,IAAA,CAAAI,MAAA;UACrC,MAAEC,WAAQ,GAAAP,KAAA,CAAAK,IAAkB,CAAAH,IAAA,IAAAA,IAAA,CAAAM,EAAA,KAAAf,KAAA,CAAAzB,eAAA;UAClC,MAAAyC,UAEE,IAAAL,UAAA,EAAAG,WAAA,KAAAP,KACC,EAAAC,MAAA,CAAAS,OACD;UACA,MAAMC,cAAgB,GAAAF,UAAU,CAAAG,GAAA,CAAAV,IAAA,IAAAA,IAAyB,CAAAjC,GAAA;UACzD4C,UAAA,CAAAF,cAAA;QAEA;MACE;MAKAtC,YAAM,CAAAY,KAAA,QAAmB;IACzB;IAA0B,MAAA6B,MACvB,GAAA3B,oBAAA,CAAAC,CAA2B,IAAe;MAC7C,IAAAC,GAAA;MACA,CAAAA,GAAA,GAAA3B,KAAM,CAAAoD,MAAA,SAAc,YAA2BzB,GAAA,CAAAC,IAAA,CAAA5B,KAAG,EAAA0B,CAAA;IAAO,CACvD;MACFhB,YAAA,CAAAa,KAAA;IACA;IACA,MAAA8B,gBAAyB,GAAAA,CAAA,GAAAC,IAAA;MAC3BlD,IAAA,kBAAAkD,IAAA;IAAA,CACF;IAEAC,OAAA,CAAAC,gCAAqB;MACvBlD,eAAA,EAAAmD,QAAA,CAAAnD,eAAA;MACFoD,IAAA,EAAAC,KAAA,CAAA3D,KAAA;MAEA4D,QAAe,EAAA3C,QAAA;QACC,OAAAc,KAAA,CAAArB,YAAA;MACZ;MACFE,mBAAA;MACAI,yBAAM;MACJ6C,WAAA,EAAAF,KAAqB,CAAA3D,KAAA;MACvB8D,GAAA,EAAAH,KAAA,CAAA3D,KAAA;MACFoB,WAAA;MAEME,cAAA;MACC8B,MAAA;MACPvB,OAAA;MAEAL;IAA0C,CACxC;IACAuC,KAAA,OAAY/D,KAAA,CAAAQ,YAAa,EAAAwD,GAAA;MACzB1D,eAAA,CAAAiB,KAAyB,GAAAyC,GAAA,WAAAA,GAAA;IACvB,CAAO;IAA2BC,gBACnC,CAAArD,mBAAA,EAAApB,eAAA,EAAA6D,gBAAA;EAAA;AACD,CACA;AACuC,SACvCa,WAAWA,CAAAC,IAAY,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,QAAA;EACvB,OAAAC,UAAA,CAAAN,IAAA,CAAAO,MAAA;AAAA;AAEA,IAAAC,sBAAA,GACA,eAAAC,WAAA,CAAAhF,SAAA,cAAAsE,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}