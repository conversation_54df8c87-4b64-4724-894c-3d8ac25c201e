{"ast": null, "code": "import { defineComponent, onBeforeUnmount, openBlock, createElementBlock, unref, normalizeClass, withModifiers, createElementVNode, toDisplayString, createCommentVNode, Fragment, renderList, createVNode } from 'vue';\nimport { basicDateTableProps, basicDateTableEmits } from '../props/basic-date-table.mjs';\nimport { useBasicDateTable, useBasicDateTableDOM } from '../composables/use-basic-date-table.mjs';\nimport ElDatePickerCell from './basic-cell-render.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"basic-date-table\",\n  props: basicDateTableProps,\n  emits: basicDateTableEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      WEEKS,\n      rows,\n      tbodyRef,\n      currentCellRef,\n      focus,\n      isCurrent,\n      isWeekActive,\n      isSelectedCell,\n      handlePickDate,\n      handleMouseUp,\n      handleMouseDown,\n      handleMouseMove,\n      handleFocus\n    } = useBasicDateTable(props, emit);\n    const {\n      tableLabel,\n      tableKls,\n      weekLabel,\n      getCellClasses,\n      getRowKls,\n      t\n    } = useBasicDateTableDOM(props, {\n      isCurrent,\n      isWeekActive\n    });\n    let isUnmounting = false;\n    onBeforeUnmount(() => {\n      isUnmounting = true;\n    });\n    expose({\n      focus\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"table\", {\n        \"aria-label\": unref(tableLabel),\n        class: normalizeClass(unref(tableKls)),\n        cellspacing: \"0\",\n        cellpadding: \"0\",\n        role: \"grid\",\n        onClick: unref(handlePickDate),\n        onMousemove: unref(handleMouseMove),\n        onMousedown: withModifiers(unref(handleMouseDown), [\"prevent\"]),\n        onMouseup: unref(handleMouseUp)\n      }, [createElementVNode(\"tbody\", {\n        ref_key: \"tbodyRef\",\n        ref: tbodyRef\n      }, [createElementVNode(\"tr\", null, [_ctx.showWeekNumber ? (openBlock(), createElementBlock(\"th\", {\n        key: 0,\n        scope: \"col\"\n      }, toDisplayString(unref(weekLabel)), 1)) : createCommentVNode(\"v-if\", true), (openBlock(true), createElementBlock(Fragment, null, renderList(unref(WEEKS), (week, key) => {\n        return openBlock(), createElementBlock(\"th\", {\n          key,\n          \"aria-label\": unref(t)(\"el.datepicker.weeksFull.\" + week),\n          scope: \"col\"\n        }, toDisplayString(unref(t)(\"el.datepicker.weeks.\" + week)), 9, [\"aria-label\"]);\n      }), 128))]), (openBlock(true), createElementBlock(Fragment, null, renderList(unref(rows), (row, rowKey) => {\n        return openBlock(), createElementBlock(\"tr\", {\n          key: rowKey,\n          class: normalizeClass(unref(getRowKls)(row[1]))\n        }, [(openBlock(true), createElementBlock(Fragment, null, renderList(row, (cell, columnKey) => {\n          return openBlock(), createElementBlock(\"td\", {\n            key: `${rowKey}.${columnKey}`,\n            ref_for: true,\n            ref: el => !unref(isUnmounting) && unref(isSelectedCell)(cell) && (currentCellRef.value = el),\n            class: normalizeClass(unref(getCellClasses)(cell)),\n            \"aria-current\": cell.isCurrent ? \"date\" : void 0,\n            \"aria-selected\": cell.isCurrent,\n            tabindex: unref(isSelectedCell)(cell) ? 0 : -1,\n            onFocus: unref(handleFocus)\n          }, [createVNode(unref(ElDatePickerCell), {\n            cell\n          }, null, 8, [\"cell\"])], 42, [\"aria-current\", \"aria-selected\", \"tabindex\", \"onFocus\"]);\n        }), 128))], 2);\n      }), 128))], 512)], 42, [\"aria-label\", \"onClick\", \"onMousemove\", \"onMousedown\", \"onMouseup\"]);\n    };\n  }\n});\nvar DateTable = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"basic-date-table.vue\"]]);\nexport { DateTable as default };", "map": {"version": 3, "names": ["WEEKS", "rows", "tbodyRef", "currentCellRef", "focus", "isCurrent", "isWeekActive", "isSelectedCell", "handlePickDate", "handleMouseUp", "handleMouseDown", "handleMouseMove", "handleFocus", "useBasicDateTable", "props", "emit", "tableLabel", "tableKls", "week<PERSON><PERSON><PERSON>", "getCellClasses", "getRowKls", "t", "useBasicDateTableDOM", "isUnmounting", "onBeforeUnmount", "expose", "_ctx", "_cache", "openBlock", "createElementBlock", "unref"], "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/basic-date-table.vue"], "sourcesContent": ["<template>\n  <table\n    :aria-label=\"tableLabel\"\n    :class=\"tableKls\"\n    cellspacing=\"0\"\n    cellpadding=\"0\"\n    role=\"grid\"\n    @click=\"handlePickDate\"\n    @mousemove=\"handleMouseMove\"\n    @mousedown.prevent=\"handleMouseDown\"\n    @mouseup=\"handleMouseUp\"\n  >\n    <tbody ref=\"tbodyRef\">\n      <tr>\n        <th v-if=\"showWeekNumber\" scope=\"col\">{{ weekLabel }}</th>\n        <th\n          v-for=\"(week, key) in WEEKS\"\n          :key=\"key\"\n          :aria-label=\"t('el.datepicker.weeksFull.' + week)\"\n          scope=\"col\"\n        >\n          {{ t('el.datepicker.weeks.' + week) }}\n        </th>\n      </tr>\n      <tr\n        v-for=\"(row, rowKey) in rows\"\n        :key=\"rowKey\"\n        :class=\"getRowKls(row[1])\"\n      >\n        <td\n          v-for=\"(cell, columnKey) in row\"\n          :key=\"`${rowKey}.${columnKey}`\"\n          :ref=\"(el) => !isUnmounting && isSelectedCell(cell) && (currentCellRef = el as HTMLElement)\"\n          :class=\"getCellClasses(cell)\"\n          :aria-current=\"cell.isCurrent ? 'date' : undefined\"\n          :aria-selected=\"cell.isCurrent\"\n          :tabindex=\"isSelectedCell(cell) ? 0 : -1\"\n          @focus=\"handleFocus\"\n        >\n          <el-date-picker-cell :cell=\"cell\" />\n        </td>\n      </tr>\n    </tbody>\n  </table>\n</template>\n\n<script lang=\"ts\" setup>\nimport { onBeforeUnmount } from 'vue'\nimport {\n  basicDateTableEmits,\n  basicDateTableProps,\n} from '../props/basic-date-table'\nimport {\n  useBasicDateTable,\n  useBasicDateTableDOM,\n} from '../composables/use-basic-date-table'\nimport ElDatePickerCell from './basic-cell-render'\n\nconst props = defineProps(basicDateTableProps)\nconst emit = defineEmits(basicDateTableEmits)\n\nconst {\n  WEEKS,\n  rows,\n  tbodyRef,\n  currentCellRef,\n\n  focus,\n  isCurrent,\n  isWeekActive,\n  isSelectedCell,\n\n  handlePickDate,\n  handleMouseUp,\n  handleMouseDown,\n  handleMouseMove,\n  handleFocus,\n} = useBasicDateTable(props, emit)\nconst { tableLabel, tableKls, weekLabel, getCellClasses, getRowKls, t } =\n  useBasicDateTableDOM(props, {\n    isCurrent,\n    isWeekActive,\n  })\nlet isUnmounting = false\n\nonBeforeUnmount(() => {\n  isUnmounting = true\n})\n\ndefineExpose({\n  /**\n   * @description focus on current cell\n   */\n  focus,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;IA6DM;MACJA,KAAA;MACAC,IAAA;MACAC,QAAA;MACAC,cAAA;MAEAC,KAAA;MACAC,SAAA;MACAC,YAAA;MACAC,cAAA;MAEAC,cAAA;MACAC,aAAA;MACAC,eAAA;MACAC,eAAA;MACAC;IAAA,CACF,GAAIC,iBAAkB,CAAAC,KAAA,EAAOC,IAAI;IAC3B;MAAEC,UAAA;MAAYC,QAAU;MAAAC,SAAA;MAAWC,cAAA;MAAgBC,SAAW;MAAAC;IAAA,CAClE,GAAAC,oBAAA,CAAqBR,KAAO;MAC1BT,SAAA;MACAC;IAAA,CACD;IACH,IAAIiB,YAAe;IAEnBC,eAAA,CAAgB,MAAM;MACLD,YAAA;IAAA,CAChB;IAEYE,MAAA;MAAArB;IAAA;IAAA,QAAAsB,IAAA,EAAAC,MAAA;MAIX,OAAAC,SAAA,IAAAC,kBAAA;QACD,cAAAC,KAAA,CAAAd,UAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}