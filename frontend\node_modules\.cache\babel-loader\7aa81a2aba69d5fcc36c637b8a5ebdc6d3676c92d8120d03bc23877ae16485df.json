{"ast": null, "code": "import { defineComponent, computed, provide, reactive, toRef, ref, createVNode, mergeProps } from 'vue';\nimport dayjs from 'dayjs';\nimport customParseFormat from 'dayjs/plugin/customParseFormat.js';\nimport advancedFormat from 'dayjs/plugin/advancedFormat.js';\nimport localeData from 'dayjs/plugin/localeData.js';\nimport weekOfYear from 'dayjs/plugin/weekOfYear.js';\nimport weekYear from 'dayjs/plugin/weekYear.js';\nimport dayOfYear from 'dayjs/plugin/dayOfYear.js';\nimport isSameOrAfter from 'dayjs/plugin/isSameOrAfter.js';\nimport isSameOrBefore from 'dayjs/plugin/isSameOrBefore.js';\nimport '../../time-picker/index.mjs';\nimport { ROOT_PICKER_INJECTION_KEY } from './constants.mjs';\nimport { datePickerProps } from './props/date-picker.mjs';\nimport { getPanel } from './panel-utils.mjs';\nimport { DEFAULT_FORMATS_DATEPICKER, DEFAULT_FORMATS_DATE } from '../../time-picker/src/constants.mjs';\nimport CommonPicker from '../../time-picker/src/common/picker.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\ndayjs.extend(localeData);\ndayjs.extend(advancedFormat);\ndayjs.extend(customParseFormat);\ndayjs.extend(weekOfYear);\ndayjs.extend(weekYear);\ndayjs.extend(dayOfYear);\ndayjs.extend(isSameOrAfter);\ndayjs.extend(isSameOrBefore);\nvar DatePicker = defineComponent({\n  name: \"ElDatePicker\",\n  install: null,\n  props: datePickerProps,\n  emits: [UPDATE_MODEL_EVENT],\n  setup(props, {\n    expose,\n    emit,\n    slots\n  }) {\n    const ns = useNamespace(\"picker-panel\");\n    const isDefaultFormat = computed(() => {\n      return !props.format;\n    });\n    provide(\"ElIsDefaultFormat\", isDefaultFormat);\n    provide(\"ElPopperOptions\", reactive(toRef(props, \"popperOptions\")));\n    provide(ROOT_PICKER_INJECTION_KEY, {\n      slots,\n      pickerNs: ns\n    });\n    const commonPicker = ref();\n    const refProps = {\n      focus: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.focus();\n      },\n      blur: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.blur();\n      },\n      handleOpen: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.handleOpen();\n      },\n      handleClose: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.handleClose();\n      }\n    };\n    expose(refProps);\n    const onModelValueUpdated = val => {\n      emit(UPDATE_MODEL_EVENT, val);\n    };\n    return () => {\n      var _a;\n      const format = (_a = props.format) != null ? _a : DEFAULT_FORMATS_DATEPICKER[props.type] || DEFAULT_FORMATS_DATE;\n      const Component = getPanel(props.type);\n      return createVNode(CommonPicker, mergeProps(props, {\n        \"format\": format,\n        \"type\": props.type,\n        \"ref\": commonPicker,\n        \"onUpdate:modelValue\": onModelValueUpdated\n      }), {\n        default: scopedProps => createVNode(Component, scopedProps, {\n          \"prev-month\": slots[\"prev-month\"],\n          \"next-month\": slots[\"next-month\"],\n          \"prev-year\": slots[\"prev-year\"],\n          \"next-year\": slots[\"next-year\"]\n        }),\n        \"range-separator\": slots[\"range-separator\"]\n      });\n    };\n  }\n});\nexport { DatePicker as default };", "map": {"version": 3, "names": ["dayjs", "extend", "localeData", "advancedFormat", "customParseFormat", "weekOfYear", "weekYear", "dayOfYear", "isSameOrAfter", "isSameOrBefore", "DatePicker", "defineComponent", "name", "install", "props", "datePickerProps", "emits", "UPDATE_MODEL_EVENT", "expose", "emit", "slots", "ns", "useNamespace", "isDefaultFormat", "computed", "format", "provide", "reactive", "toRef", "ROOT_PICKER_INJECTION_KEY", "pickerNs", "commonPicker", "ref", "refProps", "focus", "_a", "value", "blur", "handleOpen", "handleClose", "onModelValueUpdated", "val", "DEFAULT_FORMATS_DATEPICKER", "type", "DEFAULT_FORMATS_DATE", "Component", "getPanel", "createVNode", "CommonPicker", "mergeProps", "default", "scopedProps"], "sources": ["../../../../../../packages/components/date-picker/src/date-picker.tsx"], "sourcesContent": ["import { computed, defineComponent, provide, reactive, ref, toRef } from 'vue'\nimport dayjs from 'dayjs'\nimport customParseFormat from 'dayjs/plugin/customParseFormat.js'\nimport advancedFormat from 'dayjs/plugin/advancedFormat.js'\nimport localeData from 'dayjs/plugin/localeData.js'\nimport weekOfYear from 'dayjs/plugin/weekOfYear.js'\nimport weekYear from 'dayjs/plugin/weekYear.js'\nimport dayOfYear from 'dayjs/plugin/dayOfYear.js'\nimport isSameOrAfter from 'dayjs/plugin/isSameOrAfter.js'\nimport isSameOrBefore from 'dayjs/plugin/isSameOrBefore.js'\nimport { useNamespace } from '@element-plus/hooks'\nimport {\n  CommonPicker,\n  DEFAULT_FORMATS_DATE,\n  DEFAULT_FORMATS_DATEPICKER,\n  type DateModelType,\n  type SingleOrRange,\n} from '@element-plus/components/time-picker'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { ROOT_PICKER_INJECTION_KEY } from './constants'\n\nimport { datePickerProps } from './props/date-picker'\nimport { getPanel } from './panel-utils'\nimport type { DatePickerExpose } from './instance'\n\ndayjs.extend(localeData)\ndayjs.extend(advancedFormat)\ndayjs.extend(customParseFormat)\ndayjs.extend(weekOfYear)\ndayjs.extend(weekYear)\ndayjs.extend(dayOfYear)\ndayjs.extend(isSameOrAfter)\ndayjs.extend(isSameOrBefore)\n\nexport default defineComponent({\n  name: 'ElDatePicker',\n  install: null,\n  props: datePickerProps,\n  emits: [UPDATE_MODEL_EVENT],\n  setup(props, { expose, emit, slots }) {\n    const ns = useNamespace('picker-panel')\n    const isDefaultFormat = computed(() => {\n      return !props.format\n    })\n    provide('ElIsDefaultFormat', isDefaultFormat)\n    provide('ElPopperOptions', reactive(toRef(props, 'popperOptions')))\n    provide(ROOT_PICKER_INJECTION_KEY, {\n      slots,\n      pickerNs: ns,\n    })\n\n    const commonPicker = ref<InstanceType<typeof CommonPicker>>()\n    const refProps: DatePickerExpose = {\n      focus: () => {\n        commonPicker.value?.focus()\n      },\n      blur: () => {\n        commonPicker.value?.blur()\n      },\n      handleOpen: () => {\n        commonPicker.value?.handleOpen()\n      },\n      handleClose: () => {\n        commonPicker.value?.handleClose()\n      },\n    }\n\n    expose(refProps)\n\n    const onModelValueUpdated = (val: SingleOrRange<DateModelType> | null) => {\n      emit(UPDATE_MODEL_EVENT, val)\n    }\n\n    return () => {\n      // since props always have all defined keys on it, {format, ...props} will always overwrite format\n      // pick props.format or provide default value here before spreading\n      const format =\n        props.format ??\n        (DEFAULT_FORMATS_DATEPICKER[props.type] || DEFAULT_FORMATS_DATE)\n\n      const Component = getPanel(props.type)\n\n      return (\n        <CommonPicker\n          {...props}\n          format={format}\n          type={props.type}\n          ref={commonPicker}\n          onUpdate:modelValue={onModelValueUpdated}\n        >\n          {{\n            default: (scopedProps: /**FIXME: remove any type */ any) => (\n              <Component {...scopedProps}>\n                {{\n                  'prev-month': slots['prev-month'],\n                  'next-month': slots['next-month'],\n                  'prev-year': slots['prev-year'],\n                  'next-year': slots['next-year'],\n                }}\n              </Component>\n            ),\n            'range-separator': slots['range-separator'],\n          }}\n        </CommonPicker>\n      )\n    }\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAyBAA,KAAK,CAACC,MAAN,CAAaC,UAAb;AACAF,KAAK,CAACC,MAAN,CAAaE,cAAb;AACAH,KAAK,CAACC,MAAN,CAAaG,iBAAb;AACAJ,KAAK,CAACC,MAAN,CAAaI,UAAb;AACAL,KAAK,CAACC,MAAN,CAAaK,QAAb;AACAN,KAAK,CAACC,MAAN,CAAaM,SAAb;AACAP,KAAK,CAACC,MAAN,CAAaO,aAAb;AACAR,KAAK,CAACC,MAAN,CAAaQ,cAAb;AAEA,IAAAC,UAAA,GAAeC,eAAe,CAAC;EAC7BC,IAAI,EAAE,cADuB;EAE7BC,OAAO,EAAE,IAFoB;EAG7BC,KAAK,EAAEC,eAHsB;EAI7BC,KAAK,EAAE,CAACC,kBAAD,CAJsB;;IAKxBC,MAAA;IAAUC,IAAF;IAAUC;EAAM;IAAS,MAAAC,EAAA,GAAAC,YAAA;IACpC,MAAMC,eAAiB,GAACC,QAAA,OAAxB;MACA,OAAqB,CAAAV,KAAA,CAAAW,MAAA;MACnB;IACDC,OAFD,sBAAAH,eAAA;IAGAG,OAAO,CAAC,mBAADC,QAAsB,CAAAC,KAAA,CAAAd,KAA7B;IACAY,OAAO,CAACG,yBAAmB,EAAQ;MAC5BT,KAAA;MACLU,QADiC,EAAAT;IAEjC;IAFiC,MAAnCU,YAAA,GAAAC,GAAA;IAKA,MAAMC,QAAA;MACNC,KAAA,EAAAA,CAAA,KAAmC;QACjC,IAAAC,EAAa;QACX,CAAYA,EAAA,GAAAJ,YAAZ,CAAAK,KAAA,qBAAAD,EAAA,CAAAD,KAAA;OAF+B;MAIjCG,IAAI,EAAEA,CAAA,KAAM;QACV,IAAYF,EAAA;QALmB,CAAAA,EAAA,GAAAJ,YAAA,CAAAK,KAAA,qBAAAD,EAAA,CAAAE,IAAA;MAOjC;gBACc,EAAAC,CAAA,KAAZ;QAR+B,IAAAH,EAAA;QAUjC,CAAAA,EAAA,GAAAJ,YAAmB,CAAAK,KAAA,qBAAAD,EAAA,CAAAG,UAAA;;MAElBC,WAAA,EAAAA,CAAA;QAZH,IAAAJ,EAAA;QAeM,CAAAA,EAAC,GAAAJ,YAAP,CAAAK,KAAA,qBAAAD,EAAA,CAAAI,WAAA;;KAEM;IACJrB,MAAI,CAACe,QAAA;IACN,MAFDO,mBAAA,GAAAC,GAAA;;IAIA;IACE;MACA,IAAAN,EAAA;MACA,MAAMV,MAAM,GACV,CAAKU,EAAA,GAACrB,KAAA,CAANW,MACC,YAAAU,EAAA,GAAAO,0BAAA,CAAA5B,KAAA,CAAA6B,IAAA,KAAAC,oBAFH;MAIA,MAAMC,SAAS,GAAGC,QAAQ,CAAChC,KAAK,CAAC6B,IAAP,CAA1B;MAEA,OAAAI,WAAA,CAAAC,YAAA,EAAAC,UAAA,CAEQnC,KAFR;QAAA,UAGYW,MAHZ;QAAA,MAIU,EAAAX,KAAK,CAAC6B,IAJhB;QAAA,OAKSZ,YALT;QAAA,qBAMyB,EAAAS;MANzB;QASMU,OAAO,EAAEC,WAAA,IAAAJ,WAAA,CAAAF,SACQ,EADRM,WAAA;UAGH,YAAc,EAAA/B,KAAK,CAAC,YAAD,CAHhB;UAIH,YAAc,EAAAA,KAAK,CAAC,YAAD,CAJhB;UAKH,WAAa,EAAAA,KAAK,CAAC,WAAD,CALf;UAMH,WAAa,EAAAA,KAAK,CAAC,WAAD;SAf9B;QAmBM,iBAAmB,EAAAA,KAAK,CAAC,iBAAD;MAnB9B;KATF;EAiCD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}