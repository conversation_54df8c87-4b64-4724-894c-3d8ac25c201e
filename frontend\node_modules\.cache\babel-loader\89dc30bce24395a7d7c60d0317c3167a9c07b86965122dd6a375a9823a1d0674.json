{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst COMPONENT_NAME = \"ElOption\";\nconst optionProps = buildProps({\n  value: {\n    type: [String, Number, Boolean, Object],\n    required: true\n  },\n  label: {\n    type: [String, Number]\n  },\n  created: Boolean,\n  disabled: Boolean\n});\nexport { COMPONENT_NAME, optionProps };", "map": {"version": 3, "names": ["COMPONENT_NAME", "optionProps", "buildProps", "value", "type", "String", "Number", "Boolean", "Object", "required", "label", "created", "disabled"], "sources": ["../../../../../../packages/components/select/src/option.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\n\nexport const COMPONENT_NAME = 'ElOption'\nexport const optionProps = buildProps({\n  /**\n   * @description value of option\n   */\n  value: {\n    type: [String, Number, Boolean, Object],\n    required: true as const,\n  },\n  /**\n   * @description label of option, same as `value` if omitted\n   */\n  label: {\n    type: [String, Number],\n  },\n  created: Boolean,\n  /**\n   * @description whether option is disabled\n   */\n  disabled: Boolean,\n})\n"], "mappings": ";AACY,MAACA,cAAc,GAAG;AAClB,MAACC,WAAW,GAAGC,UAAU,CAAC;EACpCC,KAAK,EAAE;IACLC,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,CAAC;IACvCC,QAAQ,EAAE;EACd,CAAG;EACDC,KAAK,EAAE;IACLN,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM;EACzB,CAAG;EACDK,OAAO,EAAEJ,OAAO;EAChBK,QAAQ,EAAEL;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}