{"ast": null, "code": "import { isNumber } from '../../../utils/types.mjs';\nconst timeUnits = [[\"Y\", 1e3 * 60 * 60 * 24 * 365], [\"M\", 1e3 * 60 * 60 * 24 * 30], [\"D\", 1e3 * 60 * 60 * 24], [\"H\", 1e3 * 60 * 60], [\"m\", 1e3 * 60], [\"s\", 1e3], [\"S\", 1]];\nconst getTime = value => {\n  return isNumber(value) ? new Date(value).getTime() : value.valueOf();\n};\nconst formatTime = (timestamp, format) => {\n  let timeLeft = timestamp;\n  const escapeRegex = /\\[([^\\]]*)]/g;\n  const replacedText = timeUnits.reduce((current, [name, unit]) => {\n    const replaceRegex = new RegExp(`${name}+(?![^\\\\[\\\\]]*\\\\])`, \"g\");\n    if (replaceRegex.test(current)) {\n      const value = Math.floor(timeLeft / unit);\n      timeLeft -= value * unit;\n      return current.replace(replaceRegex, match => String(value).padStart(match.length, \"0\"));\n    }\n    return current;\n  }, format);\n  return replacedText.replace(escapeRegex, \"$1\");\n};\nexport { formatTime, getTime };", "map": {"version": 3, "names": ["timeUnits", "getTime", "value", "isNumber", "Date", "valueOf", "formatTime", "timestamp", "format", "timeLeft", "escapeRegex", "replacedText", "reduce", "current", "name", "unit", "replaceRegex", "RegExp", "test", "Math", "floor", "replace", "match", "String", "padStart", "length"], "sources": ["../../../../../../packages/components/countdown/src/utils.ts"], "sourcesContent": ["import { isNumber } from '@element-plus/utils'\n\nimport type { Dayjs } from 'dayjs'\n\nconst timeUnits = [\n  ['Y', 1000 * 60 * 60 * 24 * 365], // years\n  ['M', 1000 * 60 * 60 * 24 * 30], // months\n  ['D', 1000 * 60 * 60 * 24], // days\n  ['H', 1000 * 60 * 60], // hours\n  ['m', 1000 * 60], // minutes\n  ['s', 1000], // seconds\n  ['S', 1], // million seconds\n] as const\n\nexport const getTime = (value: number | Dayjs) => {\n  return isNumber(value) ? new Date(value).getTime() : value.valueOf()\n}\n\nexport const formatTime = (timestamp: number, format: string) => {\n  let timeLeft = timestamp\n  const escapeRegex = /\\[([^\\]]*)]/g\n\n  const replacedText = timeUnits.reduce((current, [name, unit]) => {\n    const replaceRegex = new RegExp(`${name}+(?![^\\\\[\\\\]]*\\\\])`, 'g')\n    if (replaceRegex.test(current)) {\n      const value = Math.floor(timeLeft / unit)\n      timeLeft -= value * unit\n      return current.replace(replaceRegex, (match) =>\n        String(value).padStart(match.length, '0')\n      )\n    }\n    return current\n  }, format)\n\n  return replacedText.replace(escapeRegex, '$1')\n}\n"], "mappings": ";AACA,MAAMA,SAAS,GAAG,CAChB,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,EAC/B,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAC9B,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EACzB,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC,EACpB,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC,EACf,CAAC,GAAG,EAAE,GAAG,CAAC,EACV,CAAC,GAAG,EAAE,CAAC,CAAC,CACT;AACW,MAACC,OAAO,GAAIC,KAAK,IAAK;EAChC,OAAOC,QAAQ,CAACD,KAAK,CAAC,GAAG,IAAIE,IAAI,CAACF,KAAK,CAAC,CAACD,OAAO,EAAE,GAAGC,KAAK,CAACG,OAAO,EAAE;AACtE;AACY,MAACC,UAAU,GAAGA,CAACC,SAAS,EAAEC,MAAM,KAAK;EAC/C,IAAIC,QAAQ,GAAGF,SAAS;EACxB,MAAMG,WAAW,GAAG,cAAc;EAClC,MAAMC,YAAY,GAAGX,SAAS,CAACY,MAAM,CAAC,CAACC,OAAO,EAAE,CAACC,IAAI,EAAEC,IAAI,CAAC,KAAK;IAC/D,MAAMC,YAAY,GAAG,IAAIC,MAAM,CAAC,GAAGH,IAAI,oBAAoB,EAAE,GAAG,CAAC;IACjE,IAAIE,YAAY,CAACE,IAAI,CAACL,OAAO,CAAC,EAAE;MAC9B,MAAMX,KAAK,GAAGiB,IAAI,CAACC,KAAK,CAACX,QAAQ,GAAGM,IAAI,CAAC;MACzCN,QAAQ,IAAIP,KAAK,GAAGa,IAAI;MACxB,OAAOF,OAAO,CAACQ,OAAO,CAACL,YAAY,EAAGM,KAAK,IAAKC,MAAM,CAACrB,KAAK,CAAC,CAACsB,QAAQ,CAACF,KAAK,CAACG,MAAM,EAAE,GAAG,CAAC,CAAC;IAChG;IACI,OAAOZ,OAAO;EAClB,CAAG,EAAEL,MAAM,CAAC;EACV,OAAOG,YAAY,CAACU,OAAO,CAACX,WAAW,EAAE,IAAI,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}