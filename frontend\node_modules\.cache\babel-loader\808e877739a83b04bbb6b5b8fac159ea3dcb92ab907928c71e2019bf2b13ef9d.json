{"ast": null, "code": "import Tag from './src/tag2.mjs';\nexport { tagEmits, tagProps } from './src/tag.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElTag = withInstall(Tag);\nexport { ElTag, ElTag as default };", "map": {"version": 3, "names": ["ElTag", "withInstall", "Tag"], "sources": ["../../../../../packages/components/tag/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Tag from './src/tag.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTag: SFCWithInstall<typeof Tag> = withInstall(Tag)\nexport default ElTag\n\nexport * from './src/tag'\n"], "mappings": ";;;AAEY,MAACA,KAAK,GAAGC,WAAW,CAACC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}