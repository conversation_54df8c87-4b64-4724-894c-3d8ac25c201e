{"ast": null, "code": "import Popconfirm from './src/popconfirm2.mjs';\nexport { popconfirmEmits, popconfirmProps } from './src/popconfirm.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElPopconfirm = withInstall(Popconfirm);\nexport { ElPopconfirm, ElPopconfirm as default };", "map": {"version": 3, "names": ["ElPopconfirm", "withInstall", "Popconfirm"], "sources": ["../../../../../packages/components/popconfirm/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Popconfirm from './src/popconfirm.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElPopconfirm: SFCWithInstall<typeof Popconfirm> =\n  withInstall(Popconfirm)\nexport default ElPopconfirm\n\nexport * from './src/popconfirm'\n"], "mappings": ";;;AAEY,MAACA,YAAY,GAAGC,WAAW,CAACC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}