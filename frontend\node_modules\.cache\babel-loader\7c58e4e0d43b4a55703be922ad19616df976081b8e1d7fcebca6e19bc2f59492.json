{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { computed, getCurrentInstance } from 'vue';\nimport { fromPairs } from 'lodash-unified';\nimport { debugWarn } from '../../utils/error.mjs';\nconst DEFAULT_EXCLUDE_KEYS = [\"class\", \"style\"];\nconst LISTENER_PREFIX = /^on[A-Z]/;\nconst useAttrs = (params = {}) => {\n  const {\n    excludeListeners = false,\n    excludeKeys\n  } = params;\n  const allExcludeKeys = computed(() => {\n    return ((excludeKeys == null ? void 0 : excludeKeys.value) || []).concat(DEFAULT_EXCLUDE_KEYS);\n  });\n  const instance = getCurrentInstance();\n  if (!instance) {\n    debugWarn(\"use-attrs\", \"getCurrentInstance() returned null. useAttrs() must be called at the top of a setup function\");\n    return computed(() => ({}));\n  }\n  return computed(() => {\n    var _a;\n    return fromPairs(Object.entries((_a = instance.proxy) == null ? void 0 : _a.$attrs).filter(([key]) => !allExcludeKeys.value.includes(key) && !(excludeListeners && LISTENER_PREFIX.test(key))));\n  });\n};\nexport { useAttrs };", "map": {"version": 3, "names": ["DEFAULT_EXCLUDE_KEYS", "LISTENER_PREFIX", "useAttrs", "params", "excludeListeners", "excludeKeys", "allExcludeKeys", "computed", "value", "concat", "instance", "getCurrentInstance", "debugWarn", "_a", "fromPairs", "Object", "entries", "proxy", "$attrs", "filter", "key", "includes", "test"], "sources": ["../../../../../packages/hooks/use-attrs/index.ts"], "sourcesContent": ["import { computed, getCurrentInstance } from 'vue'\nimport { fromPairs } from 'lodash-unified'\nimport { debugWarn } from '@element-plus/utils'\n\nimport type { ComputedRef } from 'vue'\n\ninterface Params {\n  excludeListeners?: boolean\n  excludeKeys?: ComputedRef<string[]>\n}\n\nconst DEFAULT_EXCLUDE_KEYS = ['class', 'style']\nconst LISTENER_PREFIX = /^on[A-Z]/\n\nexport const useAttrs = (\n  params: Params = {}\n): ComputedRef<Record<string, unknown>> => {\n  const { excludeListeners = false, excludeKeys } = params\n  const allExcludeKeys = computed<string[]>(() => {\n    return (excludeKeys?.value || []).concat(DEFAULT_EXCLUDE_KEYS)\n  })\n\n  const instance = getCurrentInstance()\n  if (!instance) {\n    debugWarn(\n      'use-attrs',\n      'getCurrentInstance() returned null. useAttrs() must be called at the top of a setup function'\n    )\n    return computed(() => ({}))\n  }\n\n  return computed(() =>\n    fromPairs(\n      Object.entries(instance.proxy?.$attrs!).filter(\n        ([key]) =>\n          !allExcludeKeys.value.includes(key) &&\n          !(excludeListeners && LISTENER_PREFIX.test(key))\n      )\n    )\n  )\n}\n"], "mappings": ";;;;;AAGA,MAAMA,oBAAoB,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;AAC/C,MAAMC,eAAe,GAAG,UAAU;AACtB,MAACC,QAAQ,GAAGA,CAACC,MAAM,GAAG,EAAE,KAAK;EACvC,MAAM;IAAEC,gBAAgB,GAAG,KAAK;IAAEC;EAAW,CAAE,GAAGF,MAAM;EACxD,MAAMG,cAAc,GAAGC,QAAQ,CAAC,MAAM;IACpC,OAAO,CAAC,CAACF,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACG,KAAK,KAAK,EAAE,EAAEC,MAAM,CAACT,oBAAoB,CAAC;EAClG,CAAG,CAAC;EACF,MAAMU,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,IAAI,CAACD,QAAQ,EAAE;IACbE,SAAS,CAAC,WAAW,EAAE,8FAA8F,CAAC;IACtH,OAAOL,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;EAC/B;EACE,OAAOA,QAAQ,CAAC,MAAM;IACpB,IAAIM,EAAE;IACN,OAAOC,SAAS,CAACC,MAAM,CAACC,OAAO,CAAC,CAACH,EAAE,GAAGH,QAAQ,CAACO,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,EAAE,CAACK,MAAM,CAAC,CAACC,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,KAAK,CAACd,cAAc,CAACE,KAAK,CAACa,QAAQ,CAACD,GAAG,CAAC,IAAI,EAAEhB,gBAAgB,IAAIH,eAAe,CAACqB,IAAI,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;EACnM,CAAG,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}