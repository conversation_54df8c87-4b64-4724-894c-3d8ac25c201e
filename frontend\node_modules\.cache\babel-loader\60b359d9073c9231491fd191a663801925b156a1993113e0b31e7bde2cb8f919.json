{"ast": null, "code": "import { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../../utils/vue/icon.mjs';\nconst paginationPrevProps = buildProps({\n  disabled: Boolean,\n  currentPage: {\n    type: Number,\n    default: 1\n  },\n  prevText: {\n    type: String\n  },\n  prevIcon: {\n    type: iconPropType\n  }\n});\nconst paginationPrevEmits = {\n  click: evt => evt instanceof MouseEvent\n};\nexport { paginationPrevEmits, paginationPrevProps };", "map": {"version": 3, "names": ["paginationPrevProps", "buildProps", "disabled", "Boolean", "currentPage", "type", "Number", "default", "prevText", "String", "prevIcon", "iconPropType", "paginationPrevEmits", "click", "evt", "MouseEvent"], "sources": ["../../../../../../../packages/components/pagination/src/components/prev.ts"], "sourcesContent": ["import { buildProps, iconPropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Prev from './prev.vue'\n\nexport const paginationPrevProps = buildProps({\n  disabled: Boolean,\n  currentPage: {\n    type: Number,\n    default: 1,\n  },\n  prevText: {\n    type: String,\n  },\n  prevIcon: {\n    type: iconPropType,\n  },\n} as const)\n\nexport const paginationPrevEmits = {\n  click: (evt: MouseEvent) => evt instanceof MouseEvent,\n}\n\nexport type PaginationPrevProps = ExtractPropTypes<typeof paginationPrevProps>\n\nexport type PrevInstance = InstanceType<typeof Prev> & unknown\n"], "mappings": ";;AACY,MAACA,mBAAmB,GAAGC,UAAU,CAAC;EAC5CC,QAAQ,EAAEC,OAAO;EACjBC,WAAW,EAAE;IACXC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,QAAQ,EAAE;IACRH,IAAI,EAAEI;EACV,CAAG;EACDC,QAAQ,EAAE;IACRL,IAAI,EAAEM;EACV;AACA,CAAC;AACW,MAACC,mBAAmB,GAAG;EACjCC,KAAK,EAAGC,GAAG,IAAKA,GAAG,YAAYC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}