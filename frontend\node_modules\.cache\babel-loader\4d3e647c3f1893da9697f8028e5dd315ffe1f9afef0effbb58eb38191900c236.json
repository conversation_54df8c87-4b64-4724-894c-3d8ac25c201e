{"ast": null, "code": "import { getCurrentInstance } from 'vue';\nconst AFTER_APPEAR = \"after-appear\";\nconst AFTER_ENTER = \"after-enter\";\nconst AFTER_LEAVE = \"after-leave\";\nconst APPEAR = \"appear\";\nconst APPEAR_CANCELLED = \"appear-cancelled\";\nconst BEFORE_ENTER = \"before-enter\";\nconst BEFORE_LEAVE = \"before-leave\";\nconst ENTER = \"enter\";\nconst ENTER_CANCELLED = \"enter-cancelled\";\nconst LEAVE = \"leave\";\nconst LEAVE_CANCELLED = \"leave-cancelled\";\nconst useTransitionFallthroughEmits = [AFTER_APPEAR, AFTER_ENTER, AFTER_LEAVE, APPEAR, APPEAR_CANCELLED, BEFORE_ENTER, BEFORE_LEAVE, ENTER, ENTER_CANCELLED, LEAVE, LEAVE_CANCELLED];\nconst useTransitionFallthrough = () => {\n  const {\n    emit\n  } = getCurrentInstance();\n  return {\n    onAfterAppear: () => {\n      emit(AFTER_APPEAR);\n    },\n    onAfterEnter: () => {\n      emit(AFTER_ENTER);\n    },\n    onAfterLeave: () => {\n      emit(AFTER_LEAVE);\n    },\n    onAppearCancelled: () => {\n      emit(APPEAR_CANCELLED);\n    },\n    onBeforeEnter: () => {\n      emit(BEFORE_ENTER);\n    },\n    onBeforeLeave: () => {\n      emit(BEFORE_LEAVE);\n    },\n    onEnter: () => {\n      emit(ENTER);\n    },\n    onEnterCancelled: () => {\n      emit(ENTER_CANCELLED);\n    },\n    onLeave: () => {\n      emit(LEAVE);\n    },\n    onLeaveCancelled: () => {\n      emit(LEAVE_CANCELLED);\n    }\n  };\n};\nexport { useTransitionFallthrough, useTransitionFallthroughEmits };", "map": {"version": 3, "names": ["AFTER_APPEAR", "AFTER_ENTER", "AFTER_LEAVE", "APPEAR", "APPEAR_CANCELLED", "BEFORE_ENTER", "BEFORE_LEAVE", "ENTER", "ENTER_CANCELLED", "LEAVE", "LEAVE_CANCELLED", "useTransitionFallthroughEmits", "useTransitionFallthrough", "emit", "getCurrentInstance", "onAfterAppear", "onAfterEnter", "onAfterLeave", "onAppearCancelled", "onBeforeEnter", "onBeforeLeave", "onEnter", "onEnterCancelled", "onLeave", "onLeaveCancelled"], "sources": ["../../../../../packages/hooks/use-transition-fallthrough/index.ts"], "sourcesContent": ["/* istanbul ignore file */\nimport { getCurrentInstance } from 'vue'\n\nconst AFTER_APPEAR = 'after-appear'\nconst AFTER_ENTER = 'after-enter'\nconst AFTER_LEAVE = 'after-leave'\nconst APPEAR = 'appear'\nconst APPEAR_CANCELLED = 'appear-cancelled'\nconst BEFORE_ENTER = 'before-enter'\nconst BEFORE_LEAVE = 'before-leave'\nconst ENTER = 'enter'\nconst ENTER_CANCELLED = 'enter-cancelled'\nconst LEAVE = 'leave'\nconst LEAVE_CANCELLED = 'leave-cancelled'\n\nexport const useTransitionFallthroughEmits = [\n  AFTER_APPEAR,\n  AFTER_ENTER,\n  AFTER_LEAVE,\n  APPEAR,\n  APPEAR_CANCELLED,\n  BEFORE_ENTER,\n  BEFORE_LEAVE,\n  ENTER,\n  ENTER_CANCELLED,\n  LEAVE,\n  LEAVE_CANCELLED,\n] as const\n\n// Sometimes we want to delegate the transition emitted event\n// we have to right the function locally, which is not a good\n// approach to this, so we created this hook for the event\n// fallthrough\n\n/**\n * NOTE:\n * This is only a delegator for delegating transition callbacks.\n * Use this at your need.\n */\n\n/**\n * Simple usage\n *\n * In your setups:\n *\n * setup() {\n *   const fallthroughMethods = useTransitionFallthrough()\n *   return fallthrough\n * }\n *\n * In your template:\n *\n * <template>\n *  <transition name=\"whatever\" v-bind=\"fallthrough\">\n *    <slot />\n *  </transition>\n * </template>\n *\n */\n\nexport const useTransitionFallthrough = () => {\n  const { emit } = getCurrentInstance()!\n\n  return {\n    onAfterAppear: () => {\n      emit(AFTER_APPEAR)\n    },\n    onAfterEnter: () => {\n      emit(AFTER_ENTER)\n    },\n    onAfterLeave: () => {\n      emit(AFTER_LEAVE)\n    },\n    onAppearCancelled: () => {\n      emit(APPEAR_CANCELLED)\n    },\n    onBeforeEnter: () => {\n      emit(BEFORE_ENTER)\n    },\n    onBeforeLeave: () => {\n      emit(BEFORE_LEAVE)\n    },\n    onEnter: () => {\n      emit(ENTER)\n    },\n    onEnterCancelled: () => {\n      emit(ENTER_CANCELLED)\n    },\n    onLeave: () => {\n      emit(LEAVE)\n    },\n    onLeaveCancelled: () => {\n      emit(LEAVE_CANCELLED)\n    },\n  }\n}\n"], "mappings": ";AACA,MAAMA,YAAY,GAAG,cAAc;AACnC,MAAMC,WAAW,GAAG,aAAa;AACjC,MAAMC,WAAW,GAAG,aAAa;AACjC,MAAMC,MAAM,GAAG,QAAQ;AACvB,MAAMC,gBAAgB,GAAG,kBAAkB;AAC3C,MAAMC,YAAY,GAAG,cAAc;AACnC,MAAMC,YAAY,GAAG,cAAc;AACnC,MAAMC,KAAK,GAAG,OAAO;AACrB,MAAMC,eAAe,GAAG,iBAAiB;AACzC,MAAMC,KAAK,GAAG,OAAO;AACrB,MAAMC,eAAe,GAAG,iBAAiB;AAC7B,MAACC,6BAA6B,GAAG,CAC3CX,YAAY,EACZC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,KAAK,EACLC,eAAe,EACfC,KAAK,EACLC,eAAe,CACjB;AACY,MAACE,wBAAwB,GAAGA,CAAA,KAAM;EAC5C,MAAM;IAAEC;EAAI,CAAE,GAAGC,kBAAkB,EAAE;EACrC,OAAO;IACLC,aAAa,EAAEA,CAAA,KAAM;MACnBF,IAAI,CAACb,YAAY,CAAC;IACxB,CAAK;IACDgB,YAAY,EAAEA,CAAA,KAAM;MAClBH,IAAI,CAACZ,WAAW,CAAC;IACvB,CAAK;IACDgB,YAAY,EAAEA,CAAA,KAAM;MAClBJ,IAAI,CAACX,WAAW,CAAC;IACvB,CAAK;IACDgB,iBAAiB,EAAEA,CAAA,KAAM;MACvBL,IAAI,CAACT,gBAAgB,CAAC;IAC5B,CAAK;IACDe,aAAa,EAAEA,CAAA,KAAM;MACnBN,IAAI,CAACR,YAAY,CAAC;IACxB,CAAK;IACDe,aAAa,EAAEA,CAAA,KAAM;MACnBP,IAAI,CAACP,YAAY,CAAC;IACxB,CAAK;IACDe,OAAO,EAAEA,CAAA,KAAM;MACbR,IAAI,CAACN,KAAK,CAAC;IACjB,CAAK;IACDe,gBAAgB,EAAEA,CAAA,KAAM;MACtBT,IAAI,CAACL,eAAe,CAAC;IAC3B,CAAK;IACDe,OAAO,EAAEA,CAAA,KAAM;MACbV,IAAI,CAACJ,KAAK,CAAC;IACjB,CAAK;IACDe,gBAAgB,EAAEA,CAAA,KAAM;MACtBX,IAAI,CAACH,eAAe,CAAC;IAC3B;EACA,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}