{"ast": null, "code": "/* eslint-disable no-use-before-define */\nconst {\n  BigInteger\n} = require('jsbn');\nconst {\n  encodeDer,\n  decodeDer\n} = require('./asn1');\nconst _ = require('./utils');\nconst sm3 = require('./sm3').sm3;\nconst {\n  G,\n  curve,\n  n\n} = _.generateEcparam();\nconst C1C2C3 = 0;\n\n/**\n * 加密\n */\nfunction doEncrypt(msg, publicKey, cipherMode = 1) {\n  msg = typeof msg === 'string' ? _.hexToArray(_.utf8ToHex(msg)) : Array.prototype.slice.call(msg);\n  publicKey = _.getGlobalCurve().decodePointHex(publicKey); // 先将公钥转成点\n\n  const keypair = _.generateKeyPairHex();\n  const k = new BigInteger(keypair.privateKey, 16); // 随机数 k\n\n  // c1 = k * G\n  let c1 = keypair.publicKey;\n  if (c1.length > 128) c1 = c1.substr(c1.length - 128);\n\n  // (x2, y2) = k * publicKey\n  const p = publicKey.multiply(k);\n  const x2 = _.hexToArray(_.leftPad(p.getX().toBigInteger().toRadix(16), 64));\n  const y2 = _.hexToArray(_.leftPad(p.getY().toBigInteger().toRadix(16), 64));\n\n  // c3 = hash(x2 || msg || y2)\n  const c3 = _.arrayToHex(sm3([].concat(x2, msg, y2)));\n  let ct = 1;\n  let offset = 0;\n  let t = []; // 256 位\n  const z = [].concat(x2, y2);\n  const nextT = () => {\n    // (1) Hai = hash(z || ct)\n    // (2) ct++\n    t = sm3([...z, ct >> 24 & 0x00ff, ct >> 16 & 0x00ff, ct >> 8 & 0x00ff, ct & 0x00ff]);\n    ct++;\n    offset = 0;\n  };\n  nextT(); // 先生成 Ha1\n\n  for (let i = 0, len = msg.length; i < len; i++) {\n    // t = Ha1 || Ha2 || Ha3 || Ha4\n    if (offset === t.length) nextT();\n\n    // c2 = msg ^ t\n    msg[i] ^= t[offset++] & 0xff;\n  }\n  const c2 = _.arrayToHex(msg);\n  return cipherMode === C1C2C3 ? c1 + c2 + c3 : c1 + c3 + c2;\n}\n\n/**\n * 解密\n */\nfunction doDecrypt(encryptData, privateKey, cipherMode = 1, {\n  output = 'string'\n} = {}) {\n  privateKey = new BigInteger(privateKey, 16);\n  let c3 = encryptData.substr(128, 64);\n  let c2 = encryptData.substr(128 + 64);\n  if (cipherMode === C1C2C3) {\n    c3 = encryptData.substr(encryptData.length - 64);\n    c2 = encryptData.substr(128, encryptData.length - 128 - 64);\n  }\n  const msg = _.hexToArray(c2);\n  const c1 = _.getGlobalCurve().decodePointHex('04' + encryptData.substr(0, 128));\n  const p = c1.multiply(privateKey);\n  const x2 = _.hexToArray(_.leftPad(p.getX().toBigInteger().toRadix(16), 64));\n  const y2 = _.hexToArray(_.leftPad(p.getY().toBigInteger().toRadix(16), 64));\n  let ct = 1;\n  let offset = 0;\n  let t = []; // 256 位\n  const z = [].concat(x2, y2);\n  const nextT = () => {\n    // (1) Hai = hash(z || ct)\n    // (2) ct++\n    t = sm3([...z, ct >> 24 & 0x00ff, ct >> 16 & 0x00ff, ct >> 8 & 0x00ff, ct & 0x00ff]);\n    ct++;\n    offset = 0;\n  };\n  nextT(); // 先生成 Ha1\n\n  for (let i = 0, len = msg.length; i < len; i++) {\n    // t = Ha1 || Ha2 || Ha3 || Ha4\n    if (offset === t.length) nextT();\n\n    // c2 = msg ^ t\n    msg[i] ^= t[offset++] & 0xff;\n  }\n\n  // c3 = hash(x2 || msg || y2)\n  const checkC3 = _.arrayToHex(sm3([].concat(x2, msg, y2)));\n  if (checkC3 === c3.toLowerCase()) {\n    return output === 'array' ? msg : _.arrayToUtf8(msg);\n  } else {\n    return output === 'array' ? [] : '';\n  }\n}\n\n/**\n * 签名\n */\nfunction doSignature(msg, privateKey, {\n  pointPool,\n  der,\n  hash,\n  publicKey,\n  userId\n} = {}) {\n  let hashHex = typeof msg === 'string' ? _.utf8ToHex(msg) : _.arrayToHex(msg);\n  if (hash) {\n    // sm3杂凑\n    publicKey = publicKey || getPublicKeyFromPrivateKey(privateKey);\n    hashHex = getHash(hashHex, publicKey, userId);\n  }\n  const dA = new BigInteger(privateKey, 16);\n  const e = new BigInteger(hashHex, 16);\n\n  // k\n  let k = null;\n  let r = null;\n  let s = null;\n  do {\n    do {\n      let point;\n      if (pointPool && pointPool.length) {\n        point = pointPool.pop();\n      } else {\n        point = getPoint();\n      }\n      k = point.k;\n\n      // r = (e + x1) mod n\n      r = e.add(point.x1).mod(n);\n    } while (r.equals(BigInteger.ZERO) || r.add(k).equals(n));\n\n    // s = ((1 + dA)^-1 * (k - r * dA)) mod n\n    s = dA.add(BigInteger.ONE).modInverse(n).multiply(k.subtract(r.multiply(dA))).mod(n);\n  } while (s.equals(BigInteger.ZERO));\n  if (der) return encodeDer(r, s); // asn.1 der 编码\n\n  return _.leftPad(r.toString(16), 64) + _.leftPad(s.toString(16), 64);\n}\n\n/**\n * 验签\n */\nfunction doVerifySignature(msg, signHex, publicKey, {\n  der,\n  hash,\n  userId\n} = {}) {\n  let hashHex = typeof msg === 'string' ? _.utf8ToHex(msg) : _.arrayToHex(msg);\n  if (hash) {\n    // sm3杂凑\n    hashHex = getHash(hashHex, publicKey, userId);\n  }\n  let r;\n  let s;\n  if (der) {\n    const decodeDerObj = decodeDer(signHex); // asn.1 der 解码\n    r = decodeDerObj.r;\n    s = decodeDerObj.s;\n  } else {\n    r = new BigInteger(signHex.substring(0, 64), 16);\n    s = new BigInteger(signHex.substring(64), 16);\n  }\n  const PA = curve.decodePointHex(publicKey);\n  const e = new BigInteger(hashHex, 16);\n\n  // t = (r + s) mod n\n  const t = r.add(s).mod(n);\n  if (t.equals(BigInteger.ZERO)) return false;\n\n  // x1y1 = s * G + t * PA\n  const x1y1 = G.multiply(s).add(PA.multiply(t));\n\n  // R = (e + x1) mod n\n  const R = e.add(x1y1.getX().toBigInteger()).mod(n);\n  return r.equals(R);\n}\n\n/**\n * sm3杂凑算法\n */\nfunction getHash(hashHex, publicKey, userId = '1234567812345678') {\n  // z = hash(entl || userId || a || b || gx || gy || px || py)\n  userId = _.utf8ToHex(userId);\n  const a = _.leftPad(G.curve.a.toBigInteger().toRadix(16), 64);\n  const b = _.leftPad(G.curve.b.toBigInteger().toRadix(16), 64);\n  const gx = _.leftPad(G.getX().toBigInteger().toRadix(16), 64);\n  const gy = _.leftPad(G.getY().toBigInteger().toRadix(16), 64);\n  let px;\n  let py;\n  if (publicKey.length === 128) {\n    px = publicKey.substr(0, 64);\n    py = publicKey.substr(64, 64);\n  } else {\n    const point = G.curve.decodePointHex(publicKey);\n    px = _.leftPad(point.getX().toBigInteger().toRadix(16), 64);\n    py = _.leftPad(point.getY().toBigInteger().toRadix(16), 64);\n  }\n  const data = _.hexToArray(userId + a + b + gx + gy + px + py);\n  const entl = userId.length * 4;\n  data.unshift(entl & 0x00ff);\n  data.unshift(entl >> 8 & 0x00ff);\n  const z = sm3(data);\n\n  // e = hash(z || msg)\n  return _.arrayToHex(sm3(z.concat(_.hexToArray(hashHex))));\n}\n\n/**\n * 计算公钥\n */\nfunction getPublicKeyFromPrivateKey(privateKey) {\n  const PA = G.multiply(new BigInteger(privateKey, 16));\n  const x = _.leftPad(PA.getX().toBigInteger().toString(16), 64);\n  const y = _.leftPad(PA.getY().toBigInteger().toString(16), 64);\n  return '04' + x + y;\n}\n\n/**\n * 获取椭圆曲线点\n */\nfunction getPoint() {\n  const keypair = _.generateKeyPairHex();\n  const PA = curve.decodePointHex(keypair.publicKey);\n  keypair.k = new BigInteger(keypair.privateKey, 16);\n  keypair.x1 = PA.getX().toBigInteger();\n  return keypair;\n}\nmodule.exports = {\n  generateKeyPairHex: _.generateKeyPairHex,\n  compressPublicKeyHex: _.compressPublicKeyHex,\n  comparePublicKeyHex: _.comparePublicKeyHex,\n  doEncrypt,\n  doDecrypt,\n  doSignature,\n  doVerifySignature,\n  getPublicKeyFromPrivateKey,\n  getPoint,\n  verifyPublicKey: _.verifyPublicKey\n};", "map": {"version": 3, "names": ["BigInteger", "require", "encodeDer", "decodeDer", "_", "sm3", "G", "curve", "n", "generateEcparam", "C1C2C3", "doEncrypt", "msg", "public<PERSON>ey", "cipherMode", "hexToArray", "utf8ToHex", "Array", "prototype", "slice", "call", "getGlobalCurve", "decodePointHex", "keypair", "generateKeyPairHex", "k", "privateKey", "c1", "length", "substr", "p", "multiply", "x2", "leftPad", "getX", "toBigInteger", "toRadix", "y2", "getY", "c3", "arrayToHex", "concat", "ct", "offset", "t", "z", "nextT", "i", "len", "c2", "doDecrypt", "encryptData", "output", "checkC3", "toLowerCase", "arrayToUtf8", "doSignature", "pointPool", "der", "hash", "userId", "hashHex", "getPublicKeyFromPrivateKey", "getHash", "dA", "e", "r", "s", "point", "pop", "getPoint", "add", "x1", "mod", "equals", "ZERO", "ONE", "modInverse", "subtract", "toString", "doVerifySignature", "signHex", "decodeDerObj", "substring", "PA", "x1y1", "R", "a", "b", "gx", "gy", "px", "py", "data", "entl", "unshift", "x", "y", "module", "exports", "compressPublicKeyHex", "comparePublicKeyHex", "verifyPublicKey"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/sm-crypto/src/sm2/index.js"], "sourcesContent": ["/* eslint-disable no-use-before-define */\nconst {BigInteger} = require('jsbn')\nconst {encodeDer, decodeDer} = require('./asn1')\nconst _ = require('./utils')\nconst sm3 = require('./sm3').sm3\n\nconst {G, curve, n} = _.generateEcparam()\nconst C1C2C3 = 0\n\n/**\n * 加密\n */\nfunction doEncrypt(msg, publicKey, cipherMode = 1) {\n  msg = typeof msg === 'string' ? _.hexToArray(_.utf8ToHex(msg)) : Array.prototype.slice.call(msg)\n  publicKey = _.getGlobalCurve().decodePointHex(publicKey) // 先将公钥转成点\n\n  const keypair = _.generateKeyPairHex()\n  const k = new BigInteger(keypair.privateKey, 16) // 随机数 k\n\n  // c1 = k * G\n  let c1 = keypair.publicKey\n  if (c1.length > 128) c1 = c1.substr(c1.length - 128)\n\n  // (x2, y2) = k * publicKey\n  const p = publicKey.multiply(k)\n  const x2 = _.hexToArray(_.leftPad(p.getX().toBigInteger().toRadix(16), 64))\n  const y2 = _.hexToArray(_.leftPad(p.getY().toBigInteger().toRadix(16), 64))\n\n  // c3 = hash(x2 || msg || y2)\n  const c3 = _.arrayToHex(sm3([].concat(x2, msg, y2)))\n\n  let ct = 1\n  let offset = 0\n  let t = [] // 256 位\n  const z = [].concat(x2, y2)\n  const nextT = () => {\n    // (1) Hai = hash(z || ct)\n    // (2) ct++\n    t = sm3([...z, ct >> 24 & 0x00ff, ct >> 16 & 0x00ff, ct >> 8 & 0x00ff, ct & 0x00ff])\n    ct++\n    offset = 0\n  }\n  nextT() // 先生成 Ha1\n\n  for (let i = 0, len = msg.length; i < len; i++) {\n    // t = Ha1 || Ha2 || Ha3 || Ha4\n    if (offset === t.length) nextT()\n\n    // c2 = msg ^ t\n    msg[i] ^= t[offset++] & 0xff\n  }\n  const c2 = _.arrayToHex(msg)\n\n  return cipherMode === C1C2C3 ? c1 + c2 + c3 : c1 + c3 + c2\n}\n\n/**\n * 解密\n */\nfunction doDecrypt(encryptData, privateKey, cipherMode = 1, {\n  output = 'string',\n} = {}) {\n  privateKey = new BigInteger(privateKey, 16)\n\n  let c3 = encryptData.substr(128, 64)\n  let c2 = encryptData.substr(128 + 64)\n\n  if (cipherMode === C1C2C3) {\n    c3 = encryptData.substr(encryptData.length - 64)\n    c2 = encryptData.substr(128, encryptData.length - 128 - 64)\n  }\n\n  const msg = _.hexToArray(c2)\n  const c1 = _.getGlobalCurve().decodePointHex('04' + encryptData.substr(0, 128))\n\n  const p = c1.multiply(privateKey)\n  const x2 = _.hexToArray(_.leftPad(p.getX().toBigInteger().toRadix(16), 64))\n  const y2 = _.hexToArray(_.leftPad(p.getY().toBigInteger().toRadix(16), 64))\n\n  let ct = 1\n  let offset = 0\n  let t = [] // 256 位\n  const z = [].concat(x2, y2)\n  const nextT = () => {\n    // (1) Hai = hash(z || ct)\n    // (2) ct++\n    t = sm3([...z, ct >> 24 & 0x00ff, ct >> 16 & 0x00ff, ct >> 8 & 0x00ff, ct & 0x00ff])\n    ct++\n    offset = 0\n  }\n  nextT() // 先生成 Ha1\n\n  for (let i = 0, len = msg.length; i < len; i++) {\n    // t = Ha1 || Ha2 || Ha3 || Ha4\n    if (offset === t.length) nextT()\n\n    // c2 = msg ^ t\n    msg[i] ^= t[offset++] & 0xff\n  }\n\n  // c3 = hash(x2 || msg || y2)\n  const checkC3 = _.arrayToHex(sm3([].concat(x2, msg, y2)))\n\n  if (checkC3 === c3.toLowerCase()) {\n    return output === 'array' ? msg : _.arrayToUtf8(msg)\n  } else {\n    return output === 'array' ? [] : ''\n  }\n}\n\n/**\n * 签名\n */\nfunction doSignature(msg, privateKey, {\n  pointPool, der, hash, publicKey, userId\n} = {}) {\n  let hashHex = typeof msg === 'string' ? _.utf8ToHex(msg) : _.arrayToHex(msg)\n\n  if (hash) {\n    // sm3杂凑\n    publicKey = publicKey || getPublicKeyFromPrivateKey(privateKey)\n    hashHex = getHash(hashHex, publicKey, userId)\n  }\n\n  const dA = new BigInteger(privateKey, 16)\n  const e = new BigInteger(hashHex, 16)\n\n  // k\n  let k = null\n  let r = null\n  let s = null\n\n  do {\n    do {\n      let point\n      if (pointPool && pointPool.length) {\n        point = pointPool.pop()\n      } else {\n        point = getPoint()\n      }\n      k = point.k\n\n      // r = (e + x1) mod n\n      r = e.add(point.x1).mod(n)\n    } while (r.equals(BigInteger.ZERO) || r.add(k).equals(n))\n\n    // s = ((1 + dA)^-1 * (k - r * dA)) mod n\n    s = dA.add(BigInteger.ONE).modInverse(n).multiply(k.subtract(r.multiply(dA))).mod(n)\n  } while (s.equals(BigInteger.ZERO))\n\n  if (der) return encodeDer(r, s) // asn.1 der 编码\n\n  return _.leftPad(r.toString(16), 64) + _.leftPad(s.toString(16), 64)\n}\n\n/**\n * 验签\n */\nfunction doVerifySignature(msg, signHex, publicKey, {der, hash, userId} = {}) {\n  let hashHex = typeof msg === 'string' ? _.utf8ToHex(msg) : _.arrayToHex(msg)\n\n  if (hash) {\n    // sm3杂凑\n    hashHex = getHash(hashHex, publicKey, userId)\n  }\n\n  let r; let\n    s\n  if (der) {\n    const decodeDerObj = decodeDer(signHex) // asn.1 der 解码\n    r = decodeDerObj.r\n    s = decodeDerObj.s\n  } else {\n    r = new BigInteger(signHex.substring(0, 64), 16)\n    s = new BigInteger(signHex.substring(64), 16)\n  }\n\n  const PA = curve.decodePointHex(publicKey)\n  const e = new BigInteger(hashHex, 16)\n\n  // t = (r + s) mod n\n  const t = r.add(s).mod(n)\n\n  if (t.equals(BigInteger.ZERO)) return false\n\n  // x1y1 = s * G + t * PA\n  const x1y1 = G.multiply(s).add(PA.multiply(t))\n\n  // R = (e + x1) mod n\n  const R = e.add(x1y1.getX().toBigInteger()).mod(n)\n\n  return r.equals(R)\n}\n\n/**\n * sm3杂凑算法\n */\nfunction getHash(hashHex, publicKey, userId = '1234567812345678') {\n  // z = hash(entl || userId || a || b || gx || gy || px || py)\n  userId = _.utf8ToHex(userId)\n  const a = _.leftPad(G.curve.a.toBigInteger().toRadix(16), 64)\n  const b = _.leftPad(G.curve.b.toBigInteger().toRadix(16), 64)\n  const gx = _.leftPad(G.getX().toBigInteger().toRadix(16), 64)\n  const gy = _.leftPad(G.getY().toBigInteger().toRadix(16), 64)\n  let px\n  let py\n  if (publicKey.length === 128) {\n    px = publicKey.substr(0, 64)\n    py = publicKey.substr(64, 64)\n  } else {\n    const point = G.curve.decodePointHex(publicKey)\n    px = _.leftPad(point.getX().toBigInteger().toRadix(16), 64)\n    py = _.leftPad(point.getY().toBigInteger().toRadix(16), 64)\n  }\n  const data = _.hexToArray(userId + a + b + gx + gy + px + py)\n\n  const entl = userId.length * 4\n  data.unshift(entl & 0x00ff)\n  data.unshift(entl >> 8 & 0x00ff)\n\n  const z = sm3(data)\n\n  // e = hash(z || msg)\n  return _.arrayToHex(sm3(z.concat(_.hexToArray(hashHex))))\n}\n\n/**\n * 计算公钥\n */\nfunction getPublicKeyFromPrivateKey(privateKey) {\n  const PA = G.multiply(new BigInteger(privateKey, 16))\n  const x = _.leftPad(PA.getX().toBigInteger().toString(16), 64)\n  const y = _.leftPad(PA.getY().toBigInteger().toString(16), 64)\n  return '04' + x + y\n}\n\n/**\n * 获取椭圆曲线点\n */\nfunction getPoint() {\n  const keypair = _.generateKeyPairHex()\n  const PA = curve.decodePointHex(keypair.publicKey)\n\n  keypair.k = new BigInteger(keypair.privateKey, 16)\n  keypair.x1 = PA.getX().toBigInteger()\n\n  return keypair\n}\n\nmodule.exports = {\n  generateKeyPairHex: _.generateKeyPairHex,\n  compressPublicKeyHex: _.compressPublicKeyHex,\n  comparePublicKeyHex: _.comparePublicKeyHex,\n  doEncrypt,\n  doDecrypt,\n  doSignature,\n  doVerifySignature,\n  getPublicKeyFromPrivateKey,\n  getPoint,\n  verifyPublicKey: _.verifyPublicKey,\n}\n"], "mappings": "AAAA;AACA,MAAM;EAACA;AAAU,CAAC,GAAGC,OAAO,CAAC,MAAM,CAAC;AACpC,MAAM;EAACC,SAAS;EAAEC;AAAS,CAAC,GAAGF,OAAO,CAAC,QAAQ,CAAC;AAChD,MAAMG,CAAC,GAAGH,OAAO,CAAC,SAAS,CAAC;AAC5B,MAAMI,GAAG,GAAGJ,OAAO,CAAC,OAAO,CAAC,CAACI,GAAG;AAEhC,MAAM;EAACC,CAAC;EAAEC,KAAK;EAAEC;AAAC,CAAC,GAAGJ,CAAC,CAACK,eAAe,CAAC,CAAC;AACzC,MAAMC,MAAM,GAAG,CAAC;;AAEhB;AACA;AACA;AACA,SAASC,SAASA,CAACC,GAAG,EAAEC,SAAS,EAAEC,UAAU,GAAG,CAAC,EAAE;EACjDF,GAAG,GAAG,OAAOA,GAAG,KAAK,QAAQ,GAAGR,CAAC,CAACW,UAAU,CAACX,CAAC,CAACY,SAAS,CAACJ,GAAG,CAAC,CAAC,GAAGK,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACR,GAAG,CAAC;EAChGC,SAAS,GAAGT,CAAC,CAACiB,cAAc,CAAC,CAAC,CAACC,cAAc,CAACT,SAAS,CAAC,EAAC;;EAEzD,MAAMU,OAAO,GAAGnB,CAAC,CAACoB,kBAAkB,CAAC,CAAC;EACtC,MAAMC,CAAC,GAAG,IAAIzB,UAAU,CAACuB,OAAO,CAACG,UAAU,EAAE,EAAE,CAAC,EAAC;;EAEjD;EACA,IAAIC,EAAE,GAAGJ,OAAO,CAACV,SAAS;EAC1B,IAAIc,EAAE,CAACC,MAAM,GAAG,GAAG,EAAED,EAAE,GAAGA,EAAE,CAACE,MAAM,CAACF,EAAE,CAACC,MAAM,GAAG,GAAG,CAAC;;EAEpD;EACA,MAAME,CAAC,GAAGjB,SAAS,CAACkB,QAAQ,CAACN,CAAC,CAAC;EAC/B,MAAMO,EAAE,GAAG5B,CAAC,CAACW,UAAU,CAACX,CAAC,CAAC6B,OAAO,CAACH,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;EAC3E,MAAMC,EAAE,GAAGjC,CAAC,CAACW,UAAU,CAACX,CAAC,CAAC6B,OAAO,CAACH,CAAC,CAACQ,IAAI,CAAC,CAAC,CAACH,YAAY,CAAC,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;;EAE3E;EACA,MAAMG,EAAE,GAAGnC,CAAC,CAACoC,UAAU,CAACnC,GAAG,CAAC,EAAE,CAACoC,MAAM,CAACT,EAAE,EAAEpB,GAAG,EAAEyB,EAAE,CAAC,CAAC,CAAC;EAEpD,IAAIK,EAAE,GAAG,CAAC;EACV,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,CAAC,GAAG,EAAE,EAAC;EACX,MAAMC,CAAC,GAAG,EAAE,CAACJ,MAAM,CAACT,EAAE,EAAEK,EAAE,CAAC;EAC3B,MAAMS,KAAK,GAAGA,CAAA,KAAM;IAClB;IACA;IACAF,CAAC,GAAGvC,GAAG,CAAC,CAAC,GAAGwC,CAAC,EAAEH,EAAE,IAAI,EAAE,GAAG,MAAM,EAAEA,EAAE,IAAI,EAAE,GAAG,MAAM,EAAEA,EAAE,IAAI,CAAC,GAAG,MAAM,EAAEA,EAAE,GAAG,MAAM,CAAC,CAAC;IACpFA,EAAE,EAAE;IACJC,MAAM,GAAG,CAAC;EACZ,CAAC;EACDG,KAAK,CAAC,CAAC,EAAC;;EAER,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGpC,GAAG,CAACgB,MAAM,EAAEmB,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC9C;IACA,IAAIJ,MAAM,KAAKC,CAAC,CAAChB,MAAM,EAAEkB,KAAK,CAAC,CAAC;;IAEhC;IACAlC,GAAG,CAACmC,CAAC,CAAC,IAAIH,CAAC,CAACD,MAAM,EAAE,CAAC,GAAG,IAAI;EAC9B;EACA,MAAMM,EAAE,GAAG7C,CAAC,CAACoC,UAAU,CAAC5B,GAAG,CAAC;EAE5B,OAAOE,UAAU,KAAKJ,MAAM,GAAGiB,EAAE,GAAGsB,EAAE,GAAGV,EAAE,GAAGZ,EAAE,GAAGY,EAAE,GAAGU,EAAE;AAC5D;;AAEA;AACA;AACA;AACA,SAASC,SAASA,CAACC,WAAW,EAAEzB,UAAU,EAAEZ,UAAU,GAAG,CAAC,EAAE;EAC1DsC,MAAM,GAAG;AACX,CAAC,GAAG,CAAC,CAAC,EAAE;EACN1B,UAAU,GAAG,IAAI1B,UAAU,CAAC0B,UAAU,EAAE,EAAE,CAAC;EAE3C,IAAIa,EAAE,GAAGY,WAAW,CAACtB,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC;EACpC,IAAIoB,EAAE,GAAGE,WAAW,CAACtB,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC;EAErC,IAAIf,UAAU,KAAKJ,MAAM,EAAE;IACzB6B,EAAE,GAAGY,WAAW,CAACtB,MAAM,CAACsB,WAAW,CAACvB,MAAM,GAAG,EAAE,CAAC;IAChDqB,EAAE,GAAGE,WAAW,CAACtB,MAAM,CAAC,GAAG,EAAEsB,WAAW,CAACvB,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC;EAC7D;EAEA,MAAMhB,GAAG,GAAGR,CAAC,CAACW,UAAU,CAACkC,EAAE,CAAC;EAC5B,MAAMtB,EAAE,GAAGvB,CAAC,CAACiB,cAAc,CAAC,CAAC,CAACC,cAAc,CAAC,IAAI,GAAG6B,WAAW,CAACtB,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;EAE/E,MAAMC,CAAC,GAAGH,EAAE,CAACI,QAAQ,CAACL,UAAU,CAAC;EACjC,MAAMM,EAAE,GAAG5B,CAAC,CAACW,UAAU,CAACX,CAAC,CAAC6B,OAAO,CAACH,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;EAC3E,MAAMC,EAAE,GAAGjC,CAAC,CAACW,UAAU,CAACX,CAAC,CAAC6B,OAAO,CAACH,CAAC,CAACQ,IAAI,CAAC,CAAC,CAACH,YAAY,CAAC,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;EAE3E,IAAIM,EAAE,GAAG,CAAC;EACV,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,CAAC,GAAG,EAAE,EAAC;EACX,MAAMC,CAAC,GAAG,EAAE,CAACJ,MAAM,CAACT,EAAE,EAAEK,EAAE,CAAC;EAC3B,MAAMS,KAAK,GAAGA,CAAA,KAAM;IAClB;IACA;IACAF,CAAC,GAAGvC,GAAG,CAAC,CAAC,GAAGwC,CAAC,EAAEH,EAAE,IAAI,EAAE,GAAG,MAAM,EAAEA,EAAE,IAAI,EAAE,GAAG,MAAM,EAAEA,EAAE,IAAI,CAAC,GAAG,MAAM,EAAEA,EAAE,GAAG,MAAM,CAAC,CAAC;IACpFA,EAAE,EAAE;IACJC,MAAM,GAAG,CAAC;EACZ,CAAC;EACDG,KAAK,CAAC,CAAC,EAAC;;EAER,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGpC,GAAG,CAACgB,MAAM,EAAEmB,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC9C;IACA,IAAIJ,MAAM,KAAKC,CAAC,CAAChB,MAAM,EAAEkB,KAAK,CAAC,CAAC;;IAEhC;IACAlC,GAAG,CAACmC,CAAC,CAAC,IAAIH,CAAC,CAACD,MAAM,EAAE,CAAC,GAAG,IAAI;EAC9B;;EAEA;EACA,MAAMU,OAAO,GAAGjD,CAAC,CAACoC,UAAU,CAACnC,GAAG,CAAC,EAAE,CAACoC,MAAM,CAACT,EAAE,EAAEpB,GAAG,EAAEyB,EAAE,CAAC,CAAC,CAAC;EAEzD,IAAIgB,OAAO,KAAKd,EAAE,CAACe,WAAW,CAAC,CAAC,EAAE;IAChC,OAAOF,MAAM,KAAK,OAAO,GAAGxC,GAAG,GAAGR,CAAC,CAACmD,WAAW,CAAC3C,GAAG,CAAC;EACtD,CAAC,MAAM;IACL,OAAOwC,MAAM,KAAK,OAAO,GAAG,EAAE,GAAG,EAAE;EACrC;AACF;;AAEA;AACA;AACA;AACA,SAASI,WAAWA,CAAC5C,GAAG,EAAEc,UAAU,EAAE;EACpC+B,SAAS;EAAEC,GAAG;EAAEC,IAAI;EAAE9C,SAAS;EAAE+C;AACnC,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,IAAIC,OAAO,GAAG,OAAOjD,GAAG,KAAK,QAAQ,GAAGR,CAAC,CAACY,SAAS,CAACJ,GAAG,CAAC,GAAGR,CAAC,CAACoC,UAAU,CAAC5B,GAAG,CAAC;EAE5E,IAAI+C,IAAI,EAAE;IACR;IACA9C,SAAS,GAAGA,SAAS,IAAIiD,0BAA0B,CAACpC,UAAU,CAAC;IAC/DmC,OAAO,GAAGE,OAAO,CAACF,OAAO,EAAEhD,SAAS,EAAE+C,MAAM,CAAC;EAC/C;EAEA,MAAMI,EAAE,GAAG,IAAIhE,UAAU,CAAC0B,UAAU,EAAE,EAAE,CAAC;EACzC,MAAMuC,CAAC,GAAG,IAAIjE,UAAU,CAAC6D,OAAO,EAAE,EAAE,CAAC;;EAErC;EACA,IAAIpC,CAAC,GAAG,IAAI;EACZ,IAAIyC,CAAC,GAAG,IAAI;EACZ,IAAIC,CAAC,GAAG,IAAI;EAEZ,GAAG;IACD,GAAG;MACD,IAAIC,KAAK;MACT,IAAIX,SAAS,IAAIA,SAAS,CAAC7B,MAAM,EAAE;QACjCwC,KAAK,GAAGX,SAAS,CAACY,GAAG,CAAC,CAAC;MACzB,CAAC,MAAM;QACLD,KAAK,GAAGE,QAAQ,CAAC,CAAC;MACpB;MACA7C,CAAC,GAAG2C,KAAK,CAAC3C,CAAC;;MAEX;MACAyC,CAAC,GAAGD,CAAC,CAACM,GAAG,CAACH,KAAK,CAACI,EAAE,CAAC,CAACC,GAAG,CAACjE,CAAC,CAAC;IAC5B,CAAC,QAAQ0D,CAAC,CAACQ,MAAM,CAAC1E,UAAU,CAAC2E,IAAI,CAAC,IAAIT,CAAC,CAACK,GAAG,CAAC9C,CAAC,CAAC,CAACiD,MAAM,CAAClE,CAAC,CAAC;;IAExD;IACA2D,CAAC,GAAGH,EAAE,CAACO,GAAG,CAACvE,UAAU,CAAC4E,GAAG,CAAC,CAACC,UAAU,CAACrE,CAAC,CAAC,CAACuB,QAAQ,CAACN,CAAC,CAACqD,QAAQ,CAACZ,CAAC,CAACnC,QAAQ,CAACiC,EAAE,CAAC,CAAC,CAAC,CAACS,GAAG,CAACjE,CAAC,CAAC;EACtF,CAAC,QAAQ2D,CAAC,CAACO,MAAM,CAAC1E,UAAU,CAAC2E,IAAI,CAAC;EAElC,IAAIjB,GAAG,EAAE,OAAOxD,SAAS,CAACgE,CAAC,EAAEC,CAAC,CAAC,EAAC;;EAEhC,OAAO/D,CAAC,CAAC6B,OAAO,CAACiC,CAAC,CAACa,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG3E,CAAC,CAAC6B,OAAO,CAACkC,CAAC,CAACY,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;AACtE;;AAEA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACpE,GAAG,EAAEqE,OAAO,EAAEpE,SAAS,EAAE;EAAC6C,GAAG;EAAEC,IAAI;EAAEC;AAAM,CAAC,GAAG,CAAC,CAAC,EAAE;EAC5E,IAAIC,OAAO,GAAG,OAAOjD,GAAG,KAAK,QAAQ,GAAGR,CAAC,CAACY,SAAS,CAACJ,GAAG,CAAC,GAAGR,CAAC,CAACoC,UAAU,CAAC5B,GAAG,CAAC;EAE5E,IAAI+C,IAAI,EAAE;IACR;IACAE,OAAO,GAAGE,OAAO,CAACF,OAAO,EAAEhD,SAAS,EAAE+C,MAAM,CAAC;EAC/C;EAEA,IAAIM,CAAC;EAAE,IACLC,CAAC;EACH,IAAIT,GAAG,EAAE;IACP,MAAMwB,YAAY,GAAG/E,SAAS,CAAC8E,OAAO,CAAC,EAAC;IACxCf,CAAC,GAAGgB,YAAY,CAAChB,CAAC;IAClBC,CAAC,GAAGe,YAAY,CAACf,CAAC;EACpB,CAAC,MAAM;IACLD,CAAC,GAAG,IAAIlE,UAAU,CAACiF,OAAO,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;IAChDhB,CAAC,GAAG,IAAInE,UAAU,CAACiF,OAAO,CAACE,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EAC/C;EAEA,MAAMC,EAAE,GAAG7E,KAAK,CAACe,cAAc,CAACT,SAAS,CAAC;EAC1C,MAAMoD,CAAC,GAAG,IAAIjE,UAAU,CAAC6D,OAAO,EAAE,EAAE,CAAC;;EAErC;EACA,MAAMjB,CAAC,GAAGsB,CAAC,CAACK,GAAG,CAACJ,CAAC,CAAC,CAACM,GAAG,CAACjE,CAAC,CAAC;EAEzB,IAAIoC,CAAC,CAAC8B,MAAM,CAAC1E,UAAU,CAAC2E,IAAI,CAAC,EAAE,OAAO,KAAK;;EAE3C;EACA,MAAMU,IAAI,GAAG/E,CAAC,CAACyB,QAAQ,CAACoC,CAAC,CAAC,CAACI,GAAG,CAACa,EAAE,CAACrD,QAAQ,CAACa,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM0C,CAAC,GAAGrB,CAAC,CAACM,GAAG,CAACc,IAAI,CAACnD,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC,CAACsC,GAAG,CAACjE,CAAC,CAAC;EAElD,OAAO0D,CAAC,CAACQ,MAAM,CAACY,CAAC,CAAC;AACpB;;AAEA;AACA;AACA;AACA,SAASvB,OAAOA,CAACF,OAAO,EAAEhD,SAAS,EAAE+C,MAAM,GAAG,kBAAkB,EAAE;EAChE;EACAA,MAAM,GAAGxD,CAAC,CAACY,SAAS,CAAC4C,MAAM,CAAC;EAC5B,MAAM2B,CAAC,GAAGnF,CAAC,CAAC6B,OAAO,CAAC3B,CAAC,CAACC,KAAK,CAACgF,CAAC,CAACpD,YAAY,CAAC,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EAC7D,MAAMoD,CAAC,GAAGpF,CAAC,CAAC6B,OAAO,CAAC3B,CAAC,CAACC,KAAK,CAACiF,CAAC,CAACrD,YAAY,CAAC,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EAC7D,MAAMqD,EAAE,GAAGrF,CAAC,CAAC6B,OAAO,CAAC3B,CAAC,CAAC4B,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EAC7D,MAAMsD,EAAE,GAAGtF,CAAC,CAAC6B,OAAO,CAAC3B,CAAC,CAACgC,IAAI,CAAC,CAAC,CAACH,YAAY,CAAC,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EAC7D,IAAIuD,EAAE;EACN,IAAIC,EAAE;EACN,IAAI/E,SAAS,CAACe,MAAM,KAAK,GAAG,EAAE;IAC5B+D,EAAE,GAAG9E,SAAS,CAACgB,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;IAC5B+D,EAAE,GAAG/E,SAAS,CAACgB,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC;EAC/B,CAAC,MAAM;IACL,MAAMuC,KAAK,GAAG9D,CAAC,CAACC,KAAK,CAACe,cAAc,CAACT,SAAS,CAAC;IAC/C8E,EAAE,GAAGvF,CAAC,CAAC6B,OAAO,CAACmC,KAAK,CAAClC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC3DwD,EAAE,GAAGxF,CAAC,CAAC6B,OAAO,CAACmC,KAAK,CAAC9B,IAAI,CAAC,CAAC,CAACH,YAAY,CAAC,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EAC7D;EACA,MAAMyD,IAAI,GAAGzF,CAAC,CAACW,UAAU,CAAC6C,MAAM,GAAG2B,CAAC,GAAGC,CAAC,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,CAAC;EAE7D,MAAME,IAAI,GAAGlC,MAAM,CAAChC,MAAM,GAAG,CAAC;EAC9BiE,IAAI,CAACE,OAAO,CAACD,IAAI,GAAG,MAAM,CAAC;EAC3BD,IAAI,CAACE,OAAO,CAACD,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC;EAEhC,MAAMjD,CAAC,GAAGxC,GAAG,CAACwF,IAAI,CAAC;;EAEnB;EACA,OAAOzF,CAAC,CAACoC,UAAU,CAACnC,GAAG,CAACwC,CAAC,CAACJ,MAAM,CAACrC,CAAC,CAACW,UAAU,CAAC8C,OAAO,CAAC,CAAC,CAAC,CAAC;AAC3D;;AAEA;AACA;AACA;AACA,SAASC,0BAA0BA,CAACpC,UAAU,EAAE;EAC9C,MAAM0D,EAAE,GAAG9E,CAAC,CAACyB,QAAQ,CAAC,IAAI/B,UAAU,CAAC0B,UAAU,EAAE,EAAE,CAAC,CAAC;EACrD,MAAMsE,CAAC,GAAG5F,CAAC,CAAC6B,OAAO,CAACmD,EAAE,CAAClD,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC4C,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EAC9D,MAAMkB,CAAC,GAAG7F,CAAC,CAAC6B,OAAO,CAACmD,EAAE,CAAC9C,IAAI,CAAC,CAAC,CAACH,YAAY,CAAC,CAAC,CAAC4C,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EAC9D,OAAO,IAAI,GAAGiB,CAAC,GAAGC,CAAC;AACrB;;AAEA;AACA;AACA;AACA,SAAS3B,QAAQA,CAAA,EAAG;EAClB,MAAM/C,OAAO,GAAGnB,CAAC,CAACoB,kBAAkB,CAAC,CAAC;EACtC,MAAM4D,EAAE,GAAG7E,KAAK,CAACe,cAAc,CAACC,OAAO,CAACV,SAAS,CAAC;EAElDU,OAAO,CAACE,CAAC,GAAG,IAAIzB,UAAU,CAACuB,OAAO,CAACG,UAAU,EAAE,EAAE,CAAC;EAClDH,OAAO,CAACiD,EAAE,GAAGY,EAAE,CAAClD,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;EAErC,OAAOZ,OAAO;AAChB;AAEA2E,MAAM,CAACC,OAAO,GAAG;EACf3E,kBAAkB,EAAEpB,CAAC,CAACoB,kBAAkB;EACxC4E,oBAAoB,EAAEhG,CAAC,CAACgG,oBAAoB;EAC5CC,mBAAmB,EAAEjG,CAAC,CAACiG,mBAAmB;EAC1C1F,SAAS;EACTuC,SAAS;EACTM,WAAW;EACXwB,iBAAiB;EACjBlB,0BAA0B;EAC1BQ,QAAQ;EACRgC,eAAe,EAAElG,CAAC,CAACkG;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}