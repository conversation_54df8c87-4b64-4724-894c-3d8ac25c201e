{"ast": null, "code": "import { columns } from './common.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst requiredNumberType = {\n  type: Number,\n  required: true\n};\nconst tableV2HeaderProps = buildProps({\n  class: String,\n  columns,\n  fixedHeaderData: {\n    type: definePropType(Array)\n  },\n  headerData: {\n    type: definePropType(Array),\n    required: true\n  },\n  headerHeight: {\n    type: definePropType([Number, Array]),\n    default: 50\n  },\n  rowWidth: requiredNumberType,\n  rowHeight: {\n    type: Number,\n    default: 50\n  },\n  height: requiredNumberType,\n  width: requiredNumberType\n});\nexport { tableV2HeaderProps };", "map": {"version": 3, "names": ["requiredNumberType", "type", "Number", "required", "tableV2HeaderProps", "buildProps", "class", "String", "columns", "fixedHeaderData", "definePropType", "Array", "headerData", "headerHeight", "default", "row<PERSON>id<PERSON>", "rowHeight", "height", "width"], "sources": ["../../../../../../packages/components/table-v2/src/header.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { columns } from './common'\n\nimport type { ExtractPropTypes } from 'vue'\n\nconst requiredNumberType = {\n  type: Number,\n  required: true,\n} as const\n\nexport const tableV2HeaderProps = buildProps({\n  class: String,\n  columns,\n  fixedHeaderData: {\n    type: definePropType<any[]>(Array),\n  },\n  headerData: {\n    type: definePropType<any[]>(Array),\n    required: true,\n  },\n  headerHeight: {\n    type: definePropType<number | number[]>([Number, Array]),\n    default: 50,\n  },\n  rowWidth: requiredNumberType,\n  rowHeight: {\n    type: Number,\n    default: 50,\n  },\n  height: requiredNumberType,\n  width: requiredNumberType,\n} as const)\n\nexport type TableV2HeaderProps = ExtractPropTypes<typeof tableV2HeaderProps>\n"], "mappings": ";;AAEA,MAAMA,kBAAkB,GAAG;EACzBC,IAAI,EAAEC,MAAM;EACZC,QAAQ,EAAE;AACZ,CAAC;AACW,MAACC,kBAAkB,GAAGC,UAAU,CAAC;EAC3CC,KAAK,EAAEC,MAAM;EACbC,OAAO;EACPC,eAAe,EAAE;IACfR,IAAI,EAAES,cAAc,CAACC,KAAK;EAC9B,CAAG;EACDC,UAAU,EAAE;IACVX,IAAI,EAAES,cAAc,CAACC,KAAK,CAAC;IAC3BR,QAAQ,EAAE;EACd,CAAG;EACDU,YAAY,EAAE;IACZZ,IAAI,EAAES,cAAc,CAAC,CAACR,MAAM,EAAES,KAAK,CAAC,CAAC;IACrCG,OAAO,EAAE;EACb,CAAG;EACDC,QAAQ,EAAEf,kBAAkB;EAC5BgB,SAAS,EAAE;IACTf,IAAI,EAAEC,MAAM;IACZY,OAAO,EAAE;EACb,CAAG;EACDG,MAAM,EAAEjB,kBAAkB;EAC1BkB,KAAK,EAAElB;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}