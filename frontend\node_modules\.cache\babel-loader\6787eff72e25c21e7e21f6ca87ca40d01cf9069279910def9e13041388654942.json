{"ast": null, "code": "const POPPER_INJECTION_KEY = Symbol(\"popper\");\nconst POPPER_CONTENT_INJECTION_KEY = Symbol(\"popperContent\");\nexport { POPPER_CONTENT_INJECTION_KEY, POPPER_INJECTION_KEY };", "map": {"version": 3, "names": ["POPPER_INJECTION_KEY", "Symbol", "POPPER_CONTENT_INJECTION_KEY"], "sources": ["../../../../../../packages/components/popper/src/constants.ts"], "sourcesContent": ["import type { CSSProperties, ComputedRef, InjectionKey, Ref } from 'vue'\nimport type { Instance } from '@popperjs/core'\n\nexport type Measurable = {\n  getBoundingClientRect: () => DOMRect\n}\n\n/**\n * triggerRef indicates the element that triggers popper\n * contentRef indicates the element of popper content\n * referenceRef indicates the element that popper content relative with\n */\nexport type ElPopperInjectionContext = {\n  triggerRef: Ref<Measurable | undefined>\n  contentRef: Ref<HTMLElement | undefined>\n  popperInstanceRef: Ref<Instance | undefined>\n  referenceRef: Ref<Measurable | undefined>\n  role: ComputedRef<string>\n}\n\nexport type ElPopperContentInjectionContext = {\n  arrowRef: Ref<HTMLElement | undefined>\n  arrowStyle: ComputedRef<CSSProperties>\n}\n\nexport const POPPER_INJECTION_KEY: InjectionKey<ElPopperInjectionContext> =\n  Symbol('popper')\n\nexport const POPPER_CONTENT_INJECTION_KEY: InjectionKey<ElPopperContentInjectionContext> =\n  Symbol('popperContent')\n"], "mappings": "AAAY,MAACA,oBAAoB,GAAGC,MAAM,CAAC,QAAQ;AACvC,MAACC,4BAA4B,GAAGD,MAAM,CAAC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}