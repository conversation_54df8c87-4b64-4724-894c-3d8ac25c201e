{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst progressProps = buildProps({\n  type: {\n    type: String,\n    default: \"line\",\n    values: [\"line\", \"circle\", \"dashboard\"]\n  },\n  percentage: {\n    type: Number,\n    default: 0,\n    validator: val => val >= 0 && val <= 100\n  },\n  status: {\n    type: String,\n    default: \"\",\n    values: [\"\", \"success\", \"exception\", \"warning\"]\n  },\n  indeterminate: Boolean,\n  duration: {\n    type: Number,\n    default: 3\n  },\n  strokeWidth: {\n    type: Number,\n    default: 6\n  },\n  strokeLinecap: {\n    type: definePropType(String),\n    default: \"round\"\n  },\n  textInside: Boolean,\n  width: {\n    type: Number,\n    default: 126\n  },\n  showText: {\n    type: Boolean,\n    default: true\n  },\n  color: {\n    type: definePropType([String, Array, Function]),\n    default: \"\"\n  },\n  striped: Boolean,\n  stripedFlow: Boolean,\n  format: {\n    type: definePropType(Function),\n    default: percentage => `${percentage}%`\n  }\n});\nexport { progressProps };", "map": {"version": 3, "names": ["progressProps", "buildProps", "type", "String", "default", "values", "percentage", "Number", "validator", "val", "status", "indeterminate", "Boolean", "duration", "strokeWidth", "strokeLinecap", "definePropType", "textInside", "width", "showText", "color", "Array", "Function", "striped", "stripedFlow", "format"], "sources": ["../../../../../../packages/components/progress/src/progress.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes, SVGAttributes } from 'vue'\nimport type Progress from './progress.vue'\n\nexport type ProgressColor = { color: string; percentage: number }\nexport type ProgressFn = (percentage: number) => string\n\nexport const progressProps = buildProps({\n  /**\n   * @description type of progress bar\n   */\n  type: {\n    type: String,\n    default: 'line',\n    values: ['line', 'circle', 'dashboard'],\n  },\n  /**\n   * @description percentage, required\n   */\n  percentage: {\n    type: Number,\n    default: 0,\n    validator: (val: number): boolean => val >= 0 && val <= 100,\n  },\n  /**\n   * @description the current status of progress bar\n   */\n  status: {\n    type: String,\n    default: '',\n    values: ['', 'success', 'exception', 'warning'],\n  },\n  /**\n   * @description set indeterminate progress\n   */\n  indeterminate: Boolean,\n  /**\n   * @description control the animation duration of indeterminate progress or striped flow progress\n   */\n  duration: {\n    type: Number,\n    default: 3,\n  },\n  /**\n   * @description the width of progress bar\n   */\n  strokeWidth: {\n    type: Number,\n    default: 6,\n  },\n  /**\n   * @description butt/circle/dashboard type shape at the end path\n   */\n  strokeLinecap: {\n    type: definePropType<NonNullable<SVGAttributes['stroke-linecap']>>(String),\n    default: 'round',\n  },\n  /**\n   * @description whether to place the percentage inside progress bar, only works when `type` is 'line'\n   */\n  textInside: Boolean,\n  /**\n   * @description the canvas width of circle progress bar\n   */\n  width: {\n    type: Number,\n    default: 126,\n  },\n  /**\n   * @description whether to show percentage\n   */\n  showText: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description background color of progress bar. Overrides `status` prop\n   */\n  color: {\n    type: definePropType<string | ProgressColor[] | ProgressFn>([\n      String,\n      Array,\n      Function,\n    ]),\n    default: '',\n  },\n  /**\n   * @description stripe over the progress bar's color\n   */\n  striped: Boolean,\n  /**\n   * @description get the stripes to flow\n   */\n  stripedFlow: Boolean,\n  /**\n   * @description custom text format\n   */\n  format: {\n    type: definePropType<ProgressFn>(Function),\n    default: (percentage: number): string => `${percentage}%`,\n  },\n} as const)\n\nexport type ProgressProps = ExtractPropTypes<typeof progressProps>\nexport type ProgressInstance = InstanceType<typeof Progress> & unknown\n"], "mappings": ";AACY,MAACA,aAAa,GAAGC,UAAU,CAAC;EACtCC,IAAI,EAAE;IACJA,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW;EAC1C,CAAG;EACDC,UAAU,EAAE;IACVJ,IAAI,EAAEK,MAAM;IACZH,OAAO,EAAE,CAAC;IACVI,SAAS,EAAGC,GAAG,IAAKA,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI;EAC3C,CAAG;EACDC,MAAM,EAAE;IACNR,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS;EAClD,CAAG;EACDM,aAAa,EAAEC,OAAO;EACtBC,QAAQ,EAAE;IACRX,IAAI,EAAEK,MAAM;IACZH,OAAO,EAAE;EACb,CAAG;EACDU,WAAW,EAAE;IACXZ,IAAI,EAAEK,MAAM;IACZH,OAAO,EAAE;EACb,CAAG;EACDW,aAAa,EAAE;IACbb,IAAI,EAAEc,cAAc,CAACb,MAAM,CAAC;IAC5BC,OAAO,EAAE;EACb,CAAG;EACDa,UAAU,EAAEL,OAAO;EACnBM,KAAK,EAAE;IACLhB,IAAI,EAAEK,MAAM;IACZH,OAAO,EAAE;EACb,CAAG;EACDe,QAAQ,EAAE;IACRjB,IAAI,EAAEU,OAAO;IACbR,OAAO,EAAE;EACb,CAAG;EACDgB,KAAK,EAAE;IACLlB,IAAI,EAAEc,cAAc,CAAC,CACnBb,MAAM,EACNkB,KAAK,EACLC,QAAQ,CACT,CAAC;IACFlB,OAAO,EAAE;EACb,CAAG;EACDmB,OAAO,EAAEX,OAAO;EAChBY,WAAW,EAAEZ,OAAO;EACpBa,MAAM,EAAE;IACNvB,IAAI,EAAEc,cAAc,CAACM,QAAQ,CAAC;IAC9BlB,OAAO,EAAGE,UAAU,IAAK,GAAGA,UAAU;EAC1C;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}