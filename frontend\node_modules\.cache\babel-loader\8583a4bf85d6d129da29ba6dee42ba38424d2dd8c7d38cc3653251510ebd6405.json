{"ast": null, "code": "import trimmedEndIndex from './_trimmedEndIndex.js';\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\nfunction baseTrim(string) {\n  return string ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '') : string;\n}\nexport default baseTrim;", "map": {"version": 3, "names": ["trimmedEndIndex", "reTrimStart", "baseTrim", "string", "slice", "replace"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/node_modules/lodash-es/_baseTrim.js"], "sourcesContent": ["import trimmedEndIndex from './_trimmedEndIndex.js';\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\nfunction baseTrim(string) {\n  return string\n    ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '')\n    : string;\n}\n\nexport default baseTrim;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;;AAEnD;AACA,IAAIC,WAAW,GAAG,MAAM;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,MAAM,EAAE;EACxB,OAAOA,MAAM,GACTA,MAAM,CAACC,KAAK,CAAC,CAAC,EAAEJ,eAAe,CAACG,MAAM,CAAC,GAAG,CAAC,CAAC,CAACE,OAAO,CAACJ,WAAW,EAAE,EAAE,CAAC,GACrEE,MAAM;AACZ;AAEA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}