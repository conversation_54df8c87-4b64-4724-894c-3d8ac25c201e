{"ast": null, "code": "import Timeline from './src/timeline.mjs';\nimport TimelineItem from './src/timeline-item2.mjs';\nexport { timelineItemProps } from './src/timeline-item.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElTimeline = withInstall(Timeline, {\n  TimelineItem\n});\nconst ElTimelineItem = withNoopInstall(TimelineItem);\nexport { ElTimeline, ElTimelineItem, ElTimeline as default };", "map": {"version": 3, "names": ["ElTimeline", "withInstall", "Timeline", "TimelineItem", "ElTimelineItem", "withNoopInstall"], "sources": ["../../../../../packages/components/timeline/index.ts"], "sourcesContent": ["import { withInstall, withNoopInstall } from '@element-plus/utils'\nimport Timeline from './src/timeline'\nimport TimelineItem from './src/timeline-item.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTimeline: SFCWithInstall<typeof Timeline> & {\n  TimelineItem: typeof TimelineItem\n} = withInstall(Timeline, {\n  TimelineItem,\n})\nexport default ElTimeline\nexport const ElTimelineItem: SFCWithInstall<typeof TimelineItem> =\n  withNoopInstall(TimelineItem)\n\nexport * from './src/timeline'\nexport * from './src/timeline-item'\n"], "mappings": ";;;;AAGY,MAACA,UAAU,GAAGC,WAAW,CAACC,QAAQ,EAAE;EAC9CC;AACF,CAAC;AAEW,MAACC,cAAc,GAAGC,eAAe,CAACF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}