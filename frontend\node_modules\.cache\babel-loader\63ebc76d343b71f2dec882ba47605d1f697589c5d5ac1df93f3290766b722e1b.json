{"ast": null, "code": "import Switch from './src/switch2.mjs';\nexport { switchEmits, switchProps } from './src/switch.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElSwitch = withInstall(Switch);\nexport { ElSwitch, ElSwitch as default };", "map": {"version": 3, "names": ["ElSwitch", "withInstall", "Switch"], "sources": ["../../../../../packages/components/switch/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Switch from './src/switch.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSwitch: SFCWithInstall<typeof Switch> = withInstall(Switch)\nexport default ElSwitch\n\nexport * from './src/switch'\n"], "mappings": ";;;AAEY,MAACA,QAAQ,GAAGC,WAAW,CAACC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}