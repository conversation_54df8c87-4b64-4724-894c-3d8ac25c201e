{"ast": null, "code": "const uploadContextKey = Symbol(\"uploadContextKey\");\nexport { uploadContextKey };", "map": {"version": 3, "names": ["uploadContextKey", "Symbol"], "sources": ["../../../../../../packages/components/upload/src/constants.ts"], "sourcesContent": ["import type { ComputedRef, InjectionKey } from 'vue'\n\nexport interface UploadContext {\n  accept: ComputedRef<string>\n}\n\nexport const uploadContextKey: InjectionKey<UploadContext> =\n  Symbol('uploadContextKey')\n"], "mappings": "AAAY,MAACA,gBAAgB,GAAGC,MAAM,CAAC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}