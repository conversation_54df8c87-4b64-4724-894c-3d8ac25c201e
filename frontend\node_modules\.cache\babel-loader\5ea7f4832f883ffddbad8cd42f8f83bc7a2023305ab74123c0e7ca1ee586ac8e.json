{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { defineComponent, inject, ref, computed, watch, provide, getCurrentInstance, resolveComponent, openBlock, createElementBlock, normalizeClass, Fragment, renderList, createBlock, renderSlot, createElementVNode, toDisplayString, createCommentVNode, withDirectives, vShow } from 'vue';\nimport { selectKey } from '../../select/src/token.mjs';\nimport TreeStore from './model/tree-store.mjs';\nimport { getNodeKey, handleCurrentChange } from './model/util.mjs';\nimport ElTreeNode from './tree-node.mjs';\nimport { useNodeExpandEventBroadcast } from './model/useNodeExpandEventBroadcast.mjs';\nimport { useDragNodeHandler } from './model/useDragNode.mjs';\nimport { useKeydown } from './model/useKeydown.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { formItemContextKey } from '../../form/src/constants.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElTree\",\n  components: {\n    ElTreeNode\n  },\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    emptyText: {\n      type: String\n    },\n    renderAfterExpand: {\n      type: Boolean,\n      default: true\n    },\n    nodeKey: String,\n    checkStrictly: Boolean,\n    defaultExpandAll: Boolean,\n    expandOnClickNode: {\n      type: Boolean,\n      default: true\n    },\n    checkOnClickNode: Boolean,\n    checkOnClickLeaf: {\n      type: Boolean,\n      default: true\n    },\n    checkDescendants: {\n      type: Boolean,\n      default: false\n    },\n    autoExpandParent: {\n      type: Boolean,\n      default: true\n    },\n    defaultCheckedKeys: Array,\n    defaultExpandedKeys: Array,\n    currentNodeKey: [String, Number],\n    renderContent: Function,\n    showCheckbox: {\n      type: Boolean,\n      default: false\n    },\n    draggable: {\n      type: Boolean,\n      default: false\n    },\n    allowDrag: Function,\n    allowDrop: Function,\n    props: {\n      type: Object,\n      default: () => ({\n        children: \"children\",\n        label: \"label\",\n        disabled: \"disabled\"\n      })\n    },\n    lazy: {\n      type: Boolean,\n      default: false\n    },\n    highlightCurrent: Boolean,\n    load: Function,\n    filterNodeMethod: Function,\n    accordion: Boolean,\n    indent: {\n      type: Number,\n      default: 18\n    },\n    icon: {\n      type: iconPropType\n    }\n  },\n  emits: [\"check-change\", \"current-change\", \"node-click\", \"node-contextmenu\", \"node-collapse\", \"node-expand\", \"check\", \"node-drag-start\", \"node-drag-end\", \"node-drop\", \"node-drag-leave\", \"node-drag-enter\", \"node-drag-over\"],\n  setup(props, ctx) {\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"tree\");\n    const selectInfo = inject(selectKey, null);\n    const store = ref(new TreeStore({\n      key: props.nodeKey,\n      data: props.data,\n      lazy: props.lazy,\n      props: props.props,\n      load: props.load,\n      currentNodeKey: props.currentNodeKey,\n      checkStrictly: props.checkStrictly,\n      checkDescendants: props.checkDescendants,\n      defaultCheckedKeys: props.defaultCheckedKeys,\n      defaultExpandedKeys: props.defaultExpandedKeys,\n      autoExpandParent: props.autoExpandParent,\n      defaultExpandAll: props.defaultExpandAll,\n      filterNodeMethod: props.filterNodeMethod\n    }));\n    store.value.initialize();\n    const root = ref(store.value.root);\n    const currentNode = ref(null);\n    const el$ = ref(null);\n    const dropIndicator$ = ref(null);\n    const {\n      broadcastExpanded\n    } = useNodeExpandEventBroadcast(props);\n    const {\n      dragState\n    } = useDragNodeHandler({\n      props,\n      ctx,\n      el$,\n      dropIndicator$,\n      store\n    });\n    useKeydown({\n      el$\n    }, store);\n    const isEmpty = computed(() => {\n      const {\n        childNodes\n      } = root.value;\n      const hasFilteredOptions = selectInfo ? selectInfo.hasFilteredOptions !== 0 : false;\n      return (!childNodes || childNodes.length === 0 || childNodes.every(({\n        visible\n      }) => !visible)) && !hasFilteredOptions;\n    });\n    watch(() => props.currentNodeKey, newVal => {\n      store.value.setCurrentNodeKey(newVal);\n    });\n    watch(() => props.defaultCheckedKeys, newVal => {\n      store.value.setDefaultCheckedKey(newVal);\n    });\n    watch(() => props.defaultExpandedKeys, newVal => {\n      store.value.setDefaultExpandedKeys(newVal);\n    });\n    watch(() => props.data, newVal => {\n      store.value.setData(newVal);\n    }, {\n      deep: true\n    });\n    watch(() => props.checkStrictly, newVal => {\n      store.value.checkStrictly = newVal;\n    });\n    const filter = value => {\n      if (!props.filterNodeMethod) throw new Error(\"[Tree] filterNodeMethod is required when filter\");\n      store.value.filter(value);\n    };\n    const getNodeKey$1 = node => {\n      return getNodeKey(props.nodeKey, node.data);\n    };\n    const getNodePath = data => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in getNodePath\");\n      const node = store.value.getNode(data);\n      if (!node) return [];\n      const path = [node.data];\n      let parent = node.parent;\n      while (parent && parent !== root.value) {\n        path.push(parent.data);\n        parent = parent.parent;\n      }\n      return path.reverse();\n    };\n    const getCheckedNodes = (leafOnly, includeHalfChecked) => {\n      return store.value.getCheckedNodes(leafOnly, includeHalfChecked);\n    };\n    const getCheckedKeys = leafOnly => {\n      return store.value.getCheckedKeys(leafOnly);\n    };\n    const getCurrentNode = () => {\n      const currentNode2 = store.value.getCurrentNode();\n      return currentNode2 ? currentNode2.data : null;\n    };\n    const getCurrentKey = () => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in getCurrentKey\");\n      const currentNode2 = getCurrentNode();\n      return currentNode2 ? currentNode2[props.nodeKey] : null;\n    };\n    const setCheckedNodes = (nodes, leafOnly) => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in setCheckedNodes\");\n      store.value.setCheckedNodes(nodes, leafOnly);\n    };\n    const setCheckedKeys = (keys, leafOnly) => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in setCheckedKeys\");\n      store.value.setCheckedKeys(keys, leafOnly);\n    };\n    const setChecked = (data, checked, deep) => {\n      store.value.setChecked(data, checked, deep);\n    };\n    const getHalfCheckedNodes = () => {\n      return store.value.getHalfCheckedNodes();\n    };\n    const getHalfCheckedKeys = () => {\n      return store.value.getHalfCheckedKeys();\n    };\n    const setCurrentNode = (node, shouldAutoExpandParent = true) => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in setCurrentNode\");\n      handleCurrentChange(store, ctx.emit, () => {\n        broadcastExpanded(node);\n        store.value.setUserCurrentNode(node, shouldAutoExpandParent);\n      });\n    };\n    const setCurrentKey = (key, shouldAutoExpandParent = true) => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in setCurrentKey\");\n      handleCurrentChange(store, ctx.emit, () => {\n        broadcastExpanded();\n        store.value.setCurrentNodeKey(key, shouldAutoExpandParent);\n      });\n    };\n    const getNode = data => {\n      return store.value.getNode(data);\n    };\n    const remove = data => {\n      store.value.remove(data);\n    };\n    const append = (data, parentNode) => {\n      store.value.append(data, parentNode);\n    };\n    const insertBefore = (data, refNode) => {\n      store.value.insertBefore(data, refNode);\n    };\n    const insertAfter = (data, refNode) => {\n      store.value.insertAfter(data, refNode);\n    };\n    const handleNodeExpand = (nodeData, node, instance) => {\n      broadcastExpanded(node);\n      ctx.emit(\"node-expand\", nodeData, node, instance);\n    };\n    const updateKeyChildren = (key, data) => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in updateKeyChild\");\n      store.value.updateChildren(key, data);\n    };\n    provide(\"RootTree\", {\n      ctx,\n      props,\n      store,\n      root,\n      currentNode,\n      instance: getCurrentInstance()\n    });\n    provide(formItemContextKey, void 0);\n    return {\n      ns,\n      store,\n      root,\n      currentNode,\n      dragState,\n      el$,\n      dropIndicator$,\n      isEmpty,\n      filter,\n      getNodeKey: getNodeKey$1,\n      getNodePath,\n      getCheckedNodes,\n      getCheckedKeys,\n      getCurrentNode,\n      getCurrentKey,\n      setCheckedNodes,\n      setCheckedKeys,\n      setChecked,\n      getHalfCheckedNodes,\n      getHalfCheckedKeys,\n      setCurrentNode,\n      setCurrentKey,\n      t,\n      getNode,\n      remove,\n      append,\n      insertBefore,\n      insertAfter,\n      handleNodeExpand,\n      updateKeyChildren\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_tree_node = resolveComponent(\"el-tree-node\");\n  return openBlock(), createElementBlock(\"div\", {\n    ref: \"el$\",\n    class: normalizeClass([_ctx.ns.b(), _ctx.ns.is(\"dragging\", !!_ctx.dragState.draggingNode), _ctx.ns.is(\"drop-not-allow\", !_ctx.dragState.allowDrop), _ctx.ns.is(\"drop-inner\", _ctx.dragState.dropType === \"inner\"), {\n      [_ctx.ns.m(\"highlight-current\")]: _ctx.highlightCurrent\n    }]),\n    role: \"tree\"\n  }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.root.childNodes, child => {\n    return openBlock(), createBlock(_component_el_tree_node, {\n      key: _ctx.getNodeKey(child),\n      node: child,\n      props: _ctx.props,\n      accordion: _ctx.accordion,\n      \"render-after-expand\": _ctx.renderAfterExpand,\n      \"show-checkbox\": _ctx.showCheckbox,\n      \"render-content\": _ctx.renderContent,\n      onNodeExpand: _ctx.handleNodeExpand\n    }, null, 8, [\"node\", \"props\", \"accordion\", \"render-after-expand\", \"show-checkbox\", \"render-content\", \"onNodeExpand\"]);\n  }), 128)), _ctx.isEmpty ? (openBlock(), createElementBlock(\"div\", {\n    key: 0,\n    class: normalizeClass(_ctx.ns.e(\"empty-block\"))\n  }, [renderSlot(_ctx.$slots, \"empty\", {}, () => {\n    var _a;\n    return [createElementVNode(\"span\", {\n      class: normalizeClass(_ctx.ns.e(\"empty-text\"))\n    }, toDisplayString((_a = _ctx.emptyText) != null ? _a : _ctx.t(\"el.tree.emptyText\")), 3)];\n  })], 2)) : createCommentVNode(\"v-if\", true), withDirectives(createElementVNode(\"div\", {\n    ref: \"dropIndicator$\",\n    class: normalizeClass(_ctx.ns.e(\"drop-indicator\"))\n  }, null, 2), [[vShow, _ctx.dragState.showDropIndicator]])], 2);\n}\nvar Tree = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"tree.vue\"]]);\nexport { Tree as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "name", "components", "ElTreeNode", "props", "data", "type", "Array", "default", "emptyText", "String", "renderAfterExpand", "Boolean", "nodeKey", "checkStrictly", "defaultExpandAll", "expandOnClickNode", "checkOnClickNode", "checkOnClickLeaf", "checkDescendants", "autoExpandParent", "defaultCheckedKeys", "defaultExpandedKeys", "currentNodeKey", "Number", "renderContent", "Function", "showCheckbox", "draggable", "allowDrag", "allowDrop", "Object", "children", "label", "disabled", "lazy", "highlightCurrent", "load", "filterNodeMethod", "accordion", "indent", "icon", "iconPropType", "emits", "setup", "ctx", "t", "useLocale", "ns", "useNamespace", "selectInfo", "inject", "<PERSON><PERSON><PERSON>", "store", "ref", "TreeStore", "key", "value", "initialize", "root", "currentNode", "el$", "dropIndicator$", "broadcastExpanded", "useNodeExpandEventBroadcast", "dragState", "useDragNodeHandler", "useKeydown", "isEmpty", "computed", "childNodes", "hasFilteredOptions", "length", "every", "visible", "watch", "newVal", "setCurrentNodeKey", "setDefaultCheckedKey", "setDefaultExpandedKeys", "setData", "deep", "filter", "Error", "getNodeKey$1", "node", "getNodeKey", "getNodePath", "getNode", "path", "parent", "push", "reverse", "getCheckedNodes", "leafOnly", "includeHalfChecked", "getChe<PERSON><PERSON>eys", "getCurrentNode", "currentNode2", "get<PERSON><PERSON><PERSON><PERSON>ey", "setCheckedNodes", "nodes", "set<PERSON><PERSON><PERSON><PERSON>eys", "keys", "setChecked", "checked", "getHalfCheckedNodes", "getHalfCheckedKeys", "setCurrentNode", "shouldAutoExpandParent", "handleCurrentChange", "emit", "setUserCurrentNode", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "remove", "append", "parentNode", "insertBefore", "refNode", "insertAfter", "handleNodeExpand", "nodeData", "instance", "update<PERSON>ey<PERSON><PERSON><PERSON>n", "update<PERSON><PERSON><PERSON>n", "provide", "getCurrentInstance", "formItemContextKey", "_sfc_render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_el_tree_node", "resolveComponent", "openBlock", "createElementBlock", "class", "normalizeClass", "b", "is", "draggingNode", "dropType", "m", "role", "Fragment", "renderList", "child", "createBlock", "onNodeExpand", "e", "renderSlot", "$slots", "_a", "createElementVNode", "toDisplayString", "createCommentVNode", "withDirectives", "vShow", "showDropIndicator", "Tree", "_export_sfc"], "sources": ["../../../../../../packages/components/tree/src/tree.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"el$\"\n    :class=\"[\n      ns.b(),\n      ns.is('dragging', !!dragState.draggingNode),\n      ns.is('drop-not-allow', !dragState.allowDrop),\n      ns.is('drop-inner', dragState.dropType === 'inner'),\n      { [ns.m('highlight-current')]: highlightCurrent },\n    ]\"\n    role=\"tree\"\n  >\n    <el-tree-node\n      v-for=\"child in root.childNodes\"\n      :key=\"getNodeKey(child)\"\n      :node=\"child\"\n      :props=\"props\"\n      :accordion=\"accordion\"\n      :render-after-expand=\"renderAfterExpand\"\n      :show-checkbox=\"showCheckbox\"\n      :render-content=\"renderContent\"\n      @node-expand=\"handleNodeExpand\"\n    />\n    <div v-if=\"isEmpty\" :class=\"ns.e('empty-block')\">\n      <slot name=\"empty\">\n        <span :class=\"ns.e('empty-text')\">\n          {{ emptyText ?? t('el.tree.emptyText') }}\n        </span>\n      </slot>\n    </div>\n    <div\n      v-show=\"dragState.showDropIndicator\"\n      ref=\"dropIndicator$\"\n      :class=\"ns.e('drop-indicator')\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\">\n// @ts-nocheck\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  inject,\n  provide,\n  ref,\n  watch,\n} from 'vue'\nimport { iconPropType } from '@element-plus/utils'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { formItemContextKey } from '@element-plus/components/form'\nimport { selectKey } from '@element-plus/components/select/src/token'\nimport TreeStore from './model/tree-store'\nimport { getNodeKey as getNodeKeyUtil, handleCurrentChange } from './model/util'\nimport ElTreeNode from './tree-node.vue'\nimport { useNodeExpandEventBroadcast } from './model/useNodeExpandEventBroadcast'\nimport { useDragNodeHandler } from './model/useDragNode'\nimport { useKeydown } from './model/useKeydown'\nimport type Node from './model/node'\n\nimport type { ComponentInternalInstance, PropType } from 'vue'\nimport type { Nullable } from '@element-plus/utils'\nimport type {\n  TreeComponentProps,\n  TreeData,\n  TreeKey,\n  TreeNodeData,\n} from './tree.type'\n\nexport default defineComponent({\n  name: 'ElTree',\n  components: { ElTreeNode },\n  props: {\n    data: {\n      type: Array,\n      default: () => [],\n    },\n    emptyText: {\n      type: String,\n    },\n    renderAfterExpand: {\n      type: Boolean,\n      default: true,\n    },\n    nodeKey: String,\n    checkStrictly: Boolean,\n    defaultExpandAll: Boolean,\n    expandOnClickNode: {\n      type: Boolean,\n      default: true,\n    },\n    checkOnClickNode: Boolean,\n    checkOnClickLeaf: {\n      type: Boolean,\n      default: true,\n    },\n    checkDescendants: {\n      type: Boolean,\n      default: false,\n    },\n    autoExpandParent: {\n      type: Boolean,\n      default: true,\n    },\n    defaultCheckedKeys: Array as PropType<\n      TreeComponentProps['defaultCheckedKeys']\n    >,\n    defaultExpandedKeys: Array as PropType<\n      TreeComponentProps['defaultExpandedKeys']\n    >,\n    currentNodeKey: [String, Number] as PropType<string | number>,\n    renderContent: Function,\n    showCheckbox: {\n      type: Boolean,\n      default: false,\n    },\n    draggable: {\n      type: Boolean,\n      default: false,\n    },\n    allowDrag: Function,\n    allowDrop: Function,\n    props: {\n      type: Object as PropType<TreeComponentProps['props']>,\n      default: () => ({\n        children: 'children',\n        label: 'label',\n        disabled: 'disabled',\n      }),\n    },\n    lazy: {\n      type: Boolean,\n      default: false,\n    },\n    highlightCurrent: Boolean,\n    load: Function as PropType<TreeComponentProps['load']>,\n    filterNodeMethod: Function as PropType<\n      TreeComponentProps['filterNodeMethod']\n    >,\n    accordion: Boolean,\n    indent: {\n      type: Number,\n      default: 18,\n    },\n    icon: {\n      type: iconPropType,\n    },\n  },\n  emits: [\n    'check-change',\n    'current-change',\n    'node-click',\n    'node-contextmenu',\n    'node-collapse',\n    'node-expand',\n    'check',\n    'node-drag-start',\n    'node-drag-end',\n    'node-drop',\n    'node-drag-leave',\n    'node-drag-enter',\n    'node-drag-over',\n  ],\n  setup(props, ctx) {\n    const { t } = useLocale()\n    const ns = useNamespace('tree')\n    const selectInfo = inject(selectKey, null)\n\n    const store = ref<TreeStore>(\n      new TreeStore({\n        key: props.nodeKey,\n        data: props.data,\n        lazy: props.lazy,\n        props: props.props,\n        load: props.load,\n        currentNodeKey: props.currentNodeKey,\n        checkStrictly: props.checkStrictly,\n        checkDescendants: props.checkDescendants,\n        defaultCheckedKeys: props.defaultCheckedKeys,\n        defaultExpandedKeys: props.defaultExpandedKeys,\n        autoExpandParent: props.autoExpandParent,\n        defaultExpandAll: props.defaultExpandAll,\n        filterNodeMethod: props.filterNodeMethod,\n      })\n    )\n\n    store.value.initialize()\n\n    const root = ref<Node>(store.value.root)\n    const currentNode = ref<Node>(null)\n    const el$ = ref<Nullable<HTMLElement>>(null)\n    const dropIndicator$ = ref<Nullable<HTMLElement>>(null)\n\n    const { broadcastExpanded } = useNodeExpandEventBroadcast(props)\n\n    const { dragState } = useDragNodeHandler({\n      props,\n      ctx,\n      el$,\n      dropIndicator$,\n      store,\n    })\n\n    useKeydown({ el$ }, store)\n\n    const isEmpty = computed(() => {\n      const { childNodes } = root.value\n      const hasFilteredOptions = selectInfo\n        ? selectInfo.hasFilteredOptions !== 0\n        : false\n      return (\n        (!childNodes ||\n          childNodes.length === 0 ||\n          childNodes.every(({ visible }) => !visible)) &&\n        !hasFilteredOptions\n      )\n    })\n\n    watch(\n      () => props.currentNodeKey,\n      (newVal) => {\n        store.value.setCurrentNodeKey(newVal)\n      }\n    )\n\n    watch(\n      () => props.defaultCheckedKeys,\n      (newVal) => {\n        store.value.setDefaultCheckedKey(newVal)\n      }\n    )\n\n    watch(\n      () => props.defaultExpandedKeys,\n      (newVal) => {\n        store.value.setDefaultExpandedKeys(newVal)\n      }\n    )\n\n    watch(\n      () => props.data,\n      (newVal) => {\n        store.value.setData(newVal)\n      },\n      { deep: true }\n    )\n\n    watch(\n      () => props.checkStrictly,\n      (newVal) => {\n        store.value.checkStrictly = newVal\n      }\n    )\n\n    const filter = (value) => {\n      if (!props.filterNodeMethod)\n        throw new Error('[Tree] filterNodeMethod is required when filter')\n      store.value.filter(value)\n    }\n\n    const getNodeKey = (node: Node) => {\n      return getNodeKeyUtil(props.nodeKey, node.data)\n    }\n\n    const getNodePath = (data: TreeKey | TreeNodeData) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in getNodePath')\n      const node = store.value.getNode(data)\n      if (!node) return []\n      const path = [node.data]\n      let parent = node.parent\n      while (parent && parent !== root.value) {\n        path.push(parent.data)\n        parent = parent.parent\n      }\n      return path.reverse()\n    }\n\n    const getCheckedNodes = (\n      leafOnly?: boolean,\n      includeHalfChecked?: boolean\n    ): TreeNodeData[] => {\n      return store.value.getCheckedNodes(leafOnly, includeHalfChecked)\n    }\n\n    const getCheckedKeys = (leafOnly?: boolean): TreeKey[] => {\n      return store.value.getCheckedKeys(leafOnly)\n    }\n\n    const getCurrentNode = (): TreeNodeData => {\n      const currentNode = store.value.getCurrentNode()\n      return currentNode ? currentNode.data : null\n    }\n\n    const getCurrentKey = (): any => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in getCurrentKey')\n      const currentNode = getCurrentNode()\n      return currentNode ? currentNode[props.nodeKey] : null\n    }\n\n    const setCheckedNodes = (nodes: Node[], leafOnly?: boolean) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCheckedNodes')\n      store.value.setCheckedNodes(nodes, leafOnly)\n    }\n\n    const setCheckedKeys = (keys: TreeKey[], leafOnly?: boolean) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCheckedKeys')\n      store.value.setCheckedKeys(keys, leafOnly)\n    }\n\n    const setChecked = (\n      data: TreeKey | TreeNodeData,\n      checked: boolean,\n      deep: boolean\n    ) => {\n      store.value.setChecked(data, checked, deep)\n    }\n\n    const getHalfCheckedNodes = (): TreeNodeData[] => {\n      return store.value.getHalfCheckedNodes()\n    }\n\n    const getHalfCheckedKeys = (): TreeKey[] => {\n      return store.value.getHalfCheckedKeys()\n    }\n\n    const setCurrentNode = (node: Node, shouldAutoExpandParent = true) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCurrentNode')\n\n      handleCurrentChange(store, ctx.emit, () => {\n        broadcastExpanded(node)\n        store.value.setUserCurrentNode(node, shouldAutoExpandParent)\n      })\n    }\n\n    const setCurrentKey = (key?: TreeKey, shouldAutoExpandParent = true) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCurrentKey')\n\n      handleCurrentChange(store, ctx.emit, () => {\n        broadcastExpanded()\n        store.value.setCurrentNodeKey(key, shouldAutoExpandParent)\n      })\n    }\n\n    const getNode = (data: TreeKey | TreeNodeData): Node => {\n      return store.value.getNode(data)\n    }\n\n    const remove = (data: TreeNodeData | Node) => {\n      store.value.remove(data)\n    }\n\n    const append = (\n      data: TreeNodeData,\n      parentNode: TreeNodeData | TreeKey | Node\n    ) => {\n      store.value.append(data, parentNode)\n    }\n\n    const insertBefore = (\n      data: TreeNodeData,\n      refNode: TreeKey | TreeNodeData | Node\n    ) => {\n      store.value.insertBefore(data, refNode)\n    }\n\n    const insertAfter = (\n      data: TreeNodeData,\n      refNode: TreeKey | TreeNodeData | Node\n    ) => {\n      store.value.insertAfter(data, refNode)\n    }\n\n    const handleNodeExpand = (\n      nodeData: TreeNodeData,\n      node: Node,\n      instance: ComponentInternalInstance\n    ) => {\n      broadcastExpanded(node)\n      ctx.emit('node-expand', nodeData, node, instance)\n    }\n\n    const updateKeyChildren = (key: TreeKey, data: TreeData) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in updateKeyChild')\n      store.value.updateChildren(key, data)\n    }\n\n    provide('RootTree', {\n      ctx,\n      props,\n      store,\n      root,\n      currentNode,\n      instance: getCurrentInstance(),\n    } as any)\n\n    provide(formItemContextKey, undefined)\n\n    return {\n      ns,\n      // ref\n      store,\n      root,\n      currentNode,\n      dragState,\n      el$,\n      dropIndicator$,\n\n      // computed\n      isEmpty,\n\n      // methods\n      filter,\n      getNodeKey,\n      getNodePath,\n      getCheckedNodes,\n      getCheckedKeys,\n      getCurrentNode,\n      getCurrentKey,\n      setCheckedNodes,\n      setCheckedKeys,\n      setChecked,\n      getHalfCheckedNodes,\n      getHalfCheckedKeys,\n      setCurrentNode,\n      setCurrentKey,\n      t,\n      getNode,\n      remove,\n      append,\n      insertBefore,\n      insertAfter,\n      handleNodeExpand,\n      updateKeyChildren,\n    }\n  },\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;AAsEA,MAAKA,SAAA,GAAaC,eAAa;EAC7BC,IAAM;EACNC,UAAA,EAAY;IAAEC;EAAW;EACzBC,KAAO;IACLC,IAAM;MACJC,IAAM,EAAAC,KAAA;MACNC,OAAA,EAASA,CAAA,KAAM;IAAC,CAClB;IACAC,SAAW;MACTH,IAAM,EAAAI;IAAA,CACR;IACAC,iBAAmB;MACjBL,IAAM,EAAAM,OAAA;MACNJ,OAAS;IAAA,CACX;IACAK,OAAS,EAAAH,MAAA;IACTI,aAAe,EAAAF,OAAA;IACfG,gBAAkB,EAAAH,OAAA;IAClBI,iBAAmB;MACjBV,IAAM,EAAAM,OAAA;MACNJ,OAAS;IAAA,CACX;IACAS,gBAAkB,EAAAL,OAAA;IAClBM,gBAAkB;MAChBZ,IAAM,EAAAM,OAAA;MACNJ,OAAS;IAAA,CACX;IACAW,gBAAkB;MAChBb,IAAM,EAAAM,OAAA;MACNJ,OAAS;IAAA,CACX;IACAY,gBAAkB;MAChBd,IAAM,EAAAM,OAAA;MACNJ,OAAS;IAAA,CACX;IACAa,kBAAoB,EAAAd,KAAA;IAGpBe,mBAAqB,EAAAf,KAAA;IAGrBgB,cAAA,EAAgB,CAACb,MAAA,EAAQc,MAAM;IAC/BC,aAAe,EAAAC,QAAA;IACfC,YAAc;MACZrB,IAAM,EAAAM,OAAA;MACNJ,OAAS;IAAA,CACX;IACAoB,SAAW;MACTtB,IAAM,EAAAM,OAAA;MACNJ,OAAS;IAAA,CACX;IACAqB,SAAW,EAAAH,QAAA;IACXI,SAAW,EAAAJ,QAAA;IACXtB,KAAO;MACLE,IAAM,EAAAyB,MAAA;MACNvB,OAAA,EAASA,CAAA,MAAO;QACdwB,QAAU;QACVC,KAAO;QACPC,QAAU;MAAA,CACZ;IAAA,CACF;IACAC,IAAM;MACJ7B,IAAM,EAAAM,OAAA;MACNJ,OAAS;IAAA,CACX;IACA4B,gBAAkB,EAAAxB,OAAA;IAClByB,IAAM,EAAAX,QAAA;IACNY,gBAAkB,EAAAZ,QAAA;IAGlBa,SAAW,EAAA3B,OAAA;IACX4B,MAAQ;MACNlC,IAAM,EAAAkB,MAAA;MACNhB,OAAS;IAAA,CACX;IACAiC,IAAM;MACJnC,IAAM,EAAAoC;IAAA;EACR,CACF;EACAC,KAAO,GACL,gBACA,kBACA,cACA,oBACA,iBACA,eACA,SACA,mBACA,iBACA,aACA,mBACA,mBACA,iBACF;EACAC,MAAMxC,KAAA,EAAOyC,GAAK;IACV;MAAEC;IAAE,IAAIC,SAAU;IAClB,MAAAC,EAAA,GAAKC,YAAA,CAAa,MAAM;IACxB,MAAAC,UAAA,GAAaC,MAAO,CAAAC,SAAA,EAAW,IAAI;IAEzC,MAAMC,KAAQ,GAAAC,GAAA,KAAAC,SAAA;MACZC,GAAA,EAAcpD,KAAA,CAAAS,OAAA;MAAAR,IAAA,EAAAD,KACD,CAAAC,IAAA;MAAA8B,IAAA,EAAA/B,KACC,CAAA+B,IAAA;MAAA/B,KAAA,EAAAA,KACA,CAAAA,KAAA;MAAAiC,IAAA,EAAAjC,KACC,CAAAiC,IAAA;MAAAd,cACD,EAAAnB,KAAA,CAAAmB,cAAA;MAAAT,aAAA,EAAAV,KACU,CAAAU,aAAA;MAAAK,gBAAA,EACDf,KAAA,CAAAe,gBAAA;MAAAE,kBAAA,EACHjB,KAAM,CAAAiB,kBAAA;MAAAC,mBAAA,EAAAlB,KACE,CAAAkB,mBAAA;MAAAF,gBAAA,EAAAhB,KACL,CAAMgB,gBAAA;MAAAL,gBAAA,EAAAX,KACH,CAAAW,gBAAA;MAAAuB,gBAAA,EAAAlC,KACA,CAAAkC;IAAA;IACAe,KACzB,CAAAI,KAAA,CAAAC,UAAA;IACH,MAAAC,IAAA,GAAAL,GAAA,CAAAD,KAAA,CAAAI,KAAA,CAAAE,IAAA;IAEA,MAAMC,WAAiB,GAAAN,GAAA;IAEvB,MAAMO,GAAO,GAAAP,GAAA,KAAgB;IACvB,MAAAQ,cAAc,GAAAR,GAAc;IAC5B;MAAAS;IAAqC,IAAAC,2BAAA,CAAA5D,KAAA;IACrC;MAAA6D;IAAA,IAAAC,kBAAgD;MAEtD9D,KAAQ;MAEFyC,GAAA;MACJgB,GAAA;MACAC,cAAA;MACAT;IAAA,CACA;IACAc,UAAA;MAAAN;IAAA,GAAAR,KAAA;IACF,MAACe,OAAA,GAAAC,QAAA;MAEU;QAAAC;MAAS,CAAK,GAAAX,IAAA,CAAAF,KAAA;MAEnB,MAAAc,kBAAyB,GAAArB,UAAA,GAAAA,UAAA,CAAAqB,kBAAA;MACvB,QAAE,CAAWD,UAAA,IAAIA,UAAK,CAAAE,MAAA,UAAAF,UAAA,CAAAG,KAAA;QAAAC;MAAA,OAAAA,OAAA,OAAAH,kBAAA;IAC5B;IAGAI,KAAA,OACIvE,KAAA,CAAAmB,cACW,EAAAqD,MAAA;MAIhBvB,KAAA,CAAAI,KAAA,CAAAoB,iBAAA,CAAAD,MAAA;IAED;IAAAD,KAAA,OACcvE,KAAA,CAAAiB,kBAAA,EAAAuD,MAAA;MACZvB,KAAY,CAAAI,KAAA,CAAAqB,oBAAA,CAAAF,MAAA;IACV,CAAM;IACRD,KAAA,OAAAvE,KAAA,CAAAkB,mBAAA,EAAAsD,MAAA;MACFvB,KAAA,CAAAI,KAAA,CAAAsB,sBAAA,CAAAH,MAAA;IAEA;IAAAD,KAAA,OACcvE,KAAA,CAAAC,IAAA,EAAAuE,MAAA;MACZvB,KAAY,CAAAI,KAAA,CAAAuB,OAAA,CAAAJ,MAAA;IACV,CAAM;MAAAK,IAAA;IAAA;IACRN,KAAA,OAAAvE,KAAA,CAAAU,aAAA,EAAA8D,MAAA;MACFvB,KAAA,CAAAI,KAAA,CAAA3C,aAAA,GAAA8D,MAAA;IAEA;IAAA,MAAAM,MACc,GAAAzB,KAAA;MACZ,IAAY,CAAArD,KAAA,CAAAkC,gBAAA,EACJ,UAAA6C,KAAA,kDAAmC;MAC3C9B,KAAA,CAAAI,KAAA,CAAAyB,MAAA,CAAAzB,KAAA;IAAA,CACF;IAEA,MAAA2B,YAAA,GAAAC,IAAA;MACE,OAAYC,UAAA,CAAAlF,KAAA,CAAAS,OAAA,EAAAwE,IAAA,CAAAhF,IAAA;IAAA;IAEJ,MAAAkF,WAAA,GAAAlF,IAAoB;MAC5B,KAAAD,KAAA,CAAAS,OAAA,EACE,MAAM,IAAKsE,KAAA;MACf,MAAAE,IAAA,GAAAhC,KAAA,CAAAI,KAAA,CAAA+B,OAAA,CAAAnF,IAAA;MAEA,KAAAgF,IAAA,SACc;MACZ,MAAYI,IAAA,IAAAJ,IAAA,CAAAhF,IAAA;MACV,IAAAqF,MAAA,GAAAL,IAA4B,CAAAK,MAAA;MAC9B,OAAAA,MAAA,IAAAA,MAAA,KAAA/B,IAAA,CAAAF,KAAA;QACFgC,IAAA,CAAAE,IAAA,CAAAD,MAAA,CAAArF,IAAA;QAEMqF,MAAA,GAAAA,MAAoB,CAAAA,MAAA;MACxB;MACQ,OAAAD,IAAA,CAAIG,OAAuD;IACnE,CAAM;IACR,MAAAC,eAAA,GAAAA,CAAAC,QAAA,EAAAC,kBAAA;MAEM,OAAA1C,KAAA,CAAAI,KAAc,CAAeoC,eAAA,CAAAC,QAAA,EAAAC,kBAAA;IACjC;IACF,MAAAC,cAAA,GAAAF,QAAA;MAEM,OAAAzC,KAAA,CAAAI,KAAc,CAACuC,cAAiC,CAAAF,QAAA;IACpD;IACQ,MAAAG,cAAU,GAA2CA,CAAA;MAC7D,MAAMC,YAAO,GAAY7C,KAAA,CAAAI,KAAA,CAAQwC,cAAI;MACjC,OAAOC,YAAQ,GAAAA,YAAA,CAAA7F,IAAA;IACnB,CAAM;IACN,MAAI8F,aAAc,GAAAA,CAAA;MACX,KAAA/F,KAAA,CAAAS,OAAqB,EACrB,UAAKsE,KAAA,8CAAW;MACrB,MAAAe,YAAgB,GAAAD,cAAA;MAClB,OAAAC,YAAA,GAAAA,YAAA,CAAA9F,KAAA,CAAAS,OAAA;IACA;IACF,MAAAuF,eAAA,GAAAA,CAAAC,KAAA,EAAAP,QAAA;MAEM,KAAA1F,KAAA,CAAAS,OAAA,EAIJ,MAAa,IAAAsE,KAAA,gDAAkD;MACjE9B,KAAA,CAAAI,KAAA,CAAA2C,eAAA,CAAAC,KAAA,EAAAP,QAAA;IAEA,CAAM;IACG,MAAAQ,cAAY,GAAAA,CAAAC,IAAA,EAAAT,QAAuB;MAC5C,KAAA1F,KAAA,CAAAS,OAAA,EAEA,UAAAsE,KAAA,+CAA2C;MACnC9B,KAAA,CAAAI,KAAA,CAAA6C,cAAoB,CAAAC,IAAA,EAAMT,QAAe;IAC/C,CAAO;IACT,MAAAU,UAAA,GAAAA,CAAAnG,IAAA,EAAAoG,OAAA,EAAAxB,IAAA;MAEA5B,KAAA,CAAAI,KAAA,CAAA+C,UAAiC,CAAAnG,IAAA,EAAAoG,OAAA,EAAAxB,IAAA;IAC/B;IACQ,MAAAyB,mBAAuD,GAAAA,CAAA;MAC/D,OAAArD,KAAA,CAAAI,KAAA,CAAAiD,mBAAmC;IACnC;IACF,MAAAC,kBAAA,GAAAA,CAAA;MAEM,OAAAtD,KAAA,CAAAI,KAAA,CAAAkD,kBAAyD;IAC7D;IACQ,MAAAC,cAAU,GAA+CA,CAAAvB,IAAA,EAAAwB,sBAAA;MAC3D,KAAAzG,KAAA,CAAAS,OAAsB,EAC9B,UAAAsE,KAAA;MAEM2B,mBAAA,CAAAzD,KAAkB,EAAAR,GAAwC,CAAAkE,IAAA;QAC9DhD,iBAAW,CAAAsB,IAAA;QACHhC,KAAA,CAAAI,KAAA,CAAAuD,kBAAwD,CAAA3B,IAAA,EAAAwB,sBAAA;MAChE,CAAM;IAAmC,CAC3C;IAEA,MAAMI,aAAa,GAEjBA,CAAAzD,GAAA,EAAAqD,sBAEG;MACH,KAAAzG,KAAY,CAAAS,OAAA,EACd,UAAAsE,KAAA;MAEA2B,mBAAA,CAAAzD,KAAA,EAAkDR,GAAA,CAAAkE,IAAA;QACzChD,iBAAY,EAAoB;QACzCV,KAAA,CAAAI,KAAA,CAAAoB,iBAAA,CAAArB,GAAA,EAAAqD,sBAAA;MAEA;IACE,CAAO;IACT,MAAArB,OAAA,GAAAnF,IAAA;MAEA,OAAuBgD,KAAA,CAAAI,KAAA,CAAA+B,OAAa,CAAAnF,IAAA;IAClC;IACQ,MAAA6G,MAAA,GAAA7G,IAAU,IAA8C;MAE5CgD,KAAA,CAAAI,KAAA,CAAAyD,MAAA,CAAA7G,IAAA;IAClB;IACM,MAAA8G,MAAA,GAAAA,CAAM9G,IAAmB,EAAA+G,UAAA;MACjC/D,KAAC,CAAAI,KAAA,CAAA0D,MAAA,CAAA9G,IAAA,EAAA+G,UAAA;IAAA,CACH;IAEA,MAAMC,YAAgB,GAAAA,CAAAhH,IAAgB,EAAAiH,OAAA;MACpCjE,KAAK,CAAMI,KAAA,CAAA4D,YAAA,CAAAhH,IAAA,EAAAiH,OAAA;IACT,CAAM;IAEY,MAAAC,WAAA,GAAAA,CAAAlH,IAAA,EAAAiH,OAAW,KAAM;MACjBjE,KAAA,CAAAI,KAAA,CAAA8D,WAAA,CAAAlH,IAAA,EAAAiH,OAAA;IAClB,CAAM;IAAmD,MAC1DE,gBAAA,GAAAA,CAAAC,QAAA,EAAApC,IAAA,EAAAqC,QAAA;MACH3D,iBAAA,CAAAsB,IAAA;MAEMxC,GAAA,CAAAkE,IAAA,cAAkD,EAAAU,QAAA,EAAApC,IAAA,EAAAqC,QAAA;IACtD,CAAO;IACT,MAAAC,iBAAA,GAAAA,CAAAnE,GAAA,EAAAnD,IAAA;MAEM,KAAAD,KAAA,CAAAS,OAAwC,EACtC,UAAMsE,KAAA,+CAAW;MACzB9B,KAAA,CAAAI,KAAA,CAAAmE,cAAA,CAAApE,GAAA,EAAAnD,IAAA;IAEA,CAAM;IAIEwH,OAAA,WAAa;MACrBhF,GAAA;MAEMzC,KAAA;MAIEiD,KAAA;MACRM,IAAA;MAEMC,WAAA;MAIE8D,QAAA,EAAAI,kBAAkB;IAAa,CACvC;IAEAD,OAAyB,CAAAE,kBACvB,EACA;IAGA;MACA/E,EAAA;MACFK,KAAA;MAEMM,IAAA;MACJC,WAAW;MACHK,SAAA;MACFJ,GAAA;MACRC,cAAA;MAEAM,OAAoB;MAClBc,MAAA;MAAAI,UAAA,EACAF,YAAA;MACAG,WAAA;MACAM,eAAA;MACAG,cAAA;MACAC,cAA6B;MACvBE,aAAA;MAERC,eAAA;MAEOE,cAAA;MACLE,UAAA;MAAAE,mBAAA;MAEAC,kBAAA;MACAC,cAAA;MACAK,aAAA;MACAnE,CAAA;MACA0C,OAAA;MACA0B,MAAA;MAAAC,MAAA;MAGAE,YAAA;MAAAE,WAAA;MAGAC,gBAAA;MACAG;IAAA,CACA;EAAA;AACA,CACA;AACA,SACAK,YAAAC,IAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,QAAA;EACA,MAAAC,uBAAA,GAAAC,gBAAA;EACA,OAAAC,SAAA,IAAAC,kBAAA;IACApF,GAAA;IACAqF,KAAA,EAAAC,cAAA,EACAX,IAAA,CAAAjF,EAAA,CAAA6F,CAAA,IACAZ,IAAA,CAAAjF,EAAA,CAAA8F,EAAA,eAAAb,IAAA,CAAAhE,SAAA,CAAA8E,YAAA,GACAd,IAAA,CAAAjF,EAAA,CAAA8F,EAAA,oBAAAb,IAAA,CAAAhE,SAAA,CAAAnC,SAAA,GACAmG,IAAA,CAAAjF,EAAA,CAAA8F,EAAA,eAAAb,IAAA,CAAAhE,SAAA,CAAA+E,QAAA,eACA;MAAA,CAAAf,IAAA,CAAAjF,EAAA,CAAAiG,CAAA,wBAAAhB,IAAA,CAAA7F;IAAA,EACA;IACA8G,IAAA;EAAA,CACA,IACAT,SAAA,QAAAC,kBAAA,CAAAS,QAAA,QAAAC,UAAA,CAAAnB,IAAA,CAAAtE,IAAA,CAAAW,UAAA,EAAA+E,KAAA;IACA,OAAAZ,SAAA,IAAAa,WAAA,CAAAf,uBAAA;MACA/E,GAAA,EAAAyE,IAAA,CAAA3C,UAAA,CAAA+D,KAAA;MACFhE,IAAA,EAAAgE,KAAA;MACFjJ,KAAA,EAAA6H,IAAA,CAAA7H,KAAA;MACDmC,SAAA,EAAA0F,IAAA,CAAA1F,SAAA;;;;MA1bCgH,YAAA,EAAAtB,IAAA,CAAAT;IAAA,CAkCM;EAAA,WAAAS,IAjCA,CAAA7D,OAAA,IAAAqE,SAAA,IAAAC,kBAAA;IACHlF,GAAK;IAAAmF,KAAA,EAAAC,cAAc,CAAAX,IAAA,CAAAjF,EAAA,CAAAwG,CAAA;EAAA,IAAoDC,UAAY,CAAAxB,IAAsB,CAAAyB,MAAA;IAA4B,IAAGC,EAAA;IAA2D,QAA4CC,kBAAA;MAO3OjB,KAAA,EAAAC,cAAA,CAAAX,IAAA,CAAAjF,EAAA,CAAAwG,CAAA;IAAA,GAAAK,eAAA,EAAAF,EAAA,GAAA1B,IAAA,CAAAxH,SAAA,YAAAkJ,EAAA,GAAA1B,IAAA,CAAAnF,CAAA,2B;EAEL,GAUE,QAAAgH,kBAAA,gBAAAC,cAAA,CAAAH,kBAAA;IATgBtG,GAAA;8BAShB,CAAAN,EAAA,CAAAwG,CAAA;EAAA,CARC,YAAK,CAAgB,CAAAQ,KACf,EAAA/B,IAAA,CAAAhE,SAAA,CAAAgG,iBAAA,IACC;AACI;AAEI,IAAAC,IAAA,GACC,eAAAC,WAAA,CAAApK,SAAA,cAAAiI,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}