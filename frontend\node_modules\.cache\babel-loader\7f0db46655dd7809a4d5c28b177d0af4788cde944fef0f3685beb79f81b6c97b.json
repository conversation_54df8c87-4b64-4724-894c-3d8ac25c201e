{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"room-list\"\n};\nconst _hoisted_2 = {\n  class: \"page-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  class: \"filter-section\"\n};\nconst _hoisted_5 = {\n  class: \"filter-container\"\n};\nconst _hoisted_6 = {\n  class: \"filter-item\"\n};\nconst _hoisted_7 = {\n  class: \"filter-item\"\n};\nconst _hoisted_8 = {\n  class: \"filter-item\"\n};\nconst _hoisted_9 = {\n  key: 0,\n  class: \"loading-container\"\n};\nconst _hoisted_10 = {\n  key: 1,\n  class: \"empty-container\"\n};\nconst _hoisted_11 = {\n  key: 2,\n  class: \"room-grid\"\n};\nconst _hoisted_12 = {\n  class: \"room-header\"\n};\nconst _hoisted_13 = {\n  class: \"room-info\"\n};\nconst _hoisted_14 = {\n  class: \"info-item\"\n};\nconst _hoisted_15 = {\n  class: \"info-item\"\n};\nconst _hoisted_16 = {\n  class: \"info-item\"\n};\nconst _hoisted_17 = {\n  class: \"info-item\"\n};\nconst _hoisted_18 = {\n  class: \"room-footer\"\n};\nconst _hoisted_19 = {\n  class: \"seat-availability\"\n};\nconst _hoisted_20 = {\n  class: \"progress-label\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_Location = _resolveComponent(\"Location\");\n  const _component_OfficeBuilding = _resolveComponent(\"OfficeBuilding\");\n  const _component_User = _resolveComponent(\"User\");\n  const _component_Clock = _resolveComponent(\"Clock\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[5] || (_cache[5] = _createElementVNode(\"h2\", null, \"自习室列表\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_input, {\n    modelValue: $setup.searchQuery,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.searchQuery = $event),\n    placeholder: \"搜索自习室\",\n    \"prefix-icon\": $setup.Search,\n    clearable: \"\",\n    onClear: $setup.handleSearch,\n    onInput: $setup.handleSearch,\n    class: \"search-input\"\n  }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\", \"onClear\", \"onInput\"]), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.refreshRooms\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Refresh)]),\n      _: 1 /* STABLE */\n    }), _cache[4] || (_cache[4] = _createTextVNode(\" 刷新 \"))]),\n    _: 1 /* STABLE */,\n    __: [4]\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_card, {\n    shadow: \"never\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[6] || (_cache[6] = _createElementVNode(\"span\", {\n      class: \"filter-label\"\n    }, \"楼层：\", -1 /* HOISTED */)), _createVNode(_component_el_select, {\n      modelValue: $setup.filters.floor,\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.filters.floor = $event),\n      placeholder: \"全部楼层\",\n      clearable: \"\",\n      onChange: $setup.handleFilterChange\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.floorOptions, floor => {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: floor.value,\n          label: floor.label,\n          value: floor.value\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]), _createElementVNode(\"div\", _hoisted_7, [_cache[7] || (_cache[7] = _createElementVNode(\"span\", {\n      class: \"filter-label\"\n    }, \"状态：\", -1 /* HOISTED */)), _createVNode(_component_el_select, {\n      modelValue: $setup.filters.status,\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.filters.status = $event),\n      placeholder: \"全部状态\",\n      clearable: \"\",\n      onChange: $setup.handleFilterChange\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.statusOptions, status => {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: status.value,\n          label: status.label,\n          value: status.value\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]), _createElementVNode(\"div\", _hoisted_8, [_cache[8] || (_cache[8] = _createElementVNode(\"span\", {\n      class: \"filter-label\"\n    }, \"排序：\", -1 /* HOISTED */)), _createVNode(_component_el_select, {\n      modelValue: $setup.sortBy,\n      \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.sortBy = $event),\n      placeholder: \"排序方式\",\n      onChange: $setup.handleSortChange\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.sortOptions, option => {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: option.value,\n          label: option.label,\n          value: option.value\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])])])]),\n    _: 1 /* STABLE */\n  })]), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createVNode(_component_el_skeleton, {\n    rows: 3,\n    animated: \"\"\n  }), _createVNode(_component_el_skeleton, {\n    rows: 3,\n    animated: \"\",\n    style: {\n      \"margin-top\": \"20px\"\n    }\n  })])) : $setup.filteredRooms.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createVNode(_component_el_empty, {\n    description: \"没有找到符合条件的自习室\"\n  })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.filteredRooms, room => {\n    return _openBlock(), _createBlock(_component_el_card, {\n      key: room.id,\n      class: _normalizeClass([\"room-card\", {\n        'room-closed': room.status !== 'open'\n      }]),\n      onClick: $event => $setup.viewRoomDetail(room)\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"h3\", null, _toDisplayString(room.name), 1 /* TEXT */), _createVNode(_component_el_tag, {\n        type: $setup.getStatusType(room.status)\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText(room.status)), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Location)]),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"span\", null, _toDisplayString(room.location), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_OfficeBuilding)]),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"span\", null, _toDisplayString(room.floor) + \"楼\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_User)]),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"span\", null, \"容量: \" + _toDisplayString(room.capacity) + \"座\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_17, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Clock)]),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"span\", null, \"开放时间: \" + _toDisplayString($setup.formatTime(room.open_time)) + \" - \" + _toDisplayString($setup.formatTime(room.close_time)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_cache[9] || (_cache[9] = _createElementVNode(\"span\", null, \"可用座位\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString(room.available_seats) + \"/\" + _toDisplayString(room.capacity), 1 /* TEXT */)]), _createVNode(_component_el_progress, {\n        percentage: room.available_seats / room.capacity * 100,\n        format: $setup.formatAvailability,\n        \"stroke-width\": 10,\n        color: $setup.getAvailabilityColor(room.available_seats, room.capacity)\n      }, null, 8 /* PROPS */, [\"percentage\", \"format\", \"color\"])]), _createVNode(_component_el_button, {\n        type: \"primary\",\n        size: \"small\",\n        disabled: room.status !== 'open'\n      }, {\n        default: _withCtx(() => [...(_cache[10] || (_cache[10] = [_createTextVNode(\" 查看座位 \")]))]),\n        _: 2 /* DYNAMIC */,\n        __: [10]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"disabled\"])])]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"class\", \"onClick\"]);\n  }), 128 /* KEYED_FRAGMENT */))]))]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_input", "modelValue", "$setup", "searchQuery", "_cache", "$event", "placeholder", "Search", "clearable", "onClear", "handleSearch", "onInput", "_component_el_button", "type", "onClick", "refreshRooms", "default", "_withCtx", "_component_el_icon", "_component_Refresh", "_", "_createTextVNode", "__", "_hoisted_4", "_component_el_card", "shadow", "_hoisted_5", "_hoisted_6", "_component_el_select", "filters", "floor", "onChange", "handleFilterChange", "_Fragment", "_renderList", "floorOptions", "_createBlock", "_component_el_option", "value", "label", "_hoisted_7", "status", "statusOptions", "_hoisted_8", "sortBy", "handleSortChange", "sortOptions", "option", "loading", "_hoisted_9", "_component_el_skeleton", "rows", "animated", "style", "filteredRooms", "length", "_hoisted_10", "_component_el_empty", "description", "_hoisted_11", "room", "id", "_normalizeClass", "viewRoomDetail", "_hoisted_12", "_toDisplayString", "name", "_component_el_tag", "getStatusType", "getStatusText", "_hoisted_13", "_hoisted_14", "_component_Location", "location", "_hoisted_15", "_component_OfficeBuilding", "_hoisted_16", "_component_User", "capacity", "_hoisted_17", "_component_Clock", "formatTime", "open_time", "close_time", "_hoisted_18", "_hoisted_19", "_hoisted_20", "available_seats", "_component_el_progress", "percentage", "format", "formatAvailability", "color", "getAvailabilityColor", "size", "disabled"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\RoomList.vue"], "sourcesContent": ["<template>\n  <div class=\"room-list\">\n    <div class=\"page-header\">\n      <h2>自习室列表</h2>\n      <div class=\"header-actions\">\n        <el-input\n          v-model=\"searchQuery\"\n          placeholder=\"搜索自习室\"\n          :prefix-icon=\"Search\"\n          clearable\n          @clear=\"handleSearch\"\n          @input=\"handleSearch\"\n          class=\"search-input\"\n        />\n        <el-button type=\"primary\" @click=\"refreshRooms\">\n          <el-icon><Refresh /></el-icon>\n          刷新\n        </el-button>\n      </div>\n    </div>\n\n    <div class=\"filter-section\">\n      <el-card shadow=\"never\">\n        <div class=\"filter-container\">\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">楼层：</span>\n            <el-select\n              v-model=\"filters.floor\"\n              placeholder=\"全部楼层\"\n              clearable\n              @change=\"handleFilterChange\"\n            >\n              <el-option\n                v-for=\"floor in floorOptions\"\n                :key=\"floor.value\"\n                :label=\"floor.label\"\n                :value=\"floor.value\"\n              />\n            </el-select>\n          </div>\n\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">状态：</span>\n            <el-select\n              v-model=\"filters.status\"\n              placeholder=\"全部状态\"\n              clearable\n              @change=\"handleFilterChange\"\n            >\n              <el-option\n                v-for=\"status in statusOptions\"\n                :key=\"status.value\"\n                :label=\"status.label\"\n                :value=\"status.value\"\n              />\n            </el-select>\n          </div>\n\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">排序：</span>\n            <el-select v-model=\"sortBy\" placeholder=\"排序方式\" @change=\"handleSortChange\">\n              <el-option\n                v-for=\"option in sortOptions\"\n                :key=\"option.value\"\n                :label=\"option.label\"\n                :value=\"option.value\"\n              />\n            </el-select>\n          </div>\n        </div>\n      </el-card>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"3\" animated />\n      <el-skeleton :rows=\"3\" animated style=\"margin-top: 20px\" />\n    </div>\n\n    <div v-else-if=\"filteredRooms.length === 0\" class=\"empty-container\">\n      <el-empty description=\"没有找到符合条件的自习室\" />\n    </div>\n\n    <div v-else class=\"room-grid\">\n      <el-card\n        v-for=\"room in filteredRooms\"\n        :key=\"room.id\"\n        class=\"room-card\"\n        :class=\"{ 'room-closed': room.status !== 'open' }\"\n        @click=\"viewRoomDetail(room)\"\n      >\n        <div class=\"room-header\">\n          <h3>{{ room.name }}</h3>\n          <el-tag :type=\"getStatusType(room.status)\">\n            {{ getStatusText(room.status) }}\n          </el-tag>\n        </div>\n\n        <div class=\"room-info\">\n          <div class=\"info-item\">\n            <el-icon><Location /></el-icon>\n            <span>{{ room.location }}</span>\n          </div>\n\n          <div class=\"info-item\">\n            <el-icon><OfficeBuilding /></el-icon>\n            <span>{{ room.floor }}楼</span>\n          </div>\n\n          <div class=\"info-item\">\n            <el-icon><User /></el-icon>\n            <span>容量: {{ room.capacity }}座</span>\n          </div>\n\n          <div class=\"info-item\">\n            <el-icon><Clock /></el-icon>\n            <span\n              >开放时间: {{ formatTime(room.open_time) }} - {{ formatTime(room.close_time) }}</span\n            >\n          </div>\n        </div>\n\n        <div class=\"room-footer\">\n          <div class=\"seat-availability\">\n            <div class=\"progress-label\">\n              <span>可用座位</span>\n              <span>{{ room.available_seats }}/{{ room.capacity }}</span>\n            </div>\n            <el-progress\n              :percentage=\"(room.available_seats / room.capacity) * 100\"\n              :format=\"formatAvailability\"\n              :stroke-width=\"10\"\n              :color=\"getAvailabilityColor(room.available_seats, room.capacity)\"\n            />\n          </div>\n\n          <el-button type=\"primary\" size=\"small\" :disabled=\"room.status !== 'open'\">\n            查看座位\n          </el-button>\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted, reactive } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { Search, Refresh, Location, OfficeBuilding, User, Clock } from \"@element-plus/icons-vue\";\n\nexport default {\n  name: \"RoomList\",\n  components: {\n    Refresh,\n    Location,\n    OfficeBuilding,\n    User,\n    Clock,\n  },\n  setup() {\n    const store = useStore();\n    const router = useRouter();\n\n    const loading = ref(true);\n    const searchQuery = ref(\"\");\n    const sortBy = ref(\"floor\");\n\n    const filters = reactive({\n      floor: \"\",\n      status: \"\",\n    });\n\n    // 楼层选项\n    const floorOptions = [\n      { value: 1, label: \"1楼\" },\n      { value: 2, label: \"2楼\" },\n      { value: 3, label: \"3楼\" },\n      { value: 4, label: \"4楼\" },\n      { value: 5, label: \"5楼\" },\n    ];\n\n    // 状态选项\n    const statusOptions = [\n      { value: \"open\", label: \"开放中\" },\n      { value: \"closed\", label: \"已关闭\" },\n      { value: \"maintenance\", label: \"维护中\" },\n    ];\n\n    // 排序选项\n    const sortOptions = [\n      { value: \"floor\", label: \"按楼层排序\" },\n      { value: \"name\", label: \"按名称排序\" },\n      { value: \"capacity\", label: \"按容量排序\" },\n      { value: \"available\", label: \"按可用座位排序\" },\n    ];\n\n    // 获取自习室列表\n    const getRooms = async () => {\n      try {\n        loading.value = true;\n        await store.dispatch(\"seat/getRooms\");\n      } catch (error) {\n        ElMessage.error(\"获取自习室列表失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 刷新自习室列表\n    const refreshRooms = () => {\n      getRooms();\n    };\n\n    // 过滤后的自习室列表\n    const filteredRooms = computed(() => {\n      let result = store.getters[\"seat/rooms\"];\n\n      // 搜索过滤\n      if (searchQuery.value) {\n        const query = searchQuery.value.toLowerCase();\n        result = result.filter(\n          (room) =>\n            room.name.toLowerCase().includes(query) || room.location.toLowerCase().includes(query)\n        );\n      }\n\n      // 楼层过滤\n      if (filters.floor) {\n        result = result.filter((room) => room.floor === filters.floor);\n      }\n\n      // 状态过滤\n      if (filters.status) {\n        result = result.filter((room) => room.status === filters.status);\n      }\n\n      // 排序\n      result = [...result].sort((a, b) => {\n        switch (sortBy.value) {\n          case \"name\":\n            return a.name.localeCompare(b.name);\n          case \"capacity\":\n            return b.capacity - a.capacity;\n          case \"available\":\n            return b.available_seats / b.capacity - a.available_seats / a.capacity;\n          case \"floor\":\n          default:\n            return a.floor - b.floor || a.name.localeCompare(b.name);\n        }\n      });\n\n      return result;\n    });\n\n    // 处理搜索\n    const handleSearch = () => {\n      // 搜索逻辑已在计算属性中实现\n    };\n\n    // 处理过滤变化\n    const handleFilterChange = () => {\n      // 过滤逻辑已在计算属性中实现\n    };\n\n    // 处理排序变化\n    const handleSortChange = () => {\n      // 排序逻辑已在计算属性中实现\n    };\n\n    // 查看自习室详情\n    const viewRoomDetail = (room) => {\n      if (room.status !== \"open\") {\n        ElMessage.warning(\"该自习室当前不开放\");\n        return;\n      }\n\n      router.push(`/seat/map?roomId=${room.id}`);\n    };\n\n    // 格式化时间\n    const formatTime = (timeString) => {\n      if (!timeString) return \"\";\n\n      // 时间格式为 \"HH:MM:SS\"，只显示 \"HH:MM\"\n      return timeString.substring(0, 5);\n    };\n\n    // 获取状态类型\n    const getStatusType = (status) => {\n      switch (status) {\n        case \"open\":\n          return \"success\";\n        case \"closed\":\n          return \"danger\";\n        case \"maintenance\":\n          return \"warning\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取状态文本\n    const getStatusText = (status) => {\n      switch (status) {\n        case \"open\":\n          return \"开放中\";\n        case \"closed\":\n          return \"已关闭\";\n        case \"maintenance\":\n          return \"维护中\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化可用座位百分比\n    const formatAvailability = () => {\n      return \"\";\n    };\n\n    // 获取可用座位颜色\n    const getAvailabilityColor = (available, total) => {\n      const percentage = (available / total) * 100;\n      if (percentage <= 20) return \"#f56c6c\";\n      if (percentage <= 50) return \"#e6a23c\";\n      return \"#67c23a\";\n    };\n\n    onMounted(() => {\n      getRooms();\n    });\n\n    return {\n      loading,\n      searchQuery,\n      filters,\n      sortBy,\n      floorOptions,\n      statusOptions,\n      sortOptions,\n      filteredRooms,\n      refreshRooms,\n      handleSearch,\n      handleFilterChange,\n      handleSortChange,\n      viewRoomDetail,\n      formatTime,\n      getStatusType,\n      getStatusText,\n      formatAvailability,\n      getAvailabilityColor,\n      Search,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.room-list {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  h2 {\n    margin: 0;\n  }\n\n  .header-actions {\n    display: flex;\n    gap: 10px;\n\n    .search-input {\n      width: 250px;\n    }\n  }\n}\n\n.filter-section {\n  margin-bottom: 20px;\n\n  .filter-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 20px;\n\n    .filter-item {\n      display: flex;\n      align-items: center;\n\n      .filter-label {\n        margin-right: 10px;\n        white-space: nowrap;\n      }\n    }\n  }\n}\n\n.loading-container,\n.empty-container {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.room-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.room-card {\n  cursor: pointer;\n  transition: transform 0.3s, box-shadow 0.3s;\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n  }\n\n  &.room-closed {\n    opacity: 0.7;\n\n    &:hover {\n      transform: none;\n    }\n  }\n\n  .room-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n\n    h3 {\n      margin: 0;\n      font-size: 18px;\n    }\n  }\n\n  .room-info {\n    margin-bottom: 15px;\n\n    .info-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 8px;\n\n      .el-icon {\n        margin-right: 8px;\n        color: #909399;\n      }\n    }\n  }\n\n  .room-footer {\n    .seat-availability {\n      margin-bottom: 15px;\n\n      .progress-label {\n        display: flex;\n        justify-content: space-between;\n        margin-bottom: 5px;\n        font-size: 14px;\n        color: #606266;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAgB;;EAiBxBA,KAAK,EAAC;AAAgB;;EAElBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAa;;EAiBnBA,KAAK,EAAC;AAAa;;EAiBnBA,KAAK,EAAC;AAAa;;EA1DlCC,GAAA;EAyEwBD,KAAK,EAAC;;;EAzE9BC,GAAA;EA8EgDD,KAAK,EAAC;;;EA9EtDC,GAAA;EAkFgBD,KAAK,EAAC;;;EAQTA,KAAK,EAAC;AAAa;;EAOnBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAW;;EAKjBA,KAAK,EAAC;AAAW;;EAKjBA,KAAK,EAAC;AAAW;;EAKjBA,KAAK,EAAC;AAAW;;EAQnBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAgB;;;;;;;;;;;;;;;;;uBA1HrCE,mBAAA,CA4IM,OA5INC,UA4IM,GA3IJC,mBAAA,CAiBM,OAjBNC,UAiBM,G,0BAhBJD,mBAAA,CAAc,YAAV,OAAK,sBACTA,mBAAA,CAcM,OAdNE,UAcM,GAbJC,YAAA,CAQEC,mBAAA;IAbVC,UAAA,EAMmBC,MAAA,CAAAC,WAAW;IAN9B,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAMmBH,MAAA,CAAAC,WAAW,GAAAE,MAAA;IACpBC,WAAW,EAAC,OAAO;IAClB,aAAW,EAAEJ,MAAA,CAAAK,MAAM;IACpBC,SAAS,EAAT,EAAS;IACRC,OAAK,EAAEP,MAAA,CAAAQ,YAAY;IACnBC,OAAK,EAAET,MAAA,CAAAQ,YAAY;IACpBlB,KAAK,EAAC;gFAERO,YAAA,CAGYa,oBAAA;IAHDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEZ,MAAA,CAAAa;;IAd1CC,OAAA,EAAAC,QAAA,CAeU,MAA8B,CAA9BlB,YAAA,CAA8BmB,kBAAA;MAfxCF,OAAA,EAAAC,QAAA,CAemB,MAAW,CAAXlB,YAAA,CAAWoB,kBAAA,E;MAf9BC,CAAA;kCAAAC,gBAAA,CAewC,MAEhC,G;IAjBRD,CAAA;IAAAE,EAAA;sCAqBI1B,mBAAA,CAkDM,OAlDN2B,UAkDM,GAjDJxB,YAAA,CAgDUyB,kBAAA;IAhDDC,MAAM,EAAC;EAAO;IAtB7BT,OAAA,EAAAC,QAAA,CAuBQ,MA8CM,CA9CNrB,mBAAA,CA8CM,OA9CN8B,UA8CM,GA7CJ9B,mBAAA,CAeM,OAfN+B,UAeM,G,0BAdJ/B,mBAAA,CAAqC;MAA/BJ,KAAK,EAAC;IAAc,GAAC,KAAG,sBAC9BO,YAAA,CAYY6B,oBAAA;MAtCxB3B,UAAA,EA2BuBC,MAAA,CAAA2B,OAAO,CAACC,KAAK;MA3BpC,uBAAA1B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA2BuBH,MAAA,CAAA2B,OAAO,CAACC,KAAK,GAAAzB,MAAA;MACtBC,WAAW,EAAC,MAAM;MAClBE,SAAS,EAAT,EAAS;MACRuB,QAAM,EAAE7B,MAAA,CAAA8B;;MA9BvBhB,OAAA,EAAAC,QAAA,CAiCgB,MAA6B,E,kBAD/BvB,mBAAA,CAKEuC,SAAA,QArChBC,WAAA,CAiCgChC,MAAA,CAAAiC,YAAY,EAArBL,KAAK;6BADdM,YAAA,CAKEC,oBAAA;UAHC5C,GAAG,EAAEqC,KAAK,CAACQ,KAAK;UAChBC,KAAK,EAAET,KAAK,CAACS,KAAK;UAClBD,KAAK,EAAER,KAAK,CAACQ;;;MApC9BlB,CAAA;qDAyCUxB,mBAAA,CAeM,OAfN4C,UAeM,G,0BAdJ5C,mBAAA,CAAqC;MAA/BJ,KAAK,EAAC;IAAc,GAAC,KAAG,sBAC9BO,YAAA,CAYY6B,oBAAA;MAvDxB3B,UAAA,EA4CuBC,MAAA,CAAA2B,OAAO,CAACY,MAAM;MA5CrC,uBAAArC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA4CuBH,MAAA,CAAA2B,OAAO,CAACY,MAAM,GAAApC,MAAA;MACvBC,WAAW,EAAC,MAAM;MAClBE,SAAS,EAAT,EAAS;MACRuB,QAAM,EAAE7B,MAAA,CAAA8B;;MA/CvBhB,OAAA,EAAAC,QAAA,CAkDgB,MAA+B,E,kBADjCvB,mBAAA,CAKEuC,SAAA,QAtDhBC,WAAA,CAkDiChC,MAAA,CAAAwC,aAAa,EAAvBD,MAAM;6BADfL,YAAA,CAKEC,oBAAA;UAHC5C,GAAG,EAAEgD,MAAM,CAACH,KAAK;UACjBC,KAAK,EAAEE,MAAM,CAACF,KAAK;UACnBD,KAAK,EAAEG,MAAM,CAACH;;;MArD/BlB,CAAA;qDA0DUxB,mBAAA,CAUM,OAVN+C,UAUM,G,0BATJ/C,mBAAA,CAAqC;MAA/BJ,KAAK,EAAC;IAAc,GAAC,KAAG,sBAC9BO,YAAA,CAOY6B,oBAAA;MAnExB3B,UAAA,EA4DgCC,MAAA,CAAA0C,MAAM;MA5DtC,uBAAAxC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA4DgCH,MAAA,CAAA0C,MAAM,GAAAvC,MAAA;MAAEC,WAAW,EAAC,MAAM;MAAEyB,QAAM,EAAE7B,MAAA,CAAA2C;;MA5DpE7B,OAAA,EAAAC,QAAA,CA8DgB,MAA6B,E,kBAD/BvB,mBAAA,CAKEuC,SAAA,QAlEhBC,WAAA,CA8DiChC,MAAA,CAAA4C,WAAW,EAArBC,MAAM;6BADfX,YAAA,CAKEC,oBAAA;UAHC5C,GAAG,EAAEsD,MAAM,CAACT,KAAK;UACjBC,KAAK,EAAEQ,MAAM,CAACR,KAAK;UACnBD,KAAK,EAAES,MAAM,CAACT;;;MAjE/BlB,CAAA;;IAAAA,CAAA;QAyEelB,MAAA,CAAA8C,OAAO,I,cAAlBtD,mBAAA,CAGM,OAHNuD,UAGM,GAFJlD,YAAA,CAAkCmD,sBAAA;IAApBC,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAR;MACvBrD,YAAA,CAA2DmD,sBAAA;IAA7CC,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAR,EAAQ;IAACC,KAAwB,EAAxB;MAAA;IAAA;UAGlBnD,MAAA,CAAAoD,aAAa,CAACC,MAAM,U,cAApC7D,mBAAA,CAEM,OAFN8D,WAEM,GADJzD,YAAA,CAAuC0D,mBAAA;IAA7BC,WAAW,EAAC;EAAc,G,oBAGtChE,mBAAA,CA0DM,OA1DNiE,WA0DM,I,kBAzDJjE,mBAAA,CAwDUuC,SAAA,QA3IhBC,WAAA,CAoFuBhC,MAAA,CAAAoD,aAAa,EAArBM,IAAI;yBADbxB,YAAA,CAwDUZ,kBAAA;MAtDP/B,GAAG,EAAEmE,IAAI,CAACC,EAAE;MACbrE,KAAK,EAtFbsE,eAAA,EAsFc,WAAW;QAAA,eACQF,IAAI,CAACnB,MAAM;MAAA;MACnC3B,OAAK,EAAAT,MAAA,IAAEH,MAAA,CAAA6D,cAAc,CAACH,IAAI;;MAxFnC5C,OAAA,EAAAC,QAAA,CA0FQ,MAKM,CALNrB,mBAAA,CAKM,OALNoE,WAKM,GAJJpE,mBAAA,CAAwB,YAAAqE,gBAAA,CAAjBL,IAAI,CAACM,IAAI,kBAChBnE,YAAA,CAESoE,iBAAA;QAFAtD,IAAI,EAAEX,MAAA,CAAAkE,aAAa,CAACR,IAAI,CAACnB,MAAM;;QA5FlDzB,OAAA,EAAAC,QAAA,CA6FY,MAAgC,CA7F5CI,gBAAA,CAAA4C,gBAAA,CA6Fe/D,MAAA,CAAAmE,aAAa,CAACT,IAAI,CAACnB,MAAM,kB;QA7FxCrB,CAAA;uDAiGQxB,mBAAA,CAsBM,OAtBN0E,WAsBM,GArBJ1E,mBAAA,CAGM,OAHN2E,WAGM,GAFJxE,YAAA,CAA+BmB,kBAAA;QAnG3CF,OAAA,EAAAC,QAAA,CAmGqB,MAAY,CAAZlB,YAAA,CAAYyE,mBAAA,E;QAnGjCpD,CAAA;UAoGYxB,mBAAA,CAAgC,cAAAqE,gBAAA,CAAvBL,IAAI,CAACa,QAAQ,iB,GAGxB7E,mBAAA,CAGM,OAHN8E,WAGM,GAFJ3E,YAAA,CAAqCmB,kBAAA;QAxGjDF,OAAA,EAAAC,QAAA,CAwGqB,MAAkB,CAAlBlB,YAAA,CAAkB4E,yBAAA,E;QAxGvCvD,CAAA;UAyGYxB,mBAAA,CAA8B,cAAAqE,gBAAA,CAArBL,IAAI,CAAC9B,KAAK,IAAG,GAAC,gB,GAGzBlC,mBAAA,CAGM,OAHNgF,WAGM,GAFJ7E,YAAA,CAA2BmB,kBAAA;QA7GvCF,OAAA,EAAAC,QAAA,CA6GqB,MAAQ,CAARlB,YAAA,CAAQ8E,eAAA,E;QA7G7BzD,CAAA;UA8GYxB,mBAAA,CAAqC,cAA/B,MAAI,GAAAqE,gBAAA,CAAGL,IAAI,CAACkB,QAAQ,IAAG,GAAC,gB,GAGhClF,mBAAA,CAKM,OALNmF,WAKM,GAJJhF,YAAA,CAA4BmB,kBAAA;QAlHxCF,OAAA,EAAAC,QAAA,CAkHqB,MAAS,CAATlB,YAAA,CAASiF,gBAAA,E;QAlH9B5D,CAAA;UAmHYxB,mBAAA,CAEC,cADE,QAAM,GAAAqE,gBAAA,CAAG/D,MAAA,CAAA+E,UAAU,CAACrB,IAAI,CAACsB,SAAS,KAAI,KAAG,GAAAjB,gBAAA,CAAG/D,MAAA,CAAA+E,UAAU,CAACrB,IAAI,CAACuB,UAAU,kB,KAK7EvF,mBAAA,CAiBM,OAjBNwF,WAiBM,GAhBJxF,mBAAA,CAWM,OAXNyF,WAWM,GAVJzF,mBAAA,CAGM,OAHN0F,WAGM,G,0BAFJ1F,mBAAA,CAAiB,cAAX,MAAI,sBACVA,mBAAA,CAA2D,cAAAqE,gBAAA,CAAlDL,IAAI,CAAC2B,eAAe,IAAG,GAAC,GAAAtB,gBAAA,CAAGL,IAAI,CAACkB,QAAQ,iB,GAEnD/E,YAAA,CAKEyF,sBAAA;QAJCC,UAAU,EAAG7B,IAAI,CAAC2B,eAAe,GAAG3B,IAAI,CAACkB,QAAQ;QACjDY,MAAM,EAAExF,MAAA,CAAAyF,kBAAkB;QAC1B,cAAY,EAAE,EAAE;QAChBC,KAAK,EAAE1F,MAAA,CAAA2F,oBAAoB,CAACjC,IAAI,CAAC2B,eAAe,EAAE3B,IAAI,CAACkB,QAAQ;oEAIpE/E,YAAA,CAEYa,oBAAA;QAFDC,IAAI,EAAC,SAAS;QAACiF,IAAI,EAAC,OAAO;QAAEC,QAAQ,EAAEnC,IAAI,CAACnB,MAAM;;QAvIvEzB,OAAA,EAAAC,QAAA,CAuIoF,MAE1E,KAAAb,MAAA,SAAAA,MAAA,QAzIViB,gBAAA,CAuIoF,QAE1E,E;QAzIVD,CAAA;QAAAE,EAAA;;MAAAF,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}